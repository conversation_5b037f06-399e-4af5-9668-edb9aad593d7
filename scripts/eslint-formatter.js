/**
 * @fileoverview Stylish reporter
 * @originalAuthor Sindre Sorhus
 * @customization add linkable error/warning location
 */
'use strict';

const chalk = require('chalk');
const path = require('path');

const pluralize = (word, count) => {
  return (count <= 1 ? word : `${word}s`);
};

module.exports = (results, data) => {
  let output = '\n';
  let ruleUrls = {};
  let errorCount = 0;
  let warningCount = 0;
  let fixableErrorCount = 0;
  let fixableWarningCount = 0;
  let summaryColor = 'yellow';

  results.forEach(result => {
    const messages = result.messages;

    if (messages.length === 0) {
      return;
    }

    errorCount += result.errorCount;
    warningCount += result.warningCount;
    fixableErrorCount += result.fixableErrorCount;
    fixableWarningCount += result.fixableWarningCount;

    const relPath = path.relative(__dirname + '/..', result.filePath);
    output += `${chalk.underline(relPath)}\n`;

    // eslint-disable-next-line prefer-template, @typescript-eslint/restrict-plus-operands
    output += messages.map((msg) => {
      let messageType = chalk.yellow('Warning');
      if (msg.fatal || msg.severity === 2) {
        messageType = chalk.red('Error');
        summaryColor = 'red';
      }
      // var ruleUrl = data.rulesMeta[msg.ruleId].docs.url
      // ruleUrls[msg.ruleId] = data.rulesMeta[msg.ruleId].docs.url;

      return (
        // eslint-disable-next-line prefer-template, @typescript-eslint/restrict-plus-operands
        messageType +
        ': ' +
        relPath +
        ':' +
        msg.line +
        ':' +
        msg.column +
        ' ' +
        chalk.dim(msg.ruleId) +
        ' ' +
        chalk.yellow(msg.message)
        // ' ' +
        // chalk.dim(ruleUrl ? ' (' + ruleUrl + ')' : '') +
      );
    }).join('\n') + '\n\n';
  });

  const total = errorCount + warningCount;

  if (total > 0) {
    output += '\n';
    output += chalk[summaryColor].bold([
      '\u2716 ',
      total,
      pluralize(' problem', total),
      ' (',
      errorCount,
      pluralize(' error', errorCount),
      ', ',
      warningCount,
      pluralize(' warning', warningCount),
      ')\n',
    ].join(''));

    if (fixableErrorCount > 0 || fixableWarningCount > 0) {
      output += chalk[summaryColor].bold([
        '  ',
        fixableErrorCount,
        pluralize(' error', fixableErrorCount),
        ' and ',
        fixableWarningCount,
        pluralize(' warning', fixableWarningCount),
        ' potentially fixable by running `npm run lint-fix`.\n',
      ].join(''));
    }

    output += chalk.blue.bold('\n\nEslint Rule References\n');
    // eslint-disable-next-line guard-for-in
    // for (const key in ruleUrls) {
    //   // eslint-disable-next-line prefer-template, @typescript-eslint/restrict-plus-operands
    //   output += (chalk.dim(key) + ':\n' + chalk.green(ruleUrls[key]) + '\n');
    // }
  }

  return total > 0 ? chalk.reset(output) : '';
};
