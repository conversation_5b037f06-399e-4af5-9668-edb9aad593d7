#!/usr/bin/env bash

SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PARENT_DIR="$(dirname "$SCRIPT_DIR")"
echo "source ${PARENT_DIR}/.env"
# shellcheck disable=SC2086
source ${PARENT_DIR}/.env

# shellcheck disable=SC2154
echo "npm install --verbose --prefer-offline --no-audit  --legacy-peer-deps --dist-url=${npm_config_disturl} --runtime=${npm_config_runtime} --target=${npm_config_target}"
# shellcheck disable=SC2086
npm install --verbose --prefer-offline --no-audit  --legacy-peer-deps --dist-url=${npm_config_disturl} --runtime=${npm_config_runtime} --target=${npm_config_target}
