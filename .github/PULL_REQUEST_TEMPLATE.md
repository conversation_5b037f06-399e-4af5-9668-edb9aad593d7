### PR注释 (PR Comments):
...

### 所有提交的PR (All PR Submission):
* [ ] Is this PR connected to proper ticket?<br>PR关联到正确的github ticket了吗？
* [ ] Have you discuss the intended goal and implementation approach with team member?<br>你跟相关的同事(包括审核代码的同事)讨论了预期的实现目标和实现方法了吗？
* [ ] Have you checked that this PR is to be merged into correct (master or release) branch?<br>你检查了这个PR是否要合并到正确的master或者release分支了吗？
* [ ] Have you rebase from the branch you want to merge into?<br>你把要合并进去的分支rebase到这个分支了吗？
* [ ] Have you checked to ensure there aren't other open [PR](../../../pulls) for the same change?<br>你检查了是否有同样的改动是否有其他 [PR](../../../pulls) 了吗？
* [ ] Have you successfully run `npm run check`?<br>你成功的运行过了`npm run check`了吗？
* [ ] Have you successfully run `npm run build`?<br>你成功的运行过了`npm run build`了吗？
* [ ] Have you maintained at least the same level of unit test coverage?<br>你维持了至少跟以前一样的单元测试覆盖率了吗？
* [ ] What's the unit test coverage in this branch before PR submission? Please type here:<br>在这个分支上的单元测试覆盖率是多少？在后面填写：
* [ ] Have you tested your code locally in both English locale and Chinese locale?<br>你在本地测试过了吗？测试过中文和英文版本了吗？
* [ ] Have you demo the feature on your local computer to UX, QA and code reviewers?<br>你在你的开发机器上给UX，测试和审核代码的同事演示过了吗？
