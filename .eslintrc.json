{
  "env": {
    "browser": true,
    "es6": true,
    "node": true
  },
  "extends": [
    "prettier"
  ],
  "parser": "@typescript-eslint/parser",
  "parserOptions": {
    "project": "tsconfig.json",
    "sourceType": "module",
    "createDefaultProgram": true
  },
  "plugins": [
    "@typescript-eslint",
    "unicorn",
    "import",
    "jsdoc",
    "prefer-arrow",
    "jest",
    "security",
    "react",
    "jsx-a11y"
  ],
  "settings": {
    "react": {
      "version": "detect"
    }
  },
  "rules": {
    "@typescript-eslint/adjacent-overload-signatures": "error",
    "@typescript-eslint/array-type": [
      "error",
      {
        "default": "array"
      }
    ],
    "@typescript-eslint/await-thenable": "error",
    // "@typescript-eslint/ban-types": "error",
    // "@typescript-eslint/class-name-casing": "error",
    "@typescript-eslint/consistent-type-assertions": "error",
    "@typescript-eslint/consistent-type-definitions": "off",
    "@typescript-eslint/dot-notation": "error",
    "@typescript-eslint/indent": [
      "error",
      2,
      {
        "CallExpression": {
          "arguments": "first"
        },
        "ArrayExpression": "off",
        "ObjectExpression": "first",
        "FunctionDeclaration": {
          "parameters": "first"
        },
        "FunctionExpression": {
          "parameters": "first"
        },
        "SwitchCase": 1,
        "offsetTernaryExpressions": true
      }
    ],
    "@typescript-eslint/interface-name-prefix": "off",
    "@typescript-eslint/member-delimiter-style": [
      "error",
      {
        "multiline": {
          "delimiter": "semi",
          "requireLast": true
        },
        "singleline": {
          "delimiter": "semi",
          "requireLast": false
        }
      }
    ],
    "@typescript-eslint/method-signature-style": [
      "error",
      "method"
    ],
    "@typescript-eslint/no-array-constructor": "error",
    "@typescript-eslint/no-empty-function": "error",
    "@typescript-eslint/no-empty-interface": "error",
    "@typescript-eslint/no-explicit-any": "off",
    "@typescript-eslint/no-floating-promises": "error",
    "@typescript-eslint/no-for-in-array": "error",
    "@typescript-eslint/no-misused-new": "error",
    "@typescript-eslint/no-namespace": "error",
    "@typescript-eslint/no-parameter-properties": "off",
    "@typescript-eslint/no-unnecessary-qualifier": "error",
    "@typescript-eslint/no-unnecessary-type-arguments": "error",
    "@typescript-eslint/no-unnecessary-type-assertion": "error",
    "@typescript-eslint/no-unused-expressions": "error",
    "@typescript-eslint/no-use-before-define": "off",
    "@typescript-eslint/no-var-requires": "error",
    "@typescript-eslint/prefer-for-of": "error",
    "@typescript-eslint/prefer-function-type": "error",
    "@typescript-eslint/prefer-namespace-keyword": "error",
    "@typescript-eslint/promise-function-async": "error",
    "@typescript-eslint/quotes": [
      "error",
      "single"
    ],
    "@typescript-eslint/restrict-plus-operands": "error",
    "@typescript-eslint/semi": [
      "error",
      "always"
    ],
    "@typescript-eslint/strict-boolean-expressions": "off",
    "@typescript-eslint/triple-slash-reference": [
      "error",
      {
        "path": "always",
        "types": "prefer-import",
        "lib": "always"
      }
    ],
    "@typescript-eslint/type-annotation-spacing": "error",
    "@typescript-eslint/typedef": [
      "error",
      {
        "propertyDeclaration": true,
        "memberVariableDeclaration": true,
        "arrayDestructuring": false,
        "parameter": false,
        "arrowParameter": false,
        "variableDeclarationIgnoreFunction": true
      }
    ],
    "@typescript-eslint/unified-signatures": "error",
    "arrow-parens": [
      "off",
      "always"
    ],
    "brace-style": [
      "error",
      "1tbs",
      {
        "allowSingleLine": true
      }
    ],
    "camelcase": [
      "off",
      {
        "properties": "never",
        "ignoreImports": true,
        "ignoreDestructuring": true
      }
    ],
    "comma-dangle": [
      "error",
      {
        "objects": "always-multiline",
        "arrays": "always-multiline",
        "functions": "never",
        "imports": "always-multiline",
        "exports": "always-multiline"
      }
    ],
    "complexity": "off",
    "constructor-super": "error",
    "eol-last": "error",
    "eqeqeq": [
      "error",
      "smart"
    ],
    "func-style": [
      "error",
      "declaration",
      {
        "allowArrowFunctions": true
      }
    ],
    "guard-for-in": "error",
    "id-blacklist": [
      "error",
      "any",
      "Number",
      "number",
      "String",
      "string",
      "Boolean",
      "boolean",
      "Undefined",
      "undefined"
    ],
    "id-match": "error",
    // "import/no-default-export": "error",
    "import/no-extraneous-dependencies": [
      "error",
      {
        "devDependencies": true
      }
    ],
    "import/no-internal-modules": [
      "error",
      {
        "allow": [
          // "bootstrap/*",
          // "font-awesome/*",
          // "jquery/*",
          // "ionicons/*",
          // "admin-lte/*",
          // "brainbrowser/*",
          "**/*.less",
          "**/*.json",
          "**/*.css",
          "**/*.svg",
          "**/*.gif",
          "**/*.png",
          "@ant-design/**/*",
          "antd/**/*",
          "brainbrowser/**/*"
        ]
      }
    ],
    "import/no-unassigned-import": [
      "error",
      {
        "allow": [
          "**/*.less",
          "**/*.module.less",
          "**/*.css",
          "brainbrowser/**/*.js"
        ]
      }
    ],
    "import/order": "off",
    "jest/no-focused-tests": "error",
    "jsdoc/check-alignment": "error",
    "jsdoc/check-indentation": "error",
    "jsdoc/newline-after-description": "off",
    "jsdoc/no-types": "error",
    "jsx-a11y/alt-text": "error",
    "jsx-a11y/aria-props": "error",
    "jsx-a11y/aria-proptypes": "error",
    "jsx-a11y/aria-role": "error",
    "jsx-a11y/aria-unsupported-elements": "error",
    "jsx-a11y/heading-has-content": "error",
    "jsx-a11y/no-onchange": "error",
    "jsx-a11y/no-static-element-interactions": "off",
    "jsx-a11y/role-has-required-aria-props": "error",
    "jsx-a11y/role-supports-aria-props": "error",
    "jsx-a11y/tabindex-no-positive": "error",
    "linebreak-style": "off",
    "max-classes-per-file": [
      "error",
      5
    ],
    "max-len": "off",
    "max-lines-per-function": [
      "off",
      {
        "max": 100,
        "skipBlankLines": true,
        "skipComments": true
      }
    ],
    "new-parens": "off",
    "newline-per-chained-call": "off",
    "no-bitwise": "off",
    "no-caller": "error",
    "no-cond-assign": "error",
    "no-console": [
      "error",
      {
        "allow": [
          "log",
          "info",
          "error",
          "time",
          "timeEnd"
        ]
      }
    ],
    "no-constant-condition": "error",
    "no-control-regex": "error",
    "no-debugger": "error",
    "no-delete-var": "error",
    "no-duplicate-imports": "error",
    "no-empty": "error",
    "no-eval": "error",
    "no-extra-bind": "error",
    "no-extra-semi": "error",
    "no-fallthrough": "off",
    "no-invalid-regexp": "error",
    "no-invalid-this": "off",
    "no-irregular-whitespace": "off",
    "no-multi-str": "error",
    "no-multiple-empty-lines": [
      "error",
      {
        "max": 1,
        "maxEOF": 1,
        "maxBOF": 0
      }
    ],
    "no-new-func": "error",
    "no-new-wrappers": "error",
    "no-octal": "error",
    "no-octal-escape": "error",
    "no-regex-spaces": "error",
    "no-restricted-syntax": [
      "off",
      "ForInStatement",
      "FunctionExpression"
    ],
    "no-shadow": "off",
    "@typescript-eslint/no-shadow": [
      "error"
    ],
    "no-throw-literal": "error",
    "no-trailing-spaces": "error",
    "no-undef-init": "error",
    "no-unsafe-finally": "error",
    "no-unused-labels": "error",
    "no-var": "error",
    "no-void": "error",
    "no-warning-comments": [
      1,
      {
        "terms": [
          "todo",
          "fixme",
          "xxx"
        ],
        "location": "start"
      }
    ],
    "no-with": "error",
    "object-shorthand": "off",
    "one-var": [
      "error",
      "never"
    ],
    "padding-line-between-statements": [
      "error",
      {
        "blankLine": "always",
        "prev": "*",
        "next": "return"
      }
    ],
    "padded-blocks": [
      "error",
      "never"
    ],
    "prefer-arrow/prefer-arrow-functions": "error",
    "prefer-const": "off",
    "prefer-template": "error",
    "quote-props": "off",
    "radix": "error",
    "react/jsx-curly-spacing": [
      "error",
      {
        "when": "never"
      }
    ],
    "react/jsx-boolean-value": [
      "error",
      "never",
      {
        "always": []
      }
    ],
    "react/jsx-key": "error",
    "react/jsx-no-bind": [
      "error",
      {
        "allowArrowFunctions": true
      }
    ],
    "react/no-danger": "error",
    "react/self-closing-comp": [
      "error",
      {
        "component": true,
        "html": true
      }
    ],
    "security/detect-non-literal-require": "error",
    "security/detect-possible-timing-attacks": "error",
    "space-before-function-paren": "off",
    "space-in-parens": [
      "error",
      "never"
    ],
    "spaced-comment": [
      "error",
      "always",
      {
        "markers": [
          "/"
        ]
      }
    ],
    "unicorn/filename-case": [
      "error",
      {
        "case": "camelCase"
      }
    ],
    "use-isnan": "error",
    "valid-typeof": "off"
  },
  "overrides": [
    {
      "files": [
        "**/*/*.test.tsx",
        "**/*/*.test.ts"
      ],
      "rules": {
        "max-lines-per-function": "off"
      }
    },
    {
      "files": [
        "scripts/**/*.js"
      ],
      "rules": {
        "@typescript-eslint/no-var-requires": "off",
        "import/no-internal-modules": "off",
        "prefer-template": "off",
        "unicorn/filename-case": "off"
      }
    }
  ]
}
