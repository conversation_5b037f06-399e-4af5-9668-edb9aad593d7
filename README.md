# ngiq-pro-desktop

### 初始化项目只需要三步完成

#### 安装nvm

```shell
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.5/install.sh | bash
```

#### 切换node的16版本

```shell
nvm install
```

#### 下载npm的依赖包

```shell
./scripts/npm_install.sh
```

#### 启动开发模式的renderer

```shell
npm run vite-start-renderer-dev
```

#### 启动开发模式的main

```shell
npm run start-main-dev
```

### 文件目录结构如下
```
src
├── common // main 和 render 用到的公共文件
│   ├── api // api 请求类
│   ├── auth.ts
│   ├── getProductionLine.ts
│   ├── i18n // 语言包
│   │   └── locale.ts
│   ├── ipc // 进程通信
│   │   ├── ipcChannels.ts
│   │   └── preload.ts
│   ├── util // 工具类
│   └── types // ts 声明的类型
├── main // main 进程
│   ├── appMenu.ts
│   ├── i18n // 语言包
│   ├── ipc // ipc 通信
│   │   ├── ipcHandle.ts
│   │   └── listeners.ts
│   ├── util // 封装的工具类
│   └── main.ts // main 入口文件
└── renderer // 前端目录
├── app.less
├── app.tsx // 前端入口文件
├── components // 前端各页面使用到的公共组件
├── container // 页面入口
│   ├── children1
│   ├── home
│   ├── login
│   └── uiComponentDemo
│   ├── component // 当前 container 用到的组件
│   │   ├── button.tsx
│   │   ├── form.tsx
│   │   ├── input.tsx
│   │   ├── modal.tsx
│   │   ├── radio.tsx
│   │   └── steps.tsx
│   ├── index.less
│   └── index.tsx
├── hocComponent // hoc 工具类
│   ├── withIntl.tsx
│   ├── withRouter.tsx
│   └── withUserSession.tsx
├── recoil // 状态管理 recoil
│   ├── intl.ts
│   └── user.ts
├── router // 路由
│   ├── authRoute.tsx
│   ├── index.tsx
│   └── routers.tsx
├── static // 静态资源
│   ├── SourceHanSansSC-Medium-2.otf
│   ├── SourceHanSansSC-Normal-2.otf
│   ├── images
│   │   └── symbols
│   │   ├── invertTriangle.png
│   │   └── success.png
│   ├── messages
│   │   ├── en-us.json
│   │   └── zh-cn.json
│   ├── style // base less
│   │   ├── antd.less
│   │   ├── base.less
│   │   ├── baseColor.module.less
│   │   └── index.css
│   └── svg
└── uiComponent // 跟业务无关的组件库, 在 antd 基础上封装出来的组件
├── NgButton
│   ├── index.less
│   └── index.tsx
├── NgButtonText
│   ├── index.less
│   └── ngButtonText.tsx
├── NgCheckbox
│   ├── index.less
│   └── index.tsx
├── NgForm
│   └── index.tsx
├── NgInput
│   ├── index.less
│   └── index.tsx
├── NgModal
│   └── index.tsx
├── NgRadio
│   ├── index.less
│   └── index.tsx
├── NgStep
│   ├── index.less
│   └── index.tsx
└── NgTable
└── index.tsx
```

## i18n

### 关于扫描与替换文件的脚本

### m200中使用
```sh
npm run i18n-scan
```
```sh
npm run i18n-cover
```
### analysis.config.js 文件
```js
const defaultConfig = {
  entry: ['src/renderer'], // 需要扫面入口
  exclude: '**{/tes?/**/*,/*/*.test.*}', // 排除的文件(tips: 目前只支持类regexp， 不支持数组-_-||)
  dataPath: 'src/renderer/static/messages', // 需要输出的目录(tips: 需要提前写好文件夹!，没做兜底处理-_-||)
}

module.exports = defaultConfig
```
### 使用
#### i18n scan
  扫面文件，找到文件中汉字并追加到defaultConfig.dataPath中
#### i18n cover 
  包含scan内容+解析文件并添加import&intlMessage.t到中文处 (tips: 其他文件不要叫这个名字,暂时没有做引用兼容-_-||)
### 其他
  * 需要一个导出为intlMessage{t:(val)=><format>}的文件
  * babel.config.json不可以设置chrome版本(暂时不知道为什么,在找原因, working-_-||)
  * cover不支持其他intl使用格式(原因: 没做兼容,且使用方式因项目而异,无法全部做处理 :D),处理文件时会出问题,如:
  ```js
  <FormatMessage id={id} />// 如果要使用请exclude文件
  ```
  * 可能会有eslint格式冲突,如果使用cover时建议处理项目内eslint

  
### 生产环境version.json 配置
```javascript
{
  'REACT_APP_SHOW_APP_MENU': 'show',
  'BACKEND_LOG_PATH':[
    '/var/log/ngiq-pro-server',
    '/var/log/mysql',
    '/var/log/slbs',
    '/var/log/tunnels',
  ],
}
```

### 产品线区分
本机开发：  修改 product.ts    
线上： versions.json 同级目录 product_config, 读字符串解密

## 新来一个拍子模型，如何判断和之前的坐标系是否一样
正确的拍子坐标系，红色： X，  绿色： Y ，  蓝色： Z。 这是brainBrowser 的坐标系
![img.png](img.png)    
后续新来的拍子模型，要求必须是X轴在长臂上，Z轴指向刺激大脑方向
## 如果不能直接使用，该如何变换呢
group: three.js 的一个对象，可以理解为一个容器，可以包含多个mesh，可以对group进行变换，变换后，group中的mesh也会跟着变换。    
原理： 拍子内部有一套自己的坐标系，需要将内部变换后，然后抛给外部一个group；因为外部操作的是一个group，所以对于外部而言是一样的。    
经过下面的变换后，外部软件对于拍子的初始化是 无感的。    
这是一个错误的拍子模型，可以看到，拍子长臂不指向红色的X轴。不在 0 0 0上。
![img_1.png](img_1.png)     
可以看到拍子的原始坐标系
![img_2.png](img_2.png)    
所以，需要调整位置和 旋转让长臂指向红色的X 轴。通过坐标可以看到，需要移动的大概距离，然后细微调整即可。
```js
object.children.forEach((item: any) => {
    item.position.set(624.5, 0.3, 668)
    item.rotation.z += Math.PI / 2;
});
```
这是调整后的，可以看到和 第一版有圆孔的拍子模型，在位置和角度上都一样了。
![img_3.png](img_3.png)
## 关于箭头指示器原理（可以不用关注，因为M200 采用机械臂移动，没有价值了）
### 如何计算拍子翻转角
请在下面的图上想象一个钟表， // x 指向6点钟   y指向9点钟   z 指向表内側      
结合三角函数，如何一个进入点法线 在 绿色拍子的法线左侧30度夹角，意味着拍子需要往左侧压30度可以和 治疗点法线重合。   
所以拍子需要绘制一个  向左 越起来的箭头，提示向左压30度。    
明确了需要向左越，那么拍子应该向左 压。 进入点法线在 拍子表面投影，即可得到 拍子往左跳跃的基准。   
请使用刚才得到投影基准和 6点钟方向 相减，可以得到水平旋转角。 竖直跳跃角 是法线夹角。

### 水平移动角如何计算
进入点法线  在拍子上的交点和拍子中心连线，与 6点中方向做夹角，可以得到水平旋转角。距离，表示需要移动的距离。
![img_5.png](img_5.png)
## surface 需要切换头皮的颜色，亮度该如何操作。
```js
 color: options.material.color || 0XBAB9BE,    // 基础颜色
 metalness: options.material.metainess || 0,   // 金属感 0-1， 0表示没有金属感
 emissive: options.material.emissive || 0x000000,   // 自发光颜色
 emissiveIntensity: options.material.emissiveIntensity || 0.6,  // 自发光强度
```
使用emissive时，建议选择和 color 相近的亮色，或者纯白色，否则会出现调色的情况。   


## 如何在surface上做切片，将surface 透视到脑图内部。可以
