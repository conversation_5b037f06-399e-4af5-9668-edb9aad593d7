/*
* BrainBrowser: Web-based Neurological Visualization Tools
* (https://brainbrowser.cbrain.mcgill.ca)
*
* Copyright (C) 2011
* The Royal Institution for the Advancement of Learning
* McGill University
*
* This program is free software: you can redistribute it and/or modify
* it under the terms of the GNU Affero General Public License as
* published by the Free Software Foundation, either version 3 of the
* License, or (at your option) any later version.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU Affero General Public License for more details.
*
* You should have received a copy of the GNU Affero General Public License
* along with this program.  If not, see <http://www.gnu.org/licenses/>.
*/

/*
* BrainBrowser v2.5.5
*
* Author: <PERSON><PERSON>  <<EMAIL>> (http://tareksherif.ca/)
* Author: <PERSON>
* Author: <PERSON>
*
* three.js (c) 2010-2014 three.js authors, used under the MIT license
*/
!function(a){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=a();else if("function"==typeof define&&define.amd)define([],a);else{var b;b="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this,b.gifti=a()}}(function(){return function a(b,c,d){function e(g,h){if(!c[g]){if(!b[g]){var i="function"==typeof require&&require;if(!h&&i)return i(g,!0);if(f)return f(g,!0);var j=new Error("Cannot find module '"+g+"'");throw j.code="MODULE_NOT_FOUND",j}var k=c[g]={exports:{}};b[g][0].call(k.exports,function(a){var c=b[g][1][a];return e(c?c:a)},k,k.exports,a,b,c,d)}return c[g].exports}for(var f="function"==typeof require&&require,g=0;g<d.length;g++)e(d[g]);return e}({1:[function(a,b,c){"use strict";var d={_keyStr:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",decodeArrayBuffer:function(a){var b=this._keyStr.indexOf(a.charAt(a.length-1)),c=this._keyStr.indexOf(a.charAt(a.length-2)),d=a.length/4*3;64==b&&d--,64==c&&d--;var e=new ArrayBuffer(d);return this.decode(a,e,d),e},decode:function(a,b,c){var d,e,f,g,h,i,j,k,l=0,m=0;for(d=b?new Uint8Array(b):new Uint8Array(c),a=a.replace(/[^A-Za-z0-9\+\/\=]/g,""),l=0;c>l;l+=3)h=this._keyStr.indexOf(a.charAt(m++)),i=this._keyStr.indexOf(a.charAt(m++)),j=this._keyStr.indexOf(a.charAt(m++)),k=this._keyStr.indexOf(a.charAt(m++)),e=h<<2|i>>4,f=(15&i)<<4|j>>2,g=(3&j)<<6|k,d[l]=e,64!=j&&(d[l+1]=f),64!=k&&(d[l+2]=g);return d}},e=typeof b;"undefined"!==e&&b.exports&&(b.exports=d)},{}],2:[function(a,b,c){},{}],3:[function(a,b,c){(function(b){"use strict";function d(){try{var a=new Uint8Array(1);return a.foo=function(){return 42},42===a.foo()&&"function"==typeof a.subarray&&0===a.subarray(1,1).byteLength}catch(b){return!1}}function e(){return f.TYPED_ARRAY_SUPPORT?2147483647:1073741823}function f(a){return this instanceof f?(f.TYPED_ARRAY_SUPPORT||(this.length=0,this.parent=void 0),"number"==typeof a?g(this,a):"string"==typeof a?h(this,a,arguments.length>1?arguments[1]:"utf8"):i(this,a)):arguments.length>1?new f(a,arguments[1]):new f(a)}function g(a,b){if(a=p(a,0>b?0:0|q(b)),!f.TYPED_ARRAY_SUPPORT)for(var c=0;b>c;c++)a[c]=0;return a}function h(a,b,c){("string"!=typeof c||""===c)&&(c="utf8");var d=0|s(b,c);return a=p(a,d),a.write(b,c),a}function i(a,b){if(f.isBuffer(b))return j(a,b);if(Y(b))return k(a,b);if(null==b)throw new TypeError("must start with number, buffer, array or string");if("undefined"!=typeof ArrayBuffer){if(b.buffer instanceof ArrayBuffer)return l(a,b);if(b instanceof ArrayBuffer)return m(a,b)}return b.length?n(a,b):o(a,b)}function j(a,b){var c=0|q(b.length);return a=p(a,c),b.copy(a,0,0,c),a}function k(a,b){var c=0|q(b.length);a=p(a,c);for(var d=0;c>d;d+=1)a[d]=255&b[d];return a}function l(a,b){var c=0|q(b.length);a=p(a,c);for(var d=0;c>d;d+=1)a[d]=255&b[d];return a}function m(a,b){return b.byteLength,f.TYPED_ARRAY_SUPPORT?(a=new Uint8Array(b),a.__proto__=f.prototype):a=l(a,new Uint8Array(b)),a}function n(a,b){var c=0|q(b.length);a=p(a,c);for(var d=0;c>d;d+=1)a[d]=255&b[d];return a}function o(a,b){var c,d=0;"Buffer"===b.type&&Y(b.data)&&(c=b.data,d=0|q(c.length)),a=p(a,d);for(var e=0;d>e;e+=1)a[e]=255&c[e];return a}function p(a,b){f.TYPED_ARRAY_SUPPORT?(a=new Uint8Array(b),a.__proto__=f.prototype):a.length=b;var c=0!==b&&b<=f.poolSize>>>1;return c&&(a.parent=Z),a}function q(a){if(a>=e())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+e().toString(16)+" bytes");return 0|a}function r(a,b){if(!(this instanceof r))return new r(a,b);var c=new f(a,b);return delete c.parent,c}function s(a,b){"string"!=typeof a&&(a=""+a);var c=a.length;if(0===c)return 0;for(var d=!1;;)switch(b){case"ascii":case"binary":case"raw":case"raws":return c;case"utf8":case"utf-8":return R(a).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*c;case"hex":return c>>>1;case"base64":return U(a).length;default:if(d)return R(a).length;b=(""+b).toLowerCase(),d=!0}}function t(a,b,c){var d=!1;if(b=0|b,c=void 0===c||c===1/0?this.length:0|c,a||(a="utf8"),0>b&&(b=0),c>this.length&&(c=this.length),b>=c)return"";for(;;)switch(a){case"hex":return F(this,b,c);case"utf8":case"utf-8":return B(this,b,c);case"ascii":return D(this,b,c);case"binary":return E(this,b,c);case"base64":return A(this,b,c);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return G(this,b,c);default:if(d)throw new TypeError("Unknown encoding: "+a);a=(a+"").toLowerCase(),d=!0}}function u(a,b,c,d){c=Number(c)||0;var e=a.length-c;d?(d=Number(d),d>e&&(d=e)):d=e;var f=b.length;if(f%2!==0)throw new Error("Invalid hex string");d>f/2&&(d=f/2);for(var g=0;d>g;g++){var h=parseInt(b.substr(2*g,2),16);if(isNaN(h))throw new Error("Invalid hex string");a[c+g]=h}return g}function v(a,b,c,d){return V(R(b,a.length-c),a,c,d)}function w(a,b,c,d){return V(S(b),a,c,d)}function x(a,b,c,d){return w(a,b,c,d)}function y(a,b,c,d){return V(U(b),a,c,d)}function z(a,b,c,d){return V(T(b,a.length-c),a,c,d)}function A(a,b,c){return 0===b&&c===a.length?W.fromByteArray(a):W.fromByteArray(a.slice(b,c))}function B(a,b,c){c=Math.min(a.length,c);for(var d=[],e=b;c>e;){var f=a[e],g=null,h=f>239?4:f>223?3:f>191?2:1;if(c>=e+h){var i,j,k,l;switch(h){case 1:128>f&&(g=f);break;case 2:i=a[e+1],128===(192&i)&&(l=(31&f)<<6|63&i,l>127&&(g=l));break;case 3:i=a[e+1],j=a[e+2],128===(192&i)&&128===(192&j)&&(l=(15&f)<<12|(63&i)<<6|63&j,l>2047&&(55296>l||l>57343)&&(g=l));break;case 4:i=a[e+1],j=a[e+2],k=a[e+3],128===(192&i)&&128===(192&j)&&128===(192&k)&&(l=(15&f)<<18|(63&i)<<12|(63&j)<<6|63&k,l>65535&&1114112>l&&(g=l))}}null===g?(g=65533,h=1):g>65535&&(g-=65536,d.push(g>>>10&1023|55296),g=56320|1023&g),d.push(g),e+=h}return C(d)}function C(a){var b=a.length;if($>=b)return String.fromCharCode.apply(String,a);for(var c="",d=0;b>d;)c+=String.fromCharCode.apply(String,a.slice(d,d+=$));return c}function D(a,b,c){var d="";c=Math.min(a.length,c);for(var e=b;c>e;e++)d+=String.fromCharCode(127&a[e]);return d}function E(a,b,c){var d="";c=Math.min(a.length,c);for(var e=b;c>e;e++)d+=String.fromCharCode(a[e]);return d}function F(a,b,c){var d=a.length;(!b||0>b)&&(b=0),(!c||0>c||c>d)&&(c=d);for(var e="",f=b;c>f;f++)e+=Q(a[f]);return e}function G(a,b,c){for(var d=a.slice(b,c),e="",f=0;f<d.length;f+=2)e+=String.fromCharCode(d[f]+256*d[f+1]);return e}function H(a,b,c){if(a%1!==0||0>a)throw new RangeError("offset is not uint");if(a+b>c)throw new RangeError("Trying to access beyond buffer length")}function I(a,b,c,d,e,g){if(!f.isBuffer(a))throw new TypeError("buffer must be a Buffer instance");if(b>e||g>b)throw new RangeError("value is out of bounds");if(c+d>a.length)throw new RangeError("index out of range")}function J(a,b,c,d){0>b&&(b=65535+b+1);for(var e=0,f=Math.min(a.length-c,2);f>e;e++)a[c+e]=(b&255<<8*(d?e:1-e))>>>8*(d?e:1-e)}function K(a,b,c,d){0>b&&(b=4294967295+b+1);for(var e=0,f=Math.min(a.length-c,4);f>e;e++)a[c+e]=b>>>8*(d?e:3-e)&255}function L(a,b,c,d,e,f){if(c+d>a.length)throw new RangeError("index out of range");if(0>c)throw new RangeError("index out of range")}function M(a,b,c,d,e){return e||L(a,b,c,4,3.4028234663852886e38,-3.4028234663852886e38),X.write(a,b,c,d,23,4),c+4}function N(a,b,c,d,e){return e||L(a,b,c,8,1.7976931348623157e308,-1.7976931348623157e308),X.write(a,b,c,d,52,8),c+8}function O(a){if(a=P(a).replace(_,""),a.length<2)return"";for(;a.length%4!==0;)a+="=";return a}function P(a){return a.trim?a.trim():a.replace(/^\s+|\s+$/g,"")}function Q(a){return 16>a?"0"+a.toString(16):a.toString(16)}function R(a,b){b=b||1/0;for(var c,d=a.length,e=null,f=[],g=0;d>g;g++){if(c=a.charCodeAt(g),c>55295&&57344>c){if(!e){if(c>56319){(b-=3)>-1&&f.push(239,191,189);continue}if(g+1===d){(b-=3)>-1&&f.push(239,191,189);continue}e=c;continue}if(56320>c){(b-=3)>-1&&f.push(239,191,189),e=c;continue}c=(e-55296<<10|c-56320)+65536}else e&&(b-=3)>-1&&f.push(239,191,189);if(e=null,128>c){if((b-=1)<0)break;f.push(c)}else if(2048>c){if((b-=2)<0)break;f.push(c>>6|192,63&c|128)}else if(65536>c){if((b-=3)<0)break;f.push(c>>12|224,c>>6&63|128,63&c|128)}else{if(!(1114112>c))throw new Error("Invalid code point");if((b-=4)<0)break;f.push(c>>18|240,c>>12&63|128,c>>6&63|128,63&c|128)}}return f}function S(a){for(var b=[],c=0;c<a.length;c++)b.push(255&a.charCodeAt(c));return b}function T(a,b){for(var c,d,e,f=[],g=0;g<a.length&&!((b-=2)<0);g++)c=a.charCodeAt(g),d=c>>8,e=c%256,f.push(e),f.push(d);return f}function U(a){return W.toByteArray(O(a))}function V(a,b,c,d){for(var e=0;d>e&&!(e+c>=b.length||e>=a.length);e++)b[e+c]=a[e];return e}var W=a("base64-js"),X=a("ieee754"),Y=a("isarray");c.Buffer=f,c.SlowBuffer=r,c.INSPECT_MAX_BYTES=50,f.poolSize=8192;var Z={};f.TYPED_ARRAY_SUPPORT=void 0!==b.TYPED_ARRAY_SUPPORT?b.TYPED_ARRAY_SUPPORT:d(),f._augment=function(a){return a.__proto__=f.prototype,a},f.TYPED_ARRAY_SUPPORT?(f.prototype.__proto__=Uint8Array.prototype,f.__proto__=Uint8Array,"undefined"!=typeof Symbol&&Symbol.species&&f[Symbol.species]===f&&Object.defineProperty(f,Symbol.species,{value:null,configurable:!0})):(f.prototype.length=void 0,f.prototype.parent=void 0),f.isBuffer=function(a){return!(null==a||!a._isBuffer)},f.compare=function(a,b){if(!f.isBuffer(a)||!f.isBuffer(b))throw new TypeError("Arguments must be Buffers");if(a===b)return 0;for(var c=a.length,d=b.length,e=0,g=Math.min(c,d);g>e&&a[e]===b[e];)++e;return e!==g&&(c=a[e],d=b[e]),d>c?-1:c>d?1:0},f.isEncoding=function(a){switch(String(a).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"raw":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},f.concat=function(a,b){if(!Y(a))throw new TypeError("list argument must be an Array of Buffers.");if(0===a.length)return new f(0);var c;if(void 0===b)for(b=0,c=0;c<a.length;c++)b+=a[c].length;var d=new f(b),e=0;for(c=0;c<a.length;c++){var g=a[c];g.copy(d,e),e+=g.length}return d},f.byteLength=s,f.prototype._isBuffer=!0,f.prototype.toString=function(){var a=0|this.length;return 0===a?"":0===arguments.length?B(this,0,a):t.apply(this,arguments)},f.prototype.equals=function(a){if(!f.isBuffer(a))throw new TypeError("Argument must be a Buffer");return this===a?!0:0===f.compare(this,a)},f.prototype.inspect=function(){var a="",b=c.INSPECT_MAX_BYTES;return this.length>0&&(a=this.toString("hex",0,b).match(/.{2}/g).join(" "),this.length>b&&(a+=" ... ")),"<Buffer "+a+">"},f.prototype.compare=function(a){if(!f.isBuffer(a))throw new TypeError("Argument must be a Buffer");return this===a?0:f.compare(this,a)},f.prototype.indexOf=function(a,b){function c(a,b,c){for(var d=-1,e=0;c+e<a.length;e++)if(a[c+e]===b[-1===d?0:e-d]){if(-1===d&&(d=e),e-d+1===b.length)return c+d}else d=-1;return-1}if(b>2147483647?b=2147483647:-2147483648>b&&(b=-2147483648),b>>=0,0===this.length)return-1;if(b>=this.length)return-1;if(0>b&&(b=Math.max(this.length+b,0)),"string"==typeof a)return 0===a.length?-1:String.prototype.indexOf.call(this,a,b);if(f.isBuffer(a))return c(this,a,b);if("number"==typeof a)return f.TYPED_ARRAY_SUPPORT&&"function"===Uint8Array.prototype.indexOf?Uint8Array.prototype.indexOf.call(this,a,b):c(this,[a],b);throw new TypeError("val must be string, number or Buffer")},f.prototype.write=function(a,b,c,d){if(void 0===b)d="utf8",c=this.length,b=0;else if(void 0===c&&"string"==typeof b)d=b,c=this.length,b=0;else if(isFinite(b))b=0|b,isFinite(c)?(c=0|c,void 0===d&&(d="utf8")):(d=c,c=void 0);else{var e=d;d=b,b=0|c,c=e}var f=this.length-b;if((void 0===c||c>f)&&(c=f),a.length>0&&(0>c||0>b)||b>this.length)throw new RangeError("attempt to write outside buffer bounds");d||(d="utf8");for(var g=!1;;)switch(d){case"hex":return u(this,a,b,c);case"utf8":case"utf-8":return v(this,a,b,c);case"ascii":return w(this,a,b,c);case"binary":return x(this,a,b,c);case"base64":return y(this,a,b,c);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return z(this,a,b,c);default:if(g)throw new TypeError("Unknown encoding: "+d);d=(""+d).toLowerCase(),g=!0}},f.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var $=4096;f.prototype.slice=function(a,b){var c=this.length;a=~~a,b=void 0===b?c:~~b,0>a?(a+=c,0>a&&(a=0)):a>c&&(a=c),0>b?(b+=c,0>b&&(b=0)):b>c&&(b=c),a>b&&(b=a);var d;if(f.TYPED_ARRAY_SUPPORT)d=this.subarray(a,b),d.__proto__=f.prototype;else{var e=b-a;d=new f(e,void 0);for(var g=0;e>g;g++)d[g]=this[g+a]}return d.length&&(d.parent=this.parent||this),d},f.prototype.readUIntLE=function(a,b,c){a=0|a,b=0|b,c||H(a,b,this.length);for(var d=this[a],e=1,f=0;++f<b&&(e*=256);)d+=this[a+f]*e;return d},f.prototype.readUIntBE=function(a,b,c){a=0|a,b=0|b,c||H(a,b,this.length);for(var d=this[a+--b],e=1;b>0&&(e*=256);)d+=this[a+--b]*e;return d},f.prototype.readUInt8=function(a,b){return b||H(a,1,this.length),this[a]},f.prototype.readUInt16LE=function(a,b){return b||H(a,2,this.length),this[a]|this[a+1]<<8},f.prototype.readUInt16BE=function(a,b){return b||H(a,2,this.length),this[a]<<8|this[a+1]},f.prototype.readUInt32LE=function(a,b){return b||H(a,4,this.length),(this[a]|this[a+1]<<8|this[a+2]<<16)+16777216*this[a+3]},f.prototype.readUInt32BE=function(a,b){return b||H(a,4,this.length),16777216*this[a]+(this[a+1]<<16|this[a+2]<<8|this[a+3])},f.prototype.readIntLE=function(a,b,c){a=0|a,b=0|b,c||H(a,b,this.length);for(var d=this[a],e=1,f=0;++f<b&&(e*=256);)d+=this[a+f]*e;return e*=128,d>=e&&(d-=Math.pow(2,8*b)),d},f.prototype.readIntBE=function(a,b,c){a=0|a,b=0|b,c||H(a,b,this.length);for(var d=b,e=1,f=this[a+--d];d>0&&(e*=256);)f+=this[a+--d]*e;return e*=128,f>=e&&(f-=Math.pow(2,8*b)),f},f.prototype.readInt8=function(a,b){return b||H(a,1,this.length),128&this[a]?-1*(255-this[a]+1):this[a]},f.prototype.readInt16LE=function(a,b){b||H(a,2,this.length);var c=this[a]|this[a+1]<<8;return 32768&c?4294901760|c:c},f.prototype.readInt16BE=function(a,b){b||H(a,2,this.length);var c=this[a+1]|this[a]<<8;return 32768&c?4294901760|c:c},f.prototype.readInt32LE=function(a,b){return b||H(a,4,this.length),this[a]|this[a+1]<<8|this[a+2]<<16|this[a+3]<<24},f.prototype.readInt32BE=function(a,b){return b||H(a,4,this.length),this[a]<<24|this[a+1]<<16|this[a+2]<<8|this[a+3]},f.prototype.readFloatLE=function(a,b){return b||H(a,4,this.length),X.read(this,a,!0,23,4)},f.prototype.readFloatBE=function(a,b){return b||H(a,4,this.length),X.read(this,a,!1,23,4)},f.prototype.readDoubleLE=function(a,b){return b||H(a,8,this.length),X.read(this,a,!0,52,8)},f.prototype.readDoubleBE=function(a,b){return b||H(a,8,this.length),X.read(this,a,!1,52,8)},f.prototype.writeUIntLE=function(a,b,c,d){a=+a,b=0|b,c=0|c,d||I(this,a,b,c,Math.pow(2,8*c),0);var e=1,f=0;for(this[b]=255&a;++f<c&&(e*=256);)this[b+f]=a/e&255;return b+c},f.prototype.writeUIntBE=function(a,b,c,d){a=+a,b=0|b,c=0|c,d||I(this,a,b,c,Math.pow(2,8*c),0);var e=c-1,f=1;for(this[b+e]=255&a;--e>=0&&(f*=256);)this[b+e]=a/f&255;return b+c},f.prototype.writeUInt8=function(a,b,c){return a=+a,b=0|b,c||I(this,a,b,1,255,0),f.TYPED_ARRAY_SUPPORT||(a=Math.floor(a)),this[b]=255&a,b+1},f.prototype.writeUInt16LE=function(a,b,c){return a=+a,b=0|b,c||I(this,a,b,2,65535,0),f.TYPED_ARRAY_SUPPORT?(this[b]=255&a,this[b+1]=a>>>8):J(this,a,b,!0),b+2},f.prototype.writeUInt16BE=function(a,b,c){return a=+a,b=0|b,c||I(this,a,b,2,65535,0),f.TYPED_ARRAY_SUPPORT?(this[b]=a>>>8,this[b+1]=255&a):J(this,a,b,!1),b+2},f.prototype.writeUInt32LE=function(a,b,c){return a=+a,b=0|b,c||I(this,a,b,4,4294967295,0),f.TYPED_ARRAY_SUPPORT?(this[b+3]=a>>>24,this[b+2]=a>>>16,this[b+1]=a>>>8,this[b]=255&a):K(this,a,b,!0),b+4},f.prototype.writeUInt32BE=function(a,b,c){return a=+a,b=0|b,c||I(this,a,b,4,4294967295,0),f.TYPED_ARRAY_SUPPORT?(this[b]=a>>>24,this[b+1]=a>>>16,this[b+2]=a>>>8,this[b+3]=255&a):K(this,a,b,!1),b+4},f.prototype.writeIntLE=function(a,b,c,d){if(a=+a,b=0|b,!d){var e=Math.pow(2,8*c-1);I(this,a,b,c,e-1,-e)}var f=0,g=1,h=0>a?1:0;for(this[b]=255&a;++f<c&&(g*=256);)this[b+f]=(a/g>>0)-h&255;return b+c},f.prototype.writeIntBE=function(a,b,c,d){if(a=+a,b=0|b,!d){var e=Math.pow(2,8*c-1);I(this,a,b,c,e-1,-e)}var f=c-1,g=1,h=0>a?1:0;for(this[b+f]=255&a;--f>=0&&(g*=256);)this[b+f]=(a/g>>0)-h&255;return b+c},f.prototype.writeInt8=function(a,b,c){return a=+a,b=0|b,c||I(this,a,b,1,127,-128),f.TYPED_ARRAY_SUPPORT||(a=Math.floor(a)),0>a&&(a=255+a+1),this[b]=255&a,b+1},f.prototype.writeInt16LE=function(a,b,c){return a=+a,b=0|b,c||I(this,a,b,2,32767,-32768),f.TYPED_ARRAY_SUPPORT?(this[b]=255&a,this[b+1]=a>>>8):J(this,a,b,!0),b+2},f.prototype.writeInt16BE=function(a,b,c){return a=+a,b=0|b,c||I(this,a,b,2,32767,-32768),f.TYPED_ARRAY_SUPPORT?(this[b]=a>>>8,this[b+1]=255&a):J(this,a,b,!1),b+2},f.prototype.writeInt32LE=function(a,b,c){return a=+a,b=0|b,c||I(this,a,b,4,2147483647,-2147483648),f.TYPED_ARRAY_SUPPORT?(this[b]=255&a,this[b+1]=a>>>8,this[b+2]=a>>>16,this[b+3]=a>>>24):K(this,a,b,!0),b+4},f.prototype.writeInt32BE=function(a,b,c){return a=+a,b=0|b,c||I(this,a,b,4,2147483647,-2147483648),0>a&&(a=4294967295+a+1),f.TYPED_ARRAY_SUPPORT?(this[b]=a>>>24,this[b+1]=a>>>16,this[b+2]=a>>>8,this[b+3]=255&a):K(this,a,b,!1),b+4},f.prototype.writeFloatLE=function(a,b,c){return M(this,a,b,!0,c)},f.prototype.writeFloatBE=function(a,b,c){return M(this,a,b,!1,c)},f.prototype.writeDoubleLE=function(a,b,c){return N(this,a,b,!0,c)},f.prototype.writeDoubleBE=function(a,b,c){return N(this,a,b,!1,c)},f.prototype.copy=function(a,b,c,d){if(c||(c=0),d||0===d||(d=this.length),b>=a.length&&(b=a.length),b||(b=0),d>0&&c>d&&(d=c),d===c)return 0;if(0===a.length||0===this.length)return 0;if(0>b)throw new RangeError("targetStart out of bounds");if(0>c||c>=this.length)throw new RangeError("sourceStart out of bounds");if(0>d)throw new RangeError("sourceEnd out of bounds");d>this.length&&(d=this.length),a.length-b<d-c&&(d=a.length-b+c);var e,g=d-c;if(this===a&&b>c&&d>b)for(e=g-1;e>=0;e--)a[e+b]=this[e+c];else if(1e3>g||!f.TYPED_ARRAY_SUPPORT)for(e=0;g>e;e++)a[e+b]=this[e+c];else Uint8Array.prototype.set.call(a,this.subarray(c,c+g),b);return g},f.prototype.fill=function(a,b,c){if(a||(a=0),b||(b=0),c||(c=this.length),b>c)throw new RangeError("end < start");if(c!==b&&0!==this.length){if(0>b||b>=this.length)throw new RangeError("start out of bounds");if(0>c||c>this.length)throw new RangeError("end out of bounds");var d;if("number"==typeof a)for(d=b;c>d;d++)this[d]=a;else{var e=R(a.toString()),f=e.length;for(d=b;c>d;d++)this[d]=e[d%f]}return this}};var _=/[^+\/0-9A-Za-z-_]/g}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"base64-js":4,ieee754:5,isarray:6}],4:[function(a,b,c){!function(a){"use strict";function b(a){var b=k[a.charCodeAt(0)];return void 0!==b?b:-1}function c(a){function c(a){i[k++]=a}var d,e,f,g,h,i;if(a.length%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var j=a.length;h="="===a.charAt(j-2)?2:"="===a.charAt(j-1)?1:0,i=new l(3*a.length/4-h),f=h>0?a.length-4:a.length;var k=0;for(d=0,e=0;f>d;d+=4,e+=3)g=b(a.charAt(d))<<18|b(a.charAt(d+1))<<12|b(a.charAt(d+2))<<6|b(a.charAt(d+3)),c((16711680&g)>>16),c((65280&g)>>8),c(255&g);return 2===h?(g=b(a.charAt(d))<<2|b(a.charAt(d+1))>>4,c(255&g)):1===h&&(g=b(a.charAt(d))<<10|b(a.charAt(d+1))<<4|b(a.charAt(d+2))>>2,c(g>>8&255),c(255&g)),i}function d(a){return j[a]}function e(a){return d(a>>18&63)+d(a>>12&63)+d(a>>6&63)+d(63&a)}function f(a,b,c){for(var d,f=[],g=b;c>g;g+=3)d=(a[g]<<16)+(a[g+1]<<8)+a[g+2],f.push(e(d));return f.join("")}function g(a){var b,c,e,g=a.length%3,h="",i=[],j=16383;for(b=0,e=a.length-g;e>b;b+=j)i.push(f(a,b,b+j>e?e:b+j));switch(g){case 1:c=a[a.length-1],h+=d(c>>2),h+=d(c<<4&63),h+="==";break;case 2:c=(a[a.length-2]<<8)+a[a.length-1],h+=d(c>>10),h+=d(c>>4&63),h+=d(c<<2&63),h+="="}return i.push(h),i.join("")}var h,i="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",j=[];for(h=0;h<i.length;h++)j[h]=i[h];var k=[];for(h=0;h<i.length;++h)k[i.charCodeAt(h)]=h;k["-".charCodeAt(0)]=62,k["_".charCodeAt(0)]=63;var l="undefined"!=typeof Uint8Array?Uint8Array:Array;a.toByteArray=c,a.fromByteArray=g}("undefined"==typeof c?this.base64js={}:c)},{}],5:[function(a,b,c){c.read=function(a,b,c,d,e){var f,g,h=8*e-d-1,i=(1<<h)-1,j=i>>1,k=-7,l=c?e-1:0,m=c?-1:1,n=a[b+l];for(l+=m,f=n&(1<<-k)-1,n>>=-k,k+=h;k>0;f=256*f+a[b+l],l+=m,k-=8);for(g=f&(1<<-k)-1,f>>=-k,k+=d;k>0;g=256*g+a[b+l],l+=m,k-=8);if(0===f)f=1-j;else{if(f===i)return g?NaN:(n?-1:1)*(1/0);g+=Math.pow(2,d),f-=j}return(n?-1:1)*g*Math.pow(2,f-d)},c.write=function(a,b,c,d,e,f){var g,h,i,j=8*f-e-1,k=(1<<j)-1,l=k>>1,m=23===e?Math.pow(2,-24)-Math.pow(2,-77):0,n=d?0:f-1,o=d?1:-1,p=0>b||0===b&&0>1/b?1:0;for(b=Math.abs(b),isNaN(b)||b===1/0?(h=isNaN(b)?1:0,g=k):(g=Math.floor(Math.log(b)/Math.LN2),b*(i=Math.pow(2,-g))<1&&(g--,i*=2),b+=g+l>=1?m/i:m*Math.pow(2,1-l),b*i>=2&&(g++,i/=2),g+l>=k?(h=0,g=k):g+l>=1?(h=(b*i-1)*Math.pow(2,e),g+=l):(h=b*Math.pow(2,l-1)*Math.pow(2,e),g=0));e>=8;a[c+n]=255&h,n+=o,h/=256,e-=8);for(g=g<<e|h,j+=e;j>0;a[c+n]=255&g,n+=o,g/=256,j-=8);a[c+n-o]|=128*p}},{}],6:[function(a,b,c){var d={}.toString;b.exports=Array.isArray||function(a){return"[object Array]"==d.call(a)}},{}],7:[function(a,b,c){function d(){this._events=this._events||{},this._maxListeners=this._maxListeners||void 0}function e(a){return"function"==typeof a}function f(a){return"number"==typeof a}function g(a){return"object"==typeof a&&null!==a}function h(a){return void 0===a}b.exports=d,d.EventEmitter=d,d.prototype._events=void 0,d.prototype._maxListeners=void 0,d.defaultMaxListeners=10,d.prototype.setMaxListeners=function(a){if(!f(a)||0>a||isNaN(a))throw TypeError("n must be a positive number");return this._maxListeners=a,this},d.prototype.emit=function(a){var b,c,d,f,i,j;if(this._events||(this._events={}),"error"===a&&(!this._events.error||g(this._events.error)&&!this._events.error.length)){if(b=arguments[1],b instanceof Error)throw b;throw TypeError('Uncaught, unspecified "error" event.')}if(c=this._events[a],h(c))return!1;if(e(c))switch(arguments.length){case 1:c.call(this);break;case 2:c.call(this,arguments[1]);break;case 3:c.call(this,arguments[1],arguments[2]);break;default:f=Array.prototype.slice.call(arguments,1),c.apply(this,f)}else if(g(c))for(f=Array.prototype.slice.call(arguments,1),j=c.slice(),d=j.length,i=0;d>i;i++)j[i].apply(this,f);return!0},d.prototype.addListener=function(a,b){var c;if(!e(b))throw TypeError("listener must be a function");return this._events||(this._events={}),this._events.newListener&&this.emit("newListener",a,e(b.listener)?b.listener:b),this._events[a]?g(this._events[a])?this._events[a].push(b):this._events[a]=[this._events[a],b]:this._events[a]=b,g(this._events[a])&&!this._events[a].warned&&(c=h(this._maxListeners)?d.defaultMaxListeners:this._maxListeners,c&&c>0&&this._events[a].length>c&&(this._events[a].warned=!0,console.error("(node) warning: possible EventEmitter memory leak detected. %d listeners added. Use emitter.setMaxListeners() to increase limit.",this._events[a].length),"function"==typeof console.trace&&console.trace())),this},d.prototype.on=d.prototype.addListener,d.prototype.once=function(a,b){function c(){this.removeListener(a,c),d||(d=!0,b.apply(this,arguments))}if(!e(b))throw TypeError("listener must be a function");var d=!1;return c.listener=b,this.on(a,c),this},d.prototype.removeListener=function(a,b){var c,d,f,h;if(!e(b))throw TypeError("listener must be a function");if(!this._events||!this._events[a])return this;if(c=this._events[a],f=c.length,d=-1,c===b||e(c.listener)&&c.listener===b)delete this._events[a],this._events.removeListener&&this.emit("removeListener",a,b);else if(g(c)){for(h=f;h-->0;)if(c[h]===b||c[h].listener&&c[h].listener===b){d=h;break}if(0>d)return this;1===c.length?(c.length=0,delete this._events[a]):c.splice(d,1),this._events.removeListener&&this.emit("removeListener",a,b)}return this},d.prototype.removeAllListeners=function(a){var b,c;if(!this._events)return this;if(!this._events.removeListener)return 0===arguments.length?this._events={}:this._events[a]&&delete this._events[a],this;if(0===arguments.length){for(b in this._events)"removeListener"!==b&&this.removeAllListeners(b);return this.removeAllListeners("removeListener"),this._events={},this}if(c=this._events[a],e(c))this.removeListener(a,c);else if(c)for(;c.length;)this.removeListener(a,c[c.length-1]);return delete this._events[a],this},d.prototype.listeners=function(a){var b;return b=this._events&&this._events[a]?e(this._events[a])?[this._events[a]]:this._events[a].slice():[]},d.prototype.listenerCount=function(a){if(this._events){var b=this._events[a];if(e(b))return 1;if(b)return b.length}return 0},d.listenerCount=function(a,b){return a.listenerCount(b)}},{}],8:[function(a,b,c){"function"==typeof Object.create?b.exports=function(a,b){a.super_=b,a.prototype=Object.create(b.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}})}:b.exports=function(a,b){a.super_=b;var c=function(){};c.prototype=b.prototype,a.prototype=new c,a.prototype.constructor=a}},{}],9:[function(a,b,c){b.exports=function(a){return!(null==a||!(a._isBuffer||a.constructor&&"function"==typeof a.constructor.isBuffer&&a.constructor.isBuffer(a)))}},{}],10:[function(a,b,c){b.exports=Array.isArray||function(a){return"[object Array]"==Object.prototype.toString.call(a)}},{}],11:[function(a,b,c){function d(){k=!1,h.length?j=h.concat(j):l=-1,j.length&&e()}function e(){if(!k){var a=setTimeout(d);k=!0;for(var b=j.length;b;){for(h=j,j=[];++l<b;)h&&h[l].run();l=-1,b=j.length}h=null,k=!1,clearTimeout(a)}}function f(a,b){this.fun=a,this.array=b}function g(){}var h,i=b.exports={},j=[],k=!1,l=-1;i.nextTick=function(a){var b=new Array(arguments.length-1);if(arguments.length>1)for(var c=1;c<arguments.length;c++)b[c-1]=arguments[c];j.push(new f(a,b)),1!==j.length||k||setTimeout(e,0)},f.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=g,i.addListener=g,i.once=g,i.off=g,i.removeListener=g,i.removeAllListeners=g,i.emit=g,i.binding=function(a){throw new Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(a){throw new Error("process.chdir is not supported")},i.umask=function(){return 0}},{}],12:[function(a,b,c){b.exports=a("./lib/_stream_duplex.js")},{"./lib/_stream_duplex.js":13}],13:[function(a,b,c){"use strict";function d(a){return this instanceof d?(j.call(this,a),k.call(this,a),a&&a.readable===!1&&(this.readable=!1),a&&a.writable===!1&&(this.writable=!1),this.allowHalfOpen=!0,a&&a.allowHalfOpen===!1&&(this.allowHalfOpen=!1),void this.once("end",e)):new d(a)}function e(){this.allowHalfOpen||this._writableState.ended||h(f,this)}function f(a){a.end()}var g=Object.keys||function(a){var b=[];for(var c in a)b.push(c);return b};b.exports=d;var h=a("process-nextick-args"),i=a("core-util-is");i.inherits=a("inherits");var j=a("./_stream_readable"),k=a("./_stream_writable");i.inherits(d,j);for(var l=g(k.prototype),m=0;m<l.length;m++){var n=l[m];d.prototype[n]||(d.prototype[n]=k.prototype[n])}},{"./_stream_readable":15,"./_stream_writable":17,"core-util-is":18,inherits:8,"process-nextick-args":19}],14:[function(a,b,c){"use strict";function d(a){return this instanceof d?void e.call(this,a):new d(a)}b.exports=d;var e=a("./_stream_transform"),f=a("core-util-is");f.inherits=a("inherits"),f.inherits(d,e),d.prototype._transform=function(a,b,c){c(null,a)}},{"./_stream_transform":16,"core-util-is":18,inherits:8}],15:[function(a,b,c){(function(c){"use strict";function d(b,c){I=I||a("./_stream_duplex"),b=b||{},this.objectMode=!!b.objectMode,c instanceof I&&(this.objectMode=this.objectMode||!!b.readableObjectMode);var d=b.highWaterMark,e=this.objectMode?16:16384;this.highWaterMark=d||0===d?d:e,this.highWaterMark=~~this.highWaterMark,this.buffer=[],this.length=0,this.pipes=null,this.pipesCount=0,this.flowing=null,this.ended=!1,this.endEmitted=!1,this.reading=!1,this.sync=!0,this.needReadable=!1,this.emittedReadable=!1,this.readableListening=!1,this.defaultEncoding=b.defaultEncoding||"utf8",this.ranOut=!1,this.awaitDrain=0,this.readingMore=!1,this.decoder=null,this.encoding=null,b.encoding&&(H||(H=a("string_decoder/").StringDecoder),this.decoder=new H(b.encoding),this.encoding=b.encoding)}function e(b){return I=I||a("./_stream_duplex"),this instanceof e?(this._readableState=new d(b,this),this.readable=!0,b&&"function"==typeof b.read&&(this._read=b.read),void C.call(this)):new e(b)}function f(a,b,c,d,e){var f=j(b,c);if(f)a.emit("error",f);else if(null===c)b.reading=!1,k(a,b);else if(b.objectMode||c&&c.length>0)if(b.ended&&!e){var h=new Error("stream.push() after EOF");a.emit("error",h)}else if(b.endEmitted&&e){var h=new Error("stream.unshift() after end event");a.emit("error",h)}else!b.decoder||e||d||(c=b.decoder.write(c)),e||(b.reading=!1),b.flowing&&0===b.length&&!b.sync?(a.emit("data",c),a.read(0)):(b.length+=b.objectMode?1:c.length,e?b.buffer.unshift(c):b.buffer.push(c),b.needReadable&&l(a)),n(a,b);else e||(b.reading=!1);return g(b)}function g(a){return!a.ended&&(a.needReadable||a.length<a.highWaterMark||0===a.length)}function h(a){return a>=J?a=J:(a--,a|=a>>>1,a|=a>>>2,a|=a>>>4,a|=a>>>8,a|=a>>>16,a++),a}function i(a,b){return 0===b.length&&b.ended?0:b.objectMode?0===a?0:1:null===a||isNaN(a)?b.flowing&&b.buffer.length?b.buffer[0].length:b.length:0>=a?0:(a>b.highWaterMark&&(b.highWaterMark=h(a)),a>b.length?b.ended?b.length:(b.needReadable=!0,0):a)}function j(a,b){var c=null;return B.isBuffer(b)||"string"==typeof b||null===b||void 0===b||a.objectMode||(c=new TypeError("Invalid non-string/buffer chunk")),c}function k(a,b){if(!b.ended){if(b.decoder){var c=b.decoder.end();c&&c.length&&(b.buffer.push(c),b.length+=b.objectMode?1:c.length)}b.ended=!0,l(a)}}function l(a){var b=a._readableState;b.needReadable=!1,b.emittedReadable||(F("emitReadable",b.flowing),b.emittedReadable=!0,b.sync?z(m,a):m(a))}function m(a){F("emit readable"),a.emit("readable"),t(a)}function n(a,b){b.readingMore||(b.readingMore=!0,z(o,a,b))}function o(a,b){for(var c=b.length;!b.reading&&!b.flowing&&!b.ended&&b.length<b.highWaterMark&&(F("maybeReadMore read 0"),a.read(0),c!==b.length);)c=b.length;b.readingMore=!1}function p(a){return function(){var b=a._readableState;F("pipeOnDrain",b.awaitDrain),b.awaitDrain&&b.awaitDrain--,0===b.awaitDrain&&D(a,"data")&&(b.flowing=!0,t(a))}}function q(a){F("readable nexttick read 0"),a.read(0)}function r(a,b){b.resumeScheduled||(b.resumeScheduled=!0,z(s,a,b))}function s(a,b){b.reading||(F("resume read 0"),a.read(0)),b.resumeScheduled=!1,a.emit("resume"),t(a),b.flowing&&!b.reading&&a.read(0)}function t(a){var b=a._readableState;if(F("flow",b.flowing),b.flowing)do var c=a.read();while(null!==c&&b.flowing)}function u(a,b){var c,d=b.buffer,e=b.length,f=!!b.decoder,g=!!b.objectMode;if(0===d.length)return null;if(0===e)c=null;else if(g)c=d.shift();else if(!a||a>=e)c=f?d.join(""):1===d.length?d[0]:B.concat(d,e),d.length=0;else if(a<d[0].length){var h=d[0];c=h.slice(0,a),d[0]=h.slice(a)}else if(a===d[0].length)c=d.shift();else{c=f?"":new B(a);for(var i=0,j=0,k=d.length;k>j&&a>i;j++){var h=d[0],l=Math.min(a-i,h.length);f?c+=h.slice(0,l):h.copy(c,i,0,l),l<h.length?d[0]=h.slice(l):d.shift(),i+=l}}return c}function v(a){var b=a._readableState;if(b.length>0)throw new Error("endReadable called on non-empty stream");b.endEmitted||(b.ended=!0,z(w,b,a))}function w(a,b){a.endEmitted||0!==a.length||(a.endEmitted=!0,b.readable=!1,b.emit("end"))}function x(a,b){for(var c=0,d=a.length;d>c;c++)b(a[c],c)}function y(a,b){for(var c=0,d=a.length;d>c;c++)if(a[c]===b)return c;return-1}b.exports=e;var z=a("process-nextick-args"),A=a("isarray"),B=a("buffer").Buffer;e.ReadableState=d;var C,D=(a("events"),function(a,b){return a.listeners(b).length});!function(){try{C=a("stream")}catch(b){}finally{C||(C=a("events").EventEmitter)}}();var B=a("buffer").Buffer,E=a("core-util-is");E.inherits=a("inherits");var F,G=a("util");F=G&&G.debuglog?G.debuglog("stream"):function(){};var H;E.inherits(e,C);var I,I;e.prototype.push=function(a,b){var c=this._readableState;return c.objectMode||"string"!=typeof a||(b=b||c.defaultEncoding,b!==c.encoding&&(a=new B(a,b),
b="")),f(this,c,a,b,!1)},e.prototype.unshift=function(a){var b=this._readableState;return f(this,b,a,"",!0)},e.prototype.isPaused=function(){return this._readableState.flowing===!1},e.prototype.setEncoding=function(b){return H||(H=a("string_decoder/").StringDecoder),this._readableState.decoder=new H(b),this._readableState.encoding=b,this};var J=8388608;e.prototype.read=function(a){F("read",a);var b=this._readableState,c=a;if(("number"!=typeof a||a>0)&&(b.emittedReadable=!1),0===a&&b.needReadable&&(b.length>=b.highWaterMark||b.ended))return F("read: emitReadable",b.length,b.ended),0===b.length&&b.ended?v(this):l(this),null;if(a=i(a,b),0===a&&b.ended)return 0===b.length&&v(this),null;var d=b.needReadable;F("need readable",d),(0===b.length||b.length-a<b.highWaterMark)&&(d=!0,F("length less than watermark",d)),(b.ended||b.reading)&&(d=!1,F("reading or ended",d)),d&&(F("do read"),b.reading=!0,b.sync=!0,0===b.length&&(b.needReadable=!0),this._read(b.highWaterMark),b.sync=!1),d&&!b.reading&&(a=i(c,b));var e;return e=a>0?u(a,b):null,null===e&&(b.needReadable=!0,a=0),b.length-=a,0!==b.length||b.ended||(b.needReadable=!0),c!==a&&b.ended&&0===b.length&&v(this),null!==e&&this.emit("data",e),e},e.prototype._read=function(a){this.emit("error",new Error("not implemented"))},e.prototype.pipe=function(a,b){function d(a){F("onunpipe"),a===l&&f()}function e(){F("onend"),a.end()}function f(){F("cleanup"),a.removeListener("close",i),a.removeListener("finish",j),a.removeListener("drain",q),a.removeListener("error",h),a.removeListener("unpipe",d),l.removeListener("end",e),l.removeListener("end",f),l.removeListener("data",g),r=!0,!m.awaitDrain||a._writableState&&!a._writableState.needDrain||q()}function g(b){F("ondata");var c=a.write(b);!1===c&&(1!==m.pipesCount||m.pipes[0]!==a||1!==l.listenerCount("data")||r||(F("false write response, pause",l._readableState.awaitDrain),l._readableState.awaitDrain++),l.pause())}function h(b){F("onerror",b),k(),a.removeListener("error",h),0===D(a,"error")&&a.emit("error",b)}function i(){a.removeListener("finish",j),k()}function j(){F("onfinish"),a.removeListener("close",i),k()}function k(){F("unpipe"),l.unpipe(a)}var l=this,m=this._readableState;switch(m.pipesCount){case 0:m.pipes=a;break;case 1:m.pipes=[m.pipes,a];break;default:m.pipes.push(a)}m.pipesCount+=1,F("pipe count=%d opts=%j",m.pipesCount,b);var n=(!b||b.end!==!1)&&a!==c.stdout&&a!==c.stderr,o=n?e:f;m.endEmitted?z(o):l.once("end",o),a.on("unpipe",d);var q=p(l);a.on("drain",q);var r=!1;return l.on("data",g),a._events&&a._events.error?A(a._events.error)?a._events.error.unshift(h):a._events.error=[h,a._events.error]:a.on("error",h),a.once("close",i),a.once("finish",j),a.emit("pipe",l),m.flowing||(F("pipe resume"),l.resume()),a},e.prototype.unpipe=function(a){var b=this._readableState;if(0===b.pipesCount)return this;if(1===b.pipesCount)return a&&a!==b.pipes?this:(a||(a=b.pipes),b.pipes=null,b.pipesCount=0,b.flowing=!1,a&&a.emit("unpipe",this),this);if(!a){var c=b.pipes,d=b.pipesCount;b.pipes=null,b.pipesCount=0,b.flowing=!1;for(var e=0;d>e;e++)c[e].emit("unpipe",this);return this}var e=y(b.pipes,a);return-1===e?this:(b.pipes.splice(e,1),b.pipesCount-=1,1===b.pipesCount&&(b.pipes=b.pipes[0]),a.emit("unpipe",this),this)},e.prototype.on=function(a,b){var c=C.prototype.on.call(this,a,b);if("data"===a&&!1!==this._readableState.flowing&&this.resume(),"readable"===a&&this.readable){var d=this._readableState;d.readableListening||(d.readableListening=!0,d.emittedReadable=!1,d.needReadable=!0,d.reading?d.length&&l(this,d):z(q,this))}return c},e.prototype.addListener=e.prototype.on,e.prototype.resume=function(){var a=this._readableState;return a.flowing||(F("resume"),a.flowing=!0,r(this,a)),this},e.prototype.pause=function(){return F("call pause flowing=%j",this._readableState.flowing),!1!==this._readableState.flowing&&(F("pause"),this._readableState.flowing=!1,this.emit("pause")),this},e.prototype.wrap=function(a){var b=this._readableState,c=!1,d=this;a.on("end",function(){if(F("wrapped end"),b.decoder&&!b.ended){var a=b.decoder.end();a&&a.length&&d.push(a)}d.push(null)}),a.on("data",function(e){if(F("wrapped data"),b.decoder&&(e=b.decoder.write(e)),(!b.objectMode||null!==e&&void 0!==e)&&(b.objectMode||e&&e.length)){var f=d.push(e);f||(c=!0,a.pause())}});for(var e in a)void 0===this[e]&&"function"==typeof a[e]&&(this[e]=function(b){return function(){return a[b].apply(a,arguments)}}(e));var f=["error","close","destroy","pause","resume"];return x(f,function(b){a.on(b,d.emit.bind(d,b))}),d._read=function(b){F("wrapped _read",b),c&&(c=!1,a.resume())},d},e._fromList=u}).call(this,a("_process"))},{"./_stream_duplex":13,_process:11,buffer:3,"core-util-is":18,events:7,inherits:8,isarray:10,"process-nextick-args":19,"string_decoder/":26,util:2}],16:[function(a,b,c){"use strict";function d(a){this.afterTransform=function(b,c){return e(a,b,c)},this.needTransform=!1,this.transforming=!1,this.writecb=null,this.writechunk=null}function e(a,b,c){var d=a._transformState;d.transforming=!1;var e=d.writecb;if(!e)return a.emit("error",new Error("no writecb in Transform class"));d.writechunk=null,d.writecb=null,null!==c&&void 0!==c&&a.push(c),e&&e(b);var f=a._readableState;f.reading=!1,(f.needReadable||f.length<f.highWaterMark)&&a._read(f.highWaterMark)}function f(a){if(!(this instanceof f))return new f(a);h.call(this,a),this._transformState=new d(this);var b=this;this._readableState.needReadable=!0,this._readableState.sync=!1,a&&("function"==typeof a.transform&&(this._transform=a.transform),"function"==typeof a.flush&&(this._flush=a.flush)),this.once("prefinish",function(){"function"==typeof this._flush?this._flush(function(a){g(b,a)}):g(b)})}function g(a,b){if(b)return a.emit("error",b);var c=a._writableState,d=a._transformState;if(c.length)throw new Error("calling transform done when ws.length != 0");if(d.transforming)throw new Error("calling transform done when still transforming");return a.push(null)}b.exports=f;var h=a("./_stream_duplex"),i=a("core-util-is");i.inherits=a("inherits"),i.inherits(f,h),f.prototype.push=function(a,b){return this._transformState.needTransform=!1,h.prototype.push.call(this,a,b)},f.prototype._transform=function(a,b,c){throw new Error("not implemented")},f.prototype._write=function(a,b,c){var d=this._transformState;if(d.writecb=c,d.writechunk=a,d.writeencoding=b,!d.transforming){var e=this._readableState;(d.needTransform||e.needReadable||e.length<e.highWaterMark)&&this._read(e.highWaterMark)}},f.prototype._read=function(a){var b=this._transformState;null!==b.writechunk&&b.writecb&&!b.transforming?(b.transforming=!0,this._transform(b.writechunk,b.writeencoding,b.afterTransform)):b.needTransform=!0}},{"./_stream_duplex":13,"core-util-is":18,inherits:8}],17:[function(a,b,c){"use strict";function d(){}function e(a,b,c){this.chunk=a,this.encoding=b,this.callback=c,this.next=null}function f(b,c){B=B||a("./_stream_duplex"),b=b||{},this.objectMode=!!b.objectMode,c instanceof B&&(this.objectMode=this.objectMode||!!b.writableObjectMode);var d=b.highWaterMark,e=this.objectMode?16:16384;this.highWaterMark=d||0===d?d:e,this.highWaterMark=~~this.highWaterMark,this.needDrain=!1,this.ending=!1,this.ended=!1,this.finished=!1;var f=b.decodeStrings===!1;this.decodeStrings=!f,this.defaultEncoding=b.defaultEncoding||"utf8",this.length=0,this.writing=!1,this.corked=0,this.sync=!0,this.bufferProcessing=!1,this.onwrite=function(a){o(c,a)},this.writecb=null,this.writelen=0,this.bufferedRequest=null,this.lastBufferedRequest=null,this.pendingcb=0,this.prefinished=!1,this.errorEmitted=!1}function g(b){return B=B||a("./_stream_duplex"),this instanceof g||this instanceof B?(this._writableState=new f(b,this),this.writable=!0,b&&("function"==typeof b.write&&(this._write=b.write),"function"==typeof b.writev&&(this._writev=b.writev)),void z.call(this)):new g(b)}function h(a,b){var c=new Error("write after end");a.emit("error",c),w(b,c)}function i(a,b,c,d){var e=!0;if(!x.isBuffer(c)&&"string"!=typeof c&&null!==c&&void 0!==c&&!b.objectMode){var f=new TypeError("Invalid non-string/buffer chunk");a.emit("error",f),w(d,f),e=!1}return e}function j(a,b,c){return a.objectMode||a.decodeStrings===!1||"string"!=typeof b||(b=new x(b,c)),b}function k(a,b,c,d,f){c=j(b,c,d),x.isBuffer(c)&&(d="buffer");var g=b.objectMode?1:c.length;b.length+=g;var h=b.length<b.highWaterMark;if(h||(b.needDrain=!0),b.writing||b.corked){var i=b.lastBufferedRequest;b.lastBufferedRequest=new e(c,d,f),i?i.next=b.lastBufferedRequest:b.bufferedRequest=b.lastBufferedRequest}else l(a,b,!1,g,c,d,f);return h}function l(a,b,c,d,e,f,g){b.writelen=d,b.writecb=g,b.writing=!0,b.sync=!0,c?a._writev(e,b.onwrite):a._write(e,f,b.onwrite),b.sync=!1}function m(a,b,c,d,e){--b.pendingcb,c?w(e,d):e(d),a._writableState.errorEmitted=!0,a.emit("error",d)}function n(a){a.writing=!1,a.writecb=null,a.length-=a.writelen,a.writelen=0}function o(a,b){var c=a._writableState,d=c.sync,e=c.writecb;if(n(c),b)m(a,c,d,b,e);else{var f=s(c);f||c.corked||c.bufferProcessing||!c.bufferedRequest||r(a,c),d?w(p,a,c,f,e):p(a,c,f,e)}}function p(a,b,c,d){c||q(a,b),b.pendingcb--,d(),u(a,b)}function q(a,b){0===b.length&&b.needDrain&&(b.needDrain=!1,a.emit("drain"))}function r(a,b){b.bufferProcessing=!0;var c=b.bufferedRequest;if(a._writev&&c&&c.next){for(var d=[],e=[];c;)e.push(c.callback),d.push(c),c=c.next;b.pendingcb++,b.lastBufferedRequest=null,l(a,b,!0,b.length,d,"",function(a){for(var c=0;c<e.length;c++)b.pendingcb--,e[c](a)})}else{for(;c;){var f=c.chunk,g=c.encoding,h=c.callback,i=b.objectMode?1:f.length;if(l(a,b,!1,i,f,g,h),c=c.next,b.writing)break}null===c&&(b.lastBufferedRequest=null)}b.bufferedRequest=c,b.bufferProcessing=!1}function s(a){return a.ending&&0===a.length&&null===a.bufferedRequest&&!a.finished&&!a.writing}function t(a,b){b.prefinished||(b.prefinished=!0,a.emit("prefinish"))}function u(a,b){var c=s(b);return c&&(0===b.pendingcb?(t(a,b),b.finished=!0,a.emit("finish")):t(a,b)),c}function v(a,b,c){b.ending=!0,u(a,b),c&&(b.finished?w(c):a.once("finish",c)),b.ended=!0}b.exports=g;var w=a("process-nextick-args"),x=a("buffer").Buffer;g.WritableState=f;var y=a("core-util-is");y.inherits=a("inherits");var z,A={deprecate:a("util-deprecate")};!function(){try{z=a("stream")}catch(b){}finally{z||(z=a("events").EventEmitter)}}();var x=a("buffer").Buffer;y.inherits(g,z);var B;f.prototype.getBuffer=function(){for(var a=this.bufferedRequest,b=[];a;)b.push(a),a=a.next;return b},function(){try{Object.defineProperty(f.prototype,"buffer",{get:A.deprecate(function(){return this.getBuffer()},"_writableState.buffer is deprecated. Use _writableState.getBuffer instead.")})}catch(a){}}();var B;g.prototype.pipe=function(){this.emit("error",new Error("Cannot pipe. Not readable."))},g.prototype.write=function(a,b,c){var e=this._writableState,f=!1;return"function"==typeof b&&(c=b,b=null),x.isBuffer(a)?b="buffer":b||(b=e.defaultEncoding),"function"!=typeof c&&(c=d),e.ended?h(this,c):i(this,e,a,c)&&(e.pendingcb++,f=k(this,e,a,b,c)),f},g.prototype.cork=function(){var a=this._writableState;a.corked++},g.prototype.uncork=function(){var a=this._writableState;a.corked&&(a.corked--,a.writing||a.corked||a.finished||a.bufferProcessing||!a.bufferedRequest||r(this,a))},g.prototype.setDefaultEncoding=function(a){if("string"==typeof a&&(a=a.toLowerCase()),!(["hex","utf8","utf-8","ascii","binary","base64","ucs2","ucs-2","utf16le","utf-16le","raw"].indexOf((a+"").toLowerCase())>-1))throw new TypeError("Unknown encoding: "+a);this._writableState.defaultEncoding=a},g.prototype._write=function(a,b,c){c(new Error("not implemented"))},g.prototype._writev=null,g.prototype.end=function(a,b,c){var d=this._writableState;"function"==typeof a?(c=a,a=null,b=null):"function"==typeof b&&(c=b,b=null),null!==a&&void 0!==a&&this.write(a,b),d.corked&&(d.corked=1,this.uncork()),d.ending||d.finished||v(this,d,c)}},{"./_stream_duplex":13,buffer:3,"core-util-is":18,events:7,inherits:8,"process-nextick-args":19,"util-deprecate":20}],18:[function(a,b,c){(function(a){function b(a){return Array.isArray?Array.isArray(a):"[object Array]"===q(a)}function d(a){return"boolean"==typeof a}function e(a){return null===a}function f(a){return null==a}function g(a){return"number"==typeof a}function h(a){return"string"==typeof a}function i(a){return"symbol"==typeof a}function j(a){return void 0===a}function k(a){return"[object RegExp]"===q(a)}function l(a){return"object"==typeof a&&null!==a}function m(a){return"[object Date]"===q(a)}function n(a){return"[object Error]"===q(a)||a instanceof Error}function o(a){return"function"==typeof a}function p(a){return null===a||"boolean"==typeof a||"number"==typeof a||"string"==typeof a||"symbol"==typeof a||"undefined"==typeof a}function q(a){return Object.prototype.toString.call(a)}c.isArray=b,c.isBoolean=d,c.isNull=e,c.isNullOrUndefined=f,c.isNumber=g,c.isString=h,c.isSymbol=i,c.isUndefined=j,c.isRegExp=k,c.isObject=l,c.isDate=m,c.isError=n,c.isFunction=o,c.isPrimitive=p,c.isBuffer=a.isBuffer}).call(this,{isBuffer:a("../../../../insert-module-globals/node_modules/is-buffer/index.js")})},{"../../../../insert-module-globals/node_modules/is-buffer/index.js":9}],19:[function(a,b,c){(function(a){"use strict";function c(b){for(var c=new Array(arguments.length-1),d=0;d<c.length;)c[d++]=arguments[d];a.nextTick(function(){b.apply(null,c)})}!a.version||0===a.version.indexOf("v0.")||0===a.version.indexOf("v1.")&&0!==a.version.indexOf("v1.8.")?b.exports=c:b.exports=a.nextTick}).call(this,a("_process"))},{_process:11}],20:[function(a,b,c){(function(a){function c(a,b){function c(){if(!e){if(d("throwDeprecation"))throw new Error(b);d("traceDeprecation")?console.trace(b):console.warn(b),e=!0}return a.apply(this,arguments)}if(d("noDeprecation"))return a;var e=!1;return c}function d(b){try{if(!a.localStorage)return!1}catch(c){return!1}var d=a.localStorage[b];return null==d?!1:"true"===String(d).toLowerCase()}b.exports=c}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],21:[function(a,b,c){b.exports=a("./lib/_stream_passthrough.js")},{"./lib/_stream_passthrough.js":14}],22:[function(a,b,c){var d=function(){try{return a("stream")}catch(b){}}();c=b.exports=a("./lib/_stream_readable.js"),c.Stream=d||c,c.Readable=c,c.Writable=a("./lib/_stream_writable.js"),c.Duplex=a("./lib/_stream_duplex.js"),c.Transform=a("./lib/_stream_transform.js"),c.PassThrough=a("./lib/_stream_passthrough.js")},{"./lib/_stream_duplex.js":13,"./lib/_stream_passthrough.js":14,"./lib/_stream_readable.js":15,"./lib/_stream_transform.js":16,"./lib/_stream_writable.js":17}],23:[function(a,b,c){b.exports=a("./lib/_stream_transform.js")},{"./lib/_stream_transform.js":16}],24:[function(a,b,c){b.exports=a("./lib/_stream_writable.js")},{"./lib/_stream_writable.js":17}],25:[function(a,b,c){function d(){e.call(this)}b.exports=d;var e=a("events").EventEmitter,f=a("inherits");f(d,e),d.Readable=a("readable-stream/readable.js"),d.Writable=a("readable-stream/writable.js"),d.Duplex=a("readable-stream/duplex.js"),d.Transform=a("readable-stream/transform.js"),d.PassThrough=a("readable-stream/passthrough.js"),d.Stream=d,d.prototype.pipe=function(a,b){function c(b){a.writable&&!1===a.write(b)&&j.pause&&j.pause()}function d(){j.readable&&j.resume&&j.resume()}function f(){k||(k=!0,a.end())}function g(){k||(k=!0,"function"==typeof a.destroy&&a.destroy())}function h(a){if(i(),0===e.listenerCount(this,"error"))throw a}function i(){j.removeListener("data",c),a.removeListener("drain",d),j.removeListener("end",f),j.removeListener("close",g),j.removeListener("error",h),a.removeListener("error",h),j.removeListener("end",i),j.removeListener("close",i),a.removeListener("close",i)}var j=this;j.on("data",c),a.on("drain",d),a._isStdio||b&&b.end===!1||(j.on("end",f),j.on("close",g));var k=!1;return j.on("error",h),a.on("error",h),j.on("end",i),j.on("close",i),a.on("close",i),a.emit("pipe",j),a}},{events:7,inherits:8,"readable-stream/duplex.js":12,"readable-stream/passthrough.js":21,"readable-stream/readable.js":22,"readable-stream/transform.js":23,"readable-stream/writable.js":24}],26:[function(a,b,c){function d(a){if(a&&!i(a))throw new Error("Unknown encoding: "+a)}function e(a){return a.toString(this.encoding)}function f(a){this.charReceived=a.length%2,this.charLength=this.charReceived?2:0}function g(a){this.charReceived=a.length%3,this.charLength=this.charReceived?3:0}var h=a("buffer").Buffer,i=h.isEncoding||function(a){switch(a&&a.toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":case"raw":return!0;default:return!1}},j=c.StringDecoder=function(a){switch(this.encoding=(a||"utf8").toLowerCase().replace(/[-_]/,""),d(a),this.encoding){case"utf8":this.surrogateSize=3;break;case"ucs2":case"utf16le":this.surrogateSize=2,this.detectIncompleteChar=f;break;case"base64":this.surrogateSize=3,this.detectIncompleteChar=g;break;default:return void(this.write=e)}this.charBuffer=new h(6),this.charReceived=0,this.charLength=0};j.prototype.write=function(a){for(var b="";this.charLength;){var c=a.length>=this.charLength-this.charReceived?this.charLength-this.charReceived:a.length;if(a.copy(this.charBuffer,this.charReceived,0,c),this.charReceived+=c,this.charReceived<this.charLength)return"";a=a.slice(c,a.length),b=this.charBuffer.slice(0,this.charLength).toString(this.encoding);var d=b.charCodeAt(b.length-1);if(!(d>=55296&&56319>=d)){if(this.charReceived=this.charLength=0,0===a.length)return b;break}this.charLength+=this.surrogateSize,b=""}this.detectIncompleteChar(a);var e=a.length;this.charLength&&(a.copy(this.charBuffer,0,a.length-this.charReceived,e),e-=this.charReceived),b+=a.toString(this.encoding,0,e);var e=b.length-1,d=b.charCodeAt(e);if(d>=55296&&56319>=d){var f=this.surrogateSize;return this.charLength+=f,this.charReceived+=f,this.charBuffer.copy(this.charBuffer,f,0,f),a.copy(this.charBuffer,0,0,f),b.substring(0,e)}return b},j.prototype.detectIncompleteChar=function(a){for(var b=a.length>=3?3:a.length;b>0;b--){var c=a[a.length-b];if(1==b&&c>>5==6){this.charLength=2;break}if(2>=b&&c>>4==14){this.charLength=3;break}if(3>=b&&c>>3==30){this.charLength=4;break}}this.charReceived=b},j.prototype.end=function(a){var b="";if(a&&a.length&&(b=this.write(a)),this.charReceived){var c=this.charReceived,d=this.charBuffer,e=this.encoding;b+=d.slice(0,c).toString(e)}return b}},{buffer:3}],27:[function(a,b,c){"use strict";var d=a("./lib/utils/common").assign,e=a("./lib/deflate"),f=a("./lib/inflate"),g=a("./lib/zlib/constants"),h={};d(h,e,f,g),b.exports=h},{"./lib/deflate":28,"./lib/inflate":29,"./lib/utils/common":30,"./lib/zlib/constants":33}],28:[function(a,b,c){"use strict";function d(a){if(!(this instanceof d))return new d(a);this.options=i.assign({level:s,method:u,chunkSize:16384,windowBits:15,memLevel:8,strategy:t,to:""},a||{});var b=this.options;b.raw&&b.windowBits>0?b.windowBits=-b.windowBits:b.gzip&&b.windowBits>0&&b.windowBits<16&&(b.windowBits+=16),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new l,this.strm.avail_out=0;var c=h.deflateInit2(this.strm,b.level,b.method,b.windowBits,b.memLevel,b.strategy);if(c!==p)throw new Error(k[c]);b.header&&h.deflateSetHeader(this.strm,b.header)}function e(a,b){var c=new d(b);if(c.push(a,!0),c.err)throw c.msg;return c.result}function f(a,b){return b=b||{},b.raw=!0,e(a,b)}function g(a,b){return b=b||{},b.gzip=!0,e(a,b)}var h=a("./zlib/deflate"),i=a("./utils/common"),j=a("./utils/strings"),k=a("./zlib/messages"),l=a("./zlib/zstream"),m=Object.prototype.toString,n=0,o=4,p=0,q=1,r=2,s=-1,t=0,u=8;d.prototype.push=function(a,b){var c,d,e=this.strm,f=this.options.chunkSize;if(this.ended)return!1;d=b===~~b?b:b===!0?o:n,"string"==typeof a?e.input=j.string2buf(a):"[object ArrayBuffer]"===m.call(a)?e.input=new Uint8Array(a):e.input=a,e.next_in=0,e.avail_in=e.input.length;do{if(0===e.avail_out&&(e.output=new i.Buf8(f),e.next_out=0,e.avail_out=f),c=h.deflate(e,d),c!==q&&c!==p)return this.onEnd(c),this.ended=!0,!1;(0===e.avail_out||0===e.avail_in&&(d===o||d===r))&&("string"===this.options.to?this.onData(j.buf2binstring(i.shrinkBuf(e.output,e.next_out))):this.onData(i.shrinkBuf(e.output,e.next_out)))}while((e.avail_in>0||0===e.avail_out)&&c!==q);return d===o?(c=h.deflateEnd(this.strm),this.onEnd(c),this.ended=!0,c===p):d===r?(this.onEnd(p),e.avail_out=0,!0):!0},d.prototype.onData=function(a){this.chunks.push(a)},d.prototype.onEnd=function(a){a===p&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=i.flattenChunks(this.chunks)),this.chunks=[],this.err=a,this.msg=this.strm.msg},c.Deflate=d,c.deflate=e,c.deflateRaw=f,c.gzip=g},{"./utils/common":30,"./utils/strings":31,"./zlib/deflate":35,"./zlib/messages":40,"./zlib/zstream":42}],29:[function(a,b,c){"use strict";function d(a){if(!(this instanceof d))return new d(a);this.options=h.assign({chunkSize:16384,windowBits:0,to:""},a||{});var b=this.options;b.raw&&b.windowBits>=0&&b.windowBits<16&&(b.windowBits=-b.windowBits,0===b.windowBits&&(b.windowBits=-15)),!(b.windowBits>=0&&b.windowBits<16)||a&&a.windowBits||(b.windowBits+=32),b.windowBits>15&&b.windowBits<48&&0===(15&b.windowBits)&&(b.windowBits|=15),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new l,this.strm.avail_out=0;var c=g.inflateInit2(this.strm,b.windowBits);if(c!==j.Z_OK)throw new Error(k[c]);this.header=new m,g.inflateGetHeader(this.strm,this.header)}function e(a,b){var c=new d(b);if(c.push(a,!0),c.err)throw c.msg;return c.result}function f(a,b){return b=b||{},b.raw=!0,e(a,b)}var g=a("./zlib/inflate"),h=a("./utils/common"),i=a("./utils/strings"),j=a("./zlib/constants"),k=a("./zlib/messages"),l=a("./zlib/zstream"),m=a("./zlib/gzheader"),n=Object.prototype.toString;d.prototype.push=function(a,b){var c,d,e,f,k,l=this.strm,m=this.options.chunkSize,o=!1;if(this.ended)return!1;d=b===~~b?b:b===!0?j.Z_FINISH:j.Z_NO_FLUSH,"string"==typeof a?l.input=i.binstring2buf(a):"[object ArrayBuffer]"===n.call(a)?l.input=new Uint8Array(a):l.input=a,l.next_in=0,l.avail_in=l.input.length;do{if(0===l.avail_out&&(l.output=new h.Buf8(m),l.next_out=0,l.avail_out=m),c=g.inflate(l,j.Z_NO_FLUSH),c===j.Z_BUF_ERROR&&o===!0&&(c=j.Z_OK,o=!1),c!==j.Z_STREAM_END&&c!==j.Z_OK)return this.onEnd(c),this.ended=!0,!1;l.next_out&&(0===l.avail_out||c===j.Z_STREAM_END||0===l.avail_in&&(d===j.Z_FINISH||d===j.Z_SYNC_FLUSH))&&("string"===this.options.to?(e=i.utf8border(l.output,l.next_out),f=l.next_out-e,k=i.buf2string(l.output,e),l.next_out=f,l.avail_out=m-f,f&&h.arraySet(l.output,l.output,e,f,0),this.onData(k)):this.onData(h.shrinkBuf(l.output,l.next_out))),0===l.avail_in&&0===l.avail_out&&(o=!0)}while((l.avail_in>0||0===l.avail_out)&&c!==j.Z_STREAM_END);return c===j.Z_STREAM_END&&(d=j.Z_FINISH),d===j.Z_FINISH?(c=g.inflateEnd(this.strm),this.onEnd(c),this.ended=!0,c===j.Z_OK):d===j.Z_SYNC_FLUSH?(this.onEnd(j.Z_OK),l.avail_out=0,!0):!0},d.prototype.onData=function(a){this.chunks.push(a)},d.prototype.onEnd=function(a){a===j.Z_OK&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=h.flattenChunks(this.chunks)),this.chunks=[],this.err=a,this.msg=this.strm.msg},c.Inflate=d,c.inflate=e,c.inflateRaw=f,c.ungzip=e},{"./utils/common":30,"./utils/strings":31,"./zlib/constants":33,"./zlib/gzheader":36,"./zlib/inflate":38,"./zlib/messages":40,"./zlib/zstream":42}],30:[function(a,b,c){"use strict";var d="undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint16Array&&"undefined"!=typeof Int32Array;c.assign=function(a){for(var b=Array.prototype.slice.call(arguments,1);b.length;){var c=b.shift();if(c){if("object"!=typeof c)throw new TypeError(c+"must be non-object");for(var d in c)c.hasOwnProperty(d)&&(a[d]=c[d])}}return a},c.shrinkBuf=function(a,b){return a.length===b?a:a.subarray?a.subarray(0,b):(a.length=b,a)};var e={arraySet:function(a,b,c,d,e){if(b.subarray&&a.subarray)return void a.set(b.subarray(c,c+d),e);for(var f=0;d>f;f++)a[e+f]=b[c+f]},flattenChunks:function(a){var b,c,d,e,f,g;for(d=0,b=0,c=a.length;c>b;b++)d+=a[b].length;for(g=new Uint8Array(d),e=0,b=0,c=a.length;c>b;b++)f=a[b],g.set(f,e),e+=f.length;return g}},f={arraySet:function(a,b,c,d,e){for(var f=0;d>f;f++)a[e+f]=b[c+f]},flattenChunks:function(a){return[].concat.apply([],a)}};c.setTyped=function(a){a?(c.Buf8=Uint8Array,c.Buf16=Uint16Array,c.Buf32=Int32Array,c.assign(c,e)):(c.Buf8=Array,c.Buf16=Array,c.Buf32=Array,c.assign(c,f))},c.setTyped(d)},{}],31:[function(a,b,c){"use strict";function d(a,b){if(65537>b&&(a.subarray&&g||!a.subarray&&f))return String.fromCharCode.apply(null,e.shrinkBuf(a,b));for(var c="",d=0;b>d;d++)c+=String.fromCharCode(a[d]);return c}var e=a("./common"),f=!0,g=!0;try{String.fromCharCode.apply(null,[0])}catch(h){f=!1}try{String.fromCharCode.apply(null,new Uint8Array(1))}catch(h){g=!1}for(var i=new e.Buf8(256),j=0;256>j;j++)i[j]=j>=252?6:j>=248?5:j>=240?4:j>=224?3:j>=192?2:1;i[254]=i[254]=1,c.string2buf=function(a){var b,c,d,f,g,h=a.length,i=0;for(f=0;h>f;f++)c=a.charCodeAt(f),55296===(64512&c)&&h>f+1&&(d=a.charCodeAt(f+1),56320===(64512&d)&&(c=65536+(c-55296<<10)+(d-56320),f++)),i+=128>c?1:2048>c?2:65536>c?3:4;for(b=new e.Buf8(i),g=0,f=0;i>g;f++)c=a.charCodeAt(f),55296===(64512&c)&&h>f+1&&(d=a.charCodeAt(f+1),56320===(64512&d)&&(c=65536+(c-55296<<10)+(d-56320),f++)),128>c?b[g++]=c:2048>c?(b[g++]=192|c>>>6,b[g++]=128|63&c):65536>c?(b[g++]=224|c>>>12,b[g++]=128|c>>>6&63,b[g++]=128|63&c):(b[g++]=240|c>>>18,b[g++]=128|c>>>12&63,b[g++]=128|c>>>6&63,b[g++]=128|63&c);return b},c.buf2binstring=function(a){return d(a,a.length)},c.binstring2buf=function(a){for(var b=new e.Buf8(a.length),c=0,d=b.length;d>c;c++)b[c]=a.charCodeAt(c);return b},c.buf2string=function(a,b){var c,e,f,g,h=b||a.length,j=new Array(2*h);for(e=0,c=0;h>c;)if(f=a[c++],128>f)j[e++]=f;else if(g=i[f],g>4)j[e++]=65533,c+=g-1;else{for(f&=2===g?31:3===g?15:7;g>1&&h>c;)f=f<<6|63&a[c++],g--;g>1?j[e++]=65533:65536>f?j[e++]=f:(f-=65536,j[e++]=55296|f>>10&1023,j[e++]=56320|1023&f)}return d(j,e)},c.utf8border=function(a,b){var c;for(b=b||a.length,b>a.length&&(b=a.length),c=b-1;c>=0&&128===(192&a[c]);)c--;return 0>c?b:0===c?b:c+i[a[c]]>b?c:b}},{"./common":30}],32:[function(a,b,c){"use strict";function d(a,b,c,d){for(var e=65535&a|0,f=a>>>16&65535|0,g=0;0!==c;){g=c>2e3?2e3:c,c-=g;do e=e+b[d++]|0,f=f+e|0;while(--g);e%=65521,f%=65521}return e|f<<16|0}b.exports=d},{}],33:[function(a,b,c){"use strict";b.exports={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8}},{}],34:[function(a,b,c){"use strict";function d(){for(var a,b=[],c=0;256>c;c++){a=c;for(var d=0;8>d;d++)a=1&a?3988292384^a>>>1:a>>>1;b[c]=a}return b}function e(a,b,c,d){var e=f,g=d+c;a^=-1;for(var h=d;g>h;h++)a=a>>>8^e[255&(a^b[h])];return-1^a}var f=d();b.exports=e},{}],35:[function(a,b,c){"use strict";function d(a,b){return a.msg=H[b],b}function e(a){return(a<<1)-(a>4?9:0)}function f(a){for(var b=a.length;--b>=0;)a[b]=0}function g(a){var b=a.state,c=b.pending;c>a.avail_out&&(c=a.avail_out),0!==c&&(D.arraySet(a.output,b.pending_buf,b.pending_out,c,a.next_out),a.next_out+=c,b.pending_out+=c,a.total_out+=c,a.avail_out-=c,b.pending-=c,0===b.pending&&(b.pending_out=0))}function h(a,b){E._tr_flush_block(a,a.block_start>=0?a.block_start:-1,a.strstart-a.block_start,b),a.block_start=a.strstart,g(a.strm)}function i(a,b){a.pending_buf[a.pending++]=b}function j(a,b){a.pending_buf[a.pending++]=b>>>8&255,a.pending_buf[a.pending++]=255&b}function k(a,b,c,d){var e=a.avail_in;return e>d&&(e=d),0===e?0:(a.avail_in-=e,D.arraySet(b,a.input,a.next_in,e,c),1===a.state.wrap?a.adler=F(a.adler,b,e,c):2===a.state.wrap&&(a.adler=G(a.adler,b,e,c)),a.next_in+=e,a.total_in+=e,e)}function l(a,b){var c,d,e=a.max_chain_length,f=a.strstart,g=a.prev_length,h=a.nice_match,i=a.strstart>a.w_size-ka?a.strstart-(a.w_size-ka):0,j=a.window,k=a.w_mask,l=a.prev,m=a.strstart+ja,n=j[f+g-1],o=j[f+g];a.prev_length>=a.good_match&&(e>>=2),h>a.lookahead&&(h=a.lookahead);do if(c=b,j[c+g]===o&&j[c+g-1]===n&&j[c]===j[f]&&j[++c]===j[f+1]){f+=2,c++;do;while(j[++f]===j[++c]&&j[++f]===j[++c]&&j[++f]===j[++c]&&j[++f]===j[++c]&&j[++f]===j[++c]&&j[++f]===j[++c]&&j[++f]===j[++c]&&j[++f]===j[++c]&&m>f);if(d=ja-(m-f),f=m-ja,d>g){if(a.match_start=b,g=d,d>=h)break;n=j[f+g-1],o=j[f+g]}}while((b=l[b&k])>i&&0!==--e);return g<=a.lookahead?g:a.lookahead}function m(a){var b,c,d,e,f,g=a.w_size;do{if(e=a.window_size-a.lookahead-a.strstart,a.strstart>=g+(g-ka)){D.arraySet(a.window,a.window,g,g,0),a.match_start-=g,a.strstart-=g,a.block_start-=g,c=a.hash_size,b=c;do d=a.head[--b],a.head[b]=d>=g?d-g:0;while(--c);c=g,b=c;do d=a.prev[--b],a.prev[b]=d>=g?d-g:0;while(--c);e+=g}if(0===a.strm.avail_in)break;if(c=k(a.strm,a.window,a.strstart+a.lookahead,e),a.lookahead+=c,a.lookahead+a.insert>=ia)for(f=a.strstart-a.insert,a.ins_h=a.window[f],a.ins_h=(a.ins_h<<a.hash_shift^a.window[f+1])&a.hash_mask;a.insert&&(a.ins_h=(a.ins_h<<a.hash_shift^a.window[f+ia-1])&a.hash_mask,a.prev[f&a.w_mask]=a.head[a.ins_h],a.head[a.ins_h]=f,f++,a.insert--,!(a.lookahead+a.insert<ia)););}while(a.lookahead<ka&&0!==a.strm.avail_in)}function n(a,b){var c=65535;for(c>a.pending_buf_size-5&&(c=a.pending_buf_size-5);;){if(a.lookahead<=1){if(m(a),0===a.lookahead&&b===I)return ta;if(0===a.lookahead)break}a.strstart+=a.lookahead,a.lookahead=0;var d=a.block_start+c;if((0===a.strstart||a.strstart>=d)&&(a.lookahead=a.strstart-d,a.strstart=d,h(a,!1),0===a.strm.avail_out))return ta;if(a.strstart-a.block_start>=a.w_size-ka&&(h(a,!1),0===a.strm.avail_out))return ta}return a.insert=0,b===L?(h(a,!0),0===a.strm.avail_out?va:wa):a.strstart>a.block_start&&(h(a,!1),0===a.strm.avail_out)?ta:ta}function o(a,b){for(var c,d;;){if(a.lookahead<ka){if(m(a),a.lookahead<ka&&b===I)return ta;if(0===a.lookahead)break}if(c=0,a.lookahead>=ia&&(a.ins_h=(a.ins_h<<a.hash_shift^a.window[a.strstart+ia-1])&a.hash_mask,c=a.prev[a.strstart&a.w_mask]=a.head[a.ins_h],a.head[a.ins_h]=a.strstart),0!==c&&a.strstart-c<=a.w_size-ka&&(a.match_length=l(a,c)),a.match_length>=ia)if(d=E._tr_tally(a,a.strstart-a.match_start,a.match_length-ia),a.lookahead-=a.match_length,a.match_length<=a.max_lazy_match&&a.lookahead>=ia){a.match_length--;do a.strstart++,a.ins_h=(a.ins_h<<a.hash_shift^a.window[a.strstart+ia-1])&a.hash_mask,c=a.prev[a.strstart&a.w_mask]=a.head[a.ins_h],a.head[a.ins_h]=a.strstart;while(0!==--a.match_length);a.strstart++}else a.strstart+=a.match_length,a.match_length=0,a.ins_h=a.window[a.strstart],a.ins_h=(a.ins_h<<a.hash_shift^a.window[a.strstart+1])&a.hash_mask;else d=E._tr_tally(a,0,a.window[a.strstart]),a.lookahead--,a.strstart++;if(d&&(h(a,!1),0===a.strm.avail_out))return ta}return a.insert=a.strstart<ia-1?a.strstart:ia-1,b===L?(h(a,!0),0===a.strm.avail_out?va:wa):a.last_lit&&(h(a,!1),0===a.strm.avail_out)?ta:ua}function p(a,b){for(var c,d,e;;){if(a.lookahead<ka){if(m(a),a.lookahead<ka&&b===I)return ta;if(0===a.lookahead)break}if(c=0,a.lookahead>=ia&&(a.ins_h=(a.ins_h<<a.hash_shift^a.window[a.strstart+ia-1])&a.hash_mask,c=a.prev[a.strstart&a.w_mask]=a.head[a.ins_h],a.head[a.ins_h]=a.strstart),a.prev_length=a.match_length,a.prev_match=a.match_start,a.match_length=ia-1,0!==c&&a.prev_length<a.max_lazy_match&&a.strstart-c<=a.w_size-ka&&(a.match_length=l(a,c),a.match_length<=5&&(a.strategy===T||a.match_length===ia&&a.strstart-a.match_start>4096)&&(a.match_length=ia-1)),a.prev_length>=ia&&a.match_length<=a.prev_length){e=a.strstart+a.lookahead-ia,d=E._tr_tally(a,a.strstart-1-a.prev_match,a.prev_length-ia),a.lookahead-=a.prev_length-1,a.prev_length-=2;do++a.strstart<=e&&(a.ins_h=(a.ins_h<<a.hash_shift^a.window[a.strstart+ia-1])&a.hash_mask,c=a.prev[a.strstart&a.w_mask]=a.head[a.ins_h],a.head[a.ins_h]=a.strstart);while(0!==--a.prev_length);if(a.match_available=0,a.match_length=ia-1,a.strstart++,d&&(h(a,!1),
0===a.strm.avail_out))return ta}else if(a.match_available){if(d=E._tr_tally(a,0,a.window[a.strstart-1]),d&&h(a,!1),a.strstart++,a.lookahead--,0===a.strm.avail_out)return ta}else a.match_available=1,a.strstart++,a.lookahead--}return a.match_available&&(d=E._tr_tally(a,0,a.window[a.strstart-1]),a.match_available=0),a.insert=a.strstart<ia-1?a.strstart:ia-1,b===L?(h(a,!0),0===a.strm.avail_out?va:wa):a.last_lit&&(h(a,!1),0===a.strm.avail_out)?ta:ua}function q(a,b){for(var c,d,e,f,g=a.window;;){if(a.lookahead<=ja){if(m(a),a.lookahead<=ja&&b===I)return ta;if(0===a.lookahead)break}if(a.match_length=0,a.lookahead>=ia&&a.strstart>0&&(e=a.strstart-1,d=g[e],d===g[++e]&&d===g[++e]&&d===g[++e])){f=a.strstart+ja;do;while(d===g[++e]&&d===g[++e]&&d===g[++e]&&d===g[++e]&&d===g[++e]&&d===g[++e]&&d===g[++e]&&d===g[++e]&&f>e);a.match_length=ja-(f-e),a.match_length>a.lookahead&&(a.match_length=a.lookahead)}if(a.match_length>=ia?(c=E._tr_tally(a,1,a.match_length-ia),a.lookahead-=a.match_length,a.strstart+=a.match_length,a.match_length=0):(c=E._tr_tally(a,0,a.window[a.strstart]),a.lookahead--,a.strstart++),c&&(h(a,!1),0===a.strm.avail_out))return ta}return a.insert=0,b===L?(h(a,!0),0===a.strm.avail_out?va:wa):a.last_lit&&(h(a,!1),0===a.strm.avail_out)?ta:ua}function r(a,b){for(var c;;){if(0===a.lookahead&&(m(a),0===a.lookahead)){if(b===I)return ta;break}if(a.match_length=0,c=E._tr_tally(a,0,a.window[a.strstart]),a.lookahead--,a.strstart++,c&&(h(a,!1),0===a.strm.avail_out))return ta}return a.insert=0,b===L?(h(a,!0),0===a.strm.avail_out?va:wa):a.last_lit&&(h(a,!1),0===a.strm.avail_out)?ta:ua}function s(a,b,c,d,e){this.good_length=a,this.max_lazy=b,this.nice_length=c,this.max_chain=d,this.func=e}function t(a){a.window_size=2*a.w_size,f(a.head),a.max_lazy_match=C[a.level].max_lazy,a.good_match=C[a.level].good_length,a.nice_match=C[a.level].nice_length,a.max_chain_length=C[a.level].max_chain,a.strstart=0,a.block_start=0,a.lookahead=0,a.insert=0,a.match_length=a.prev_length=ia-1,a.match_available=0,a.ins_h=0}function u(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=Z,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new D.Buf16(2*ga),this.dyn_dtree=new D.Buf16(2*(2*ea+1)),this.bl_tree=new D.Buf16(2*(2*fa+1)),f(this.dyn_ltree),f(this.dyn_dtree),f(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new D.Buf16(ha+1),this.heap=new D.Buf16(2*da+1),f(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new D.Buf16(2*da+1),f(this.depth),this.l_buf=0,this.lit_bufsize=0,this.last_lit=0,this.d_buf=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}function v(a){var b;return a&&a.state?(a.total_in=a.total_out=0,a.data_type=Y,b=a.state,b.pending=0,b.pending_out=0,b.wrap<0&&(b.wrap=-b.wrap),b.status=b.wrap?ma:ra,a.adler=2===b.wrap?0:1,b.last_flush=I,E._tr_init(b),N):d(a,P)}function w(a){var b=v(a);return b===N&&t(a.state),b}function x(a,b){return a&&a.state?2!==a.state.wrap?P:(a.state.gzhead=b,N):P}function y(a,b,c,e,f,g){if(!a)return P;var h=1;if(b===S&&(b=6),0>e?(h=0,e=-e):e>15&&(h=2,e-=16),1>f||f>$||c!==Z||8>e||e>15||0>b||b>9||0>g||g>W)return d(a,P);8===e&&(e=9);var i=new u;return a.state=i,i.strm=a,i.wrap=h,i.gzhead=null,i.w_bits=e,i.w_size=1<<i.w_bits,i.w_mask=i.w_size-1,i.hash_bits=f+7,i.hash_size=1<<i.hash_bits,i.hash_mask=i.hash_size-1,i.hash_shift=~~((i.hash_bits+ia-1)/ia),i.window=new D.Buf8(2*i.w_size),i.head=new D.Buf16(i.hash_size),i.prev=new D.Buf16(i.w_size),i.lit_bufsize=1<<f+6,i.pending_buf_size=4*i.lit_bufsize,i.pending_buf=new D.Buf8(i.pending_buf_size),i.d_buf=i.lit_bufsize>>1,i.l_buf=3*i.lit_bufsize,i.level=b,i.strategy=g,i.method=c,w(a)}function z(a,b){return y(a,b,Z,_,aa,X)}function A(a,b){var c,h,k,l;if(!a||!a.state||b>M||0>b)return a?d(a,P):P;if(h=a.state,!a.output||!a.input&&0!==a.avail_in||h.status===sa&&b!==L)return d(a,0===a.avail_out?R:P);if(h.strm=a,c=h.last_flush,h.last_flush=b,h.status===ma)if(2===h.wrap)a.adler=0,i(h,31),i(h,139),i(h,8),h.gzhead?(i(h,(h.gzhead.text?1:0)+(h.gzhead.hcrc?2:0)+(h.gzhead.extra?4:0)+(h.gzhead.name?8:0)+(h.gzhead.comment?16:0)),i(h,255&h.gzhead.time),i(h,h.gzhead.time>>8&255),i(h,h.gzhead.time>>16&255),i(h,h.gzhead.time>>24&255),i(h,9===h.level?2:h.strategy>=U||h.level<2?4:0),i(h,255&h.gzhead.os),h.gzhead.extra&&h.gzhead.extra.length&&(i(h,255&h.gzhead.extra.length),i(h,h.gzhead.extra.length>>8&255)),h.gzhead.hcrc&&(a.adler=G(a.adler,h.pending_buf,h.pending,0)),h.gzindex=0,h.status=na):(i(h,0),i(h,0),i(h,0),i(h,0),i(h,0),i(h,9===h.level?2:h.strategy>=U||h.level<2?4:0),i(h,xa),h.status=ra);else{var m=Z+(h.w_bits-8<<4)<<8,n=-1;n=h.strategy>=U||h.level<2?0:h.level<6?1:6===h.level?2:3,m|=n<<6,0!==h.strstart&&(m|=la),m+=31-m%31,h.status=ra,j(h,m),0!==h.strstart&&(j(h,a.adler>>>16),j(h,65535&a.adler)),a.adler=1}if(h.status===na)if(h.gzhead.extra){for(k=h.pending;h.gzindex<(65535&h.gzhead.extra.length)&&(h.pending!==h.pending_buf_size||(h.gzhead.hcrc&&h.pending>k&&(a.adler=G(a.adler,h.pending_buf,h.pending-k,k)),g(a),k=h.pending,h.pending!==h.pending_buf_size));)i(h,255&h.gzhead.extra[h.gzindex]),h.gzindex++;h.gzhead.hcrc&&h.pending>k&&(a.adler=G(a.adler,h.pending_buf,h.pending-k,k)),h.gzindex===h.gzhead.extra.length&&(h.gzindex=0,h.status=oa)}else h.status=oa;if(h.status===oa)if(h.gzhead.name){k=h.pending;do{if(h.pending===h.pending_buf_size&&(h.gzhead.hcrc&&h.pending>k&&(a.adler=G(a.adler,h.pending_buf,h.pending-k,k)),g(a),k=h.pending,h.pending===h.pending_buf_size)){l=1;break}l=h.gzindex<h.gzhead.name.length?255&h.gzhead.name.charCodeAt(h.gzindex++):0,i(h,l)}while(0!==l);h.gzhead.hcrc&&h.pending>k&&(a.adler=G(a.adler,h.pending_buf,h.pending-k,k)),0===l&&(h.gzindex=0,h.status=pa)}else h.status=pa;if(h.status===pa)if(h.gzhead.comment){k=h.pending;do{if(h.pending===h.pending_buf_size&&(h.gzhead.hcrc&&h.pending>k&&(a.adler=G(a.adler,h.pending_buf,h.pending-k,k)),g(a),k=h.pending,h.pending===h.pending_buf_size)){l=1;break}l=h.gzindex<h.gzhead.comment.length?255&h.gzhead.comment.charCodeAt(h.gzindex++):0,i(h,l)}while(0!==l);h.gzhead.hcrc&&h.pending>k&&(a.adler=G(a.adler,h.pending_buf,h.pending-k,k)),0===l&&(h.status=qa)}else h.status=qa;if(h.status===qa&&(h.gzhead.hcrc?(h.pending+2>h.pending_buf_size&&g(a),h.pending+2<=h.pending_buf_size&&(i(h,255&a.adler),i(h,a.adler>>8&255),a.adler=0,h.status=ra)):h.status=ra),0!==h.pending){if(g(a),0===a.avail_out)return h.last_flush=-1,N}else if(0===a.avail_in&&e(b)<=e(c)&&b!==L)return d(a,R);if(h.status===sa&&0!==a.avail_in)return d(a,R);if(0!==a.avail_in||0!==h.lookahead||b!==I&&h.status!==sa){var o=h.strategy===U?r(h,b):h.strategy===V?q(h,b):C[h.level].func(h,b);if((o===va||o===wa)&&(h.status=sa),o===ta||o===va)return 0===a.avail_out&&(h.last_flush=-1),N;if(o===ua&&(b===J?E._tr_align(h):b!==M&&(E._tr_stored_block(h,0,0,!1),b===K&&(f(h.head),0===h.lookahead&&(h.strstart=0,h.block_start=0,h.insert=0))),g(a),0===a.avail_out))return h.last_flush=-1,N}return b!==L?N:h.wrap<=0?O:(2===h.wrap?(i(h,255&a.adler),i(h,a.adler>>8&255),i(h,a.adler>>16&255),i(h,a.adler>>24&255),i(h,255&a.total_in),i(h,a.total_in>>8&255),i(h,a.total_in>>16&255),i(h,a.total_in>>24&255)):(j(h,a.adler>>>16),j(h,65535&a.adler)),g(a),h.wrap>0&&(h.wrap=-h.wrap),0!==h.pending?N:O)}function B(a){var b;return a&&a.state?(b=a.state.status,b!==ma&&b!==na&&b!==oa&&b!==pa&&b!==qa&&b!==ra&&b!==sa?d(a,P):(a.state=null,b===ra?d(a,Q):N)):P}var C,D=a("../utils/common"),E=a("./trees"),F=a("./adler32"),G=a("./crc32"),H=a("./messages"),I=0,J=1,K=3,L=4,M=5,N=0,O=1,P=-2,Q=-3,R=-5,S=-1,T=1,U=2,V=3,W=4,X=0,Y=2,Z=8,$=9,_=15,aa=8,ba=29,ca=256,da=ca+1+ba,ea=30,fa=19,ga=2*da+1,ha=15,ia=3,ja=258,ka=ja+ia+1,la=32,ma=42,na=69,oa=73,pa=91,qa=103,ra=113,sa=666,ta=1,ua=2,va=3,wa=4,xa=3;C=[new s(0,0,0,0,n),new s(4,4,8,4,o),new s(4,5,16,8,o),new s(4,6,32,32,o),new s(4,4,16,16,p),new s(8,16,32,32,p),new s(8,16,128,128,p),new s(8,32,128,256,p),new s(32,128,258,1024,p),new s(32,258,258,4096,p)],c.deflateInit=z,c.deflateInit2=y,c.deflateReset=w,c.deflateResetKeep=v,c.deflateSetHeader=x,c.deflate=A,c.deflateEnd=B,c.deflateInfo="pako deflate (from Nodeca project)"},{"../utils/common":30,"./adler32":32,"./crc32":34,"./messages":40,"./trees":41}],36:[function(a,b,c){"use strict";function d(){this.text=0,this.time=0,this.xflags=0,this.os=0,this.extra=null,this.extra_len=0,this.name="",this.comment="",this.hcrc=0,this.done=!1}b.exports=d},{}],37:[function(a,b,c){"use strict";var d=30,e=12;b.exports=function(a,b){var c,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C;c=a.state,f=a.next_in,B=a.input,g=f+(a.avail_in-5),h=a.next_out,C=a.output,i=h-(b-a.avail_out),j=h+(a.avail_out-257),k=c.dmax,l=c.wsize,m=c.whave,n=c.wnext,o=c.window,p=c.hold,q=c.bits,r=c.lencode,s=c.distcode,t=(1<<c.lenbits)-1,u=(1<<c.distbits)-1;a:do{15>q&&(p+=B[f++]<<q,q+=8,p+=B[f++]<<q,q+=8),v=r[p&t];b:for(;;){if(w=v>>>24,p>>>=w,q-=w,w=v>>>16&255,0===w)C[h++]=65535&v;else{if(!(16&w)){if(0===(64&w)){v=r[(65535&v)+(p&(1<<w)-1)];continue b}if(32&w){c.mode=e;break a}a.msg="invalid literal/length code",c.mode=d;break a}x=65535&v,w&=15,w&&(w>q&&(p+=B[f++]<<q,q+=8),x+=p&(1<<w)-1,p>>>=w,q-=w),15>q&&(p+=B[f++]<<q,q+=8,p+=B[f++]<<q,q+=8),v=s[p&u];c:for(;;){if(w=v>>>24,p>>>=w,q-=w,w=v>>>16&255,!(16&w)){if(0===(64&w)){v=s[(65535&v)+(p&(1<<w)-1)];continue c}a.msg="invalid distance code",c.mode=d;break a}if(y=65535&v,w&=15,w>q&&(p+=B[f++]<<q,q+=8,w>q&&(p+=B[f++]<<q,q+=8)),y+=p&(1<<w)-1,y>k){a.msg="invalid distance too far back",c.mode=d;break a}if(p>>>=w,q-=w,w=h-i,y>w){if(w=y-w,w>m&&c.sane){a.msg="invalid distance too far back",c.mode=d;break a}if(z=0,A=o,0===n){if(z+=l-w,x>w){x-=w;do C[h++]=o[z++];while(--w);z=h-y,A=C}}else if(w>n){if(z+=l+n-w,w-=n,x>w){x-=w;do C[h++]=o[z++];while(--w);if(z=0,x>n){w=n,x-=w;do C[h++]=o[z++];while(--w);z=h-y,A=C}}}else if(z+=n-w,x>w){x-=w;do C[h++]=o[z++];while(--w);z=h-y,A=C}for(;x>2;)C[h++]=A[z++],C[h++]=A[z++],C[h++]=A[z++],x-=3;x&&(C[h++]=A[z++],x>1&&(C[h++]=A[z++]))}else{z=h-y;do C[h++]=C[z++],C[h++]=C[z++],C[h++]=C[z++],x-=3;while(x>2);x&&(C[h++]=C[z++],x>1&&(C[h++]=C[z++]))}break}}break}}while(g>f&&j>h);x=q>>3,f-=x,q-=x<<3,p&=(1<<q)-1,a.next_in=f,a.next_out=h,a.avail_in=g>f?5+(g-f):5-(f-g),a.avail_out=j>h?257+(j-h):257-(h-j),c.hold=p,c.bits=q}},{}],38:[function(a,b,c){"use strict";function d(a){return(a>>>24&255)+(a>>>8&65280)+((65280&a)<<8)+((255&a)<<24)}function e(){this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new r.Buf16(320),this.work=new r.Buf16(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0}function f(a){var b;return a&&a.state?(b=a.state,a.total_in=a.total_out=b.total=0,a.msg="",b.wrap&&(a.adler=1&b.wrap),b.mode=K,b.last=0,b.havedict=0,b.dmax=32768,b.head=null,b.hold=0,b.bits=0,b.lencode=b.lendyn=new r.Buf32(oa),b.distcode=b.distdyn=new r.Buf32(pa),b.sane=1,b.back=-1,C):F}function g(a){var b;return a&&a.state?(b=a.state,b.wsize=0,b.whave=0,b.wnext=0,f(a)):F}function h(a,b){var c,d;return a&&a.state?(d=a.state,0>b?(c=0,b=-b):(c=(b>>4)+1,48>b&&(b&=15)),b&&(8>b||b>15)?F:(null!==d.window&&d.wbits!==b&&(d.window=null),d.wrap=c,d.wbits=b,g(a))):F}function i(a,b){var c,d;return a?(d=new e,a.state=d,d.window=null,c=h(a,b),c!==C&&(a.state=null),c):F}function j(a){return i(a,ra)}function k(a){if(sa){var b;for(p=new r.Buf32(512),q=new r.Buf32(32),b=0;144>b;)a.lens[b++]=8;for(;256>b;)a.lens[b++]=9;for(;280>b;)a.lens[b++]=7;for(;288>b;)a.lens[b++]=8;for(v(x,a.lens,0,288,p,0,a.work,{bits:9}),b=0;32>b;)a.lens[b++]=5;v(y,a.lens,0,32,q,0,a.work,{bits:5}),sa=!1}a.lencode=p,a.lenbits=9,a.distcode=q,a.distbits=5}function l(a,b,c,d){var e,f=a.state;return null===f.window&&(f.wsize=1<<f.wbits,f.wnext=0,f.whave=0,f.window=new r.Buf8(f.wsize)),d>=f.wsize?(r.arraySet(f.window,b,c-f.wsize,f.wsize,0),f.wnext=0,f.whave=f.wsize):(e=f.wsize-f.wnext,e>d&&(e=d),r.arraySet(f.window,b,c-d,e,f.wnext),d-=e,d?(r.arraySet(f.window,b,c-d,d,0),f.wnext=d,f.whave=f.wsize):(f.wnext+=e,f.wnext===f.wsize&&(f.wnext=0),f.whave<f.wsize&&(f.whave+=e))),0}function m(a,b){var c,e,f,g,h,i,j,m,n,o,p,q,oa,pa,qa,ra,sa,ta,ua,va,wa,xa,ya,za,Aa=0,Ba=new r.Buf8(4),Ca=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];if(!a||!a.state||!a.output||!a.input&&0!==a.avail_in)return F;c=a.state,c.mode===V&&(c.mode=W),h=a.next_out,f=a.output,j=a.avail_out,g=a.next_in,e=a.input,i=a.avail_in,m=c.hold,n=c.bits,o=i,p=j,xa=C;a:for(;;)switch(c.mode){case K:if(0===c.wrap){c.mode=W;break}for(;16>n;){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}if(2&c.wrap&&35615===m){c.check=0,Ba[0]=255&m,Ba[1]=m>>>8&255,c.check=t(c.check,Ba,2,0),m=0,n=0,c.mode=L;break}if(c.flags=0,c.head&&(c.head.done=!1),!(1&c.wrap)||(((255&m)<<8)+(m>>8))%31){a.msg="incorrect header check",c.mode=la;break}if((15&m)!==J){a.msg="unknown compression method",c.mode=la;break}if(m>>>=4,n-=4,wa=(15&m)+8,0===c.wbits)c.wbits=wa;else if(wa>c.wbits){a.msg="invalid window size",c.mode=la;break}c.dmax=1<<wa,a.adler=c.check=1,c.mode=512&m?T:V,m=0,n=0;break;case L:for(;16>n;){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}if(c.flags=m,(255&c.flags)!==J){a.msg="unknown compression method",c.mode=la;break}if(57344&c.flags){a.msg="unknown header flags set",c.mode=la;break}c.head&&(c.head.text=m>>8&1),512&c.flags&&(Ba[0]=255&m,Ba[1]=m>>>8&255,c.check=t(c.check,Ba,2,0)),m=0,n=0,c.mode=M;case M:for(;32>n;){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}c.head&&(c.head.time=m),512&c.flags&&(Ba[0]=255&m,Ba[1]=m>>>8&255,Ba[2]=m>>>16&255,Ba[3]=m>>>24&255,c.check=t(c.check,Ba,4,0)),m=0,n=0,c.mode=N;case N:for(;16>n;){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}c.head&&(c.head.xflags=255&m,c.head.os=m>>8),512&c.flags&&(Ba[0]=255&m,Ba[1]=m>>>8&255,c.check=t(c.check,Ba,2,0)),m=0,n=0,c.mode=O;case O:if(1024&c.flags){for(;16>n;){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}c.length=m,c.head&&(c.head.extra_len=m),512&c.flags&&(Ba[0]=255&m,Ba[1]=m>>>8&255,c.check=t(c.check,Ba,2,0)),m=0,n=0}else c.head&&(c.head.extra=null);c.mode=P;case P:if(1024&c.flags&&(q=c.length,q>i&&(q=i),q&&(c.head&&(wa=c.head.extra_len-c.length,c.head.extra||(c.head.extra=new Array(c.head.extra_len)),r.arraySet(c.head.extra,e,g,q,wa)),512&c.flags&&(c.check=t(c.check,e,q,g)),i-=q,g+=q,c.length-=q),c.length))break a;c.length=0,c.mode=Q;case Q:if(2048&c.flags){if(0===i)break a;q=0;do wa=e[g+q++],c.head&&wa&&c.length<65536&&(c.head.name+=String.fromCharCode(wa));while(wa&&i>q);if(512&c.flags&&(c.check=t(c.check,e,q,g)),i-=q,g+=q,wa)break a}else c.head&&(c.head.name=null);c.length=0,c.mode=R;case R:if(4096&c.flags){if(0===i)break a;q=0;do wa=e[g+q++],c.head&&wa&&c.length<65536&&(c.head.comment+=String.fromCharCode(wa));while(wa&&i>q);if(512&c.flags&&(c.check=t(c.check,e,q,g)),i-=q,g+=q,wa)break a}else c.head&&(c.head.comment=null);c.mode=S;case S:if(512&c.flags){for(;16>n;){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}if(m!==(65535&c.check)){a.msg="header crc mismatch",c.mode=la;break}m=0,n=0}c.head&&(c.head.hcrc=c.flags>>9&1,c.head.done=!0),a.adler=c.check=0,c.mode=V;break;case T:for(;32>n;){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}a.adler=c.check=d(m),m=0,n=0,c.mode=U;case U:if(0===c.havedict)return a.next_out=h,a.avail_out=j,a.next_in=g,a.avail_in=i,c.hold=m,c.bits=n,E;a.adler=c.check=1,c.mode=V;case V:if(b===A||b===B)break a;case W:if(c.last){m>>>=7&n,n-=7&n,c.mode=ia;break}for(;3>n;){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}switch(c.last=1&m,m>>>=1,n-=1,3&m){case 0:c.mode=X;break;case 1:if(k(c),c.mode=ba,b===B){m>>>=2,n-=2;break a}break;case 2:c.mode=$;break;case 3:a.msg="invalid block type",c.mode=la}m>>>=2,n-=2;break;case X:for(m>>>=7&n,n-=7&n;32>n;){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}if((65535&m)!==(m>>>16^65535)){a.msg="invalid stored block lengths",c.mode=la;break}if(c.length=65535&m,m=0,n=0,c.mode=Y,b===B)break a;case Y:c.mode=Z;case Z:if(q=c.length){if(q>i&&(q=i),q>j&&(q=j),0===q)break a;r.arraySet(f,e,g,q,h),i-=q,g+=q,j-=q,h+=q,c.length-=q;break}c.mode=V;break;case $:for(;14>n;){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}if(c.nlen=(31&m)+257,m>>>=5,n-=5,c.ndist=(31&m)+1,m>>>=5,n-=5,c.ncode=(15&m)+4,m>>>=4,n-=4,c.nlen>286||c.ndist>30){a.msg="too many length or distance symbols",c.mode=la;break}c.have=0,c.mode=_;case _:for(;c.have<c.ncode;){for(;3>n;){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}c.lens[Ca[c.have++]]=7&m,m>>>=3,n-=3}for(;c.have<19;)c.lens[Ca[c.have++]]=0;if(c.lencode=c.lendyn,c.lenbits=7,ya={bits:c.lenbits},xa=v(w,c.lens,0,19,c.lencode,0,c.work,ya),c.lenbits=ya.bits,xa){a.msg="invalid code lengths set",c.mode=la;break}c.have=0,c.mode=aa;case aa:for(;c.have<c.nlen+c.ndist;){for(;Aa=c.lencode[m&(1<<c.lenbits)-1],qa=Aa>>>24,ra=Aa>>>16&255,sa=65535&Aa,!(n>=qa);){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}if(16>sa)m>>>=qa,n-=qa,c.lens[c.have++]=sa;else{if(16===sa){for(za=qa+2;za>n;){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}if(m>>>=qa,n-=qa,0===c.have){a.msg="invalid bit length repeat",c.mode=la;break}wa=c.lens[c.have-1],q=3+(3&m),m>>>=2,n-=2}else if(17===sa){for(za=qa+3;za>n;){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}m>>>=qa,n-=qa,wa=0,q=3+(7&m),m>>>=3,n-=3}else{for(za=qa+7;za>n;){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}m>>>=qa,n-=qa,wa=0,q=11+(127&m),m>>>=7,n-=7}if(c.have+q>c.nlen+c.ndist){a.msg="invalid bit length repeat",c.mode=la;break}for(;q--;)c.lens[c.have++]=wa}}if(c.mode===la)break;if(0===c.lens[256]){a.msg="invalid code -- missing end-of-block",c.mode=la;break}if(c.lenbits=9,ya={bits:c.lenbits},xa=v(x,c.lens,0,c.nlen,c.lencode,0,c.work,ya),c.lenbits=ya.bits,xa){a.msg="invalid literal/lengths set",c.mode=la;break}if(c.distbits=6,c.distcode=c.distdyn,ya={bits:c.distbits},xa=v(y,c.lens,c.nlen,c.ndist,c.distcode,0,c.work,ya),c.distbits=ya.bits,xa){a.msg="invalid distances set",c.mode=la;break}if(c.mode=ba,b===B)break a;case ba:c.mode=ca;case ca:if(i>=6&&j>=258){a.next_out=h,a.avail_out=j,a.next_in=g,a.avail_in=i,c.hold=m,c.bits=n,u(a,p),h=a.next_out,f=a.output,j=a.avail_out,g=a.next_in,e=a.input,i=a.avail_in,m=c.hold,n=c.bits,c.mode===V&&(c.back=-1);break}for(c.back=0;Aa=c.lencode[m&(1<<c.lenbits)-1],qa=Aa>>>24,ra=Aa>>>16&255,sa=65535&Aa,!(n>=qa);){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}if(ra&&0===(240&ra)){for(ta=qa,ua=ra,va=sa;Aa=c.lencode[va+((m&(1<<ta+ua)-1)>>ta)],qa=Aa>>>24,ra=Aa>>>16&255,sa=65535&Aa,!(n>=ta+qa);){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}m>>>=ta,n-=ta,c.back+=ta}if(m>>>=qa,n-=qa,c.back+=qa,c.length=sa,0===ra){c.mode=ha;break}if(32&ra){c.back=-1,c.mode=V;break}if(64&ra){a.msg="invalid literal/length code",c.mode=la;break}c.extra=15&ra,c.mode=da;case da:if(c.extra){for(za=c.extra;za>n;){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}c.length+=m&(1<<c.extra)-1,m>>>=c.extra,n-=c.extra,c.back+=c.extra}c.was=c.length,c.mode=ea;case ea:for(;Aa=c.distcode[m&(1<<c.distbits)-1],qa=Aa>>>24,ra=Aa>>>16&255,sa=65535&Aa,!(n>=qa);){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}if(0===(240&ra)){for(ta=qa,ua=ra,va=sa;Aa=c.distcode[va+((m&(1<<ta+ua)-1)>>ta)],qa=Aa>>>24,ra=Aa>>>16&255,sa=65535&Aa,!(n>=ta+qa);){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}m>>>=ta,n-=ta,c.back+=ta}if(m>>>=qa,n-=qa,c.back+=qa,64&ra){a.msg="invalid distance code",c.mode=la;break}c.offset=sa,c.extra=15&ra,c.mode=fa;case fa:if(c.extra){for(za=c.extra;za>n;){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}c.offset+=m&(1<<c.extra)-1,m>>>=c.extra,n-=c.extra,c.back+=c.extra}if(c.offset>c.dmax){a.msg="invalid distance too far back",c.mode=la;break}c.mode=ga;case ga:if(0===j)break a;if(q=p-j,c.offset>q){if(q=c.offset-q,q>c.whave&&c.sane){a.msg="invalid distance too far back",c.mode=la;break}q>c.wnext?(q-=c.wnext,oa=c.wsize-q):oa=c.wnext-q,q>c.length&&(q=c.length),pa=c.window}else pa=f,oa=h-c.offset,q=c.length;q>j&&(q=j),j-=q,c.length-=q;do f[h++]=pa[oa++];while(--q);0===c.length&&(c.mode=ca);break;case ha:if(0===j)break a;f[h++]=c.length,j--,c.mode=ca;break;case ia:if(c.wrap){for(;32>n;){if(0===i)break a;i--,m|=e[g++]<<n,n+=8}if(p-=j,a.total_out+=p,c.total+=p,p&&(a.adler=c.check=c.flags?t(c.check,f,p,h-p):s(c.check,f,p,h-p)),p=j,(c.flags?m:d(m))!==c.check){a.msg="incorrect data check",c.mode=la;break}m=0,n=0}c.mode=ja;case ja:if(c.wrap&&c.flags){for(;32>n;){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}if(m!==(4294967295&c.total)){a.msg="incorrect length check",c.mode=la;break}m=0,n=0}c.mode=ka;case ka:xa=D;break a;case la:xa=G;break a;case ma:return H;case na:default:return F}return a.next_out=h,a.avail_out=j,a.next_in=g,a.avail_in=i,c.hold=m,c.bits=n,(c.wsize||p!==a.avail_out&&c.mode<la&&(c.mode<ia||b!==z))&&l(a,a.output,a.next_out,p-a.avail_out)?(c.mode=ma,H):(o-=a.avail_in,p-=a.avail_out,a.total_in+=o,a.total_out+=p,c.total+=p,c.wrap&&p&&(a.adler=c.check=c.flags?t(c.check,f,p,a.next_out-p):s(c.check,f,p,a.next_out-p)),a.data_type=c.bits+(c.last?64:0)+(c.mode===V?128:0)+(c.mode===ba||c.mode===Y?256:0),(0===o&&0===p||b===z)&&xa===C&&(xa=I),xa)}function n(a){if(!a||!a.state)return F;var b=a.state;return b.window&&(b.window=null),a.state=null,C}function o(a,b){var c;return a&&a.state?(c=a.state,0===(2&c.wrap)?F:(c.head=b,b.done=!1,C)):F}var p,q,r=a("../utils/common"),s=a("./adler32"),t=a("./crc32"),u=a("./inffast"),v=a("./inftrees"),w=0,x=1,y=2,z=4,A=5,B=6,C=0,D=1,E=2,F=-2,G=-3,H=-4,I=-5,J=8,K=1,L=2,M=3,N=4,O=5,P=6,Q=7,R=8,S=9,T=10,U=11,V=12,W=13,X=14,Y=15,Z=16,$=17,_=18,aa=19,ba=20,ca=21,da=22,ea=23,fa=24,ga=25,ha=26,ia=27,ja=28,ka=29,la=30,ma=31,na=32,oa=852,pa=592,qa=15,ra=qa,sa=!0;c.inflateReset=g,c.inflateReset2=h,c.inflateResetKeep=f,c.inflateInit=j,c.inflateInit2=i,c.inflate=m,c.inflateEnd=n,c.inflateGetHeader=o,c.inflateInfo="pako inflate (from Nodeca project)"},{"../utils/common":30,"./adler32":32,"./crc32":34,"./inffast":37,"./inftrees":39}],39:[function(a,b,c){"use strict";var d=a("../utils/common"),e=15,f=852,g=592,h=0,i=1,j=2,k=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0],l=[16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78],m=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0],n=[16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64];b.exports=function(a,b,c,o,p,q,r,s){var t,u,v,w,x,y,z,A,B,C=s.bits,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=null,O=0,P=new d.Buf16(e+1),Q=new d.Buf16(e+1),R=null,S=0;for(D=0;e>=D;D++)P[D]=0;for(E=0;o>E;E++)P[b[c+E]]++;for(H=C,G=e;G>=1&&0===P[G];G--);if(H>G&&(H=G),0===G)return p[q++]=20971520,p[q++]=20971520,s.bits=1,0;for(F=1;G>F&&0===P[F];F++);for(F>H&&(H=F),K=1,D=1;e>=D;D++)if(K<<=1,K-=P[D],0>K)return-1;if(K>0&&(a===h||1!==G))return-1;for(Q[1]=0,D=1;e>D;D++)Q[D+1]=Q[D]+P[D];for(E=0;o>E;E++)0!==b[c+E]&&(r[Q[b[c+E]]++]=E);if(a===h?(N=R=r,y=19):a===i?(N=k,O-=257,R=l,S-=257,y=256):(N=m,R=n,y=-1),M=0,E=0,D=F,x=q,I=H,J=0,v=-1,L=1<<H,w=L-1,a===i&&L>f||a===j&&L>g)return 1;for(var T=0;;){T++,z=D-J,r[E]<y?(A=0,B=r[E]):r[E]>y?(A=R[S+r[E]],B=N[O+r[E]]):(A=96,B=0),t=1<<D-J,u=1<<I,F=u;do u-=t,p[x+(M>>J)+u]=z<<24|A<<16|B|0;while(0!==u);for(t=1<<D-1;M&t;)t>>=1;if(0!==t?(M&=t-1,M+=t):M=0,E++,0===--P[D]){if(D===G)break;D=b[c+r[E]]}if(D>H&&(M&w)!==v){for(0===J&&(J=H),x+=F,I=D-J,K=1<<I;G>I+J&&(K-=P[I+J],!(0>=K));)I++,K<<=1;if(L+=1<<I,a===i&&L>f||a===j&&L>g)return 1;v=M&w,p[v]=H<<24|I<<16|x-q|0}}return 0!==M&&(p[x+M]=D-J<<24|64<<16|0),s.bits=H,0}},{"../utils/common":30}],40:[function(a,b,c){"use strict";b.exports={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"}},{}],41:[function(a,b,c){"use strict";function d(a){for(var b=a.length;--b>=0;)a[b]=0}function e(a,b,c,d,e){this.static_tree=a,this.extra_bits=b,this.extra_base=c,this.elems=d,this.max_length=e,this.has_stree=a&&a.length}function f(a,b){this.dyn_tree=a,this.max_code=0,this.stat_desc=b}function g(a){return 256>a?ia[a]:ia[256+(a>>>7)]}function h(a,b){a.pending_buf[a.pending++]=255&b,a.pending_buf[a.pending++]=b>>>8&255}function i(a,b,c){a.bi_valid>X-c?(a.bi_buf|=b<<a.bi_valid&65535,h(a,a.bi_buf),a.bi_buf=b>>X-a.bi_valid,a.bi_valid+=c-X):(a.bi_buf|=b<<a.bi_valid&65535,a.bi_valid+=c)}function j(a,b,c){i(a,c[2*b],c[2*b+1])}function k(a,b){var c=0;do c|=1&a,a>>>=1,c<<=1;while(--b>0);return c>>>1}function l(a){16===a.bi_valid?(h(a,a.bi_buf),a.bi_buf=0,a.bi_valid=0):a.bi_valid>=8&&(a.pending_buf[a.pending++]=255&a.bi_buf,a.bi_buf>>=8,a.bi_valid-=8)}function m(a,b){var c,d,e,f,g,h,i=b.dyn_tree,j=b.max_code,k=b.stat_desc.static_tree,l=b.stat_desc.has_stree,m=b.stat_desc.extra_bits,n=b.stat_desc.extra_base,o=b.stat_desc.max_length,p=0;for(f=0;W>=f;f++)a.bl_count[f]=0;for(i[2*a.heap[a.heap_max]+1]=0,c=a.heap_max+1;V>c;c++)d=a.heap[c],f=i[2*i[2*d+1]+1]+1,f>o&&(f=o,p++),i[2*d+1]=f,d>j||(a.bl_count[f]++,g=0,d>=n&&(g=m[d-n]),h=i[2*d],a.opt_len+=h*(f+g),l&&(a.static_len+=h*(k[2*d+1]+g)));if(0!==p){do{for(f=o-1;0===a.bl_count[f];)f--;a.bl_count[f]--,a.bl_count[f+1]+=2,a.bl_count[o]--,p-=2}while(p>0);for(f=o;0!==f;f--)for(d=a.bl_count[f];0!==d;)e=a.heap[--c],e>j||(i[2*e+1]!==f&&(a.opt_len+=(f-i[2*e+1])*i[2*e],i[2*e+1]=f),d--)}}function n(a,b,c){var d,e,f=new Array(W+1),g=0;for(d=1;W>=d;d++)f[d]=g=g+c[d-1]<<1;for(e=0;b>=e;e++){var h=a[2*e+1];0!==h&&(a[2*e]=k(f[h]++,h))}}function o(){var a,b,c,d,f,g=new Array(W+1);for(c=0,d=0;Q-1>d;d++)for(ka[d]=c,a=0;a<1<<ba[d];a++)ja[c++]=d;for(ja[c-1]=d,f=0,d=0;16>d;d++)for(la[d]=f,a=0;a<1<<ca[d];a++)ia[f++]=d;for(f>>=7;T>d;d++)for(la[d]=f<<7,a=0;a<1<<ca[d]-7;a++)ia[256+f++]=d;for(b=0;W>=b;b++)g[b]=0;for(a=0;143>=a;)ga[2*a+1]=8,a++,g[8]++;for(;255>=a;)ga[2*a+1]=9,a++,g[9]++;for(;279>=a;)ga[2*a+1]=7,a++,g[7]++;for(;287>=a;)ga[2*a+1]=8,a++,g[8]++;for(n(ga,S+1,g),a=0;T>a;a++)ha[2*a+1]=5,ha[2*a]=k(a,5);ma=new e(ga,ba,R+1,S,W),na=new e(ha,ca,0,T,W),oa=new e(new Array(0),da,0,U,Y)}function p(a){var b;for(b=0;S>b;b++)a.dyn_ltree[2*b]=0;for(b=0;T>b;b++)a.dyn_dtree[2*b]=0;for(b=0;U>b;b++)a.bl_tree[2*b]=0;a.dyn_ltree[2*Z]=1,a.opt_len=a.static_len=0,a.last_lit=a.matches=0}function q(a){a.bi_valid>8?h(a,a.bi_buf):a.bi_valid>0&&(a.pending_buf[a.pending++]=a.bi_buf),a.bi_buf=0,a.bi_valid=0}function r(a,b,c,d){q(a),d&&(h(a,c),h(a,~c)),G.arraySet(a.pending_buf,a.window,b,c,a.pending),a.pending+=c}function s(a,b,c,d){var e=2*b,f=2*c;return a[e]<a[f]||a[e]===a[f]&&d[b]<=d[c]}function t(a,b,c){for(var d=a.heap[c],e=c<<1;e<=a.heap_len&&(e<a.heap_len&&s(b,a.heap[e+1],a.heap[e],a.depth)&&e++,!s(b,d,a.heap[e],a.depth));)a.heap[c]=a.heap[e],c=e,e<<=1;a.heap[c]=d}function u(a,b,c){var d,e,f,h,k=0;if(0!==a.last_lit)do d=a.pending_buf[a.d_buf+2*k]<<8|a.pending_buf[a.d_buf+2*k+1],e=a.pending_buf[a.l_buf+k],k++,0===d?j(a,e,b):(f=ja[e],j(a,f+R+1,b),h=ba[f],0!==h&&(e-=ka[f],i(a,e,h)),d--,f=g(d),j(a,f,c),h=ca[f],0!==h&&(d-=la[f],i(a,d,h)));while(k<a.last_lit);j(a,Z,b)}function v(a,b){var c,d,e,f=b.dyn_tree,g=b.stat_desc.static_tree,h=b.stat_desc.has_stree,i=b.stat_desc.elems,j=-1;for(a.heap_len=0,a.heap_max=V,c=0;i>c;c++)0!==f[2*c]?(a.heap[++a.heap_len]=j=c,a.depth[c]=0):f[2*c+1]=0;for(;a.heap_len<2;)e=a.heap[++a.heap_len]=2>j?++j:0,f[2*e]=1,a.depth[e]=0,a.opt_len--,h&&(a.static_len-=g[2*e+1]);for(b.max_code=j,c=a.heap_len>>1;c>=1;c--)t(a,f,c);e=i;do c=a.heap[1],a.heap[1]=a.heap[a.heap_len--],t(a,f,1),d=a.heap[1],a.heap[--a.heap_max]=c,a.heap[--a.heap_max]=d,f[2*e]=f[2*c]+f[2*d],a.depth[e]=(a.depth[c]>=a.depth[d]?a.depth[c]:a.depth[d])+1,f[2*c+1]=f[2*d+1]=e,a.heap[1]=e++,t(a,f,1);while(a.heap_len>=2);a.heap[--a.heap_max]=a.heap[1],m(a,b),n(f,j,a.bl_count)}function w(a,b,c){var d,e,f=-1,g=b[1],h=0,i=7,j=4;for(0===g&&(i=138,j=3),b[2*(c+1)+1]=65535,d=0;c>=d;d++)e=g,g=b[2*(d+1)+1],++h<i&&e===g||(j>h?a.bl_tree[2*e]+=h:0!==e?(e!==f&&a.bl_tree[2*e]++,a.bl_tree[2*$]++):10>=h?a.bl_tree[2*_]++:a.bl_tree[2*aa]++,h=0,f=e,0===g?(i=138,j=3):e===g?(i=6,j=3):(i=7,j=4))}function x(a,b,c){var d,e,f=-1,g=b[1],h=0,k=7,l=4;for(0===g&&(k=138,l=3),d=0;c>=d;d++)if(e=g,g=b[2*(d+1)+1],!(++h<k&&e===g)){if(l>h){do j(a,e,a.bl_tree);while(0!==--h)}else 0!==e?(e!==f&&(j(a,e,a.bl_tree),h--),j(a,$,a.bl_tree),i(a,h-3,2)):10>=h?(j(a,_,a.bl_tree),i(a,h-3,3)):(j(a,aa,a.bl_tree),i(a,h-11,7));h=0,f=e,0===g?(k=138,l=3):e===g?(k=6,l=3):(k=7,l=4)}}function y(a){var b;for(w(a,a.dyn_ltree,a.l_desc.max_code),w(a,a.dyn_dtree,a.d_desc.max_code),v(a,a.bl_desc),b=U-1;b>=3&&0===a.bl_tree[2*ea[b]+1];b--);return a.opt_len+=3*(b+1)+5+5+4,b}function z(a,b,c,d){var e;for(i(a,b-257,5),i(a,c-1,5),i(a,d-4,4),e=0;d>e;e++)i(a,a.bl_tree[2*ea[e]+1],3);x(a,a.dyn_ltree,b-1),x(a,a.dyn_dtree,c-1)}function A(a){var b,c=4093624447;for(b=0;31>=b;b++,c>>>=1)if(1&c&&0!==a.dyn_ltree[2*b])return I;if(0!==a.dyn_ltree[18]||0!==a.dyn_ltree[20]||0!==a.dyn_ltree[26])return J;for(b=32;R>b;b++)if(0!==a.dyn_ltree[2*b])return J;return I}function B(a){pa||(o(),pa=!0),a.l_desc=new f(a.dyn_ltree,ma),a.d_desc=new f(a.dyn_dtree,na),a.bl_desc=new f(a.bl_tree,oa),a.bi_buf=0,a.bi_valid=0,p(a)}function C(a,b,c,d){i(a,(L<<1)+(d?1:0),3),r(a,b,c,!0)}function D(a){i(a,M<<1,3),j(a,Z,ga),l(a)}function E(a,b,c,d){var e,f,g=0;a.level>0?(a.strm.data_type===K&&(a.strm.data_type=A(a)),v(a,a.l_desc),v(a,a.d_desc),g=y(a),e=a.opt_len+3+7>>>3,f=a.static_len+3+7>>>3,e>=f&&(e=f)):e=f=c+5,e>=c+4&&-1!==b?C(a,b,c,d):a.strategy===H||f===e?(i(a,(M<<1)+(d?1:0),3),u(a,ga,ha)):(i(a,(N<<1)+(d?1:0),3),z(a,a.l_desc.max_code+1,a.d_desc.max_code+1,g+1),u(a,a.dyn_ltree,a.dyn_dtree)),p(a),d&&q(a)}function F(a,b,c){return a.pending_buf[a.d_buf+2*a.last_lit]=b>>>8&255,a.pending_buf[a.d_buf+2*a.last_lit+1]=255&b,a.pending_buf[a.l_buf+a.last_lit]=255&c,a.last_lit++,0===b?a.dyn_ltree[2*c]++:(a.matches++,b--,a.dyn_ltree[2*(ja[c]+R+1)]++,a.dyn_dtree[2*g(b)]++),a.last_lit===a.lit_bufsize-1}var G=a("../utils/common"),H=4,I=0,J=1,K=2,L=0,M=1,N=2,O=3,P=258,Q=29,R=256,S=R+1+Q,T=30,U=19,V=2*S+1,W=15,X=16,Y=7,Z=256,$=16,_=17,aa=18,ba=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0],ca=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],da=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7],ea=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],fa=512,ga=new Array(2*(S+2));d(ga);var ha=new Array(2*T);d(ha);var ia=new Array(fa);d(ia);var ja=new Array(P-O+1);d(ja);var ka=new Array(Q);d(ka);var la=new Array(T);d(la);var ma,na,oa,pa=!1;c._tr_init=B,c._tr_stored_block=C,c._tr_flush_block=E,c._tr_tally=F,c._tr_align=D},{"../utils/common":30}],42:[function(a,b,c){"use strict";function d(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0}b.exports=d},{}],43:[function(a,b,c){(function(b){!function(c){function d(a,b){if(!(this instanceof d))return new d(a,b);var e=this;f(e),e.q=e.c="",e.bufferCheckPosition=c.MAX_BUFFER_LENGTH,e.opt=b||{},e.opt.lowercase=e.opt.lowercase||e.opt.lowercasetags,e.looseCase=e.opt.lowercase?"toLowerCase":"toUpperCase",e.tags=[],e.closed=e.closedRoot=e.sawRoot=!1,e.tag=e.error=null,e.strict=!!a,e.noscript=!(!a&&!e.opt.noscript),e.state=V.BEGIN,e.strictEntities=e.opt.strictEntities,e.ENTITIES=e.strictEntities?Object.create(c.XML_ENTITIES):Object.create(c.ENTITIES),e.attribList=[],e.opt.xmlns&&(e.ns=Object.create(Q)),e.trackPosition=e.opt.position!==!1,e.trackPosition&&(e.position=e.line=e.column=0),n(e,"onready")}function e(a){for(var b=Math.max(c.MAX_BUFFER_LENGTH,10),d=0,e=0,f=D.length;f>e;e++){var g=a[D[e]].length;if(g>b)switch(D[e]){case"textNode":p(a);break;case"cdata":
o(a,"oncdata",a.cdata),a.cdata="";break;case"script":o(a,"onscript",a.script),a.script="";break;default:r(a,"Max buffer length exceeded: "+D[e])}d=Math.max(d,g)}var h=c.MAX_BUFFER_LENGTH-d;a.bufferCheckPosition=h+a.position}function f(a){for(var b=0,c=D.length;c>b;b++)a[D[b]]=""}function g(a){p(a),""!==a.cdata&&(o(a,"oncdata",a.cdata),a.cdata=""),""!==a.script&&(o(a,"onscript",a.script),a.script="")}function h(a,b){return new i(a,b)}function i(a,b){if(!(this instanceof i))return new i(a,b);E.apply(this),this._parser=new d(a,b),this.writable=!0,this.readable=!0;var c=this;this._parser.onend=function(){c.emit("end")},this._parser.onerror=function(a){c.emit("error",a),c._parser.error=null},this._decoder=null,G.forEach(function(a){Object.defineProperty(c,"on"+a,{get:function(){return c._parser["on"+a]},set:function(b){return b?void c.on(a,b):(c.removeAllListeners(a),c._parser["on"+a]=b,b)},enumerable:!0,configurable:!1})})}function j(a){return a.split("").reduce(function(a,b){return a[b]=!0,a},{})}function k(a){return"[object RegExp]"===Object.prototype.toString.call(a)}function l(a,b){return k(a)?!!b.match(a):a[b]}function m(a,b){return!l(a,b)}function n(a,b,c){a[b]&&a[b](c)}function o(a,b,c){a.textNode&&p(a),n(a,b,c)}function p(a){a.textNode=q(a.opt,a.textNode),a.textNode&&n(a,"ontext",a.textNode),a.textNode=""}function q(a,b){return a.trim&&(b=b.trim()),a.normalize&&(b=b.replace(/\s+/g," ")),b}function r(a,b){return p(a),a.trackPosition&&(b+="\nLine: "+a.line+"\nColumn: "+a.column+"\nChar: "+a.c),b=new Error(b),a.error=b,n(a,"onerror",b),a}function s(a){return a.sawRoot&&!a.closedRoot&&t(a,"Unclosed root tag"),a.state!==V.BEGIN&&a.state!==V.BEGIN_WHITESPACE&&a.state!==V.TEXT&&r(a,"Unexpected end"),p(a),a.c="",a.closed=!0,n(a,"onend"),d.call(a,a.strict,a.opt),a}function t(a,b){if("object"!=typeof a||!(a instanceof d))throw new Error("bad call to strictFail");a.strict&&r(a,b)}function u(a){a.strict||(a.tagName=a.tagName[a.looseCase]());var b=a.tags[a.tags.length-1]||a,c=a.tag={name:a.tagName,attributes:{}};a.opt.xmlns&&(c.ns=b.ns),a.attribList.length=0}function v(a,b){var c=a.indexOf(":"),d=0>c?["",a]:a.split(":"),e=d[0],f=d[1];return b&&"xmlns"===a&&(e="xmlns",f=""),{prefix:e,local:f}}function w(a){if(a.strict||(a.attribName=a.attribName[a.looseCase]()),-1!==a.attribList.indexOf(a.attribName)||a.tag.attributes.hasOwnProperty(a.attribName))return void(a.attribName=a.attribValue="");if(a.opt.xmlns){var b=v(a.attribName,!0),c=b.prefix,d=b.local;if("xmlns"===c)if("xml"===d&&a.attribValue!==O)t(a,"xml: prefix must be bound to "+O+"\nActual: "+a.attribValue);else if("xmlns"===d&&a.attribValue!==P)t(a,"xmlns: prefix must be bound to "+P+"\nActual: "+a.attribValue);else{var e=a.tag,f=a.tags[a.tags.length-1]||a;e.ns===f.ns&&(e.ns=Object.create(f.ns)),e.ns[d]=a.attribValue}a.attribList.push([a.attribName,a.attribValue])}else a.tag.attributes[a.attribName]=a.attribValue,o(a,"onattribute",{name:a.attribName,value:a.attribValue});a.attribName=a.attribValue=""}function x(a,b){if(a.opt.xmlns){var c=a.tag,d=v(a.tagName);c.prefix=d.prefix,c.local=d.local,c.uri=c.ns[d.prefix]||"",c.prefix&&!c.uri&&(t(a,"Unbound namespace prefix: "+JSON.stringify(a.tagName)),c.uri=d.prefix);var e=a.tags[a.tags.length-1]||a;c.ns&&e.ns!==c.ns&&Object.keys(c.ns).forEach(function(b){o(a,"onopennamespace",{prefix:b,uri:c.ns[b]})});for(var f=0,g=a.attribList.length;g>f;f++){var h=a.attribList[f],i=h[0],j=h[1],k=v(i,!0),l=k.prefix,m=k.local,n=""===l?"":c.ns[l]||"",p={name:i,value:j,prefix:l,local:m,uri:n};l&&"xmlns"!==l&&!n&&(t(a,"Unbound namespace prefix: "+JSON.stringify(l)),p.uri=l),a.tag.attributes[i]=p,o(a,"onattribute",p)}a.attribList.length=0}a.tag.isSelfClosing=!!b,a.sawRoot=!0,a.tags.push(a.tag),o(a,"onopentag",a.tag),b||(a.noscript||"script"!==a.tagName.toLowerCase()?a.state=V.TEXT:a.state=V.SCRIPT,a.tag=null,a.tagName=""),a.attribName=a.attribValue="",a.attribList.length=0}function y(a){if(!a.tagName)return t(a,"Weird empty close tag."),a.textNode+="</>",void(a.state=V.TEXT);if(a.script){if("script"!==a.tagName)return a.script+="</"+a.tagName+">",a.tagName="",void(a.state=V.SCRIPT);o(a,"onscript",a.script),a.script=""}var b=a.tags.length,c=a.tagName;a.strict||(c=c[a.looseCase]());for(var d=c;b--;){var e=a.tags[b];if(e.name===d)break;t(a,"Unexpected close tag")}if(0>b)return t(a,"Unmatched closing tag: "+a.tagName),a.textNode+="</"+a.tagName+">",void(a.state=V.TEXT);a.tagName=c;for(var f=a.tags.length;f-->b;){var g=a.tag=a.tags.pop();a.tagName=a.tag.name,o(a,"onclosetag",a.tagName);var h={};for(var i in g.ns)h[i]=g.ns[i];var j=a.tags[a.tags.length-1]||a;a.opt.xmlns&&g.ns!==j.ns&&Object.keys(g.ns).forEach(function(b){var c=g.ns[b];o(a,"onclosenamespace",{prefix:b,uri:c})})}0===b&&(a.closedRoot=!0),a.tagName=a.attribValue=a.attribName="",a.attribList.length=0,a.state=V.TEXT}function z(a){var b,c=a.entity,d=c.toLowerCase(),e="";return a.ENTITIES[c]?a.ENTITIES[c]:a.ENTITIES[d]?a.ENTITIES[d]:(c=d,"#"===c.charAt(0)&&("x"===c.charAt(1)?(c=c.slice(2),b=parseInt(c,16),e=b.toString(16)):(c=c.slice(1),b=parseInt(c,10),e=b.toString(10))),c=c.replace(/^0+/,""),e.toLowerCase()!==c?(t(a,"Invalid character entity"),"&"+a.entity+";"):String.fromCodePoint(b))}function A(a,b){"<"===b?(a.state=V.OPEN_WAKA,a.startTagPosition=a.position):m(H,b)&&(t(a,"Non-whitespace before first tag."),a.textNode=b,a.state=V.TEXT)}function B(a,b){var c="";return b<a.length&&(c=a.charAt(b)),c}function C(a){var b=this;if(this.error)throw this.error;if(b.closed)return r(b,"Cannot write after close. Assign an onready handler.");if(null===a)return s(b);for(var c=0,d="";;){if(d=B(a,c++),b.c=d,!d)break;switch(b.trackPosition&&(b.position++,"\n"===d?(b.line++,b.column=0):b.column++),b.state){case V.BEGIN:if(b.state=V.BEGIN_WHITESPACE,"\ufeff"===d)continue;A(b,d);continue;case V.BEGIN_WHITESPACE:A(b,d);continue;case V.TEXT:if(b.sawRoot&&!b.closedRoot){for(var f=c-1;d&&"<"!==d&&"&"!==d;)d=B(a,c++),d&&b.trackPosition&&(b.position++,"\n"===d?(b.line++,b.column=0):b.column++);b.textNode+=a.substring(f,c-1)}"<"!==d||b.sawRoot&&b.closedRoot&&!b.strict?(!m(H,d)||b.sawRoot&&!b.closedRoot||t(b,"Text data outside of root node."),"&"===d?b.state=V.TEXT_ENTITY:b.textNode+=d):(b.state=V.OPEN_WAKA,b.startTagPosition=b.position);continue;case V.SCRIPT:"<"===d?b.state=V.SCRIPT_ENDING:b.script+=d;continue;case V.SCRIPT_ENDING:"/"===d?b.state=V.CLOSE_TAG:(b.script+="<"+d,b.state=V.SCRIPT);continue;case V.OPEN_WAKA:if("!"===d)b.state=V.SGML_DECL,b.sgmlDecl="";else if(l(H,d));else if(l(R,d))b.state=V.OPEN_TAG,b.tagName=d;else if("/"===d)b.state=V.CLOSE_TAG,b.tagName="";else if("?"===d)b.state=V.PROC_INST,b.procInstName=b.procInstBody="";else{if(t(b,"Unencoded <"),b.startTagPosition+1<b.position){var g=b.position-b.startTagPosition;d=new Array(g).join(" ")+d}b.textNode+="<"+d,b.state=V.TEXT}continue;case V.SGML_DECL:(b.sgmlDecl+d).toUpperCase()===M?(o(b,"opencdata"),b.state=V.CDATA,b.sgmlDecl="",b.cdata=""):b.sgmlDecl+d==="--"?(b.state=V.COMMENT,b.comment="",b.sgmlDecl=""):(b.sgmlDecl+d).toUpperCase()===N?(b.state=V.DOCTYPE,(b.doctype||b.sawRoot)&&t(b,"Inappropriately located doctype declaration"),b.doctype="",b.sgmlDecl=""):">"===d?(o(b,"onsgmldeclaration",b.sgmlDecl),b.sgmlDecl="",b.state=V.TEXT):l(K,d)?(b.state=V.SGML_DECL_QUOTED,b.sgmlDecl+=d):b.sgmlDecl+=d;continue;case V.SGML_DECL_QUOTED:d===b.q&&(b.state=V.SGML_DECL,b.q=""),b.sgmlDecl+=d;continue;case V.DOCTYPE:">"===d?(b.state=V.TEXT,o(b,"ondoctype",b.doctype),b.doctype=!0):(b.doctype+=d,"["===d?b.state=V.DOCTYPE_DTD:l(K,d)&&(b.state=V.DOCTYPE_QUOTED,b.q=d));continue;case V.DOCTYPE_QUOTED:b.doctype+=d,d===b.q&&(b.q="",b.state=V.DOCTYPE);continue;case V.DOCTYPE_DTD:b.doctype+=d,"]"===d?b.state=V.DOCTYPE:l(K,d)&&(b.state=V.DOCTYPE_DTD_QUOTED,b.q=d);continue;case V.DOCTYPE_DTD_QUOTED:b.doctype+=d,d===b.q&&(b.state=V.DOCTYPE_DTD,b.q="");continue;case V.COMMENT:"-"===d?b.state=V.COMMENT_ENDING:b.comment+=d;continue;case V.COMMENT_ENDING:"-"===d?(b.state=V.COMMENT_ENDED,b.comment=q(b.opt,b.comment),b.comment&&o(b,"oncomment",b.comment),b.comment=""):(b.comment+="-"+d,b.state=V.COMMENT);continue;case V.COMMENT_ENDED:">"!==d?(t(b,"Malformed comment"),b.comment+="--"+d,b.state=V.COMMENT):b.state=V.TEXT;continue;case V.CDATA:"]"===d?b.state=V.CDATA_ENDING:b.cdata+=d;continue;case V.CDATA_ENDING:"]"===d?b.state=V.CDATA_ENDING_2:(b.cdata+="]"+d,b.state=V.CDATA);continue;case V.CDATA_ENDING_2:">"===d?(b.cdata&&o(b,"oncdata",b.cdata),o(b,"onclosecdata"),b.cdata="",b.state=V.TEXT):"]"===d?b.cdata+="]":(b.cdata+="]]"+d,b.state=V.CDATA);continue;case V.PROC_INST:"?"===d?b.state=V.PROC_INST_ENDING:l(H,d)?b.state=V.PROC_INST_BODY:b.procInstName+=d;continue;case V.PROC_INST_BODY:if(!b.procInstBody&&l(H,d))continue;"?"===d?b.state=V.PROC_INST_ENDING:b.procInstBody+=d;continue;case V.PROC_INST_ENDING:">"===d?(o(b,"onprocessinginstruction",{name:b.procInstName,body:b.procInstBody}),b.procInstName=b.procInstBody="",b.state=V.TEXT):(b.procInstBody+="?"+d,b.state=V.PROC_INST_BODY);continue;case V.OPEN_TAG:l(S,d)?b.tagName+=d:(u(b),">"===d?x(b):"/"===d?b.state=V.OPEN_TAG_SLASH:(m(H,d)&&t(b,"Invalid character in tag name"),b.state=V.ATTRIB));continue;case V.OPEN_TAG_SLASH:">"===d?(x(b,!0),y(b)):(t(b,"Forward-slash in opening tag not followed by >"),b.state=V.ATTRIB);continue;case V.ATTRIB:if(l(H,d))continue;">"===d?x(b):"/"===d?b.state=V.OPEN_TAG_SLASH:l(R,d)?(b.attribName=d,b.attribValue="",b.state=V.ATTRIB_NAME):t(b,"Invalid attribute name");continue;case V.ATTRIB_NAME:"="===d?b.state=V.ATTRIB_VALUE:">"===d?(t(b,"Attribute without value"),b.attribValue=b.attribName,w(b),x(b)):l(H,d)?b.state=V.ATTRIB_NAME_SAW_WHITE:l(S,d)?b.attribName+=d:t(b,"Invalid attribute name");continue;case V.ATTRIB_NAME_SAW_WHITE:if("="===d)b.state=V.ATTRIB_VALUE;else{if(l(H,d))continue;t(b,"Attribute without value"),b.tag.attributes[b.attribName]="",b.attribValue="",o(b,"onattribute",{name:b.attribName,value:""}),b.attribName="",">"===d?x(b):l(R,d)?(b.attribName=d,b.state=V.ATTRIB_NAME):(t(b,"Invalid attribute name"),b.state=V.ATTRIB)}continue;case V.ATTRIB_VALUE:if(l(H,d))continue;l(K,d)?(b.q=d,b.state=V.ATTRIB_VALUE_QUOTED):(t(b,"Unquoted attribute value"),b.state=V.ATTRIB_VALUE_UNQUOTED,b.attribValue=d);continue;case V.ATTRIB_VALUE_QUOTED:if(d!==b.q){"&"===d?b.state=V.ATTRIB_VALUE_ENTITY_Q:b.attribValue+=d;continue}w(b),b.q="",b.state=V.ATTRIB_VALUE_CLOSED;continue;case V.ATTRIB_VALUE_CLOSED:l(H,d)?b.state=V.ATTRIB:">"===d?x(b):"/"===d?b.state=V.OPEN_TAG_SLASH:l(R,d)?(t(b,"No whitespace between attributes"),b.attribName=d,b.attribValue="",b.state=V.ATTRIB_NAME):t(b,"Invalid attribute name");continue;case V.ATTRIB_VALUE_UNQUOTED:if(m(L,d)){"&"===d?b.state=V.ATTRIB_VALUE_ENTITY_U:b.attribValue+=d;continue}w(b),">"===d?x(b):b.state=V.ATTRIB;continue;case V.CLOSE_TAG:if(b.tagName)">"===d?y(b):l(S,d)?b.tagName+=d:b.script?(b.script+="</"+b.tagName,b.tagName="",b.state=V.SCRIPT):(m(H,d)&&t(b,"Invalid tagname in closing tag"),b.state=V.CLOSE_TAG_SAW_WHITE);else{if(l(H,d))continue;m(R,d)?b.script?(b.script+="</"+d,b.state=V.SCRIPT):t(b,"Invalid tagname in closing tag."):b.tagName=d}continue;case V.CLOSE_TAG_SAW_WHITE:if(l(H,d))continue;">"===d?y(b):t(b,"Invalid characters in closing tag");continue;case V.TEXT_ENTITY:case V.ATTRIB_VALUE_ENTITY_Q:case V.ATTRIB_VALUE_ENTITY_U:var h,i;switch(b.state){case V.TEXT_ENTITY:h=V.TEXT,i="textNode";break;case V.ATTRIB_VALUE_ENTITY_Q:h=V.ATTRIB_VALUE_QUOTED,i="attribValue";break;case V.ATTRIB_VALUE_ENTITY_U:h=V.ATTRIB_VALUE_UNQUOTED,i="attribValue"}";"===d?(b[i]+=z(b),b.entity="",b.state=h):l(b.entity.length?U:T,d)?b.entity+=d:(t(b,"Invalid character in entity name"),b[i]+="&"+b.entity+d,b.entity="",b.state=h);continue;default:throw new Error(b,"Unknown state: "+b.state)}}return b.position>=b.bufferCheckPosition&&e(b),b}c.parser=function(a,b){return new d(a,b)},c.SAXParser=d,c.SAXStream=i,c.createStream=h,c.MAX_BUFFER_LENGTH=65536;var D=["comment","sgmlDecl","textNode","tagName","doctype","procInstName","procInstBody","entity","attribName","attribValue","cdata","script"];c.EVENTS=["text","processinginstruction","sgmldeclaration","doctype","comment","attribute","opentag","closetag","opencdata","cdata","closecdata","error","end","ready","script","opennamespace","closenamespace"],Object.create||(Object.create=function(a){function b(){}b.prototype=a;var c=new b;return c}),Object.keys||(Object.keys=function(a){var b=[];for(var c in a)a.hasOwnProperty(c)&&b.push(c);return b}),d.prototype={end:function(){s(this)},write:C,resume:function(){return this.error=null,this},close:function(){return this.write(null)},flush:function(){g(this)}};var E;try{E=a("stream").Stream}catch(F){E=function(){}}var G=c.EVENTS.filter(function(a){return"error"!==a&&"end"!==a});i.prototype=Object.create(E.prototype,{constructor:{value:i}}),i.prototype.write=function(c){if("function"==typeof b&&"function"==typeof b.isBuffer&&b.isBuffer(c)){if(!this._decoder){var d=a("string_decoder").StringDecoder;this._decoder=new d("utf8")}c=this._decoder.write(c)}return this._parser.write(c.toString()),this.emit("data",c),!0},i.prototype.end=function(a){return a&&a.length&&this.write(a),this._parser.end(),!0},i.prototype.on=function(a,b){var c=this;return c._parser["on"+a]||-1===G.indexOf(a)||(c._parser["on"+a]=function(){var b=1===arguments.length?[arguments[0]]:Array.apply(null,arguments);b.splice(0,0,a),c.emit.apply(c,b)}),E.prototype.on.call(c,a,b)};var H="\r\n	 ",I="0124356789",J="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ",K="'\"",L=H+">",M="[CDATA[",N="DOCTYPE",O="http://www.w3.org/XML/1998/namespace",P="http://www.w3.org/2000/xmlns/",Q={xml:O,xmlns:P};H=j(H),I=j(I),J=j(J);var R=/[:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]/,S=/[:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\u00B7\u0300-\u036F\u203F-\u2040\.\d-]/,T=/[#:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]/,U=/[#:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\u00B7\u0300-\u036F\u203F-\u2040\.\d-]/;K=j(K),L=j(L);var V=0;c.STATE={BEGIN:V++,BEGIN_WHITESPACE:V++,TEXT:V++,TEXT_ENTITY:V++,OPEN_WAKA:V++,SGML_DECL:V++,SGML_DECL_QUOTED:V++,DOCTYPE:V++,DOCTYPE_QUOTED:V++,DOCTYPE_DTD:V++,DOCTYPE_DTD_QUOTED:V++,COMMENT_STARTING:V++,COMMENT:V++,COMMENT_ENDING:V++,COMMENT_ENDED:V++,CDATA:V++,CDATA_ENDING:V++,CDATA_ENDING_2:V++,PROC_INST:V++,PROC_INST_BODY:V++,PROC_INST_ENDING:V++,OPEN_TAG:V++,OPEN_TAG_SLASH:V++,ATTRIB:V++,ATTRIB_NAME:V++,ATTRIB_NAME_SAW_WHITE:V++,ATTRIB_VALUE:V++,ATTRIB_VALUE_QUOTED:V++,ATTRIB_VALUE_CLOSED:V++,ATTRIB_VALUE_UNQUOTED:V++,ATTRIB_VALUE_ENTITY_Q:V++,ATTRIB_VALUE_ENTITY_U:V++,CLOSE_TAG:V++,CLOSE_TAG_SAW_WHITE:V++,SCRIPT:V++,SCRIPT_ENDING:V++},c.XML_ENTITIES={amp:"&",gt:">",lt:"<",quot:'"',apos:"'"},c.ENTITIES={amp:"&",gt:">",lt:"<",quot:'"',apos:"'",AElig:198,Aacute:193,Acirc:194,Agrave:192,Aring:197,Atilde:195,Auml:196,Ccedil:199,ETH:208,Eacute:201,Ecirc:202,Egrave:200,Euml:203,Iacute:205,Icirc:206,Igrave:204,Iuml:207,Ntilde:209,Oacute:211,Ocirc:212,Ograve:210,Oslash:216,Otilde:213,Ouml:214,THORN:222,Uacute:218,Ucirc:219,Ugrave:217,Uuml:220,Yacute:221,aacute:225,acirc:226,aelig:230,agrave:224,aring:229,atilde:227,auml:228,ccedil:231,eacute:233,ecirc:234,egrave:232,eth:240,euml:235,iacute:237,icirc:238,igrave:236,iuml:239,ntilde:241,oacute:243,ocirc:244,ograve:242,oslash:248,otilde:245,ouml:246,szlig:223,thorn:254,uacute:250,ucirc:251,ugrave:249,uuml:252,yacute:253,yuml:255,copy:169,reg:174,nbsp:160,iexcl:161,cent:162,pound:163,curren:164,yen:165,brvbar:166,sect:167,uml:168,ordf:170,laquo:171,not:172,shy:173,macr:175,deg:176,plusmn:177,sup1:185,sup2:178,sup3:179,acute:180,micro:181,para:182,middot:183,cedil:184,ordm:186,raquo:187,frac14:188,frac12:189,frac34:190,iquest:191,times:215,divide:247,OElig:338,oelig:339,Scaron:352,scaron:353,Yuml:376,fnof:402,circ:710,tilde:732,Alpha:913,Beta:914,Gamma:915,Delta:916,Epsilon:917,Zeta:918,Eta:919,Theta:920,Iota:921,Kappa:922,Lambda:923,Mu:924,Nu:925,Xi:926,Omicron:927,Pi:928,Rho:929,Sigma:931,Tau:932,Upsilon:933,Phi:934,Chi:935,Psi:936,Omega:937,alpha:945,beta:946,gamma:947,delta:948,epsilon:949,zeta:950,eta:951,theta:952,iota:953,kappa:954,lambda:955,mu:956,nu:957,xi:958,omicron:959,pi:960,rho:961,sigmaf:962,sigma:963,tau:964,upsilon:965,phi:966,chi:967,psi:968,omega:969,thetasym:977,upsih:978,piv:982,ensp:8194,emsp:8195,thinsp:8201,zwnj:8204,zwj:8205,lrm:8206,rlm:8207,ndash:8211,mdash:8212,lsquo:8216,rsquo:8217,sbquo:8218,ldquo:8220,rdquo:8221,bdquo:8222,dagger:8224,Dagger:8225,bull:8226,hellip:8230,permil:8240,prime:8242,Prime:8243,lsaquo:8249,rsaquo:8250,oline:8254,frasl:8260,euro:8364,image:8465,weierp:8472,real:8476,trade:8482,alefsym:8501,larr:8592,uarr:8593,rarr:8594,darr:8595,harr:8596,crarr:8629,lArr:8656,uArr:8657,rArr:8658,dArr:8659,hArr:8660,forall:8704,part:8706,exist:8707,empty:8709,nabla:8711,isin:8712,notin:8713,ni:8715,prod:8719,sum:8721,minus:8722,lowast:8727,radic:8730,prop:8733,infin:8734,ang:8736,and:8743,or:8744,cap:8745,cup:8746,"int":8747,there4:8756,sim:8764,cong:8773,asymp:8776,ne:8800,equiv:8801,le:8804,ge:8805,sub:8834,sup:8835,nsub:8836,sube:8838,supe:8839,oplus:8853,otimes:8855,perp:8869,sdot:8901,lceil:8968,rceil:8969,lfloor:8970,rfloor:8971,lang:9001,rang:9002,loz:9674,spades:9824,clubs:9827,hearts:9829,diams:9830},Object.keys(c.ENTITIES).forEach(function(a){var b=c.ENTITIES[a],d="number"==typeof b?String.fromCharCode(b):b;c.ENTITIES[a]=d});for(var W in c.STATE)c.STATE[c.STATE[W]]=W;V=c.STATE,String.fromCodePoint||!function(){var a=String.fromCharCode,b=Math.floor,c=function(){var c,d,e=16384,f=[],g=-1,h=arguments.length;if(!h)return"";for(var i="";++g<h;){var j=Number(arguments[g]);if(!isFinite(j)||0>j||j>1114111||b(j)!==j)throw RangeError("Invalid code point: "+j);65535>=j?f.push(j):(j-=65536,c=(j>>10)+55296,d=j%1024+56320,f.push(c,d)),(g+1===h||f.length>e)&&(i+=a.apply(null,f),f.length=0)}return i};Object.defineProperty?Object.defineProperty(String,"fromCodePoint",{value:c,configurable:!0,writable:!0}):String.fromCodePoint=c}()}("undefined"==typeof c?this.sax={}:c)}).call(this,a("buffer").Buffer)},{buffer:3,stream:25,string_decoder:26}],44:[function(a,b,c){"use strict";var d=d||{};d.Transform=d.Transform||("undefined"!=typeof a?a("./transform.js"):null);var e=e||("undefined"!=typeof a?a("../lib/base64-binary.js"):null),f=f||("undefined"!=typeof a?a("pako"):null);d.NIFTI_INTENT_GENMATRIX="NIFTI_INTENT_GENMATRIX",d.NIFTI_INTENT_LABEL="NIFTI_INTENT_LABEL",d.NIFTI_INTENT_NODE_INDEX="NIFTI_INTENT_NODE_INDEX",d.NIFTI_INTENT_POINTSET="NIFTI_INTENT_POINTSET",d.NIFTI_INTENT_RGB_VECTOR="NIFTI_INTENT_RGB_VECTOR",d.NIFTI_INTENT_RGBA_VECTOR="NIFTI_INTENT_RGBA_VECTOR",d.NIFTI_INTENT_SHAPE="NIFTI_INTENT_SHAPE",d.NIFTI_INTENT_TIME_SERIES="NIFTI_INTENT_TIME_SERIES",d.NIFTI_INTENT_TRIANGLE="NIFTI_INTENT_TRIANGLE",d.NIFTI_INTENT_NONE="NIFTI_INTENT_NONE",d.NIFTI_INTENT_VECTOR="NIFTI_INTENT_VECTOR",d.ATT_ARRAYINDEXINGORDER="ArrayIndexingOrder",d.ATT_DATATYPE="DataType",d.ATT_DIMENSIONALITY="Dimensionality",d.ATT_DIMN="Dim",d.ATT_ENCODING="Encoding",d.ATT_ENDIAN="Endian",d.ATT_EXTERNALFILENAME="ExternalFileName",d.ATT_EXTERNALFILEOFFSET="ExternalFileOffset",d.ATT_INTENT="Intent",d.ENCODING_ASCII="ASCII",d.ENCODING_BASE64BINARY="Base64Binary",d.ENCODING_GZIPBASE64BINARY="GZipBase64Binary",d.ENCODING_EXTERNALFILEBINARY="ExternalFileBinary",d.TYPE_NIFTI_TYPE_UINT8="NIFTI_TYPE_UINT8",d.TYPE_NIFTI_TYPE_INT32="NIFTI_TYPE_INT32",d.TYPE_NIFTI_TYPE_FLOAT32="NIFTI_TYPE_FLOAT32",d.DataArray=d.DataArray||function(){this.attributes=null,this.metadata={},this.transforms=[],this.data=null,this.dataConverted=!1},d.DataArray.prototype.isPointSet=function(){return this.attributes[d.ATT_INTENT]===d.NIFTI_INTENT_POINTSET},d.DataArray.prototype.isTriangles=function(){return this.attributes[d.ATT_INTENT]===d.NIFTI_INTENT_TRIANGLE},d.DataArray.prototype.isNormals=function(){return this.attributes[d.ATT_INTENT]===d.NIFTI_INTENT_VECTOR},d.DataArray.prototype.isColors=function(){return this.attributes[d.ATT_INTENT]===d.NIFTI_INTENT_RGB_VECTOR||this.attributes[d.ATT_INTENT]===d.NIFTI_INTENT_RGBA_VECTOR},d.DataArray.prototype.getDimensions=function(){return parseInt(this.attributes[d.ATT_DIMENSIONALITY])},d.DataArray.prototype.getNumElements=function(a){return void 0===a&&(a=0),parseInt(this.attributes[d.ATT_DIMN+a])},d.DataArray.prototype.isScalar=function(){return 1==this.getDimensions()},d.DataArray.prototype.isTriple=function(){return 2==this.getDimensions()&&3==this.getNumElements(1)},d.DataArray.prototype.isQuad=function(){return 2==this.getDimensions()&&4==this.getNumElements(1)},d.DataArray.prototype.isAscii=function(){return d.ENCODING_ASCII===this.attributes[d.ATT_ENCODING]},d.DataArray.prototype.isBase64Binary=function(){return d.ENCODING_BASE64BINARY===this.attributes[d.ATT_ENCODING]},d.DataArray.prototype.isGzipBase64Binary=function(){return d.ENCODING_GZIPBASE64BINARY===this.attributes[d.ATT_ENCODING]},d.DataArray.prototype.isBase64Encoded=function(){return this.isBase64Binary()||this.isGzipBase64Binary()},d.DataArray.prototype.isFloat32=function(){return d.TYPE_NIFTI_TYPE_FLOAT32===this.attributes[d.ATT_DATATYPE]},d.DataArray.prototype.isInt32=function(){return d.TYPE_NIFTI_TYPE_INT32===this.attributes[d.ATT_DATATYPE]},d.DataArray.prototype.isUnsignedInt8=function(){return d.TYPE_NIFTI_TYPE_UINT8===this.attributes[d.ATT_DATATYPE]},d.DataArray.prototype.getData=function(){return this.dataConverted||(this.dataConverted=!0,this.isAscii()?this.isUnsignedInt8()?d.DataArray.readUnsignedInt8ASCII(this):this.isInt32()?d.DataArray.readSignedInt32ASCII(this):d.DataArray.readFloat32ASCII(this):this.isBase64Binary()?this.isUnsignedInt8()?d.DataArray.readUnsignedInt8Base64(this):this.isInt32()?d.DataArray.readSignedInt32Base64(this):d.DataArray.readFloat32Base64(this):this.isGzipBase64Binary()&&(this.isUnsignedInt8()?d.DataArray.readUnsignedInt8GZIPBase64(this):this.isInt32()?d.DataArray.readSignedInt32GZIPBase64(this):d.DataArray.readFloat32GZIPBase64(this))),this.data},d.DataArray.prototype.getDataAsync=function(a,b){this.dataConverted||(this.dataConverted=!0,this.isAscii()?(this.isUnsignedInt8()?d.DataArray.readUnsignedInt8ASCII(this):this.isInt32()?d.DataArray.readSignedInt32ASCII(this):d.DataArray.readFloat32ASCII(this),b()):this.isBase64Binary()?(this.isUnsignedInt8()?d.DataArray.readUnsignedInt8Base64(this):this.isInt32()?d.DataArray.readSignedInt32Base64(this):d.DataArray.readFloat32Base64(this),b()):this.isGzipBase64Binary()&&(this.isUnsignedInt8()?d.DataArray.readUnsignedInt8GZIPBase64Async(this,a,b):this.isInt32()?d.DataArray.readSignedInt32GZIPBase64Async(this,a,b):d.DataArray.readFloat32GZIPBase64Async(this,a,b)))},d.DataArray.readFloat32ASCII=function(a){var b=/[+-]?\d+(\.\d+)?/g;a.data=new Float32Array(a.data.match(b).map(function(a){return parseFloat(a)}))},d.DataArray.readSignedInt32ASCII=function(a){var b=/[+-]?\d+(\.\d+)?/g;a.data=new Int32Array(a.data.match(b).map(function(a){return parseInt(a)}))},d.DataArray.readUnsignedInt8ASCII=function(a){var b=/[+-]?\d+(\.\d+)?/g;a.data=new Uint8Array(a.data.match(b).map(function(a){return parseInt(a)}))},d.DataArray.readUnsignedInt8Base64=function(a){var b=e.decodeArrayBuffer(a.data);a.data=new Uint8Array(b,0,b.byteLength)},d.DataArray.readSignedInt32Base64=function(a){var b=e.decodeArrayBuffer(a.data);a.data=new Int32Array(b,0,b.byteLength/4)},d.DataArray.readFloat32Base64=function(a){var b=e.decodeArrayBuffer(a.data);a.data=new Float32Array(b,0,b.byteLength/4)},d.DataArray.readUnsignedInt8GZIPBase64=function(a){var b=e.decodeArrayBuffer(a.data);b=f.inflate(b).buffer,a.data=new Uint8Array(b,0,b.byteLength)},d.DataArray.readUnsignedInt8GZIPBase64Async=function(a,b,c){var g=e.decodeArrayBuffer(a.data),h=new f.Inflate,i=function(){a.data=new Uint8Array(h.result.buffer,0,h.result.buffer.byteLength),c(a.data)};setTimeout(function(){d.DataArray.readNext(h,g,0,b,i)},0)},d.DataArray.readSignedInt32GZIPBase64=function(a){var b=e.decodeArrayBuffer(a.data);b=f.inflate(b).buffer,a.data=new Int32Array(b,0,b.byteLength/4)},d.DataArray.readSignedInt32GZIPBase64Async=function(a,b,c){var g=e.decodeArrayBuffer(a.data),h=new f.Inflate,i=function(){a.data=new Int32Array(h.result.buffer,0,h.result.buffer.byteLength/4),c(a.data)};setTimeout(function(){d.DataArray.readNext(h,g,0,b,i)},0)},d.DataArray.readFloat32GZIPBase64=function(a){var b=e.decodeArrayBuffer(a.data);b=f.inflate(b).buffer,a.data=new Float32Array(b,0,b.byteLength/4)},d.DataArray.readFloat32GZIPBase64Async=function(a,b,c){var g=e.decodeArrayBuffer(a.data),h=new f.Inflate,i=function(){a.data=new Float32Array(h.result.buffer,0,h.result.buffer.byteLength/4),c(a.data)};setTimeout(function(){d.DataArray.readNext(h,g,0,b,i)},0)},d.DataArray.readNext=function(a,b,c,e,f){var g=c+32768,h=g>=b.byteLength;a.push(b.slice(c,c+32768),h),h?f():(e(g/b.byteLength),setTimeout(function(){d.DataArray.readNext(a,b,g,e,f)},0))};var g=typeof b;"undefined"!==g&&b.exports&&(b.exports=d.DataArray)},{"../lib/base64-binary.js":1,"./transform.js":47,pako:27}],45:[function(a,b,c){"use strict";var d=d||{};d.Utils=d.Utils||("undefined"!=typeof a?a("./utilities.js"):null),d.DataArray=d.DataArray||("undefined"!=typeof a?a("./dataArray.js"):null),d.Transform=d.Transform||("undefined"!=typeof a?a("./transform.js"):null),d.Label=d.Label||("undefined"!=typeof a?a("./label.js"):null);var e=e||("undefined"!=typeof a?a("sax"):null);d.TAG_TRANSFORM="CoordinateSystemTransformMatrix",d.TAG_DATA="Data",d.TAG_DATAARRAY="DataArray",d.TAG_DATASPACE="DataSpace",d.TAG_GIFTI="GIFTI",d.TAG_LABEL="Label",d.TAG_LABELTABLE="LabelTable",d.TAG_MATRIXDATA="MatrixData",d.TAG_METADATA="MetaData",d.TAG_MD="MD",d.TAG_NAME="Name",d.TAG_TRANSFORMEDSPACE="TransformedSpace",d.TAG_VALUE="Value",d.GIFTI=d.GIFTI||function(){this.attributes=null,this.metadata={},this.dataArrays=[],this.labelTable=[]},d.GIFTI.prototype.getPointsDataArray=function(){var a;for(a=0;a<this.dataArrays.length;a+=1)if(this.dataArrays[a].isPointSet())return this.dataArrays[a];return null},d.GIFTI.prototype.getTrianglesDataArray=function(){var a;for(a=0;a<this.dataArrays.length;a+=1)if(this.dataArrays[a].isTriangles())return this.dataArrays[a];return null},d.GIFTI.prototype.getNormalsDataArray=function(){var a;for(a=0;a<this.dataArrays.length;a+=1)if(this.dataArrays[a].isNormals())return this.dataArrays[a];return null},d.GIFTI.prototype.getColorsDataArray=function(){var a;for(a=0;a<this.dataArrays.length;a+=1)if(this.dataArrays[a].isColors())return this.dataArrays[a];return null},d.GIFTI.prototype.getNumPoints=function(){var a;for(a=0;a<this.dataArrays.length;a+=1)if(this.dataArrays[a].isPointSet())return this.dataArrays[a].getNumElements();return 0},d.GIFTI.prototype.getNumTriangles=function(){var a;for(a=0;a<this.dataArrays.length;a+=1)if(this.dataArrays[a].isTriangles())return this.dataArrays[a].getNumElements();return 0},d.isThisFormat=function(a){return-1!==a.indexOf(".gii")},d.parse=function(a){var b=e.parser(!0),c=null,f=null,g=null,h=null,i=null,j=null,k="",l=null,m=!1,n=!1,o=!1,p=!1,q=!1,r=!1,s=!1,t=!1,u=!1,v=!1,w=!1,x=!1,y=!1;return b.onopentag=function(a){a.name===d.TAG_GIFTI?(m=!0,g=c=new d.GIFTI,c.attributes=a.attributes):a.name===d.TAG_METADATA?n=!0:a.name===d.TAG_MD?o=!0:a.name===d.TAG_NAME?p=!0:a.name===d.TAG_VALUE?q=!0:a.name===d.TAG_LABELTABLE?x=!0:a.name===d.TAG_LABEL?(y=!0,l=new d.Label(a.attributes)):a.name===d.TAG_DATAARRAY?(r=!0,g=f=new d.DataArray,c.dataArrays.push(f),f.attributes=a.attributes):a.name===d.TAG_TRANSFORM?(s=!0,j=new d.Transform,f.transforms.push(j)):a.name===d.TAG_DATASPACE?t=!0:a.name===d.TAG_TRANSFORMEDSPACE?u=!0:a.name===d.TAG_MATRIXDATA?v=!0:a.name===d.TAG_DATA&&(w=!0)},b.ontext=b.oncdata=function(a){p?k+=a:q?k+=a:t?k+=a:u?k+=a:v?k+=a:w?k+=a:y&&(k+=a)},b.onclosetag=function(a){a===d.TAG_GIFTI?m=!1:a===d.TAG_METADATA?n=!1:a===d.TAG_MD?(o=!1,g&&(g.metadata[h]=i)):a===d.TAG_NAME?(p=!1,h=k,k=""):a===d.TAG_VALUE?(q=!1,i=k,k=""):a===d.TAG_LABELTABLE?x=!1:a===d.TAG_LABEL?(l.label=k.trim(),c.labelTable[l.key]=l,k=""):a===d.TAG_DATAARRAY?r=!1:a===d.TAG_TRANSFORM?s=!1:a===d.TAG_DATASPACE?(t=!1,j.dataSpace=k,k=""):a===d.TAG_TRANSFORMEDSPACE?(u=!1,j.transformedSpace=k,k=""):a===d.TAG_MATRIXDATA?(v=!1,j.matrixData=k,k=""):a===d.TAG_DATA&&(w=!1,f.data=k,k="")},b.onerror=function(a){console.log(a)},b.write(a).close(),c};var f=typeof b;"undefined"!==f&&b.exports&&(b.exports=d)},{"./dataArray.js":44,"./label.js":46,"./transform.js":47,"./utilities.js":48,sax:43}],46:[function(a,b,c){"use strict";var d=d||{};d.ATT_KEY="Key",d.ATT_RED="Red",d.ATT_GREEN="Green",d.ATT_BLUE="Blue",d.ATT_ALPHA="Alpha",d.ATT_INDEX="Index",d.Label=d.Label||function(a){this.key=a[d.ATT_KEY]||a[d.ATT_INDEX],this.r=parseFloat(a[d.ATT_RED]),this.g=parseFloat(a[d.ATT_GREEN]),this.b=parseFloat(a[d.ATT_BLUE]),this.a=parseFloat(a[d.ATT_ALPHA]),this.label=null};var e=typeof b;"undefined"!==e&&b.exports&&(b.exports=d.Label)},{}],47:[function(a,b,c){"use strict";var d=d||{};d.Transform=d.Transform||function(){this.dataSpace=null,this.transformedSpace=null,this.matrixData=null};var e=typeof b;"undefined"!==e&&b.exports&&(b.exports=d.Transform)},{}],48:[function(a,b,c){"use strict";var d=d||{};d.Utils=d.Utils||{},d.Utils.crcTable=null,d.Utils.makeCRCTable=function(){for(var a,b=[],c=0;256>c;c++){a=c;for(var d=0;8>d;d++)a=1&a?3988292384^a>>>1:a>>>1;b[c]=a}return b},d.Utils.crc32=function(a){for(var b=d.Utils.crcTable||(d.Utils.crcTable=d.Utils.makeCRCTable()),c=-1,e=0;e<a.byteLength;e++)c=c>>>8^b[255&(c^a.getUint8(e))];return(-1^c)>>>0};var e=typeof b;"undefined"!==e&&b.exports&&(b.exports=d.Utils)},{}]},{},[45])(45)});