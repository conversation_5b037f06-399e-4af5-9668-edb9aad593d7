/*
* BrainBrowser: Web-based Neurological Visualization Tools
* (https://brainbrowser.cbrain.mcgill.ca)
*
* Copyright (C) 2011
* The Royal Institution for the Advancement of Learning
* McGill University
*
* This program is free software: you can redistribute it and/or modify
* it under the terms of the GNU Affero General Public License as
* published by the Free Software Foundation, either version 3 of the
* License, or (at your option) any later version.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU Affero General Public License for more details.
*
* You should have received a copy of the GNU Affero General Public License
* along with this program.  If not, see <http://www.gnu.org/licenses/>.
*/

/*
* BrainBrowser v2.5.5
*
* Author: <PERSON><PERSON><PERSON>  <<EMAIL>> (http://tareksherif.ca/)
* Author: <PERSON>
* Author: <PERSON>
*
* three.js (c) 2010-2014 three.js authors, used under the MIT license
*/
!function(){"use strict";function a(a){var b=!1,c=!1,d=!1,e=!1,f=document.createElement("canvas"),g=null;b=!!f,c=!!window.Worker;try{f&&window.WebGLRenderingContext&&(g=f.getContext("webgl")||f.getContext("experimental-webgl")),d=!!g}catch(h){d=!1}d&&(e=!!g.getExtension("OES_element_index_uint")),a.CANVAS_ENABLED=b,a.WEB_WORKERS_ENABLED=c,a.WEBGL_ENABLED=d,a.WEBGL_UINT_INDEX_ENABLED=e}var b="2.5.5";b=b.indexOf("BRAINBROWSER_VERSION")>0?"D.E.V":b;var c=window.BrainBrowser={version:b};a(c),window.requestAnimationFrame=window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.oRequestAnimationFrame||window.msRequestAnimationFrame||function(a){return window.setTimeout(a,1e3/60)},window.cancelAnimationFrame=window.cancelAnimationFrame||function(a){window.clearTimeout(a)}}(),function(){"use strict";function a(b,c,d,e){return c>d?e(b):void Object.keys(b).forEach(function(f){a(b[f],c+1,d,e)})}BrainBrowser.createTreeStore=function(){var b={};return{set:function(){var a,c,d,e,f=arguments[arguments.length-1],g=Array.prototype.slice.call(arguments,0,arguments.length-1),h=b;for(c=0,d=g.length-1;d>c;c++){if(a=g[c],h[a]&&"object"!=typeof h[a])throw e="Hash key '["+g.slice(0,c+1).join("][")+"]' has already been set to a non-object value.\nCannot set '["+g.join("][")+"]'",BrainBrowser.events.triggerEvent("error",{message:e}),new Error(e);h[a]||(h[a]={}),h=h[a]}a=g[c],h[a]=f},get:function(){var a,c,d,e=Array.prototype.slice.call(arguments),f=b;if(0===e.length)return b;for(c=0,d=e.length-1;d>c;c++){if(a=e[c],void 0===f[a])return null;f=f[a]}return a=e[c],void 0!==f[a]?f[a]:null},remove:function(){var a,c,d,e,f=Array.prototype.slice.call(arguments),g=b;for(c=0,d=f.length-1;d>c;c++){if(a=f[c],void 0===g[a])return null;g=g[a]}return a=f[c],e=g[a],g[a]=void 0,e},reset:function(a){a=a&&"object"==typeof a?a:{},b=a},forEach:function(c,d){c=c>0?c:1,a(b,1,c,d)}}}}(),function(){"use strict";BrainBrowser.createColorMap=function(a,b){function c(a,b,c,d,e,f,g){var h;return(b>a||a>c)&&!e?-1:(h=Math.floor(Math.max(0,Math.min((a-b)*d,g-1))),f&&(h=g-1-h),h*=4)}function d(a,b,c){var d,e,f,g=document.createElement("canvas"),h=new Array(256);for(g.width=256,g.height=c,d=0;256>d;d++)h[d]=d;for(f=r.scale,r.scale=255,a=r.mapColors(h),r.scale=f,e=g.getContext("2d"),d=0;256>d;d++)e.fillStyle="rgb("+Math.floor(a[4*d])+", "+Math.floor(a[4*d+1])+", "+Math.floor(a[4*d+2])+")",e.fillRect(d,0,1,b);return g}b=b||{};var e,f,g,h,i,j,k,l,m=void 0===b.clamp?!0:b.clamp,n=b.flip||!1,o=b.scale||1,p=b.contrast||1,q=b.brightness||0;if(a)for(f=a.trim().split(/\n/),e=[],k=0,i=0,g=f.length;g>i;i++)if(l=f[i].trim().split(/\s+/).slice(0,5),h=l.length,!(3>h)){for(h>4&&(k=parseInt(l[0],10),k*=4,h=4,l=l.slice(1,5)),j=0;h>j;j++)e[k+j]=parseFloat(l[j]);4>h&&(e[k+3]=1),k+=4}var r={colors:e,clamp:m,flip:n,scale:o,contrast:p,brightness:q,mapColors:function(a,b){b=b||{};var d,e,f,g,h,i,j=void 0===b.min?0:b.min,k=void 0===b.max?255:b.max,l=b.default_colors||[0,0,0,1],m=b.destination||new Float32Array(4*a.length),n=r.colors,o=r.colors.length/4,p=void 0===b.scale?r.scale:b.scale,q=void 0===b.clamp?r.clamp:b.clamp,s=void 0===b.flip?r.flip:b.flip,t=void 0===b.brightness?r.brightness:b.brightness,u=void 0===b.contrast?r.contrast:b.contrast,v=4===l.length?0:1,w=k-j,x=o/w;for(t*=p,u*=p,e=0,h=a.length;h>e;e++)d=a[e],f=4*e,i=c(d,j,k,x,q,s,o),0>i?(g=f*v,m[f]=u*l[g]+t,m[f+1]=u*l[g+1]+t,m[f+2]=u*l[g+2]+t,m[f+3]=p*l[g+3]):(m[f]=u*n[i]+t,m[f+1]=u*n[i+1]+t,m[f+2]=u*n[i+2]+t,m[f+3]=p*n[i+3]);return m},colorFromValue:function(a,b){b=b||{};var d,e=b.hex||!1,f=void 0===b.min?0:b.min,g=void 0===b.max?255:b.max,h=void 0===b.scale?r.scale:b.scale,i=void 0===b.brightness?r.brightness:b.brightness,j=void 0===b.contrast?r.contrast:b.contrast,k=g-f,l=r.colors.length/4,m=l/k,n=c(a,f,g,m,r.clamp,r.flip,l);return d=n>=0?Array.prototype.slice.call(r.colors,n,n+4):[0,0,0,1],d[0]=Math.max(0,Math.min(j*d[0]+i,1)),d[1]=Math.max(0,Math.min(j*d[1]+i,1)),d[2]=Math.max(0,Math.min(j*d[2]+i,1)),e?(d[0]=Math.floor(255*d[0]),d[1]=Math.floor(255*d[1]),d[2]=Math.floor(255*d[2]),d[3]=Math.floor(255*d[3]),d[0]=("0"+d[0].toString(16)).slice(-2),d[1]=("0"+d[1].toString(16)).slice(-2),d[2]=("0"+d[2].toString(16)).slice(-2),d=d.slice(0,3).join("")):(d[0]=d[0]*h,d[1]=d[1]*h,d[2]=d[2]*h,d[3]=d[3]*h),d},createElement:function(a,b){var c,e,f=r.colors,g=b-a;return c=d(f,20,40,n),e=c.getContext("2d"),e.fillStyle="#FFA000",e.fillRect(.5,20,1,10),e.fillText(a.toPrecision(3),.5,40),e.fillRect(c.width/4,20,1,10),e.fillText((a+.25*g).toPrecision(3),.25*c.width,40),e.fillRect(c.width/2,20,1,10),e.fillText((a+.5*g).toPrecision(3),.5*c.width,40),e.fillRect(3*c.width/4,20,1,10),e.fillText((a+.75*g).toPrecision(3),.75*c.width,40),e.fillRect(c.width-.5,20,1,10),e.fillText(b.toPrecision(3),c.width-20,40),c}};return r}}(),function(){"use strict";var a=BrainBrowser.createTreeStore();BrainBrowser.config={set:function(b,c){b=b||"";var d=b.split(".");d.push(c),a.set.apply(a,d)},get:function(b){b=b||"";var c=b.split(".");return a.get.apply(a,c)}}}(),function(){"use strict";function a(a,b){try{a.call(b.target,b)}catch(c){console.error("Error in event handler for: ",b.name),console.error(c.stack||c.message||c)}}var b=["eventmodelcleanup"];BrainBrowser.events={unpropagatedEvent:function(a){b.push(a)},addEventModel:function(c){var d=[],e={};c.addEventListener=function(a,b){d[a]||(d[a]=[]),d[a].push(b)},c.triggerEvent=function(e,f){var g=this,h=c.directPropagationTargets(e);f=f||{},f.name=e,f.target=g,d[e]&&d[e].forEach(function(b){a(b,f)}),d["*"]&&d["*"].forEach(function(b){a(b,f)}),-1===b.indexOf(e)&&(h.forEach(function(a){a.triggerEvent.call(g,e,f)}),0===h.length&&c!==BrainBrowser.events&&BrainBrowser.events.triggerEvent.call(g,e,f))},c.propagateEventTo=function(a,b){if(!BrainBrowser.utils.isFunction(b.allPropagationTargets))throw new Error("Propagation target doesn't seem to have an event model.");if(c===BrainBrowser.events||-1!==b.allPropagationTargets(a).indexOf(c))throw new Error("Propagating event '"+a+"' would cause a cycle.");e[a]=e[a]||[],-1===c.directPropagationTargets().indexOf(b)&&b.addEventListener("eventmodelcleanup",function(){this===b&&c.stopPropagatingTo(b)}),-1===e[a].indexOf(b)&&e[a].push(b)},c.propagateEventFrom=function(a,b){b.propagateEventTo(a,c)},c.stopPropagatingTo=function(a){Object.keys(e).forEach(function(b){e[b]=e[b].filter(function(b){return b!==a})})},c.directPropagationTargets=function(a){var b=[],c=void 0===a?Object.keys(e):[a,"*"];return c.forEach(function(a){var c=e[a]||[];c.forEach(function(a){-1===b.indexOf(a)&&b.push(a)})}),b},c.allPropagationTargets=function(a){var b=c.directPropagationTargets(a),d=Array.prototype.slice.call(b);return b.forEach(function(b){b.allPropagationTargets(a).forEach(function(a){-1===d.indexOf(a)&&d.push(a)})}),d}}},BrainBrowser.events.addEventModel(BrainBrowser.events)}(),function(){"use strict";var a=BrainBrowser.loader={loadFromURL:function(b,c,d){d=d||{};var e,f=new XMLHttpRequest,g=d.result_type,h=b.split("/"),i=h[h.length-1];f.open("GET",b),"arraybuffer"===g&&(f.responseType="arraybuffer"),f.onreadystatechange=function(){if(4===f.readyState){if(e=f.status,!(e>=200&&300>e||304===e)){var g="error loading URL: "+b+"\nHTTP Response: "+f.status+"\nHTTP Status: "+f.statusText+"\nResponse was: \n"+f.response;throw BrainBrowser.events.triggerEvent("error",{message:g}),new Error(g)}a.checkCancel(d)||c(f.response,i,d)}},f.send()},loadFromFile:function(a,b,c){var d=a.files;if(0!==d.length){c=c||{};var e=c.result_type,f=new FileReader,g=a.value.split("\\"),h=g[g.length-1];f.file=d[0],f.onloadend=function(a){var d=a.target.result;try{var f=pako.inflate(d);d=f.buffer}catch(g){}finally{if("arraybuffer"!==e)if("function"!=typeof TextDecoder){var i=new Blob([d]),j=new FileReader;j.onload=function(a){b(a.target.result,h,c)},j.readAsText(i)}else{var k=new DataView(d),l=new TextDecoder;d=l.decode(k),b(d,h,c)}else b(d,h,c)}},f.onerror=function(){var a="error reading file: "+h;throw BrainBrowser.events.triggerEvent("error",{message:a}),new Error(a)},f.readAsArrayBuffer(d[0])}},loadColorMapFromURL:function(b,c,d){a.loadFromURL(b,function(a,b,d){c(BrainBrowser.createColorMap(a,d),b,d)},d)},loadColorMapFromFile:function(b,c,d){a.loadFromFile(b,function(a,b,d){c(BrainBrowser.createColorMap(a,d),b,d)},d)},checkCancel:function(a){a=a||{},BrainBrowser.utils.isFunction(a)&&(a={test:a});var b=a.test,c=a.cleanup,d=!1;return b&&b()&&(d=!0,c&&c()),d}}}(),function(a){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=a();else if("function"==typeof define&&define.amd)define([],a);else{var b;b="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this,b.pako=a()}}(function(){return function a(b,c,d){function e(g,h){if(!c[g]){if(!b[g]){var i="function"==typeof require&&require;if(!h&&i)return i(g,!0);if(f)return f(g,!0);var j=new Error("Cannot find module '"+g+"'");throw j.code="MODULE_NOT_FOUND",j}var k=c[g]={exports:{}};b[g][0].call(k.exports,function(a){var c=b[g][1][a];return e(c?c:a)},k,k.exports,a,b,c,d)}return c[g].exports}for(var f="function"==typeof require&&require,g=0;g<d.length;g++)e(d[g]);return e}({1:[function(a,b,c){"use strict";function d(a){if(!(this instanceof d))return new d(a);this.options=i.assign({level:s,method:u,chunkSize:16384,windowBits:15,memLevel:8,strategy:t,to:""},a||{});var b=this.options;b.raw&&b.windowBits>0?b.windowBits=-b.windowBits:b.gzip&&b.windowBits>0&&b.windowBits<16&&(b.windowBits+=16),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new l,this.strm.avail_out=0;var c=h.deflateInit2(this.strm,b.level,b.method,b.windowBits,b.memLevel,b.strategy);if(c!==p)throw new Error(k[c]);if(b.header&&h.deflateSetHeader(this.strm,b.header),b.dictionary){var e;if(e="string"==typeof b.dictionary?j.string2buf(b.dictionary):"[object ArrayBuffer]"===m.call(b.dictionary)?new Uint8Array(b.dictionary):b.dictionary,c=h.deflateSetDictionary(this.strm,e),c!==p)throw new Error(k[c]);this._dict_set=!0}}function e(a,b){var c=new d(b);if(c.push(a,!0),c.err)throw c.msg;return c.result}function f(a,b){return b=b||{},b.raw=!0,e(a,b)}function g(a,b){return b=b||{},b.gzip=!0,e(a,b)}var h=a("./zlib/deflate"),i=a("./utils/common"),j=a("./utils/strings"),k=a("./zlib/messages"),l=a("./zlib/zstream"),m=Object.prototype.toString,n=0,o=4,p=0,q=1,r=2,s=-1,t=0,u=8;d.prototype.push=function(a,b){var c,d,e=this.strm,f=this.options.chunkSize;if(this.ended)return!1;d=b===~~b?b:b===!0?o:n,"string"==typeof a?e.input=j.string2buf(a):"[object ArrayBuffer]"===m.call(a)?e.input=new Uint8Array(a):e.input=a,e.next_in=0,e.avail_in=e.input.length;do{if(0===e.avail_out&&(e.output=new i.Buf8(f),e.next_out=0,e.avail_out=f),c=h.deflate(e,d),c!==q&&c!==p)return this.onEnd(c),this.ended=!0,!1;(0===e.avail_out||0===e.avail_in&&(d===o||d===r))&&("string"===this.options.to?this.onData(j.buf2binstring(i.shrinkBuf(e.output,e.next_out))):this.onData(i.shrinkBuf(e.output,e.next_out)))}while((e.avail_in>0||0===e.avail_out)&&c!==q);return d===o?(c=h.deflateEnd(this.strm),this.onEnd(c),this.ended=!0,c===p):d===r?(this.onEnd(p),e.avail_out=0,!0):!0},d.prototype.onData=function(a){this.chunks.push(a)},d.prototype.onEnd=function(a){a===p&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=i.flattenChunks(this.chunks)),this.chunks=[],this.err=a,this.msg=this.strm.msg},c.Deflate=d,c.deflate=e,c.deflateRaw=f,c.gzip=g},{"./utils/common":3,"./utils/strings":4,"./zlib/deflate":8,"./zlib/messages":13,"./zlib/zstream":15}],2:[function(a,b,c){"use strict";function d(a){if(!(this instanceof d))return new d(a);this.options=h.assign({chunkSize:16384,windowBits:0,to:""},a||{});var b=this.options;b.raw&&b.windowBits>=0&&b.windowBits<16&&(b.windowBits=-b.windowBits,0===b.windowBits&&(b.windowBits=-15)),!(b.windowBits>=0&&b.windowBits<16)||a&&a.windowBits||(b.windowBits+=32),b.windowBits>15&&b.windowBits<48&&0===(15&b.windowBits)&&(b.windowBits|=15),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new l,this.strm.avail_out=0;var c=g.inflateInit2(this.strm,b.windowBits);if(c!==j.Z_OK)throw new Error(k[c]);this.header=new m,g.inflateGetHeader(this.strm,this.header)}function e(a,b){var c=new d(b);if(c.push(a,!0),c.err)throw c.msg;return c.result}function f(a,b){return b=b||{},b.raw=!0,e(a,b)}var g=a("./zlib/inflate"),h=a("./utils/common"),i=a("./utils/strings"),j=a("./zlib/constants"),k=a("./zlib/messages"),l=a("./zlib/zstream"),m=a("./zlib/gzheader"),n=Object.prototype.toString;d.prototype.push=function(a,b){var c,d,e,f,k,l,m=this.strm,o=this.options.chunkSize,p=this.options.dictionary,q=!1;if(this.ended)return!1;d=b===~~b?b:b===!0?j.Z_FINISH:j.Z_NO_FLUSH,"string"==typeof a?m.input=i.binstring2buf(a):"[object ArrayBuffer]"===n.call(a)?m.input=new Uint8Array(a):m.input=a,m.next_in=0,m.avail_in=m.input.length;do{if(0===m.avail_out&&(m.output=new h.Buf8(o),m.next_out=0,m.avail_out=o),c=g.inflate(m,j.Z_NO_FLUSH),c===j.Z_NEED_DICT&&p&&(l="string"==typeof p?i.string2buf(p):"[object ArrayBuffer]"===n.call(p)?new Uint8Array(p):p,c=g.inflateSetDictionary(this.strm,l)),c===j.Z_BUF_ERROR&&q===!0&&(c=j.Z_OK,q=!1),c!==j.Z_STREAM_END&&c!==j.Z_OK)return this.onEnd(c),this.ended=!0,!1;m.next_out&&(0===m.avail_out||c===j.Z_STREAM_END||0===m.avail_in&&(d===j.Z_FINISH||d===j.Z_SYNC_FLUSH))&&("string"===this.options.to?(e=i.utf8border(m.output,m.next_out),f=m.next_out-e,k=i.buf2string(m.output,e),m.next_out=f,m.avail_out=o-f,f&&h.arraySet(m.output,m.output,e,f,0),this.onData(k)):this.onData(h.shrinkBuf(m.output,m.next_out))),0===m.avail_in&&0===m.avail_out&&(q=!0)}while((m.avail_in>0||0===m.avail_out)&&c!==j.Z_STREAM_END);return c===j.Z_STREAM_END&&(d=j.Z_FINISH),d===j.Z_FINISH?(c=g.inflateEnd(this.strm),this.onEnd(c),this.ended=!0,c===j.Z_OK):d===j.Z_SYNC_FLUSH?(this.onEnd(j.Z_OK),m.avail_out=0,!0):!0},d.prototype.onData=function(a){this.chunks.push(a)},d.prototype.onEnd=function(a){a===j.Z_OK&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=h.flattenChunks(this.chunks)),this.chunks=[],this.err=a,this.msg=this.strm.msg},c.Inflate=d,c.inflate=e,c.inflateRaw=f,c.ungzip=e},{"./utils/common":3,"./utils/strings":4,"./zlib/constants":6,"./zlib/gzheader":9,"./zlib/inflate":11,"./zlib/messages":13,"./zlib/zstream":15}],3:[function(a,b,c){"use strict";var d="undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint16Array&&"undefined"!=typeof Int32Array;c.assign=function(a){for(var b=Array.prototype.slice.call(arguments,1);b.length;){var c=b.shift();if(c){if("object"!=typeof c)throw new TypeError(c+"must be non-object");for(var d in c)c.hasOwnProperty(d)&&(a[d]=c[d])}}return a},c.shrinkBuf=function(a,b){return a.length===b?a:a.subarray?a.subarray(0,b):(a.length=b,a)};var e={arraySet:function(a,b,c,d,e){if(b.subarray&&a.subarray)return void a.set(b.subarray(c,c+d),e);for(var f=0;d>f;f++)a[e+f]=b[c+f]},flattenChunks:function(a){var b,c,d,e,f,g;for(d=0,b=0,c=a.length;c>b;b++)d+=a[b].length;for(g=new Uint8Array(d),e=0,b=0,c=a.length;c>b;b++)f=a[b],g.set(f,e),e+=f.length;return g}},f={arraySet:function(a,b,c,d,e){for(var f=0;d>f;f++)a[e+f]=b[c+f]},flattenChunks:function(a){return[].concat.apply([],a)}};c.setTyped=function(a){a?(c.Buf8=Uint8Array,c.Buf16=Uint16Array,c.Buf32=Int32Array,c.assign(c,e)):(c.Buf8=Array,c.Buf16=Array,c.Buf32=Array,c.assign(c,f))},c.setTyped(d)},{}],4:[function(a,b,c){"use strict";function d(a,b){if(65537>b&&(a.subarray&&g||!a.subarray&&f))return String.fromCharCode.apply(null,e.shrinkBuf(a,b));for(var c="",d=0;b>d;d++)c+=String.fromCharCode(a[d]);return c}var e=a("./common"),f=!0,g=!0;try{String.fromCharCode.apply(null,[0])}catch(h){f=!1}try{String.fromCharCode.apply(null,new Uint8Array(1))}catch(h){g=!1}for(var i=new e.Buf8(256),j=0;256>j;j++)i[j]=j>=252?6:j>=248?5:j>=240?4:j>=224?3:j>=192?2:1;i[254]=i[254]=1,c.string2buf=function(a){var b,c,d,f,g,h=a.length,i=0;for(f=0;h>f;f++)c=a.charCodeAt(f),55296===(64512&c)&&h>f+1&&(d=a.charCodeAt(f+1),56320===(64512&d)&&(c=65536+(c-55296<<10)+(d-56320),f++)),i+=128>c?1:2048>c?2:65536>c?3:4;for(b=new e.Buf8(i),g=0,f=0;i>g;f++)c=a.charCodeAt(f),55296===(64512&c)&&h>f+1&&(d=a.charCodeAt(f+1),56320===(64512&d)&&(c=65536+(c-55296<<10)+(d-56320),f++)),128>c?b[g++]=c:2048>c?(b[g++]=192|c>>>6,b[g++]=128|63&c):65536>c?(b[g++]=224|c>>>12,b[g++]=128|c>>>6&63,b[g++]=128|63&c):(b[g++]=240|c>>>18,b[g++]=128|c>>>12&63,b[g++]=128|c>>>6&63,b[g++]=128|63&c);return b},c.buf2binstring=function(a){return d(a,a.length)},c.binstring2buf=function(a){for(var b=new e.Buf8(a.length),c=0,d=b.length;d>c;c++)b[c]=a.charCodeAt(c);return b},c.buf2string=function(a,b){var c,e,f,g,h=b||a.length,j=new Array(2*h);for(e=0,c=0;h>c;)if(f=a[c++],128>f)j[e++]=f;else if(g=i[f],g>4)j[e++]=65533,c+=g-1;else{for(f&=2===g?31:3===g?15:7;g>1&&h>c;)f=f<<6|63&a[c++],g--;g>1?j[e++]=65533:65536>f?j[e++]=f:(f-=65536,j[e++]=55296|f>>10&1023,j[e++]=56320|1023&f)}return d(j,e)},c.utf8border=function(a,b){var c;for(b=b||a.length,b>a.length&&(b=a.length),c=b-1;c>=0&&128===(192&a[c]);)c--;return 0>c?b:0===c?b:c+i[a[c]]>b?c:b}},{"./common":3}],5:[function(a,b,c){"use strict";function d(a,b,c,d){for(var e=65535&a|0,f=a>>>16&65535|0,g=0;0!==c;){g=c>2e3?2e3:c,c-=g;do e=e+b[d++]|0,f=f+e|0;while(--g);e%=65521,f%=65521}return e|f<<16|0}b.exports=d},{}],6:[function(a,b,c){"use strict";b.exports={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8}},{}],7:[function(a,b,c){"use strict";function d(){for(var a,b=[],c=0;256>c;c++){a=c;for(var d=0;8>d;d++)a=1&a?3988292384^a>>>1:a>>>1;b[c]=a}return b}function e(a,b,c,d){var e=f,g=d+c;a^=-1;for(var h=d;g>h;h++)a=a>>>8^e[255&(a^b[h])];return-1^a}var f=d();b.exports=e},{}],8:[function(a,b,c){"use strict";function d(a,b){return a.msg=I[b],b}function e(a){return(a<<1)-(a>4?9:0)}function f(a){for(var b=a.length;--b>=0;)a[b]=0}function g(a){var b=a.state,c=b.pending;c>a.avail_out&&(c=a.avail_out),0!==c&&(E.arraySet(a.output,b.pending_buf,b.pending_out,c,a.next_out),a.next_out+=c,b.pending_out+=c,a.total_out+=c,a.avail_out-=c,b.pending-=c,0===b.pending&&(b.pending_out=0))}function h(a,b){F._tr_flush_block(a,a.block_start>=0?a.block_start:-1,a.strstart-a.block_start,b),a.block_start=a.strstart,g(a.strm)}function i(a,b){a.pending_buf[a.pending++]=b}function j(a,b){a.pending_buf[a.pending++]=b>>>8&255,a.pending_buf[a.pending++]=255&b}function k(a,b,c,d){var e=a.avail_in;return e>d&&(e=d),0===e?0:(a.avail_in-=e,E.arraySet(b,a.input,a.next_in,e,c),1===a.state.wrap?a.adler=G(a.adler,b,e,c):2===a.state.wrap&&(a.adler=H(a.adler,b,e,c)),a.next_in+=e,a.total_in+=e,e)}function l(a,b){var c,d,e=a.max_chain_length,f=a.strstart,g=a.prev_length,h=a.nice_match,i=a.strstart>a.w_size-la?a.strstart-(a.w_size-la):0,j=a.window,k=a.w_mask,l=a.prev,m=a.strstart+ka,n=j[f+g-1],o=j[f+g];a.prev_length>=a.good_match&&(e>>=2),h>a.lookahead&&(h=a.lookahead);do if(c=b,j[c+g]===o&&j[c+g-1]===n&&j[c]===j[f]&&j[++c]===j[f+1]){f+=2,c++;do;while(j[++f]===j[++c]&&j[++f]===j[++c]&&j[++f]===j[++c]&&j[++f]===j[++c]&&j[++f]===j[++c]&&j[++f]===j[++c]&&j[++f]===j[++c]&&j[++f]===j[++c]&&m>f);if(d=ka-(m-f),f=m-ka,d>g){if(a.match_start=b,g=d,d>=h)break;n=j[f+g-1],o=j[f+g]}}while((b=l[b&k])>i&&0!==--e);return g<=a.lookahead?g:a.lookahead}function m(a){var b,c,d,e,f,g=a.w_size;do{if(e=a.window_size-a.lookahead-a.strstart,a.strstart>=g+(g-la)){E.arraySet(a.window,a.window,g,g,0),a.match_start-=g,a.strstart-=g,a.block_start-=g,c=a.hash_size,b=c;do d=a.head[--b],a.head[b]=d>=g?d-g:0;while(--c);c=g,b=c;do d=a.prev[--b],a.prev[b]=d>=g?d-g:0;while(--c);e+=g}if(0===a.strm.avail_in)break;if(c=k(a.strm,a.window,a.strstart+a.lookahead,e),a.lookahead+=c,a.lookahead+a.insert>=ja)for(f=a.strstart-a.insert,a.ins_h=a.window[f],a.ins_h=(a.ins_h<<a.hash_shift^a.window[f+1])&a.hash_mask;a.insert&&(a.ins_h=(a.ins_h<<a.hash_shift^a.window[f+ja-1])&a.hash_mask,a.prev[f&a.w_mask]=a.head[a.ins_h],a.head[a.ins_h]=f,f++,a.insert--,!(a.lookahead+a.insert<ja)););}while(a.lookahead<la&&0!==a.strm.avail_in)}function n(a,b){var c=65535;for(c>a.pending_buf_size-5&&(c=a.pending_buf_size-5);;){if(a.lookahead<=1){if(m(a),0===a.lookahead&&b===J)return ua;if(0===a.lookahead)break}a.strstart+=a.lookahead,a.lookahead=0;var d=a.block_start+c;if((0===a.strstart||a.strstart>=d)&&(a.lookahead=a.strstart-d,a.strstart=d,h(a,!1),0===a.strm.avail_out))return ua;if(a.strstart-a.block_start>=a.w_size-la&&(h(a,!1),0===a.strm.avail_out))return ua}return a.insert=0,b===M?(h(a,!0),0===a.strm.avail_out?wa:xa):a.strstart>a.block_start&&(h(a,!1),0===a.strm.avail_out)?ua:ua}function o(a,b){for(var c,d;;){if(a.lookahead<la){if(m(a),a.lookahead<la&&b===J)return ua;if(0===a.lookahead)break}if(c=0,a.lookahead>=ja&&(a.ins_h=(a.ins_h<<a.hash_shift^a.window[a.strstart+ja-1])&a.hash_mask,c=a.prev[a.strstart&a.w_mask]=a.head[a.ins_h],a.head[a.ins_h]=a.strstart),0!==c&&a.strstart-c<=a.w_size-la&&(a.match_length=l(a,c)),a.match_length>=ja)if(d=F._tr_tally(a,a.strstart-a.match_start,a.match_length-ja),a.lookahead-=a.match_length,a.match_length<=a.max_lazy_match&&a.lookahead>=ja){a.match_length--;do a.strstart++,a.ins_h=(a.ins_h<<a.hash_shift^a.window[a.strstart+ja-1])&a.hash_mask,c=a.prev[a.strstart&a.w_mask]=a.head[a.ins_h],a.head[a.ins_h]=a.strstart;while(0!==--a.match_length);a.strstart++}else a.strstart+=a.match_length,a.match_length=0,a.ins_h=a.window[a.strstart],a.ins_h=(a.ins_h<<a.hash_shift^a.window[a.strstart+1])&a.hash_mask;else d=F._tr_tally(a,0,a.window[a.strstart]),a.lookahead--,a.strstart++;if(d&&(h(a,!1),0===a.strm.avail_out))return ua}return a.insert=a.strstart<ja-1?a.strstart:ja-1,b===M?(h(a,!0),0===a.strm.avail_out?wa:xa):a.last_lit&&(h(a,!1),0===a.strm.avail_out)?ua:va}function p(a,b){for(var c,d,e;;){if(a.lookahead<la){if(m(a),a.lookahead<la&&b===J)return ua;if(0===a.lookahead)break}if(c=0,a.lookahead>=ja&&(a.ins_h=(a.ins_h<<a.hash_shift^a.window[a.strstart+ja-1])&a.hash_mask,c=a.prev[a.strstart&a.w_mask]=a.head[a.ins_h],a.head[a.ins_h]=a.strstart),a.prev_length=a.match_length,a.prev_match=a.match_start,a.match_length=ja-1,0!==c&&a.prev_length<a.max_lazy_match&&a.strstart-c<=a.w_size-la&&(a.match_length=l(a,c),a.match_length<=5&&(a.strategy===U||a.match_length===ja&&a.strstart-a.match_start>4096)&&(a.match_length=ja-1)),a.prev_length>=ja&&a.match_length<=a.prev_length){e=a.strstart+a.lookahead-ja,d=F._tr_tally(a,a.strstart-1-a.prev_match,a.prev_length-ja),a.lookahead-=a.prev_length-1,a.prev_length-=2;do++a.strstart<=e&&(a.ins_h=(a.ins_h<<a.hash_shift^a.window[a.strstart+ja-1])&a.hash_mask,c=a.prev[a.strstart&a.w_mask]=a.head[a.ins_h],a.head[a.ins_h]=a.strstart);while(0!==--a.prev_length);if(a.match_available=0,a.match_length=ja-1,a.strstart++,d&&(h(a,!1),0===a.strm.avail_out))return ua}else if(a.match_available){if(d=F._tr_tally(a,0,a.window[a.strstart-1]),d&&h(a,!1),a.strstart++,a.lookahead--,0===a.strm.avail_out)return ua}else a.match_available=1,a.strstart++,a.lookahead--}return a.match_available&&(d=F._tr_tally(a,0,a.window[a.strstart-1]),a.match_available=0),a.insert=a.strstart<ja-1?a.strstart:ja-1,b===M?(h(a,!0),0===a.strm.avail_out?wa:xa):a.last_lit&&(h(a,!1),0===a.strm.avail_out)?ua:va}function q(a,b){for(var c,d,e,f,g=a.window;;){if(a.lookahead<=ka){if(m(a),a.lookahead<=ka&&b===J)return ua;if(0===a.lookahead)break}if(a.match_length=0,a.lookahead>=ja&&a.strstart>0&&(e=a.strstart-1,d=g[e],d===g[++e]&&d===g[++e]&&d===g[++e])){f=a.strstart+ka;do;while(d===g[++e]&&d===g[++e]&&d===g[++e]&&d===g[++e]&&d===g[++e]&&d===g[++e]&&d===g[++e]&&d===g[++e]&&f>e);a.match_length=ka-(f-e),a.match_length>a.lookahead&&(a.match_length=a.lookahead)}if(a.match_length>=ja?(c=F._tr_tally(a,1,a.match_length-ja),a.lookahead-=a.match_length,a.strstart+=a.match_length,a.match_length=0):(c=F._tr_tally(a,0,a.window[a.strstart]),a.lookahead--,a.strstart++),c&&(h(a,!1),0===a.strm.avail_out))return ua}return a.insert=0,b===M?(h(a,!0),0===a.strm.avail_out?wa:xa):a.last_lit&&(h(a,!1),0===a.strm.avail_out)?ua:va}function r(a,b){for(var c;;){if(0===a.lookahead&&(m(a),0===a.lookahead)){if(b===J)return ua;break}if(a.match_length=0,c=F._tr_tally(a,0,a.window[a.strstart]),a.lookahead--,a.strstart++,c&&(h(a,!1),0===a.strm.avail_out))return ua}return a.insert=0,b===M?(h(a,!0),0===a.strm.avail_out?wa:xa):a.last_lit&&(h(a,!1),0===a.strm.avail_out)?ua:va}function s(a,b,c,d,e){this.good_length=a,this.max_lazy=b,this.nice_length=c,this.max_chain=d,this.func=e}function t(a){a.window_size=2*a.w_size,f(a.head),a.max_lazy_match=D[a.level].max_lazy,a.good_match=D[a.level].good_length,a.nice_match=D[a.level].nice_length,a.max_chain_length=D[a.level].max_chain,a.strstart=0,a.block_start=0,a.lookahead=0,a.insert=0,a.match_length=a.prev_length=ja-1,a.match_available=0,a.ins_h=0}function u(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=$,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new E.Buf16(2*ha),this.dyn_dtree=new E.Buf16(2*(2*fa+1)),this.bl_tree=new E.Buf16(2*(2*ga+1)),f(this.dyn_ltree),f(this.dyn_dtree),f(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new E.Buf16(ia+1),this.heap=new E.Buf16(2*ea+1),f(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new E.Buf16(2*ea+1),f(this.depth),this.l_buf=0,this.lit_bufsize=0,this.last_lit=0,this.d_buf=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}function v(a){var b;return a&&a.state?(a.total_in=a.total_out=0,a.data_type=Z,b=a.state,b.pending=0,b.pending_out=0,b.wrap<0&&(b.wrap=-b.wrap),b.status=b.wrap?na:sa,a.adler=2===b.wrap?0:1,b.last_flush=J,F._tr_init(b),O):d(a,Q)}function w(a){var b=v(a);return b===O&&t(a.state),b}function x(a,b){return a&&a.state?2!==a.state.wrap?Q:(a.state.gzhead=b,O):Q}function y(a,b,c,e,f,g){if(!a)return Q;var h=1;if(b===T&&(b=6),0>e?(h=0,e=-e):e>15&&(h=2,e-=16),1>f||f>_||c!==$||8>e||e>15||0>b||b>9||0>g||g>X)return d(a,Q);8===e&&(e=9);var i=new u;return a.state=i,i.strm=a,i.wrap=h,i.gzhead=null,i.w_bits=e,i.w_size=1<<i.w_bits,i.w_mask=i.w_size-1,i.hash_bits=f+7,i.hash_size=1<<i.hash_bits,i.hash_mask=i.hash_size-1,i.hash_shift=~~((i.hash_bits+ja-1)/ja),i.window=new E.Buf8(2*i.w_size),i.head=new E.Buf16(i.hash_size),i.prev=new E.Buf16(i.w_size),i.lit_bufsize=1<<f+6,i.pending_buf_size=4*i.lit_bufsize,i.pending_buf=new E.Buf8(i.pending_buf_size),i.d_buf=i.lit_bufsize>>1,i.l_buf=3*i.lit_bufsize,i.level=b,i.strategy=g,i.method=c,w(a)}function z(a,b){return y(a,b,$,aa,ba,Y)}function A(a,b){var c,h,k,l;if(!a||!a.state||b>N||0>b)return a?d(a,Q):Q;if(h=a.state,!a.output||!a.input&&0!==a.avail_in||h.status===ta&&b!==M)return d(a,0===a.avail_out?S:Q);if(h.strm=a,c=h.last_flush,h.last_flush=b,h.status===na)if(2===h.wrap)a.adler=0,i(h,31),i(h,139),i(h,8),h.gzhead?(i(h,(h.gzhead.text?1:0)+(h.gzhead.hcrc?2:0)+(h.gzhead.extra?4:0)+(h.gzhead.name?8:0)+(h.gzhead.comment?16:0)),i(h,255&h.gzhead.time),i(h,h.gzhead.time>>8&255),i(h,h.gzhead.time>>16&255),i(h,h.gzhead.time>>24&255),i(h,9===h.level?2:h.strategy>=V||h.level<2?4:0),i(h,255&h.gzhead.os),h.gzhead.extra&&h.gzhead.extra.length&&(i(h,255&h.gzhead.extra.length),i(h,h.gzhead.extra.length>>8&255)),h.gzhead.hcrc&&(a.adler=H(a.adler,h.pending_buf,h.pending,0)),h.gzindex=0,h.status=oa):(i(h,0),i(h,0),i(h,0),i(h,0),i(h,0),i(h,9===h.level?2:h.strategy>=V||h.level<2?4:0),i(h,ya),h.status=sa);else{var m=$+(h.w_bits-8<<4)<<8,n=-1;n=h.strategy>=V||h.level<2?0:h.level<6?1:6===h.level?2:3,m|=n<<6,0!==h.strstart&&(m|=ma),m+=31-m%31,h.status=sa,j(h,m),0!==h.strstart&&(j(h,a.adler>>>16),j(h,65535&a.adler)),a.adler=1}if(h.status===oa)if(h.gzhead.extra){for(k=h.pending;h.gzindex<(65535&h.gzhead.extra.length)&&(h.pending!==h.pending_buf_size||(h.gzhead.hcrc&&h.pending>k&&(a.adler=H(a.adler,h.pending_buf,h.pending-k,k)),g(a),k=h.pending,h.pending!==h.pending_buf_size));)i(h,255&h.gzhead.extra[h.gzindex]),h.gzindex++;h.gzhead.hcrc&&h.pending>k&&(a.adler=H(a.adler,h.pending_buf,h.pending-k,k)),h.gzindex===h.gzhead.extra.length&&(h.gzindex=0,h.status=pa)}else h.status=pa;if(h.status===pa)if(h.gzhead.name){k=h.pending;do{if(h.pending===h.pending_buf_size&&(h.gzhead.hcrc&&h.pending>k&&(a.adler=H(a.adler,h.pending_buf,h.pending-k,k)),g(a),k=h.pending,h.pending===h.pending_buf_size)){l=1;break}l=h.gzindex<h.gzhead.name.length?255&h.gzhead.name.charCodeAt(h.gzindex++):0,i(h,l)}while(0!==l);h.gzhead.hcrc&&h.pending>k&&(a.adler=H(a.adler,h.pending_buf,h.pending-k,k)),0===l&&(h.gzindex=0,h.status=qa)}else h.status=qa;if(h.status===qa)if(h.gzhead.comment){k=h.pending;do{if(h.pending===h.pending_buf_size&&(h.gzhead.hcrc&&h.pending>k&&(a.adler=H(a.adler,h.pending_buf,h.pending-k,k)),g(a),k=h.pending,h.pending===h.pending_buf_size)){l=1;break}l=h.gzindex<h.gzhead.comment.length?255&h.gzhead.comment.charCodeAt(h.gzindex++):0,i(h,l)}while(0!==l);h.gzhead.hcrc&&h.pending>k&&(a.adler=H(a.adler,h.pending_buf,h.pending-k,k)),0===l&&(h.status=ra)}else h.status=ra;if(h.status===ra&&(h.gzhead.hcrc?(h.pending+2>h.pending_buf_size&&g(a),h.pending+2<=h.pending_buf_size&&(i(h,255&a.adler),i(h,a.adler>>8&255),a.adler=0,h.status=sa)):h.status=sa),0!==h.pending){if(g(a),0===a.avail_out)return h.last_flush=-1,O}else if(0===a.avail_in&&e(b)<=e(c)&&b!==M)return d(a,S);if(h.status===ta&&0!==a.avail_in)return d(a,S);if(0!==a.avail_in||0!==h.lookahead||b!==J&&h.status!==ta){var o=h.strategy===V?r(h,b):h.strategy===W?q(h,b):D[h.level].func(h,b);if((o===wa||o===xa)&&(h.status=ta),o===ua||o===wa)return 0===a.avail_out&&(h.last_flush=-1),O;if(o===va&&(b===K?F._tr_align(h):b!==N&&(F._tr_stored_block(h,0,0,!1),b===L&&(f(h.head),0===h.lookahead&&(h.strstart=0,h.block_start=0,h.insert=0))),g(a),0===a.avail_out))return h.last_flush=-1,O}return b!==M?O:h.wrap<=0?P:(2===h.wrap?(i(h,255&a.adler),i(h,a.adler>>8&255),i(h,a.adler>>16&255),i(h,a.adler>>24&255),i(h,255&a.total_in),i(h,a.total_in>>8&255),i(h,a.total_in>>16&255),i(h,a.total_in>>24&255)):(j(h,a.adler>>>16),j(h,65535&a.adler)),g(a),h.wrap>0&&(h.wrap=-h.wrap),0!==h.pending?O:P)}function B(a){var b;return a&&a.state?(b=a.state.status,b!==na&&b!==oa&&b!==pa&&b!==qa&&b!==ra&&b!==sa&&b!==ta?d(a,Q):(a.state=null,b===sa?d(a,R):O)):Q}function C(a,b){var c,d,e,g,h,i,j,k,l=b.length;if(!a||!a.state)return Q;if(c=a.state,g=c.wrap,2===g||1===g&&c.status!==na||c.lookahead)return Q;for(1===g&&(a.adler=G(a.adler,b,l,0)),c.wrap=0,l>=c.w_size&&(0===g&&(f(c.head),c.strstart=0,c.block_start=0,c.insert=0),k=new E.Buf8(c.w_size),E.arraySet(k,b,l-c.w_size,c.w_size,0),b=k,l=c.w_size),h=a.avail_in,i=a.next_in,j=a.input,a.avail_in=l,a.next_in=0,a.input=b,m(c);c.lookahead>=ja;){d=c.strstart,e=c.lookahead-(ja-1);do c.ins_h=(c.ins_h<<c.hash_shift^c.window[d+ja-1])&c.hash_mask,c.prev[d&c.w_mask]=c.head[c.ins_h],c.head[c.ins_h]=d,d++;while(--e);c.strstart=d,c.lookahead=ja-1,m(c)}return c.strstart+=c.lookahead,c.block_start=c.strstart,c.insert=c.lookahead,c.lookahead=0,c.match_length=c.prev_length=ja-1,c.match_available=0,a.next_in=i,a.input=j,a.avail_in=h,c.wrap=g,O}var D,E=a("../utils/common"),F=a("./trees"),G=a("./adler32"),H=a("./crc32"),I=a("./messages"),J=0,K=1,L=3,M=4,N=5,O=0,P=1,Q=-2,R=-3,S=-5,T=-1,U=1,V=2,W=3,X=4,Y=0,Z=2,$=8,_=9,aa=15,ba=8,ca=29,da=256,ea=da+1+ca,fa=30,ga=19,ha=2*ea+1,ia=15,ja=3,ka=258,la=ka+ja+1,ma=32,na=42,oa=69,pa=73,qa=91,ra=103,sa=113,ta=666,ua=1,va=2,wa=3,xa=4,ya=3;
D=[new s(0,0,0,0,n),new s(4,4,8,4,o),new s(4,5,16,8,o),new s(4,6,32,32,o),new s(4,4,16,16,p),new s(8,16,32,32,p),new s(8,16,128,128,p),new s(8,32,128,256,p),new s(32,128,258,1024,p),new s(32,258,258,4096,p)],c.deflateInit=z,c.deflateInit2=y,c.deflateReset=w,c.deflateResetKeep=v,c.deflateSetHeader=x,c.deflate=A,c.deflateEnd=B,c.deflateSetDictionary=C,c.deflateInfo="pako deflate (from Nodeca project)"},{"../utils/common":3,"./adler32":5,"./crc32":7,"./messages":13,"./trees":14}],9:[function(a,b,c){"use strict";function d(){this.text=0,this.time=0,this.xflags=0,this.os=0,this.extra=null,this.extra_len=0,this.name="",this.comment="",this.hcrc=0,this.done=!1}b.exports=d},{}],10:[function(a,b,c){"use strict";var d=30,e=12;b.exports=function(a,b){var c,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C;c=a.state,f=a.next_in,B=a.input,g=f+(a.avail_in-5),h=a.next_out,C=a.output,i=h-(b-a.avail_out),j=h+(a.avail_out-257),k=c.dmax,l=c.wsize,m=c.whave,n=c.wnext,o=c.window,p=c.hold,q=c.bits,r=c.lencode,s=c.distcode,t=(1<<c.lenbits)-1,u=(1<<c.distbits)-1;a:do{15>q&&(p+=B[f++]<<q,q+=8,p+=B[f++]<<q,q+=8),v=r[p&t];b:for(;;){if(w=v>>>24,p>>>=w,q-=w,w=v>>>16&255,0===w)C[h++]=65535&v;else{if(!(16&w)){if(0===(64&w)){v=r[(65535&v)+(p&(1<<w)-1)];continue b}if(32&w){c.mode=e;break a}a.msg="invalid literal/length code",c.mode=d;break a}x=65535&v,w&=15,w&&(w>q&&(p+=B[f++]<<q,q+=8),x+=p&(1<<w)-1,p>>>=w,q-=w),15>q&&(p+=B[f++]<<q,q+=8,p+=B[f++]<<q,q+=8),v=s[p&u];c:for(;;){if(w=v>>>24,p>>>=w,q-=w,w=v>>>16&255,!(16&w)){if(0===(64&w)){v=s[(65535&v)+(p&(1<<w)-1)];continue c}a.msg="invalid distance code",c.mode=d;break a}if(y=65535&v,w&=15,w>q&&(p+=B[f++]<<q,q+=8,w>q&&(p+=B[f++]<<q,q+=8)),y+=p&(1<<w)-1,y>k){a.msg="invalid distance too far back",c.mode=d;break a}if(p>>>=w,q-=w,w=h-i,y>w){if(w=y-w,w>m&&c.sane){a.msg="invalid distance too far back",c.mode=d;break a}if(z=0,A=o,0===n){if(z+=l-w,x>w){x-=w;do C[h++]=o[z++];while(--w);z=h-y,A=C}}else if(w>n){if(z+=l+n-w,w-=n,x>w){x-=w;do C[h++]=o[z++];while(--w);if(z=0,x>n){w=n,x-=w;do C[h++]=o[z++];while(--w);z=h-y,A=C}}}else if(z+=n-w,x>w){x-=w;do C[h++]=o[z++];while(--w);z=h-y,A=C}for(;x>2;)C[h++]=A[z++],C[h++]=A[z++],C[h++]=A[z++],x-=3;x&&(C[h++]=A[z++],x>1&&(C[h++]=A[z++]))}else{z=h-y;do C[h++]=C[z++],C[h++]=C[z++],C[h++]=C[z++],x-=3;while(x>2);x&&(C[h++]=C[z++],x>1&&(C[h++]=C[z++]))}break}}break}}while(g>f&&j>h);x=q>>3,f-=x,q-=x<<3,p&=(1<<q)-1,a.next_in=f,a.next_out=h,a.avail_in=g>f?5+(g-f):5-(f-g),a.avail_out=j>h?257+(j-h):257-(h-j),c.hold=p,c.bits=q}},{}],11:[function(a,b,c){"use strict";function d(a){return(a>>>24&255)+(a>>>8&65280)+((65280&a)<<8)+((255&a)<<24)}function e(){this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new s.Buf16(320),this.work=new s.Buf16(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0}function f(a){var b;return a&&a.state?(b=a.state,a.total_in=a.total_out=b.total=0,a.msg="",b.wrap&&(a.adler=1&b.wrap),b.mode=L,b.last=0,b.havedict=0,b.dmax=32768,b.head=null,b.hold=0,b.bits=0,b.lencode=b.lendyn=new s.Buf32(pa),b.distcode=b.distdyn=new s.Buf32(qa),b.sane=1,b.back=-1,D):G}function g(a){var b;return a&&a.state?(b=a.state,b.wsize=0,b.whave=0,b.wnext=0,f(a)):G}function h(a,b){var c,d;return a&&a.state?(d=a.state,0>b?(c=0,b=-b):(c=(b>>4)+1,48>b&&(b&=15)),b&&(8>b||b>15)?G:(null!==d.window&&d.wbits!==b&&(d.window=null),d.wrap=c,d.wbits=b,g(a))):G}function i(a,b){var c,d;return a?(d=new e,a.state=d,d.window=null,c=h(a,b),c!==D&&(a.state=null),c):G}function j(a){return i(a,sa)}function k(a){if(ta){var b;for(q=new s.Buf32(512),r=new s.Buf32(32),b=0;144>b;)a.lens[b++]=8;for(;256>b;)a.lens[b++]=9;for(;280>b;)a.lens[b++]=7;for(;288>b;)a.lens[b++]=8;for(w(y,a.lens,0,288,q,0,a.work,{bits:9}),b=0;32>b;)a.lens[b++]=5;w(z,a.lens,0,32,r,0,a.work,{bits:5}),ta=!1}a.lencode=q,a.lenbits=9,a.distcode=r,a.distbits=5}function l(a,b,c,d){var e,f=a.state;return null===f.window&&(f.wsize=1<<f.wbits,f.wnext=0,f.whave=0,f.window=new s.Buf8(f.wsize)),d>=f.wsize?(s.arraySet(f.window,b,c-f.wsize,f.wsize,0),f.wnext=0,f.whave=f.wsize):(e=f.wsize-f.wnext,e>d&&(e=d),s.arraySet(f.window,b,c-d,e,f.wnext),d-=e,d?(s.arraySet(f.window,b,c-d,d,0),f.wnext=d,f.whave=f.wsize):(f.wnext+=e,f.wnext===f.wsize&&(f.wnext=0),f.whave<f.wsize&&(f.whave+=e))),0}function m(a,b){var c,e,f,g,h,i,j,m,n,o,p,q,r,pa,qa,ra,sa,ta,ua,va,wa,xa,ya,za,Aa=0,Ba=new s.Buf8(4),Ca=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];if(!a||!a.state||!a.output||!a.input&&0!==a.avail_in)return G;c=a.state,c.mode===W&&(c.mode=X),h=a.next_out,f=a.output,j=a.avail_out,g=a.next_in,e=a.input,i=a.avail_in,m=c.hold,n=c.bits,o=i,p=j,xa=D;a:for(;;)switch(c.mode){case L:if(0===c.wrap){c.mode=X;break}for(;16>n;){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}if(2&c.wrap&&35615===m){c.check=0,Ba[0]=255&m,Ba[1]=m>>>8&255,c.check=u(c.check,Ba,2,0),m=0,n=0,c.mode=M;break}if(c.flags=0,c.head&&(c.head.done=!1),!(1&c.wrap)||(((255&m)<<8)+(m>>8))%31){a.msg="incorrect header check",c.mode=ma;break}if((15&m)!==K){a.msg="unknown compression method",c.mode=ma;break}if(m>>>=4,n-=4,wa=(15&m)+8,0===c.wbits)c.wbits=wa;else if(wa>c.wbits){a.msg="invalid window size",c.mode=ma;break}c.dmax=1<<wa,a.adler=c.check=1,c.mode=512&m?U:W,m=0,n=0;break;case M:for(;16>n;){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}if(c.flags=m,(255&c.flags)!==K){a.msg="unknown compression method",c.mode=ma;break}if(57344&c.flags){a.msg="unknown header flags set",c.mode=ma;break}c.head&&(c.head.text=m>>8&1),512&c.flags&&(Ba[0]=255&m,Ba[1]=m>>>8&255,c.check=u(c.check,Ba,2,0)),m=0,n=0,c.mode=N;case N:for(;32>n;){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}c.head&&(c.head.time=m),512&c.flags&&(Ba[0]=255&m,Ba[1]=m>>>8&255,Ba[2]=m>>>16&255,Ba[3]=m>>>24&255,c.check=u(c.check,Ba,4,0)),m=0,n=0,c.mode=O;case O:for(;16>n;){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}c.head&&(c.head.xflags=255&m,c.head.os=m>>8),512&c.flags&&(Ba[0]=255&m,Ba[1]=m>>>8&255,c.check=u(c.check,Ba,2,0)),m=0,n=0,c.mode=P;case P:if(1024&c.flags){for(;16>n;){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}c.length=m,c.head&&(c.head.extra_len=m),512&c.flags&&(Ba[0]=255&m,Ba[1]=m>>>8&255,c.check=u(c.check,Ba,2,0)),m=0,n=0}else c.head&&(c.head.extra=null);c.mode=Q;case Q:if(1024&c.flags&&(q=c.length,q>i&&(q=i),q&&(c.head&&(wa=c.head.extra_len-c.length,c.head.extra||(c.head.extra=new Array(c.head.extra_len)),s.arraySet(c.head.extra,e,g,q,wa)),512&c.flags&&(c.check=u(c.check,e,q,g)),i-=q,g+=q,c.length-=q),c.length))break a;c.length=0,c.mode=R;case R:if(2048&c.flags){if(0===i)break a;q=0;do wa=e[g+q++],c.head&&wa&&c.length<65536&&(c.head.name+=String.fromCharCode(wa));while(wa&&i>q);if(512&c.flags&&(c.check=u(c.check,e,q,g)),i-=q,g+=q,wa)break a}else c.head&&(c.head.name=null);c.length=0,c.mode=S;case S:if(4096&c.flags){if(0===i)break a;q=0;do wa=e[g+q++],c.head&&wa&&c.length<65536&&(c.head.comment+=String.fromCharCode(wa));while(wa&&i>q);if(512&c.flags&&(c.check=u(c.check,e,q,g)),i-=q,g+=q,wa)break a}else c.head&&(c.head.comment=null);c.mode=T;case T:if(512&c.flags){for(;16>n;){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}if(m!==(65535&c.check)){a.msg="header crc mismatch",c.mode=ma;break}m=0,n=0}c.head&&(c.head.hcrc=c.flags>>9&1,c.head.done=!0),a.adler=c.check=0,c.mode=W;break;case U:for(;32>n;){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}a.adler=c.check=d(m),m=0,n=0,c.mode=V;case V:if(0===c.havedict)return a.next_out=h,a.avail_out=j,a.next_in=g,a.avail_in=i,c.hold=m,c.bits=n,F;a.adler=c.check=1,c.mode=W;case W:if(b===B||b===C)break a;case X:if(c.last){m>>>=7&n,n-=7&n,c.mode=ja;break}for(;3>n;){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}switch(c.last=1&m,m>>>=1,n-=1,3&m){case 0:c.mode=Y;break;case 1:if(k(c),c.mode=ca,b===C){m>>>=2,n-=2;break a}break;case 2:c.mode=_;break;case 3:a.msg="invalid block type",c.mode=ma}m>>>=2,n-=2;break;case Y:for(m>>>=7&n,n-=7&n;32>n;){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}if((65535&m)!==(m>>>16^65535)){a.msg="invalid stored block lengths",c.mode=ma;break}if(c.length=65535&m,m=0,n=0,c.mode=Z,b===C)break a;case Z:c.mode=$;case $:if(q=c.length){if(q>i&&(q=i),q>j&&(q=j),0===q)break a;s.arraySet(f,e,g,q,h),i-=q,g+=q,j-=q,h+=q,c.length-=q;break}c.mode=W;break;case _:for(;14>n;){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}if(c.nlen=(31&m)+257,m>>>=5,n-=5,c.ndist=(31&m)+1,m>>>=5,n-=5,c.ncode=(15&m)+4,m>>>=4,n-=4,c.nlen>286||c.ndist>30){a.msg="too many length or distance symbols",c.mode=ma;break}c.have=0,c.mode=aa;case aa:for(;c.have<c.ncode;){for(;3>n;){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}c.lens[Ca[c.have++]]=7&m,m>>>=3,n-=3}for(;c.have<19;)c.lens[Ca[c.have++]]=0;if(c.lencode=c.lendyn,c.lenbits=7,ya={bits:c.lenbits},xa=w(x,c.lens,0,19,c.lencode,0,c.work,ya),c.lenbits=ya.bits,xa){a.msg="invalid code lengths set",c.mode=ma;break}c.have=0,c.mode=ba;case ba:for(;c.have<c.nlen+c.ndist;){for(;Aa=c.lencode[m&(1<<c.lenbits)-1],qa=Aa>>>24,ra=Aa>>>16&255,sa=65535&Aa,!(n>=qa);){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}if(16>sa)m>>>=qa,n-=qa,c.lens[c.have++]=sa;else{if(16===sa){for(za=qa+2;za>n;){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}if(m>>>=qa,n-=qa,0===c.have){a.msg="invalid bit length repeat",c.mode=ma;break}wa=c.lens[c.have-1],q=3+(3&m),m>>>=2,n-=2}else if(17===sa){for(za=qa+3;za>n;){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}m>>>=qa,n-=qa,wa=0,q=3+(7&m),m>>>=3,n-=3}else{for(za=qa+7;za>n;){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}m>>>=qa,n-=qa,wa=0,q=11+(127&m),m>>>=7,n-=7}if(c.have+q>c.nlen+c.ndist){a.msg="invalid bit length repeat",c.mode=ma;break}for(;q--;)c.lens[c.have++]=wa}}if(c.mode===ma)break;if(0===c.lens[256]){a.msg="invalid code -- missing end-of-block",c.mode=ma;break}if(c.lenbits=9,ya={bits:c.lenbits},xa=w(y,c.lens,0,c.nlen,c.lencode,0,c.work,ya),c.lenbits=ya.bits,xa){a.msg="invalid literal/lengths set",c.mode=ma;break}if(c.distbits=6,c.distcode=c.distdyn,ya={bits:c.distbits},xa=w(z,c.lens,c.nlen,c.ndist,c.distcode,0,c.work,ya),c.distbits=ya.bits,xa){a.msg="invalid distances set",c.mode=ma;break}if(c.mode=ca,b===C)break a;case ca:c.mode=da;case da:if(i>=6&&j>=258){a.next_out=h,a.avail_out=j,a.next_in=g,a.avail_in=i,c.hold=m,c.bits=n,v(a,p),h=a.next_out,f=a.output,j=a.avail_out,g=a.next_in,e=a.input,i=a.avail_in,m=c.hold,n=c.bits,c.mode===W&&(c.back=-1);break}for(c.back=0;Aa=c.lencode[m&(1<<c.lenbits)-1],qa=Aa>>>24,ra=Aa>>>16&255,sa=65535&Aa,!(n>=qa);){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}if(ra&&0===(240&ra)){for(ta=qa,ua=ra,va=sa;Aa=c.lencode[va+((m&(1<<ta+ua)-1)>>ta)],qa=Aa>>>24,ra=Aa>>>16&255,sa=65535&Aa,!(n>=ta+qa);){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}m>>>=ta,n-=ta,c.back+=ta}if(m>>>=qa,n-=qa,c.back+=qa,c.length=sa,0===ra){c.mode=ia;break}if(32&ra){c.back=-1,c.mode=W;break}if(64&ra){a.msg="invalid literal/length code",c.mode=ma;break}c.extra=15&ra,c.mode=ea;case ea:if(c.extra){for(za=c.extra;za>n;){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}c.length+=m&(1<<c.extra)-1,m>>>=c.extra,n-=c.extra,c.back+=c.extra}c.was=c.length,c.mode=fa;case fa:for(;Aa=c.distcode[m&(1<<c.distbits)-1],qa=Aa>>>24,ra=Aa>>>16&255,sa=65535&Aa,!(n>=qa);){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}if(0===(240&ra)){for(ta=qa,ua=ra,va=sa;Aa=c.distcode[va+((m&(1<<ta+ua)-1)>>ta)],qa=Aa>>>24,ra=Aa>>>16&255,sa=65535&Aa,!(n>=ta+qa);){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}m>>>=ta,n-=ta,c.back+=ta}if(m>>>=qa,n-=qa,c.back+=qa,64&ra){a.msg="invalid distance code",c.mode=ma;break}c.offset=sa,c.extra=15&ra,c.mode=ga;case ga:if(c.extra){for(za=c.extra;za>n;){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}c.offset+=m&(1<<c.extra)-1,m>>>=c.extra,n-=c.extra,c.back+=c.extra}if(c.offset>c.dmax){a.msg="invalid distance too far back",c.mode=ma;break}c.mode=ha;case ha:if(0===j)break a;if(q=p-j,c.offset>q){if(q=c.offset-q,q>c.whave&&c.sane){a.msg="invalid distance too far back",c.mode=ma;break}q>c.wnext?(q-=c.wnext,r=c.wsize-q):r=c.wnext-q,q>c.length&&(q=c.length),pa=c.window}else pa=f,r=h-c.offset,q=c.length;q>j&&(q=j),j-=q,c.length-=q;do f[h++]=pa[r++];while(--q);0===c.length&&(c.mode=da);break;case ia:if(0===j)break a;f[h++]=c.length,j--,c.mode=da;break;case ja:if(c.wrap){for(;32>n;){if(0===i)break a;i--,m|=e[g++]<<n,n+=8}if(p-=j,a.total_out+=p,c.total+=p,p&&(a.adler=c.check=c.flags?u(c.check,f,p,h-p):t(c.check,f,p,h-p)),p=j,(c.flags?m:d(m))!==c.check){a.msg="incorrect data check",c.mode=ma;break}m=0,n=0}c.mode=ka;case ka:if(c.wrap&&c.flags){for(;32>n;){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}if(m!==(4294967295&c.total)){a.msg="incorrect length check",c.mode=ma;break}m=0,n=0}c.mode=la;case la:xa=E;break a;case ma:xa=H;break a;case na:return I;case oa:default:return G}return a.next_out=h,a.avail_out=j,a.next_in=g,a.avail_in=i,c.hold=m,c.bits=n,(c.wsize||p!==a.avail_out&&c.mode<ma&&(c.mode<ja||b!==A))&&l(a,a.output,a.next_out,p-a.avail_out)?(c.mode=na,I):(o-=a.avail_in,p-=a.avail_out,a.total_in+=o,a.total_out+=p,c.total+=p,c.wrap&&p&&(a.adler=c.check=c.flags?u(c.check,f,p,a.next_out-p):t(c.check,f,p,a.next_out-p)),a.data_type=c.bits+(c.last?64:0)+(c.mode===W?128:0)+(c.mode===ca||c.mode===Z?256:0),(0===o&&0===p||b===A)&&xa===D&&(xa=J),xa)}function n(a){if(!a||!a.state)return G;var b=a.state;return b.window&&(b.window=null),a.state=null,D}function o(a,b){var c;return a&&a.state?(c=a.state,0===(2&c.wrap)?G:(c.head=b,b.done=!1,D)):G}function p(a,b){var c,d,e,f=b.length;return a&&a.state?(c=a.state,0!==c.wrap&&c.mode!==V?G:c.mode===V&&(d=1,d=t(d,b,f,0),d!==c.check)?H:(e=l(a,b,f,f))?(c.mode=na,I):(c.havedict=1,D)):G}var q,r,s=a("../utils/common"),t=a("./adler32"),u=a("./crc32"),v=a("./inffast"),w=a("./inftrees"),x=0,y=1,z=2,A=4,B=5,C=6,D=0,E=1,F=2,G=-2,H=-3,I=-4,J=-5,K=8,L=1,M=2,N=3,O=4,P=5,Q=6,R=7,S=8,T=9,U=10,V=11,W=12,X=13,Y=14,Z=15,$=16,_=17,aa=18,ba=19,ca=20,da=21,ea=22,fa=23,ga=24,ha=25,ia=26,ja=27,ka=28,la=29,ma=30,na=31,oa=32,pa=852,qa=592,ra=15,sa=ra,ta=!0;c.inflateReset=g,c.inflateReset2=h,c.inflateResetKeep=f,c.inflateInit=j,c.inflateInit2=i,c.inflate=m,c.inflateEnd=n,c.inflateGetHeader=o,c.inflateSetDictionary=p,c.inflateInfo="pako inflate (from Nodeca project)"},{"../utils/common":3,"./adler32":5,"./crc32":7,"./inffast":10,"./inftrees":12}],12:[function(a,b,c){"use strict";var d=a("../utils/common"),e=15,f=852,g=592,h=0,i=1,j=2,k=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0],l=[16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78],m=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0],n=[16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64];b.exports=function(a,b,c,o,p,q,r,s){var t,u,v,w,x,y,z,A,B,C=s.bits,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=null,O=0,P=new d.Buf16(e+1),Q=new d.Buf16(e+1),R=null,S=0;for(D=0;e>=D;D++)P[D]=0;for(E=0;o>E;E++)P[b[c+E]]++;for(H=C,G=e;G>=1&&0===P[G];G--);if(H>G&&(H=G),0===G)return p[q++]=20971520,p[q++]=20971520,s.bits=1,0;for(F=1;G>F&&0===P[F];F++);for(F>H&&(H=F),K=1,D=1;e>=D;D++)if(K<<=1,K-=P[D],0>K)return-1;if(K>0&&(a===h||1!==G))return-1;for(Q[1]=0,D=1;e>D;D++)Q[D+1]=Q[D]+P[D];for(E=0;o>E;E++)0!==b[c+E]&&(r[Q[b[c+E]]++]=E);if(a===h?(N=R=r,y=19):a===i?(N=k,O-=257,R=l,S-=257,y=256):(N=m,R=n,y=-1),M=0,E=0,D=F,x=q,I=H,J=0,v=-1,L=1<<H,w=L-1,a===i&&L>f||a===j&&L>g)return 1;for(var T=0;;){T++,z=D-J,r[E]<y?(A=0,B=r[E]):r[E]>y?(A=R[S+r[E]],B=N[O+r[E]]):(A=96,B=0),t=1<<D-J,u=1<<I,F=u;do u-=t,p[x+(M>>J)+u]=z<<24|A<<16|B|0;while(0!==u);for(t=1<<D-1;M&t;)t>>=1;if(0!==t?(M&=t-1,M+=t):M=0,E++,0===--P[D]){if(D===G)break;D=b[c+r[E]]}if(D>H&&(M&w)!==v){for(0===J&&(J=H),x+=F,I=D-J,K=1<<I;G>I+J&&(K-=P[I+J],!(0>=K));)I++,K<<=1;if(L+=1<<I,a===i&&L>f||a===j&&L>g)return 1;v=M&w,p[v]=H<<24|I<<16|x-q|0}}return 0!==M&&(p[x+M]=D-J<<24|64<<16|0),s.bits=H,0}},{"../utils/common":3}],13:[function(a,b,c){"use strict";b.exports={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"}},{}],14:[function(a,b,c){"use strict";function d(a){for(var b=a.length;--b>=0;)a[b]=0}function e(a,b,c,d,e){this.static_tree=a,this.extra_bits=b,this.extra_base=c,this.elems=d,this.max_length=e,this.has_stree=a&&a.length}function f(a,b){this.dyn_tree=a,this.max_code=0,this.stat_desc=b}function g(a){return 256>a?ia[a]:ia[256+(a>>>7)]}function h(a,b){a.pending_buf[a.pending++]=255&b,a.pending_buf[a.pending++]=b>>>8&255}function i(a,b,c){a.bi_valid>X-c?(a.bi_buf|=b<<a.bi_valid&65535,h(a,a.bi_buf),a.bi_buf=b>>X-a.bi_valid,a.bi_valid+=c-X):(a.bi_buf|=b<<a.bi_valid&65535,a.bi_valid+=c)}function j(a,b,c){i(a,c[2*b],c[2*b+1])}function k(a,b){var c=0;do c|=1&a,a>>>=1,c<<=1;while(--b>0);return c>>>1}function l(a){16===a.bi_valid?(h(a,a.bi_buf),a.bi_buf=0,a.bi_valid=0):a.bi_valid>=8&&(a.pending_buf[a.pending++]=255&a.bi_buf,a.bi_buf>>=8,a.bi_valid-=8)}function m(a,b){var c,d,e,f,g,h,i=b.dyn_tree,j=b.max_code,k=b.stat_desc.static_tree,l=b.stat_desc.has_stree,m=b.stat_desc.extra_bits,n=b.stat_desc.extra_base,o=b.stat_desc.max_length,p=0;for(f=0;W>=f;f++)a.bl_count[f]=0;for(i[2*a.heap[a.heap_max]+1]=0,c=a.heap_max+1;V>c;c++)d=a.heap[c],f=i[2*i[2*d+1]+1]+1,f>o&&(f=o,p++),i[2*d+1]=f,d>j||(a.bl_count[f]++,g=0,d>=n&&(g=m[d-n]),h=i[2*d],a.opt_len+=h*(f+g),l&&(a.static_len+=h*(k[2*d+1]+g)));if(0!==p){do{for(f=o-1;0===a.bl_count[f];)f--;a.bl_count[f]--,a.bl_count[f+1]+=2,a.bl_count[o]--,p-=2}while(p>0);for(f=o;0!==f;f--)for(d=a.bl_count[f];0!==d;)e=a.heap[--c],e>j||(i[2*e+1]!==f&&(a.opt_len+=(f-i[2*e+1])*i[2*e],i[2*e+1]=f),d--)}}function n(a,b,c){var d,e,f=new Array(W+1),g=0;for(d=1;W>=d;d++)f[d]=g=g+c[d-1]<<1;for(e=0;b>=e;e++){var h=a[2*e+1];0!==h&&(a[2*e]=k(f[h]++,h))}}function o(){var a,b,c,d,f,g=new Array(W+1);for(c=0,d=0;Q-1>d;d++)for(ka[d]=c,a=0;a<1<<ba[d];a++)ja[c++]=d;for(ja[c-1]=d,f=0,d=0;16>d;d++)for(la[d]=f,a=0;a<1<<ca[d];a++)ia[f++]=d;for(f>>=7;T>d;d++)for(la[d]=f<<7,a=0;a<1<<ca[d]-7;a++)ia[256+f++]=d;for(b=0;W>=b;b++)g[b]=0;for(a=0;143>=a;)ga[2*a+1]=8,a++,g[8]++;for(;255>=a;)ga[2*a+1]=9,a++,g[9]++;for(;279>=a;)ga[2*a+1]=7,a++,g[7]++;for(;287>=a;)ga[2*a+1]=8,a++,g[8]++;for(n(ga,S+1,g),a=0;T>a;a++)ha[2*a+1]=5,ha[2*a]=k(a,5);ma=new e(ga,ba,R+1,S,W),na=new e(ha,ca,0,T,W),oa=new e(new Array(0),da,0,U,Y)}function p(a){var b;for(b=0;S>b;b++)a.dyn_ltree[2*b]=0;for(b=0;T>b;b++)a.dyn_dtree[2*b]=0;for(b=0;U>b;b++)a.bl_tree[2*b]=0;a.dyn_ltree[2*Z]=1,a.opt_len=a.static_len=0,a.last_lit=a.matches=0}function q(a){a.bi_valid>8?h(a,a.bi_buf):a.bi_valid>0&&(a.pending_buf[a.pending++]=a.bi_buf),a.bi_buf=0,a.bi_valid=0}function r(a,b,c,d){q(a),d&&(h(a,c),h(a,~c)),G.arraySet(a.pending_buf,a.window,b,c,a.pending),a.pending+=c}function s(a,b,c,d){var e=2*b,f=2*c;return a[e]<a[f]||a[e]===a[f]&&d[b]<=d[c]}function t(a,b,c){for(var d=a.heap[c],e=c<<1;e<=a.heap_len&&(e<a.heap_len&&s(b,a.heap[e+1],a.heap[e],a.depth)&&e++,!s(b,d,a.heap[e],a.depth));)a.heap[c]=a.heap[e],c=e,e<<=1;a.heap[c]=d}function u(a,b,c){var d,e,f,h,k=0;if(0!==a.last_lit)do d=a.pending_buf[a.d_buf+2*k]<<8|a.pending_buf[a.d_buf+2*k+1],e=a.pending_buf[a.l_buf+k],k++,0===d?j(a,e,b):(f=ja[e],j(a,f+R+1,b),h=ba[f],0!==h&&(e-=ka[f],i(a,e,h)),d--,f=g(d),j(a,f,c),h=ca[f],0!==h&&(d-=la[f],i(a,d,h)));while(k<a.last_lit);j(a,Z,b)}function v(a,b){var c,d,e,f=b.dyn_tree,g=b.stat_desc.static_tree,h=b.stat_desc.has_stree,i=b.stat_desc.elems,j=-1;for(a.heap_len=0,a.heap_max=V,c=0;i>c;c++)0!==f[2*c]?(a.heap[++a.heap_len]=j=c,a.depth[c]=0):f[2*c+1]=0;for(;a.heap_len<2;)e=a.heap[++a.heap_len]=2>j?++j:0,f[2*e]=1,a.depth[e]=0,a.opt_len--,h&&(a.static_len-=g[2*e+1]);for(b.max_code=j,c=a.heap_len>>1;c>=1;c--)t(a,f,c);e=i;do c=a.heap[1],a.heap[1]=a.heap[a.heap_len--],t(a,f,1),d=a.heap[1],a.heap[--a.heap_max]=c,a.heap[--a.heap_max]=d,f[2*e]=f[2*c]+f[2*d],a.depth[e]=(a.depth[c]>=a.depth[d]?a.depth[c]:a.depth[d])+1,f[2*c+1]=f[2*d+1]=e,a.heap[1]=e++,t(a,f,1);while(a.heap_len>=2);a.heap[--a.heap_max]=a.heap[1],m(a,b),n(f,j,a.bl_count)}function w(a,b,c){var d,e,f=-1,g=b[1],h=0,i=7,j=4;for(0===g&&(i=138,j=3),b[2*(c+1)+1]=65535,d=0;c>=d;d++)e=g,g=b[2*(d+1)+1],++h<i&&e===g||(j>h?a.bl_tree[2*e]+=h:0!==e?(e!==f&&a.bl_tree[2*e]++,a.bl_tree[2*$]++):10>=h?a.bl_tree[2*_]++:a.bl_tree[2*aa]++,h=0,f=e,0===g?(i=138,j=3):e===g?(i=6,j=3):(i=7,j=4))}function x(a,b,c){var d,e,f=-1,g=b[1],h=0,k=7,l=4;for(0===g&&(k=138,l=3),d=0;c>=d;d++)if(e=g,g=b[2*(d+1)+1],!(++h<k&&e===g)){if(l>h){do j(a,e,a.bl_tree);while(0!==--h)}else 0!==e?(e!==f&&(j(a,e,a.bl_tree),h--),j(a,$,a.bl_tree),i(a,h-3,2)):10>=h?(j(a,_,a.bl_tree),i(a,h-3,3)):(j(a,aa,a.bl_tree),i(a,h-11,7));h=0,f=e,0===g?(k=138,l=3):e===g?(k=6,l=3):(k=7,l=4)}}function y(a){var b;for(w(a,a.dyn_ltree,a.l_desc.max_code),w(a,a.dyn_dtree,a.d_desc.max_code),v(a,a.bl_desc),b=U-1;b>=3&&0===a.bl_tree[2*ea[b]+1];b--);return a.opt_len+=3*(b+1)+5+5+4,b}function z(a,b,c,d){var e;for(i(a,b-257,5),i(a,c-1,5),i(a,d-4,4),e=0;d>e;e++)i(a,a.bl_tree[2*ea[e]+1],3);x(a,a.dyn_ltree,b-1),x(a,a.dyn_dtree,c-1)}function A(a){var b,c=4093624447;for(b=0;31>=b;b++,c>>>=1)if(1&c&&0!==a.dyn_ltree[2*b])return I;if(0!==a.dyn_ltree[18]||0!==a.dyn_ltree[20]||0!==a.dyn_ltree[26])return J;for(b=32;R>b;b++)if(0!==a.dyn_ltree[2*b])return J;return I}function B(a){pa||(o(),pa=!0),a.l_desc=new f(a.dyn_ltree,ma),a.d_desc=new f(a.dyn_dtree,na),a.bl_desc=new f(a.bl_tree,oa),a.bi_buf=0,a.bi_valid=0,p(a)}function C(a,b,c,d){i(a,(L<<1)+(d?1:0),3),r(a,b,c,!0)}function D(a){i(a,M<<1,3),j(a,Z,ga),l(a)}function E(a,b,c,d){var e,f,g=0;a.level>0?(a.strm.data_type===K&&(a.strm.data_type=A(a)),v(a,a.l_desc),v(a,a.d_desc),g=y(a),e=a.opt_len+3+7>>>3,f=a.static_len+3+7>>>3,e>=f&&(e=f)):e=f=c+5,e>=c+4&&-1!==b?C(a,b,c,d):a.strategy===H||f===e?(i(a,(M<<1)+(d?1:0),3),u(a,ga,ha)):(i(a,(N<<1)+(d?1:0),3),z(a,a.l_desc.max_code+1,a.d_desc.max_code+1,g+1),u(a,a.dyn_ltree,a.dyn_dtree)),p(a),d&&q(a)}function F(a,b,c){return a.pending_buf[a.d_buf+2*a.last_lit]=b>>>8&255,a.pending_buf[a.d_buf+2*a.last_lit+1]=255&b,a.pending_buf[a.l_buf+a.last_lit]=255&c,a.last_lit++,0===b?a.dyn_ltree[2*c]++:(a.matches++,b--,a.dyn_ltree[2*(ja[c]+R+1)]++,a.dyn_dtree[2*g(b)]++),a.last_lit===a.lit_bufsize-1}var G=a("../utils/common"),H=4,I=0,J=1,K=2,L=0,M=1,N=2,O=3,P=258,Q=29,R=256,S=R+1+Q,T=30,U=19,V=2*S+1,W=15,X=16,Y=7,Z=256,$=16,_=17,aa=18,ba=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0],ca=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],da=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7],ea=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],fa=512,ga=new Array(2*(S+2));d(ga);var ha=new Array(2*T);d(ha);var ia=new Array(fa);d(ia);var ja=new Array(P-O+1);d(ja);var ka=new Array(Q);d(ka);var la=new Array(T);d(la);var ma,na,oa,pa=!1;c._tr_init=B,c._tr_stored_block=C,c._tr_flush_block=E,c._tr_tally=F,c._tr_align=D},{"../utils/common":3}],15:[function(a,b,c){"use strict";function d(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0}b.exports=d},{}],"/":[function(a,b,c){"use strict";var d=a("./lib/utils/common").assign,e=a("./lib/deflate"),f=a("./lib/inflate"),g=a("./lib/zlib/constants"),h={};d(h,e,f,g),b.exports=h},{"./lib/deflate":1,"./lib/inflate":2,"./lib/utils/common":3,"./lib/zlib/constants":6}]},{},[])("/")}),function(){"use strict";BrainBrowser.utils={webglExtensionAvailable:function(a){if(!BrainBrowser.WEBGL_ENABLED)return!1;var b=document.createElement("canvas"),c=b.getContext("webgl")||b.getContext("experimental-webgl");return!!c.getExtension(a)},webGLErrorMessage:function(){var a,b='BrainBrowser requires <a href="http://khronos.org/webgl/wiki/Getting_a_WebGL_Implementation">WebGL</a>.<br/>';return b+=window.WebGLRenderingContext?"Your browser seems to support it, but it is <br/> disabled or unavailable.<br/>":"Your browser does not seem to support it.<br/>",b+='Test your browser\'s WebGL support <a href="http://get.webgl.org/">here</a>.',a=document.createElement("div"),a.id="webgl-error",a.innerHTML=b,a},isFunction:function(a){return a instanceof Function||"function"==typeof a},isNumeric:function(a){return!isNaN(parseFloat(a))},createDataURL:function(a,b){if(!window.URL||!window.URL.createObjectURL)throw new Error("createDataURL requires URL.createObjectURL which does not seem to be available is this browser.");return window.URL.createObjectURL(new Blob([a],{type:b||"text/plain"}))},getWorkerImportURL:function(){var a=BrainBrowser.config.get("worker_dir"),b=document.location.origin+"/"+a,c=document.location.href,d=c.lastIndexOf("/");return d>=0&&(b=c.substring(0,d+1)+a),b},min:function(){var a=Array.prototype.slice.call(arguments);a=1===a.length&&BrainBrowser.utils.isNumeric(a[0].length)?a[0]:a;var b,c,d=a[0];for(b=1,c=a.length;c>b;b++)a[b]<d&&(d=a[b]);return d},max:function(){var a=Array.prototype.slice.call(arguments);a=1===a.length&&BrainBrowser.utils.isNumeric(a[0].length)?a[0]:a;var b,c,d=a[0];for(b=1,c=a.length;c>b;b++)a[b]>d&&(d=a[b]);return d},getOffset:function(a){for(var b=0,c=0;a.offsetParent;)b+=a.offsetTop,c+=a.offsetLeft,a=a.offsetParent;return{top:b,left:c}},captureMouse:function(a){var b={x:0,y:0,left:!1,middle:!1,right:!1};return document.addEventListener("mousemove",function(c){var d,e,f=BrainBrowser.utils.getOffset(a);void 0!==c.pageX?(d=c.pageX,e=c.pageY):(d=c.clientX+window.pageXOffset,e=c.clientY+window.pageYOffset),b.x=d-f.left,b.y=e-f.top},!1),a.addEventListener("mousedown",function(a){a.preventDefault(),0===a.button&&(b.left=!0),1===a.button&&(b.middle=!0),2===a.button&&(b.right=!0)},!1),a.addEventListener("mouseup",function(a){a.preventDefault(),0===a.button&&(b.left=!1),1===a.button&&(b.middle=!1),2===a.button&&(b.right=!1)},!1),a.addEventListener("mouseleave",function(a){a.preventDefault(),b.left=b.middle=b.right=!1},!1),a.addEventListener("contextmenu",function(a){a.preventDefault()},!1),b},captureTouch:function(a){function b(b){var d,e,f,g,h,i=BrainBrowser.utils.getOffset(a);for(c.length=g=b.touches.length,f=0;g>f;f++)h=b.touches[f],void 0!==h.pageX?(d=h.pageX,e=h.pageY):(d=h.clientX+window.pageXOffset,e=h.clientY+window.pageYOffset),c[f]=c[f]||{},c[f].x=d-i.left,c[f].y=e-i.top}var c=[];return a.addEventListener("touchstart",b,!1),a.addEventListener("touchmove",b,!1),a.addEventListener("touchend",b,!1),c}}}(),function(){"use strict";var a=BrainBrowser.VolumeViewer={};a.modules={},a.volume_loaders={},a.start=function(b,c){function d(){document.addEventListener("keydown",function(a){if(f.active_panel){var b,c,d=f.active_panel,e=d.volume,g=d.axis,h=a.which,i={17:function(){d.anchor||(d.mouse.left||d.mouse.middle||d.mouse.right)&&(d.anchor={x:d.mouse.x,y:d.mouse.y})},37:function(){b=d.slice.width_space.name,e.position[b]>0&&e.position[b]--},38:function(){b=d.slice.height_space.name,e.position[b]<d.slice.height_space.space_length&&e.position[b]++},39:function(){b=d.slice.width_space.name,e.position[b]<d.slice.width_space.space_length&&e.position[b]++},40:function(){b=d.slice.height_space.name,e.position[b]>0&&e.position[b]--}};return"function"==typeof i[h]?(a.preventDefault(),i[h](),d.updated=!0,e.display.forEach(function(a){d!==a&&a.updateSlice()}),f.synced&&f.syncPosition(d,e,g),!1):32===h&&(a.preventDefault(),e.header.time)?(c=a.shiftKey?Math.max(0,e.current_time-1):Math.min(e.current_time+1,e.header.time.space_length-1),e.current_time=c,f.synced&&f.volumes.forEach(function(a){a!==e&&(a.current_time=Math.max(0,Math.min(c,a.header.time.space_length-1)))}),f.redrawVolumes(),!1):void 0}},!1),document.addEventListener("keyup",function(a){var b=a.which,c={17:function(){f.volumes.forEach(function(a){a.display.forEach(function(a){a.anchor=null})})}};return"function"==typeof c[b]?(a.preventDefault(),c[b](),!1):void 0},!1)}var e;e="string"==typeof b?document.getElementById(b):b;var f={dom_element:e,volumes:[],containers:[],synced:!1};return Object.keys(a.modules).forEach(function(b){a.modules[b](f)}),BrainBrowser.events.addEventModel(f),console.log("BrainBrowser Volume Viewer v"+BrainBrowser.version),d(),c(f),f}}(),function(){"use strict";BrainBrowser.VolumeViewer.createDisplay=function(){var a={},b={setPanel:function(c,d){a[c]&&a[c].triggerEvent("eventmodelcleanup"),d.propagateEventTo("*",b),a[c]=d},getPanel:function(b){return a[b]},refreshPanels:function(){b.forEach(function(a){a.updateSlice()})},setContrast:function(a){b.forEach(function(b){b.contrast=a})},setBrightness:function(a){b.forEach(function(b){b.brightness=a})},forEach:function(b){Object.keys(a).forEach(function(c,d){b(a[c],c,d)})}};return BrainBrowser.events.addEventModel(b),b.addEventListener("eventmodelcleanup",function(){b.forEach(function(a){a.triggerEvent("eventmodelcleanup")})}),b}}(),function(){"use strict";function a(a,b){a.slice=b,a.slice_image=a.volume.getSliceImage(a.slice,a.zoom,a.contrast,a.brightness)}function b(a,b){var c,d,e,f,g,h,i=a.context,j=a.getCursorPosition(),k=a.zoom,l=8*(k/a.default_zoom);b=b||"#FF0000",i.save(),i.strokeStyle=b,i.fillStyle=b,e=1,c=j.x,d=j.y,i.lineWidth=2*e,i.beginPath(),i.moveTo(c,d-l),i.lineTo(c,d-e),i.moveTo(c,d+e),i.lineTo(c,d+l),i.moveTo(c-l,d),i.lineTo(c-e,d),i.moveTo(c+e,d),i.lineTo(c+l,d),i.stroke(),a.anchor&&(g=(a.anchor.x-j.x)/a.zoom,h=(a.anchor.y-j.y)/a.zoom,f=Math.sqrt(g*g+h*h),i.font="bold 12px arial",a.canvas.width-j.x<50?(i.textAlign="right",c=j.x-l):(i.textAlign="left",c=j.x+l),j.y<30?(i.textBaseline="top",d=j.y+l):(i.textBaseline="bottom",d=j.y-l),i.fillText(f.toFixed(2),c,d),i.lineWidth=1,i.beginPath(),i.arc(a.anchor.x,a.anchor.y,2*e,0,2*Math.PI),i.fill(),i.moveTo(a.anchor.x,a.anchor.y),i.lineTo(j.x,j.y),i.stroke()),i.restore()}function c(a){var b,c=a.slice_image;c&&(b={x:a.image_center.x-a.slice_image.width/2,y:a.image_center.y-a.slice_image.height/2},a.context.putImageData(c,b.x,b.y))}function d(a){var b=a.slice;return{x:a.image_center.x-Math.abs(b.width_space.step*b.width_space.space_length*a.zoom)/2,y:a.image_center.y-Math.abs(b.height_space.step*b.height_space.space_length*a.zoom)/2}}BrainBrowser.VolumeViewer.createPanel=function(e){e=e||{};var f=0,g={x:0,y:0},h={x:0,y:0},i=null,j=[],k={image_center:{x:0,y:0},zoom:1,contrast:1,brightness:0,updated:!0,setSize:function(a,b,c){c=c||{},a=a>0?a:0,b=b>0?b:0;var d,e,f,g=c.scale_image;g&&(d=k.canvas.width,e=k.canvas.width,f=Math.min(a/d,b/e)),k.canvas.width=a,k.canvas.height=b,g&&(k.zoom=k.zoom*f,k.default_zoom=k.default_zoom*f,k.image_center.x=a/2,k.image_center.y=b/2,k.updateVolumePosition(),k.updateSlice()),k.updated=!0},followPointer:function(a){var b=a.x-h.x,c=a.y-h.y;return k.translateImage(b,c),h.x=a.x,h.y=a.y,{dx:b,dy:c}},translateImage:function(a,b){k.image_center.x+=a,k.image_center.y+=b,k.updated=!0},reset:function(){k.zoom=k.default_zoom,k.image_center.x=k.canvas.width/2,k.image_center.y=k.canvas.height/2,k.updated=!0},getCursorPosition:function(){var a=k.volume,b=k.slice,c=d(k);return{x:a.position[b.width_space.name]*Math.abs(b.width_space.step)*k.zoom+c.x,y:(b.height_space.space_length-a.position[b.height_space.name]-1)*Math.abs(b.height_space.step)*k.zoom+c.y}},updateVolumePosition:function(a,b){var c,e,f,g=d(k),h=k.zoom,i=k.volume,j=k.slice;(void 0===a||void 0===b)&&(c=k.getCursorPosition(),a=c.x,b=c.y),e=Math.round((a-g.x)/h/Math.abs(j.width_space.step)),f=Math.round(j.height_space.space_length-(b-g.y)/h/Math.abs(j.height_space.step)-1),i.position[k.slice.width_space.name]=e,i.position[k.slice.height_space.name]=f,k.updated=!0},updateSlice:function(b){clearTimeout(i),BrainBrowser.utils.isFunction(b)&&j.push(b),i=setTimeout(function(){var b,c=k.volume;b=c.slice(k.axis),a(k,b),k.triggerEvent("sliceupdate",{volume:c,slice:b}),k.updated=!0,j.forEach(function(a){a(b)}),j.length=0},0)},draw:function(a,d){var e=k.getCursorPosition();if((g.x!==e.x||g.y!==e.y)&&(g.x=e.x,g.y=e.y,k.updated=!0,k.triggerEvent("cursorupdate",{volume:k.volume,cursor:e})),f!==k.zoom&&(f=k.zoom,k.updated=!0,k.triggerEvent("zoom",{volume:k.volume,zoom:k.zoom})),k.touches[0]?(h.x=k.touches[0].x,
h.y=k.touches[0].y):(h.x=k.mouse.x,h.y=k.mouse.y),k.updated){var i=k.canvas,j=k.context,l=4,m=l/2;j.globalAlpha=255,j.clearRect(0,0,i.width,i.height),c(k),k.triggerEvent("draw",{volume:k.volume,cursor:e,canvas:i,context:j}),b(k,a),d&&(j.save(),j.strokeStyle="#EC2121",j.lineWidth=l,j.strokeRect(m,m,i.width-l,i.height-l),j.restore()),k.updated=!1}}};if(Object.keys(e).forEach(function(a){BrainBrowser.utils.isFunction(k[a])||(k[a]=e[a])}),BrainBrowser.events.addEventModel(k),k.canvas&&BrainBrowser.utils.isFunction(k.canvas.getContext)&&(k.context=k.canvas.getContext("2d"),k.mouse=BrainBrowser.utils.captureMouse(k.canvas),k.touches=BrainBrowser.utils.captureTouch(k.canvas)),k.volume){var l=k.volume;a(k,l.slice(k.axis)),k.default_zoom=l.getPreferredZoom(k.canvas.width,k.canvas.height),k.zoom=k.default_zoom}return k}}(),function(){"use strict";BrainBrowser.VolumeViewer.utils={nearestNeighbor:function(a,b,c,d,e,f){f=f||{};var g,h,i,j,k,l,m,n,o,p,q=f.block_size||1,r=f.array_type||Uint8ClampedArray;if(b===d&&c===e)return a;for(k=new r(d*e*q),g=b/d,h=c/e,m=0;e>m;m++)for(i=Math.floor(m*h)*b,n=m*d,l=0;d>l;l++)for(j=(i+Math.floor(l*g))*q,o=(n+l)*q,p=0;q>p;p++)k[o+p]=a[j+p];return k},flipArray:function(a,b,c,d){d=d||{};var e,f,g,h,i,j,k,l,m,n=d.flipx||!1,o=d.flipy||!1,p=d.block_size||1,q=new a.constructor(a.length);if(!n&&!o){for(e=0,f=a.length;f>e;e++)q[e]=a[e];return q}for(f=0;c>f;f++)for(j=f*b,i=o?c-f-1:f,l=i*b,e=0;b>e;e++)for(k=(j+e)*p,h=n?b-e-1:e,m=(l+h)*p,g=0;p>g;g++)q[k+g]=a[m+g];return q}}}(),BrainBrowser.VolumeViewer.modules.loading=function(a){"use strict";function b(a,b){var c,d=h.volume_loaders[a.type];if(!d)throw c="Unsuported Volume Type",BrainBrowser.events.triggerEvent("error",{message:c}),new Error(c);d(a,b)}function c(c,d,e){b(d,function(b){var f=0,h=d.views||["xspace","yspace","zspace"];BrainBrowser.events.addEventModel(b),b.addEventListener("eventmodelcleanup",function(){b.display.triggerEvent("eventmodelcleanup")}),a.volumes[c]=b,b.color_map=i,b.display=g(a.dom_element,c,d),b.propagateEventTo("*",a),["xspace","yspace","zspace"].forEach(function(a){b.position[a]=Math.floor(b.header[a].space_length/2)}),b.display.forEach(function(c){c.updateSlice(function(){++f===h.length&&(a.triggerEvent("volumeloaded",{volume:b}),BrainBrowser.utils.isFunction(e)&&e(b))})})})}function d(b,c,d){b.cursor_color=c,i=b,a.volumes.forEach(function(a){a.color_map=a.color_map||i}),BrainBrowser.utils.isFunction(d)&&d(b)}function e(b,c,d,e){c.cursor_color=d,a.setVolumeColorMap(b,c),BrainBrowser.utils.isFunction(e)&&e(a.volumes[b],c)}function f(a,b,c,d){var e=document.getElementById(c).innerHTML.replace(/\{\{VOLID\}\}/gm,b),f=document.createElement("div");f.innerHTML=e;var g,h,i,j=f.childNodes,k=f.getElementsByClassName(d)[0];for(g=0,h=a.childNodes.length;h>g;g++)i=a.childNodes[g],1===i.nodeType&&(k.appendChild(i),g--,h--);return j}function g(b,c,d){var e,g=document.createElement("div"),i=a.volumes[c],l=h.createDisplay(),m=d.template||{},n=d.views||["xspace","yspace","zspace"];l.propagateEventTo("*",i),g.classList.add("volume-container"),n.forEach(function(a){var b=document.createElement("canvas");b.width=j,b.height=k,b.classList.add("slice-display"),b.style.backgroundColor="#000000",g.appendChild(b),l.setPanel(a,h.createPanel({volume:i,volume_id:c,axis:a,canvas:b,image_center:{x:b.width/2,y:b.height/2}}))}),m.element_id&&m.viewer_insert_class&&(e=f(g,c,m.element_id,m.viewer_insert_class),"function"==typeof m.complete&&m.complete(i,e),Array.prototype.forEach.call(e,function(a){1===a.nodeType&&g.appendChild(a)})),function(){var b=null;n.forEach(function(d){function e(b,c,e){e&&(a.volumes.forEach(function(a){a.display.forEach(function(a){a.anchor=null})}),r.anchor={x:b.x,y:b.y}),c||(r.updateVolumePosition(b.x,b.y),i.display.forEach(function(a){r!==a&&a.updateSlice()}),a.synced&&a.syncPosition(r,i,d)),r.updated=!0}function f(b,e){var f;e?(f=r.followPointer(b),a.synced&&a.volumes.forEach(function(a,b){var e;b!==c&&(e=a.display.getPanel(d),e.translateImage(f.dx,f.dy))})):(r.updateVolumePosition(b.x,b.y),i.display.forEach(function(a){r!==a&&a.updateSlice()}),a.synced&&a.syncPosition(r,i,d)),r.updated=!0}function g(a){a.target===b&&(a.preventDefault(),f(r.mouse,a.shiftKey))}function h(a){a.target===b&&(a.preventDefault(),f(r.touches[0],r.touches.length===n.length))}function j(){document.removeEventListener("mousemove",g,!1),document.removeEventListener("mouseup",j,!1),a.volumes.forEach(function(a){a.display.forEach(function(a){a.anchor=null})}),b=null}function k(){document.removeEventListener("touchmove",h,!1),document.removeEventListener("touchend",k,!1),a.volumes.forEach(function(a){a.display.forEach(function(a){a.anchor=null})}),b=null}function m(a){var b,c=r.touches[0].x-r.touches[1].x,d=r.touches[0].y-r.touches[1].y,e=Math.sqrt(c*c+d*d);a.preventDefault(),null!==t&&(b=e-t,q(.2*b)),t=e}function o(){document.removeEventListener("touchmove",m,!1),document.removeEventListener("touchend",o,!1),t=null}function p(a){a.preventDefault(),q(Math.max(-1,Math.min(1,a.wheelDelta||-a.detail)))}function q(b){r.zoom*=0>b?1/1.05:1.05,r.zoom=Math.max(r.zoom,.25),r.updateVolumePosition(),r.updateSlice(),a.synced&&a.volumes.forEach(function(a,b){var e=a.display.getPanel(d);b!==c&&(e.zoom=r.zoom,e.updateVolumePosition(),e.updateSlice())})}var r=l.getPanel(d),s=r.canvas,t=null;s.addEventListener("mousedown",function(c){c.preventDefault(),b=c.target,a.active_panel&&(a.active_panel.updated=!0),a.active_panel=r,document.addEventListener("mousemove",g,!1),document.addEventListener("mouseup",j,!1),e(r.mouse,c.shiftKey,c.ctrlKey)},!1),s.addEventListener("touchstart",function(c){c.preventDefault(),b=c.target,a.active_panel&&(a.active_panel.updated=!0),a.active_panel=r,2===r.touches.length?(document.removeEventListener("touchmove",h,!1),document.removeEventListener("touchend",k,!1),document.addEventListener("touchmove",m,!1),document.addEventListener("touchend",o,!1)):(document.removeEventListener("touchmove",m,!1),document.removeEventListener("touchend",o,!1),document.addEventListener("touchmove",h,!1),document.addEventListener("touchend",k,!1),e(r.touches[0],3===r.touches.length,!0))},!1),s.addEventListener("mousewheel",p,!1),s.addEventListener("DOMMouseScroll",p,!1)})}(),a.containers[c]=g;var o,p=a.containers;for(o=c+1;o<p.length;o++)if(o in p){b.insertBefore(g,p[o]);break}return o===p.length&&b.appendChild(g),a.triggerEvent("volumeuiloaded",{container:g,volume:i,volume_id:c}),l}var h=BrainBrowser.VolumeViewer,i=null,j=256,k=256;a.loadVolumes=function(b){function d(d){c(d,g[d],function(){++j<h||(b.overlay&&h>1?a.createOverlay(f,function(){BrainBrowser.utils.isFunction(i)&&i(),a.triggerEvent("volumesloaded")}):(BrainBrowser.utils.isFunction(i)&&i(),a.triggerEvent("volumesloaded")))})}b=b||{};var e,f=b.overlay&&"object"==typeof b.overlay?b.overlay:{},g=b.volumes,h=b.volumes.length,i=b.complete,j=0;for(e=0;h>e;e++)d(e)},a.loadVolumeColorMapFromURL=function(a,b,c,d){BrainBrowser.loader.loadColorMapFromURL(b,function(b){e(a,b,c,d)},{scale:255})},a.loadDefaultColorMapFromURL=function(a,b,c){BrainBrowser.loader.loadColorMapFromURL(a,function(a){d(a,b,c)},{scale:255})},a.loadVolumeColorMapFromFile=function(a,b,c,d){BrainBrowser.loader.loadColorMapFromFile(b,function(b){e(a,b,c,d)},{scale:255})},a.loadDefaultColorMapFromFile=function(a,b,c){BrainBrowser.loader.loadColorMapFromFile(a,function(a){d(a,b,c)},{scale:255})},a.setVolumeColorMap=function(b,c){a.volumes[b].color_map=c},a.loadVolume=function(b,d){c(a.volumes.length,b,d)},a.clearVolumes=function(){a.volumes.forEach(function(a){a.triggerEvent("eventmodelcleanup")}),a.volumes=[],a.containers=[],a.active_panel=null,a.dom_element.innerHTML=""},a.createOverlay=function(b,c){b=b||{},a.loadVolume({volumes:a.volumes,type:"overlay",template:b.template},c)},a.setDefaultPanelSize=function(a,b){j=a,k=b},a.syncPosition=function(b,c,d){var e=c.getWorldCoords();a.volumes.forEach(function(a){if(a!==c){var b=a.display.getPanel(d);b.volume.setWorldCoords(e.x,e.y,e.z),b.updated=!0,a.display.forEach(function(a){a!==b&&a.updateSlice()})}})}},BrainBrowser.VolumeViewer.modules.rendering=function(a){"use strict";a.draw=function(){a.volumes.forEach(function(b){b.display.forEach(function(c){c.draw(b.color_map.cursor_color,a.active_panel===c)})})},a.render=function(){a.triggerEvent("rendering"),function b(){window.requestAnimationFrame(b),a.draw()}()},a.redrawVolume=function(b){var c=a.volumes[b];c.display.forEach(function(a){a.updateSlice()})},a.redrawVolumes=function(){a.volumes.forEach(function(b,c){a.redrawVolume(c)})},a.resetDisplays=function(){a.volumes.forEach(function(a){a.display.forEach(function(a){a.reset()})})},a.setPanelSize=function(b,c,d){a.volumes.forEach(function(a){a.display.forEach(function(a){a.setSize(b,c,d)})})}},function(){"use strict";function a(a){return"undefined"!=typeof a}function b(b){return a(b)?b.constructor.name:"undefined"}function c(a){if(a>=m.INT8&&a<n.length)return n[a];throw new Error("Unknown type "+a)}function d(a){return a>=m.FLT&&a<=m.DBL}function e(a,b){function d(){var a={};return a.hdr_offset=0,a.data_offset=0,a.data_length=0,a.n_filled=0,a.chunk_size=0,a.chunk_dims=[],a.sym_btree=0,a.sym_lheap=0,a.name="",a.attributes={},a.children=[],a.array=void 0,a.type=-1,a.inflate=!1,a.dims=[],a}function e(){da=Z}function f(){var a=Z-da;if(a%$!==0){var c=$-a%$;Z+=c,b&&console.log("skipping "+c+" bytes at "+a+" for alignmnent")}}function g(a){Z+=a}function h(a){Z=a}function i(){return Z}function j(){var a=ba.getUint8(Z);return Z+=1,a}function k(){var a=ba.getUint16(Z,_);return Z+=2,a}function l(){var a=ba.getUint32(Z,_);return Z+=4,a}function n(){var a=ba.getUint64(Z,_);return Z+=8,a}function o(){var a=ba.getFloat32(Z,_);return Z+=4,a}function p(){var a=ba.getFloat64(Z,_);return Z+=8,a}function q(a){var b=0;if(a=a||ca.offsz,4===a)b=ba.getUint32(Z,_);else{if(8!==a)throw new Error("Unsupported value for offset size "+a);b=ba.getUint64(Z,_)}return Z+=a,b}function r(){var a=ba.getUint64(Z,_);return Z+=ca.lensz,a}function s(a){var b,c,d="";for(b=0;a>b;b+=1){if(c=j(),0===c){Z+=a-b-1;break}d+=String.fromCharCode(c)}return d}function t(b,c,d){var e,f,g,h,i=Z;switch(d&&(Z=d),b){case m.INT8:e=new Int8Array(a,Z,c);break;case m.UINT8:e=new Uint8Array(a,Z,c);break;case m.INT16:if(Z%2!==0)for(g=new ArrayBuffer(c),f=c/2,e=new Int16Array(g),h=0;f>h;h+=1)e[h]=k();else e=new Int16Array(a,Z,c/2),Z+=c;break;case m.UINT16:if(Z%2!==0)for(g=new ArrayBuffer(c),f=c/2,e=new Uint16Array(g),h=0;f>h;h+=1)e[h]=k();else e=new Uint16Array(a,Z,c/2),Z+=c;break;case m.INT32:if(Z%4!==0)for(g=new ArrayBuffer(c),f=c/4,e=new Int32Array(g),h=0;f>h;h+=1)e[h]=l();else e=new Int32Array(a,Z,c/4),Z+=c;break;case m.UINT32:if(Z%4!==0)for(g=new ArrayBuffer(c),f=c/4,e=new Uint32Array(g),h=0;f>h;h+=1)e[h]=l();else e=new Uint32Array(a,Z,c/4),Z+=c;break;case m.FLT:if(Z%4!==0)for(g=new ArrayBuffer(c),f=c/4,e=new Float32Array(g),h=0;f>h;h+=1)e[h]=o();else e=new Float32Array(a,Z,c/4),Z+=c;break;case m.DBL:if(Z%8!==0)for(g=new ArrayBuffer(c),f=c/8,e=new Float64Array(g),h=0;f>h;h+=1)e[h]=p();else e=new Float64Array(a,Z,c/8),Z+=c;break;default:throw new Error("Bad type in getArray "+b)}return d&&(Z=i),e}function u(a){var b,c;switch(a){case 1:b=ba.getUint8(Z);break;case 2:b=ba.getUint16(Z,_);break;case 4:b=ba.getUint32(Z,_);break;case 8:b=ba.getUint64(Z,_);break;default:if(b=0,_)for(c=a-1;c>=0;c--)b=(b<<8)+ba.getUint8(Z+c);else for(c=0;a>c;c++)b=(b<<8)+ba.getUint8(Z+c)}return Z+=a,b}function v(a){var b;for(b=0;b<a.length;b+=1)if(ba.getUint8(Z+b)!==a.charCodeAt(b))return!1;return g(a.length),!0}function w(){var a={};if(!v("HDF\r\n\n"))throw new Error("Bad magic string in HDF5");if(a.sbver=j(),a.sbver>2)throw new Error("Unsupported HDF5 superblock version "+a.sbver);return a.sbver<=1?(a.fsver=j(),a.rgver=j(),g(1),a.shver=j(),a.offsz=j(),a.lensz=j(),g(1),a.gln_k=k(),a.gin_k=k(),a.cflags=l(),1===a.sbver&&(a.isin_k=k(),g(2)),a.base_addr=q(a.offsz),a.gfsi_addr=q(a.offsz),a.eof_addr=q(a.offsz),a.dib_addr=q(a.offsz),a.root_ln_offs=q(a.offsz),a.root_addr=q(a.offsz),a.root_cache_type=l(),g(4),g(16)):(a.offsz=j(),a.lensz=j(),a.cflags=j(),a.base_addr=q(a.offsz),a.ext_addr=q(a.offsz),a.eof_addr=q(a.offsz),a.root_addr=q(a.offsz),a.checksum=l()),b&&console.log("HDF5 SB "+a.sbver+" "+a.offsz+" "+a.lensz+" "+a.cflags),a}function x(){var a={};if(!v("FRHP"))throw new Error("Bad or missing FRHP signature");a.ver=j(),a.idlen=k(),a.iof_el=k(),a.flags=j(),a.objmax=l(),a.objnid=r(),a.objbta=q(),a.nf_blk=r(),a.af_blk=q(),a.heap_total=r(),a.heap_alloc=r(),a.bai_offset=r(),a.heap_nobj=r(),a.heap_chuge=r(),a.heap_nhuge=r(),a.heap_ctiny=r(),a.heap_ntiny=r(),a.table_width=k(),a.start_blksz=r(),a.max_blksz=r(),a.max_heapsz=k(),a.rib_srows=k(),a.root_addr=q(),a.rib_crows=k();var c=Math.log2(a.max_blksz)-Math.log2(a.start_blksz)+2;if(a.K=Math.min(a.rib_crows,c)*a.table_width,a.N=a.rib_crows<c?0:a.K-c*a.table_width,b&&(console.log("FRHP V"+a.ver+" F"+a.flags+" "+a.objbta+" Total:"+a.heap_total+" Alloc:"+a.heap_alloc+" #obj:"+a.heap_nobj+" width:"+a.table_width+" start_blksz:"+a.start_blksz+" max_blksz:"+a.max_blksz+" "+a.max_heapsz+" srows:"+a.rib_srows+" crows:"+a.rib_crows+" "+a.heap_nhuge),console.log("   K: "+a.K+" N: "+a.N)),a.iof_el>0)throw new Error("Filters present in fractal heap.");return a}function y(){var a={};if(!v("BTHD"))throw new Error("Bad or missing BTHD signature");return a.ver=j(),a.type=j(),a.nodesz=l(),a.recsz=k(),a.depth=k(),a.splitp=j(),a.mergep=j(),a.root_addr=q(),a.root_nrec=k(),a.total_nrec=r(),a.checksum=l(),b&&console.log("BTHD V"+a.ver+" T"+a.type+" "+a.nodesz+" "+a.recsz+" "+a.depth+" "+a.root_addr+" "+a.root_nrec+" "+a.total_nrec),a}function z(a,c,d,e){var f,g,k,m;if(1===c)for(f=0;d>f;f++){k=q(),m=r();var n=r();b&&console.log("  -> "+k+" "+m+" "+n+" "+ea),g=i(),n===ea&&(h(k),N(m,e)),h(g)}else{if(8!==c)throw new Error("Unhandled V2 btree type.");var o,p;o=a.max_heapsz/8;var s=Math.min(a.objmax,a.max_blksz);for(p=256>=s?1:65536>=s?2:4,f=0;d>f;f++){var t=j();if(0!==(192&t))throw new Error("Bad Fractal Heap ID version "+t);var v,w=48&t;if(16===w)ea=u(7);else{if(0!==w)throw new Error("Can't handle this Heap ID: "+t);k=u(o),m=u(p)}if(v=j(),l(),l(),b&&console.log("  -> "+t+" "+k+" "+m+" "+v),g=i(),16===w){h(a.objbta);var x=y();if(1!==x.type)throw new Error("Can only handle type-1 btrees");h(x.root_addr),A(a,x.root_nrec,e)}else{var z=T(a,k);z>=0&&(h(z),N(m,e))}h(g)}}}function A(a,c,d){if(!v("BTLF"))throw new Error("Bad or missing BTLF signature");var e=j(),f=j();b&&console.log("BTLF V"+e+" T"+f+" "+i()),z(a,f,c,d)}function B(a,c,d,e){if(!v("BTIN"))throw new Error("Bad or missing BTIN signature");var f,g=j(),h=j();for(b&&console.log("BTIN V"+g+" T"+h),z(a,h,c,e),f=0;c>=f;f++){var i,k=q(),l=u(1);d>1&&(i=u(1)),b&&console.log(" child->"+k+" "+l+" "+i)}}function C(a){if(a<fa.length)return fa[a];throw new Error("Unknown message type "+a+" "+i())}function D(a,b,c){var d,e=1;for(d=0;d<a.length;d++){var f=a[d]-c[d];e*=b[d]>f?f:b[d]}return e}function E(c){var d,e={};if(!v("TREE"))throw new Error("Bad TREE signature at "+i());if(e.keys=[],e.node_type=j(),e.node_level=j(),e.entries_used=k(),e.left_sibling=q(),e.right_sibling=q(),b&&console.log("BTREE type "+e.node_type+" lvl "+e.node_level+" n_used "+e.entries_used+" "+e.left_sibling+" "+e.right_sibling),c){var f,g=[];for(d=0;d<e.entries_used;d+=1){for(e.keys[d]={},g[d]={},g[d].chunk_size=l(),g[d].filter_mask=l(),g[d].chunk_offsets=[],f=0;f<c.dims.length+1;f+=1)g[d].chunk_offsets.push(n());e.keys[d].child_address=q(),d<e.entries_used&&b&&console.log("  BTREE "+d+" chunk_size "+g[d].chunk_size+" filter_mask "+g[d].filter_mask+" addr "+e.keys[d].child_address)}for(g[d]={},g[d].chunk_size=l(),g[d].filter_mask=l(),g[d].chunk_offsets=[],f=0;f<c.dims.length+1;f+=1)g[d].chunk_offsets.push(n());if(0===e.node_level){var o,p,s,u;for(d=0;d<e.entries_used;d+=1){o=g[d].chunk_size,p=e.keys[d].child_address;var w=D(c.dims,c.chunk_dims,g[d].chunk_offsets);if(c.inflate){switch(s=new Uint8Array(a,p,o),u=pako.inflate(s),c.type){case m.INT8:u=new Int8Array(u.buffer);break;case m.UINT8:u=new Uint8Array(u.buffer);break;case m.INT16:u=new Int16Array(u.buffer);break;case m.UINT16:u=new Uint16Array(u.buffer);break;case m.INT32:u=new Int32Array(u.buffer);break;case m.UINT32:u=new Uint32Array(u.buffer);break;case m.FLT:u=new Float32Array(u.buffer);break;case m.DBL:u=new Float64Array(u.buffer);break;default:throw new Error("Unknown type code "+c.type)}w<u.length&&(u=u.subarray(0,w)),c.array.length-c.n_filled<u.length&&(u=u.subarray(0,c.array.length-c.n_filled),console.log("WARNING: Discarding excess data.")),c.array.set(u,c.n_filled),c.n_filled+=u.length,b&&console.log(c.name+" "+s.length+" "+u.length+" "+c.n_filled+"/"+c.array.length)}else u=t(c.type,o,p),c.array.set(u,c.n_filled),c.n_filled+=u.length}}else for(d=0;d<e.entries_used;d+=1)h(e.keys[d].child_address),E(c)}else for(d=0;d<e.entries_used;d+=1)e.keys[d]={},e.keys[d].key_value=r(),e.keys[d].child_address=q(),b&&console.log("  BTREE "+d+" key "+e.keys[d].key_value+" adr "+e.keys[d].child_address);return e}function F(a,c){if(!v("SNOD"))throw new Error("Bad or missing SNOD signature");var e=j();g(1);var f=k();b&&console.log("hdf5GroupSymbolTable V"+e+" #"+f+" '"+c.name+"'");var m,n,o,p,r,t;for(m=0;m<2*ca.gln_k;m+=1)n=q(),o=q(),p=l(),g(20),f>m&&(r=d(),r.hdr_offset=o,a&&(t=i(),h(a.lh_dseg_off+n),r.name=s(a.lh_dseg_len),h(t)),b&&console.log("    "+m+" O "+n+" A "+o+" T "+p+" '"+r.name+"'"),c.children.push(r))}function G(){var a={};if(!v("HEAP"))throw new Error("Bad or missing HEAP signature");return a.lh_ver=j(),g(3),a.lh_dseg_len=r(),a.lh_flst_len=r(),a.lh_dseg_off=q(),b&&console.log("LHEAP V"+a.lh_ver+" "+a.lh_dseg_len+" "+a.lh_flst_len+" "+a.lh_dseg_off),a}function H(a,c){var d,e=j(),f=j(),h=j();g(1>=e?5:1);var i,k=1,l=[];for(i=0;f>i;i+=1)l[i]=r(),k*=l[i];d=f*ca.lensz+(1>=e?8:4);var m=[];if(0!==(1&h))for(d+=f*ca.lensz,i=0;f>i;i+=1)m[i]=r();var n=[];if(0!==(2&h))for(d+=f*ca.lensz,i=0;f>i;i+=1)n[i]=r();var o="hdf5MsgDataspace V"+e+" N"+f+" F"+h;return b&&(0!==f&&(o+="["+l.join(", ")+"]"),console.log(o)),a>d&&g(a-d),c&&(c.dims=l),k}function I(a){var c=j(),d=j();0!==(1&d)&&n();var e=q(),f=q();0!==(2&d)&&q(),b&&console.log("hdf5MsgLinkInfo V"+c+" F"+d+" FH "+e+" BT "+f);var g=i();if(e<ca.eof_addr){h(e);var k=x(),l=0;S(k,function(b,c,d,e){for(var f=c+e-4;l<k.heap_nobj&&i()<f;)P(a),l+=1;return!0})}h(g)}function J(a){var b=["Fixed-Point","Floating-Point","Time","String","BitField","Opaque","Compound","Reference","Enumerated","Variable-Length","Array"];if(a<b.length)return b[a];throw new Error("Unknown datatype class: "+a)}function K(a){var c,d,e,f,h,i,n,o,p={},q=8,r="",s=j(),t=s>>4,u=15&s,v=[];for(o=0;3>o;o+=1)v[o]=j();var w=l();switch(b&&console.log("hdf5MsgDatatype V"+t+" C"+u+" "+J(u)+" "+v[0]+"."+v[1]+"."+v[2]+" "+w),u){case 0:switch(c=k(),d=k(),w){case 4:p.typ_type=8&v[0]?m.INT32:m.UINT32;break;case 2:p.typ_type=8&v[0]?m.INT16:m.UINT16;break;case 1:p.typ_type=8&v[0]?m.INT8:m.UINT8;break;default:throw new Error("Unknown type size "+w)}p.typ_length=w,q+=4,b&&console.log("  ("+c+" "+d+")");break;case 1:if(r="",b)switch(65&v[0]){case 0:r+="LE ";break;case 1:r+="BE ";break;case 65:r+="VX ";break;default:throw new Error("Reserved fp byte order: "+v[0])}if(c=k(),d=k(),e=j(),f=j(),h=j(),i=j(),n=l(),b&&(r+=c+" "+d+" "+e+" "+f+" "+h+" "+i+" "+n),64===d&&0===c&&52===e&&11===f&&0===h&&52===i&&1023===n&&8===w)p.typ_type=m.DBL;else{if(32!==d||0!==c||23!==e||8!==f||0!==h||23!==i||127!==n||4!==w)throw new Error("Unsupported floating-point type");p.typ_type=m.FLT}b&&console.log(r),p.typ_length=w,q+=12;break;case 3:p.typ_type=m.STR,p.typ_length=w;break;default:throw new Error("Unimplemented HDF5 data class "+u)}return a>q&&g(a-q),p}function L(a){var c,d,e,f,h,m,n,o="",p=j(),s=[],t=1;if(1===p||2===p){if(d=j(),c=j(),g(5),b&&(o+="hdf5MsgLayout V"+p+" N"+d+" C"+c),1===c||2===c){var u=q();b&&(o+=" A"+u),a.data_offset=u}for(f=0;d>f;f+=1)s[f]=l(),t*=s[f];b&&(o+="["+s.join(", ")+"]"),2===c&&(n=l(),a.chunk_dims=s,a.chunk_size=t*n,b&&(o+=" E"+n)),0===c?(e=l(),b&&(o+="("+e+")"),a.data_offset=i(),a.data_length=e):1===c&&(a.data_length=t)}else{if(3!==p)throw new Error("Illegal layout version "+p);if(c=j(),o="hdf5MsgLayout V"+p+" C"+c,0===c)e=k(),b&&(o+="("+e+")"),a.data_offset=i(),a.data_length=e;else if(1===c)h=q(),m=r(),b&&(o+="("+h+", "+m+")"),a.data_offset=h,a.data_length=m;else if(2===c){for(d=j(),h=q(),a.data_offset=h,a.chunk_size=1,f=0;d-1>f;f+=1)s[f]=l(),t*=s[f];b&&(o+="(N"+d+", A"+h+" ["+s.join(",")+"]"),n=l(),a.chunk_dims=s,a.chunk_size=t*n,b&&(o+=" E"+n)}}b&&console.log(o)}function M(a){var c=j(),d=j(),e="hdf5MsgPipeline V"+c+" N"+d;1===c&&g(6),b&&console.log(e);var f,h,i,l,m;for(f=0;d>f;f+=1){if(h=k(),1!==h)throw new Error("Unimplemented HDF5 filter "+h);if("object"!=typeof pako)throw new Error("Need pako to inflate data.");a.inflate=!0,i=1===c||h>256?k():0,l=k(),m=k(),0!==(1&m)&&(m+=1),0!==i&&g(i),g(4*m),b&&console.log("  "+f+" ID"+h+" F"+l+" "+m)}}function N(a,c){var d=j(),e=j(),f=k(),g=k(),h=k(),l="hdf5MsgAttribute V"+d+" F"+e+" "+a+": ";if(0!==(3&e))throw new Error("Shared dataspaces and datatypes are not supported.");if(3===d){var n=j();b&&(l+=0===n?"ASCII":"UTF-8")}b&&(l+="("+f+" "+g+" "+h+")"),3>d&&(f=8*Math.floor((f+7)/8),g=8*Math.floor((g+7)/8),h=8*Math.floor((h+7)/8),b&&(l+="/("+f+" "+g+" "+h+")"));var o=s(f);b&&(l+=" Name: "+o,console.log(l));var p=K(g),q=H(h),r=0;r=a>0?3>d?a-(8+f+g+h):a-(9+f+g+h):p.typ_length*q,b&&console.log("  attribute data size "+r+" "+i());var u;u=p.typ_type===m.STR?s(r):t(p.typ_type,r),c.attributes[o]=u}function O(){var a=4,c=8,d=j(),e=j();0!==(1&e)&&(k(),k()),0!==(2&e)&&(a=k(),c=k()),b&&console.log("hdf5MsgGroupInfo V"+d+" F"+e+" ENT "+a+" LNL "+c)}function P(a){var c=j(),e=0;if(1!==c)throw new Error("Bad link message version "+c);var f=j();0!==(8&f)&&(e=j()),0!==(4&f)&&n(),0!==(16&f)&&j();var g=1<<(3&f),h=u(g),i=d();i.name=s(h),0===(8&f)&&(i.hdr_offset=q()),b&&console.log("hdf5MsgLink V"+c+" F"+f+" T"+e+" NM "+i.name+" OF "+i.hdr_offset),a.children.push(i)}function Q(a,c,d,e){if(!v("FHDB"))throw new Error("Bad or missing FHDB signature");var f=j();if(0!==f)throw new Error("Bad FHDB version: "+f);q();var g=Math.ceil(a.max_heapsz/8),h=u(g);0!==(2&a.flags)&&l(),b&&console.log("FHDB V:"+f+" R:"+c+" O:"+h+" A:"+d);var i=5+ca.offsz+g;0!==(2&a.flags)&&(i+=4);var k;return k=1>=c?a.start_blksz:Math.pow(2,c-1)*a.start_blksz,e?e(c,d,h,k):!0}function R(a,c){if(!v("FHIB"))throw new Error("Bad or missing FHIB signature");var d=j();if(0!==d)throw new Error("Bad FHIB version: "+d);q();var e=Math.ceil(a.max_heapsz/8),f=u(e);b&&console.log("FHIB V:"+d+" O:"+f);var g,i,k=[];for(g=0;g<a.K;g+=1)i=q(),i<ca.eof_addr&&(b&&console.log("direct block at "+i),k.push(i));var m=[];for(g=0;g<a.N;g+=1)i=q(),i<ca.eof_addr&&(b&&console.log("indirect block at "+i),m.push(i));for(l(),g=0;g<k.length;g++)if(h(k[g]),!Q(a,Math.floor(g/a.table_width),k[g],c))return!1;for(g=0;g<m.length;g++)if(h(m[g]),!R(a,c))return!1;return!0}function S(a,b){h(a.root_addr),0===a.K?Q(a,0,a.root_addr,b):R(a,b)}function T(a,b){var c=-1;return S(a,function(a,d,e,f){return b>=e&&e+f>b?(c=d+(b-e),!1):!0}),c}function U(a){var c=j();if(0!==c)throw new Error("Bad attribute information message version: "+c);var d=j();0!==(1&d)&&k();var e=q(),f=q();0!==(2&d)&&q(),b&&console.log("hdf5MsgAttrInfo V"+c+" F"+d+" HP "+e+" AN "+f);var g,l=i();if(e<ca.eof_addr&&(h(e),g=x()),f<ca.eof_addr){h(f);var m=y();if(8!==m.type)throw new Error("Can only handle indexed attributes.");h(m.root_addr),m.depth>0?B(g,m.root_nrec,m.depth,a):A(g,m.root_nrec,a)}h(l)}function V(a,c){var d,e={};switch(a.hm_type){case 1:H(a.hm_size,c);break;case 2:I(c);break;case 3:d=K(a.hm_size),c&&(c.type=d.typ_type);break;case 6:P(c);break;case 8:L(c);break;case 10:O();break;case 11:M(c);break;case 12:N(a.hm_size,c);break;case 16:e.cq_off=q(),e.cq_len=r(),aa.push(e),b&&console.log("hdf5MsgObjHdrContinue "+e.cq_off+" "+e.cq_len);break;case 17:c.sym_btree=q(),c.sym_lheap=q(),b&&console.log("hdf5MsgSymbolTable "+c.sym_btree+" "+c.sym_lheap);break;case 21:U(c);break;case 0:case 4:case 5:case 7:case 18:case 19:case 20:case 22:case 24:g(a.hm_size);break;default:throw new Error("Unknown message type: "+a.hm_type)}}function W(a){if(!v("OHDR"))throw new Error("Bad or missing OHDR signature");var c=j(),d=j();0!==(32&d)&&(l(),l(),l(),l()),0!==(16&d)&&(k(),k());var e=1<<(3&d),f=u(e),m=0,n=0,o=f;b&&console.log("hdf5V2ObjectHeader V"+c+" F"+d+" HS"+f);for(var p,q,r;;){for(;o-n>=8;)p={},p.hm_type=j(),p.hm_size=k(),p.hm_flags=j(),b&&console.log("  msg"+m+" F"+p.hm_flags+" T "+p.hm_type+" S "+p.hm_size+" ("+n+"/"+o+") "+C(p.hm_type)),0!==(4&d)&&(p.hm_corder=k()),r=i(),V(p,a),h(r+p.hm_size),n+=p.hm_size+4,m+=1;if(o-n>4&&g(o-(n+4)),l(),0===aa.length)break;if(q=aa.shift(),h(q.cq_off),o=q.cq_len-4,n=0,b&&console.log("continuing with "+q.cq_len+" bytes at "+i()),!v("OCHK"))throw new Error("Bad v2 object continuation")}a.children.forEach(function(a,c){h(a.hdr_offset),b&&console.log(c+" "+a.hdr_offset+" "+a.name),v("OHDR")?(h(a.hdr_offset),W(a)):(h(a.hdr_offset),Y(a))})}function X(a){if(0!==a.chunk_size&&a.data_offset>0&&a.data_offset<ca.eof_addr){h(a.data_offset);var d,e=1;for(d=0;d<a.dims.length;d+=1)e*=a.dims[d];e*=c(a.type),b&&console.log("allocating "+e+" bytes");var f=new ArrayBuffer(e);switch(a.n_filled=0,a.type){case m.INT8:a.array=new Int8Array(f);break;case m.UINT8:a.array=new Uint8Array(f);break;case m.INT16:a.array=new Int16Array(f);break;case m.UINT16:a.array=new Uint16Array(f);break;case m.INT32:a.array=new Int32Array(f);break;case m.UINT32:a.array=new Uint32Array(f);break;case m.FLT:a.array=new Float32Array(f);break;case m.DBL:a.array=new Float64Array(f);break;default:throw new Error("Illegal type: "+a.type)}E(a)}else a.data_offset>0&&a.data_offset<ca.eof_addr?(b&&console.log("loading "+a.data_length+" bytes from "+a.data_offset+" to "+a.name),a.array=t(a.type,a.data_length,a.data_offset)):b&&console.log("data not present for /"+a.name+"/");a.children.forEach(function(a){X(a)})}function Y(a){var c={};if(e(),c.oh_ver=j(),g(1),c.oh_n_msgs=k(),c.oh_ref_cnt=l(),c.oh_hdr_sz=l(),1!==c.oh_ver)throw new Error("Bad v1 object header version: "+c.oh_ver);b&&console.log("hdf5V1ObjectHeader V"+c.oh_ver+" #M "+c.oh_n_msgs+" RC "+c.oh_ref_cnt+" HS "+c.oh_hdr_sz);var d,m,n,o,p=c.oh_hdr_sz;for(m=0;m<c.oh_n_msgs;m+=1){if(8>=p){if(0===aa.length)break;d=aa.shift(),h(d.cq_off),p=d.cq_len,b&&console.log("continuing with "+p+" bytes at "+i()),e()}if(f(),n={},n.hm_type=k(),n.hm_size=k(),n.hm_flags=j(),n.hm_size%8!==0)throw new Error("Size is not 8-byte aligned: "+n.hm_size);g(3),p-=8+n.hm_size,b&&console.log("  msg"+m+" F "+n.hm_flags+" T "+n.hm_type+" S "+n.hm_size+"("+p+") "+C(n.hm_type)),o=i(),V(n,a),h(o+n.hm_size)}if(0!==a.sym_btree&&0!==a.sym_lheap){h(a.sym_btree);var q=E();h(a.sym_lheap);var r,s=G();for(r=0;r<q.entries_used;r+=1)h(q.keys[r].child_address),v("SNOD")?(h(q.keys[r].child_address),F(s,a)):(h(q.keys[r].child_address),Y(a));a.children.forEach(function(a){h(a.hdr_offset),Y(a)})}}var Z=0,$=8,_=!0,aa=[],ba=new DataView(a),ca={},da=0;b=b||!1,ba.getUint64=function(a,b){var c=ba.getUint32(a+0,b),d=ba.getUint32(a+4,b);return b?(d<<32)+c:(c<<32)+d};var ea,fa=["NIL","Dataspace","LinkInfo","Datatype","FillValue 1","FillValue 2","Link","ExternalFiles","Layout","BOGUS","GroupInfo","FilterPipeline","Attribute","ObjectComment","ObjectModTime 1","SharedMsgTable","ObjHdrContinue","SymbolTable","ObjectModTime 2","BtreeKValue","DriverInfo","AttrInfo","ObjectRefCnt","MESSAGE23","FileSpaceInfo"],ga=d();return ca=w(),h(ca.root_addr),ca.sbver<=1?Y(ga):W(ga),X(ga),ga}function f(a,b){var c="";if(a&&a.length){var d;for(d=0;d<a.length-1;d+=1)c+=a[d],c+=b;c+=a[d]}return c}function g(a,c){var d,e="";for(d=0;2*c>d;d+=1)e+=" ";e+=a.name+(a.children.length?"/":""),a.type>0&&(e+=" "+b(a.array),a.dims.length&&(e+="["+a.dims.join(", ")+"]"),e+=a.array?":"+a.array.length:" NULL"),console.log(e),Object.keys(a.attributes).forEach(function(g){var h=a.attributes[g];for(e="",d=0;2*c+1>d;d+=1)e+=" ";e+=a.name+":"+g+" "+b(h)+"["+h.length+"] ","string"==typeof h?e+=JSON.stringify(h):(e+="{"+f(h.slice(0,16),", "),h.length>16&&(e+=", ..."),e+="}"),console.log(e)}),a.children.forEach(function(a){g(a,c+1)})}function h(b,c,d){var e;return b.name===c&&b.type>0?e=b:b.children.find(function(b){return e=h(b,c,d+1),a(e)}),e}function i(b,c,d){var e=b.attributes[c];return e?e:(b.children.find(function(b){return e=i(b,c,d+1),a(e)}),e)}function j(a,b,c,e,f){var g=new ArrayBuffer(a.array.length*Float32Array.BYTES_PER_ELEMENT),h=new Float32Array(g),i=a.dims.length-b.dims.length;if(1>i)throw new Error("Too few slice dimensions: "+a.dims.length+" "+b.dims.length);var j,k=1;for(j=b.dims.length;j<a.dims.length;j+=1)k*=a.dims[j];f&&console.log(k+" voxels in slice.");var l=0,m=0,n=-Number.MAX_VALUE,o=Number.MAX_VALUE,p=a.array,q=c.array,r=b.array;f&&console.log("valid range is "+e[0]+" to "+e[1]);var s,t,u,v,w,x=e[0],y=d(a.type);for(j=0;j<b.array.length;j+=1)if(f&&console.log(j+" "+r[j]+" "+q[j]+" "+p[j*k]),y)for(v=0;k>v;v+=1)w=p[m],w<e[0]||w>e[1]?h[m]=0:(h[m]=w,l+=w,w>n&&(n=w),o>w&&(o=w)),m+=1;else for(s=e[1]-e[0],t=q[j]-r[j],u=r[j],v=0;k>v;v+=1)w=(p[m]-x)/s*t+u,h[m]=w,l+=w,m+=1,w>n&&(n=w),o>w&&(o=w);return f&&(console.log("Min: "+o),console.log("Max: "+n),console.log("Sum: "+l),console.log("Mean: "+l/m),console.log("Count: "+m)),g}function k(a,b){var c=a.order;return"Uint8Array"===b.array.constructor.name&&c.length>0&&"vector_dimension"===c[c.length-1]&&3===a.vector_dimension.space_length}function l(a){var b,c=a.array,d=c.length,e=new ArrayBuffer(d/3*4),f=new Uint8Array(e),g=0;for(b=0;d>b;b+=3)f[g+0]=c[b+0],f[g+1]=c[b+1],f[g+2]=c[b+2],f[g+3]=255,g+=4;return e}var m={INT8:1,UINT8:2,INT16:3,UINT16:4,INT32:5,UINT32:6,FLT:7,DBL:8,STR:9};Array.prototype.find||(Array.prototype.find=function(a){if(null===this)throw new TypeError("Array.prototype.find called on null or undefined");if("function"!=typeof a)throw new TypeError("predicate must be a function");for(var b,c=Object(this),d=c.length>>>0,e=arguments[1],f=0;d>f;f++)if(b=c[f],a.call(e,b,f,c))return b;return void 0});var n=[0,1,1,2,2,4,4,4,8,0],o=BrainBrowser.VolumeViewer;o.utils.hdf5Loader=function(b){var c,d=!1;try{c=e(b,d)}catch(f){d&&(console.log(f),console.log("Trying as NetCDF...")),c=o.utils.netcdfReader(b,d)}d&&g(c,0);var n=h(c,"image");if(!a(n))throw new Error("Can't find image dataset.");var p=i(n,"valid_range",0);if(!a(p)){var q,r;switch(n.type){case m.INT8:q=-128,r=127;break;case m.UINT8:q=0,r=255;break;case m.INT16:q=-32768,r=32767;break;case m.UINT16:q=0,r=65535;break;case m.INT32:q=-(1<<31),r=(1<<31)-1;break;case m.UINT32:q=0,r=0}p=Float32Array.of(q,r)}var s=h(c,"image-min");a(s)&&a(s.array)||(s={array:Float32Array.of(0),dims:[]});var t=h(c,"image-max");a(t)&&a(t.array)||(t={array:Float32Array.of(1),dims:[]});var u={},v=i(n,"dimorder",0);if("string"!=typeof v)throw new Error("Can't find dimension order.");u.order=v.split(","),u.order.forEach(function(b){var d=h(c,b);if(!a(d))throw new Error("Can't find dimension variable "+b);if(u[b]={},v=i(d,"step",0),a(v)||(v=Float32Array.of(1)),u[b].step=v[0],v=i(d,"start",0),a(v)||(v=Float32Array.of(0)),u[b].start=v[0],v=i(d,"length",0),!a(v))throw new Error("Can't find length for "+b);u[b].space_length=v[0],v=i(d,"direction_cosines",0),a(v)?u[b].direction_cosines=Array.prototype.slice.call(v):"xspace"===b?u[b].direction_cosines=[1,0,0]:"yspace"===b?u[b].direction_cosines=[0,1,0]:"zspace"===b&&(u[b].direction_cosines=[0,0,1])});var w;return k(u,n)?(u.order.pop(),u.datatype="rgb8",w=l(n)):(u.datatype="float32",w=j(n,s,t,p,d)),{header_text:JSON.stringify(u),raw_data:w}}}(),function(){"use strict";function a(a,b){var c,f={order:["xspace","yspace","zspace"],xspace:{},yspace:{},zspace:{}},g=new DataView(a,0,284),h=!0,i=g.getUint32(0,!0);1===i?h=!0:16777216===i?h=!1:c="This does not look like an MGH file.";var j=0,k=[0,0,0,0],l=4,m=1;for(j=0;4>j&&(k[j]=g.getUint32(l,h),!(k[j]<=1));j++)m*=k[j],l+=4;(3>j||j>4)&&(c="Cannot handle "+j+"-dimensional images yet.");var n,o,p=g.getUint32(20,h),q=g.getUint16(28,h),r=[1,1,1],s=[[-1,0,0],[0,0,-1],[0,1,0],[0,0,0]];
if(q){for(l=30,n=0;3>n;n++)r[n]=g.getFloat32(l,h),l+=4;for(n=0;4>n;n++)for(o=0;3>o;o++)s[n][o]=g.getFloat32(l,h),l+=4}if(e)for(n=0;3>n;n++){var t="";for(o=0;4>o;o++)t+="xyzc"[o]+"_"+"ras"[n]+" "+s[o][n]+" ";console.log(t)}for(var u=[0,1,2],v=0;3>v;v++){var w=0,x=Math.abs(s[v][0]),y=Math.abs(s[v][1]),z=Math.abs(s[v][2]);f.order[v]="xspace",y>x&&y>z&&(w=1,f.order[v]="yspace"),z>x&&z>y&&(w=2,f.order[v]="zspace"),u[v]=w}4===j&&(e&&console.log("Creating time dimension: "+k[3]),f.time={space_length:k[3],step:1,start:0,name:"time"},f.order.push("time"));var A=!1,B=[[0,0,0,0],[0,0,0,0],[0,0,0,0]];for(n=0;3>n;n++)for(o=0;3>o;o++)B[n][o]=s[o][n]*r[n];for(n=0;3>n;n++){var C=0;for(o=0;3>o;o++)C+=B[n][o]*(k[o]/2);A?B[n][3]=-C:B[n][3]=s[3][n]-C}var D=[[0,0,0,0],[0,0,0,0],[0,0,0,0],[0,0,0,0]];for(n=0;3>n;n++)for(o=0;4>o;o++){var E=o;3>o&&(E=u[o]),D[n][E]=B[n][o]}if(d.utils.transformToMinc(D,f),void 0!==c)throw BrainBrowser.events.triggerEvent("error",{message:c}),new Error(c);for(f.datatype=p,f.little_endian=h,f.nvoxels=m,n=0;3>n;n++)f[f.order[n]].space_length=k[n];BrainBrowser.utils.isFunction(b)&&b(f)}function b(a,b,e){var f=d.createVolume(a,c(a,b));f.type="mgh",f.intensity_min=a.voxel_min,f.intensity_max=a.voxel_max,f.saveOriginAndTransform(a),BrainBrowser.utils.isFunction(e)&&e(f)}function c(a,b){var c=null,e=1;switch(a.datatype){case 0:e=1;break;case 1:case 3:e=4;break;case 4:e=2;break;default:var f="Unsupported data type: "+a.datatype;throw BrainBrowser.events.triggerEvent("error",{message:f}),new Error(f)}var g=a.nvoxels*e;switch(e>1&&!a.little_endian&&d.utils.swapn(new Uint8Array(b,284,g),e),a.datatype){case 0:c=new Uint8Array(b,284,a.nvoxels);break;case 1:c=new Int32Array(b,284,a.nvoxels);break;case 3:c=new Float32Array(b,284,a.nvoxels);break;case 4:c=new Int16Array(b,284,a.nvoxels)}d.utils.scanDataRange(c,a);var h,i=1;for(h=0;h<a.order.length;h++)a[a.order[h]].offset=i,i*=a[a.order[h]].space_length;return c}var d=BrainBrowser.VolumeViewer,e=!1;d.volume_loaders.mgh=function(c,d){var e;if(c.url)BrainBrowser.loader.loadFromURL(c.url,function(c){a(c,function(a){b(a,c,d)})},{result_type:"arraybuffer"});else if(c.file)BrainBrowser.loader.loadFromFile(c.file,function(c){a(c,function(a){b(a,c,d)})},{result_type:"arraybuffer"});else{if(!c.source)throw e="invalid volume description.\nDescription must contain the property 'url', 'file' or 'source'.",BrainBrowser.events.triggerEvent("error",{message:e}),new Error(e);a(c.source,function(a){b(a,c.source,d)})}}}(),function(){"use strict";function a(a,b){var c=null;switch(a.datatype){case"int8":c=new Int8Array(b);break;case"int16":c=new Int16Array(b);break;case"int32":c=new Int32Array(b);break;case"float32":c=new Float32Array(b);break;case"float64":c=new Float64Array(b);break;case"uint8":c=new Uint8Array(b);break;case"uint16":c=new Uint16Array(b);break;case"uint32":case"rgb8":c=new Uint32Array(b);break;default:var d="Unsupported data type: "+a.datatype;throw BrainBrowser.events.triggerEvent("error",{message:d}),new Error(d)}return e.utils.scanDataRange(c,a),c}function b(b,c,d){var f=e.createVolume(b,a(b,c));f.type="minc",f.saveOriginAndTransform(b),f.intensity_min=b.voxel_min,f.intensity_max=b.voxel_max,BrainBrowser.utils.isFunction(d)&&d(f)}function c(a){a.xspace.name="xspace",a.yspace.name="yspace",a.zspace.name="zspace",a.xspace.width_space=a.yspace,a.xspace.width=a.yspace.space_length,a.xspace.height_space=a.zspace,a.xspace.height=a.zspace.space_length,a.yspace.width_space=a.xspace,a.yspace.width=a.xspace.space_length,a.yspace.height_space=a.zspace,a.yspace.height=a.zspace.space_length,a.zspace.width_space=a.xspace,a.zspace.width=a.xspace.space_length,a.zspace.height_space=a.yspace,a.zspace.height=a.yspace.space_length,void 0===a.voxel_min&&(a.voxel_min=0),void 0===a.voxel_max&&(a.voxel_max=255)}function d(a,b){var c,d;try{c=JSON.parse(a)}catch(e){throw d="server did not respond with valid JSON\nResponse was: \n"+a,BrainBrowser.events.triggerEvent("error",{message:d}),new Error(d)}4===c.order.length&&(c.order=c.order.slice(1)),c.datatype=c.datatype||"uint8",c.xspace.space_length=parseFloat(c.xspace.space_length),c.yspace.space_length=parseFloat(c.yspace.space_length),c.zspace.space_length=parseFloat(c.zspace.space_length),c.xspace.start=parseFloat(c.xspace.start),c.yspace.start=parseFloat(c.yspace.start),c.zspace.start=parseFloat(c.zspace.start),c.xspace.step=parseFloat(c.xspace.step),c.yspace.step=parseFloat(c.yspace.step),c.zspace.step=parseFloat(c.zspace.step),c.xspace.direction_cosines=c.xspace.direction_cosines||[1,0,0],c.yspace.direction_cosines=c.yspace.direction_cosines||[0,1,0],c.zspace.direction_cosines=c.zspace.direction_cosines||[0,0,1],c.xspace.direction_cosines=c.xspace.direction_cosines.map(parseFloat),c.yspace.direction_cosines=c.yspace.direction_cosines.map(parseFloat),c.zspace.direction_cosines=c.zspace.direction_cosines.map(parseFloat),c[c.order[0]].offset=c[c.order[1]].space_length*c[c.order[2]].space_length,c[c.order[1]].offset=c[c.order[2]].space_length,c[c.order[2]].offset=1,c.time&&(c.time.space_length=parseFloat(c.time.space_length),c.time.start=parseFloat(c.time.start),c.time.step=parseFloat(c.time.step),c.time.offset=c.xspace.space_length*c.yspace.space_length*c.zspace.space_length),BrainBrowser.utils.isFunction(b)&&b(c)}var e=BrainBrowser.VolumeViewer;e.volume_loaders.minc=function(a,c){var f;if(!a.header_file&&a.raw_data_file)BrainBrowser.loader.loadFromFile(a.raw_data_file,function(a){var f=e.utils.hdf5Loader(a);d(f.header_text,function(a){b(a,f.raw_data,c)})},{result_type:"arraybuffer"});else if(!a.header_url&&a.raw_data_url)BrainBrowser.loader.loadFromURL(a.raw_data_url,function(a){var f=e.utils.hdf5Loader(a);d(f.header_text,function(a){b(a,f.raw_data,c)})},{result_type:"arraybuffer"});else if(a.header_url&&a.raw_data_url)BrainBrowser.loader.loadFromURL(a.header_url,function(e){d(e,function(d){BrainBrowser.loader.loadFromURL(a.raw_data_url,function(a){b(d,a,c)},{result_type:"arraybuffer"})})});else if(a.header_file&&a.raw_data_file)BrainBrowser.loader.loadFromFile(a.header_file,function(e){d(e,function(d){BrainBrowser.loader.loadFromFile(a.raw_data_file,function(a){b(d,a,c)},{result_type:"arraybuffer"})})});else{if(!a.header_source||!a.raw_data_source)throw f="invalid volume description.\nDescription must contain property pair 'header_url' and 'raw_data_url', \n'header_file' and 'raw_data_file' \nor 'header_source' and 'raw_data_source'.",BrainBrowser.events.triggerEvent("error",{message:f}),new Error(f);d(a.header_source,function(d){b(d,a.raw_data_source,c)})}},e.createVolume=function(a,b){var d=document.createElement("canvas").getContext("2d"),f={};c(a);var g={position:{},current_time:0,data:b,header:a,intensity_min:0,intensity_max:255,slice:function(a,b,c){b=void 0===b?g.position[a]:b,c=void 0===c?g.current_time:c;var d=g.header;if(void 0===d.order)return null;if(c=c||0,f[a]=f[a]||[],f[a][c]=f[a][c]||[],void 0!==f[a][c][b])return f[a][c][b];var e,h,i,j,k,l,m,n,o,p=d.time?c*d.time.offset:0,q=d[a],r=q.width_space,s=q.height_space,t=q.width,u=q.height,v=q.offset,w=r.offset,x=s.offset,y=new g.data.constructor(t*u),z=r.step>0,A=s.step>0,B=q.step>0,C=0;if(l=B?b:q.space_length-b-1,l>=0&&l<q.space_length)for(m=p+l*v,h=u-1;h>=0;h--)for(k=A?h:u-h-1,n=m+k*x,i=0;t>i;i++)j=z?i:t-i-1,o=n+j*w,y[C++]=g.data[o];return e={axis:a,data:y,width_space:r,height_space:s,width:t,height:u},f[a][c][b]=e,e},saveOriginAndTransform:function(a){var b=a.xspace.start,c=a.yspace.start,d=a.zspace.start,e=a.xspace.direction_cosines,f=a.yspace.direction_cosines,g=a.zspace.direction_cosines,h=a.xspace.step,i=a.yspace.step,j=a.zspace.step;a.voxel_origin={x:b*e[0]+c*f[0]+d*g[0],y:b*e[1]+c*f[1]+d*g[1],z:b*e[2]+c*f[2]+d*g[2]};var k=a.voxel_origin,l=(-k.x*e[0]-k.y*e[1]-k.z*e[2])/h,m=(-k.x*f[0]-k.y*f[1]-k.z*f[2])/i,n=(-k.x*g[0]-k.y*g[1]-k.z*g[2])/j;a.w2v=[[e[0]/h,e[1]/h,e[2]/h,l],[f[0]/i,f[1]/i,f[2]/i,m],[g[0]/j,g[1]/j,g[2]/j,n]]},getSliceImage:function(a,b,c,f){b=b||1;var h,i=g.color_map;if(!i)throw h="No color map set for this volume. Cannot render slice.",g.triggerEvent("error",h),new Error(h);var j=a.width_space.step,k=a.height_space.step,l=Math.abs(Math.floor(a.width*j*b)),m=Math.abs(Math.floor(a.height*k*b)),n=d.createImageData(a.width,a.height),o=d.createImageData(l,m);if("rgb8"===g.header.datatype){var p=new Uint8ClampedArray(a.data.buffer);n.data.set(p,0)}else i.mapColors(a.data,{min:g.intensity_min,max:g.intensity_max,contrast:c,brightness:f,destination:n.data});return o.data.set(e.utils.nearestNeighbor(n.data,n.width,n.height,l,m,{block_size:4})),o},getIntensityValue:function(a,b,c,d){var e=g.header,f=g.getVoxelCoords();if(a=void 0===a?f.i:a,b=void 0===b?f.j:b,c=void 0===c?f.k:c,d=void 0===d?g.current_time:d,0>a||a>=e[e.order[0]].space_length||0>b||b>=e[e.order[1]].space_length||0>c||c>=e[e.order[2]].space_length)return 0;var h=e.time?d*e.time.offset:0,i=a*e[e.order[0]].offset+b*e[e.order[1]].offset+c*e[e.order[2]].offset+h;return g.data[i]},getVoxelCoords:function(){var a=g.header,b={xspace:a.xspace.step>0?g.position.xspace:a.xspace.space_length-g.position.xspace,yspace:a.yspace.step>0?g.position.yspace:a.yspace.space_length-g.position.yspace,zspace:a.zspace.step>0?g.position.zspace:a.zspace.space_length-g.position.zspace};return{i:b[a.order[0]],j:b[a.order[1]],k:b[a.order[2]]}},setVoxelCoords:function(a,b,c){var d=g.header,e=d.order[0],f=d.order[1],h=d.order[2];g.position[e]=d[e].step>0?a:d[e].space_length-a,g.position[f]=d[f].step>0?b:d[f].space_length-b,g.position[h]=d[h].step>0?c:d[h].space_length-c},getWorldCoords:function(){var a=g.getVoxelCoords();return g.voxelToWorld(a.i,a.j,a.k)},setWorldCoords:function(a,b,c){var d=g.worldToVoxel(a,b,c);g.setVoxelCoords(d.i,d.j,d.k)},voxelToWorld:function(a,b,c){var d,e,f,h={},i=g.header;h[i.order[0]]=a,h[i.order[1]]=b,h[i.order[2]]=c,d=h.xspace,e=h.yspace,f=h.zspace;var j=i.xspace.direction_cosines,k=i.yspace.direction_cosines,l=i.zspace.direction_cosines,m=i.xspace.step,n=i.yspace.step,o=i.zspace.step,p=i.voxel_origin;return{x:d*j[0]*m+e*k[0]*n+f*l[0]*o+p.x,y:d*j[1]*m+e*k[1]*n+f*l[1]*o+p.y,z:d*j[2]*m+e*k[2]*n+f*l[2]*o+p.z}},worldToVoxel:function(b,c,d){var e=a.w2v,f={vx:b*e[0][0]+c*e[0][1]+d*e[0][2]+e[0][3],vy:b*e[1][0]+c*e[1][1]+d*e[1][2]+e[1][3],vz:b*e[2][0]+c*e[2][1]+d*e[2][2]+e[2][3]},g={};return g[a.order[0]]=Math.round(f.vx),g[a.order[1]]=Math.round(f.vy),g[a.order[2]]=Math.round(f.vz),{i:g.xspace,j:g.yspace,k:g.zspace}},getVoxelMin:function(){return g.header.voxel_min},getVoxelMax:function(){return g.header.voxel_max},getPreferredZoom:function(a,b){var c=g.header,d=c.xspace.space_length*Math.abs(c.xspace.step),e=c.yspace.space_length*Math.abs(c.yspace.step),f=c.zspace.space_length*Math.abs(c.xspace.step),h=a/d,i=a/e,j=b/e,k=b/f;return Math.min(i,h,k,j)}};return g},e.utils.scanDataRange=function(a,b){var c=0,d=+(1/0),e=-(1/0);for(c=0;c<a.length;c++){var f=a[c];f>e&&(e=f),d>f&&(d=f)}b.voxel_min=d,b.voxel_max=e}}(),function(){"use strict";function a(a){if(a>=c.BYTE&&a<d.length)return d[a];throw"Unknown type "+a}function b(a,b,c){var d=c.find(function(b){return a.name===b.name});if(d){var e=new Int32Array(new ArrayBuffer(4));e[0]=d.length,a.attributes.length=e}var f,g,h="";for(f=0;f<b.length;f++)g=b[f],h.length>0&&(h+=","),h+=c[g].name;h.length>0&&(a.attributes.dimorder=h)}var c={BYTE:1,CHAR:2,SHORT:3,INT:4,FLOAT:5,DOUBLE:6},d=[0,1,1,2,4,4,8],e=BrainBrowser.VolumeViewer;e.utils.netcdfReader=function(d,f){function g(){return{begin:0,vsize:0,name:"",attributes:{},children:[],array:void 0,type:-1,dims:[],nelem:0}}function h(){var a=x.getUint8(v);return v+=1,a}function i(){var a=x.getUint32(v,w);return v+=4,a}function j(a){var b,c,d="";for(b=0;a>b;b+=1){if(c=h(),0===c){v+=a-b-1;break}d+=String.fromCharCode(c)}return d}function k(a){return 4*Math.floor((a+3)/4)}function l(b,f,g,h){var i,j;if(h=h||v,b===c.CHAR)i=new Int8Array(d,h,f);else if(b===c.BYTE)i=new Uint8Array(d,h,f);else if(b===c.DOUBLE){var l=new ArrayBuffer(f),m=0;for(i=new Float64Array(l),j=0;f>j;j+=8)i[m]=x.getFloat64(h+j),m+=1}else{var n=f/a(b);if(e.utils.swapn(new Uint8Array(d,h,f),a(b)),b===c.SHORT)i=new Int16Array(d,h,n);else if(b===c.INT)i=new Int32Array(d,h,n);else{if(b!==c.FLOAT)throw"Bad type in getArray "+b;i=new Float32Array(d,h,n)}}return h===v&&(v+=k(f)),i}function m(){var a=j(3);if("CDF"!==a)return!1;var b=h();return 1!==b?!1:!0}function n(a,b,c){var d,e=i(),f=i();if(e===a)for(d=0;f>d;d++)b(c);else if(0!==e||0!==f)throw new Error("Protocol error.")}function o(){var a=i(),b=j(k(a)),c=i();y.push({name:b,length:c})}function p(){n(10,o)}function q(b){var d=i(),e=j(k(d)),f=i(),g=i(),h=l(f,a(f)*g,[]);if(f===c.CHAR){var m,n="";for(m=0;m<h.length;m++){var o=h[m];if(0===o)break;n+=String.fromCharCode(o)}h=n}b.attributes[e]=h}function r(a){n(12,q,a)}function s(d){var e,f,h=i(),m=j(k(h)),n=i(),o=[],p=[],q=g(),s=!1;for(e=0;n>e;e++){if(f=i(),0>f||f>=y.length)throw new Error("Illegal dimension id: "+f);p.push(f),o.push(y[f].length),0===o[e]&&(s=!0)}r(q);var t=i(),u=i(),v=i();for(q.name=m,q.type=t,q.dims=o,q.dimids=p,q.vsize=u,q.begin=v,q.nelem=1,e=0;e<o.length;e++)0!==o[e]&&(q.nelem*=o[e]);if(q.nelem*=a(t),d.children.push(q),s){var w=new ArrayBuffer(q.nelem*A);switch(B.push(q),t){case c.BYTE:q.array=new Uint8Array(w);break;case c.CHAR:q.array=new Int8Array(w);break;case c.SHORT:q.array=new Int16Array(w);break;case c.INT:q.array=new Int32Array(w);break;case c.FLOAT:q.array=new Float32Array(w);break;case c.DOUBLE:q.array=new Float64Array(w);break;default:throw new Error("Unknown type: "+t)}}else v+q.nelem<=x.byteLength&&(q.array=l(t,q.nelem,o,v));b(q,p,y)}function t(a){n(11,s,a)}function u(){var a,b,c,d,e,g=0;for(a=0;a<B.length;a++)e=B[a],g+=e.vsize;for(a=0;a<B.length;a++){e=B[a],f&&console.log(e.name+" "+e.nelem+" "+e.vsize+" "+A),c=e.begin,d=0;var h;for(b=0;A>b;b++)h=l(e.type,e.nelem,[],c),e.array.set(h,d),c+=g,d+=h.length}}var v=0,w=!1,x=new DataView(d),y=[];if(f=f||!1,!m())throw new Error("Sorry, this does not look like a NetCDF file.");var z=g(),A=i(),B=[];return p(),r(z),t(z),u(),z.dimensions=y,z.numrec=A,z}}(),function(){"use strict";function a(a,c){var d,f={order:[],xspace:{},yspace:{},zspace:{}},g=new DataView(a,0,348),h=new Uint8Array(a,0,348),i=!0,j=g.getUint32(0,!0);348===j?i=!0:1543569408===j?i=!1:d="This does not look like a NIfTI-1 file.";var k=g.getUint16(40,i);(3>k||k>4)&&(d="Cannot handle "+k+"-dimensional images yet.");var l=String.fromCharCode.apply(null,h.subarray(344,348));if("n+1\x00"!==l&&(d="Bad magic number: '"+l+"'"),void 0!==d)throw BrainBrowser.events.triggerEvent("error",{message:d}),new Error(d);var m=g.getUint16(48,i),n=g.getUint16(70,i),o=g.getUint16(72,i),p=g.getFloat32(80,i),q=g.getFloat32(84,i),r=g.getFloat32(88,i),s=g.getFloat32(92,i),t=g.getFloat32(108,i);352>t&&(t=352);var u=g.getFloat32(112,i),v=g.getFloat32(116,i),w=g.getUint16(252,i),x=g.getUint16(254,i),y=[[1,0,0,0],[0,1,0,0],[0,0,1,0],[0,0,0,1]];if(m>=1&&(f.time={},f.time.space_length=m,f.time.step=s,f.time.start=0,f.time.name="time"),f.bytes_per_voxel=o/8,f.must_swap_data=!i&&f.bytes_per_voxel>1,x>0)y[0][0]=g.getFloat32(280,i),y[0][1]=g.getFloat32(284,i),y[0][2]=g.getFloat32(288,i),y[0][3]=g.getFloat32(292,i),y[1][0]=g.getFloat32(296,i),y[1][1]=g.getFloat32(300,i),y[1][2]=g.getFloat32(304,i),y[1][3]=g.getFloat32(308,i),y[2][0]=g.getFloat32(312,i),y[2][1]=g.getFloat32(316,i),y[2][2]=g.getFloat32(320,i),y[2][3]=g.getFloat32(324,i);else if(w>0){var z=g.getFloat32(256,i),A=g.getFloat32(260,i),B=g.getFloat32(264,i),C=g.getFloat32(268,i),D=g.getFloat32(272,i),E=g.getFloat32(276,i),F=g.getFloat32(76,i)<0?-1:1;y=b(z,A,B,C,D,E,p,q,r,F)}else y[0][0]=p,y[1][1]=q,y[2][2]=r;var G,H,I=[0,1,2],J=[[0,0,0,0],[0,0,0,0],[0,0,0,0],[0,0,0,1]];for(G=0;3>G;G++){var K=Math.abs(y[0][G]),L=Math.abs(y[1][G]),M=Math.abs(y[2][G]);K>L&&K>M?(f.order[2-G]="xspace",I[G]=0):L>K&&L>M?(f.order[2-G]="yspace",I[G]=1):(f.order[2-G]="zspace",I[G]=2)}for(G=0;3>G;G++)for(H=0;4>H;H++){var N=H;3>H&&(N=I[H]),J[G][N]=y[G][H]}e.utils.transformToMinc(J,f),f[f.order[2]].space_length=g.getUint16(42,i),f[f.order[1]].space_length=g.getUint16(44,i),f[f.order[0]].space_length=g.getUint16(46,i),m>=1&&f.order.unshift("time"),f.datatype=n,f.vox_offset=t,f.scl_slope=u,f.scl_inter=v,BrainBrowser.utils.isFunction(c)&&c(f)}function b(a,b,c,d,e,f,g,h,i,j){var k,l,m,n,o=[[0,0,0,0],[0,0,0,0],[0,0,0,0],[0,0,0,1]],p=a,q=b,r=c;return k=1-(p*p+q*q+r*r),1e-7>k?(k=1/Math.sqrt(p*p+q*q+r*r),p*=k,q*=k,r*=k,k=0):k=Math.sqrt(k),l=g>0?g:1,m=h>0?h:1,n=i>0?i:1,0>j&&(n=-n),o[0][0]=(k*k+p*p-q*q-r*r)*l,o[0][1]=2*(p*q-k*r)*m,o[0][2]=2*(p*r+k*q)*n,o[1][0]=2*(p*q+k*r)*l,o[1][1]=(k*k+q*q-p*p-r*r)*m,o[1][2]=2*(q*r-k*p)*n,o[2][0]=2*(p*r-k*q)*l,o[2][1]=2*(q*r+k*p)*m,o[2][2]=(k*k+r*r-q*q-p*p)*n,o[0][3]=d,o[1][3]=e,o[2][3]=f,o}function c(a,b,c){var f=e.createVolume(a,d(a,b));f.type="nifti",f.intensity_min=f.header.voxel_min,f.intensity_max=f.header.voxel_max,f.saveOriginAndTransform(a),BrainBrowser.utils.isFunction(c)&&c(f)}function d(a,b){var c=null;switch(a.must_swap_data&&e.utils.swapn(new Uint8Array(b,a.vox_offset),a.bytes_per_voxel),a.datatype){case 2:c=new Uint8Array(b,a.vox_offset);break;case 4:c=new Int16Array(b,a.vox_offset);break;case 8:c=new Int32Array(b,a.vox_offset);break;case 16:c=new Float32Array(b,a.vox_offset);break;case 64:c=new Float64Array(b,a.vox_offset);break;case 256:c=new Int8Array(b,a.vox_offset);break;case 512:c=new Uint16Array(b,a.vox_offset);break;case 768:c=new Uint32Array(b,a.vox_offset);break;default:var d="Unsupported data type: "+a.datatype;throw BrainBrowser.events.triggerEvent("error",{message:d}),new Error(d)}var f=0,g=a.scl_slope,h=a.scl_inter;if(0!==g){var i=new Float32Array(c.length);for(f=0;f<c.length;f++)i[f]=c[f]*g+h;c=i}return e.utils.scanDataRange(c,a),4===a.order.length&&(a.order=a.order.slice(1)),a[a.order[0]].offset=a[a.order[1]].space_length*a[a.order[2]].space_length,a[a.order[1]].offset=a[a.order[2]].space_length,a[a.order[2]].offset=1,a.time&&(a.time.offset=a.xspace.space_length*a.yspace.space_length*a.zspace.space_length),c}var e=BrainBrowser.VolumeViewer;e.volume_loaders.nifti1=function(b,d){var e;if(b.nii_url)BrainBrowser.loader.loadFromURL(b.nii_url,function(b){a(b,function(a){c(a,b,d)})},{result_type:"arraybuffer"});else if(b.nii_file)BrainBrowser.loader.loadFromFile(b.nii_file,function(b){a(b,function(a){c(a,b,d)})},{result_type:"arraybuffer"});else{if(!b.nii_source)throw e="invalid volume description.\nDescription must contain the property 'nii_url' or 'nii_file' or 'nii_source'.",BrainBrowser.events.triggerEvent("error",{message:e}),new Error(e);a(b.nii_source,function(a){c(a,b.nii_source,d)})}},e.utils.transformToMinc=function(a,b){function c(a){var b=a[0]*a[0]+a[1]*a[1]+a[2]*a[2];return 0>=b&&(b=1),Math.sqrt(b)}function d(a,b,c){return a[0]*(b[1]*c[2]-b[2]*c[1])-a[1]*(b[0]*c[2]-b[2]*c[0])+a[2]*(b[0]*c[1]-b[1]*c[0])}for(var e=[],f=[],g=[],h=c(a[0]),i=c(a[1]),j=c(a[2]),k=a[0][0]<0?-h:h,l=a[1][1]<0?-i:i,m=a[2][2]<0?-j:j,n=0;3>n;n++)e[n]=a[n][0]/k,f[n]=a[n][1]/l,g[n]=a[n][2]/m;b.xspace.step=k,b.yspace.step=l,b.zspace.step=m;var o=[a[0][3],a[1][3],a[2][3]],p=d(e,f,g),q=d(o,f,g),r=d(e,o,g),s=d(e,f,o);b.xspace.start=q/p,b.yspace.start=r/p,b.zspace.start=s/p,b.xspace.direction_cosines=e,b.yspace.direction_cosines=f,b.zspace.direction_cosines=g},e.utils.swapn=function(a,b){for(var c=0;c<a.length;c+=b)for(var d=b-1,e=0;d>e;){var f=a[c+d];a[c+d]=a[c+e],a[c+e]=f,d--,e++}}}(),function(){"use strict";function a(a,b,c){var d=a.length;if(1===d)return a[0];var e,f,g,h,i,j,k,l,m,n=c.data,o=c.width,p=c.height,q=new Uint32Array(a.length),r=new Float32Array(b);for(e=0;p>e;e+=1)for(m=e*o,f=0;o>f;f+=1)for(j=4*(m+f),k=0,g=0;d>g;g+=1)h=a[g],e<h.height&&f<h.width&&(i=h.data,l=q[g],n[j]=n[j]*k+i[l]*r[g],n[j+1]=n[j+1]*k+i[l+1]*r[g],n[j+2]=n[j+2]*k+i[l+2]*r[g],n[j+3]=255,k+=r[g],q[g]+=4);return c}var b=BrainBrowser.VolumeViewer,c=document.createElement("canvas").getContext("2d");b.volume_loaders.overlay=function(d,e){d=d||{};var f=d.volumes||[],g={},h=256,i=["xspace","yspace","zspace"];g.order=i;for(var j=0;j<i.length;j++)g[i[j]]={},g[i[j]].step=1,g[i[j]].start=-h/2,g[i[j]].direction_cosines=[0,0,0],g[i[j]].direction_cosines[j]=1,g[i[j]].space_length=h;var k=b.createVolume(g,[]);k.type="overlay",k.size=h,k.volumes=[],k.blend_ratios=[],k.saveOriginAndTransform(g),k.slice=function(a,b,c){b=void 0===b?this.position[a]:b,c=void 0===c?this.current_time:c;var d=[];return this.volumes.forEach(function(e){var g=e.header[a].step/f[0].header[a].step,h=Math.round(b/g),i=e.slice(a,h,c);d.push(i)}),{height_space:g[a].height_space,width_space:g[a].width_space,slices:d}},k.getSliceImage=function(b,d,e,f){function g(a,b,c){var d=b.charCodeAt(0)-"x".charCodeAt(0),e={xspace:a.w2v[0][d],yspace:a.w2v[1][d],zspace:a.w2v[2][d]};return{di:e[a.order[0]]/c,dj:e[a.order[1]]/c,dk:e[a.order[2]]/c}}d=d||1;var h=b.slices,i=[],j=Math.round(this.size*d),l=j;return h.forEach(function(a,b){var h,m=k.volumes[b],n=m.color_map,o=m.intensity_min,p=m.intensity_max;if(!n)throw h="No color map set for this volume. Cannot render slice.",this.triggerEvent("error",{message:h}),new Error(h);var q=c.createImageData(j,l),r=-j/2,s=j/2,t=-l/2,u=l/2,v=m.header,w=v.time?m.current_time*v.time.offset:0,x=v[a.axis],y=x.width_space,z=x.height_space,A=v[v.order[0]].offset,B=v[v.order[1]].offset,C=v[v.order[2]].offset,D=v[v.order[0]].space_length,E=v[v.order[1]].space_length,F=v[v.order[2]].space_length,G=j*l,H=new m.data.constructor(G),I=0,J=k.getWorldCoords();J[y.name[0]]=r/d,J[z.name[0]]=u/d;for(var K=m.worldToVoxel(J.x,J.y,J.z),L=g(v,y.name,d),M=g(v,z.name,d),N={i:K.i,j:K.j,k:K.k},O=u-1;O>=t;O--){for(var P={i:N.i,j:N.j,k:N.k},Q=r;s>Q;Q++){if(P.i<0||P.i>=D||P.j<0||P.j>=E||P.k<0||P.k>=F)H[I]=0;else{var R=w+Math.round(P.i)*A+Math.round(P.j)*B+Math.round(P.k)*C;G>I&&(H[I]=m.data[R])}I++,P.i+=L.di,P.j+=L.dj,P.k+=L.dk}N.i-=M.di,N.j-=M.dj,N.k-=M.dk}n.mapColors(H,{min:o,max:p,contrast:e,brightness:f,destination:q.data}),i.push(q)}),a(i,k.blend_ratios,c.createImageData(j,l))},k.getIntensityValue=function(a,b,c,d){var e=k.getVoxelCoords();a=void 0===a?e.i:a,b=void 0===b?e.j:b,c=void 0===c?e.k:c,d=void 0===d?this.current_time:d;var f=[],g=k.voxelToWorld(a,b,c);return this.volumes.forEach(function(a){var b=a.worldToVoxel(g.x,g.y,g.z);f.push(a.getIntensityValue(b.i,b.j,b.k,d))}),f.reduce(function(a,b,c){return a+b*k.blend_ratios[c]},0)},f.forEach(function(a){k.volumes.push(a),k.blend_ratios.push(1/f.length)}),BrainBrowser.utils.isFunction(e)&&e(k)}}();