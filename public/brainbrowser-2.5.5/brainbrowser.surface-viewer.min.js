/*
* BrainBrowser: Web-based Neurological Visualization Tools
* (https://brainbrowser.cbrain.mcgill.ca)
*
* Copyright (C) 2011
* The Royal Institution for the Advancement of Learning
* McGill University
*
* This program is free software: you can redistribute it and/or modify
* it under the terms of the GNU Affero General Public License as
* published by the Free Software Foundation, either version 3 of the
* License, or (at your option) any later version.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU Affero General Public License for more details.
*
* You should have received a copy of the GNU Affero General Public License
* along with this program.  If not, see <http://www.gnu.org/licenses/>.
*/

/*
* BrainBrowser v2.5.5
*
* Author: <PERSON><PERSON><PERSON>  <<EMAIL>> (http://tareksherif.ca/)
* Author: <PERSON>
* Author: <PERSON>
*
* three.js (c) 2010-2014 three.js authors, used under the MIT license
*/
!function(){"use strict";function a(a){var b=!1,c=!1,d=!1,e=!1,f=document.createElement("canvas"),g=null;b=!!f,c=!!window.Worker;try{f&&window.WebGLRenderingContext&&(g=f.getContext("webgl")||f.getContext("experimental-webgl")),d=!!g}catch(h){d=!1}d&&(e=!!g.getExtension("OES_element_index_uint")),a.CANVAS_ENABLED=b,a.WEB_WORKERS_ENABLED=c,a.WEBGL_ENABLED=d,a.WEBGL_UINT_INDEX_ENABLED=e}var b="2.5.5";b=b.indexOf("BRAINBROWSER_VERSION")>0?"D.E.V":b;var c=window.BrainBrowser={version:b};a(c),window.requestAnimationFrame=window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.oRequestAnimationFrame||window.msRequestAnimationFrame||function(a){return window.setTimeout(a,1e3/60)},window.cancelAnimationFrame=window.cancelAnimationFrame||function(a){window.clearTimeout(a)}}(),function(){"use strict";function a(b,c,d,e){return c>d?e(b):void Object.keys(b).forEach(function(f){a(b[f],c+1,d,e)})}BrainBrowser.createTreeStore=function(){var b={};return{set:function(){var a,c,d,e,f=arguments[arguments.length-1],g=Array.prototype.slice.call(arguments,0,arguments.length-1),h=b;for(c=0,d=g.length-1;d>c;c++){if(a=g[c],h[a]&&"object"!=typeof h[a])throw e="Hash key '["+g.slice(0,c+1).join("][")+"]' has already been set to a non-object value.\nCannot set '["+g.join("][")+"]'",BrainBrowser.events.triggerEvent("error",{message:e}),new Error(e);h[a]||(h[a]={}),h=h[a]}a=g[c],h[a]=f},get:function(){var a,c,d,e=Array.prototype.slice.call(arguments),f=b;if(0===e.length)return b;for(c=0,d=e.length-1;d>c;c++){if(a=e[c],void 0===f[a])return null;f=f[a]}return a=e[c],void 0!==f[a]?f[a]:null},remove:function(){var a,c,d,e,f=Array.prototype.slice.call(arguments),g=b;for(c=0,d=f.length-1;d>c;c++){if(a=f[c],void 0===g[a])return null;g=g[a]}return a=f[c],e=g[a],g[a]=void 0,e},reset:function(a){a=a&&"object"==typeof a?a:{},b=a},forEach:function(c,d){c=c>0?c:1,a(b,1,c,d)}}}}(),function(){"use strict";BrainBrowser.createColorMap=function(a,b){function c(a,b,c,d,e,f,g){var h;return(b>a||a>c)&&!e?-1:(h=Math.floor(Math.max(0,Math.min((a-b)*d,g-1))),f&&(h=g-1-h),h*=4)}function d(a,b,c){var d,e,f,g=document.createElement("canvas"),h=new Array(256);for(g.width=256,g.height=c,d=0;256>d;d++)h[d]=d;for(f=r.scale,r.scale=255,a=r.mapColors(h),r.scale=f,e=g.getContext("2d"),d=0;256>d;d++)e.fillStyle="rgb("+Math.floor(a[4*d])+", "+Math.floor(a[4*d+1])+", "+Math.floor(a[4*d+2])+")",e.fillRect(d,0,1,b);return g}b=b||{};var e,f,g,h,i,j,k,l,m=void 0===b.clamp?!0:b.clamp,n=b.flip||!1,o=b.scale||1,p=b.contrast||1,q=b.brightness||0;if(a)for(f=a.trim().split(/\n/),e=[],k=0,i=0,g=f.length;g>i;i++)if(l=f[i].trim().split(/\s+/).slice(0,5),h=l.length,!(3>h)){for(h>4&&(k=parseInt(l[0],10),k*=4,h=4,l=l.slice(1,5)),j=0;h>j;j++)e[k+j]=parseFloat(l[j]);4>h&&(e[k+3]=1),k+=4}var r={colors:e,clamp:m,flip:n,scale:o,contrast:p,brightness:q,mapColors:function(a,b){b=b||{};var d,e,f,g,h,i,j=void 0===b.min?0:b.min,k=void 0===b.max?255:b.max,l=b.default_colors||[0,0,0,1],m=b.destination||new Float32Array(4*a.length),n=r.colors,o=r.colors.length/4,p=void 0===b.scale?r.scale:b.scale,q=void 0===b.clamp?r.clamp:b.clamp,s=void 0===b.flip?r.flip:b.flip,t=void 0===b.brightness?r.brightness:b.brightness,u=void 0===b.contrast?r.contrast:b.contrast,v=4===l.length?0:1,w=k-j,x=o/w;for(t*=p,u*=p,e=0,h=a.length;h>e;e++)d=a[e],f=4*e,i=c(d,j,k,x,q,s,o),0>i?(g=f*v,m[f]=u*l[g]+t,m[f+1]=u*l[g+1]+t,m[f+2]=u*l[g+2]+t,m[f+3]=p*l[g+3]):(m[f]=u*n[i]+t,m[f+1]=u*n[i+1]+t,m[f+2]=u*n[i+2]+t,m[f+3]=p*n[i+3]);return m},colorFromValue:function(a,b){b=b||{};var d,e=b.hex||!1,f=void 0===b.min?0:b.min,g=void 0===b.max?255:b.max,h=void 0===b.scale?r.scale:b.scale,i=void 0===b.brightness?r.brightness:b.brightness,j=void 0===b.contrast?r.contrast:b.contrast,k=g-f,l=r.colors.length/4,m=l/k,n=c(a,f,g,m,r.clamp,r.flip,l);return d=n>=0?Array.prototype.slice.call(r.colors,n,n+4):[0,0,0,1],d[0]=Math.max(0,Math.min(j*d[0]+i,1)),d[1]=Math.max(0,Math.min(j*d[1]+i,1)),d[2]=Math.max(0,Math.min(j*d[2]+i,1)),e?(d[0]=Math.floor(255*d[0]),d[1]=Math.floor(255*d[1]),d[2]=Math.floor(255*d[2]),d[3]=Math.floor(255*d[3]),d[0]=("0"+d[0].toString(16)).slice(-2),d[1]=("0"+d[1].toString(16)).slice(-2),d[2]=("0"+d[2].toString(16)).slice(-2),d=d.slice(0,3).join("")):(d[0]=d[0]*h,d[1]=d[1]*h,d[2]=d[2]*h,d[3]=d[3]*h),d},createElement:function(a,b){var c,e,f=r.colors,g=b-a;return c=d(f,20,40,n),e=c.getContext("2d"),e.fillStyle="#FFA000",e.fillRect(.5,20,1,10),e.fillText(a.toPrecision(3),.5,40),e.fillRect(c.width/4,20,1,10),e.fillText((a+.25*g).toPrecision(3),.25*c.width,40),e.fillRect(c.width/2,20,1,10),e.fillText((a+.5*g).toPrecision(3),.5*c.width,40),e.fillRect(3*c.width/4,20,1,10),e.fillText((a+.75*g).toPrecision(3),.75*c.width,40),e.fillRect(c.width-.5,20,1,10),e.fillText(b.toPrecision(3),c.width-20,40),c}};return r}}(),function(){"use strict";var a=BrainBrowser.createTreeStore();BrainBrowser.config={set:function(b,c){b=b||"";var d=b.split(".");d.push(c),a.set.apply(a,d)},get:function(b){b=b||"";var c=b.split(".");return a.get.apply(a,c)}}}(),function(){"use strict";function a(a,b){try{a.call(b.target,b)}catch(c){console.error("Error in event handler for: ",b.name),console.error(c.stack||c.message||c)}}var b=["eventmodelcleanup"];BrainBrowser.events={unpropagatedEvent:function(a){b.push(a)},addEventModel:function(c){var d=[],e={};c.addEventListener=function(a,b){d[a]||(d[a]=[]),d[a].push(b)},c.triggerEvent=function(e,f){var g=this,h=c.directPropagationTargets(e);f=f||{},f.name=e,f.target=g,d[e]&&d[e].forEach(function(b){a(b,f)}),d["*"]&&d["*"].forEach(function(b){a(b,f)}),-1===b.indexOf(e)&&(h.forEach(function(a){a.triggerEvent.call(g,e,f)}),0===h.length&&c!==BrainBrowser.events&&BrainBrowser.events.triggerEvent.call(g,e,f))},c.propagateEventTo=function(a,b){if(!BrainBrowser.utils.isFunction(b.allPropagationTargets))throw new Error("Propagation target doesn't seem to have an event model.");if(c===BrainBrowser.events||-1!==b.allPropagationTargets(a).indexOf(c))throw new Error("Propagating event '"+a+"' would cause a cycle.");e[a]=e[a]||[],-1===c.directPropagationTargets().indexOf(b)&&b.addEventListener("eventmodelcleanup",function(){this===b&&c.stopPropagatingTo(b)}),-1===e[a].indexOf(b)&&e[a].push(b)},c.propagateEventFrom=function(a,b){b.propagateEventTo(a,c)},c.stopPropagatingTo=function(a){Object.keys(e).forEach(function(b){e[b]=e[b].filter(function(b){return b!==a})})},c.directPropagationTargets=function(a){var b=[],c=void 0===a?Object.keys(e):[a,"*"];return c.forEach(function(a){var c=e[a]||[];c.forEach(function(a){-1===b.indexOf(a)&&b.push(a)})}),b},c.allPropagationTargets=function(a){var b=c.directPropagationTargets(a),d=Array.prototype.slice.call(b);return b.forEach(function(b){b.allPropagationTargets(a).forEach(function(a){-1===d.indexOf(a)&&d.push(a)})}),d}}},BrainBrowser.events.addEventModel(BrainBrowser.events)}(),function(){"use strict";var a=BrainBrowser.loader={loadFromURL:function(b,c,d){d=d||{};var e,f=new XMLHttpRequest,g=d.result_type,h=b.split("/"),i=h[h.length-1];f.open("GET",b),"arraybuffer"===g&&(f.responseType="arraybuffer"),f.onreadystatechange=function(){if(4===f.readyState){if(e=f.status,!(e>=200&&300>e||304===e)){var g="error loading URL: "+b+"\nHTTP Response: "+f.status+"\nHTTP Status: "+f.statusText+"\nResponse was: \n"+f.response;throw BrainBrowser.events.triggerEvent("error",{message:g}),new Error(g)}a.checkCancel(d)||c(f.response,i,d)}},f.send()},loadFromFile:function(a,b,c){var d=a.files;if(0!==d.length){c=c||{};var e=c.result_type,f=new FileReader,g=a.value.split("\\"),h=g[g.length-1];f.file=d[0],f.onloadend=function(a){var d=a.target.result;try{var f=pako.inflate(d);d=f.buffer}catch(g){}finally{if("arraybuffer"!==e)if("function"!=typeof TextDecoder){var i=new Blob([d]),j=new FileReader;j.onload=function(a){b(a.target.result,h,c)},j.readAsText(i)}else{var k=new DataView(d),l=new TextDecoder;d=l.decode(k),b(d,h,c)}else b(d,h,c)}},f.onerror=function(){var a="error reading file: "+h;throw BrainBrowser.events.triggerEvent("error",{message:a}),new Error(a)},f.readAsArrayBuffer(d[0])}},loadColorMapFromURL:function(b,c,d){a.loadFromURL(b,function(a,b,d){c(BrainBrowser.createColorMap(a,d),b,d)},d)},loadColorMapFromFile:function(b,c,d){a.loadFromFile(b,function(a,b,d){c(BrainBrowser.createColorMap(a,d),b,d)},d)},checkCancel:function(a){a=a||{},BrainBrowser.utils.isFunction(a)&&(a={test:a});var b=a.test,c=a.cleanup,d=!1;return b&&b()&&(d=!0,c&&c()),d}}}(),function(a){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=a();else if("function"==typeof define&&define.amd)define([],a);else{var b;b="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this,b.pako=a()}}(function(){return function a(b,c,d){function e(g,h){if(!c[g]){if(!b[g]){var i="function"==typeof require&&require;if(!h&&i)return i(g,!0);if(f)return f(g,!0);var j=new Error("Cannot find module '"+g+"'");throw j.code="MODULE_NOT_FOUND",j}var k=c[g]={exports:{}};b[g][0].call(k.exports,function(a){var c=b[g][1][a];return e(c?c:a)},k,k.exports,a,b,c,d)}return c[g].exports}for(var f="function"==typeof require&&require,g=0;g<d.length;g++)e(d[g]);return e}({1:[function(a,b,c){"use strict";function d(a){if(!(this instanceof d))return new d(a);this.options=i.assign({level:s,method:u,chunkSize:16384,windowBits:15,memLevel:8,strategy:t,to:""},a||{});var b=this.options;b.raw&&b.windowBits>0?b.windowBits=-b.windowBits:b.gzip&&b.windowBits>0&&b.windowBits<16&&(b.windowBits+=16),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new l,this.strm.avail_out=0;var c=h.deflateInit2(this.strm,b.level,b.method,b.windowBits,b.memLevel,b.strategy);if(c!==p)throw new Error(k[c]);if(b.header&&h.deflateSetHeader(this.strm,b.header),b.dictionary){var e;if(e="string"==typeof b.dictionary?j.string2buf(b.dictionary):"[object ArrayBuffer]"===m.call(b.dictionary)?new Uint8Array(b.dictionary):b.dictionary,c=h.deflateSetDictionary(this.strm,e),c!==p)throw new Error(k[c]);this._dict_set=!0}}function e(a,b){var c=new d(b);if(c.push(a,!0),c.err)throw c.msg;return c.result}function f(a,b){return b=b||{},b.raw=!0,e(a,b)}function g(a,b){return b=b||{},b.gzip=!0,e(a,b)}var h=a("./zlib/deflate"),i=a("./utils/common"),j=a("./utils/strings"),k=a("./zlib/messages"),l=a("./zlib/zstream"),m=Object.prototype.toString,n=0,o=4,p=0,q=1,r=2,s=-1,t=0,u=8;d.prototype.push=function(a,b){var c,d,e=this.strm,f=this.options.chunkSize;if(this.ended)return!1;d=b===~~b?b:b===!0?o:n,"string"==typeof a?e.input=j.string2buf(a):"[object ArrayBuffer]"===m.call(a)?e.input=new Uint8Array(a):e.input=a,e.next_in=0,e.avail_in=e.input.length;do{if(0===e.avail_out&&(e.output=new i.Buf8(f),e.next_out=0,e.avail_out=f),c=h.deflate(e,d),c!==q&&c!==p)return this.onEnd(c),this.ended=!0,!1;(0===e.avail_out||0===e.avail_in&&(d===o||d===r))&&("string"===this.options.to?this.onData(j.buf2binstring(i.shrinkBuf(e.output,e.next_out))):this.onData(i.shrinkBuf(e.output,e.next_out)))}while((e.avail_in>0||0===e.avail_out)&&c!==q);return d===o?(c=h.deflateEnd(this.strm),this.onEnd(c),this.ended=!0,c===p):d===r?(this.onEnd(p),e.avail_out=0,!0):!0},d.prototype.onData=function(a){this.chunks.push(a)},d.prototype.onEnd=function(a){a===p&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=i.flattenChunks(this.chunks)),this.chunks=[],this.err=a,this.msg=this.strm.msg},c.Deflate=d,c.deflate=e,c.deflateRaw=f,c.gzip=g},{"./utils/common":3,"./utils/strings":4,"./zlib/deflate":8,"./zlib/messages":13,"./zlib/zstream":15}],2:[function(a,b,c){"use strict";function d(a){if(!(this instanceof d))return new d(a);this.options=h.assign({chunkSize:16384,windowBits:0,to:""},a||{});var b=this.options;b.raw&&b.windowBits>=0&&b.windowBits<16&&(b.windowBits=-b.windowBits,0===b.windowBits&&(b.windowBits=-15)),!(b.windowBits>=0&&b.windowBits<16)||a&&a.windowBits||(b.windowBits+=32),b.windowBits>15&&b.windowBits<48&&0===(15&b.windowBits)&&(b.windowBits|=15),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new l,this.strm.avail_out=0;var c=g.inflateInit2(this.strm,b.windowBits);if(c!==j.Z_OK)throw new Error(k[c]);this.header=new m,g.inflateGetHeader(this.strm,this.header)}function e(a,b){var c=new d(b);if(c.push(a,!0),c.err)throw c.msg;return c.result}function f(a,b){return b=b||{},b.raw=!0,e(a,b)}var g=a("./zlib/inflate"),h=a("./utils/common"),i=a("./utils/strings"),j=a("./zlib/constants"),k=a("./zlib/messages"),l=a("./zlib/zstream"),m=a("./zlib/gzheader"),n=Object.prototype.toString;d.prototype.push=function(a,b){var c,d,e,f,k,l,m=this.strm,o=this.options.chunkSize,p=this.options.dictionary,q=!1;if(this.ended)return!1;d=b===~~b?b:b===!0?j.Z_FINISH:j.Z_NO_FLUSH,"string"==typeof a?m.input=i.binstring2buf(a):"[object ArrayBuffer]"===n.call(a)?m.input=new Uint8Array(a):m.input=a,m.next_in=0,m.avail_in=m.input.length;do{if(0===m.avail_out&&(m.output=new h.Buf8(o),m.next_out=0,m.avail_out=o),c=g.inflate(m,j.Z_NO_FLUSH),c===j.Z_NEED_DICT&&p&&(l="string"==typeof p?i.string2buf(p):"[object ArrayBuffer]"===n.call(p)?new Uint8Array(p):p,c=g.inflateSetDictionary(this.strm,l)),c===j.Z_BUF_ERROR&&q===!0&&(c=j.Z_OK,q=!1),c!==j.Z_STREAM_END&&c!==j.Z_OK)return this.onEnd(c),this.ended=!0,!1;m.next_out&&(0===m.avail_out||c===j.Z_STREAM_END||0===m.avail_in&&(d===j.Z_FINISH||d===j.Z_SYNC_FLUSH))&&("string"===this.options.to?(e=i.utf8border(m.output,m.next_out),f=m.next_out-e,k=i.buf2string(m.output,e),m.next_out=f,m.avail_out=o-f,f&&h.arraySet(m.output,m.output,e,f,0),this.onData(k)):this.onData(h.shrinkBuf(m.output,m.next_out))),0===m.avail_in&&0===m.avail_out&&(q=!0)}while((m.avail_in>0||0===m.avail_out)&&c!==j.Z_STREAM_END);return c===j.Z_STREAM_END&&(d=j.Z_FINISH),d===j.Z_FINISH?(c=g.inflateEnd(this.strm),this.onEnd(c),this.ended=!0,c===j.Z_OK):d===j.Z_SYNC_FLUSH?(this.onEnd(j.Z_OK),m.avail_out=0,!0):!0},d.prototype.onData=function(a){this.chunks.push(a)},d.prototype.onEnd=function(a){a===j.Z_OK&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=h.flattenChunks(this.chunks)),this.chunks=[],this.err=a,this.msg=this.strm.msg},c.Inflate=d,c.inflate=e,c.inflateRaw=f,c.ungzip=e},{"./utils/common":3,"./utils/strings":4,"./zlib/constants":6,"./zlib/gzheader":9,"./zlib/inflate":11,"./zlib/messages":13,"./zlib/zstream":15}],3:[function(a,b,c){"use strict";var d="undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint16Array&&"undefined"!=typeof Int32Array;c.assign=function(a){for(var b=Array.prototype.slice.call(arguments,1);b.length;){var c=b.shift();if(c){if("object"!=typeof c)throw new TypeError(c+"must be non-object");for(var d in c)c.hasOwnProperty(d)&&(a[d]=c[d])}}return a},c.shrinkBuf=function(a,b){return a.length===b?a:a.subarray?a.subarray(0,b):(a.length=b,a)};var e={arraySet:function(a,b,c,d,e){if(b.subarray&&a.subarray)return void a.set(b.subarray(c,c+d),e);for(var f=0;d>f;f++)a[e+f]=b[c+f]},flattenChunks:function(a){var b,c,d,e,f,g;for(d=0,b=0,c=a.length;c>b;b++)d+=a[b].length;for(g=new Uint8Array(d),e=0,b=0,c=a.length;c>b;b++)f=a[b],g.set(f,e),e+=f.length;return g}},f={arraySet:function(a,b,c,d,e){for(var f=0;d>f;f++)a[e+f]=b[c+f]},flattenChunks:function(a){return[].concat.apply([],a)}};c.setTyped=function(a){a?(c.Buf8=Uint8Array,c.Buf16=Uint16Array,c.Buf32=Int32Array,c.assign(c,e)):(c.Buf8=Array,c.Buf16=Array,c.Buf32=Array,c.assign(c,f))},c.setTyped(d)},{}],4:[function(a,b,c){"use strict";function d(a,b){if(65537>b&&(a.subarray&&g||!a.subarray&&f))return String.fromCharCode.apply(null,e.shrinkBuf(a,b));for(var c="",d=0;b>d;d++)c+=String.fromCharCode(a[d]);return c}var e=a("./common"),f=!0,g=!0;try{String.fromCharCode.apply(null,[0])}catch(h){f=!1}try{String.fromCharCode.apply(null,new Uint8Array(1))}catch(h){g=!1}for(var i=new e.Buf8(256),j=0;256>j;j++)i[j]=j>=252?6:j>=248?5:j>=240?4:j>=224?3:j>=192?2:1;i[254]=i[254]=1,c.string2buf=function(a){var b,c,d,f,g,h=a.length,i=0;for(f=0;h>f;f++)c=a.charCodeAt(f),55296===(64512&c)&&h>f+1&&(d=a.charCodeAt(f+1),56320===(64512&d)&&(c=65536+(c-55296<<10)+(d-56320),f++)),i+=128>c?1:2048>c?2:65536>c?3:4;for(b=new e.Buf8(i),g=0,f=0;i>g;f++)c=a.charCodeAt(f),55296===(64512&c)&&h>f+1&&(d=a.charCodeAt(f+1),56320===(64512&d)&&(c=65536+(c-55296<<10)+(d-56320),f++)),128>c?b[g++]=c:2048>c?(b[g++]=192|c>>>6,b[g++]=128|63&c):65536>c?(b[g++]=224|c>>>12,b[g++]=128|c>>>6&63,b[g++]=128|63&c):(b[g++]=240|c>>>18,b[g++]=128|c>>>12&63,b[g++]=128|c>>>6&63,b[g++]=128|63&c);return b},c.buf2binstring=function(a){return d(a,a.length)},c.binstring2buf=function(a){for(var b=new e.Buf8(a.length),c=0,d=b.length;d>c;c++)b[c]=a.charCodeAt(c);return b},c.buf2string=function(a,b){var c,e,f,g,h=b||a.length,j=new Array(2*h);for(e=0,c=0;h>c;)if(f=a[c++],128>f)j[e++]=f;else if(g=i[f],g>4)j[e++]=65533,c+=g-1;else{for(f&=2===g?31:3===g?15:7;g>1&&h>c;)f=f<<6|63&a[c++],g--;g>1?j[e++]=65533:65536>f?j[e++]=f:(f-=65536,j[e++]=55296|f>>10&1023,j[e++]=56320|1023&f)}return d(j,e)},c.utf8border=function(a,b){var c;for(b=b||a.length,b>a.length&&(b=a.length),c=b-1;c>=0&&128===(192&a[c]);)c--;return 0>c?b:0===c?b:c+i[a[c]]>b?c:b}},{"./common":3}],5:[function(a,b,c){"use strict";function d(a,b,c,d){for(var e=65535&a|0,f=a>>>16&65535|0,g=0;0!==c;){g=c>2e3?2e3:c,c-=g;do e=e+b[d++]|0,f=f+e|0;while(--g);e%=65521,f%=65521}return e|f<<16|0}b.exports=d},{}],6:[function(a,b,c){"use strict";b.exports={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8}},{}],7:[function(a,b,c){"use strict";function d(){for(var a,b=[],c=0;256>c;c++){a=c;for(var d=0;8>d;d++)a=1&a?3988292384^a>>>1:a>>>1;b[c]=a}return b}function e(a,b,c,d){var e=f,g=d+c;a^=-1;for(var h=d;g>h;h++)a=a>>>8^e[255&(a^b[h])];return-1^a}var f=d();b.exports=e},{}],8:[function(a,b,c){"use strict";function d(a,b){return a.msg=I[b],b}function e(a){return(a<<1)-(a>4?9:0)}function f(a){for(var b=a.length;--b>=0;)a[b]=0}function g(a){var b=a.state,c=b.pending;c>a.avail_out&&(c=a.avail_out),0!==c&&(E.arraySet(a.output,b.pending_buf,b.pending_out,c,a.next_out),a.next_out+=c,b.pending_out+=c,a.total_out+=c,a.avail_out-=c,b.pending-=c,0===b.pending&&(b.pending_out=0))}function h(a,b){F._tr_flush_block(a,a.block_start>=0?a.block_start:-1,a.strstart-a.block_start,b),a.block_start=a.strstart,g(a.strm)}function i(a,b){a.pending_buf[a.pending++]=b}function j(a,b){a.pending_buf[a.pending++]=b>>>8&255,a.pending_buf[a.pending++]=255&b}function k(a,b,c,d){var e=a.avail_in;return e>d&&(e=d),0===e?0:(a.avail_in-=e,E.arraySet(b,a.input,a.next_in,e,c),1===a.state.wrap?a.adler=G(a.adler,b,e,c):2===a.state.wrap&&(a.adler=H(a.adler,b,e,c)),a.next_in+=e,a.total_in+=e,e)}function l(a,b){var c,d,e=a.max_chain_length,f=a.strstart,g=a.prev_length,h=a.nice_match,i=a.strstart>a.w_size-la?a.strstart-(a.w_size-la):0,j=a.window,k=a.w_mask,l=a.prev,m=a.strstart+ka,n=j[f+g-1],o=j[f+g];a.prev_length>=a.good_match&&(e>>=2),h>a.lookahead&&(h=a.lookahead);do if(c=b,j[c+g]===o&&j[c+g-1]===n&&j[c]===j[f]&&j[++c]===j[f+1]){f+=2,c++;do;while(j[++f]===j[++c]&&j[++f]===j[++c]&&j[++f]===j[++c]&&j[++f]===j[++c]&&j[++f]===j[++c]&&j[++f]===j[++c]&&j[++f]===j[++c]&&j[++f]===j[++c]&&m>f);if(d=ka-(m-f),f=m-ka,d>g){if(a.match_start=b,g=d,d>=h)break;n=j[f+g-1],o=j[f+g]}}while((b=l[b&k])>i&&0!==--e);return g<=a.lookahead?g:a.lookahead}function m(a){var b,c,d,e,f,g=a.w_size;do{if(e=a.window_size-a.lookahead-a.strstart,a.strstart>=g+(g-la)){E.arraySet(a.window,a.window,g,g,0),a.match_start-=g,a.strstart-=g,a.block_start-=g,c=a.hash_size,b=c;do d=a.head[--b],a.head[b]=d>=g?d-g:0;while(--c);c=g,b=c;do d=a.prev[--b],a.prev[b]=d>=g?d-g:0;while(--c);e+=g}if(0===a.strm.avail_in)break;if(c=k(a.strm,a.window,a.strstart+a.lookahead,e),a.lookahead+=c,a.lookahead+a.insert>=ja)for(f=a.strstart-a.insert,a.ins_h=a.window[f],a.ins_h=(a.ins_h<<a.hash_shift^a.window[f+1])&a.hash_mask;a.insert&&(a.ins_h=(a.ins_h<<a.hash_shift^a.window[f+ja-1])&a.hash_mask,a.prev[f&a.w_mask]=a.head[a.ins_h],a.head[a.ins_h]=f,f++,a.insert--,!(a.lookahead+a.insert<ja)););}while(a.lookahead<la&&0!==a.strm.avail_in)}function n(a,b){var c=65535;for(c>a.pending_buf_size-5&&(c=a.pending_buf_size-5);;){if(a.lookahead<=1){if(m(a),0===a.lookahead&&b===J)return ua;if(0===a.lookahead)break}a.strstart+=a.lookahead,a.lookahead=0;var d=a.block_start+c;if((0===a.strstart||a.strstart>=d)&&(a.lookahead=a.strstart-d,a.strstart=d,h(a,!1),0===a.strm.avail_out))return ua;if(a.strstart-a.block_start>=a.w_size-la&&(h(a,!1),0===a.strm.avail_out))return ua}return a.insert=0,b===M?(h(a,!0),0===a.strm.avail_out?wa:xa):a.strstart>a.block_start&&(h(a,!1),0===a.strm.avail_out)?ua:ua}function o(a,b){for(var c,d;;){if(a.lookahead<la){if(m(a),a.lookahead<la&&b===J)return ua;if(0===a.lookahead)break}if(c=0,a.lookahead>=ja&&(a.ins_h=(a.ins_h<<a.hash_shift^a.window[a.strstart+ja-1])&a.hash_mask,c=a.prev[a.strstart&a.w_mask]=a.head[a.ins_h],a.head[a.ins_h]=a.strstart),0!==c&&a.strstart-c<=a.w_size-la&&(a.match_length=l(a,c)),a.match_length>=ja)if(d=F._tr_tally(a,a.strstart-a.match_start,a.match_length-ja),a.lookahead-=a.match_length,a.match_length<=a.max_lazy_match&&a.lookahead>=ja){a.match_length--;do a.strstart++,a.ins_h=(a.ins_h<<a.hash_shift^a.window[a.strstart+ja-1])&a.hash_mask,c=a.prev[a.strstart&a.w_mask]=a.head[a.ins_h],a.head[a.ins_h]=a.strstart;while(0!==--a.match_length);a.strstart++}else a.strstart+=a.match_length,a.match_length=0,a.ins_h=a.window[a.strstart],a.ins_h=(a.ins_h<<a.hash_shift^a.window[a.strstart+1])&a.hash_mask;else d=F._tr_tally(a,0,a.window[a.strstart]),a.lookahead--,a.strstart++;if(d&&(h(a,!1),0===a.strm.avail_out))return ua}return a.insert=a.strstart<ja-1?a.strstart:ja-1,b===M?(h(a,!0),0===a.strm.avail_out?wa:xa):a.last_lit&&(h(a,!1),0===a.strm.avail_out)?ua:va}function p(a,b){for(var c,d,e;;){if(a.lookahead<la){if(m(a),a.lookahead<la&&b===J)return ua;if(0===a.lookahead)break}if(c=0,a.lookahead>=ja&&(a.ins_h=(a.ins_h<<a.hash_shift^a.window[a.strstart+ja-1])&a.hash_mask,c=a.prev[a.strstart&a.w_mask]=a.head[a.ins_h],a.head[a.ins_h]=a.strstart),a.prev_length=a.match_length,a.prev_match=a.match_start,a.match_length=ja-1,0!==c&&a.prev_length<a.max_lazy_match&&a.strstart-c<=a.w_size-la&&(a.match_length=l(a,c),a.match_length<=5&&(a.strategy===U||a.match_length===ja&&a.strstart-a.match_start>4096)&&(a.match_length=ja-1)),a.prev_length>=ja&&a.match_length<=a.prev_length){e=a.strstart+a.lookahead-ja,d=F._tr_tally(a,a.strstart-1-a.prev_match,a.prev_length-ja),a.lookahead-=a.prev_length-1,a.prev_length-=2;do++a.strstart<=e&&(a.ins_h=(a.ins_h<<a.hash_shift^a.window[a.strstart+ja-1])&a.hash_mask,c=a.prev[a.strstart&a.w_mask]=a.head[a.ins_h],a.head[a.ins_h]=a.strstart);while(0!==--a.prev_length);if(a.match_available=0,a.match_length=ja-1,a.strstart++,d&&(h(a,!1),0===a.strm.avail_out))return ua}else if(a.match_available){if(d=F._tr_tally(a,0,a.window[a.strstart-1]),d&&h(a,!1),a.strstart++,a.lookahead--,0===a.strm.avail_out)return ua}else a.match_available=1,a.strstart++,a.lookahead--}return a.match_available&&(d=F._tr_tally(a,0,a.window[a.strstart-1]),a.match_available=0),a.insert=a.strstart<ja-1?a.strstart:ja-1,b===M?(h(a,!0),0===a.strm.avail_out?wa:xa):a.last_lit&&(h(a,!1),0===a.strm.avail_out)?ua:va}function q(a,b){for(var c,d,e,f,g=a.window;;){if(a.lookahead<=ka){if(m(a),a.lookahead<=ka&&b===J)return ua;if(0===a.lookahead)break}if(a.match_length=0,a.lookahead>=ja&&a.strstart>0&&(e=a.strstart-1,d=g[e],d===g[++e]&&d===g[++e]&&d===g[++e])){f=a.strstart+ka;do;while(d===g[++e]&&d===g[++e]&&d===g[++e]&&d===g[++e]&&d===g[++e]&&d===g[++e]&&d===g[++e]&&d===g[++e]&&f>e);a.match_length=ka-(f-e),a.match_length>a.lookahead&&(a.match_length=a.lookahead)}if(a.match_length>=ja?(c=F._tr_tally(a,1,a.match_length-ja),a.lookahead-=a.match_length,a.strstart+=a.match_length,a.match_length=0):(c=F._tr_tally(a,0,a.window[a.strstart]),a.lookahead--,a.strstart++),c&&(h(a,!1),0===a.strm.avail_out))return ua}return a.insert=0,b===M?(h(a,!0),0===a.strm.avail_out?wa:xa):a.last_lit&&(h(a,!1),0===a.strm.avail_out)?ua:va}function r(a,b){for(var c;;){if(0===a.lookahead&&(m(a),0===a.lookahead)){if(b===J)return ua;break}if(a.match_length=0,c=F._tr_tally(a,0,a.window[a.strstart]),a.lookahead--,a.strstart++,c&&(h(a,!1),0===a.strm.avail_out))return ua}return a.insert=0,b===M?(h(a,!0),0===a.strm.avail_out?wa:xa):a.last_lit&&(h(a,!1),0===a.strm.avail_out)?ua:va}function s(a,b,c,d,e){this.good_length=a,this.max_lazy=b,this.nice_length=c,this.max_chain=d,this.func=e}function t(a){a.window_size=2*a.w_size,f(a.head),a.max_lazy_match=D[a.level].max_lazy,a.good_match=D[a.level].good_length,a.nice_match=D[a.level].nice_length,a.max_chain_length=D[a.level].max_chain,a.strstart=0,a.block_start=0,a.lookahead=0,a.insert=0,a.match_length=a.prev_length=ja-1,a.match_available=0,a.ins_h=0}function u(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=$,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new E.Buf16(2*ha),this.dyn_dtree=new E.Buf16(2*(2*fa+1)),this.bl_tree=new E.Buf16(2*(2*ga+1)),f(this.dyn_ltree),f(this.dyn_dtree),f(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new E.Buf16(ia+1),this.heap=new E.Buf16(2*ea+1),f(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new E.Buf16(2*ea+1),f(this.depth),this.l_buf=0,this.lit_bufsize=0,this.last_lit=0,this.d_buf=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}function v(a){var b;return a&&a.state?(a.total_in=a.total_out=0,a.data_type=Z,b=a.state,b.pending=0,b.pending_out=0,b.wrap<0&&(b.wrap=-b.wrap),b.status=b.wrap?na:sa,a.adler=2===b.wrap?0:1,b.last_flush=J,F._tr_init(b),O):d(a,Q)}function w(a){var b=v(a);return b===O&&t(a.state),b}function x(a,b){return a&&a.state?2!==a.state.wrap?Q:(a.state.gzhead=b,O):Q}function y(a,b,c,e,f,g){if(!a)return Q;var h=1;if(b===T&&(b=6),0>e?(h=0,e=-e):e>15&&(h=2,e-=16),1>f||f>_||c!==$||8>e||e>15||0>b||b>9||0>g||g>X)return d(a,Q);8===e&&(e=9);var i=new u;return a.state=i,i.strm=a,i.wrap=h,i.gzhead=null,i.w_bits=e,i.w_size=1<<i.w_bits,i.w_mask=i.w_size-1,i.hash_bits=f+7,i.hash_size=1<<i.hash_bits,i.hash_mask=i.hash_size-1,i.hash_shift=~~((i.hash_bits+ja-1)/ja),i.window=new E.Buf8(2*i.w_size),i.head=new E.Buf16(i.hash_size),i.prev=new E.Buf16(i.w_size),i.lit_bufsize=1<<f+6,i.pending_buf_size=4*i.lit_bufsize,i.pending_buf=new E.Buf8(i.pending_buf_size),i.d_buf=i.lit_bufsize>>1,i.l_buf=3*i.lit_bufsize,i.level=b,i.strategy=g,i.method=c,w(a)}function z(a,b){return y(a,b,$,aa,ba,Y)}function A(a,b){var c,h,k,l;if(!a||!a.state||b>N||0>b)return a?d(a,Q):Q;if(h=a.state,!a.output||!a.input&&0!==a.avail_in||h.status===ta&&b!==M)return d(a,0===a.avail_out?S:Q);if(h.strm=a,c=h.last_flush,h.last_flush=b,h.status===na)if(2===h.wrap)a.adler=0,i(h,31),i(h,139),i(h,8),h.gzhead?(i(h,(h.gzhead.text?1:0)+(h.gzhead.hcrc?2:0)+(h.gzhead.extra?4:0)+(h.gzhead.name?8:0)+(h.gzhead.comment?16:0)),i(h,255&h.gzhead.time),i(h,h.gzhead.time>>8&255),i(h,h.gzhead.time>>16&255),i(h,h.gzhead.time>>24&255),i(h,9===h.level?2:h.strategy>=V||h.level<2?4:0),i(h,255&h.gzhead.os),h.gzhead.extra&&h.gzhead.extra.length&&(i(h,255&h.gzhead.extra.length),i(h,h.gzhead.extra.length>>8&255)),h.gzhead.hcrc&&(a.adler=H(a.adler,h.pending_buf,h.pending,0)),h.gzindex=0,h.status=oa):(i(h,0),i(h,0),i(h,0),i(h,0),i(h,0),i(h,9===h.level?2:h.strategy>=V||h.level<2?4:0),i(h,ya),h.status=sa);else{var m=$+(h.w_bits-8<<4)<<8,n=-1;n=h.strategy>=V||h.level<2?0:h.level<6?1:6===h.level?2:3,m|=n<<6,0!==h.strstart&&(m|=ma),m+=31-m%31,h.status=sa,j(h,m),0!==h.strstart&&(j(h,a.adler>>>16),j(h,65535&a.adler)),a.adler=1}if(h.status===oa)if(h.gzhead.extra){for(k=h.pending;h.gzindex<(65535&h.gzhead.extra.length)&&(h.pending!==h.pending_buf_size||(h.gzhead.hcrc&&h.pending>k&&(a.adler=H(a.adler,h.pending_buf,h.pending-k,k)),g(a),k=h.pending,h.pending!==h.pending_buf_size));)i(h,255&h.gzhead.extra[h.gzindex]),h.gzindex++;h.gzhead.hcrc&&h.pending>k&&(a.adler=H(a.adler,h.pending_buf,h.pending-k,k)),h.gzindex===h.gzhead.extra.length&&(h.gzindex=0,h.status=pa)}else h.status=pa;if(h.status===pa)if(h.gzhead.name){k=h.pending;do{if(h.pending===h.pending_buf_size&&(h.gzhead.hcrc&&h.pending>k&&(a.adler=H(a.adler,h.pending_buf,h.pending-k,k)),g(a),k=h.pending,h.pending===h.pending_buf_size)){l=1;break}l=h.gzindex<h.gzhead.name.length?255&h.gzhead.name.charCodeAt(h.gzindex++):0,i(h,l)}while(0!==l);h.gzhead.hcrc&&h.pending>k&&(a.adler=H(a.adler,h.pending_buf,h.pending-k,k)),0===l&&(h.gzindex=0,h.status=qa)}else h.status=qa;if(h.status===qa)if(h.gzhead.comment){k=h.pending;do{if(h.pending===h.pending_buf_size&&(h.gzhead.hcrc&&h.pending>k&&(a.adler=H(a.adler,h.pending_buf,h.pending-k,k)),g(a),k=h.pending,h.pending===h.pending_buf_size)){l=1;break}l=h.gzindex<h.gzhead.comment.length?255&h.gzhead.comment.charCodeAt(h.gzindex++):0,i(h,l)}while(0!==l);h.gzhead.hcrc&&h.pending>k&&(a.adler=H(a.adler,h.pending_buf,h.pending-k,k)),0===l&&(h.status=ra)}else h.status=ra;if(h.status===ra&&(h.gzhead.hcrc?(h.pending+2>h.pending_buf_size&&g(a),h.pending+2<=h.pending_buf_size&&(i(h,255&a.adler),i(h,a.adler>>8&255),a.adler=0,h.status=sa)):h.status=sa),0!==h.pending){if(g(a),0===a.avail_out)return h.last_flush=-1,O}else if(0===a.avail_in&&e(b)<=e(c)&&b!==M)return d(a,S);if(h.status===ta&&0!==a.avail_in)return d(a,S);if(0!==a.avail_in||0!==h.lookahead||b!==J&&h.status!==ta){var o=h.strategy===V?r(h,b):h.strategy===W?q(h,b):D[h.level].func(h,b);if((o===wa||o===xa)&&(h.status=ta),o===ua||o===wa)return 0===a.avail_out&&(h.last_flush=-1),O;if(o===va&&(b===K?F._tr_align(h):b!==N&&(F._tr_stored_block(h,0,0,!1),b===L&&(f(h.head),0===h.lookahead&&(h.strstart=0,h.block_start=0,h.insert=0))),g(a),0===a.avail_out))return h.last_flush=-1,O}return b!==M?O:h.wrap<=0?P:(2===h.wrap?(i(h,255&a.adler),i(h,a.adler>>8&255),i(h,a.adler>>16&255),i(h,a.adler>>24&255),i(h,255&a.total_in),i(h,a.total_in>>8&255),i(h,a.total_in>>16&255),i(h,a.total_in>>24&255)):(j(h,a.adler>>>16),j(h,65535&a.adler)),g(a),h.wrap>0&&(h.wrap=-h.wrap),0!==h.pending?O:P)}function B(a){var b;return a&&a.state?(b=a.state.status,b!==na&&b!==oa&&b!==pa&&b!==qa&&b!==ra&&b!==sa&&b!==ta?d(a,Q):(a.state=null,b===sa?d(a,R):O)):Q}function C(a,b){var c,d,e,g,h,i,j,k,l=b.length;if(!a||!a.state)return Q;if(c=a.state,g=c.wrap,2===g||1===g&&c.status!==na||c.lookahead)return Q;for(1===g&&(a.adler=G(a.adler,b,l,0)),c.wrap=0,l>=c.w_size&&(0===g&&(f(c.head),c.strstart=0,c.block_start=0,c.insert=0),k=new E.Buf8(c.w_size),E.arraySet(k,b,l-c.w_size,c.w_size,0),b=k,l=c.w_size),h=a.avail_in,i=a.next_in,j=a.input,a.avail_in=l,a.next_in=0,a.input=b,m(c);c.lookahead>=ja;){d=c.strstart,e=c.lookahead-(ja-1);do c.ins_h=(c.ins_h<<c.hash_shift^c.window[d+ja-1])&c.hash_mask,c.prev[d&c.w_mask]=c.head[c.ins_h],c.head[c.ins_h]=d,d++;while(--e);c.strstart=d,c.lookahead=ja-1,m(c)}return c.strstart+=c.lookahead,c.block_start=c.strstart,c.insert=c.lookahead,c.lookahead=0,c.match_length=c.prev_length=ja-1,c.match_available=0,a.next_in=i,a.input=j,a.avail_in=h,c.wrap=g,O}var D,E=a("../utils/common"),F=a("./trees"),G=a("./adler32"),H=a("./crc32"),I=a("./messages"),J=0,K=1,L=3,M=4,N=5,O=0,P=1,Q=-2,R=-3,S=-5,T=-1,U=1,V=2,W=3,X=4,Y=0,Z=2,$=8,_=9,aa=15,ba=8,ca=29,da=256,ea=da+1+ca,fa=30,ga=19,ha=2*ea+1,ia=15,ja=3,ka=258,la=ka+ja+1,ma=32,na=42,oa=69,pa=73,qa=91,ra=103,sa=113,ta=666,ua=1,va=2,wa=3,xa=4,ya=3;
D=[new s(0,0,0,0,n),new s(4,4,8,4,o),new s(4,5,16,8,o),new s(4,6,32,32,o),new s(4,4,16,16,p),new s(8,16,32,32,p),new s(8,16,128,128,p),new s(8,32,128,256,p),new s(32,128,258,1024,p),new s(32,258,258,4096,p)],c.deflateInit=z,c.deflateInit2=y,c.deflateReset=w,c.deflateResetKeep=v,c.deflateSetHeader=x,c.deflate=A,c.deflateEnd=B,c.deflateSetDictionary=C,c.deflateInfo="pako deflate (from Nodeca project)"},{"../utils/common":3,"./adler32":5,"./crc32":7,"./messages":13,"./trees":14}],9:[function(a,b,c){"use strict";function d(){this.text=0,this.time=0,this.xflags=0,this.os=0,this.extra=null,this.extra_len=0,this.name="",this.comment="",this.hcrc=0,this.done=!1}b.exports=d},{}],10:[function(a,b,c){"use strict";var d=30,e=12;b.exports=function(a,b){var c,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C;c=a.state,f=a.next_in,B=a.input,g=f+(a.avail_in-5),h=a.next_out,C=a.output,i=h-(b-a.avail_out),j=h+(a.avail_out-257),k=c.dmax,l=c.wsize,m=c.whave,n=c.wnext,o=c.window,p=c.hold,q=c.bits,r=c.lencode,s=c.distcode,t=(1<<c.lenbits)-1,u=(1<<c.distbits)-1;a:do{15>q&&(p+=B[f++]<<q,q+=8,p+=B[f++]<<q,q+=8),v=r[p&t];b:for(;;){if(w=v>>>24,p>>>=w,q-=w,w=v>>>16&255,0===w)C[h++]=65535&v;else{if(!(16&w)){if(0===(64&w)){v=r[(65535&v)+(p&(1<<w)-1)];continue b}if(32&w){c.mode=e;break a}a.msg="invalid literal/length code",c.mode=d;break a}x=65535&v,w&=15,w&&(w>q&&(p+=B[f++]<<q,q+=8),x+=p&(1<<w)-1,p>>>=w,q-=w),15>q&&(p+=B[f++]<<q,q+=8,p+=B[f++]<<q,q+=8),v=s[p&u];c:for(;;){if(w=v>>>24,p>>>=w,q-=w,w=v>>>16&255,!(16&w)){if(0===(64&w)){v=s[(65535&v)+(p&(1<<w)-1)];continue c}a.msg="invalid distance code",c.mode=d;break a}if(y=65535&v,w&=15,w>q&&(p+=B[f++]<<q,q+=8,w>q&&(p+=B[f++]<<q,q+=8)),y+=p&(1<<w)-1,y>k){a.msg="invalid distance too far back",c.mode=d;break a}if(p>>>=w,q-=w,w=h-i,y>w){if(w=y-w,w>m&&c.sane){a.msg="invalid distance too far back",c.mode=d;break a}if(z=0,A=o,0===n){if(z+=l-w,x>w){x-=w;do C[h++]=o[z++];while(--w);z=h-y,A=C}}else if(w>n){if(z+=l+n-w,w-=n,x>w){x-=w;do C[h++]=o[z++];while(--w);if(z=0,x>n){w=n,x-=w;do C[h++]=o[z++];while(--w);z=h-y,A=C}}}else if(z+=n-w,x>w){x-=w;do C[h++]=o[z++];while(--w);z=h-y,A=C}for(;x>2;)C[h++]=A[z++],C[h++]=A[z++],C[h++]=A[z++],x-=3;x&&(C[h++]=A[z++],x>1&&(C[h++]=A[z++]))}else{z=h-y;do C[h++]=C[z++],C[h++]=C[z++],C[h++]=C[z++],x-=3;while(x>2);x&&(C[h++]=C[z++],x>1&&(C[h++]=C[z++]))}break}}break}}while(g>f&&j>h);x=q>>3,f-=x,q-=x<<3,p&=(1<<q)-1,a.next_in=f,a.next_out=h,a.avail_in=g>f?5+(g-f):5-(f-g),a.avail_out=j>h?257+(j-h):257-(h-j),c.hold=p,c.bits=q}},{}],11:[function(a,b,c){"use strict";function d(a){return(a>>>24&255)+(a>>>8&65280)+((65280&a)<<8)+((255&a)<<24)}function e(){this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new s.Buf16(320),this.work=new s.Buf16(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0}function f(a){var b;return a&&a.state?(b=a.state,a.total_in=a.total_out=b.total=0,a.msg="",b.wrap&&(a.adler=1&b.wrap),b.mode=L,b.last=0,b.havedict=0,b.dmax=32768,b.head=null,b.hold=0,b.bits=0,b.lencode=b.lendyn=new s.Buf32(pa),b.distcode=b.distdyn=new s.Buf32(qa),b.sane=1,b.back=-1,D):G}function g(a){var b;return a&&a.state?(b=a.state,b.wsize=0,b.whave=0,b.wnext=0,f(a)):G}function h(a,b){var c,d;return a&&a.state?(d=a.state,0>b?(c=0,b=-b):(c=(b>>4)+1,48>b&&(b&=15)),b&&(8>b||b>15)?G:(null!==d.window&&d.wbits!==b&&(d.window=null),d.wrap=c,d.wbits=b,g(a))):G}function i(a,b){var c,d;return a?(d=new e,a.state=d,d.window=null,c=h(a,b),c!==D&&(a.state=null),c):G}function j(a){return i(a,sa)}function k(a){if(ta){var b;for(q=new s.Buf32(512),r=new s.Buf32(32),b=0;144>b;)a.lens[b++]=8;for(;256>b;)a.lens[b++]=9;for(;280>b;)a.lens[b++]=7;for(;288>b;)a.lens[b++]=8;for(w(y,a.lens,0,288,q,0,a.work,{bits:9}),b=0;32>b;)a.lens[b++]=5;w(z,a.lens,0,32,r,0,a.work,{bits:5}),ta=!1}a.lencode=q,a.lenbits=9,a.distcode=r,a.distbits=5}function l(a,b,c,d){var e,f=a.state;return null===f.window&&(f.wsize=1<<f.wbits,f.wnext=0,f.whave=0,f.window=new s.Buf8(f.wsize)),d>=f.wsize?(s.arraySet(f.window,b,c-f.wsize,f.wsize,0),f.wnext=0,f.whave=f.wsize):(e=f.wsize-f.wnext,e>d&&(e=d),s.arraySet(f.window,b,c-d,e,f.wnext),d-=e,d?(s.arraySet(f.window,b,c-d,d,0),f.wnext=d,f.whave=f.wsize):(f.wnext+=e,f.wnext===f.wsize&&(f.wnext=0),f.whave<f.wsize&&(f.whave+=e))),0}function m(a,b){var c,e,f,g,h,i,j,m,n,o,p,q,r,pa,qa,ra,sa,ta,ua,va,wa,xa,ya,za,Aa=0,Ba=new s.Buf8(4),Ca=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];if(!a||!a.state||!a.output||!a.input&&0!==a.avail_in)return G;c=a.state,c.mode===W&&(c.mode=X),h=a.next_out,f=a.output,j=a.avail_out,g=a.next_in,e=a.input,i=a.avail_in,m=c.hold,n=c.bits,o=i,p=j,xa=D;a:for(;;)switch(c.mode){case L:if(0===c.wrap){c.mode=X;break}for(;16>n;){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}if(2&c.wrap&&35615===m){c.check=0,Ba[0]=255&m,Ba[1]=m>>>8&255,c.check=u(c.check,Ba,2,0),m=0,n=0,c.mode=M;break}if(c.flags=0,c.head&&(c.head.done=!1),!(1&c.wrap)||(((255&m)<<8)+(m>>8))%31){a.msg="incorrect header check",c.mode=ma;break}if((15&m)!==K){a.msg="unknown compression method",c.mode=ma;break}if(m>>>=4,n-=4,wa=(15&m)+8,0===c.wbits)c.wbits=wa;else if(wa>c.wbits){a.msg="invalid window size",c.mode=ma;break}c.dmax=1<<wa,a.adler=c.check=1,c.mode=512&m?U:W,m=0,n=0;break;case M:for(;16>n;){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}if(c.flags=m,(255&c.flags)!==K){a.msg="unknown compression method",c.mode=ma;break}if(57344&c.flags){a.msg="unknown header flags set",c.mode=ma;break}c.head&&(c.head.text=m>>8&1),512&c.flags&&(Ba[0]=255&m,Ba[1]=m>>>8&255,c.check=u(c.check,Ba,2,0)),m=0,n=0,c.mode=N;case N:for(;32>n;){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}c.head&&(c.head.time=m),512&c.flags&&(Ba[0]=255&m,Ba[1]=m>>>8&255,Ba[2]=m>>>16&255,Ba[3]=m>>>24&255,c.check=u(c.check,Ba,4,0)),m=0,n=0,c.mode=O;case O:for(;16>n;){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}c.head&&(c.head.xflags=255&m,c.head.os=m>>8),512&c.flags&&(Ba[0]=255&m,Ba[1]=m>>>8&255,c.check=u(c.check,Ba,2,0)),m=0,n=0,c.mode=P;case P:if(1024&c.flags){for(;16>n;){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}c.length=m,c.head&&(c.head.extra_len=m),512&c.flags&&(Ba[0]=255&m,Ba[1]=m>>>8&255,c.check=u(c.check,Ba,2,0)),m=0,n=0}else c.head&&(c.head.extra=null);c.mode=Q;case Q:if(1024&c.flags&&(q=c.length,q>i&&(q=i),q&&(c.head&&(wa=c.head.extra_len-c.length,c.head.extra||(c.head.extra=new Array(c.head.extra_len)),s.arraySet(c.head.extra,e,g,q,wa)),512&c.flags&&(c.check=u(c.check,e,q,g)),i-=q,g+=q,c.length-=q),c.length))break a;c.length=0,c.mode=R;case R:if(2048&c.flags){if(0===i)break a;q=0;do wa=e[g+q++],c.head&&wa&&c.length<65536&&(c.head.name+=String.fromCharCode(wa));while(wa&&i>q);if(512&c.flags&&(c.check=u(c.check,e,q,g)),i-=q,g+=q,wa)break a}else c.head&&(c.head.name=null);c.length=0,c.mode=S;case S:if(4096&c.flags){if(0===i)break a;q=0;do wa=e[g+q++],c.head&&wa&&c.length<65536&&(c.head.comment+=String.fromCharCode(wa));while(wa&&i>q);if(512&c.flags&&(c.check=u(c.check,e,q,g)),i-=q,g+=q,wa)break a}else c.head&&(c.head.comment=null);c.mode=T;case T:if(512&c.flags){for(;16>n;){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}if(m!==(65535&c.check)){a.msg="header crc mismatch",c.mode=ma;break}m=0,n=0}c.head&&(c.head.hcrc=c.flags>>9&1,c.head.done=!0),a.adler=c.check=0,c.mode=W;break;case U:for(;32>n;){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}a.adler=c.check=d(m),m=0,n=0,c.mode=V;case V:if(0===c.havedict)return a.next_out=h,a.avail_out=j,a.next_in=g,a.avail_in=i,c.hold=m,c.bits=n,F;a.adler=c.check=1,c.mode=W;case W:if(b===B||b===C)break a;case X:if(c.last){m>>>=7&n,n-=7&n,c.mode=ja;break}for(;3>n;){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}switch(c.last=1&m,m>>>=1,n-=1,3&m){case 0:c.mode=Y;break;case 1:if(k(c),c.mode=ca,b===C){m>>>=2,n-=2;break a}break;case 2:c.mode=_;break;case 3:a.msg="invalid block type",c.mode=ma}m>>>=2,n-=2;break;case Y:for(m>>>=7&n,n-=7&n;32>n;){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}if((65535&m)!==(m>>>16^65535)){a.msg="invalid stored block lengths",c.mode=ma;break}if(c.length=65535&m,m=0,n=0,c.mode=Z,b===C)break a;case Z:c.mode=$;case $:if(q=c.length){if(q>i&&(q=i),q>j&&(q=j),0===q)break a;s.arraySet(f,e,g,q,h),i-=q,g+=q,j-=q,h+=q,c.length-=q;break}c.mode=W;break;case _:for(;14>n;){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}if(c.nlen=(31&m)+257,m>>>=5,n-=5,c.ndist=(31&m)+1,m>>>=5,n-=5,c.ncode=(15&m)+4,m>>>=4,n-=4,c.nlen>286||c.ndist>30){a.msg="too many length or distance symbols",c.mode=ma;break}c.have=0,c.mode=aa;case aa:for(;c.have<c.ncode;){for(;3>n;){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}c.lens[Ca[c.have++]]=7&m,m>>>=3,n-=3}for(;c.have<19;)c.lens[Ca[c.have++]]=0;if(c.lencode=c.lendyn,c.lenbits=7,ya={bits:c.lenbits},xa=w(x,c.lens,0,19,c.lencode,0,c.work,ya),c.lenbits=ya.bits,xa){a.msg="invalid code lengths set",c.mode=ma;break}c.have=0,c.mode=ba;case ba:for(;c.have<c.nlen+c.ndist;){for(;Aa=c.lencode[m&(1<<c.lenbits)-1],qa=Aa>>>24,ra=Aa>>>16&255,sa=65535&Aa,!(n>=qa);){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}if(16>sa)m>>>=qa,n-=qa,c.lens[c.have++]=sa;else{if(16===sa){for(za=qa+2;za>n;){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}if(m>>>=qa,n-=qa,0===c.have){a.msg="invalid bit length repeat",c.mode=ma;break}wa=c.lens[c.have-1],q=3+(3&m),m>>>=2,n-=2}else if(17===sa){for(za=qa+3;za>n;){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}m>>>=qa,n-=qa,wa=0,q=3+(7&m),m>>>=3,n-=3}else{for(za=qa+7;za>n;){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}m>>>=qa,n-=qa,wa=0,q=11+(127&m),m>>>=7,n-=7}if(c.have+q>c.nlen+c.ndist){a.msg="invalid bit length repeat",c.mode=ma;break}for(;q--;)c.lens[c.have++]=wa}}if(c.mode===ma)break;if(0===c.lens[256]){a.msg="invalid code -- missing end-of-block",c.mode=ma;break}if(c.lenbits=9,ya={bits:c.lenbits},xa=w(y,c.lens,0,c.nlen,c.lencode,0,c.work,ya),c.lenbits=ya.bits,xa){a.msg="invalid literal/lengths set",c.mode=ma;break}if(c.distbits=6,c.distcode=c.distdyn,ya={bits:c.distbits},xa=w(z,c.lens,c.nlen,c.ndist,c.distcode,0,c.work,ya),c.distbits=ya.bits,xa){a.msg="invalid distances set",c.mode=ma;break}if(c.mode=ca,b===C)break a;case ca:c.mode=da;case da:if(i>=6&&j>=258){a.next_out=h,a.avail_out=j,a.next_in=g,a.avail_in=i,c.hold=m,c.bits=n,v(a,p),h=a.next_out,f=a.output,j=a.avail_out,g=a.next_in,e=a.input,i=a.avail_in,m=c.hold,n=c.bits,c.mode===W&&(c.back=-1);break}for(c.back=0;Aa=c.lencode[m&(1<<c.lenbits)-1],qa=Aa>>>24,ra=Aa>>>16&255,sa=65535&Aa,!(n>=qa);){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}if(ra&&0===(240&ra)){for(ta=qa,ua=ra,va=sa;Aa=c.lencode[va+((m&(1<<ta+ua)-1)>>ta)],qa=Aa>>>24,ra=Aa>>>16&255,sa=65535&Aa,!(n>=ta+qa);){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}m>>>=ta,n-=ta,c.back+=ta}if(m>>>=qa,n-=qa,c.back+=qa,c.length=sa,0===ra){c.mode=ia;break}if(32&ra){c.back=-1,c.mode=W;break}if(64&ra){a.msg="invalid literal/length code",c.mode=ma;break}c.extra=15&ra,c.mode=ea;case ea:if(c.extra){for(za=c.extra;za>n;){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}c.length+=m&(1<<c.extra)-1,m>>>=c.extra,n-=c.extra,c.back+=c.extra}c.was=c.length,c.mode=fa;case fa:for(;Aa=c.distcode[m&(1<<c.distbits)-1],qa=Aa>>>24,ra=Aa>>>16&255,sa=65535&Aa,!(n>=qa);){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}if(0===(240&ra)){for(ta=qa,ua=ra,va=sa;Aa=c.distcode[va+((m&(1<<ta+ua)-1)>>ta)],qa=Aa>>>24,ra=Aa>>>16&255,sa=65535&Aa,!(n>=ta+qa);){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}m>>>=ta,n-=ta,c.back+=ta}if(m>>>=qa,n-=qa,c.back+=qa,64&ra){a.msg="invalid distance code",c.mode=ma;break}c.offset=sa,c.extra=15&ra,c.mode=ga;case ga:if(c.extra){for(za=c.extra;za>n;){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}c.offset+=m&(1<<c.extra)-1,m>>>=c.extra,n-=c.extra,c.back+=c.extra}if(c.offset>c.dmax){a.msg="invalid distance too far back",c.mode=ma;break}c.mode=ha;case ha:if(0===j)break a;if(q=p-j,c.offset>q){if(q=c.offset-q,q>c.whave&&c.sane){a.msg="invalid distance too far back",c.mode=ma;break}q>c.wnext?(q-=c.wnext,r=c.wsize-q):r=c.wnext-q,q>c.length&&(q=c.length),pa=c.window}else pa=f,r=h-c.offset,q=c.length;q>j&&(q=j),j-=q,c.length-=q;do f[h++]=pa[r++];while(--q);0===c.length&&(c.mode=da);break;case ia:if(0===j)break a;f[h++]=c.length,j--,c.mode=da;break;case ja:if(c.wrap){for(;32>n;){if(0===i)break a;i--,m|=e[g++]<<n,n+=8}if(p-=j,a.total_out+=p,c.total+=p,p&&(a.adler=c.check=c.flags?u(c.check,f,p,h-p):t(c.check,f,p,h-p)),p=j,(c.flags?m:d(m))!==c.check){a.msg="incorrect data check",c.mode=ma;break}m=0,n=0}c.mode=ka;case ka:if(c.wrap&&c.flags){for(;32>n;){if(0===i)break a;i--,m+=e[g++]<<n,n+=8}if(m!==(4294967295&c.total)){a.msg="incorrect length check",c.mode=ma;break}m=0,n=0}c.mode=la;case la:xa=E;break a;case ma:xa=H;break a;case na:return I;case oa:default:return G}return a.next_out=h,a.avail_out=j,a.next_in=g,a.avail_in=i,c.hold=m,c.bits=n,(c.wsize||p!==a.avail_out&&c.mode<ma&&(c.mode<ja||b!==A))&&l(a,a.output,a.next_out,p-a.avail_out)?(c.mode=na,I):(o-=a.avail_in,p-=a.avail_out,a.total_in+=o,a.total_out+=p,c.total+=p,c.wrap&&p&&(a.adler=c.check=c.flags?u(c.check,f,p,a.next_out-p):t(c.check,f,p,a.next_out-p)),a.data_type=c.bits+(c.last?64:0)+(c.mode===W?128:0)+(c.mode===ca||c.mode===Z?256:0),(0===o&&0===p||b===A)&&xa===D&&(xa=J),xa)}function n(a){if(!a||!a.state)return G;var b=a.state;return b.window&&(b.window=null),a.state=null,D}function o(a,b){var c;return a&&a.state?(c=a.state,0===(2&c.wrap)?G:(c.head=b,b.done=!1,D)):G}function p(a,b){var c,d,e,f=b.length;return a&&a.state?(c=a.state,0!==c.wrap&&c.mode!==V?G:c.mode===V&&(d=1,d=t(d,b,f,0),d!==c.check)?H:(e=l(a,b,f,f))?(c.mode=na,I):(c.havedict=1,D)):G}var q,r,s=a("../utils/common"),t=a("./adler32"),u=a("./crc32"),v=a("./inffast"),w=a("./inftrees"),x=0,y=1,z=2,A=4,B=5,C=6,D=0,E=1,F=2,G=-2,H=-3,I=-4,J=-5,K=8,L=1,M=2,N=3,O=4,P=5,Q=6,R=7,S=8,T=9,U=10,V=11,W=12,X=13,Y=14,Z=15,$=16,_=17,aa=18,ba=19,ca=20,da=21,ea=22,fa=23,ga=24,ha=25,ia=26,ja=27,ka=28,la=29,ma=30,na=31,oa=32,pa=852,qa=592,ra=15,sa=ra,ta=!0;c.inflateReset=g,c.inflateReset2=h,c.inflateResetKeep=f,c.inflateInit=j,c.inflateInit2=i,c.inflate=m,c.inflateEnd=n,c.inflateGetHeader=o,c.inflateSetDictionary=p,c.inflateInfo="pako inflate (from Nodeca project)"},{"../utils/common":3,"./adler32":5,"./crc32":7,"./inffast":10,"./inftrees":12}],12:[function(a,b,c){"use strict";var d=a("../utils/common"),e=15,f=852,g=592,h=0,i=1,j=2,k=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0],l=[16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78],m=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0],n=[16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64];b.exports=function(a,b,c,o,p,q,r,s){var t,u,v,w,x,y,z,A,B,C=s.bits,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=null,O=0,P=new d.Buf16(e+1),Q=new d.Buf16(e+1),R=null,S=0;for(D=0;e>=D;D++)P[D]=0;for(E=0;o>E;E++)P[b[c+E]]++;for(H=C,G=e;G>=1&&0===P[G];G--);if(H>G&&(H=G),0===G)return p[q++]=20971520,p[q++]=20971520,s.bits=1,0;for(F=1;G>F&&0===P[F];F++);for(F>H&&(H=F),K=1,D=1;e>=D;D++)if(K<<=1,K-=P[D],0>K)return-1;if(K>0&&(a===h||1!==G))return-1;for(Q[1]=0,D=1;e>D;D++)Q[D+1]=Q[D]+P[D];for(E=0;o>E;E++)0!==b[c+E]&&(r[Q[b[c+E]]++]=E);if(a===h?(N=R=r,y=19):a===i?(N=k,O-=257,R=l,S-=257,y=256):(N=m,R=n,y=-1),M=0,E=0,D=F,x=q,I=H,J=0,v=-1,L=1<<H,w=L-1,a===i&&L>f||a===j&&L>g)return 1;for(var T=0;;){T++,z=D-J,r[E]<y?(A=0,B=r[E]):r[E]>y?(A=R[S+r[E]],B=N[O+r[E]]):(A=96,B=0),t=1<<D-J,u=1<<I,F=u;do u-=t,p[x+(M>>J)+u]=z<<24|A<<16|B|0;while(0!==u);for(t=1<<D-1;M&t;)t>>=1;if(0!==t?(M&=t-1,M+=t):M=0,E++,0===--P[D]){if(D===G)break;D=b[c+r[E]]}if(D>H&&(M&w)!==v){for(0===J&&(J=H),x+=F,I=D-J,K=1<<I;G>I+J&&(K-=P[I+J],!(0>=K));)I++,K<<=1;if(L+=1<<I,a===i&&L>f||a===j&&L>g)return 1;v=M&w,p[v]=H<<24|I<<16|x-q|0}}return 0!==M&&(p[x+M]=D-J<<24|64<<16|0),s.bits=H,0}},{"../utils/common":3}],13:[function(a,b,c){"use strict";b.exports={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"}},{}],14:[function(a,b,c){"use strict";function d(a){for(var b=a.length;--b>=0;)a[b]=0}function e(a,b,c,d,e){this.static_tree=a,this.extra_bits=b,this.extra_base=c,this.elems=d,this.max_length=e,this.has_stree=a&&a.length}function f(a,b){this.dyn_tree=a,this.max_code=0,this.stat_desc=b}function g(a){return 256>a?ia[a]:ia[256+(a>>>7)]}function h(a,b){a.pending_buf[a.pending++]=255&b,a.pending_buf[a.pending++]=b>>>8&255}function i(a,b,c){a.bi_valid>X-c?(a.bi_buf|=b<<a.bi_valid&65535,h(a,a.bi_buf),a.bi_buf=b>>X-a.bi_valid,a.bi_valid+=c-X):(a.bi_buf|=b<<a.bi_valid&65535,a.bi_valid+=c)}function j(a,b,c){i(a,c[2*b],c[2*b+1])}function k(a,b){var c=0;do c|=1&a,a>>>=1,c<<=1;while(--b>0);return c>>>1}function l(a){16===a.bi_valid?(h(a,a.bi_buf),a.bi_buf=0,a.bi_valid=0):a.bi_valid>=8&&(a.pending_buf[a.pending++]=255&a.bi_buf,a.bi_buf>>=8,a.bi_valid-=8)}function m(a,b){var c,d,e,f,g,h,i=b.dyn_tree,j=b.max_code,k=b.stat_desc.static_tree,l=b.stat_desc.has_stree,m=b.stat_desc.extra_bits,n=b.stat_desc.extra_base,o=b.stat_desc.max_length,p=0;for(f=0;W>=f;f++)a.bl_count[f]=0;for(i[2*a.heap[a.heap_max]+1]=0,c=a.heap_max+1;V>c;c++)d=a.heap[c],f=i[2*i[2*d+1]+1]+1,f>o&&(f=o,p++),i[2*d+1]=f,d>j||(a.bl_count[f]++,g=0,d>=n&&(g=m[d-n]),h=i[2*d],a.opt_len+=h*(f+g),l&&(a.static_len+=h*(k[2*d+1]+g)));if(0!==p){do{for(f=o-1;0===a.bl_count[f];)f--;a.bl_count[f]--,a.bl_count[f+1]+=2,a.bl_count[o]--,p-=2}while(p>0);for(f=o;0!==f;f--)for(d=a.bl_count[f];0!==d;)e=a.heap[--c],e>j||(i[2*e+1]!==f&&(a.opt_len+=(f-i[2*e+1])*i[2*e],i[2*e+1]=f),d--)}}function n(a,b,c){var d,e,f=new Array(W+1),g=0;for(d=1;W>=d;d++)f[d]=g=g+c[d-1]<<1;for(e=0;b>=e;e++){var h=a[2*e+1];0!==h&&(a[2*e]=k(f[h]++,h))}}function o(){var a,b,c,d,f,g=new Array(W+1);for(c=0,d=0;Q-1>d;d++)for(ka[d]=c,a=0;a<1<<ba[d];a++)ja[c++]=d;for(ja[c-1]=d,f=0,d=0;16>d;d++)for(la[d]=f,a=0;a<1<<ca[d];a++)ia[f++]=d;for(f>>=7;T>d;d++)for(la[d]=f<<7,a=0;a<1<<ca[d]-7;a++)ia[256+f++]=d;for(b=0;W>=b;b++)g[b]=0;for(a=0;143>=a;)ga[2*a+1]=8,a++,g[8]++;for(;255>=a;)ga[2*a+1]=9,a++,g[9]++;for(;279>=a;)ga[2*a+1]=7,a++,g[7]++;for(;287>=a;)ga[2*a+1]=8,a++,g[8]++;for(n(ga,S+1,g),a=0;T>a;a++)ha[2*a+1]=5,ha[2*a]=k(a,5);ma=new e(ga,ba,R+1,S,W),na=new e(ha,ca,0,T,W),oa=new e(new Array(0),da,0,U,Y)}function p(a){var b;for(b=0;S>b;b++)a.dyn_ltree[2*b]=0;for(b=0;T>b;b++)a.dyn_dtree[2*b]=0;for(b=0;U>b;b++)a.bl_tree[2*b]=0;a.dyn_ltree[2*Z]=1,a.opt_len=a.static_len=0,a.last_lit=a.matches=0}function q(a){a.bi_valid>8?h(a,a.bi_buf):a.bi_valid>0&&(a.pending_buf[a.pending++]=a.bi_buf),a.bi_buf=0,a.bi_valid=0}function r(a,b,c,d){q(a),d&&(h(a,c),h(a,~c)),G.arraySet(a.pending_buf,a.window,b,c,a.pending),a.pending+=c}function s(a,b,c,d){var e=2*b,f=2*c;return a[e]<a[f]||a[e]===a[f]&&d[b]<=d[c]}function t(a,b,c){for(var d=a.heap[c],e=c<<1;e<=a.heap_len&&(e<a.heap_len&&s(b,a.heap[e+1],a.heap[e],a.depth)&&e++,!s(b,d,a.heap[e],a.depth));)a.heap[c]=a.heap[e],c=e,e<<=1;a.heap[c]=d}function u(a,b,c){var d,e,f,h,k=0;if(0!==a.last_lit)do d=a.pending_buf[a.d_buf+2*k]<<8|a.pending_buf[a.d_buf+2*k+1],e=a.pending_buf[a.l_buf+k],k++,0===d?j(a,e,b):(f=ja[e],j(a,f+R+1,b),h=ba[f],0!==h&&(e-=ka[f],i(a,e,h)),d--,f=g(d),j(a,f,c),h=ca[f],0!==h&&(d-=la[f],i(a,d,h)));while(k<a.last_lit);j(a,Z,b)}function v(a,b){var c,d,e,f=b.dyn_tree,g=b.stat_desc.static_tree,h=b.stat_desc.has_stree,i=b.stat_desc.elems,j=-1;for(a.heap_len=0,a.heap_max=V,c=0;i>c;c++)0!==f[2*c]?(a.heap[++a.heap_len]=j=c,a.depth[c]=0):f[2*c+1]=0;for(;a.heap_len<2;)e=a.heap[++a.heap_len]=2>j?++j:0,f[2*e]=1,a.depth[e]=0,a.opt_len--,h&&(a.static_len-=g[2*e+1]);for(b.max_code=j,c=a.heap_len>>1;c>=1;c--)t(a,f,c);e=i;do c=a.heap[1],a.heap[1]=a.heap[a.heap_len--],t(a,f,1),d=a.heap[1],a.heap[--a.heap_max]=c,a.heap[--a.heap_max]=d,f[2*e]=f[2*c]+f[2*d],a.depth[e]=(a.depth[c]>=a.depth[d]?a.depth[c]:a.depth[d])+1,f[2*c+1]=f[2*d+1]=e,a.heap[1]=e++,t(a,f,1);while(a.heap_len>=2);a.heap[--a.heap_max]=a.heap[1],m(a,b),n(f,j,a.bl_count)}function w(a,b,c){var d,e,f=-1,g=b[1],h=0,i=7,j=4;for(0===g&&(i=138,j=3),b[2*(c+1)+1]=65535,d=0;c>=d;d++)e=g,g=b[2*(d+1)+1],++h<i&&e===g||(j>h?a.bl_tree[2*e]+=h:0!==e?(e!==f&&a.bl_tree[2*e]++,a.bl_tree[2*$]++):10>=h?a.bl_tree[2*_]++:a.bl_tree[2*aa]++,h=0,f=e,0===g?(i=138,j=3):e===g?(i=6,j=3):(i=7,j=4))}function x(a,b,c){var d,e,f=-1,g=b[1],h=0,k=7,l=4;for(0===g&&(k=138,l=3),d=0;c>=d;d++)if(e=g,g=b[2*(d+1)+1],!(++h<k&&e===g)){if(l>h){do j(a,e,a.bl_tree);while(0!==--h)}else 0!==e?(e!==f&&(j(a,e,a.bl_tree),h--),j(a,$,a.bl_tree),i(a,h-3,2)):10>=h?(j(a,_,a.bl_tree),i(a,h-3,3)):(j(a,aa,a.bl_tree),i(a,h-11,7));h=0,f=e,0===g?(k=138,l=3):e===g?(k=6,l=3):(k=7,l=4)}}function y(a){var b;for(w(a,a.dyn_ltree,a.l_desc.max_code),w(a,a.dyn_dtree,a.d_desc.max_code),v(a,a.bl_desc),b=U-1;b>=3&&0===a.bl_tree[2*ea[b]+1];b--);return a.opt_len+=3*(b+1)+5+5+4,b}function z(a,b,c,d){var e;for(i(a,b-257,5),i(a,c-1,5),i(a,d-4,4),e=0;d>e;e++)i(a,a.bl_tree[2*ea[e]+1],3);x(a,a.dyn_ltree,b-1),x(a,a.dyn_dtree,c-1)}function A(a){var b,c=4093624447;for(b=0;31>=b;b++,c>>>=1)if(1&c&&0!==a.dyn_ltree[2*b])return I;if(0!==a.dyn_ltree[18]||0!==a.dyn_ltree[20]||0!==a.dyn_ltree[26])return J;for(b=32;R>b;b++)if(0!==a.dyn_ltree[2*b])return J;return I}function B(a){pa||(o(),pa=!0),a.l_desc=new f(a.dyn_ltree,ma),a.d_desc=new f(a.dyn_dtree,na),a.bl_desc=new f(a.bl_tree,oa),a.bi_buf=0,a.bi_valid=0,p(a)}function C(a,b,c,d){i(a,(L<<1)+(d?1:0),3),r(a,b,c,!0)}function D(a){i(a,M<<1,3),j(a,Z,ga),l(a)}function E(a,b,c,d){var e,f,g=0;a.level>0?(a.strm.data_type===K&&(a.strm.data_type=A(a)),v(a,a.l_desc),v(a,a.d_desc),g=y(a),e=a.opt_len+3+7>>>3,f=a.static_len+3+7>>>3,e>=f&&(e=f)):e=f=c+5,e>=c+4&&-1!==b?C(a,b,c,d):a.strategy===H||f===e?(i(a,(M<<1)+(d?1:0),3),u(a,ga,ha)):(i(a,(N<<1)+(d?1:0),3),z(a,a.l_desc.max_code+1,a.d_desc.max_code+1,g+1),u(a,a.dyn_ltree,a.dyn_dtree)),p(a),d&&q(a)}function F(a,b,c){return a.pending_buf[a.d_buf+2*a.last_lit]=b>>>8&255,a.pending_buf[a.d_buf+2*a.last_lit+1]=255&b,a.pending_buf[a.l_buf+a.last_lit]=255&c,a.last_lit++,0===b?a.dyn_ltree[2*c]++:(a.matches++,b--,a.dyn_ltree[2*(ja[c]+R+1)]++,a.dyn_dtree[2*g(b)]++),a.last_lit===a.lit_bufsize-1}var G=a("../utils/common"),H=4,I=0,J=1,K=2,L=0,M=1,N=2,O=3,P=258,Q=29,R=256,S=R+1+Q,T=30,U=19,V=2*S+1,W=15,X=16,Y=7,Z=256,$=16,_=17,aa=18,ba=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0],ca=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],da=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7],ea=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],fa=512,ga=new Array(2*(S+2));d(ga);var ha=new Array(2*T);d(ha);var ia=new Array(fa);d(ia);var ja=new Array(P-O+1);d(ja);var ka=new Array(Q);d(ka);var la=new Array(T);d(la);var ma,na,oa,pa=!1;c._tr_init=B,c._tr_stored_block=C,c._tr_flush_block=E,c._tr_tally=F,c._tr_align=D},{"../utils/common":3}],15:[function(a,b,c){"use strict";function d(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0}b.exports=d},{}],"/":[function(a,b,c){"use strict";var d=a("./lib/utils/common").assign,e=a("./lib/deflate"),f=a("./lib/inflate"),g=a("./lib/zlib/constants"),h={};d(h,e,f,g),b.exports=h},{"./lib/deflate":1,"./lib/inflate":2,"./lib/utils/common":3,"./lib/zlib/constants":6}]},{},[])("/")}),function(){"use strict";BrainBrowser.utils={webglExtensionAvailable:function(a){if(!BrainBrowser.WEBGL_ENABLED)return!1;var b=document.createElement("canvas"),c=b.getContext("webgl")||b.getContext("experimental-webgl");return!!c.getExtension(a)},webGLErrorMessage:function(){var a,b='BrainBrowser requires <a href="http://khronos.org/webgl/wiki/Getting_a_WebGL_Implementation">WebGL</a>.<br/>';return b+=window.WebGLRenderingContext?"Your browser seems to support it, but it is <br/> disabled or unavailable.<br/>":"Your browser does not seem to support it.<br/>",b+='Test your browser\'s WebGL support <a href="http://get.webgl.org/">here</a>.',a=document.createElement("div"),a.id="webgl-error",a.innerHTML=b,a},isFunction:function(a){return a instanceof Function||"function"==typeof a},isNumeric:function(a){return!isNaN(parseFloat(a))},createDataURL:function(a,b){if(!window.URL||!window.URL.createObjectURL)throw new Error("createDataURL requires URL.createObjectURL which does not seem to be available is this browser.");return window.URL.createObjectURL(new Blob([a],{type:b||"text/plain"}))},getWorkerImportURL:function(){var a=BrainBrowser.config.get("worker_dir"),b=document.location.origin+"/"+a,c=document.location.href,d=c.lastIndexOf("/");return d>=0&&(b=c.substring(0,d+1)+a),b},min:function(){var a=Array.prototype.slice.call(arguments);a=1===a.length&&BrainBrowser.utils.isNumeric(a[0].length)?a[0]:a;var b,c,d=a[0];for(b=1,c=a.length;c>b;b++)a[b]<d&&(d=a[b]);return d},max:function(){var a=Array.prototype.slice.call(arguments);a=1===a.length&&BrainBrowser.utils.isNumeric(a[0].length)?a[0]:a;var b,c,d=a[0];for(b=1,c=a.length;c>b;b++)a[b]>d&&(d=a[b]);return d},getOffset:function(a){for(var b=0,c=0;a.offsetParent;)b+=a.offsetTop,c+=a.offsetLeft,a=a.offsetParent;return{top:b,left:c}},captureMouse:function(a){var b={x:0,y:0,left:!1,middle:!1,right:!1};return document.addEventListener("mousemove",function(c){var d,e,f=BrainBrowser.utils.getOffset(a);void 0!==c.pageX?(d=c.pageX,e=c.pageY):(d=c.clientX+window.pageXOffset,e=c.clientY+window.pageYOffset),b.x=d-f.left,b.y=e-f.top},!1),a.addEventListener("mousedown",function(a){a.preventDefault(),0===a.button&&(b.left=!0),1===a.button&&(b.middle=!0),2===a.button&&(b.right=!0)},!1),a.addEventListener("mouseup",function(a){a.preventDefault(),0===a.button&&(b.left=!1),1===a.button&&(b.middle=!1),2===a.button&&(b.right=!1)},!1),a.addEventListener("mouseleave",function(a){a.preventDefault(),b.left=b.middle=b.right=!1},!1),a.addEventListener("contextmenu",function(a){a.preventDefault()},!1),b},captureTouch:function(a){function b(b){var d,e,f,g,h,i=BrainBrowser.utils.getOffset(a);for(c.length=g=b.touches.length,f=0;g>f;f++)h=b.touches[f],void 0!==h.pageX?(d=h.pageX,e=h.pageY):(d=h.clientX+window.pageXOffset,e=h.clientY+window.pageYOffset),c[f]=c[f]||{},c[f].x=d-i.left,c[f].y=e-i.top}var c=[];return a.addEventListener("touchstart",b,!1),a.addEventListener("touchmove",b,!1),a.addEventListener("touchend",b,!1),c}}}(),function(){"use strict";function a(a){var c,d=BrainBrowser.config.get("worker_dir");if(null===d)throw c="error in SurfaceViewer configuration.\nBrainBrowser configuration parameter 'worker_dir' not defined.\nUse 'BrainBrowser.config.set(\"worker_dir\", ...)' to set it.",BrainBrowser.events.triggerEvent("error",{message:c}),new Error(c);var e,f={deindex:"deindex.worker.js",wireframe:"wireframe.worker.js"},g=0,h=BrainBrowser.config.get("model_types"),i=BrainBrowser.config.get("intensity_data_types");return null!==h&&Object.keys(h).forEach(function(a){f[a+"_model"]=h[a].worker}),null!==i&&Object.keys(i).forEach(function(a){f[a+"_intensity"]=i[a].worker}),e=Object.keys(f),0===e.length?void a():void(window.URL&&window.URL.createObjectURL?e.forEach(function(c){var h,i=d+"/"+f[c],j=new XMLHttpRequest;j.open("GET",i),j.onreadystatechange=function(){4===j.readyState&&(h=j.status,h>=200&&300>h||304===h?b.worker_urls[c]=BrainBrowser.utils.createDataURL(j.response,"application/javascript"):b.worker_urls[c]=i,++g===e.length&&a())},j.send()}):(e.forEach(function(a){b.worker_urls[a]=d+"/"+f[a]}),a()))}var b=BrainBrowser.SurfaceViewer={start:function(c,d){console.log("BrainBrowser Surface Viewer v"+BrainBrowser.version);var e,f={};e="string"==typeof c?document.getElementById(c):c;var g={dom_element:e,model:null,model_data:null,mouse:BrainBrowser.utils.captureMouse(e),touches:BrainBrowser.utils.captureTouch(e),updated:!0,zoom:1,autorotate:{x:!1,y:!1,z:!1},getAttribute:function(a){return f[a]},setAttribute:function(a,b){f[a]=b},getVertex:function(a,c){c=c||{};var d=g.model_data.get(c.model_name).vertices,e=3*a;return new b.THREE.Vector3(d[e],d[e+1],d[e+2])}};return Object.keys(b.modules).forEach(function(a){b.modules[a](g)}),BrainBrowser.events.addEventModel(g),BrainBrowser.events.addEventListener("*",function(a){"draw"!==a&&(g.updated=!0)}),a(function(){d(g)}),g}};b.modules={},b.worker_urls={},BrainBrowser.config.set("model_types.json.worker","json.worker.js"),BrainBrowser.config.set("model_types.mniobj.worker","mniobj.worker.js"),BrainBrowser.config.set("model_types.wavefrontobj.worker","wavefrontobj.worker.js"),BrainBrowser.config.set("model_types.freesurferbin.worker","freesurferbin.worker.js"),BrainBrowser.config.set("model_types.freesurferbin.binary",!0),BrainBrowser.config.set("model_types.freesurferasc.worker","freesurferasc.worker.js"),BrainBrowser.config.set("model_types.gifti.worker","gifti.worker.js"),BrainBrowser.config.set("intensity_data_types.text.worker","text.intensity.worker.js"),BrainBrowser.config.set("intensity_data_types.freesurferbin.worker","freesurferbin.intensity.worker.js"),BrainBrowser.config.set("intensity_data_types.freesurferbin.binary",!0),BrainBrowser.config.set("intensity_data_types.freesurferasc.worker","freesurferasc.intensity.worker.js"),BrainBrowser.config.set("intensity_data_types.gifti.worker","gifti.worker.js")}(),BrainBrowser.SurfaceViewer.parseIntensityData=function(a,b,c){"use strict";var d,e=b+"_intensity";if(!BrainBrowser.SurfaceViewer.worker_urls[e])throw d="error in SurfaceViewer configuration.\nIntensity data worker URL for "+b+" not defined.\nUse 'BrainBrowser.config.set(\"intensity_data_types."+b+".worker\", ...)' to set it.",BrainBrowser.events.triggerEvent("error",{message:d}),new Error(d);var f=new Worker(BrainBrowser.SurfaceViewer.worker_urls[e]);f.addEventListener("message",function(a){c(a.data),f.terminate()});var g=BrainBrowser.utils.getWorkerImportURL();f.postMessage({cmd:"parse",data:a,url:g})},function(){"use strict";var a=BrainBrowser.SurfaceViewer.THREE={REVISION:"69"};"object"==typeof module&&(module.exports=a),void 0===Math.sign&&(Math.sign=function(a){return 0>a?-1:a>0?1:0}),a.MOUSE={LEFT:0,MIDDLE:1,RIGHT:2},a.CullFaceNone=0,a.CullFaceBack=1,a.CullFaceFront=2,a.CullFaceFrontBack=3,a.FrontFaceDirectionCW=0,a.FrontFaceDirectionCCW=1,a.BasicShadowMap=0,a.PCFShadowMap=1,a.PCFSoftShadowMap=2,a.FrontSide=0,a.BackSide=1,a.DoubleSide=2,a.NoShading=0,a.FlatShading=1,a.SmoothShading=2,a.NoColors=0,a.FaceColors=1,a.VertexColors=2,a.NoBlending=0,a.NormalBlending=1,a.AdditiveBlending=2,a.SubtractiveBlending=3,a.MultiplyBlending=4,a.CustomBlending=5,a.AddEquation=100,a.SubtractEquation=101,a.ReverseSubtractEquation=102,a.MinEquation=103,a.MaxEquation=104,a.ZeroFactor=200,a.OneFactor=201,a.SrcColorFactor=202,a.OneMinusSrcColorFactor=203,a.SrcAlphaFactor=204,a.OneMinusSrcAlphaFactor=205,a.DstAlphaFactor=206,a.OneMinusDstAlphaFactor=207,a.DstColorFactor=208,a.OneMinusDstColorFactor=209,a.SrcAlphaSaturateFactor=210,a.MultiplyOperation=0,a.MixOperation=1,a.AddOperation=2,a.UVMapping=function(){},a.CubeReflectionMapping=function(){},a.CubeRefractionMapping=function(){},a.SphericalReflectionMapping=function(){},a.SphericalRefractionMapping=function(){},a.RepeatWrapping=1e3,a.ClampToEdgeWrapping=1001,a.MirroredRepeatWrapping=1002,a.NearestFilter=1003,a.NearestMipMapNearestFilter=1004,a.NearestMipMapLinearFilter=1005,a.LinearFilter=1006,a.LinearMipMapNearestFilter=1007,a.LinearMipMapLinearFilter=1008,a.UnsignedByteType=1009,a.ByteType=1010,a.ShortType=1011,a.UnsignedShortType=1012,a.IntType=1013,a.UnsignedIntType=1014,a.FloatType=1015,a.UnsignedShort4444Type=1016,a.UnsignedShort5551Type=1017,a.UnsignedShort565Type=1018,a.AlphaFormat=1019,a.RGBFormat=1020,a.RGBAFormat=1021,a.LuminanceFormat=1022,a.LuminanceAlphaFormat=1023,a.RGB_S3TC_DXT1_Format=2001,a.RGBA_S3TC_DXT1_Format=2002,a.RGBA_S3TC_DXT3_Format=2003,a.RGBA_S3TC_DXT5_Format=2004,a.RGB_PVRTC_4BPPV1_Format=2100,a.RGB_PVRTC_2BPPV1_Format=2101,a.RGBA_PVRTC_4BPPV1_Format=2102,a.RGBA_PVRTC_2BPPV1_Format=2103,a.Color=function(a){return 3===arguments.length?this.setRGB(arguments[0],arguments[1],arguments[2]):this.set(a);
},a.Color.prototype={constructor:a.Color,r:1,g:1,b:1,set:function(b){return b instanceof a.Color?this.copy(b):"number"==typeof b?this.setHex(b):"string"==typeof b&&this.setStyle(b),this},setHex:function(a){return a=Math.floor(a),this.r=(a>>16&255)/255,this.g=(a>>8&255)/255,this.b=(255&a)/255,this},setRGB:function(a,b,c){return this.r=a,this.g=b,this.b=c,this},setHSL:function(a,b,c){if(0===b)this.r=this.g=this.b=c;else{var d=function(a,b,c){return 0>c&&(c+=1),c>1&&(c-=1),1/6>c?a+6*(b-a)*c:.5>c?b:2/3>c?a+6*(b-a)*(2/3-c):a},e=.5>=c?c*(1+b):c+b-c*b,f=2*c-e;this.r=d(f,e,a+1/3),this.g=d(f,e,a),this.b=d(f,e,a-1/3)}return this},setStyle:function(b){if(/^rgb\((\d+), ?(\d+), ?(\d+)\)$/i.test(b)){var c=/^rgb\((\d+), ?(\d+), ?(\d+)\)$/i.exec(b);return this.r=Math.min(255,parseInt(c[1],10))/255,this.g=Math.min(255,parseInt(c[2],10))/255,this.b=Math.min(255,parseInt(c[3],10))/255,this}if(/^rgb\((\d+)\%, ?(\d+)\%, ?(\d+)\%\)$/i.test(b)){var c=/^rgb\((\d+)\%, ?(\d+)\%, ?(\d+)\%\)$/i.exec(b);return this.r=Math.min(100,parseInt(c[1],10))/100,this.g=Math.min(100,parseInt(c[2],10))/100,this.b=Math.min(100,parseInt(c[3],10))/100,this}if(/^\#([0-9a-f]{6})$/i.test(b)){var c=/^\#([0-9a-f]{6})$/i.exec(b);return this.setHex(parseInt(c[1],16)),this}if(/^\#([0-9a-f])([0-9a-f])([0-9a-f])$/i.test(b)){var c=/^\#([0-9a-f])([0-9a-f])([0-9a-f])$/i.exec(b);return this.setHex(parseInt(c[1]+c[1]+c[2]+c[2]+c[3]+c[3],16)),this}return/^(\w+)$/i.test(b)?(this.setHex(a.ColorKeywords[b]),this):void 0},copy:function(a){return this.r=a.r,this.g=a.g,this.b=a.b,this},copyGammaToLinear:function(a){return this.r=a.r*a.r,this.g=a.g*a.g,this.b=a.b*a.b,this},copyLinearToGamma:function(a){return this.r=Math.sqrt(a.r),this.g=Math.sqrt(a.g),this.b=Math.sqrt(a.b),this},convertGammaToLinear:function(){var a=this.r,b=this.g,c=this.b;return this.r=a*a,this.g=b*b,this.b=c*c,this},convertLinearToGamma:function(){return this.r=Math.sqrt(this.r),this.g=Math.sqrt(this.g),this.b=Math.sqrt(this.b),this},getHex:function(){return 255*this.r<<16^255*this.g<<8^255*this.b<<0},getHexString:function(){return("000000"+this.getHex().toString(16)).slice(-6)},getHSL:function(a){var b,c,d=a||{h:0,s:0,l:0},e=this.r,f=this.g,g=this.b,h=Math.max(e,f,g),i=Math.min(e,f,g),j=(i+h)/2;if(i===h)b=0,c=0;else{var k=h-i;switch(c=.5>=j?k/(h+i):k/(2-h-i),h){case e:b=(f-g)/k+(g>f?6:0);break;case f:b=(g-e)/k+2;break;case g:b=(e-f)/k+4}b/=6}return d.h=b,d.s=c,d.l=j,d},getStyle:function(){return"rgb("+(255*this.r|0)+","+(255*this.g|0)+","+(255*this.b|0)+")"},offsetHSL:function(a,b,c){var d=this.getHSL();return d.h+=a,d.s+=b,d.l+=c,this.setHSL(d.h,d.s,d.l),this},add:function(a){return this.r+=a.r,this.g+=a.g,this.b+=a.b,this},addColors:function(a,b){return this.r=a.r+b.r,this.g=a.g+b.g,this.b=a.b+b.b,this},addScalar:function(a){return this.r+=a,this.g+=a,this.b+=a,this},multiply:function(a){return this.r*=a.r,this.g*=a.g,this.b*=a.b,this},multiplyScalar:function(a){return this.r*=a,this.g*=a,this.b*=a,this},lerp:function(a,b){return this.r+=(a.r-this.r)*b,this.g+=(a.g-this.g)*b,this.b+=(a.b-this.b)*b,this},equals:function(a){return a.r===this.r&&a.g===this.g&&a.b===this.b},fromArray:function(a){return this.r=a[0],this.g=a[1],this.b=a[2],this},toArray:function(){return[this.r,this.g,this.b]},clone:function(){return(new a.Color).setRGB(this.r,this.g,this.b)}},a.ColorKeywords={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074},a.Quaternion=function(a,b,c,d){this._x=a||0,this._y=b||0,this._z=c||0,this._w=void 0!==d?d:1},a.Quaternion.prototype={constructor:a.Quaternion,_x:0,_y:0,_z:0,_w:0,get x(){return this._x},set x(a){this._x=a,this.onChangeCallback()},get y(){return this._y},set y(a){this._y=a,this.onChangeCallback()},get z(){return this._z},set z(a){this._z=a,this.onChangeCallback()},get w(){return this._w},set w(a){this._w=a,this.onChangeCallback()},set:function(a,b,c,d){return this._x=a,this._y=b,this._z=c,this._w=d,this.onChangeCallback(),this},copy:function(a){return this._x=a.x,this._y=a.y,this._z=a.z,this._w=a.w,this.onChangeCallback(),this},setFromEuler:function(b,c){if(b instanceof a.Euler==!1)throw new Error("THREE.Quaternion: .setFromEuler() now expects a Euler rotation rather than a Vector3 and order.");var d=Math.cos(b._x/2),e=Math.cos(b._y/2),f=Math.cos(b._z/2),g=Math.sin(b._x/2),h=Math.sin(b._y/2),i=Math.sin(b._z/2);return"XYZ"===b.order?(this._x=g*e*f+d*h*i,this._y=d*h*f-g*e*i,this._z=d*e*i+g*h*f,this._w=d*e*f-g*h*i):"YXZ"===b.order?(this._x=g*e*f+d*h*i,this._y=d*h*f-g*e*i,this._z=d*e*i-g*h*f,this._w=d*e*f+g*h*i):"ZXY"===b.order?(this._x=g*e*f-d*h*i,this._y=d*h*f+g*e*i,this._z=d*e*i+g*h*f,this._w=d*e*f-g*h*i):"ZYX"===b.order?(this._x=g*e*f-d*h*i,this._y=d*h*f+g*e*i,this._z=d*e*i-g*h*f,this._w=d*e*f+g*h*i):"YZX"===b.order?(this._x=g*e*f+d*h*i,this._y=d*h*f+g*e*i,this._z=d*e*i-g*h*f,this._w=d*e*f-g*h*i):"XZY"===b.order&&(this._x=g*e*f-d*h*i,this._y=d*h*f-g*e*i,this._z=d*e*i+g*h*f,this._w=d*e*f+g*h*i),c!==!1&&this.onChangeCallback(),this},setFromAxisAngle:function(a,b){var c=b/2,d=Math.sin(c);return this._x=a.x*d,this._y=a.y*d,this._z=a.z*d,this._w=Math.cos(c),this.onChangeCallback(),this},setFromRotationMatrix:function(a){var b,c=a.elements,d=c[0],e=c[4],f=c[8],g=c[1],h=c[5],i=c[9],j=c[2],k=c[6],l=c[10],m=d+h+l;return m>0?(b=.5/Math.sqrt(m+1),this._w=.25/b,this._x=(k-i)*b,this._y=(f-j)*b,this._z=(g-e)*b):d>h&&d>l?(b=2*Math.sqrt(1+d-h-l),this._w=(k-i)/b,this._x=.25*b,this._y=(e+g)/b,this._z=(f+j)/b):h>l?(b=2*Math.sqrt(1+h-d-l),this._w=(f-j)/b,this._x=(e+g)/b,this._y=.25*b,this._z=(i+k)/b):(b=2*Math.sqrt(1+l-d-h),this._w=(g-e)/b,this._x=(f+j)/b,this._y=(i+k)/b,this._z=.25*b),this.onChangeCallback(),this},setFromUnitVectors:function(){var b,c,d=1e-6;return function(e,f){return void 0===b&&(b=new a.Vector3),c=e.dot(f)+1,d>c?(c=0,Math.abs(e.x)>Math.abs(e.z)?b.set(-e.y,e.x,0):b.set(0,-e.z,e.y)):b.crossVectors(e,f),this._x=b.x,this._y=b.y,this._z=b.z,this._w=c,this.normalize(),this}}(),inverse:function(){return this.conjugate().normalize(),this},conjugate:function(){return this._x*=-1,this._y*=-1,this._z*=-1,this.onChangeCallback(),this},dot:function(a){return this._x*a._x+this._y*a._y+this._z*a._z+this._w*a._w},lengthSq:function(){return this._x*this._x+this._y*this._y+this._z*this._z+this._w*this._w},length:function(){return Math.sqrt(this._x*this._x+this._y*this._y+this._z*this._z+this._w*this._w)},normalize:function(){var a=this.length();return 0===a?(this._x=0,this._y=0,this._z=0,this._w=1):(a=1/a,this._x=this._x*a,this._y=this._y*a,this._z=this._z*a,this._w=this._w*a),this.onChangeCallback(),this},multiply:function(a,b){return void 0!==b?(console.warn("THREE.Quaternion: .multiply() now only accepts one argument. Use .multiplyQuaternions( a, b ) instead."),this.multiplyQuaternions(a,b)):this.multiplyQuaternions(this,a)},multiplyQuaternions:function(a,b){var c=a._x,d=a._y,e=a._z,f=a._w,g=b._x,h=b._y,i=b._z,j=b._w;return this._x=c*j+f*g+d*i-e*h,this._y=d*j+f*h+e*g-c*i,this._z=e*j+f*i+c*h-d*g,this._w=f*j-c*g-d*h-e*i,this.onChangeCallback(),this},multiplyVector3:function(a){return console.warn("THREE.Quaternion: .multiplyVector3() has been removed. Use is now vector.applyQuaternion( quaternion ) instead."),a.applyQuaternion(this)},slerp:function(a,b){if(0===b)return this;if(1===b)return this.copy(a);var c=this._x,d=this._y,e=this._z,f=this._w,g=f*a._w+c*a._x+d*a._y+e*a._z;if(0>g?(this._w=-a._w,this._x=-a._x,this._y=-a._y,this._z=-a._z,g=-g):this.copy(a),g>=1)return this._w=f,this._x=c,this._y=d,this._z=e,this;var h=Math.acos(g),i=Math.sqrt(1-g*g);if(Math.abs(i)<.001)return this._w=.5*(f+this._w),this._x=.5*(c+this._x),this._y=.5*(d+this._y),this._z=.5*(e+this._z),this;var j=Math.sin((1-b)*h)/i,k=Math.sin(b*h)/i;return this._w=f*j+this._w*k,this._x=c*j+this._x*k,this._y=d*j+this._y*k,this._z=e*j+this._z*k,this.onChangeCallback(),this},equals:function(a){return a._x===this._x&&a._y===this._y&&a._z===this._z&&a._w===this._w},fromArray:function(a,b){return void 0===b&&(b=0),this._x=a[b],this._y=a[b+1],this._z=a[b+2],this._w=a[b+3],this.onChangeCallback(),this},toArray:function(a,b){return void 0===a&&(a=[]),void 0===b&&(b=0),a[b]=this._x,a[b+1]=this._y,a[b+2]=this._z,a[b+3]=this._w,a},onChange:function(a){return this.onChangeCallback=a,this},onChangeCallback:function(){},clone:function(){return new a.Quaternion(this._x,this._y,this._z,this._w)}},a.Quaternion.slerp=function(a,b,c,d){return c.copy(a).slerp(b,d)},a.Vector2=function(a,b){this.x=a||0,this.y=b||0},a.Vector2.prototype={constructor:a.Vector2,set:function(a,b){return this.x=a,this.y=b,this},setX:function(a){return this.x=a,this},setY:function(a){return this.y=a,this},setComponent:function(a,b){switch(a){case 0:this.x=b;break;case 1:this.y=b;break;default:throw new Error("index is out of range: "+a)}},getComponent:function(a){switch(a){case 0:return this.x;case 1:return this.y;default:throw new Error("index is out of range: "+a)}},copy:function(a){return this.x=a.x,this.y=a.y,this},add:function(a,b){return void 0!==b?(console.warn("THREE.Vector2: .add() now only accepts one argument. Use .addVectors( a, b ) instead."),this.addVectors(a,b)):(this.x+=a.x,this.y+=a.y,this)},addVectors:function(a,b){return this.x=a.x+b.x,this.y=a.y+b.y,this},addScalar:function(a){return this.x+=a,this.y+=a,this},sub:function(a,b){return void 0!==b?(console.warn("THREE.Vector2: .sub() now only accepts one argument. Use .subVectors( a, b ) instead."),this.subVectors(a,b)):(this.x-=a.x,this.y-=a.y,this)},subVectors:function(a,b){return this.x=a.x-b.x,this.y=a.y-b.y,this},multiply:function(a){return this.x*=a.x,this.y*=a.y,this},multiplyScalar:function(a){return this.x*=a,this.y*=a,this},divide:function(a){return this.x/=a.x,this.y/=a.y,this},divideScalar:function(a){if(0!==a){var b=1/a;this.x*=b,this.y*=b}else this.x=0,this.y=0;return this},min:function(a){return this.x>a.x&&(this.x=a.x),this.y>a.y&&(this.y=a.y),this},max:function(a){return this.x<a.x&&(this.x=a.x),this.y<a.y&&(this.y=a.y),this},clamp:function(a,b){return this.x<a.x?this.x=a.x:this.x>b.x&&(this.x=b.x),this.y<a.y?this.y=a.y:this.y>b.y&&(this.y=b.y),this},clampScalar:function(){var b,c;return function(d,e){return void 0===b&&(b=new a.Vector2,c=new a.Vector2),b.set(d,d),c.set(e,e),this.clamp(b,c)}}(),floor:function(){return this.x=Math.floor(this.x),this.y=Math.floor(this.y),this},ceil:function(){return this.x=Math.ceil(this.x),this.y=Math.ceil(this.y),this},round:function(){return this.x=Math.round(this.x),this.y=Math.round(this.y),this},roundToZero:function(){return this.x=this.x<0?Math.ceil(this.x):Math.floor(this.x),this.y=this.y<0?Math.ceil(this.y):Math.floor(this.y),this},negate:function(){return this.x=-this.x,this.y=-this.y,this},dot:function(a){return this.x*a.x+this.y*a.y},lengthSq:function(){return this.x*this.x+this.y*this.y},length:function(){return Math.sqrt(this.x*this.x+this.y*this.y)},normalize:function(){return this.divideScalar(this.length())},distanceTo:function(a){return Math.sqrt(this.distanceToSquared(a))},distanceToSquared:function(a){var b=this.x-a.x,c=this.y-a.y;return b*b+c*c},setLength:function(a){var b=this.length();return 0!==b&&a!==b&&this.multiplyScalar(a/b),this},lerp:function(a,b){return this.x+=(a.x-this.x)*b,this.y+=(a.y-this.y)*b,this},equals:function(a){return a.x===this.x&&a.y===this.y},fromArray:function(a,b){return void 0===b&&(b=0),this.x=a[b],this.y=a[b+1],this},toArray:function(a,b){return void 0===a&&(a=[]),void 0===b&&(b=0),a[b]=this.x,a[b+1]=this.y,a},clone:function(){return new a.Vector2(this.x,this.y)}},a.Vector3=function(a,b,c){this.x=a||0,this.y=b||0,this.z=c||0},a.Vector3.prototype={constructor:a.Vector3,set:function(a,b,c){return this.x=a,this.y=b,this.z=c,this},setX:function(a){return this.x=a,this},setY:function(a){return this.y=a,this},setZ:function(a){return this.z=a,this},setComponent:function(a,b){switch(a){case 0:this.x=b;break;case 1:this.y=b;break;case 2:this.z=b;break;default:throw new Error("index is out of range: "+a)}},getComponent:function(a){switch(a){case 0:return this.x;case 1:return this.y;case 2:return this.z;default:throw new Error("index is out of range: "+a)}},copy:function(a){return this.x=a.x,this.y=a.y,this.z=a.z,this},add:function(a,b){return void 0!==b?(console.warn("THREE.Vector3: .add() now only accepts one argument. Use .addVectors( a, b ) instead."),this.addVectors(a,b)):(this.x+=a.x,this.y+=a.y,this.z+=a.z,this)},addScalar:function(a){return this.x+=a,this.y+=a,this.z+=a,this},addVectors:function(a,b){return this.x=a.x+b.x,this.y=a.y+b.y,this.z=a.z+b.z,this},sub:function(a,b){return void 0!==b?(console.warn("THREE.Vector3: .sub() now only accepts one argument. Use .subVectors( a, b ) instead."),this.subVectors(a,b)):(this.x-=a.x,this.y-=a.y,this.z-=a.z,this)},subVectors:function(a,b){return this.x=a.x-b.x,this.y=a.y-b.y,this.z=a.z-b.z,this},multiply:function(a,b){return void 0!==b?(console.warn("THREE.Vector3: .multiply() now only accepts one argument. Use .multiplyVectors( a, b ) instead."),this.multiplyVectors(a,b)):(this.x*=a.x,this.y*=a.y,this.z*=a.z,this)},multiplyScalar:function(a){return this.x*=a,this.y*=a,this.z*=a,this},multiplyVectors:function(a,b){return this.x=a.x*b.x,this.y=a.y*b.y,this.z=a.z*b.z,this},applyEuler:function(){var b;return function(c){return c instanceof a.Euler==!1&&console.error("THREE.Vector3: .applyEuler() now expects a Euler rotation rather than a Vector3 and order."),void 0===b&&(b=new a.Quaternion),this.applyQuaternion(b.setFromEuler(c)),this}}(),applyAxisAngle:function(){var b;return function(c,d){return void 0===b&&(b=new a.Quaternion),this.applyQuaternion(b.setFromAxisAngle(c,d)),this}}(),applyMatrix3:function(a){var b=this.x,c=this.y,d=this.z,e=a.elements;return this.x=e[0]*b+e[3]*c+e[6]*d,this.y=e[1]*b+e[4]*c+e[7]*d,this.z=e[2]*b+e[5]*c+e[8]*d,this},applyMatrix4:function(a){var b=this.x,c=this.y,d=this.z,e=a.elements;return this.x=e[0]*b+e[4]*c+e[8]*d+e[12],this.y=e[1]*b+e[5]*c+e[9]*d+e[13],this.z=e[2]*b+e[6]*c+e[10]*d+e[14],this},applyProjection:function(a){var b=this.x,c=this.y,d=this.z,e=a.elements,f=1/(e[3]*b+e[7]*c+e[11]*d+e[15]);return this.x=(e[0]*b+e[4]*c+e[8]*d+e[12])*f,this.y=(e[1]*b+e[5]*c+e[9]*d+e[13])*f,this.z=(e[2]*b+e[6]*c+e[10]*d+e[14])*f,this},applyQuaternion:function(a){var b=this.x,c=this.y,d=this.z,e=a.x,f=a.y,g=a.z,h=a.w,i=h*b+f*d-g*c,j=h*c+g*b-e*d,k=h*d+e*c-f*b,l=-e*b-f*c-g*d;return this.x=i*h+l*-e+j*-g-k*-f,this.y=j*h+l*-f+k*-e-i*-g,this.z=k*h+l*-g+i*-f-j*-e,this},project:function(){var b;return function(c){return void 0===b&&(b=new a.Matrix4),b.multiplyMatrices(c.projectionMatrix,b.getInverse(c.matrixWorld)),this.applyProjection(b)}}(),unproject:function(){var b;return function(c){return void 0===b&&(b=new a.Matrix4),b.multiplyMatrices(c.matrixWorld,b.getInverse(c.projectionMatrix)),this.applyProjection(b)}}(),transformDirection:function(a){var b=this.x,c=this.y,d=this.z,e=a.elements;return this.x=e[0]*b+e[4]*c+e[8]*d,this.y=e[1]*b+e[5]*c+e[9]*d,this.z=e[2]*b+e[6]*c+e[10]*d,this.normalize(),this},divide:function(a){return this.x/=a.x,this.y/=a.y,this.z/=a.z,this},divideScalar:function(a){if(0!==a){var b=1/a;this.x*=b,this.y*=b,this.z*=b}else this.x=0,this.y=0,this.z=0;return this},min:function(a){return this.x>a.x&&(this.x=a.x),this.y>a.y&&(this.y=a.y),this.z>a.z&&(this.z=a.z),this},max:function(a){return this.x<a.x&&(this.x=a.x),this.y<a.y&&(this.y=a.y),this.z<a.z&&(this.z=a.z),this},clamp:function(a,b){return this.x<a.x?this.x=a.x:this.x>b.x&&(this.x=b.x),this.y<a.y?this.y=a.y:this.y>b.y&&(this.y=b.y),this.z<a.z?this.z=a.z:this.z>b.z&&(this.z=b.z),this},clampScalar:function(){var b,c;return function(d,e){return void 0===b&&(b=new a.Vector3,c=new a.Vector3),b.set(d,d,d),c.set(e,e,e),this.clamp(b,c)}}(),floor:function(){return this.x=Math.floor(this.x),this.y=Math.floor(this.y),this.z=Math.floor(this.z),this},ceil:function(){return this.x=Math.ceil(this.x),this.y=Math.ceil(this.y),this.z=Math.ceil(this.z),this},round:function(){return this.x=Math.round(this.x),this.y=Math.round(this.y),this.z=Math.round(this.z),this},roundToZero:function(){return this.x=this.x<0?Math.ceil(this.x):Math.floor(this.x),this.y=this.y<0?Math.ceil(this.y):Math.floor(this.y),this.z=this.z<0?Math.ceil(this.z):Math.floor(this.z),this},negate:function(){return this.x=-this.x,this.y=-this.y,this.z=-this.z,this},dot:function(a){return this.x*a.x+this.y*a.y+this.z*a.z},lengthSq:function(){return this.x*this.x+this.y*this.y+this.z*this.z},length:function(){return Math.sqrt(this.x*this.x+this.y*this.y+this.z*this.z)},lengthManhattan:function(){return Math.abs(this.x)+Math.abs(this.y)+Math.abs(this.z)},normalize:function(){return this.divideScalar(this.length())},setLength:function(a){var b=this.length();return 0!==b&&a!==b&&this.multiplyScalar(a/b),this},lerp:function(a,b){return this.x+=(a.x-this.x)*b,this.y+=(a.y-this.y)*b,this.z+=(a.z-this.z)*b,this},cross:function(a,b){if(void 0!==b)return console.warn("THREE.Vector3: .cross() now only accepts one argument. Use .crossVectors( a, b ) instead."),this.crossVectors(a,b);var c=this.x,d=this.y,e=this.z;return this.x=d*a.z-e*a.y,this.y=e*a.x-c*a.z,this.z=c*a.y-d*a.x,this},crossVectors:function(a,b){var c=a.x,d=a.y,e=a.z,f=b.x,g=b.y,h=b.z;return this.x=d*h-e*g,this.y=e*f-c*h,this.z=c*g-d*f,this},projectOnVector:function(){var b,c;return function(d){return void 0===b&&(b=new a.Vector3),b.copy(d).normalize(),c=this.dot(b),this.copy(b).multiplyScalar(c)}}(),projectOnPlane:function(){var b;return function(c){return void 0===b&&(b=new a.Vector3),b.copy(this).projectOnVector(c),this.sub(b)}}(),reflect:function(){var b;return function(c){return void 0===b&&(b=new a.Vector3),this.sub(b.copy(c).multiplyScalar(2*this.dot(c)))}}(),angleTo:function(b){var c=this.dot(b)/(this.length()*b.length());return Math.acos(a.Math.clamp(c,-1,1))},distanceTo:function(a){return Math.sqrt(this.distanceToSquared(a))},distanceToSquared:function(a){var b=this.x-a.x,c=this.y-a.y,d=this.z-a.z;return b*b+c*c+d*d},setEulerFromRotationMatrix:function(a,b){console.error("THREE.Vector3: .setEulerFromRotationMatrix() has been removed. Use Euler.setFromRotationMatrix() instead.")},setEulerFromQuaternion:function(a,b){console.error("THREE.Vector3: .setEulerFromQuaternion() has been removed. Use Euler.setFromQuaternion() instead.")},getPositionFromMatrix:function(a){return console.warn("THREE.Vector3: .getPositionFromMatrix() has been renamed to .setFromMatrixPosition()."),this.setFromMatrixPosition(a)},getScaleFromMatrix:function(a){return console.warn("THREE.Vector3: .getScaleFromMatrix() has been renamed to .setFromMatrixScale()."),this.setFromMatrixScale(a)},getColumnFromMatrix:function(a,b){return console.warn("THREE.Vector3: .getColumnFromMatrix() has been renamed to .setFromMatrixColumn()."),this.setFromMatrixColumn(a,b)},setFromMatrixPosition:function(a){return this.x=a.elements[12],this.y=a.elements[13],this.z=a.elements[14],this},setFromMatrixScale:function(a){var b=this.set(a.elements[0],a.elements[1],a.elements[2]).length(),c=this.set(a.elements[4],a.elements[5],a.elements[6]).length(),d=this.set(a.elements[8],a.elements[9],a.elements[10]).length();return this.x=b,this.y=c,this.z=d,this},setFromMatrixColumn:function(a,b){var c=4*a,d=b.elements;return this.x=d[c],this.y=d[c+1],this.z=d[c+2],this},equals:function(a){return a.x===this.x&&a.y===this.y&&a.z===this.z},fromArray:function(a,b){return void 0===b&&(b=0),this.x=a[b],this.y=a[b+1],this.z=a[b+2],this},toArray:function(a,b){return void 0===a&&(a=[]),void 0===b&&(b=0),a[b]=this.x,a[b+1]=this.y,a[b+2]=this.z,a},clone:function(){return new a.Vector3(this.x,this.y,this.z)}},a.Vector4=function(a,b,c,d){this.x=a||0,this.y=b||0,this.z=c||0,this.w=void 0!==d?d:1},a.Vector4.prototype={constructor:a.Vector4,set:function(a,b,c,d){return this.x=a,this.y=b,this.z=c,this.w=d,this},setX:function(a){return this.x=a,this},setY:function(a){return this.y=a,this},setZ:function(a){return this.z=a,this},setW:function(a){return this.w=a,this},setComponent:function(a,b){switch(a){case 0:this.x=b;break;case 1:this.y=b;break;case 2:this.z=b;break;case 3:this.w=b;break;default:throw new Error("index is out of range: "+a)}},getComponent:function(a){switch(a){case 0:return this.x;case 1:return this.y;case 2:return this.z;case 3:return this.w;default:throw new Error("index is out of range: "+a)}},copy:function(a){return this.x=a.x,this.y=a.y,this.z=a.z,this.w=void 0!==a.w?a.w:1,this},add:function(a,b){return void 0!==b?(console.warn("THREE.Vector4: .add() now only accepts one argument. Use .addVectors( a, b ) instead."),this.addVectors(a,b)):(this.x+=a.x,this.y+=a.y,this.z+=a.z,this.w+=a.w,this)},addScalar:function(a){return this.x+=a,this.y+=a,this.z+=a,this.w+=a,this},addVectors:function(a,b){return this.x=a.x+b.x,this.y=a.y+b.y,this.z=a.z+b.z,this.w=a.w+b.w,this},sub:function(a,b){return void 0!==b?(console.warn("THREE.Vector4: .sub() now only accepts one argument. Use .subVectors( a, b ) instead."),this.subVectors(a,b)):(this.x-=a.x,this.y-=a.y,this.z-=a.z,this.w-=a.w,this)},subVectors:function(a,b){return this.x=a.x-b.x,this.y=a.y-b.y,this.z=a.z-b.z,this.w=a.w-b.w,this},multiplyScalar:function(a){return this.x*=a,this.y*=a,this.z*=a,this.w*=a,this},applyMatrix4:function(a){var b=this.x,c=this.y,d=this.z,e=this.w,f=a.elements;return this.x=f[0]*b+f[4]*c+f[8]*d+f[12]*e,this.y=f[1]*b+f[5]*c+f[9]*d+f[13]*e,this.z=f[2]*b+f[6]*c+f[10]*d+f[14]*e,this.w=f[3]*b+f[7]*c+f[11]*d+f[15]*e,this},divideScalar:function(a){if(0!==a){var b=1/a;this.x*=b,this.y*=b,this.z*=b,this.w*=b}else this.x=0,this.y=0,this.z=0,this.w=1;return this},setAxisAngleFromQuaternion:function(a){this.w=2*Math.acos(a.w);var b=Math.sqrt(1-a.w*a.w);return 1e-4>b?(this.x=1,this.y=0,this.z=0):(this.x=a.x/b,this.y=a.y/b,this.z=a.z/b),this},setAxisAngleFromRotationMatrix:function(a){var b,c,d,e,f=.01,g=.1,h=a.elements,i=h[0],j=h[4],k=h[8],l=h[1],m=h[5],n=h[9],o=h[2],p=h[6],q=h[10];if(Math.abs(j-l)<f&&Math.abs(k-o)<f&&Math.abs(n-p)<f){if(Math.abs(j+l)<g&&Math.abs(k+o)<g&&Math.abs(n+p)<g&&Math.abs(i+m+q-3)<g)return this.set(1,0,0,0),this;b=Math.PI;var r=(i+1)/2,s=(m+1)/2,t=(q+1)/2,u=(j+l)/4,v=(k+o)/4,w=(n+p)/4;return r>s&&r>t?f>r?(c=0,d=.*********,e=.*********):(c=Math.sqrt(r),d=u/c,e=v/c):s>t?f>s?(c=.*********,d=0,e=.*********):(d=Math.sqrt(s),c=u/d,e=w/d):f>t?(c=.*********,d=.*********,e=0):(e=Math.sqrt(t),c=v/e,d=w/e),this.set(c,d,e,b),this}var x=Math.sqrt((p-n)*(p-n)+(k-o)*(k-o)+(l-j)*(l-j));return Math.abs(x)<.001&&(x=1),this.x=(p-n)/x,this.y=(k-o)/x,this.z=(l-j)/x,this.w=Math.acos((i+m+q-1)/2),this},min:function(a){return this.x>a.x&&(this.x=a.x),this.y>a.y&&(this.y=a.y),this.z>a.z&&(this.z=a.z),this.w>a.w&&(this.w=a.w),this},max:function(a){return this.x<a.x&&(this.x=a.x),this.y<a.y&&(this.y=a.y),this.z<a.z&&(this.z=a.z),this.w<a.w&&(this.w=a.w),this},clamp:function(a,b){return this.x<a.x?this.x=a.x:this.x>b.x&&(this.x=b.x),this.y<a.y?this.y=a.y:this.y>b.y&&(this.y=b.y),this.z<a.z?this.z=a.z:this.z>b.z&&(this.z=b.z),this.w<a.w?this.w=a.w:this.w>b.w&&(this.w=b.w),this},clampScalar:function(){var b,c;return function(d,e){return void 0===b&&(b=new a.Vector4,c=new a.Vector4),b.set(d,d,d,d),c.set(e,e,e,e),this.clamp(b,c)}}(),floor:function(){return this.x=Math.floor(this.x),this.y=Math.floor(this.y),this.z=Math.floor(this.z),this.w=Math.floor(this.w),this},ceil:function(){return this.x=Math.ceil(this.x),this.y=Math.ceil(this.y),this.z=Math.ceil(this.z),this.w=Math.ceil(this.w),this},round:function(){return this.x=Math.round(this.x),this.y=Math.round(this.y),this.z=Math.round(this.z),this.w=Math.round(this.w),this},roundToZero:function(){return this.x=this.x<0?Math.ceil(this.x):Math.floor(this.x),this.y=this.y<0?Math.ceil(this.y):Math.floor(this.y),this.z=this.z<0?Math.ceil(this.z):Math.floor(this.z),this.w=this.w<0?Math.ceil(this.w):Math.floor(this.w),this},negate:function(){return this.x=-this.x,this.y=-this.y,this.z=-this.z,this.w=-this.w,this},dot:function(a){return this.x*a.x+this.y*a.y+this.z*a.z+this.w*a.w},lengthSq:function(){return this.x*this.x+this.y*this.y+this.z*this.z+this.w*this.w},length:function(){return Math.sqrt(this.x*this.x+this.y*this.y+this.z*this.z+this.w*this.w)},lengthManhattan:function(){return Math.abs(this.x)+Math.abs(this.y)+Math.abs(this.z)+Math.abs(this.w)},normalize:function(){return this.divideScalar(this.length())},setLength:function(a){var b=this.length();return 0!==b&&a!==b&&this.multiplyScalar(a/b),this},lerp:function(a,b){return this.x+=(a.x-this.x)*b,this.y+=(a.y-this.y)*b,this.z+=(a.z-this.z)*b,this.w+=(a.w-this.w)*b,this},equals:function(a){return a.x===this.x&&a.y===this.y&&a.z===this.z&&a.w===this.w},fromArray:function(a,b){return void 0===b&&(b=0),this.x=a[b],this.y=a[b+1],this.z=a[b+2],this.w=a[b+3],this},toArray:function(a,b){return void 0===a&&(a=[]),void 0===b&&(b=0),a[b]=this.x,a[b+1]=this.y,a[b+2]=this.z,a[b+3]=this.w,a},clone:function(){return new a.Vector4(this.x,this.y,this.z,this.w)}},a.Euler=function(b,c,d,e){this._x=b||0,this._y=c||0,this._z=d||0,this._order=e||a.Euler.DefaultOrder},a.Euler.RotationOrders=["XYZ","YZX","ZXY","XZY","YXZ","ZYX"],a.Euler.DefaultOrder="XYZ",a.Euler.prototype={constructor:a.Euler,_x:0,_y:0,_z:0,_order:a.Euler.DefaultOrder,get x(){return this._x},set x(a){this._x=a,this.onChangeCallback()},get y(){return this._y},set y(a){this._y=a,this.onChangeCallback()},get z(){return this._z},set z(a){this._z=a,this.onChangeCallback()},get order(){return this._order},set order(a){this._order=a,this.onChangeCallback()},set:function(a,b,c,d){return this._x=a,this._y=b,this._z=c,this._order=d||this._order,this.onChangeCallback(),this},copy:function(a){return this._x=a._x,this._y=a._y,this._z=a._z,this._order=a._order,this.onChangeCallback(),this},setFromRotationMatrix:function(b,c){var d=a.Math.clamp,e=b.elements,f=e[0],g=e[4],h=e[8],i=e[1],j=e[5],k=e[9],l=e[2],m=e[6],n=e[10];return c=c||this._order,"XYZ"===c?(this._y=Math.asin(d(h,-1,1)),Math.abs(h)<.99999?(this._x=Math.atan2(-k,n),this._z=Math.atan2(-g,f)):(this._x=Math.atan2(m,j),this._z=0)):"YXZ"===c?(this._x=Math.asin(-d(k,-1,1)),Math.abs(k)<.99999?(this._y=Math.atan2(h,n),this._z=Math.atan2(i,j)):(this._y=Math.atan2(-l,f),this._z=0)):"ZXY"===c?(this._x=Math.asin(d(m,-1,1)),Math.abs(m)<.99999?(this._y=Math.atan2(-l,n),this._z=Math.atan2(-g,j)):(this._y=0,this._z=Math.atan2(i,f))):"ZYX"===c?(this._y=Math.asin(-d(l,-1,1)),Math.abs(l)<.99999?(this._x=Math.atan2(m,n),this._z=Math.atan2(i,f)):(this._x=0,this._z=Math.atan2(-g,j))):"YZX"===c?(this._z=Math.asin(d(i,-1,1)),Math.abs(i)<.99999?(this._x=Math.atan2(-k,j),this._y=Math.atan2(-l,f)):(this._x=0,this._y=Math.atan2(h,n))):"XZY"===c?(this._z=Math.asin(-d(g,-1,1)),Math.abs(g)<.99999?(this._x=Math.atan2(m,j),this._y=Math.atan2(h,f)):(this._x=Math.atan2(-k,n),this._y=0)):console.warn("THREE.Euler: .setFromRotationMatrix() given unsupported order: "+c),this._order=c,this.onChangeCallback(),this},setFromQuaternion:function(b,c,d){var e=a.Math.clamp,f=b.x*b.x,g=b.y*b.y,h=b.z*b.z,i=b.w*b.w;return c=c||this._order,"XYZ"===c?(this._x=Math.atan2(2*(b.x*b.w-b.y*b.z),i-f-g+h),this._y=Math.asin(e(2*(b.x*b.z+b.y*b.w),-1,1)),this._z=Math.atan2(2*(b.z*b.w-b.x*b.y),i+f-g-h)):"YXZ"===c?(this._x=Math.asin(e(2*(b.x*b.w-b.y*b.z),-1,1)),this._y=Math.atan2(2*(b.x*b.z+b.y*b.w),i-f-g+h),this._z=Math.atan2(2*(b.x*b.y+b.z*b.w),i-f+g-h)):"ZXY"===c?(this._x=Math.asin(e(2*(b.x*b.w+b.y*b.z),-1,1)),this._y=Math.atan2(2*(b.y*b.w-b.z*b.x),i-f-g+h),this._z=Math.atan2(2*(b.z*b.w-b.x*b.y),i-f+g-h)):"ZYX"===c?(this._x=Math.atan2(2*(b.x*b.w+b.z*b.y),i-f-g+h),this._y=Math.asin(e(2*(b.y*b.w-b.x*b.z),-1,1)),this._z=Math.atan2(2*(b.x*b.y+b.z*b.w),i+f-g-h)):"YZX"===c?(this._x=Math.atan2(2*(b.x*b.w-b.z*b.y),i-f+g-h),this._y=Math.atan2(2*(b.y*b.w-b.x*b.z),i+f-g-h),this._z=Math.asin(e(2*(b.x*b.y+b.z*b.w),-1,1))):"XZY"===c?(this._x=Math.atan2(2*(b.x*b.w+b.y*b.z),i-f+g-h),this._y=Math.atan2(2*(b.x*b.z+b.y*b.w),i+f-g-h),this._z=Math.asin(e(2*(b.z*b.w-b.x*b.y),-1,1))):console.warn("THREE.Euler: .setFromQuaternion() given unsupported order: "+c),this._order=c,d!==!1&&this.onChangeCallback(),this},reorder:function(){var b=new a.Quaternion;return function(a){b.setFromEuler(this),this.setFromQuaternion(b,a)}}(),equals:function(a){return a._x===this._x&&a._y===this._y&&a._z===this._z&&a._order===this._order},fromArray:function(a){return this._x=a[0],this._y=a[1],this._z=a[2],void 0!==a[3]&&(this._order=a[3]),this.onChangeCallback(),this},toArray:function(){return[this._x,this._y,this._z,this._order]},onChange:function(a){return this.onChangeCallback=a,this},onChangeCallback:function(){},clone:function(){return new a.Euler(this._x,this._y,this._z,this._order)}},a.Line3=function(b,c){this.start=void 0!==b?b:new a.Vector3,this.end=void 0!==c?c:new a.Vector3},a.Line3.prototype={constructor:a.Line3,set:function(a,b){return this.start.copy(a),this.end.copy(b),this},copy:function(a){return this.start.copy(a.start),this.end.copy(a.end),this},center:function(b){var c=b||new a.Vector3;return c.addVectors(this.start,this.end).multiplyScalar(.5)},delta:function(b){var c=b||new a.Vector3;return c.subVectors(this.end,this.start)},distanceSq:function(){return this.start.distanceToSquared(this.end)},distance:function(){return this.start.distanceTo(this.end)},at:function(b,c){var d=c||new a.Vector3;return this.delta(d).multiplyScalar(b).add(this.start)},closestPointToPointParameter:function(){var b=new a.Vector3,c=new a.Vector3;return function(d,e){b.subVectors(d,this.start),c.subVectors(this.end,this.start);
var f=c.dot(c),g=c.dot(b),h=g/f;return e&&(h=a.Math.clamp(h,0,1)),h}}(),closestPointToPoint:function(b,c,d){var e=this.closestPointToPointParameter(b,c),f=d||new a.Vector3;return this.delta(f).multiplyScalar(e).add(this.start)},applyMatrix4:function(a){return this.start.applyMatrix4(a),this.end.applyMatrix4(a),this},equals:function(a){return a.start.equals(this.start)&&a.end.equals(this.end)},clone:function(){return(new a.Line3).copy(this)}},a.Box2=function(b,c){this.min=void 0!==b?b:new a.Vector2(1/0,1/0),this.max=void 0!==c?c:new a.Vector2(-(1/0),-(1/0))},a.Box2.prototype={constructor:a.Box2,set:function(a,b){return this.min.copy(a),this.max.copy(b),this},setFromPoints:function(a){this.makeEmpty();for(var b=0,c=a.length;c>b;b++)this.expandByPoint(a[b]);return this},setFromCenterAndSize:function(){var b=new a.Vector2;return function(a,c){var d=b.copy(c).multiplyScalar(.5);return this.min.copy(a).sub(d),this.max.copy(a).add(d),this}}(),copy:function(a){return this.min.copy(a.min),this.max.copy(a.max),this},makeEmpty:function(){return this.min.x=this.min.y=1/0,this.max.x=this.max.y=-(1/0),this},empty:function(){return this.max.x<this.min.x||this.max.y<this.min.y},center:function(b){var c=b||new a.Vector2;return c.addVectors(this.min,this.max).multiplyScalar(.5)},size:function(b){var c=b||new a.Vector2;return c.subVectors(this.max,this.min)},expandByPoint:function(a){return this.min.min(a),this.max.max(a),this},expandByVector:function(a){return this.min.sub(a),this.max.add(a),this},expandByScalar:function(a){return this.min.addScalar(-a),this.max.addScalar(a),this},containsPoint:function(a){return a.x<this.min.x||a.x>this.max.x||a.y<this.min.y||a.y>this.max.y?!1:!0},containsBox:function(a){return this.min.x<=a.min.x&&a.max.x<=this.max.x&&this.min.y<=a.min.y&&a.max.y<=this.max.y?!0:!1},getParameter:function(b,c){var d=c||new a.Vector2;return d.set((b.x-this.min.x)/(this.max.x-this.min.x),(b.y-this.min.y)/(this.max.y-this.min.y))},isIntersectionBox:function(a){return a.max.x<this.min.x||a.min.x>this.max.x||a.max.y<this.min.y||a.min.y>this.max.y?!1:!0},clampPoint:function(b,c){var d=c||new a.Vector2;return d.copy(b).clamp(this.min,this.max)},distanceToPoint:function(){var b=new a.Vector2;return function(a){var c=b.copy(a).clamp(this.min,this.max);return c.sub(a).length()}}(),intersect:function(a){return this.min.max(a.min),this.max.min(a.max),this},union:function(a){return this.min.min(a.min),this.max.max(a.max),this},translate:function(a){return this.min.add(a),this.max.add(a),this},equals:function(a){return a.min.equals(this.min)&&a.max.equals(this.max)},clone:function(){return(new a.Box2).copy(this)}},a.Box3=function(b,c){this.min=void 0!==b?b:new a.Vector3(1/0,1/0,1/0),this.max=void 0!==c?c:new a.Vector3(-(1/0),-(1/0),-(1/0))},a.Box3.prototype={constructor:a.Box3,set:function(a,b){return this.min.copy(a),this.max.copy(b),this},setFromPoints:function(a){this.makeEmpty();for(var b=0,c=a.length;c>b;b++)this.expandByPoint(a[b]);return this},setFromCenterAndSize:function(){var b=new a.Vector3;return function(a,c){var d=b.copy(c).multiplyScalar(.5);return this.min.copy(a).sub(d),this.max.copy(a).add(d),this}}(),setFromObject:function(){var b=new a.Vector3;return function(c){var d=this;return c.updateMatrixWorld(!0),this.makeEmpty(),c.traverse(function(c){var e=c.geometry;if(void 0!==e)if(e instanceof a.Geometry)for(var f=e.vertices,g=0,h=f.length;h>g;g++)b.copy(f[g]),b.applyMatrix4(c.matrixWorld),d.expandByPoint(b);else if(e instanceof a.BufferGeometry&&void 0!==e.attributes.position)for(var i=e.attributes.position.array,g=0,h=i.length;h>g;g+=3)b.set(i[g],i[g+1],i[g+2]),b.applyMatrix4(c.matrixWorld),d.expandByPoint(b)}),this}}(),copy:function(a){return this.min.copy(a.min),this.max.copy(a.max),this},makeEmpty:function(){return this.min.x=this.min.y=this.min.z=1/0,this.max.x=this.max.y=this.max.z=-(1/0),this},empty:function(){return this.max.x<this.min.x||this.max.y<this.min.y||this.max.z<this.min.z},center:function(b){var c=b||new a.Vector3;return c.addVectors(this.min,this.max).multiplyScalar(.5)},size:function(b){var c=b||new a.Vector3;return c.subVectors(this.max,this.min)},expandByPoint:function(a){return this.min.min(a),this.max.max(a),this},expandByVector:function(a){return this.min.sub(a),this.max.add(a),this},expandByScalar:function(a){return this.min.addScalar(-a),this.max.addScalar(a),this},containsPoint:function(a){return a.x<this.min.x||a.x>this.max.x||a.y<this.min.y||a.y>this.max.y||a.z<this.min.z||a.z>this.max.z?!1:!0},containsBox:function(a){return this.min.x<=a.min.x&&a.max.x<=this.max.x&&this.min.y<=a.min.y&&a.max.y<=this.max.y&&this.min.z<=a.min.z&&a.max.z<=this.max.z?!0:!1},getParameter:function(b,c){var d=c||new a.Vector3;return d.set((b.x-this.min.x)/(this.max.x-this.min.x),(b.y-this.min.y)/(this.max.y-this.min.y),(b.z-this.min.z)/(this.max.z-this.min.z))},isIntersectionBox:function(a){return a.max.x<this.min.x||a.min.x>this.max.x||a.max.y<this.min.y||a.min.y>this.max.y||a.max.z<this.min.z||a.min.z>this.max.z?!1:!0},clampPoint:function(b,c){var d=c||new a.Vector3;return d.copy(b).clamp(this.min,this.max)},distanceToPoint:function(){var b=new a.Vector3;return function(a){var c=b.copy(a).clamp(this.min,this.max);return c.sub(a).length()}}(),getBoundingSphere:function(){var b=new a.Vector3;return function(c){var d=c||new a.Sphere;return d.center=this.center(),d.radius=.5*this.size(b).length(),d}}(),intersect:function(a){return this.min.max(a.min),this.max.min(a.max),this},union:function(a){return this.min.min(a.min),this.max.max(a.max),this},applyMatrix4:function(){var b=[new a.Vector3,new a.Vector3,new a.Vector3,new a.Vector3,new a.Vector3,new a.Vector3,new a.Vector3,new a.Vector3];return function(a){return b[0].set(this.min.x,this.min.y,this.min.z).applyMatrix4(a),b[1].set(this.min.x,this.min.y,this.max.z).applyMatrix4(a),b[2].set(this.min.x,this.max.y,this.min.z).applyMatrix4(a),b[3].set(this.min.x,this.max.y,this.max.z).applyMatrix4(a),b[4].set(this.max.x,this.min.y,this.min.z).applyMatrix4(a),b[5].set(this.max.x,this.min.y,this.max.z).applyMatrix4(a),b[6].set(this.max.x,this.max.y,this.min.z).applyMatrix4(a),b[7].set(this.max.x,this.max.y,this.max.z).applyMatrix4(a),this.makeEmpty(),this.setFromPoints(b),this}}(),translate:function(a){return this.min.add(a),this.max.add(a),this},equals:function(a){return a.min.equals(this.min)&&a.max.equals(this.max)},clone:function(){return(new a.Box3).copy(this)}},a.Matrix3=function(){this.elements=new Float32Array([1,0,0,0,1,0,0,0,1]),arguments.length>0&&console.error("THREE.Matrix3: the constructor no longer reads arguments. use .set() instead.")},a.Matrix3.prototype={constructor:a.Matrix3,set:function(a,b,c,d,e,f,g,h,i){var j=this.elements;return j[0]=a,j[3]=b,j[6]=c,j[1]=d,j[4]=e,j[7]=f,j[2]=g,j[5]=h,j[8]=i,this},identity:function(){return this.set(1,0,0,0,1,0,0,0,1),this},copy:function(a){var b=a.elements;return this.set(b[0],b[3],b[6],b[1],b[4],b[7],b[2],b[5],b[8]),this},multiplyVector3:function(a){return console.warn("THREE.Matrix3: .multiplyVector3() has been removed. Use vector.applyMatrix3( matrix ) instead."),a.applyMatrix3(this)},multiplyVector3Array:function(a){return console.warn("THREE.Matrix3: .multiplyVector3Array() has been renamed. Use matrix.applyToVector3Array( array ) instead."),this.applyToVector3Array(a)},applyToVector3Array:function(){var b=new a.Vector3;return function(a,c,d){void 0===c&&(c=0),void 0===d&&(d=a.length);for(var e=0,f=c;d>e;e+=3,f+=3)b.x=a[f],b.y=a[f+1],b.z=a[f+2],b.applyMatrix3(this),a[f]=b.x,a[f+1]=b.y,a[f+2]=b.z;return a}}(),multiplyScalar:function(a){var b=this.elements;return b[0]*=a,b[3]*=a,b[6]*=a,b[1]*=a,b[4]*=a,b[7]*=a,b[2]*=a,b[5]*=a,b[8]*=a,this},determinant:function(){var a=this.elements,b=a[0],c=a[1],d=a[2],e=a[3],f=a[4],g=a[5],h=a[6],i=a[7],j=a[8];return b*f*j-b*g*i-c*e*j+c*g*h+d*e*i-d*f*h},getInverse:function(a,b){var c=a.elements,d=this.elements;d[0]=c[10]*c[5]-c[6]*c[9],d[1]=-c[10]*c[1]+c[2]*c[9],d[2]=c[6]*c[1]-c[2]*c[5],d[3]=-c[10]*c[4]+c[6]*c[8],d[4]=c[10]*c[0]-c[2]*c[8],d[5]=-c[6]*c[0]+c[2]*c[4],d[6]=c[9]*c[4]-c[5]*c[8],d[7]=-c[9]*c[0]+c[1]*c[8],d[8]=c[5]*c[0]-c[1]*c[4];var e=c[0]*d[0]+c[1]*d[3]+c[2]*d[6];if(0===e){var f="Matrix3.getInverse(): can't invert matrix, determinant is 0";if(b)throw new Error(f);return console.warn(f),this.identity(),this}return this.multiplyScalar(1/e),this},transpose:function(){var a,b=this.elements;return a=b[1],b[1]=b[3],b[3]=a,a=b[2],b[2]=b[6],b[6]=a,a=b[5],b[5]=b[7],b[7]=a,this},flattenToArrayOffset:function(a,b){var c=this.elements;return a[b]=c[0],a[b+1]=c[1],a[b+2]=c[2],a[b+3]=c[3],a[b+4]=c[4],a[b+5]=c[5],a[b+6]=c[6],a[b+7]=c[7],a[b+8]=c[8],a},getNormalMatrix:function(a){return this.getInverse(a).transpose(),this},transposeIntoArray:function(a){var b=this.elements;return a[0]=b[0],a[1]=b[3],a[2]=b[6],a[3]=b[1],a[4]=b[4],a[5]=b[7],a[6]=b[2],a[7]=b[5],a[8]=b[8],this},fromArray:function(a){return this.elements.set(a),this},toArray:function(){var a=this.elements;return[a[0],a[1],a[2],a[3],a[4],a[5],a[6],a[7],a[8]]},clone:function(){return(new a.Matrix3).fromArray(this.elements)}},a.Matrix4=function(){this.elements=new Float32Array([1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1]),arguments.length>0&&console.error("THREE.Matrix4: the constructor no longer reads arguments. use .set() instead.")},a.Matrix4.prototype={constructor:a.Matrix4,set:function(a,b,c,d,e,f,g,h,i,j,k,l,m,n,o,p){var q=this.elements;return q[0]=a,q[4]=b,q[8]=c,q[12]=d,q[1]=e,q[5]=f,q[9]=g,q[13]=h,q[2]=i,q[6]=j,q[10]=k,q[14]=l,q[3]=m,q[7]=n,q[11]=o,q[15]=p,this},identity:function(){return this.set(1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1),this},copy:function(a){return this.elements.set(a.elements),this},extractPosition:function(a){return console.warn("THREE.Matrix4: .extractPosition() has been renamed to .copyPosition()."),this.copyPosition(a)},copyPosition:function(a){var b=this.elements,c=a.elements;return b[12]=c[12],b[13]=c[13],b[14]=c[14],this},extractRotation:function(){var b=new a.Vector3;return function(a){var c=this.elements,d=a.elements,e=1/b.set(d[0],d[1],d[2]).length(),f=1/b.set(d[4],d[5],d[6]).length(),g=1/b.set(d[8],d[9],d[10]).length();return c[0]=d[0]*e,c[1]=d[1]*e,c[2]=d[2]*e,c[4]=d[4]*f,c[5]=d[5]*f,c[6]=d[6]*f,c[8]=d[8]*g,c[9]=d[9]*g,c[10]=d[10]*g,this}}(),makeRotationFromEuler:function(b){b instanceof a.Euler==!1&&console.error("THREE.Matrix: .makeRotationFromEuler() now expects a Euler rotation rather than a Vector3 and order.");var c=this.elements,d=b.x,e=b.y,f=b.z,g=Math.cos(d),h=Math.sin(d),i=Math.cos(e),j=Math.sin(e),k=Math.cos(f),l=Math.sin(f);if("XYZ"===b.order){var m=g*k,n=g*l,o=h*k,p=h*l;c[0]=i*k,c[4]=-i*l,c[8]=j,c[1]=n+o*j,c[5]=m-p*j,c[9]=-h*i,c[2]=p-m*j,c[6]=o+n*j,c[10]=g*i}else if("YXZ"===b.order){var q=i*k,r=i*l,s=j*k,t=j*l;c[0]=q+t*h,c[4]=s*h-r,c[8]=g*j,c[1]=g*l,c[5]=g*k,c[9]=-h,c[2]=r*h-s,c[6]=t+q*h,c[10]=g*i}else if("ZXY"===b.order){var q=i*k,r=i*l,s=j*k,t=j*l;c[0]=q-t*h,c[4]=-g*l,c[8]=s+r*h,c[1]=r+s*h,c[5]=g*k,c[9]=t-q*h,c[2]=-g*j,c[6]=h,c[10]=g*i}else if("ZYX"===b.order){var m=g*k,n=g*l,o=h*k,p=h*l;c[0]=i*k,c[4]=o*j-n,c[8]=m*j+p,c[1]=i*l,c[5]=p*j+m,c[9]=n*j-o,c[2]=-j,c[6]=h*i,c[10]=g*i}else if("YZX"===b.order){var u=g*i,v=g*j,w=h*i,x=h*j;c[0]=i*k,c[4]=x-u*l,c[8]=w*l+v,c[1]=l,c[5]=g*k,c[9]=-h*k,c[2]=-j*k,c[6]=v*l+w,c[10]=u-x*l}else if("XZY"===b.order){var u=g*i,v=g*j,w=h*i,x=h*j;c[0]=i*k,c[4]=-l,c[8]=j*k,c[1]=u*l+x,c[5]=g*k,c[9]=v*l-w,c[2]=w*l-v,c[6]=h*k,c[10]=x*l+u}return c[3]=0,c[7]=0,c[11]=0,c[12]=0,c[13]=0,c[14]=0,c[15]=1,this},setRotationFromQuaternion:function(a){return console.warn("THREE.Matrix4: .setRotationFromQuaternion() has been renamed to .makeRotationFromQuaternion()."),this.makeRotationFromQuaternion(a)},makeRotationFromQuaternion:function(a){var b=this.elements,c=a.x,d=a.y,e=a.z,f=a.w,g=c+c,h=d+d,i=e+e,j=c*g,k=c*h,l=c*i,m=d*h,n=d*i,o=e*i,p=f*g,q=f*h,r=f*i;return b[0]=1-(m+o),b[4]=k-r,b[8]=l+q,b[1]=k+r,b[5]=1-(j+o),b[9]=n-p,b[2]=l-q,b[6]=n+p,b[10]=1-(j+m),b[3]=0,b[7]=0,b[11]=0,b[12]=0,b[13]=0,b[14]=0,b[15]=1,this},lookAt:function(){var b=new a.Vector3,c=new a.Vector3,d=new a.Vector3;return function(a,e,f){var g=this.elements;return d.subVectors(a,e).normalize(),0===d.length()&&(d.z=1),b.crossVectors(f,d).normalize(),0===b.length()&&(d.x+=1e-4,b.crossVectors(f,d).normalize()),c.crossVectors(d,b),g[0]=b.x,g[4]=c.x,g[8]=d.x,g[1]=b.y,g[5]=c.y,g[9]=d.y,g[2]=b.z,g[6]=c.z,g[10]=d.z,this}}(),multiply:function(a,b){return void 0!==b?(console.warn("THREE.Matrix4: .multiply() now only accepts one argument. Use .multiplyMatrices( a, b ) instead."),this.multiplyMatrices(a,b)):this.multiplyMatrices(this,a)},multiplyMatrices:function(a,b){var c=a.elements,d=b.elements,e=this.elements,f=c[0],g=c[4],h=c[8],i=c[12],j=c[1],k=c[5],l=c[9],m=c[13],n=c[2],o=c[6],p=c[10],q=c[14],r=c[3],s=c[7],t=c[11],u=c[15],v=d[0],w=d[4],x=d[8],y=d[12],z=d[1],A=d[5],B=d[9],C=d[13],D=d[2],E=d[6],F=d[10],G=d[14],H=d[3],I=d[7],J=d[11],K=d[15];return e[0]=f*v+g*z+h*D+i*H,e[4]=f*w+g*A+h*E+i*I,e[8]=f*x+g*B+h*F+i*J,e[12]=f*y+g*C+h*G+i*K,e[1]=j*v+k*z+l*D+m*H,e[5]=j*w+k*A+l*E+m*I,e[9]=j*x+k*B+l*F+m*J,e[13]=j*y+k*C+l*G+m*K,e[2]=n*v+o*z+p*D+q*H,e[6]=n*w+o*A+p*E+q*I,e[10]=n*x+o*B+p*F+q*J,e[14]=n*y+o*C+p*G+q*K,e[3]=r*v+s*z+t*D+u*H,e[7]=r*w+s*A+t*E+u*I,e[11]=r*x+s*B+t*F+u*J,e[15]=r*y+s*C+t*G+u*K,this},multiplyToArray:function(a,b,c){var d=this.elements;return this.multiplyMatrices(a,b),c[0]=d[0],c[1]=d[1],c[2]=d[2],c[3]=d[3],c[4]=d[4],c[5]=d[5],c[6]=d[6],c[7]=d[7],c[8]=d[8],c[9]=d[9],c[10]=d[10],c[11]=d[11],c[12]=d[12],c[13]=d[13],c[14]=d[14],c[15]=d[15],this},multiplyScalar:function(a){var b=this.elements;return b[0]*=a,b[4]*=a,b[8]*=a,b[12]*=a,b[1]*=a,b[5]*=a,b[9]*=a,b[13]*=a,b[2]*=a,b[6]*=a,b[10]*=a,b[14]*=a,b[3]*=a,b[7]*=a,b[11]*=a,b[15]*=a,this},multiplyVector3:function(a){return console.warn("THREE.Matrix4: .multiplyVector3() has been removed. Use vector.applyMatrix4( matrix ) or vector.applyProjection( matrix ) instead."),a.applyProjection(this)},multiplyVector4:function(a){return console.warn("THREE.Matrix4: .multiplyVector4() has been removed. Use vector.applyMatrix4( matrix ) instead."),a.applyMatrix4(this)},multiplyVector3Array:function(a){return console.warn("THREE.Matrix4: .multiplyVector3Array() has been renamed. Use matrix.applyToVector3Array( array ) instead."),this.applyToVector3Array(a)},applyToVector3Array:function(){var b=new a.Vector3;return function(a,c,d){void 0===c&&(c=0),void 0===d&&(d=a.length);for(var e=0,f=c;d>e;e+=3,f+=3)b.x=a[f],b.y=a[f+1],b.z=a[f+2],b.applyMatrix4(this),a[f]=b.x,a[f+1]=b.y,a[f+2]=b.z;return a}}(),rotateAxis:function(a){console.warn("THREE.Matrix4: .rotateAxis() has been removed. Use Vector3.transformDirection( matrix ) instead."),a.transformDirection(this)},crossVector:function(a){return console.warn("THREE.Matrix4: .crossVector() has been removed. Use vector.applyMatrix4( matrix ) instead."),a.applyMatrix4(this)},determinant:function(){var a=this.elements,b=a[0],c=a[4],d=a[8],e=a[12],f=a[1],g=a[5],h=a[9],i=a[13],j=a[2],k=a[6],l=a[10],m=a[14],n=a[3],o=a[7],p=a[11],q=a[15];return n*(+e*h*k-d*i*k-e*g*l+c*i*l+d*g*m-c*h*m)+o*(+b*h*m-b*i*l+e*f*l-d*f*m+d*i*j-e*h*j)+p*(+b*i*k-b*g*m-e*f*k+c*f*m+e*g*j-c*i*j)+q*(-d*g*j-b*h*k+b*g*l+d*f*k-c*f*l+c*h*j)},transpose:function(){var a,b=this.elements;return a=b[1],b[1]=b[4],b[4]=a,a=b[2],b[2]=b[8],b[8]=a,a=b[6],b[6]=b[9],b[9]=a,a=b[3],b[3]=b[12],b[12]=a,a=b[7],b[7]=b[13],b[13]=a,a=b[11],b[11]=b[14],b[14]=a,this},flattenToArrayOffset:function(a,b){var c=this.elements;return a[b]=c[0],a[b+1]=c[1],a[b+2]=c[2],a[b+3]=c[3],a[b+4]=c[4],a[b+5]=c[5],a[b+6]=c[6],a[b+7]=c[7],a[b+8]=c[8],a[b+9]=c[9],a[b+10]=c[10],a[b+11]=c[11],a[b+12]=c[12],a[b+13]=c[13],a[b+14]=c[14],a[b+15]=c[15],a},getPosition:function(){var b=new a.Vector3;return function(){console.warn("THREE.Matrix4: .getPosition() has been removed. Use Vector3.setFromMatrixPosition( matrix ) instead.");var a=this.elements;return b.set(a[12],a[13],a[14])}}(),setPosition:function(a){var b=this.elements;return b[12]=a.x,b[13]=a.y,b[14]=a.z,this},getInverse:function(a,b){var c=this.elements,d=a.elements,e=d[0],f=d[4],g=d[8],h=d[12],i=d[1],j=d[5],k=d[9],l=d[13],m=d[2],n=d[6],o=d[10],p=d[14],q=d[3],r=d[7],s=d[11],t=d[15];c[0]=k*p*r-l*o*r+l*n*s-j*p*s-k*n*t+j*o*t,c[4]=h*o*r-g*p*r-h*n*s+f*p*s+g*n*t-f*o*t,c[8]=g*l*r-h*k*r+h*j*s-f*l*s-g*j*t+f*k*t,c[12]=h*k*n-g*l*n-h*j*o+f*l*o+g*j*p-f*k*p,c[1]=l*o*q-k*p*q-l*m*s+i*p*s+k*m*t-i*o*t,c[5]=g*p*q-h*o*q+h*m*s-e*p*s-g*m*t+e*o*t,c[9]=h*k*q-g*l*q-h*i*s+e*l*s+g*i*t-e*k*t,c[13]=g*l*m-h*k*m+h*i*o-e*l*o-g*i*p+e*k*p,c[2]=j*p*q-l*n*q+l*m*r-i*p*r-j*m*t+i*n*t,c[6]=h*n*q-f*p*q-h*m*r+e*p*r+f*m*t-e*n*t,c[10]=f*l*q-h*j*q+h*i*r-e*l*r-f*i*t+e*j*t,c[14]=h*j*m-f*l*m-h*i*n+e*l*n+f*i*p-e*j*p,c[3]=k*n*q-j*o*q-k*m*r+i*o*r+j*m*s-i*n*s,c[7]=f*o*q-g*n*q+g*m*r-e*o*r-f*m*s+e*n*s,c[11]=g*j*q-f*k*q-g*i*r+e*k*r+f*i*s-e*j*s,c[15]=f*k*m-g*j*m+g*i*n-e*k*n-f*i*o+e*j*o;var u=e*c[0]+i*c[4]+m*c[8]+q*c[12];if(0==u){var v="Matrix4.getInverse(): can't invert matrix, determinant is 0";if(b)throw new Error(v);return console.warn(v),this.identity(),this}return this.multiplyScalar(1/u),this},translate:function(a){console.warn("THREE.Matrix4: .translate() has been removed.")},rotateX:function(a){console.warn("THREE.Matrix4: .rotateX() has been removed.")},rotateY:function(a){console.warn("THREE.Matrix4: .rotateY() has been removed.")},rotateZ:function(a){console.warn("THREE.Matrix4: .rotateZ() has been removed.")},rotateByAxis:function(a,b){console.warn("THREE.Matrix4: .rotateByAxis() has been removed.")},scale:function(a){var b=this.elements,c=a.x,d=a.y,e=a.z;return b[0]*=c,b[4]*=d,b[8]*=e,b[1]*=c,b[5]*=d,b[9]*=e,b[2]*=c,b[6]*=d,b[10]*=e,b[3]*=c,b[7]*=d,b[11]*=e,this},getMaxScaleOnAxis:function(){var a=this.elements,b=a[0]*a[0]+a[1]*a[1]+a[2]*a[2],c=a[4]*a[4]+a[5]*a[5]+a[6]*a[6],d=a[8]*a[8]+a[9]*a[9]+a[10]*a[10];return Math.sqrt(Math.max(b,Math.max(c,d)))},makeTranslation:function(a,b,c){return this.set(1,0,0,a,0,1,0,b,0,0,1,c,0,0,0,1),this},makeRotationX:function(a){var b=Math.cos(a),c=Math.sin(a);return this.set(1,0,0,0,0,b,-c,0,0,c,b,0,0,0,0,1),this},makeRotationY:function(a){var b=Math.cos(a),c=Math.sin(a);return this.set(b,0,c,0,0,1,0,0,-c,0,b,0,0,0,0,1),this},makeRotationZ:function(a){var b=Math.cos(a),c=Math.sin(a);return this.set(b,-c,0,0,c,b,0,0,0,0,1,0,0,0,0,1),this},makeRotationAxis:function(a,b){var c=Math.cos(b),d=Math.sin(b),e=1-c,f=a.x,g=a.y,h=a.z,i=e*f,j=e*g;return this.set(i*f+c,i*g-d*h,i*h+d*g,0,i*g+d*h,j*g+c,j*h-d*f,0,i*h-d*g,j*h+d*f,e*h*h+c,0,0,0,0,1),this},makeScale:function(a,b,c){return this.set(a,0,0,0,0,b,0,0,0,0,c,0,0,0,0,1),this},compose:function(a,b,c){return this.makeRotationFromQuaternion(b),this.scale(c),this.setPosition(a),this},decompose:function(){var b=new a.Vector3,c=new a.Matrix4;return function(a,d,e){var f=this.elements,g=b.set(f[0],f[1],f[2]).length(),h=b.set(f[4],f[5],f[6]).length(),i=b.set(f[8],f[9],f[10]).length(),j=this.determinant();0>j&&(g=-g),a.x=f[12],a.y=f[13],a.z=f[14],c.elements.set(this.elements);var k=1/g,l=1/h,m=1/i;return c.elements[0]*=k,c.elements[1]*=k,c.elements[2]*=k,c.elements[4]*=l,c.elements[5]*=l,c.elements[6]*=l,c.elements[8]*=m,c.elements[9]*=m,c.elements[10]*=m,d.setFromRotationMatrix(c),e.x=g,e.y=h,e.z=i,this}}(),makeFrustum:function(a,b,c,d,e,f){var g=this.elements,h=2*e/(b-a),i=2*e/(d-c),j=(b+a)/(b-a),k=(d+c)/(d-c),l=-(f+e)/(f-e),m=-2*f*e/(f-e);return g[0]=h,g[4]=0,g[8]=j,g[12]=0,g[1]=0,g[5]=i,g[9]=k,g[13]=0,g[2]=0,g[6]=0,g[10]=l,g[14]=m,g[3]=0,g[7]=0,g[11]=-1,g[15]=0,this},makePerspective:function(b,c,d,e){var f=d*Math.tan(a.Math.degToRad(.5*b)),g=-f,h=g*c,i=f*c;return this.makeFrustum(h,i,g,f,d,e)},makeOrthographic:function(a,b,c,d,e,f){var g=this.elements,h=b-a,i=c-d,j=f-e,k=(b+a)/h,l=(c+d)/i,m=(f+e)/j;return g[0]=2/h,g[4]=0,g[8]=0,g[12]=-k,g[1]=0,g[5]=2/i,g[9]=0,g[13]=-l,g[2]=0,g[6]=0,g[10]=-2/j,g[14]=-m,g[3]=0,g[7]=0,g[11]=0,g[15]=1,this},fromArray:function(a){return this.elements.set(a),this},toArray:function(){var a=this.elements;return[a[0],a[1],a[2],a[3],a[4],a[5],a[6],a[7],a[8],a[9],a[10],a[11],a[12],a[13],a[14],a[15]]},clone:function(){return(new a.Matrix4).fromArray(this.elements)}},a.Ray=function(b,c){this.origin=void 0!==b?b:new a.Vector3,this.direction=void 0!==c?c:new a.Vector3},a.Ray.prototype={constructor:a.Ray,set:function(a,b){return this.origin.copy(a),this.direction.copy(b),this},copy:function(a){return this.origin.copy(a.origin),this.direction.copy(a.direction),this},at:function(b,c){var d=c||new a.Vector3;return d.copy(this.direction).multiplyScalar(b).add(this.origin)},recast:function(){var b=new a.Vector3;return function(a){return this.origin.copy(this.at(a,b)),this}}(),closestPointToPoint:function(b,c){var d=c||new a.Vector3;d.subVectors(b,this.origin);var e=d.dot(this.direction);return 0>e?d.copy(this.origin):d.copy(this.direction).multiplyScalar(e).add(this.origin)},distanceToPoint:function(){var b=new a.Vector3;return function(a){var c=b.subVectors(a,this.origin).dot(this.direction);return 0>c?this.origin.distanceTo(a):(b.copy(this.direction).multiplyScalar(c).add(this.origin),b.distanceTo(a))}}(),distanceSqToSegment:function(a,b,c,d){var e,f,g,h,i=a.clone().add(b).multiplyScalar(.5),j=b.clone().sub(a).normalize(),k=.5*a.distanceTo(b),l=this.origin.clone().sub(i),m=-this.direction.dot(j),n=l.dot(this.direction),o=-l.dot(j),p=l.lengthSq(),q=Math.abs(1-m*m);if(q>=0)if(e=m*o-n,f=m*n-o,h=k*q,e>=0)if(f>=-h)if(h>=f){var r=1/q;e*=r,f*=r,g=e*(e+m*f+2*n)+f*(m*e+f+2*o)+p}else f=k,e=Math.max(0,-(m*f+n)),g=-e*e+f*(f+2*o)+p;else f=-k,e=Math.max(0,-(m*f+n)),g=-e*e+f*(f+2*o)+p;else-h>=f?(e=Math.max(0,-(-m*k+n)),f=e>0?-k:Math.min(Math.max(-k,-o),k),g=-e*e+f*(f+2*o)+p):h>=f?(e=0,f=Math.min(Math.max(-k,-o),k),g=f*(f+2*o)+p):(e=Math.max(0,-(m*k+n)),f=e>0?k:Math.min(Math.max(-k,-o),k),g=-e*e+f*(f+2*o)+p);else f=m>0?-k:k,e=Math.max(0,-(m*f+n)),g=-e*e+f*(f+2*o)+p;return c&&c.copy(this.direction.clone().multiplyScalar(e).add(this.origin)),d&&d.copy(j.clone().multiplyScalar(f).add(i)),g},isIntersectionSphere:function(a){return this.distanceToPoint(a.center)<=a.radius},intersectSphere:function(){var b=new a.Vector3;return function(a,c){b.subVectors(a.center,this.origin);var d=b.dot(this.direction),e=b.dot(b)-d*d,f=a.radius*a.radius;if(e>f)return null;var g=Math.sqrt(f-e),h=d-g,i=d+g;return 0>h&&0>i?null:0>h?this.at(i,c):this.at(h,c)}}(),isIntersectionPlane:function(a){var b=a.distanceToPoint(this.origin);if(0===b)return!0;var c=a.normal.dot(this.direction);return 0>c*b?!0:!1},distanceToPlane:function(a){var b=a.normal.dot(this.direction);if(0==b)return 0==a.distanceToPoint(this.origin)?0:null;var c=-(this.origin.dot(a.normal)+a.constant)/b;return c>=0?c:null},intersectPlane:function(a,b){var c=this.distanceToPlane(a);return null===c?null:this.at(c,b)},isIntersectionBox:function(){var b=new a.Vector3;return function(a){return null!==this.intersectBox(a,b)}}(),intersectBox:function(a,b){var c,d,e,f,g,h,i=1/this.direction.x,j=1/this.direction.y,k=1/this.direction.z,l=this.origin;return i>=0?(c=(a.min.x-l.x)*i,d=(a.max.x-l.x)*i):(c=(a.max.x-l.x)*i,d=(a.min.x-l.x)*i),j>=0?(e=(a.min.y-l.y)*j,f=(a.max.y-l.y)*j):(e=(a.max.y-l.y)*j,f=(a.min.y-l.y)*j),c>f||e>d?null:((e>c||c!==c)&&(c=e),(d>f||d!==d)&&(d=f),k>=0?(g=(a.min.z-l.z)*k,h=(a.max.z-l.z)*k):(g=(a.max.z-l.z)*k,h=(a.min.z-l.z)*k),c>h||g>d?null:((g>c||c!==c)&&(c=g),(d>h||d!==d)&&(d=h),0>d?null:this.at(c>=0?c:d,b)))},intersectTriangle:function(){var b=new a.Vector3,c=new a.Vector3,d=new a.Vector3,e=new a.Vector3;return function(a,f,g,h,i){c.subVectors(f,a),d.subVectors(g,a),e.crossVectors(c,d);var j,k=this.direction.dot(e);if(k>0){if(h)return null;j=1}else{if(!(0>k))return null;j=-1,k=-k}b.subVectors(this.origin,a);var l=j*this.direction.dot(d.crossVectors(b,d));if(0>l)return null;var m=j*this.direction.dot(c.cross(b));if(0>m)return null;if(l+m>k)return null;var n=-j*b.dot(e);return 0>n?null:this.at(n/k,i)}}(),applyMatrix4:function(a){return this.direction.add(this.origin).applyMatrix4(a),this.origin.applyMatrix4(a),this.direction.sub(this.origin),this.direction.normalize(),this},equals:function(a){return a.origin.equals(this.origin)&&a.direction.equals(this.direction)},clone:function(){return(new a.Ray).copy(this)}},a.Sphere=function(b,c){this.center=void 0!==b?b:new a.Vector3,this.radius=void 0!==c?c:0},a.Sphere.prototype={constructor:a.Sphere,set:function(a,b){return this.center.copy(a),this.radius=b,this},setFromPoints:function(){var b=new a.Box3;return function(a,c){var d=this.center;void 0!==c?d.copy(c):b.setFromPoints(a).center(d);for(var e=0,f=0,g=a.length;g>f;f++)e=Math.max(e,d.distanceToSquared(a[f]));return this.radius=Math.sqrt(e),this}}(),copy:function(a){return this.center.copy(a.center),this.radius=a.radius,this},empty:function(){return this.radius<=0},containsPoint:function(a){return a.distanceToSquared(this.center)<=this.radius*this.radius},distanceToPoint:function(a){return a.distanceTo(this.center)-this.radius},intersectsSphere:function(a){var b=this.radius+a.radius;return a.center.distanceToSquared(this.center)<=b*b},clampPoint:function(b,c){var d=this.center.distanceToSquared(b),e=c||new a.Vector3;return e.copy(b),d>this.radius*this.radius&&(e.sub(this.center).normalize(),e.multiplyScalar(this.radius).add(this.center)),e},getBoundingBox:function(b){var c=b||new a.Box3;return c.set(this.center,this.center),c.expandByScalar(this.radius),c},applyMatrix4:function(a){return this.center.applyMatrix4(a),this.radius=this.radius*a.getMaxScaleOnAxis(),this},translate:function(a){return this.center.add(a),this},equals:function(a){return a.center.equals(this.center)&&a.radius===this.radius},clone:function(){return(new a.Sphere).copy(this)}},a.Frustum=function(b,c,d,e,f,g){this.planes=[void 0!==b?b:new a.Plane,void 0!==c?c:new a.Plane,void 0!==d?d:new a.Plane,void 0!==e?e:new a.Plane,void 0!==f?f:new a.Plane,void 0!==g?g:new a.Plane]},a.Frustum.prototype={constructor:a.Frustum,set:function(a,b,c,d,e,f){var g=this.planes;return g[0].copy(a),g[1].copy(b),g[2].copy(c),g[3].copy(d),g[4].copy(e),g[5].copy(f),this},copy:function(a){for(var b=this.planes,c=0;6>c;c++)b[c].copy(a.planes[c]);return this},setFromMatrix:function(a){var b=this.planes,c=a.elements,d=c[0],e=c[1],f=c[2],g=c[3],h=c[4],i=c[5],j=c[6],k=c[7],l=c[8],m=c[9],n=c[10],o=c[11],p=c[12],q=c[13],r=c[14],s=c[15];return b[0].setComponents(g-d,k-h,o-l,s-p).normalize(),b[1].setComponents(g+d,k+h,o+l,s+p).normalize(),b[2].setComponents(g+e,k+i,o+m,s+q).normalize(),b[3].setComponents(g-e,k-i,o-m,s-q).normalize(),b[4].setComponents(g-f,k-j,o-n,s-r).normalize(),b[5].setComponents(g+f,k+j,o+n,s+r).normalize(),this},intersectsObject:function(){var b=new a.Sphere;return function(a){var c=a.geometry;return null===c.boundingSphere&&c.computeBoundingSphere(),b.copy(c.boundingSphere),b.applyMatrix4(a.matrixWorld),this.intersectsSphere(b)}}(),intersectsSphere:function(a){for(var b=this.planes,c=a.center,d=-a.radius,e=0;6>e;e++){var f=b[e].distanceToPoint(c);if(d>f)return!1}return!0},intersectsBox:function(){var b=new a.Vector3,c=new a.Vector3;return function(a){for(var d=this.planes,e=0;6>e;e++){var f=d[e];b.x=f.normal.x>0?a.min.x:a.max.x,c.x=f.normal.x>0?a.max.x:a.min.x,b.y=f.normal.y>0?a.min.y:a.max.y,c.y=f.normal.y>0?a.max.y:a.min.y,b.z=f.normal.z>0?a.min.z:a.max.z,c.z=f.normal.z>0?a.max.z:a.min.z;var g=f.distanceToPoint(b),h=f.distanceToPoint(c);if(0>g&&0>h)return!1}return!0}}(),containsPoint:function(a){for(var b=this.planes,c=0;6>c;c++)if(b[c].distanceToPoint(a)<0)return!1;return!0},clone:function(){return(new a.Frustum).copy(this)}},a.Plane=function(b,c){this.normal=void 0!==b?b:new a.Vector3(1,0,0),this.constant=void 0!==c?c:0},a.Plane.prototype={constructor:a.Plane,set:function(a,b){return this.normal.copy(a),this.constant=b,this},setComponents:function(a,b,c,d){return this.normal.set(a,b,c),this.constant=d,this},setFromNormalAndCoplanarPoint:function(a,b){return this.normal.copy(a),this.constant=-b.dot(this.normal),this},setFromCoplanarPoints:function(){var b=new a.Vector3,c=new a.Vector3;return function(a,d,e){var f=b.subVectors(e,d).cross(c.subVectors(a,d)).normalize();return this.setFromNormalAndCoplanarPoint(f,a),this}}(),copy:function(a){return this.normal.copy(a.normal),this.constant=a.constant,this},normalize:function(){var a=1/this.normal.length();return this.normal.multiplyScalar(a),this.constant*=a,this},negate:function(){return this.constant*=-1,this.normal.negate(),this},distanceToPoint:function(a){return this.normal.dot(a)+this.constant},distanceToSphere:function(a){return this.distanceToPoint(a.center)-a.radius},projectPoint:function(a,b){return this.orthoPoint(a,b).sub(a).negate()},orthoPoint:function(b,c){var d=this.distanceToPoint(b),e=c||new a.Vector3;return e.copy(this.normal).multiplyScalar(d)},isIntersectionLine:function(a){var b=this.distanceToPoint(a.start),c=this.distanceToPoint(a.end);return 0>b&&c>0||0>c&&b>0},intersectLine:function(){var b=new a.Vector3;return function(c,d){var e=d||new a.Vector3,f=c.delta(b),g=this.normal.dot(f);if(0==g)return 0==this.distanceToPoint(c.start)?e.copy(c.start):void 0;var h=-(c.start.dot(this.normal)+this.constant)/g;return 0>h||h>1?void 0:e.copy(f).multiplyScalar(h).add(c.start)}}(),coplanarPoint:function(b){var c=b||new a.Vector3;return c.copy(this.normal).multiplyScalar(-this.constant)},applyMatrix4:function(){var b=new a.Vector3,c=new a.Vector3,d=new a.Matrix3;return function(a,e){var f=e||d.getNormalMatrix(a),g=b.copy(this.normal).applyMatrix3(f),h=this.coplanarPoint(c);return h.applyMatrix4(a),this.setFromNormalAndCoplanarPoint(g,h),this}}(),translate:function(a){return this.constant=this.constant-a.dot(this.normal),this},equals:function(a){return a.normal.equals(this.normal)&&a.constant==this.constant},clone:function(){return(new a.Plane).copy(this)}},a.Math={generateUUID:function(){var a,b="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".split(""),c=new Array(36),d=0;return function(){for(var e=0;36>e;e++)8==e||13==e||18==e||23==e?c[e]="-":14==e?c[e]="4":(2>=d&&(d=33554432+16777216*Math.random()|0),a=15&d,d>>=4,c[e]=b[19==e?3&a|8:a]);return c.join("")}}(),clamp:function(a,b,c){return b>a?b:a>c?c:a},clampBottom:function(a,b){return b>a?b:a},mapLinear:function(a,b,c,d,e){return d+(a-b)*(e-d)/(c-b)},smoothstep:function(a,b,c){return b>=a?0:a>=c?1:(a=(a-b)/(c-b),a*a*(3-2*a))},smootherstep:function(a,b,c){return b>=a?0:a>=c?1:(a=(a-b)/(c-b),a*a*a*(a*(6*a-15)+10))},random16:function(){return(65280*Math.random()+255*Math.random())/65535},randInt:function(a,b){return a+Math.floor(Math.random()*(b-a+1))},randFloat:function(a,b){return a+Math.random()*(b-a)},randFloatSpread:function(a){return a*(.5-Math.random())},degToRad:function(){var a=Math.PI/180;return function(b){return b*a}}(),radToDeg:function(){var a=180/Math.PI;return function(b){return b*a}}(),isPowerOfTwo:function(a){return 0===(a&a-1)&&0!==a}},a.Spline=function(b){function c(a,b,c,d,e,f,g){var h=.5*(c-a),i=.5*(d-b);return(2*(b-c)+h+i)*g+(-3*(b-c)-2*h-i)*f+h*e+b}this.points=b;var d,e,f,g,h,i,j,k,l,m=[],n={x:0,y:0,z:0};this.initFromArray=function(a){this.points=[];for(var b=0;b<a.length;b++)this.points[b]={x:a[b][0],y:a[b][1],z:a[b][2]}},this.getPoint=function(a){return d=(this.points.length-1)*a,e=Math.floor(d),f=d-e,m[0]=0===e?e:e-1,m[1]=e,m[2]=e>this.points.length-2?this.points.length-1:e+1,m[3]=e>this.points.length-3?this.points.length-1:e+2,i=this.points[m[0]],j=this.points[m[1]],k=this.points[m[2]],l=this.points[m[3]],g=f*f,h=f*g,n.x=c(i.x,j.x,k.x,l.x,f,g,h),n.y=c(i.y,j.y,k.y,l.y,f,g,h),n.z=c(i.z,j.z,k.z,l.z,f,g,h),n},this.getControlPointsArray=function(){var a,b,c=this.points.length,d=[];for(a=0;c>a;a++)b=this.points[a],d[a]=[b.x,b.y,b.z];return d},this.getLength=function(b){var c,d,e,f,g=0,h=0,i=0,j=new a.Vector3,k=new a.Vector3,l=[],m=0;for(l[0]=0,b||(b=100),e=this.points.length*b,j.copy(this.points[0]),c=1;e>c;c++)d=c/e,f=this.getPoint(d),k.copy(f),m+=k.distanceTo(j),j.copy(f),g=(this.points.length-1)*d,h=Math.floor(g),h!=i&&(l[h]=m,i=h);return l[l.length]=m,{chunks:l,total:m}},this.reparametrizeByArcLength=function(b){var c,d,e,f,g,h,i,j,k=[],l=new a.Vector3,m=this.getLength();
for(k.push(l.copy(this.points[0]).clone()),c=1;c<this.points.length;c++){for(h=m.chunks[c]-m.chunks[c-1],i=Math.ceil(b*h/m.total),f=(c-1)/(this.points.length-1),g=c/(this.points.length-1),d=1;i-1>d;d++)e=f+d*(1/i)*(g-f),j=this.getPoint(e),k.push(l.copy(j).clone());k.push(l.copy(this.points[c]).clone())}this.points=k}},a.Triangle=function(b,c,d){this.a=void 0!==b?b:new a.Vector3,this.b=void 0!==c?c:new a.Vector3,this.c=void 0!==d?d:new a.Vector3},a.Triangle.normal=function(){var b=new a.Vector3;return function(c,d,e,f){var g=f||new a.Vector3;g.subVectors(e,d),b.subVectors(c,d),g.cross(b);var h=g.lengthSq();return h>0?g.multiplyScalar(1/Math.sqrt(h)):g.set(0,0,0)}}(),a.Triangle.barycoordFromPoint=function(){var b=new a.Vector3,c=new a.Vector3,d=new a.Vector3;return function(e,f,g,h,i){b.subVectors(h,f),c.subVectors(g,f),d.subVectors(e,f);var j=b.dot(b),k=b.dot(c),l=b.dot(d),m=c.dot(c),n=c.dot(d),o=j*m-k*k,p=i||new a.Vector3;if(0==o)return p.set(-2,-1,-1);var q=1/o,r=(m*l-k*n)*q,s=(j*n-k*l)*q;return p.set(1-r-s,s,r)}}(),a.Triangle.containsPoint=function(){var b=new a.Vector3;return function(c,d,e,f){var g=a.Triangle.barycoordFromPoint(c,d,e,f,b);return g.x>=0&&g.y>=0&&g.x+g.y<=1}}(),a.Triangle.prototype={constructor:a.Triangle,set:function(a,b,c){return this.a.copy(a),this.b.copy(b),this.c.copy(c),this},setFromPointsAndIndices:function(a,b,c,d){return this.a.copy(a[b]),this.b.copy(a[c]),this.c.copy(a[d]),this},copy:function(a){return this.a.copy(a.a),this.b.copy(a.b),this.c.copy(a.c),this},area:function(){var b=new a.Vector3,c=new a.Vector3;return function(){return b.subVectors(this.c,this.b),c.subVectors(this.a,this.b),.5*b.cross(c).length()}}(),midpoint:function(b){var c=b||new a.Vector3;return c.addVectors(this.a,this.b).add(this.c).multiplyScalar(1/3)},normal:function(b){return a.Triangle.normal(this.a,this.b,this.c,b)},plane:function(b){var c=b||new a.Plane;return c.setFromCoplanarPoints(this.a,this.b,this.c)},barycoordFromPoint:function(b,c){return a.Triangle.barycoordFromPoint(b,this.a,this.b,this.c,c)},containsPoint:function(b){return a.Triangle.containsPoint(b,this.a,this.b,this.c)},equals:function(a){return a.a.equals(this.a)&&a.b.equals(this.b)&&a.c.equals(this.c)},clone:function(){return(new a.Triangle).copy(this)}},a.Clock=function(a){this.autoStart=void 0!==a?a:!0,this.startTime=0,this.oldTime=0,this.elapsedTime=0,this.running=!1},a.Clock.prototype={constructor:a.Clock,start:function(){this.startTime=void 0!==self.performance&&void 0!==self.performance.now?self.performance.now():Date.now(),this.oldTime=this.startTime,this.running=!0},stop:function(){this.getElapsedTime(),this.running=!1},getElapsedTime:function(){return this.getDelta(),this.elapsedTime},getDelta:function(){var a=0;if(this.autoStart&&!this.running&&this.start(),this.running){var b=void 0!==self.performance&&void 0!==self.performance.now?self.performance.now():Date.now();a=.001*(b-this.oldTime),this.oldTime=b,this.elapsedTime+=a}return a}},a.EventDispatcher=function(){},a.EventDispatcher.prototype={constructor:a.EventDispatcher,apply:function(b){b.addEventListener=a.EventDispatcher.prototype.addEventListener,b.hasEventListener=a.EventDispatcher.prototype.hasEventListener,b.removeEventListener=a.EventDispatcher.prototype.removeEventListener,b.dispatchEvent=a.EventDispatcher.prototype.dispatchEvent},addEventListener:function(a,b){void 0===this._listeners&&(this._listeners={});var c=this._listeners;void 0===c[a]&&(c[a]=[]),-1===c[a].indexOf(b)&&c[a].push(b)},hasEventListener:function(a,b){if(void 0===this._listeners)return!1;var c=this._listeners;return void 0!==c[a]&&-1!==c[a].indexOf(b)?!0:!1},removeEventListener:function(a,b){if(void 0!==this._listeners){var c=this._listeners,d=c[a];if(void 0!==d){var e=d.indexOf(b);-1!==e&&d.splice(e,1)}}},dispatchEvent:function(a){if(void 0!==this._listeners){var b=this._listeners,c=b[a.type];if(void 0!==c){a.target=this;for(var d=[],e=c.length,f=0;e>f;f++)d[f]=c[f];for(var f=0;e>f;f++)d[f].call(this,a)}}}},function(a){a.Raycaster=function(b,c,d,e){this.ray=new a.Ray(b,c),this.near=d||0,this.far=e||1/0,this.params={Sprite:{},Mesh:{},PointCloud:{threshold:1},LOD:{},Line:{}}};var b=function(a,b){return a.distance-b.distance},c=function(a,b,d,e){if(a.raycast(b,d),e===!0)for(var f=a.children,g=0,h=f.length;h>g;g++)c(f[g],b,d,!0)};a.Raycaster.prototype={constructor:a.Raycaster,precision:1e-4,linePrecision:1,set:function(a,b){this.ray.set(a,b)},intersectObject:function(a,d){var e=[];return c(a,this,e,d),e.sort(b),e},intersectObjects:function(a,d){var e=[];if(a instanceof Array==!1)return console.log("THREE.Raycaster.intersectObjects: objects is not an Array."),e;for(var f=0,g=a.length;g>f;f++)c(a[f],this,e,d);return e.sort(b),e}}}(a),a.Object3D=function(){Object.defineProperty(this,"id",{value:a.Object3DIdCount++}),this.uuid=a.Math.generateUUID(),this.name="",this.type="Object3D",this.parent=void 0,this.children=[],this.up=a.Object3D.DefaultUp.clone();var b=new a.Vector3,c=new a.Euler,d=new a.Quaternion,e=new a.Vector3(1,1,1),f=function(){d.setFromEuler(c,!1)},g=function(){c.setFromQuaternion(d,void 0,!1)};c.onChange(f),d.onChange(g),Object.defineProperties(this,{position:{enumerable:!0,value:b},rotation:{enumerable:!0,value:c},quaternion:{enumerable:!0,value:d},scale:{enumerable:!0,value:e}}),this.renderDepth=null,this.rotationAutoUpdate=!0,this.matrix=new a.Matrix4,this.matrixWorld=new a.Matrix4,this.matrixAutoUpdate=!0,this.matrixWorldNeedsUpdate=!1,this.visible=!0,this.castShadow=!1,this.receiveShadow=!1,this.frustumCulled=!0,this.userData={}},a.Object3D.DefaultUp=new a.Vector3(0,1,0),a.Object3D.prototype={constructor:a.Object3D,get eulerOrder(){return console.warn("THREE.Object3D: .eulerOrder has been moved to .rotation.order."),this.rotation.order},set eulerOrder(a){console.warn("THREE.Object3D: .eulerOrder has been moved to .rotation.order."),this.rotation.order=a},get useQuaternion(){console.warn("THREE.Object3D: .useQuaternion has been removed. The library now uses quaternions by default.")},set useQuaternion(a){console.warn("THREE.Object3D: .useQuaternion has been removed. The library now uses quaternions by default.")},applyMatrix:function(a){this.matrix.multiplyMatrices(a,this.matrix),this.matrix.decompose(this.position,this.quaternion,this.scale)},setRotationFromAxisAngle:function(a,b){this.quaternion.setFromAxisAngle(a,b)},setRotationFromEuler:function(a){this.quaternion.setFromEuler(a,!0)},setRotationFromMatrix:function(a){this.quaternion.setFromRotationMatrix(a)},setRotationFromQuaternion:function(a){this.quaternion.copy(a)},rotateOnAxis:function(){var b=new a.Quaternion;return function(a,c){return b.setFromAxisAngle(a,c),this.quaternion.multiply(b),this}}(),rotateX:function(){var b=new a.Vector3(1,0,0);return function(a){return this.rotateOnAxis(b,a)}}(),rotateY:function(){var b=new a.Vector3(0,1,0);return function(a){return this.rotateOnAxis(b,a)}}(),rotateZ:function(){var b=new a.Vector3(0,0,1);return function(a){return this.rotateOnAxis(b,a)}}(),translateOnAxis:function(){var b=new a.Vector3;return function(a,c){return b.copy(a).applyQuaternion(this.quaternion),this.position.add(b.multiplyScalar(c)),this}}(),translate:function(a,b){return console.warn("THREE.Object3D: .translate() has been removed. Use .translateOnAxis( axis, distance ) instead."),this.translateOnAxis(b,a)},translateX:function(){var b=new a.Vector3(1,0,0);return function(a){return this.translateOnAxis(b,a)}}(),translateY:function(){var b=new a.Vector3(0,1,0);return function(a){return this.translateOnAxis(b,a)}}(),translateZ:function(){var b=new a.Vector3(0,0,1);return function(a){return this.translateOnAxis(b,a)}}(),localToWorld:function(a){return a.applyMatrix4(this.matrixWorld)},worldToLocal:function(){var b=new a.Matrix4;return function(a){return a.applyMatrix4(b.getInverse(this.matrixWorld))}}(),lookAt:function(){var b=new a.Matrix4;return function(a){b.lookAt(a,this.position,this.up),this.quaternion.setFromRotationMatrix(b)}}(),add:function(b){if(arguments.length>1){for(var c=0;c<arguments.length;c++)this.add(arguments[c]);return this}return b===this?(console.error("THREE.Object3D.add:",b,"can't be added as a child of itself."),this):(b instanceof a.Object3D?(void 0!==b.parent&&b.parent.remove(b),b.parent=this,b.dispatchEvent({type:"added"}),this.children.push(b)):console.error("THREE.Object3D.add:",b,"is not an instance of THREE.Object3D."),this)},remove:function(a){if(arguments.length>1)for(var b=0;b<arguments.length;b++)this.remove(arguments[b]);var c=this.children.indexOf(a);-1!==c&&(a.parent=void 0,a.dispatchEvent({type:"removed"}),this.children.splice(c,1))},getChildByName:function(a,b){return console.warn("THREE.Object3D: .getChildByName() has been renamed to .getObjectByName()."),this.getObjectByName(a,b)},getObjectById:function(a,b){if(this.id===a)return this;for(var c=0,d=this.children.length;d>c;c++){var e=this.children[c],f=e.getObjectById(a,b);if(void 0!==f)return f}return void 0},getObjectByName:function(a,b){if(this.name===a)return this;for(var c=0,d=this.children.length;d>c;c++){var e=this.children[c],f=e.getObjectByName(a,b);if(void 0!==f)return f}return void 0},getWorldPosition:function(b){var c=b||new a.Vector3;return this.updateMatrixWorld(!0),c.setFromMatrixPosition(this.matrixWorld)},getWorldQuaternion:function(){var b=new a.Vector3,c=new a.Vector3;return function(d){var e=d||new a.Quaternion;return this.updateMatrixWorld(!0),this.matrixWorld.decompose(b,e,c),e}}(),getWorldRotation:function(){var b=new a.Quaternion;return function(c){var d=c||new a.Euler;return this.getWorldQuaternion(b),d.setFromQuaternion(b,this.rotation.order,!1)}}(),getWorldScale:function(){var b=new a.Vector3,c=new a.Quaternion;return function(d){var e=d||new a.Vector3;return this.updateMatrixWorld(!0),this.matrixWorld.decompose(b,c,e),e}}(),getWorldDirection:function(){var b=new a.Quaternion;return function(c){var d=c||new a.Vector3;return this.getWorldQuaternion(b),d.set(0,0,1).applyQuaternion(b)}}(),raycast:function(){},traverse:function(a){a(this);for(var b=0,c=this.children.length;c>b;b++)this.children[b].traverse(a)},traverseVisible:function(a){if(this.visible!==!1){a(this);for(var b=0,c=this.children.length;c>b;b++)this.children[b].traverseVisible(a)}},updateMatrix:function(){this.matrix.compose(this.position,this.quaternion,this.scale),this.matrixWorldNeedsUpdate=!0},updateMatrixWorld:function(a){this.matrixAutoUpdate===!0&&this.updateMatrix(),(this.matrixWorldNeedsUpdate===!0||a===!0)&&(void 0===this.parent?this.matrixWorld.copy(this.matrix):this.matrixWorld.multiplyMatrices(this.parent.matrixWorld,this.matrix),this.matrixWorldNeedsUpdate=!1,a=!0);for(var b=0,c=this.children.length;c>b;b++)this.children[b].updateMatrixWorld(a)},toJSON:function(){var b={metadata:{version:4.3,type:"Object",generator:"ObjectExporter"}},c={},d=function(a){if(void 0===b.geometries&&(b.geometries=[]),void 0===c[a.uuid]){var d=a.toJSON();delete d.metadata,c[a.uuid]=d,b.geometries.push(d)}return a.uuid},e={},f=function(a){if(void 0===b.materials&&(b.materials=[]),void 0===e[a.uuid]){var c=a.toJSON();delete c.metadata,e[a.uuid]=c,b.materials.push(c)}return a.uuid},g=function(b){var c={};if(c.uuid=b.uuid,c.type=b.type,""!==b.name&&(c.name=b.name),"{}"!==JSON.stringify(b.userData)&&(c.userData=b.userData),b.visible!==!0&&(c.visible=b.visible),b instanceof a.PerspectiveCamera?(c.fov=b.fov,c.aspect=b.aspect,c.near=b.near,c.far=b.far):b instanceof a.OrthographicCamera?(c.left=b.left,c.right=b.right,c.top=b.top,c.bottom=b.bottom,c.near=b.near,c.far=b.far):b instanceof a.AmbientLight?c.color=b.color.getHex():b instanceof a.DirectionalLight?(c.color=b.color.getHex(),c.intensity=b.intensity):b instanceof a.PointLight?(c.color=b.color.getHex(),c.intensity=b.intensity,c.distance=b.distance):b instanceof a.SpotLight?(c.color=b.color.getHex(),c.intensity=b.intensity,c.distance=b.distance,c.angle=b.angle,c.exponent=b.exponent):b instanceof a.HemisphereLight?(c.color=b.color.getHex(),c.groundColor=b.groundColor.getHex()):b instanceof a.Mesh?(c.geometry=d(b.geometry),c.material=f(b.material)):b instanceof a.Line?(c.geometry=d(b.geometry),c.material=f(b.material)):b instanceof a.Sprite&&(c.material=f(b.material)),c.matrix=b.matrix.toArray(),b.children.length>0){c.children=[];for(var e=0;e<b.children.length;e++)c.children.push(g(b.children[e]))}return c};return b.object=g(this),b},clone:function(b,c){if(void 0===b&&(b=new a.Object3D),void 0===c&&(c=!0),b.name=this.name,b.up.copy(this.up),b.position.copy(this.position),b.quaternion.copy(this.quaternion),b.scale.copy(this.scale),b.renderDepth=this.renderDepth,b.rotationAutoUpdate=this.rotationAutoUpdate,b.matrix.copy(this.matrix),b.matrixWorld.copy(this.matrixWorld),b.matrixAutoUpdate=this.matrixAutoUpdate,b.matrixWorldNeedsUpdate=this.matrixWorldNeedsUpdate,b.visible=this.visible,b.castShadow=this.castShadow,b.receiveShadow=this.receiveShadow,b.frustumCulled=this.frustumCulled,b.userData=JSON.parse(JSON.stringify(this.userData)),c===!0)for(var d=0;d<this.children.length;d++){var e=this.children[d];b.add(e.clone())}return b}},a.EventDispatcher.prototype.apply(a.Object3D.prototype),a.Object3DIdCount=0,a.Projector=function(){console.warn("THREE.Projector has been moved to /examples/renderers/Projector.js."),this.projectVector=function(a,b){console.warn("THREE.Projector: .projectVector() is now vector.project()."),a.project(b)},this.unprojectVector=function(a,b){console.warn("THREE.Projector: .unprojectVector() is now vector.unproject()."),a.unproject(b)},this.pickingRay=function(a,b){console.error("THREE.Projector: .pickingRay() has been removed.")}},a.Face3=function(b,c,d,e,f,g){this.a=b,this.b=c,this.c=d,this.normal=e instanceof a.Vector3?e:new a.Vector3,this.vertexNormals=e instanceof Array?e:[],this.color=f instanceof a.Color?f:new a.Color,this.vertexColors=f instanceof Array?f:[],this.vertexTangents=[],this.materialIndex=void 0!==g?g:0},a.Face3.prototype={constructor:a.Face3,clone:function(){var b=new a.Face3(this.a,this.b,this.c);b.normal.copy(this.normal),b.color.copy(this.color),b.materialIndex=this.materialIndex;for(var c=0,d=this.vertexNormals.length;d>c;c++)b.vertexNormals[c]=this.vertexNormals[c].clone();for(var c=0,d=this.vertexColors.length;d>c;c++)b.vertexColors[c]=this.vertexColors[c].clone();for(var c=0,d=this.vertexTangents.length;d>c;c++)b.vertexTangents[c]=this.vertexTangents[c].clone();return b}},a.Face4=function(b,c,d,e,f,g,h){return console.warn("THREE.Face4 has been removed. A THREE.Face3 will be created instead."),new a.Face3(b,c,d,f,g,h)},a.BufferAttribute=function(a,b){this.array=a,this.itemSize=b,this.needsUpdate=!1},a.BufferAttribute.prototype={constructor:a.BufferAttribute,get length(){return this.array.length},copyAt:function(a,b,c){a*=this.itemSize,c*=b.itemSize;for(var d=0,e=this.itemSize;e>d;d++)this.array[a+d]=b.array[c+d]},set:function(a){return this.array.set(a),this},setX:function(a,b){return this.array[a*this.itemSize]=b,this},setY:function(a,b){return this.array[a*this.itemSize+1]=b,this},setZ:function(a,b){return this.array[a*this.itemSize+2]=b,this},setXY:function(a,b,c){return a*=this.itemSize,this.array[a]=b,this.array[a+1]=c,this},setXYZ:function(a,b,c,d){return a*=this.itemSize,this.array[a]=b,this.array[a+1]=c,this.array[a+2]=d,this},setXYZW:function(a,b,c,d,e){return a*=this.itemSize,this.array[a]=b,this.array[a+1]=c,this.array[a+2]=d,this.array[a+3]=e,this},clone:function(){return new a.BufferAttribute(new this.array.constructor(this.array),this.itemSize)}},a.Int8Attribute=function(b,c){return console.warn("THREE.Int8Attribute has been removed. Use THREE.BufferAttribute( array, itemSize ) instead."),new a.BufferAttribute(b,c)},a.Uint8Attribute=function(b,c){return console.warn("THREE.Uint8Attribute has been removed. Use THREE.BufferAttribute( array, itemSize ) instead."),new a.BufferAttribute(b,c)},a.Uint8ClampedAttribute=function(b,c){return console.warn("THREE.Uint8ClampedAttribute has been removed. Use THREE.BufferAttribute( array, itemSize ) instead."),new a.BufferAttribute(b,c)},a.Int16Attribute=function(b,c){return console.warn("THREE.Int16Attribute has been removed. Use THREE.BufferAttribute( array, itemSize ) instead."),new a.BufferAttribute(b,c)},a.Uint16Attribute=function(b,c){return console.warn("THREE.Uint16Attribute has been removed. Use THREE.BufferAttribute( array, itemSize ) instead."),new a.BufferAttribute(b,c)},a.Int32Attribute=function(b,c){return console.warn("THREE.Int32Attribute has been removed. Use THREE.BufferAttribute( array, itemSize ) instead."),new a.BufferAttribute(b,c)},a.Uint32Attribute=function(b,c){return console.warn("THREE.Uint32Attribute has been removed. Use THREE.BufferAttribute( array, itemSize ) instead."),new a.BufferAttribute(b,c)},a.Float32Attribute=function(b,c){return console.warn("THREE.Float32Attribute has been removed. Use THREE.BufferAttribute( array, itemSize ) instead."),new a.BufferAttribute(b,c)},a.Float64Attribute=function(b,c){return console.warn("THREE.Float64Attribute has been removed. Use THREE.BufferAttribute( array, itemSize ) instead."),new a.BufferAttribute(b,c)},a.BufferGeometry=function(){Object.defineProperty(this,"id",{value:a.GeometryIdCount++}),this.uuid=a.Math.generateUUID(),this.name="",this.type="BufferGeometry",this.attributes={},this.attributesKeys=[],this.drawcalls=[],this.offsets=this.drawcalls,this.boundingBox=null,this.boundingSphere=null},a.BufferGeometry.prototype={constructor:a.BufferGeometry,addAttribute:function(b,c){return c instanceof a.BufferAttribute==!1?(console.warn("THREE.BufferGeometry: .addAttribute() now expects ( name, attribute )."),void(this.attributes[b]={array:arguments[1],itemSize:arguments[2]})):(this.attributes[b]=c,void(this.attributesKeys=Object.keys(this.attributes)))},getAttribute:function(a){return this.attributes[a]},addDrawCall:function(a,b,c){this.drawcalls.push({start:a,count:b,index:void 0!==c?c:0})},applyMatrix:function(b){var c=this.attributes.position;void 0!==c&&(b.applyToVector3Array(c.array),c.needsUpdate=!0);var d=this.attributes.normal;if(void 0!==d){var e=(new a.Matrix3).getNormalMatrix(b);e.applyToVector3Array(d.array),d.needsUpdate=!0}},center:function(){},fromGeometry:function(b,c){c=c||{vertexColors:a.NoColors};var d=b.vertices,e=b.faces,f=b.faceVertexUvs,g=c.vertexColors,h=f[0].length>0,i=3==e[0].vertexNormals.length,j=new Float32Array(3*e.length*3);this.addAttribute("position",new a.BufferAttribute(j,3));var k=new Float32Array(3*e.length*3);if(this.addAttribute("normal",new a.BufferAttribute(k,3)),g!==a.NoColors){var l=new Float32Array(3*e.length*3);this.addAttribute("color",new a.BufferAttribute(l,3))}if(h===!0){var m=new Float32Array(3*e.length*2);this.addAttribute("uv",new a.BufferAttribute(m,2))}for(var n=0,o=0,p=0;n<e.length;n++,o+=6,p+=9){var q=e[n],r=d[q.a],s=d[q.b],t=d[q.c];if(j[p]=r.x,j[p+1]=r.y,j[p+2]=r.z,j[p+3]=s.x,j[p+4]=s.y,j[p+5]=s.z,j[p+6]=t.x,j[p+7]=t.y,j[p+8]=t.z,i===!0){var u=q.vertexNormals[0],v=q.vertexNormals[1],w=q.vertexNormals[2];k[p]=u.x,k[p+1]=u.y,k[p+2]=u.z,k[p+3]=v.x,k[p+4]=v.y,k[p+5]=v.z,k[p+6]=w.x,k[p+7]=w.y,k[p+8]=w.z}else{var x=q.normal;k[p]=x.x,k[p+1]=x.y,k[p+2]=x.z,k[p+3]=x.x,k[p+4]=x.y,k[p+5]=x.z,k[p+6]=x.x,k[p+7]=x.y,k[p+8]=x.z}if(g===a.FaceColors){var y=q.color;l[p]=y.r,l[p+1]=y.g,l[p+2]=y.b,l[p+3]=y.r,l[p+4]=y.g,l[p+5]=y.b,l[p+6]=y.r,l[p+7]=y.g,l[p+8]=y.b}else if(g===a.VertexColors){var z=q.vertexColors[0],A=q.vertexColors[1],B=q.vertexColors[2];l[p]=z.r,l[p+1]=z.g,l[p+2]=z.b,l[p+3]=A.r,l[p+4]=A.g,l[p+5]=A.b,l[p+6]=B.r,l[p+7]=B.g,l[p+8]=B.b}if(h===!0){var C=f[0][n][0],D=f[0][n][1],E=f[0][n][2];m[o]=C.x,m[o+1]=C.y,m[o+2]=D.x,m[o+3]=D.y,m[o+4]=E.x,m[o+5]=E.y}}return this.computeBoundingSphere(),this},computeBoundingBox:function(){var b=new a.Vector3;return function(){null===this.boundingBox&&(this.boundingBox=new a.Box3);var c=this.attributes.position.array;if(c){var d=this.boundingBox;d.makeEmpty();for(var e=0,f=c.length;f>e;e+=3)b.set(c[e],c[e+1],c[e+2]),d.expandByPoint(b)}(void 0===c||0===c.length)&&(this.boundingBox.min.set(0,0,0),this.boundingBox.max.set(0,0,0)),(isNaN(this.boundingBox.min.x)||isNaN(this.boundingBox.min.y)||isNaN(this.boundingBox.min.z))&&console.error('THREE.BufferGeometry.computeBoundingBox: Computed min/max have NaN values. The "position" attribute is likely to have NaN values.')}}(),computeBoundingSphere:function(){var b=new a.Box3,c=new a.Vector3;return function(){null===this.boundingSphere&&(this.boundingSphere=new a.Sphere);var d=this.attributes.position.array;if(d){b.makeEmpty();for(var e=this.boundingSphere.center,f=0,g=d.length;g>f;f+=3)c.set(d[f],d[f+1],d[f+2]),b.expandByPoint(c);b.center(e);for(var h=0,f=0,g=d.length;g>f;f+=3)c.set(d[f],d[f+1],d[f+2]),h=Math.max(h,e.distanceToSquared(c));this.boundingSphere.radius=Math.sqrt(h),isNaN(this.boundingSphere.radius)&&console.error('THREE.BufferGeometry.computeBoundingSphere(): Computed radius is NaN. The "position" attribute is likely to have NaN values.')}}}(),computeFaceNormals:function(){},computeVertexNormals:function(){var b=this.attributes;if(b.position){var c=b.position.array;if(void 0===b.normal)this.addAttribute("normal",new a.BufferAttribute(new Float32Array(c.length),3));else for(var d=b.normal.array,e=0,f=d.length;f>e;e++)d[e]=0;var g,h,i,d=b.normal.array,j=new a.Vector3,k=new a.Vector3,l=new a.Vector3,m=new a.Vector3,n=new a.Vector3;if(b.index)for(var o=b.index.array,p=this.offsets.length>0?this.offsets:[{start:0,count:o.length,index:0}],q=0,r=p.length;r>q;++q)for(var s=p[q].start,t=p[q].count,u=p[q].index,e=s,f=s+t;f>e;e+=3)g=3*(u+o[e]),h=3*(u+o[e+1]),i=3*(u+o[e+2]),j.fromArray(c,g),k.fromArray(c,h),l.fromArray(c,i),m.subVectors(l,k),n.subVectors(j,k),m.cross(n),d[g]+=m.x,d[g+1]+=m.y,d[g+2]+=m.z,d[h]+=m.x,d[h+1]+=m.y,d[h+2]+=m.z,d[i]+=m.x,d[i+1]+=m.y,d[i+2]+=m.z;else for(var e=0,f=c.length;f>e;e+=9)j.fromArray(c,e),k.fromArray(c,e+3),l.fromArray(c,e+6),m.subVectors(l,k),n.subVectors(j,k),m.cross(n),d[e]=m.x,d[e+1]=m.y,d[e+2]=m.z,d[e+3]=m.x,d[e+4]=m.y,d[e+5]=m.z,d[e+6]=m.x,d[e+7]=m.y,d[e+8]=m.z;this.normalizeNormals(),b.normal.needsUpdate=!0}},computeTangents:function(){function b(a,b,c){E.fromArray(e,3*a),F.fromArray(e,3*b),G.fromArray(e,3*c),H.fromArray(g,2*a),I.fromArray(g,2*b),J.fromArray(g,2*c),m=F.x-E.x,n=G.x-E.x,o=F.y-E.y,p=G.y-E.y,q=F.z-E.z,r=G.z-E.z,s=I.x-H.x,t=J.x-H.x,u=I.y-H.y,v=J.y-H.y,w=1/(s*v-t*u),K.set((v*m-u*n)*w,(v*o-u*p)*w,(v*q-u*r)*w),L.set((s*n-t*m)*w,(s*p-t*o)*w,(s*r-t*q)*w),j[a].add(K),j[b].add(K),j[c].add(K),k[a].add(L),k[b].add(L),k[c].add(L)}function c(a){V.fromArray(f,3*a),W.copy(V),R=j[a],T.copy(R),T.sub(V.multiplyScalar(V.dot(R))).normalize(),U.crossVectors(W,R),S=U.dot(k[a]),Q=0>S?-1:1,i[4*a]=T.x,i[4*a+1]=T.y,i[4*a+2]=T.z,i[4*a+3]=Q}if(void 0===this.attributes.index||void 0===this.attributes.position||void 0===this.attributes.normal||void 0===this.attributes.uv)return void console.warn("Missing required attributes (index, position, normal or uv) in BufferGeometry.computeTangents()");var d=this.attributes.index.array,e=this.attributes.position.array,f=this.attributes.normal.array,g=this.attributes.uv.array,h=e.length/3;void 0===this.attributes.tangent&&this.addAttribute("tangent",new a.BufferAttribute(new Float32Array(4*h),4));for(var i=this.attributes.tangent.array,j=[],k=[],l=0;h>l;l++)j[l]=new a.Vector3,k[l]=new a.Vector3;var m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E=new a.Vector3,F=new a.Vector3,G=new a.Vector3,H=new a.Vector2,I=new a.Vector2,J=new a.Vector2,K=new a.Vector3,L=new a.Vector3;0===this.drawcalls.length&&this.addDrawCall(0,d.length,0);var M=this.drawcalls;for(z=0,A=M.length;A>z;++z){var N=M[z].start,O=M[z].count,P=M[z].index;for(x=N,y=N+O;y>x;x+=3)B=P+d[x],C=P+d[x+1],D=P+d[x+2],b(B,C,D)}var Q,R,S,T=new a.Vector3,U=new a.Vector3,V=new a.Vector3,W=new a.Vector3;for(z=0,A=M.length;A>z;++z){var N=M[z].start,O=M[z].count,P=M[z].index;for(x=N,y=N+O;y>x;x+=3)B=P+d[x],C=P+d[x+1],D=P+d[x+2],c(B),c(C),c(D)}},computeOffsets:function(a){var b=a;void 0===a&&(b=65535);for(var c=(Date.now(),this.attributes.index.array),d=this.attributes.position.array,e=(d.length/3,c.length/3),f=new Uint16Array(c.length),g=0,h=0,i=[{start:0,count:0,index:0}],j=i[0],k=0,l=0,m=new Int32Array(6),n=new Int32Array(d.length),o=new Int32Array(d.length),p=0;p<d.length;p++)n[p]=-1,o[p]=-1;for(var q=0;e>q;q++){l=0;for(var r=0;3>r;r++){var s=c[3*q+r];-1==n[s]?(m[2*r]=s,m[2*r+1]=-1,l++):n[s]<j.index?(m[2*r]=s,m[2*r+1]=-1,k++):(m[2*r]=s,m[2*r+1]=n[s])}var t=h+l;if(t>j.index+b){var u={start:g,count:0,index:h};i.push(u),j=u;for(var v=0;6>v;v+=2){var w=m[v+1];w>-1&&w<j.index&&(m[v+1]=-1)}}for(var v=0;6>v;v+=2){var s=m[v],w=m[v+1];-1===w&&(w=h++),n[s]=w,o[w]=s,f[g++]=w-j.index,j.count++}}return this.reorderBuffers(f,o,h),this.offsets=i,i},merge:function(){console.log("BufferGeometry.merge(): TODO")},normalizeNormals:function(){for(var a,b,c,d,e=this.attributes.normal.array,f=0,g=e.length;g>f;f+=3)a=e[f],b=e[f+1],c=e[f+2],d=1/Math.sqrt(a*a+b*b+c*c),e[f]*=d,e[f+1]*=d,e[f+2]*=d},reorderBuffers:function(a,b,c){var d={};for(var e in this.attributes)if("index"!=e){var f=this.attributes[e].array;d[e]=new f.constructor(this.attributes[e].itemSize*c)}for(var g=0;c>g;g++){var h=b[g];for(var e in this.attributes)if("index"!=e)for(var i=this.attributes[e].array,j=this.attributes[e].itemSize,k=d[e],l=0;j>l;l++)k[g*j+l]=i[h*j+l]}this.attributes.index.array=a;for(var e in this.attributes)"index"!=e&&(this.attributes[e].array=d[e],this.attributes[e].numItems=this.attributes[e].itemSize*c)},toJSON:function(){var a={metadata:{version:4,type:"BufferGeometry",generator:"BufferGeometryExporter"},uuid:this.uuid,type:this.type,data:{attributes:{}}},b=this.attributes,c=this.offsets,d=this.boundingSphere;for(var e in b){for(var f=b[e],g=[],h=f.array,i=0,j=h.length;j>i;i++)g[i]=h[i];a.data.attributes[e]={itemSize:f.itemSize,type:f.array.constructor.name,array:g}}return c.length>0&&(a.data.offsets=JSON.parse(JSON.stringify(c))),null!==d&&(a.data.boundingSphere={center:d.center.toArray(),radius:d.radius}),a},clone:function(){var b=new a.BufferGeometry;for(var c in this.attributes){var d=this.attributes[c];b.addAttribute(c,d.clone())}for(var e=0,f=this.offsets.length;f>e;e++){var g=this.offsets[e];b.offsets.push({start:g.start,index:g.index,count:g.count})}return b},dispose:function(){this.dispatchEvent({type:"dispose"})}},a.EventDispatcher.prototype.apply(a.BufferGeometry.prototype),a.Geometry=function(){Object.defineProperty(this,"id",{value:a.GeometryIdCount++}),this.uuid=a.Math.generateUUID(),this.name="",this.type="Geometry",this.vertices=[],this.colors=[],this.faces=[],this.faceVertexUvs=[[]],this.morphTargets=[],this.morphColors=[],this.morphNormals=[],this.skinWeights=[],this.skinIndices=[],this.lineDistances=[],this.boundingBox=null,this.boundingSphere=null,this.hasTangents=!1,this.dynamic=!0,this.verticesNeedUpdate=!1,this.elementsNeedUpdate=!1,this.uvsNeedUpdate=!1,this.normalsNeedUpdate=!1,this.tangentsNeedUpdate=!1,this.colorsNeedUpdate=!1,this.lineDistancesNeedUpdate=!1,this.groupsNeedUpdate=!1},a.Geometry.prototype={constructor:a.Geometry,applyMatrix:function(b){for(var c=(new a.Matrix3).getNormalMatrix(b),d=0,e=this.vertices.length;e>d;d++){var f=this.vertices[d];f.applyMatrix4(b)}for(var d=0,e=this.faces.length;e>d;d++){var g=this.faces[d];g.normal.applyMatrix3(c).normalize();for(var h=0,i=g.vertexNormals.length;i>h;h++)g.vertexNormals[h].applyMatrix3(c).normalize()}this.boundingBox instanceof a.Box3&&this.computeBoundingBox(),this.boundingSphere instanceof a.Sphere&&this.computeBoundingSphere()},fromBufferGeometry:function(b){for(var c=this,d=b.attributes,e=d.position.array,f=void 0!==d.index?d.index.array:void 0,g=void 0!==d.normal?d.normal.array:void 0,h=void 0!==d.color?d.color.array:void 0,i=void 0!==d.uv?d.uv.array:void 0,j=[],k=[],l=0,m=0;l<e.length;l+=3,m+=2)c.vertices.push(new a.Vector3(e[l],e[l+1],e[l+2])),void 0!==g&&j.push(new a.Vector3(g[l],g[l+1],g[l+2])),void 0!==h&&c.colors.push(new a.Color(h[l],h[l+1],h[l+2])),void 0!==i&&k.push(new a.Vector2(i[m],i[m+1]));var n=function(b,d,e){var f=void 0!==g?[j[b].clone(),j[d].clone(),j[e].clone()]:[],i=void 0!==h?[c.colors[b].clone(),c.colors[d].clone(),c.colors[e].clone()]:[];c.faces.push(new a.Face3(b,d,e,f,i)),c.faceVertexUvs[0].push([k[b],k[d],k[e]])};if(void 0!==f)for(var l=0;l<f.length;l+=3)n(f[l],f[l+1],f[l+2]);else for(var l=0;l<e.length/3;l+=3)n(l,l+1,l+2);return this.computeFaceNormals(),null!==b.boundingBox&&(this.boundingBox=b.boundingBox.clone()),null!==b.boundingSphere&&(this.boundingSphere=b.boundingSphere.clone()),this},center:function(){this.computeBoundingBox();var b=new a.Vector3;return b.addVectors(this.boundingBox.min,this.boundingBox.max),b.multiplyScalar(-.5),this.applyMatrix((new a.Matrix4).makeTranslation(b.x,b.y,b.z)),this.computeBoundingBox(),b},computeFaceNormals:function(){for(var b=new a.Vector3,c=new a.Vector3,d=0,e=this.faces.length;e>d;d++){var f=this.faces[d],g=this.vertices[f.a],h=this.vertices[f.b],i=this.vertices[f.c];b.subVectors(i,h),c.subVectors(g,h),b.cross(c),b.normalize(),f.normal.copy(b)}},computeVertexNormals:function(b){var c,d,e,f,g,h;for(h=new Array(this.vertices.length),c=0,d=this.vertices.length;d>c;c++)h[c]=new a.Vector3;if(b){var i,j,k,l=new a.Vector3,m=new a.Vector3;new a.Vector3,new a.Vector3,new a.Vector3;for(e=0,f=this.faces.length;f>e;e++)g=this.faces[e],i=this.vertices[g.a],j=this.vertices[g.b],k=this.vertices[g.c],l.subVectors(k,j),m.subVectors(i,j),l.cross(m),h[g.a].add(l),h[g.b].add(l),h[g.c].add(l)}else for(e=0,f=this.faces.length;f>e;e++)g=this.faces[e],h[g.a].add(g.normal),h[g.b].add(g.normal),h[g.c].add(g.normal);for(c=0,d=this.vertices.length;d>c;c++)h[c].normalize();for(e=0,f=this.faces.length;f>e;e++)g=this.faces[e],g.vertexNormals[0]=h[g.a].clone(),g.vertexNormals[1]=h[g.b].clone(),g.vertexNormals[2]=h[g.c].clone()},computeMorphNormals:function(){var b,c,d,e,f;for(d=0,e=this.faces.length;e>d;d++)for(f=this.faces[d],f.__originalFaceNormal?f.__originalFaceNormal.copy(f.normal):f.__originalFaceNormal=f.normal.clone(),f.__originalVertexNormals||(f.__originalVertexNormals=[]),b=0,c=f.vertexNormals.length;c>b;b++)f.__originalVertexNormals[b]?f.__originalVertexNormals[b].copy(f.vertexNormals[b]):f.__originalVertexNormals[b]=f.vertexNormals[b].clone();var g=new a.Geometry;for(g.faces=this.faces,b=0,c=this.morphTargets.length;c>b;b++){if(!this.morphNormals[b]){this.morphNormals[b]={},this.morphNormals[b].faceNormals=[],this.morphNormals[b].vertexNormals=[];var h,i,j=this.morphNormals[b].faceNormals,k=this.morphNormals[b].vertexNormals;for(d=0,e=this.faces.length;e>d;d++)h=new a.Vector3,i={a:new a.Vector3,b:new a.Vector3,c:new a.Vector3},j.push(h),k.push(i)}var l=this.morphNormals[b];g.vertices=this.morphTargets[b].vertices,g.computeFaceNormals(),g.computeVertexNormals();var h,i;for(d=0,e=this.faces.length;e>d;d++)f=this.faces[d],h=l.faceNormals[d],i=l.vertexNormals[d],h.copy(f.normal),i.a.copy(f.vertexNormals[0]),i.b.copy(f.vertexNormals[1]),i.c.copy(f.vertexNormals[2])}for(d=0,e=this.faces.length;e>d;d++)f=this.faces[d],f.normal=f.__originalFaceNormal,f.vertexNormals=f.__originalVertexNormals},computeTangents:function(){function b(a,b,c,d,e,f,g){k=a.vertices[b],l=a.vertices[c],m=a.vertices[d],n=j[e],o=j[f],p=j[g],q=l.x-k.x,r=m.x-k.x,s=l.y-k.y,t=m.y-k.y,u=l.z-k.z,v=m.z-k.z,w=o.x-n.x,x=p.x-n.x,y=o.y-n.y,z=p.y-n.y,A=1/(w*z-x*y),G.set((z*q-y*r)*A,(z*s-y*t)*A,(z*u-y*v)*A),H.set((w*r-x*q)*A,(w*t-x*s)*A,(w*v-x*u)*A),E[b].add(G),E[c].add(G),E[d].add(G),F[b].add(H),F[c].add(H),F[d].add(H)}var c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E=[],F=[],G=new a.Vector3,H=new a.Vector3,I=new a.Vector3,J=new a.Vector3,K=new a.Vector3;for(e=0,f=this.vertices.length;f>e;e++)E[e]=new a.Vector3,F[e]=new a.Vector3;for(c=0,d=this.faces.length;d>c;c++)i=this.faces[c],j=this.faceVertexUvs[0][c],b(this,i.a,i.b,i.c,0,1,2);var L=["a","b","c","d"];for(c=0,d=this.faces.length;d>c;c++)for(i=this.faces[c],g=0;g<Math.min(i.vertexNormals.length,3);g++)K.copy(i.vertexNormals[g]),h=i[L[g]],B=E[h],I.copy(B),I.sub(K.multiplyScalar(K.dot(B))).normalize(),
J.crossVectors(i.vertexNormals[g],B),C=J.dot(F[h]),D=0>C?-1:1,i.vertexTangents[g]=new a.Vector4(I.x,I.y,I.z,D);this.hasTangents=!0},computeLineDistances:function(){for(var a=0,b=this.vertices,c=0,d=b.length;d>c;c++)c>0&&(a+=b[c].distanceTo(b[c-1])),this.lineDistances[c]=a},computeBoundingBox:function(){null===this.boundingBox&&(this.boundingBox=new a.Box3),this.boundingBox.setFromPoints(this.vertices)},computeBoundingSphere:function(){null===this.boundingSphere&&(this.boundingSphere=new a.Sphere),this.boundingSphere.setFromPoints(this.vertices)},merge:function(b,c,d){if(b instanceof a.Geometry==!1)return void console.error("THREE.Geometry.merge(): geometry not an instance of THREE.Geometry.",b);var e,f=this.vertices.length,g=this.vertices,h=b.vertices,i=this.faces,j=b.faces,k=this.faceVertexUvs[0],l=b.faceVertexUvs[0];void 0===d&&(d=0),void 0!==c&&(e=(new a.Matrix3).getNormalMatrix(c));for(var m=0,n=h.length;n>m;m++){var o=h[m],p=o.clone();void 0!==c&&p.applyMatrix4(c),g.push(p)}for(m=0,n=j.length;n>m;m++){var q,r,s,t=j[m],u=t.vertexNormals,v=t.vertexColors;q=new a.Face3(t.a+f,t.b+f,t.c+f),q.normal.copy(t.normal),void 0!==e&&q.normal.applyMatrix3(e).normalize();for(var w=0,x=u.length;x>w;w++)r=u[w].clone(),void 0!==e&&r.applyMatrix3(e).normalize(),q.vertexNormals.push(r);q.color.copy(t.color);for(var w=0,x=v.length;x>w;w++)s=v[w],q.vertexColors.push(s.clone());q.materialIndex=t.materialIndex+d,i.push(q)}for(m=0,n=l.length;n>m;m++){var y=l[m],z=[];if(void 0!==y){for(var w=0,x=y.length;x>w;w++)z.push(new a.Vector2(y[w].x,y[w].y));k.push(z)}}},mergeVertices:function(){var a,b,c,d,e,f,g,h,i={},j=[],k=[],l=4,m=Math.pow(10,l);for(c=0,d=this.vertices.length;d>c;c++)a=this.vertices[c],b=Math.round(a.x*m)+"_"+Math.round(a.y*m)+"_"+Math.round(a.z*m),void 0===i[b]?(i[b]=c,j.push(this.vertices[c]),k[c]=j.length-1):k[c]=k[i[b]];var n=[];for(c=0,d=this.faces.length;d>c;c++){e=this.faces[c],e.a=k[e.a],e.b=k[e.b],e.c=k[e.c],f=[e.a,e.b,e.c];for(var o=-1,p=0;3>p;p++)if(f[p]==f[(p+1)%3]){o=p,n.push(c);break}}for(c=n.length-1;c>=0;c--){var q=n[c];for(this.faces.splice(q,1),g=0,h=this.faceVertexUvs.length;h>g;g++)this.faceVertexUvs[g].splice(q,1)}var r=this.vertices.length-j.length;return this.vertices=j,r},toJSON:function(){function a(a,b,c){return c?a|1<<b:a&~(1<<b)}function b(a){var b=a.x.toString()+a.y.toString()+a.z.toString();return void 0!==m[b]?m[b]:(m[b]=l.length/3,l.push(a.x,a.y,a.z),m[b])}function c(a){var b=a.r.toString()+a.g.toString()+a.b.toString();return void 0!==o[b]?o[b]:(o[b]=n.length,n.push(a.getHex()),o[b])}function d(a){var b=a.x.toString()+a.y.toString();return void 0!==q[b]?q[b]:(q[b]=p.length/2,p.push(a.x,a.y),q[b])}var e={metadata:{version:4,type:"BufferGeometry",generator:"BufferGeometryExporter"},uuid:this.uuid,type:this.type};if(""!==this.name&&(e.name=this.name),void 0!==this.parameters){var f=this.parameters;for(var g in f)void 0!==f[g]&&(e[g]=f[g]);return e}for(var h=[],i=0;i<this.vertices.length;i++){var j=this.vertices[i];h.push(j.x,j.y,j.z)}for(var k=[],l=[],m={},n=[],o={},p=[],q={},i=0;i<this.faces.length;i++){var r=this.faces[i],s=!1,t=!1,u=void 0!==this.faceVertexUvs[0][i],v=r.normal.length()>0,w=r.vertexNormals.length>0,x=1!==r.color.r||1!==r.color.g||1!==r.color.b,y=r.vertexColors.length>0,z=0;if(z=a(z,0,0),z=a(z,1,s),z=a(z,2,t),z=a(z,3,u),z=a(z,4,v),z=a(z,5,w),z=a(z,6,x),z=a(z,7,y),k.push(z),k.push(r.a,r.b,r.c),u){var A=this.faceVertexUvs[0][i];k.push(d(A[0]),d(A[1]),d(A[2]))}if(v&&k.push(b(r.normal)),w){var B=r.vertexNormals;k.push(b(B[0]),b(B[1]),b(B[2]))}if(x&&k.push(c(r.color)),y){var C=r.vertexColors;k.push(c(C[0]),c(C[1]),c(C[2]))}}return e.data={},e.data.vertices=h,e.data.normals=l,n.length>0&&(e.data.colors=n),p.length>0&&(e.data.uvs=[p]),e.data.faces=k,e},clone:function(){for(var b=new a.Geometry,c=this.vertices,d=0,e=c.length;e>d;d++)b.vertices.push(c[d].clone());for(var f=this.faces,d=0,e=f.length;e>d;d++)b.faces.push(f[d].clone());for(var g=this.faceVertexUvs[0],d=0,e=g.length;e>d;d++){for(var h=g[d],i=[],j=0,k=h.length;k>j;j++)i.push(new a.Vector2(h[j].x,h[j].y));b.faceVertexUvs[0].push(i)}return b},dispose:function(){this.dispatchEvent({type:"dispose"})}},a.EventDispatcher.prototype.apply(a.Geometry.prototype),a.GeometryIdCount=0,a.Camera=function(){a.Object3D.call(this),this.type="Camera",this.matrixWorldInverse=new a.Matrix4,this.projectionMatrix=new a.Matrix4},a.Camera.prototype=Object.create(a.Object3D.prototype),a.Camera.prototype.getWorldDirection=function(){var b=new a.Quaternion;return function(c){var d=c||new a.Vector3;return this.getWorldQuaternion(b),d.set(0,0,-1).applyQuaternion(b)}}(),a.Camera.prototype.lookAt=function(){var b=new a.Matrix4;return function(a){b.lookAt(this.position,a,this.up),this.quaternion.setFromRotationMatrix(b)}}(),a.Camera.prototype.clone=function(b){return void 0===b&&(b=new a.Camera),a.Object3D.prototype.clone.call(this,b),b.matrixWorldInverse.copy(this.matrixWorldInverse),b.projectionMatrix.copy(this.projectionMatrix),b},a.CubeCamera=function(b,c,d){a.Object3D.call(this),this.type="CubeCamera";var e=90,f=1,g=new a.PerspectiveCamera(e,f,b,c);g.up.set(0,-1,0),g.lookAt(new a.Vector3(1,0,0)),this.add(g);var h=new a.PerspectiveCamera(e,f,b,c);h.up.set(0,-1,0),h.lookAt(new a.Vector3(-1,0,0)),this.add(h);var i=new a.PerspectiveCamera(e,f,b,c);i.up.set(0,0,1),i.lookAt(new a.Vector3(0,1,0)),this.add(i);var j=new a.PerspectiveCamera(e,f,b,c);j.up.set(0,0,-1),j.lookAt(new a.Vector3(0,-1,0)),this.add(j);var k=new a.PerspectiveCamera(e,f,b,c);k.up.set(0,-1,0),k.lookAt(new a.Vector3(0,0,1)),this.add(k);var l=new a.PerspectiveCamera(e,f,b,c);l.up.set(0,-1,0),l.lookAt(new a.Vector3(0,0,-1)),this.add(l),this.renderTarget=new a.WebGLRenderTargetCube(d,d,{format:a.RGBFormat,magFilter:a.LinearFilter,minFilter:a.LinearFilter}),this.updateCubeMap=function(a,b){var c=this.renderTarget,d=c.generateMipmaps;c.generateMipmaps=!1,c.activeCubeFace=0,a.render(b,g,c),c.activeCubeFace=1,a.render(b,h,c),c.activeCubeFace=2,a.render(b,i,c),c.activeCubeFace=3,a.render(b,j,c),c.activeCubeFace=4,a.render(b,k,c),c.generateMipmaps=d,c.activeCubeFace=5,a.render(b,l,c)}},a.CubeCamera.prototype=Object.create(a.Object3D.prototype),a.OrthographicCamera=function(b,c,d,e,f,g){a.Camera.call(this),this.type="OrthographicCamera",this.zoom=1,this.left=b,this.right=c,this.top=d,this.bottom=e,this.near=void 0!==f?f:.1,this.far=void 0!==g?g:2e3,this.updateProjectionMatrix()},a.OrthographicCamera.prototype=Object.create(a.Camera.prototype),a.OrthographicCamera.prototype.updateProjectionMatrix=function(){var a=(this.right-this.left)/(2*this.zoom),b=(this.top-this.bottom)/(2*this.zoom),c=(this.right+this.left)/2,d=(this.top+this.bottom)/2;this.projectionMatrix.makeOrthographic(c-a,c+a,d+b,d-b,this.near,this.far)},a.OrthographicCamera.prototype.clone=function(){var b=new a.OrthographicCamera;return a.Camera.prototype.clone.call(this,b),b.zoom=this.zoom,b.left=this.left,b.right=this.right,b.top=this.top,b.bottom=this.bottom,b.near=this.near,b.far=this.far,b.projectionMatrix.copy(this.projectionMatrix),b},a.PerspectiveCamera=function(b,c,d,e){a.Camera.call(this),this.type="PerspectiveCamera",this.zoom=1,this.fov=void 0!==b?b:50,this.aspect=void 0!==c?c:1,this.near=void 0!==d?d:.1,this.far=void 0!==e?e:2e3,this.updateProjectionMatrix()},a.PerspectiveCamera.prototype=Object.create(a.Camera.prototype),a.PerspectiveCamera.prototype.setLens=function(b,c){void 0===c&&(c=24),this.fov=2*a.Math.radToDeg(Math.atan(c/(2*b))),this.updateProjectionMatrix()},a.PerspectiveCamera.prototype.setViewOffset=function(a,b,c,d,e,f){this.fullWidth=a,this.fullHeight=b,this.x=c,this.y=d,this.width=e,this.height=f,this.updateProjectionMatrix()},a.PerspectiveCamera.prototype.updateProjectionMatrix=function(){var b=a.Math.radToDeg(2*Math.atan(Math.tan(.5*a.Math.degToRad(this.fov))/this.zoom));if(this.fullWidth){var c=this.fullWidth/this.fullHeight,d=Math.tan(a.Math.degToRad(.5*b))*this.near,e=-d,f=c*e,g=c*d,h=Math.abs(g-f),i=Math.abs(d-e);this.projectionMatrix.makeFrustum(f+this.x*h/this.fullWidth,f+(this.x+this.width)*h/this.fullWidth,d-(this.y+this.height)*i/this.fullHeight,d-this.y*i/this.fullHeight,this.near,this.far)}else this.projectionMatrix.makePerspective(b,this.aspect,this.near,this.far)},a.PerspectiveCamera.prototype.clone=function(){var b=new a.PerspectiveCamera;return a.Camera.prototype.clone.call(this,b),b.zoom=this.zoom,b.fov=this.fov,b.aspect=this.aspect,b.near=this.near,b.far=this.far,b.projectionMatrix.copy(this.projectionMatrix),b},a.Light=function(b){a.Object3D.call(this),this.type="Light",this.color=new a.Color(b)},a.Light.prototype=Object.create(a.Object3D.prototype),a.Light.prototype.clone=function(b){return void 0===b&&(b=new a.Light),a.Object3D.prototype.clone.call(this,b),b.color.copy(this.color),b},a.AmbientLight=function(b){a.Light.call(this,b),this.type="AmbientLight"},a.AmbientLight.prototype=Object.create(a.Light.prototype),a.AmbientLight.prototype.clone=function(){var b=new a.AmbientLight;return a.Light.prototype.clone.call(this,b),b},a.AreaLight=function(b,c){a.Light.call(this,b),this.type="AreaLight",this.normal=new a.Vector3(0,-1,0),this.right=new a.Vector3(1,0,0),this.intensity=void 0!==c?c:1,this.width=1,this.height=1,this.constantAttenuation=1.5,this.linearAttenuation=.5,this.quadraticAttenuation=.1},a.AreaLight.prototype=Object.create(a.Light.prototype),a.DirectionalLight=function(b,c){a.Light.call(this,b),this.type="DirectionalLight",this.position.set(0,1,0),this.target=new a.Object3D,this.intensity=void 0!==c?c:1,this.castShadow=!1,this.onlyShadow=!1,this.shadowCameraNear=50,this.shadowCameraFar=5e3,this.shadowCameraLeft=-500,this.shadowCameraRight=500,this.shadowCameraTop=500,this.shadowCameraBottom=-500,this.shadowCameraVisible=!1,this.shadowBias=0,this.shadowDarkness=.5,this.shadowMapWidth=512,this.shadowMapHeight=512,this.shadowCascade=!1,this.shadowCascadeOffset=new a.Vector3(0,0,-1e3),this.shadowCascadeCount=2,this.shadowCascadeBias=[0,0,0],this.shadowCascadeWidth=[512,512,512],this.shadowCascadeHeight=[512,512,512],this.shadowCascadeNearZ=[-1,.99,.998],this.shadowCascadeFarZ=[.99,.998,1],this.shadowCascadeArray=[],this.shadowMap=null,this.shadowMapSize=null,this.shadowCamera=null,this.shadowMatrix=null},a.DirectionalLight.prototype=Object.create(a.Light.prototype),a.DirectionalLight.prototype.clone=function(){var b=new a.DirectionalLight;return a.Light.prototype.clone.call(this,b),b.target=this.target.clone(),b.intensity=this.intensity,b.castShadow=this.castShadow,b.onlyShadow=this.onlyShadow,b.shadowCameraNear=this.shadowCameraNear,b.shadowCameraFar=this.shadowCameraFar,b.shadowCameraLeft=this.shadowCameraLeft,b.shadowCameraRight=this.shadowCameraRight,b.shadowCameraTop=this.shadowCameraTop,b.shadowCameraBottom=this.shadowCameraBottom,b.shadowCameraVisible=this.shadowCameraVisible,b.shadowBias=this.shadowBias,b.shadowDarkness=this.shadowDarkness,b.shadowMapWidth=this.shadowMapWidth,b.shadowMapHeight=this.shadowMapHeight,b.shadowCascade=this.shadowCascade,b.shadowCascadeOffset.copy(this.shadowCascadeOffset),b.shadowCascadeCount=this.shadowCascadeCount,b.shadowCascadeBias=this.shadowCascadeBias.slice(0),b.shadowCascadeWidth=this.shadowCascadeWidth.slice(0),b.shadowCascadeHeight=this.shadowCascadeHeight.slice(0),b.shadowCascadeNearZ=this.shadowCascadeNearZ.slice(0),b.shadowCascadeFarZ=this.shadowCascadeFarZ.slice(0),b},a.HemisphereLight=function(b,c,d){a.Light.call(this,b),this.type="HemisphereLight",this.position.set(0,100,0),this.groundColor=new a.Color(c),this.intensity=void 0!==d?d:1},a.HemisphereLight.prototype=Object.create(a.Light.prototype),a.HemisphereLight.prototype.clone=function(){var b=new a.HemisphereLight;return a.Light.prototype.clone.call(this,b),b.groundColor.copy(this.groundColor),b.intensity=this.intensity,b},a.PointLight=function(b,c,d){a.Light.call(this,b),this.type="PointLight",this.intensity=void 0!==c?c:1,this.distance=void 0!==d?d:0},a.PointLight.prototype=Object.create(a.Light.prototype),a.PointLight.prototype.clone=function(){var b=new a.PointLight;return a.Light.prototype.clone.call(this,b),b.intensity=this.intensity,b.distance=this.distance,b},a.SpotLight=function(b,c,d,e,f){a.Light.call(this,b),this.type="SpotLight",this.position.set(0,1,0),this.target=new a.Object3D,this.intensity=void 0!==c?c:1,this.distance=void 0!==d?d:0,this.angle=void 0!==e?e:Math.PI/3,this.exponent=void 0!==f?f:10,this.castShadow=!1,this.onlyShadow=!1,this.shadowCameraNear=50,this.shadowCameraFar=5e3,this.shadowCameraFov=50,this.shadowCameraVisible=!1,this.shadowBias=0,this.shadowDarkness=.5,this.shadowMapWidth=512,this.shadowMapHeight=512,this.shadowMap=null,this.shadowMapSize=null,this.shadowCamera=null,this.shadowMatrix=null},a.SpotLight.prototype=Object.create(a.Light.prototype),a.SpotLight.prototype.clone=function(){var b=new a.SpotLight;return a.Light.prototype.clone.call(this,b),b.target=this.target.clone(),b.intensity=this.intensity,b.distance=this.distance,b.angle=this.angle,b.exponent=this.exponent,b.castShadow=this.castShadow,b.onlyShadow=this.onlyShadow,b.shadowCameraNear=this.shadowCameraNear,b.shadowCameraFar=this.shadowCameraFar,b.shadowCameraFov=this.shadowCameraFov,b.shadowCameraVisible=this.shadowCameraVisible,b.shadowBias=this.shadowBias,b.shadowDarkness=this.shadowDarkness,b.shadowMapWidth=this.shadowMapWidth,b.shadowMapHeight=this.shadowMapHeight,b},a.Cache=function(){this.files={}},a.Cache.prototype={constructor:a.Cache,add:function(a,b){this.files[a]=b},get:function(a){return this.files[a]},remove:function(a){delete this.files[a]},clear:function(){this.files={}}},a.Loader=function(b){this.showStatus=b,this.statusDomElement=b?a.Loader.prototype.addStatusElement():null,this.imageLoader=new a.ImageLoader,this.onLoadStart=function(){},this.onLoadProgress=function(){},this.onLoadComplete=function(){}},a.Loader.prototype={constructor:a.Loader,crossOrigin:void 0,addStatusElement:function(){var a=document.createElement("div");return a.style.position="absolute",a.style.right="0px",a.style.top="0px",a.style.fontSize="0.8em",a.style.textAlign="left",a.style.background="rgba(0,0,0,0.25)",a.style.color="#fff",a.style.width="120px",a.style.padding="0.5em 0.5em 0.5em 0.5em",a.style.zIndex=1e3,a.innerHTML="Loading ...",a},updateProgress:function(a){var b="Loaded ";b+=a.total?(100*a.loaded/a.total).toFixed(0)+"%":(a.loaded/1024).toFixed(2)+" KB",this.statusDomElement.innerHTML=b},extractUrlBase:function(a){var b=a.split("/");return 1===b.length?"./":(b.pop(),b.join("/")+"/")},initMaterials:function(a,b){for(var c=[],d=0;d<a.length;++d)c[d]=this.createMaterial(a[d],b);return c},needsTangents:function(b){for(var c=0,d=b.length;d>c;c++){var e=b[c];if(e instanceof a.ShaderMaterial)return!0}return!1},createMaterial:function(b,c){function d(a){var b=Math.log(a)/Math.LN2;return Math.pow(2,Math.round(b))}function e(b,e,f,h,i,j,k){var l,m=c+f,n=a.Loader.Handlers.get(m);if(null!==n?l=n.load(m):(l=new a.Texture,n=g.imageLoader,n.crossOrigin=g.crossOrigin,n.load(m,function(b){if(a.Math.isPowerOfTwo(b.width)===!1||a.Math.isPowerOfTwo(b.height)===!1){var c=d(b.width),e=d(b.height),f=document.createElement("canvas");f.width=c,f.height=e;var g=f.getContext("2d");g.drawImage(b,0,0,c,e),l.image=f}else l.image=b;l.needsUpdate=!0})),l.sourceFile=f,h&&(l.repeat.set(h[0],h[1]),1!==h[0]&&(l.wrapS=a.RepeatWrapping),1!==h[1]&&(l.wrapT=a.RepeatWrapping)),i&&l.offset.set(i[0],i[1]),j){var o={repeat:a.RepeatWrapping,mirror:a.MirroredRepeatWrapping};void 0!==o[j[0]]&&(l.wrapS=o[j[0]]),void 0!==o[j[1]]&&(l.wrapT=o[j[1]])}k&&(l.anisotropy=k),b[e]=l}function f(a){return(255*a[0]<<16)+(255*a[1]<<8)+255*a[2]}var g=this,h="MeshLambertMaterial",i={color:15658734,opacity:1,map:null,lightMap:null,normalMap:null,bumpMap:null,wireframe:!1};if(b.shading){var j=b.shading.toLowerCase();"phong"===j?h="MeshPhongMaterial":"basic"===j&&(h="MeshBasicMaterial")}if(void 0!==b.blending&&void 0!==a[b.blending]&&(i.blending=a[b.blending]),(void 0!==b.transparent||b.opacity<1)&&(i.transparent=b.transparent),void 0!==b.depthTest&&(i.depthTest=b.depthTest),void 0!==b.depthWrite&&(i.depthWrite=b.depthWrite),void 0!==b.visible&&(i.visible=b.visible),void 0!==b.flipSided&&(i.side=a.BackSide),void 0!==b.doubleSided&&(i.side=a.DoubleSide),void 0!==b.wireframe&&(i.wireframe=b.wireframe),void 0!==b.vertexColors&&("face"===b.vertexColors?i.vertexColors=a.FaceColors:b.vertexColors&&(i.vertexColors=a.VertexColors)),b.colorDiffuse?i.color=f(b.colorDiffuse):b.DbgColor&&(i.color=b.DbgColor),b.colorSpecular&&(i.specular=f(b.colorSpecular)),b.colorAmbient&&(i.ambient=f(b.colorAmbient)),b.colorEmissive&&(i.emissive=f(b.colorEmissive)),b.transparency&&(i.opacity=b.transparency),b.specularCoef&&(i.shininess=b.specularCoef),b.mapDiffuse&&c&&e(i,"map",b.mapDiffuse,b.mapDiffuseRepeat,b.mapDiffuseOffset,b.mapDiffuseWrap,b.mapDiffuseAnisotropy),b.mapLight&&c&&e(i,"lightMap",b.mapLight,b.mapLightRepeat,b.mapLightOffset,b.mapLightWrap,b.mapLightAnisotropy),b.mapBump&&c&&e(i,"bumpMap",b.mapBump,b.mapBumpRepeat,b.mapBumpOffset,b.mapBumpWrap,b.mapBumpAnisotropy),b.mapNormal&&c&&e(i,"normalMap",b.mapNormal,b.mapNormalRepeat,b.mapNormalOffset,b.mapNormalWrap,b.mapNormalAnisotropy),b.mapSpecular&&c&&e(i,"specularMap",b.mapSpecular,b.mapSpecularRepeat,b.mapSpecularOffset,b.mapSpecularWrap,b.mapSpecularAnisotropy),b.mapAlpha&&c&&e(i,"alphaMap",b.mapAlpha,b.mapAlphaRepeat,b.mapAlphaOffset,b.mapAlphaWrap,b.mapAlphaAnisotropy),b.mapBumpScale&&(i.bumpScale=b.mapBumpScale),b.mapNormal){var k=a.ShaderLib.normalmap,l=a.UniformsUtils.clone(k.uniforms);l.tNormal.value=i.normalMap,b.mapNormalFactor&&l.uNormalScale.value.set(b.mapNormalFactor,b.mapNormalFactor),i.map&&(l.tDiffuse.value=i.map,l.enableDiffuse.value=!0),i.specularMap&&(l.tSpecular.value=i.specularMap,l.enableSpecular.value=!0),i.lightMap&&(l.tAO.value=i.lightMap,l.enableAO.value=!0),l.diffuse.value.setHex(i.color),l.specular.value.setHex(i.specular),l.ambient.value.setHex(i.ambient),l.shininess.value=i.shininess,void 0!==i.opacity&&(l.opacity.value=i.opacity);var m={fragmentShader:k.fragmentShader,vertexShader:k.vertexShader,uniforms:l,lights:!0,fog:!0},n=new a.ShaderMaterial(m);i.transparent&&(n.transparent=!0)}else var n=new a[h](i);return void 0!==b.DbgName&&(n.name=b.DbgName),n}},a.Loader.Handlers={handlers:[],add:function(a,b){this.handlers.push(a,b)},get:function(a){for(var b=0,c=this.handlers.length;c>b;b+=2){var d=this.handlers[b],e=this.handlers[b+1];if(d.test(a))return e}return null}},a.XHRLoader=function(b){this.cache=new a.Cache,this.manager=void 0!==b?b:a.DefaultLoadingManager},a.XHRLoader.prototype={constructor:a.XHRLoader,load:function(a,b,c,d){var e=this,f=e.cache.get(a);if(void 0!==f)return void(b&&b(f));var g=new XMLHttpRequest;g.open("GET",a,!0),g.addEventListener("load",function(c){e.cache.add(a,this.response),b&&b(this.response),e.manager.itemEnd(a)},!1),void 0!==c&&g.addEventListener("progress",function(a){c(a)},!1),void 0!==d&&g.addEventListener("error",function(a){d(a)},!1),void 0!==this.crossOrigin&&(g.crossOrigin=this.crossOrigin),void 0!==this.responseType&&(g.responseType=this.responseType),g.send(null),e.manager.itemStart(a)},setResponseType:function(a){this.responseType=a},setCrossOrigin:function(a){this.crossOrigin=a}},a.ImageLoader=function(b){this.cache=new a.Cache,this.manager=void 0!==b?b:a.DefaultLoadingManager},a.ImageLoader.prototype={constructor:a.ImageLoader,load:function(a,b,c,d){var e=this,f=e.cache.get(a);if(void 0!==f)return void b(f);var g=document.createElement("img");return void 0!==b&&g.addEventListener("load",function(c){e.cache.add(a,this),b(this),e.manager.itemEnd(a)},!1),void 0!==c&&g.addEventListener("progress",function(a){c(a)},!1),void 0!==d&&g.addEventListener("error",function(a){d(a)},!1),void 0!==this.crossOrigin&&(g.crossOrigin=this.crossOrigin),g.src=a,e.manager.itemStart(a),g},setCrossOrigin:function(a){this.crossOrigin=a}},a.JSONLoader=function(b){a.Loader.call(this,b),this.withCredentials=!1},a.JSONLoader.prototype=Object.create(a.Loader.prototype),a.JSONLoader.prototype.load=function(a,b,c){c=c&&"string"==typeof c?c:this.extractUrlBase(a),this.onLoadStart(),this.loadAjaxJSON(this,a,b,c)},a.JSONLoader.prototype.loadAjaxJSON=function(a,b,c,d,e){var f=new XMLHttpRequest,g=0;f.onreadystatechange=function(){if(f.readyState===f.DONE)if(200===f.status||0===f.status){if(f.responseText){var h=JSON.parse(f.responseText);if(void 0!==h.metadata&&"scene"===h.metadata.type)return void console.error('THREE.JSONLoader: "'+b+'" seems to be a Scene. Use THREE.SceneLoader instead.');var i=a.parse(h,d);c(i.geometry,i.materials)}else console.error('THREE.JSONLoader: "'+b+'" seems to be unreachable or the file is empty.');a.onLoadComplete()}else console.error("THREE.JSONLoader: Couldn't load \""+b+'" ('+f.status+")");else f.readyState===f.LOADING?e&&(0===g&&(g=f.getResponseHeader("Content-Length")),e({total:g,loaded:f.responseText.length})):f.readyState===f.HEADERS_RECEIVED&&void 0!==e&&(g=f.getResponseHeader("Content-Length"))},f.open("GET",b,!0),f.withCredentials=this.withCredentials,f.send(null)},a.JSONLoader.prototype.parse=function(b,c){function d(c){function d(a,b){return a&1<<b}var e,f,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G=b.faces,H=b.vertices,I=b.normals,J=b.colors,K=0;if(void 0!==b.uvs){for(e=0;e<b.uvs.length;e++)b.uvs[e].length&&K++;for(e=0;K>e;e++)g.faceVertexUvs[e]=[]}for(i=0,j=H.length;j>i;)w=new a.Vector3,w.x=H[i++]*c,w.y=H[i++]*c,w.z=H[i++]*c,g.vertices.push(w);for(i=0,j=G.length;j>i;)if(o=G[i++],p=d(o,0),q=d(o,1),r=d(o,3),s=d(o,4),t=d(o,5),u=d(o,6),v=d(o,7),p){if(y=new a.Face3,y.a=G[i],y.b=G[i+1],y.c=G[i+3],z=new a.Face3,z.a=G[i+1],z.b=G[i+2],z.c=G[i+3],i+=4,q&&(n=G[i++],y.materialIndex=n,z.materialIndex=n),h=g.faces.length,r)for(e=0;K>e;e++)for(C=b.uvs[e],g.faceVertexUvs[e][h]=[],g.faceVertexUvs[e][h+1]=[],f=0;4>f;f++)m=G[i++],E=C[2*m],F=C[2*m+1],D=new a.Vector2(E,F),2!==f&&g.faceVertexUvs[e][h].push(D),0!==f&&g.faceVertexUvs[e][h+1].push(D);if(s&&(l=3*G[i++],y.normal.set(I[l++],I[l++],I[l]),z.normal.copy(y.normal)),t)for(e=0;4>e;e++)l=3*G[i++],B=new a.Vector3(I[l++],I[l++],I[l]),2!==e&&y.vertexNormals.push(B),0!==e&&z.vertexNormals.push(B);if(u&&(k=G[i++],A=J[k],y.color.setHex(A),z.color.setHex(A)),v)for(e=0;4>e;e++)k=G[i++],A=J[k],2!==e&&y.vertexColors.push(new a.Color(A)),0!==e&&z.vertexColors.push(new a.Color(A));g.faces.push(y),g.faces.push(z)}else{if(x=new a.Face3,x.a=G[i++],x.b=G[i++],x.c=G[i++],q&&(n=G[i++],x.materialIndex=n),h=g.faces.length,r)for(e=0;K>e;e++)for(C=b.uvs[e],g.faceVertexUvs[e][h]=[],f=0;3>f;f++)m=G[i++],E=C[2*m],F=C[2*m+1],D=new a.Vector2(E,F),g.faceVertexUvs[e][h].push(D);if(s&&(l=3*G[i++],x.normal.set(I[l++],I[l++],I[l])),t)for(e=0;3>e;e++)l=3*G[i++],B=new a.Vector3(I[l++],I[l++],I[l]),x.vertexNormals.push(B);if(u&&(k=G[i++],x.color.setHex(J[k])),v)for(e=0;3>e;e++)k=G[i++],x.vertexColors.push(new a.Color(J[k]));g.faces.push(x)}}function e(){var c=void 0!==b.influencesPerVertex?b.influencesPerVertex:2;if(b.skinWeights)for(var d=0,e=b.skinWeights.length;e>d;d+=c){var f=b.skinWeights[d],h=c>1?b.skinWeights[d+1]:0,i=c>2?b.skinWeights[d+2]:0,j=c>3?b.skinWeights[d+3]:0;g.skinWeights.push(new a.Vector4(f,h,i,j))}if(b.skinIndices)for(var d=0,e=b.skinIndices.length;e>d;d+=c){var k=b.skinIndices[d],l=c>1?b.skinIndices[d+1]:0,m=c>2?b.skinIndices[d+2]:0,n=c>3?b.skinIndices[d+3]:0;g.skinIndices.push(new a.Vector4(k,l,m,n))}g.bones=b.bones,g.bones&&g.bones.length>0&&(g.skinWeights.length!==g.skinIndices.length||g.skinIndices.length!==g.vertices.length)&&console.warn("When skinning, number of vertices ("+g.vertices.length+"), skinIndices ("+g.skinIndices.length+"), and skinWeights ("+g.skinWeights.length+") should match."),g.animation=b.animation,g.animations=b.animations}function f(c){if(void 0!==b.morphTargets){var d,e,f,h,i,j;for(d=0,e=b.morphTargets.length;e>d;d++)for(g.morphTargets[d]={},g.morphTargets[d].name=b.morphTargets[d].name,g.morphTargets[d].vertices=[],i=g.morphTargets[d].vertices,j=b.morphTargets[d].vertices,f=0,h=j.length;h>f;f+=3){var k=new a.Vector3;k.x=j[f]*c,k.y=j[f+1]*c,k.z=j[f+2]*c,i.push(k)}}if(void 0!==b.morphColors){var d,e,l,m,n,o,p;for(d=0,e=b.morphColors.length;e>d;d++)for(g.morphColors[d]={},g.morphColors[d].name=b.morphColors[d].name,g.morphColors[d].colors=[],n=g.morphColors[d].colors,o=b.morphColors[d].colors,l=0,m=o.length;m>l;l+=3)p=new a.Color(16755200),p.setRGB(o[l],o[l+1],o[l+2]),n.push(p)}}var g=new a.Geometry,h=void 0!==b.scale?1/b.scale:1;if(d(h),e(),f(h),g.computeFaceNormals(),g.computeBoundingSphere(),void 0===b.materials||0===b.materials.length)return{geometry:g};var i=this.initMaterials(b.materials,c);return this.needsTangents(i)&&g.computeTangents(),{geometry:g,materials:i}},a.LoadingManager=function(a,b,c){var d=this,e=0,f=0;this.onLoad=a,this.onProgress=b,this.onError=c,this.itemStart=function(a){f++},this.itemEnd=function(a){e++,void 0!==d.onProgress&&d.onProgress(a,e,f),e===f&&void 0!==d.onLoad&&d.onLoad()}},a.DefaultLoadingManager=new a.LoadingManager,a.BufferGeometryLoader=function(b){this.manager=void 0!==b?b:a.DefaultLoadingManager},a.BufferGeometryLoader.prototype={constructor:a.BufferGeometryLoader,load:function(b,c,d,e){var f=this,g=new a.XHRLoader;g.setCrossOrigin(this.crossOrigin),g.load(b,function(a){c(f.parse(JSON.parse(a)))},d,e)},setCrossOrigin:function(a){this.crossOrigin=a},parse:function(b){var c=new a.BufferGeometry,d=b.attributes;for(var e in d){var f=d[e],g=new self[f.type](f.array);c.addAttribute(e,new a.BufferAttribute(g,f.itemSize))}var h=b.offsets;void 0!==h&&(c.offsets=JSON.parse(JSON.stringify(h)));var i=b.boundingSphere;if(void 0!==i){var j=new a.Vector3;void 0!==i.center&&j.fromArray(i.center),c.boundingSphere=new a.Sphere(j,i.radius)}return c}},a.MaterialLoader=function(b){this.manager=void 0!==b?b:a.DefaultLoadingManager},a.MaterialLoader.prototype={constructor:a.MaterialLoader,load:function(b,c,d,e){var f=this,g=new a.XHRLoader;g.setCrossOrigin(this.crossOrigin),g.load(b,function(a){c(f.parse(JSON.parse(a)))},d,e)},setCrossOrigin:function(a){this.crossOrigin=a},parse:function(b){var c=new a[b.type];if(void 0!==b.color&&c.color.setHex(b.color),void 0!==b.ambient&&c.ambient.setHex(b.ambient),void 0!==b.emissive&&c.emissive.setHex(b.emissive),void 0!==b.specular&&c.specular.setHex(b.specular),void 0!==b.shininess&&(c.shininess=b.shininess),void 0!==b.uniforms&&(c.uniforms=b.uniforms),void 0!==b.vertexShader&&(c.vertexShader=b.vertexShader),void 0!==b.fragmentShader&&(c.fragmentShader=b.fragmentShader),void 0!==b.vertexColors&&(c.vertexColors=b.vertexColors),void 0!==b.shading&&(c.shading=b.shading),void 0!==b.blending&&(c.blending=b.blending),void 0!==b.side&&(c.side=b.side),void 0!==b.opacity&&(c.opacity=b.opacity),void 0!==b.transparent&&(c.transparent=b.transparent),void 0!==b.wireframe&&(c.wireframe=b.wireframe),void 0!==b.materials)for(var d=0,e=b.materials.length;e>d;d++)c.materials.push(this.parse(b.materials[d]));return c}},a.ObjectLoader=function(b){this.manager=void 0!==b?b:a.DefaultLoadingManager},a.ObjectLoader.prototype={constructor:a.ObjectLoader,load:function(b,c,d,e){var f=this,g=new a.XHRLoader(f.manager);g.setCrossOrigin(this.crossOrigin),g.load(b,function(a){c(f.parse(JSON.parse(a)))},d,e)},setCrossOrigin:function(a){this.crossOrigin=a},parse:function(a){var b=this.parseGeometries(a.geometries),c=this.parseMaterials(a.materials),d=this.parseObject(a.object,b,c);return d},parseGeometries:function(b){var c={};if(void 0!==b)for(var d=new a.JSONLoader,e=new a.BufferGeometryLoader,f=0,g=b.length;g>f;f++){var h,i=b[f];switch(i.type){case"PlaneGeometry":h=new a.PlaneGeometry(i.width,i.height,i.widthSegments,i.heightSegments);break;case"BoxGeometry":case"CubeGeometry":h=new a.BoxGeometry(i.width,i.height,i.depth,i.widthSegments,i.heightSegments,i.depthSegments);break;case"CircleGeometry":h=new a.CircleGeometry(i.radius,i.segments);break;case"CylinderGeometry":h=new a.CylinderGeometry(i.radiusTop,i.radiusBottom,i.height,i.radialSegments,i.heightSegments,i.openEnded);break;case"SphereGeometry":h=new a.SphereGeometry(i.radius,i.widthSegments,i.heightSegments,i.phiStart,i.phiLength,i.thetaStart,i.thetaLength);break;case"IcosahedronGeometry":h=new a.IcosahedronGeometry(i.radius,i.detail);break;case"TorusGeometry":h=new a.TorusGeometry(i.radius,i.tube,i.radialSegments,i.tubularSegments,i.arc);break;case"TorusKnotGeometry":h=new a.TorusKnotGeometry(i.radius,i.tube,i.radialSegments,i.tubularSegments,i.p,i.q,i.heightScale);break;case"BufferGeometry":h=e.parse(i.data);break;case"Geometry":h=d.parse(i.data).geometry}h.uuid=i.uuid,void 0!==i.name&&(h.name=i.name),c[i.uuid]=h}return c},parseMaterials:function(b){var c={};if(void 0!==b)for(var d=new a.MaterialLoader,e=0,f=b.length;f>e;e++){var g=b[e],h=d.parse(g);h.uuid=g.uuid,void 0!==g.name&&(h.name=g.name),c[g.uuid]=h}return c},parseObject:function(){var b=new a.Matrix4;return function(c,d,e){var f;switch(c.type){case"Scene":f=new a.Scene;break;case"PerspectiveCamera":f=new a.PerspectiveCamera(c.fov,c.aspect,c.near,c.far);break;case"OrthographicCamera":f=new a.OrthographicCamera(c.left,c.right,c.top,c.bottom,c.near,c.far);break;case"AmbientLight":f=new a.AmbientLight(c.color);break;case"DirectionalLight":f=new a.DirectionalLight(c.color,c.intensity);break;case"PointLight":f=new a.PointLight(c.color,c.intensity,c.distance);break;case"SpotLight":f=new a.SpotLight(c.color,c.intensity,c.distance,c.angle,c.exponent);break;case"HemisphereLight":f=new a.HemisphereLight(c.color,c.groundColor,c.intensity);break;case"Mesh":var g=d[c.geometry],h=e[c.material];void 0===g&&console.warn("THREE.ObjectLoader: Undefined geometry",c.geometry),void 0===h&&console.warn("THREE.ObjectLoader: Undefined material",c.material),f=new a.Mesh(g,h);break;case"Line":var g=d[c.geometry],h=e[c.material];void 0===g&&console.warn("THREE.ObjectLoader: Undefined geometry",c.geometry),void 0===h&&console.warn("THREE.ObjectLoader: Undefined material",c.material),f=new a.Line(g,h);break;case"Sprite":var h=e[c.material];void 0===h&&console.warn("THREE.ObjectLoader: Undefined material",c.material),f=new a.Sprite(h);break;case"Group":f=new a.Group;break;default:f=new a.Object3D}if(f.uuid=c.uuid,void 0!==c.name&&(f.name=c.name),void 0!==c.matrix?(b.fromArray(c.matrix),b.decompose(f.position,f.quaternion,f.scale)):(void 0!==c.position&&f.position.fromArray(c.position),void 0!==c.rotation&&f.rotation.fromArray(c.rotation),void 0!==c.scale&&f.scale.fromArray(c.scale)),void 0!==c.visible&&(f.visible=c.visible),void 0!==c.userData&&(f.userData=c.userData),void 0!==c.children)for(var i in c.children)f.add(this.parseObject(c.children[i],d,e));return f}}()},a.TextureLoader=function(b){this.manager=void 0!==b?b:a.DefaultLoadingManager},a.TextureLoader.prototype={constructor:a.TextureLoader,load:function(b,c,d,e){var f=this,g=new a.ImageLoader(f.manager);g.setCrossOrigin(this.crossOrigin),g.load(b,function(b){var d=new a.Texture(b);d.needsUpdate=!0,void 0!==c&&c(d)},d,e)},setCrossOrigin:function(a){this.crossOrigin=a}},a.CompressedTextureLoader=function(){this._parser=null},a.CompressedTextureLoader.prototype={constructor:a.CompressedTextureLoader,load:function(b,c,d){var e=this,f=[],g=new a.CompressedTexture;g.image=f;var h=new a.XHRLoader;if(h.setResponseType("arraybuffer"),b instanceof Array)for(var i=0,j=function(d){h.load(b[d],function(b){var h=e._parser(b,!0);f[d]={width:h.width,height:h.height,format:h.format,mipmaps:h.mipmaps},i+=1,6===i&&(1==h.mipmapCount&&(g.minFilter=a.LinearFilter),g.format=h.format,g.needsUpdate=!0,c&&c(g))})},k=0,l=b.length;l>k;++k)j(k);else h.load(b,function(b){var d=e._parser(b,!0);if(d.isCubemap)for(var h=d.mipmaps.length/d.mipmapCount,i=0;h>i;i++){f[i]={mipmaps:[]};for(var j=0;j<d.mipmapCount;j++)f[i].mipmaps.push(d.mipmaps[i*d.mipmapCount+j]),f[i].format=d.format,f[i].width=d.width,f[i].height=d.height}else g.image.width=d.width,g.image.height=d.height,g.mipmaps=d.mipmaps;1===d.mipmapCount&&(g.minFilter=a.LinearFilter),g.format=d.format,g.needsUpdate=!0,c&&c(g)});return g}},a.Material=function(){
Object.defineProperty(this,"id",{value:a.MaterialIdCount++}),this.uuid=a.Math.generateUUID(),this.name="",this.type="Material",this.side=a.FrontSide,this.opacity=1,this.transparent=!1,this.blending=a.NormalBlending,this.blendSrc=a.SrcAlphaFactor,this.blendDst=a.OneMinusSrcAlphaFactor,this.blendEquation=a.AddEquation,this.depthTest=!0,this.depthWrite=!0,this.polygonOffset=!1,this.polygonOffsetFactor=0,this.polygonOffsetUnits=0,this.alphaTest=0,this.overdraw=0,this.visible=!0,this.needsUpdate=!0},a.Material.prototype={constructor:a.Material,setValues:function(b){if(void 0!==b)for(var c in b){var d=b[c];if(void 0!==d){if(c in this){var e=this[c];e instanceof a.Color?e.set(d):e instanceof a.Vector3&&d instanceof a.Vector3?e.copy(d):"overdraw"==c?this[c]=Number(d):this[c]=d}}else console.warn("THREE.Material: '"+c+"' parameter is undefined.")}},toJSON:function(){var b={metadata:{version:4.2,type:"material",generator:"MaterialExporter"},uuid:this.uuid,type:this.type};return""!==this.name&&(b.name=this.name),this instanceof a.MeshBasicMaterial?(b.color=this.color.getHex(),this.vertexColors!==a.NoColors&&(b.vertexColors=this.vertexColors),this.blending!==a.NormalBlending&&(b.blending=this.blending),this.side!==a.FrontSide&&(b.side=this.side)):this instanceof a.MeshLambertMaterial?(b.color=this.color.getHex(),b.ambient=this.ambient.getHex(),b.emissive=this.emissive.getHex(),this.vertexColors!==a.NoColors&&(b.vertexColors=this.vertexColors),this.blending!==a.NormalBlending&&(b.blending=this.blending),this.side!==a.FrontSide&&(b.side=this.side)):this instanceof a.MeshPhongMaterial?(b.color=this.color.getHex(),b.ambient=this.ambient.getHex(),b.emissive=this.emissive.getHex(),b.specular=this.specular.getHex(),b.shininess=this.shininess,this.vertexColors!==a.NoColors&&(b.vertexColors=this.vertexColors),this.blending!==a.NormalBlending&&(b.blending=this.blending),this.side!==a.FrontSide&&(b.side=this.side)):this instanceof a.MeshNormalMaterial?(this.shading!==a.FlatShading&&(b.shading=this.shading),this.blending!==a.NormalBlending&&(b.blending=this.blending),this.side!==a.FrontSide&&(b.side=this.side)):this instanceof a.MeshDepthMaterial?(this.blending!==a.NormalBlending&&(b.blending=this.blending),this.side!==a.FrontSide&&(b.side=this.side)):this instanceof a.ShaderMaterial?(b.uniforms=this.uniforms,b.vertexShader=this.vertexShader,b.fragmentShader=this.fragmentShader):this instanceof a.SpriteMaterial&&(b.color=this.color.getHex()),this.opacity<1&&(b.opacity=this.opacity),this.transparent!==!1&&(b.transparent=this.transparent),this.wireframe!==!1&&(b.wireframe=this.wireframe),b},clone:function(b){return void 0===b&&(b=new a.Material),b.name=this.name,b.side=this.side,b.opacity=this.opacity,b.transparent=this.transparent,b.blending=this.blending,b.blendSrc=this.blendSrc,b.blendDst=this.blendDst,b.blendEquation=this.blendEquation,b.depthTest=this.depthTest,b.depthWrite=this.depthWrite,b.polygonOffset=this.polygonOffset,b.polygonOffsetFactor=this.polygonOffsetFactor,b.polygonOffsetUnits=this.polygonOffsetUnits,b.alphaTest=this.alphaTest,b.overdraw=this.overdraw,b.visible=this.visible,b},dispose:function(){this.dispatchEvent({type:"dispose"})}},a.EventDispatcher.prototype.apply(a.Material.prototype),a.MaterialIdCount=0,a.LineBasicMaterial=function(b){a.Material.call(this),this.type="LineBasicMaterial",this.color=new a.Color(16777215),this.linewidth=1,this.linecap="round",this.linejoin="round",this.vertexColors=a.NoColors,this.fog=!0,this.setValues(b)},a.LineBasicMaterial.prototype=Object.create(a.Material.prototype),a.LineBasicMaterial.prototype.clone=function(){var b=new a.LineBasicMaterial;return a.Material.prototype.clone.call(this,b),b.color.copy(this.color),b.linewidth=this.linewidth,b.linecap=this.linecap,b.linejoin=this.linejoin,b.vertexColors=this.vertexColors,b.fog=this.fog,b},a.LineDashedMaterial=function(b){a.Material.call(this),this.type="LineDashedMaterial",this.color=new a.Color(16777215),this.linewidth=1,this.scale=1,this.dashSize=3,this.gapSize=1,this.vertexColors=!1,this.fog=!0,this.setValues(b)},a.LineDashedMaterial.prototype=Object.create(a.Material.prototype),a.LineDashedMaterial.prototype.clone=function(){var b=new a.LineDashedMaterial;return a.Material.prototype.clone.call(this,b),b.color.copy(this.color),b.linewidth=this.linewidth,b.scale=this.scale,b.dashSize=this.dashSize,b.gapSize=this.gapSize,b.vertexColors=this.vertexColors,b.fog=this.fog,b},a.MeshBasicMaterial=function(b){a.Material.call(this),this.type="MeshBasicMaterial",this.color=new a.Color(16777215),this.map=null,this.lightMap=null,this.specularMap=null,this.alphaMap=null,this.envMap=null,this.combine=a.MultiplyOperation,this.reflectivity=1,this.refractionRatio=.98,this.fog=!0,this.shading=a.SmoothShading,this.wireframe=!1,this.wireframeLinewidth=1,this.wireframeLinecap="round",this.wireframeLinejoin="round",this.vertexColors=a.NoColors,this.skinning=!1,this.morphTargets=!1,this.setValues(b)},a.MeshBasicMaterial.prototype=Object.create(a.Material.prototype),a.MeshBasicMaterial.prototype.clone=function(){var b=new a.MeshBasicMaterial;return a.Material.prototype.clone.call(this,b),b.color.copy(this.color),b.map=this.map,b.lightMap=this.lightMap,b.specularMap=this.specularMap,b.alphaMap=this.alphaMap,b.envMap=this.envMap,b.combine=this.combine,b.reflectivity=this.reflectivity,b.refractionRatio=this.refractionRatio,b.fog=this.fog,b.shading=this.shading,b.wireframe=this.wireframe,b.wireframeLinewidth=this.wireframeLinewidth,b.wireframeLinecap=this.wireframeLinecap,b.wireframeLinejoin=this.wireframeLinejoin,b.vertexColors=this.vertexColors,b.skinning=this.skinning,b.morphTargets=this.morphTargets,b},a.MeshLambertMaterial=function(b){a.Material.call(this),this.type="MeshLambertMaterial",this.color=new a.Color(16777215),this.ambient=new a.Color(16777215),this.emissive=new a.Color(0),this.wrapAround=!1,this.wrapRGB=new a.Vector3(1,1,1),this.map=null,this.lightMap=null,this.specularMap=null,this.alphaMap=null,this.envMap=null,this.combine=a.MultiplyOperation,this.reflectivity=1,this.refractionRatio=.98,this.fog=!0,this.shading=a.SmoothShading,this.wireframe=!1,this.wireframeLinewidth=1,this.wireframeLinecap="round",this.wireframeLinejoin="round",this.vertexColors=a.NoColors,this.skinning=!1,this.morphTargets=!1,this.morphNormals=!1,this.setValues(b)},a.MeshLambertMaterial.prototype=Object.create(a.Material.prototype),a.MeshLambertMaterial.prototype.clone=function(){var b=new a.MeshLambertMaterial;return a.Material.prototype.clone.call(this,b),b.color.copy(this.color),b.ambient.copy(this.ambient),b.emissive.copy(this.emissive),b.wrapAround=this.wrapAround,b.wrapRGB.copy(this.wrapRGB),b.map=this.map,b.lightMap=this.lightMap,b.specularMap=this.specularMap,b.alphaMap=this.alphaMap,b.envMap=this.envMap,b.combine=this.combine,b.reflectivity=this.reflectivity,b.refractionRatio=this.refractionRatio,b.fog=this.fog,b.shading=this.shading,b.wireframe=this.wireframe,b.wireframeLinewidth=this.wireframeLinewidth,b.wireframeLinecap=this.wireframeLinecap,b.wireframeLinejoin=this.wireframeLinejoin,b.vertexColors=this.vertexColors,b.skinning=this.skinning,b.morphTargets=this.morphTargets,b.morphNormals=this.morphNormals,b},a.MeshPhongMaterial=function(b){a.Material.call(this),this.type="MeshPhongMaterial",this.color=new a.Color(16777215),this.ambient=new a.Color(16777215),this.emissive=new a.Color(0),this.specular=new a.Color(1118481),this.shininess=30,this.metal=!1,this.wrapAround=!1,this.wrapRGB=new a.Vector3(1,1,1),this.map=null,this.lightMap=null,this.bumpMap=null,this.bumpScale=1,this.normalMap=null,this.normalScale=new a.Vector2(1,1),this.specularMap=null,this.alphaMap=null,this.envMap=null,this.combine=a.MultiplyOperation,this.reflectivity=1,this.refractionRatio=.98,this.fog=!0,this.shading=a.SmoothShading,this.wireframe=!1,this.wireframeLinewidth=1,this.wireframeLinecap="round",this.wireframeLinejoin="round",this.vertexColors=a.NoColors,this.skinning=!1,this.morphTargets=!1,this.morphNormals=!1,this.setValues(b)},a.MeshPhongMaterial.prototype=Object.create(a.Material.prototype),a.MeshPhongMaterial.prototype.clone=function(){var b=new a.MeshPhongMaterial;return a.Material.prototype.clone.call(this,b),b.color.copy(this.color),b.ambient.copy(this.ambient),b.emissive.copy(this.emissive),b.specular.copy(this.specular),b.shininess=this.shininess,b.metal=this.metal,b.wrapAround=this.wrapAround,b.wrapRGB.copy(this.wrapRGB),b.map=this.map,b.lightMap=this.lightMap,b.bumpMap=this.bumpMap,b.bumpScale=this.bumpScale,b.normalMap=this.normalMap,b.normalScale.copy(this.normalScale),b.specularMap=this.specularMap,b.alphaMap=this.alphaMap,b.envMap=this.envMap,b.combine=this.combine,b.reflectivity=this.reflectivity,b.refractionRatio=this.refractionRatio,b.fog=this.fog,b.shading=this.shading,b.wireframe=this.wireframe,b.wireframeLinewidth=this.wireframeLinewidth,b.wireframeLinecap=this.wireframeLinecap,b.wireframeLinejoin=this.wireframeLinejoin,b.vertexColors=this.vertexColors,b.skinning=this.skinning,b.morphTargets=this.morphTargets,b.morphNormals=this.morphNormals,b},a.MeshDepthMaterial=function(b){a.Material.call(this),this.type="MeshDepthMaterial",this.morphTargets=!1,this.wireframe=!1,this.wireframeLinewidth=1,this.setValues(b)},a.MeshDepthMaterial.prototype=Object.create(a.Material.prototype),a.MeshDepthMaterial.prototype.clone=function(){var b=new a.MeshDepthMaterial;return a.Material.prototype.clone.call(this,b),b.wireframe=this.wireframe,b.wireframeLinewidth=this.wireframeLinewidth,b},a.MeshNormalMaterial=function(b){a.Material.call(this,b),this.type="MeshNormalMaterial",this.shading=a.FlatShading,this.wireframe=!1,this.wireframeLinewidth=1,this.morphTargets=!1,this.setValues(b)},a.MeshNormalMaterial.prototype=Object.create(a.Material.prototype),a.MeshNormalMaterial.prototype.clone=function(){var b=new a.MeshNormalMaterial;return a.Material.prototype.clone.call(this,b),b.shading=this.shading,b.wireframe=this.wireframe,b.wireframeLinewidth=this.wireframeLinewidth,b},a.MeshFaceMaterial=function(b){this.uuid=a.Math.generateUUID(),this.type="MeshFaceMaterial",this.materials=b instanceof Array?b:[]},a.MeshFaceMaterial.prototype={constructor:a.MeshFaceMaterial,toJSON:function(){for(var a={metadata:{version:4.2,type:"material",generator:"MaterialExporter"},uuid:this.uuid,type:this.type,materials:[]},b=0,c=this.materials.length;c>b;b++)a.materials.push(this.materials[b].toJSON());return a},clone:function(){for(var b=new a.MeshFaceMaterial,c=0;c<this.materials.length;c++)b.materials.push(this.materials[c].clone());return b}},a.PointCloudMaterial=function(b){a.Material.call(this),this.type="PointCloudMaterial",this.color=new a.Color(16777215),this.map=null,this.size=1,this.sizeAttenuation=!0,this.vertexColors=a.NoColors,this.fog=!0,this.setValues(b)},a.PointCloudMaterial.prototype=Object.create(a.Material.prototype),a.PointCloudMaterial.prototype.clone=function(){var b=new a.PointCloudMaterial;return a.Material.prototype.clone.call(this,b),b.color.copy(this.color),b.map=this.map,b.size=this.size,b.sizeAttenuation=this.sizeAttenuation,b.vertexColors=this.vertexColors,b.fog=this.fog,b},a.ParticleBasicMaterial=function(b){return console.warn("THREE.ParticleBasicMaterial has been renamed to THREE.PointCloudMaterial."),new a.PointCloudMaterial(b)},a.ParticleSystemMaterial=function(b){return console.warn("THREE.ParticleSystemMaterial has been renamed to THREE.PointCloudMaterial."),new a.PointCloudMaterial(b)},a.ShaderMaterial=function(b){a.Material.call(this),this.type="ShaderMaterial",this.defines={},this.uniforms={},this.attributes=null,this.vertexShader="void main() {\n	gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n}",this.fragmentShader="void main() {\n	gl_FragColor = vec4( 1.0, 0.0, 0.0, 1.0 );\n}",this.shading=a.SmoothShading,this.linewidth=1,this.wireframe=!1,this.wireframeLinewidth=1,this.fog=!1,this.lights=!1,this.vertexColors=a.NoColors,this.skinning=!1,this.morphTargets=!1,this.morphNormals=!1,this.defaultAttributeValues={color:[1,1,1],uv:[0,0],uv2:[0,0]},this.index0AttributeName=void 0,this.setValues(b)},a.ShaderMaterial.prototype=Object.create(a.Material.prototype),a.ShaderMaterial.prototype.clone=function(){var b=new a.ShaderMaterial;return a.Material.prototype.clone.call(this,b),b.fragmentShader=this.fragmentShader,b.vertexShader=this.vertexShader,b.uniforms=a.UniformsUtils.clone(this.uniforms),b.attributes=this.attributes,b.defines=this.defines,b.shading=this.shading,b.wireframe=this.wireframe,b.wireframeLinewidth=this.wireframeLinewidth,b.fog=this.fog,b.lights=this.lights,b.vertexColors=this.vertexColors,b.skinning=this.skinning,b.morphTargets=this.morphTargets,b.morphNormals=this.morphNormals,b},a.RawShaderMaterial=function(b){a.ShaderMaterial.call(this,b),this.type="RawShaderMaterial"},a.RawShaderMaterial.prototype=Object.create(a.ShaderMaterial.prototype),a.RawShaderMaterial.prototype.clone=function(){var b=new a.RawShaderMaterial;return a.ShaderMaterial.prototype.clone.call(this,b),b},a.SpriteMaterial=function(b){a.Material.call(this),this.type="SpriteMaterial",this.color=new a.Color(16777215),this.map=null,this.rotation=0,this.fog=!1,this.setValues(b)},a.SpriteMaterial.prototype=Object.create(a.Material.prototype),a.SpriteMaterial.prototype.clone=function(){var b=new a.SpriteMaterial;return a.Material.prototype.clone.call(this,b),b.color.copy(this.color),b.map=this.map,b.rotation=this.rotation,b.fog=this.fog,b},a.Texture=function(b,c,d,e,f,g,h,i,j){Object.defineProperty(this,"id",{value:a.TextureIdCount++}),this.uuid=a.Math.generateUUID(),this.name="",this.image=void 0!==b?b:a.Texture.DEFAULT_IMAGE,this.mipmaps=[],this.mapping=void 0!==c?c:a.Texture.DEFAULT_MAPPING,this.wrapS=void 0!==d?d:a.ClampToEdgeWrapping,this.wrapT=void 0!==e?e:a.ClampToEdgeWrapping,this.magFilter=void 0!==f?f:a.LinearFilter,this.minFilter=void 0!==g?g:a.LinearMipMapLinearFilter,this.anisotropy=void 0!==j?j:1,this.format=void 0!==h?h:a.RGBAFormat,this.type=void 0!==i?i:a.UnsignedByteType,this.offset=new a.Vector2(0,0),this.repeat=new a.Vector2(1,1),this.generateMipmaps=!0,this.premultiplyAlpha=!1,this.flipY=!0,this.unpackAlignment=4,this._needsUpdate=!1,this.onUpdate=null},a.Texture.DEFAULT_IMAGE=void 0,a.Texture.DEFAULT_MAPPING=new a.UVMapping,a.Texture.prototype={constructor:a.Texture,get needsUpdate(){return this._needsUpdate},set needsUpdate(a){a===!0&&this.update(),this._needsUpdate=a},clone:function(b){return void 0===b&&(b=new a.Texture),b.image=this.image,b.mipmaps=this.mipmaps.slice(0),b.mapping=this.mapping,b.wrapS=this.wrapS,b.wrapT=this.wrapT,b.magFilter=this.magFilter,b.minFilter=this.minFilter,b.anisotropy=this.anisotropy,b.format=this.format,b.type=this.type,b.offset.copy(this.offset),b.repeat.copy(this.repeat),b.generateMipmaps=this.generateMipmaps,b.premultiplyAlpha=this.premultiplyAlpha,b.flipY=this.flipY,b.unpackAlignment=this.unpackAlignment,b},update:function(){this.dispatchEvent({type:"update"})},dispose:function(){this.dispatchEvent({type:"dispose"})}},a.EventDispatcher.prototype.apply(a.Texture.prototype),a.TextureIdCount=0,a.CubeTexture=function(b,c,d,e,f,g,h,i,j){a.Texture.call(this,b,c,d,e,f,g,h,i,j),this.images=b},a.CubeTexture.prototype=Object.create(a.Texture.prototype),a.CubeTexture.clone=function(b){return void 0===b&&(b=new a.CubeTexture),a.Texture.prototype.clone.call(this,b),b.images=this.images,b},a.CompressedTexture=function(b,c,d,e,f,g,h,i,j,k,l){a.Texture.call(this,null,g,h,i,j,k,e,f,l),this.image={width:c,height:d},this.mipmaps=b,this.flipY=!1,this.generateMipmaps=!1},a.CompressedTexture.prototype=Object.create(a.Texture.prototype),a.CompressedTexture.prototype.clone=function(){var b=new a.CompressedTexture;return a.Texture.prototype.clone.call(this,b),b},a.DataTexture=function(b,c,d,e,f,g,h,i,j,k,l){a.Texture.call(this,null,g,h,i,j,k,e,f,l),this.image={data:b,width:c,height:d}},a.DataTexture.prototype=Object.create(a.Texture.prototype),a.DataTexture.prototype.clone=function(){var b=new a.DataTexture;return a.Texture.prototype.clone.call(this,b),b},a.VideoTexture=function(b,c,d,e,f,g,h,i,j){a.Texture.call(this,b,c,d,e,f,g,h,i,j),this.generateMipmaps=!1;var k=this,l=function(){requestAnimationFrame(l),b.readyState===b.HAVE_ENOUGH_DATA&&(k.needsUpdate=!0)};l()},a.VideoTexture.prototype=Object.create(a.Texture.prototype),a.Group=function(){a.Object3D.call(this),this.type="Group"},a.Group.prototype=Object.create(a.Object3D.prototype),a.PointCloud=function(b,c){a.Object3D.call(this),this.type="PointCloud",this.geometry=void 0!==b?b:new a.Geometry,this.material=void 0!==c?c:new a.PointCloudMaterial({color:16777215*Math.random()}),this.sortParticles=!1},a.PointCloud.prototype=Object.create(a.Object3D.prototype),a.PointCloud.prototype.raycast=function(){var b=new a.Matrix4,c=new a.Ray;return function(d,e){var f=this,g=f.geometry,h=d.params.PointCloud.threshold;if(b.getInverse(this.matrixWorld),c.copy(d.ray).applyMatrix4(b),null===g.boundingBox||c.isIntersectionBox(g.boundingBox)!==!1){var i=h/((this.scale.x+this.scale.y+this.scale.z)/3),j=new a.Vector3,k=function(a,b){var g=c.distanceToPoint(a);if(i>g){var h=c.closestPointToPoint(a);h.applyMatrix4(f.matrixWorld);var j=d.ray.origin.distanceTo(h);e.push({distance:j,distanceToRay:g,point:h.clone(),index:b,face:null,object:f})}};if(g instanceof a.BufferGeometry){var l=g.attributes,m=l.position.array;if(void 0!==l.index){var n=l.index.array,o=g.offsets;if(0===o.length){var p={start:0,count:n.length,index:0};o=[p]}for(var q=0,r=o.length;r>q;++q)for(var s=o[q].start,t=o[q].count,u=o[q].index,v=s,w=s+t;w>v;v++){var x=u+n[v];j.fromArray(m,3*x),k(j,x)}}else for(var y=m.length/3,v=0;y>v;v++)j.set(m[3*v],m[3*v+1],m[3*v+2]),k(j,v)}else for(var z=this.geometry.vertices,v=0;v<z.length;v++)k(z[v],v)}}}(),a.PointCloud.prototype.clone=function(b){return void 0===b&&(b=new a.PointCloud(this.geometry,this.material)),b.sortParticles=this.sortParticles,a.Object3D.prototype.clone.call(this,b),b},a.ParticleSystem=function(b,c){return console.warn("THREE.ParticleSystem has been renamed to THREE.PointCloud."),new a.PointCloud(b,c)},a.Line=function(b,c,d){a.Object3D.call(this),this.type="Line",this.geometry=void 0!==b?b:new a.Geometry,this.material=void 0!==c?c:new a.LineBasicMaterial({color:16777215*Math.random()}),this.mode=void 0!==d?d:a.LineStrip},a.LineStrip=0,a.LinePieces=1,a.Line.prototype=Object.create(a.Object3D.prototype),a.Line.prototype.raycast=function(){var b=new a.Matrix4,c=new a.Ray,d=new a.Sphere;return function(e,f){var g=e.linePrecision,h=g*g,i=this.geometry;if(null===i.boundingSphere&&i.computeBoundingSphere(),d.copy(i.boundingSphere),d.applyMatrix4(this.matrixWorld),e.ray.isIntersectionSphere(d)!==!1&&(b.getInverse(this.matrixWorld),c.copy(e.ray).applyMatrix4(b),i instanceof a.Geometry))for(var j=i.vertices,k=j.length,l=new a.Vector3,m=new a.Vector3,n=this.mode===a.LineStrip?1:2,o=0;k-1>o;o+=n){var p=c.distanceSqToSegment(j[o],j[o+1],m,l);if(!(p>h)){var q=c.origin.distanceTo(m);q<e.near||q>e.far||f.push({distance:q,point:l.clone().applyMatrix4(this.matrixWorld),face:null,faceIndex:null,object:this})}}}}(),a.Line.prototype.clone=function(b){return void 0===b&&(b=new a.Line(this.geometry,this.material,this.mode)),a.Object3D.prototype.clone.call(this,b),b},a.Mesh=function(b,c){a.Object3D.call(this),this.type="Mesh",this.geometry=void 0!==b?b:new a.Geometry,this.material=void 0!==c?c:new a.MeshBasicMaterial({color:16777215*Math.random()}),this.updateMorphTargets()},a.Mesh.prototype=Object.create(a.Object3D.prototype),a.Mesh.prototype.updateMorphTargets=function(){if(void 0!==this.geometry.morphTargets&&this.geometry.morphTargets.length>0){this.morphTargetBase=-1,this.morphTargetForcedOrder=[],this.morphTargetInfluences=[],this.morphTargetDictionary={};for(var a=0,b=this.geometry.morphTargets.length;b>a;a++)this.morphTargetInfluences.push(0),this.morphTargetDictionary[this.geometry.morphTargets[a].name]=a}},a.Mesh.prototype.getMorphTargetIndexByName=function(a){return void 0!==this.morphTargetDictionary[a]?this.morphTargetDictionary[a]:(console.log("THREE.Mesh.getMorphTargetIndexByName: morph target "+a+" does not exist. Returning 0."),0)},a.Mesh.prototype.raycast=function(){var b=new a.Matrix4,c=new a.Ray,d=new a.Sphere,e=new a.Vector3,f=new a.Vector3,g=new a.Vector3;return function(h,i){var j=this.geometry;if(null===j.boundingSphere&&j.computeBoundingSphere(),d.copy(j.boundingSphere),d.applyMatrix4(this.matrixWorld),h.ray.isIntersectionSphere(d)!==!1&&(b.getInverse(this.matrixWorld),c.copy(h.ray).applyMatrix4(b),null===j.boundingBox||c.isIntersectionBox(j.boundingBox)!==!1))if(j instanceof a.BufferGeometry){var k=this.material;if(void 0===k)return;var l,m,n,o=j.attributes,p=h.precision;if(void 0!==o.index){var q=o.index.array,r=o.position.array,s=j.offsets;0===s.length&&(s=[{start:0,count:q.length,index:0}]);for(var t=0,u=s.length;u>t;++t)for(var v=s[t].start,w=s[t].count,x=s[t].index,y=v,z=v+w;z>y;y+=3){if(l=x+q[y],m=x+q[y+1],n=x+q[y+2],e.fromArray(r,3*l),f.fromArray(r,3*m),g.fromArray(r,3*n),k.side===a.BackSide)var A=c.intersectTriangle(g,f,e,!0);else var A=c.intersectTriangle(e,f,g,k.side!==a.DoubleSide);if(null!==A){A.applyMatrix4(this.matrixWorld);var B=h.ray.origin.distanceTo(A);p>B||B<h.near||B>h.far||i.push({distance:B,point:A,face:new a.Face3(l,m,n,a.Triangle.normal(e,f,g)),faceIndex:null,object:this})}}}else for(var r=o.position.array,y=0,C=0,z=r.length;z>y;y+=3,C+=9){if(l=y,m=y+1,n=y+2,e.fromArray(r,C),f.fromArray(r,C+3),g.fromArray(r,C+6),k.side===a.BackSide)var A=c.intersectTriangle(g,f,e,!0);else var A=c.intersectTriangle(e,f,g,k.side!==a.DoubleSide);if(null!==A){A.applyMatrix4(this.matrixWorld);var B=h.ray.origin.distanceTo(A);p>B||B<h.near||B>h.far||i.push({distance:B,point:A,face:new a.Face3(l,m,n,a.Triangle.normal(e,f,g)),faceIndex:null,object:this})}}}else if(j instanceof a.Geometry)for(var l,m,n,D=this.material instanceof a.MeshFaceMaterial,E=D===!0?this.material.materials:null,p=h.precision,F=j.vertices,G=0,H=j.faces.length;H>G;G++){var I=j.faces[G],k=D===!0?E[I.materialIndex]:this.material;if(void 0!==k){if(l=F[I.a],m=F[I.b],n=F[I.c],k.morphTargets===!0){var J=j.morphTargets,K=this.morphTargetInfluences;e.set(0,0,0),f.set(0,0,0),g.set(0,0,0);for(var L=0,M=J.length;M>L;L++){var N=K[L];if(0!==N){var O=J[L].vertices;e.x+=(O[I.a].x-l.x)*N,e.y+=(O[I.a].y-l.y)*N,e.z+=(O[I.a].z-l.z)*N,f.x+=(O[I.b].x-m.x)*N,f.y+=(O[I.b].y-m.y)*N,f.z+=(O[I.b].z-m.z)*N,g.x+=(O[I.c].x-n.x)*N,g.y+=(O[I.c].y-n.y)*N,g.z+=(O[I.c].z-n.z)*N}}e.add(l),f.add(m),g.add(n),l=e,m=f,n=g}if(k.side===a.BackSide)var A=c.intersectTriangle(n,m,l,!0);else var A=c.intersectTriangle(l,m,n,k.side!==a.DoubleSide);if(null!==A){A.applyMatrix4(this.matrixWorld);var B=h.ray.origin.distanceTo(A);p>B||B<h.near||B>h.far||i.push({distance:B,point:A,face:I,faceIndex:G,object:this})}}}}}(),a.Mesh.prototype.clone=function(b,c){return void 0===b&&(b=new a.Mesh(this.geometry,this.material)),a.Object3D.prototype.clone.call(this,b,c),b},a.Bone=function(b){a.Object3D.call(this),this.skin=b},a.Bone.prototype=Object.create(a.Object3D.prototype),a.Skeleton=function(b,c,d){if(this.useVertexTexture=void 0!==d?d:!0,this.identityMatrix=new a.Matrix4,b=b||[],this.bones=b.slice(0),this.useVertexTexture){var e;e=this.bones.length>256?64:this.bones.length>64?32:this.bones.length>16?16:8,this.boneTextureWidth=e,this.boneTextureHeight=e,this.boneMatrices=new Float32Array(this.boneTextureWidth*this.boneTextureHeight*4),this.boneTexture=new a.DataTexture(this.boneMatrices,this.boneTextureWidth,this.boneTextureHeight,a.RGBAFormat,a.FloatType),this.boneTexture.minFilter=a.NearestFilter,this.boneTexture.magFilter=a.NearestFilter,this.boneTexture.generateMipmaps=!1,this.boneTexture.flipY=!1}else this.boneMatrices=new Float32Array(16*this.bones.length);if(void 0===c)this.calculateInverses();else if(this.bones.length===c.length)this.boneInverses=c.slice(0);else{console.warn("THREE.Skeleton bonInverses is the wrong length."),this.boneInverses=[];for(var f=0,g=this.bones.length;g>f;f++)this.boneInverses.push(new a.Matrix4)}},a.Skeleton.prototype.calculateInverses=function(){this.boneInverses=[];for(var b=0,c=this.bones.length;c>b;b++){var d=new a.Matrix4;this.bones[b]&&d.getInverse(this.bones[b].matrixWorld),this.boneInverses.push(d)}},a.Skeleton.prototype.pose=function(){for(var a,b=0,c=this.bones.length;c>b;b++)a=this.bones[b],a&&a.matrixWorld.getInverse(this.boneInverses[b]);for(var b=0,c=this.bones.length;c>b;b++)a=this.bones[b],a&&(a.parent?(a.matrix.getInverse(a.parent.matrixWorld),a.matrix.multiply(a.matrixWorld)):a.matrix.copy(a.matrixWorld),a.matrix.decompose(a.position,a.quaternion,a.scale))},a.Skeleton.prototype.update=function(){var b=new a.Matrix4;return function(){for(var a=0,c=this.bones.length;c>a;a++){var d=this.bones[a]?this.bones[a].matrixWorld:this.identityMatrix;b.multiplyMatrices(d,this.boneInverses[a]),b.flattenToArrayOffset(this.boneMatrices,16*a)}this.useVertexTexture&&(this.boneTexture.needsUpdate=!0)}}(),a.SkinnedMesh=function(b,c,d){a.Mesh.call(this,b,c),this.type="SkinnedMesh",this.bindMode="attached",this.bindMatrix=new a.Matrix4,this.bindMatrixInverse=new a.Matrix4;var e=[];if(this.geometry&&void 0!==this.geometry.bones){for(var f,g,h,i,j,k=0,l=this.geometry.bones.length;l>k;++k)g=this.geometry.bones[k],h=g.pos,i=g.rotq,j=g.scl,f=new a.Bone(this),e.push(f),f.name=g.name,f.position.set(h[0],h[1],h[2]),f.quaternion.set(i[0],i[1],i[2],i[3]),void 0!==j?f.scale.set(j[0],j[1],j[2]):f.scale.set(1,1,1);for(var k=0,l=this.geometry.bones.length;l>k;++k)g=this.geometry.bones[k],-1!==g.parent?e[g.parent].add(e[k]):this.add(e[k])}this.normalizeSkinWeights(),this.updateMatrixWorld(!0),this.bind(new a.Skeleton(e,void 0,d))},a.SkinnedMesh.prototype=Object.create(a.Mesh.prototype),a.SkinnedMesh.prototype.bind=function(a,b){this.skeleton=a,void 0===b&&(this.updateMatrixWorld(!0),b=this.matrixWorld),this.bindMatrix.copy(b),this.bindMatrixInverse.getInverse(b)},a.SkinnedMesh.prototype.pose=function(){this.skeleton.pose()},a.SkinnedMesh.prototype.normalizeSkinWeights=function(){if(this.geometry instanceof a.Geometry)for(var b=0;b<this.geometry.skinIndices.length;b++){var c=this.geometry.skinWeights[b],d=1/c.lengthManhattan();d!==1/0?c.multiplyScalar(d):c.set(1)}},a.SkinnedMesh.prototype.updateMatrixWorld=function(b){a.Mesh.prototype.updateMatrixWorld.call(this,!0),"attached"===this.bindMode?this.bindMatrixInverse.getInverse(this.matrixWorld):"detached"===this.bindMode?this.bindMatrixInverse.getInverse(this.bindMatrix):console.warn("THREE.SkinnedMesh unreckognized bindMode: "+this.bindMode)},a.SkinnedMesh.prototype.clone=function(b){return void 0===b&&(b=new a.SkinnedMesh(this.geometry,this.material,this.useVertexTexture)),a.Mesh.prototype.clone.call(this,b),b},a.MorphAnimMesh=function(b,c){a.Mesh.call(this,b,c),this.type="MorphAnimMesh",this.duration=1e3,this.mirroredLoop=!1,this.time=0,this.lastKeyframe=0,this.currentKeyframe=0,this.direction=1,this.directionBackwards=!1,this.setFrameRange(0,this.geometry.morphTargets.length-1)},a.MorphAnimMesh.prototype=Object.create(a.Mesh.prototype),a.MorphAnimMesh.prototype.setFrameRange=function(a,b){this.startKeyframe=a,this.endKeyframe=b,this.length=this.endKeyframe-this.startKeyframe+1},a.MorphAnimMesh.prototype.setDirectionForward=function(){this.direction=1,this.directionBackwards=!1},a.MorphAnimMesh.prototype.setDirectionBackward=function(){this.direction=-1,this.directionBackwards=!0},a.MorphAnimMesh.prototype.parseAnimations=function(){var a=this.geometry;a.animations||(a.animations={});for(var b,c=a.animations,d=/([a-z]+)_?(\d+)/,e=0,f=a.morphTargets.length;f>e;e++){var g=a.morphTargets[e],h=g.name.match(d);if(h&&h.length>1){var i=h[1];h[2];c[i]||(c[i]={start:1/0,end:-(1/0)});var j=c[i];e<j.start&&(j.start=e),e>j.end&&(j.end=e),b||(b=i)}}a.firstAnimation=b},a.MorphAnimMesh.prototype.setAnimationLabel=function(a,b,c){this.geometry.animations||(this.geometry.animations={}),this.geometry.animations[a]={start:b,end:c}},a.MorphAnimMesh.prototype.playAnimation=function(a,b){var c=this.geometry.animations[a];c?(this.setFrameRange(c.start,c.end),this.duration=1e3*((c.end-c.start)/b),this.time=0):console.warn("animation["+a+"] undefined")},a.MorphAnimMesh.prototype.updateAnimation=function(b){var c=this.duration/this.length;this.time+=this.direction*b,this.mirroredLoop?(this.time>this.duration||this.time<0)&&(this.direction*=-1,this.time>this.duration&&(this.time=this.duration,this.directionBackwards=!0),this.time<0&&(this.time=0,this.directionBackwards=!1)):(this.time=this.time%this.duration,this.time<0&&(this.time+=this.duration));var d=this.startKeyframe+a.Math.clamp(Math.floor(this.time/c),0,this.length-1);d!==this.currentKeyframe&&(this.morphTargetInfluences[this.lastKeyframe]=0,this.morphTargetInfluences[this.currentKeyframe]=1,this.morphTargetInfluences[d]=0,this.lastKeyframe=this.currentKeyframe,this.currentKeyframe=d);var e=this.time%c/c;this.directionBackwards&&(e=1-e),this.morphTargetInfluences[this.currentKeyframe]=e,this.morphTargetInfluences[this.lastKeyframe]=1-e},a.MorphAnimMesh.prototype.interpolateTargets=function(a,b,c){for(var d=this.morphTargetInfluences,e=0,f=d.length;f>e;e++)d[e]=0;a>-1&&(d[a]=1-c),b>-1&&(d[b]=c)},a.MorphAnimMesh.prototype.clone=function(b){return void 0===b&&(b=new a.MorphAnimMesh(this.geometry,this.material)),b.duration=this.duration,b.mirroredLoop=this.mirroredLoop,b.time=this.time,b.lastKeyframe=this.lastKeyframe,b.currentKeyframe=this.currentKeyframe,b.direction=this.direction,b.directionBackwards=this.directionBackwards,a.Mesh.prototype.clone.call(this,b),b},a.LOD=function(){a.Object3D.call(this),this.objects=[]},a.LOD.prototype=Object.create(a.Object3D.prototype),a.LOD.prototype.addLevel=function(a,b){void 0===b&&(b=0),b=Math.abs(b);for(var c=0;c<this.objects.length&&!(b<this.objects[c].distance);c++);this.objects.splice(c,0,{distance:b,object:a}),this.add(a)},a.LOD.prototype.getObjectForDistance=function(a){for(var b=1,c=this.objects.length;c>b&&!(a<this.objects[b].distance);b++);return this.objects[b-1].object},a.LOD.prototype.raycast=function(){var b=new a.Vector3;return function(a,c){b.setFromMatrixPosition(this.matrixWorld);var d=a.ray.origin.distanceTo(b);this.getObjectForDistance(d).raycast(a,c)}}(),a.LOD.prototype.update=function(){var b=new a.Vector3,c=new a.Vector3;return function(a){if(this.objects.length>1){b.setFromMatrixPosition(a.matrixWorld),c.setFromMatrixPosition(this.matrixWorld);var d=b.distanceTo(c);this.objects[0].object.visible=!0;for(var e=1,f=this.objects.length;f>e&&d>=this.objects[e].distance;e++)this.objects[e-1].object.visible=!1,this.objects[e].object.visible=!0;for(;f>e;e++)this.objects[e].object.visible=!1}}}(),a.LOD.prototype.clone=function(b){void 0===b&&(b=new a.LOD),a.Object3D.prototype.clone.call(this,b);for(var c=0,d=this.objects.length;d>c;c++){var e=this.objects[c].object.clone();e.visible=0===c,b.addLevel(e,this.objects[c].distance)}return b},a.Sprite=function(){var b=new Uint16Array([0,1,2,0,2,3]),c=new Float32Array([-.5,-.5,0,.5,-.5,0,.5,.5,0,-.5,.5,0]),d=new Float32Array([0,0,1,0,1,1,0,1]),e=new a.BufferGeometry;return e.addAttribute("index",new a.BufferAttribute(b,1)),e.addAttribute("position",new a.BufferAttribute(c,3)),e.addAttribute("uv",new a.BufferAttribute(d,2)),function(b){a.Object3D.call(this),this.type="Sprite",this.geometry=e,this.material=void 0!==b?b:new a.SpriteMaterial}}(),a.Sprite.prototype=Object.create(a.Object3D.prototype),a.Sprite.prototype.raycast=function(){var b=new a.Vector3;return function(a,c){b.setFromMatrixPosition(this.matrixWorld);var d=a.ray.distanceToPoint(b);d>this.scale.x||c.push({distance:d,point:this.position,face:null,object:this})}}(),a.Sprite.prototype.clone=function(b){return void 0===b&&(b=new a.Sprite(this.material)),a.Object3D.prototype.clone.call(this,b),b},a.Particle=a.Sprite,a.LensFlare=function(b,c,d,e,f){a.Object3D.call(this),this.lensFlares=[],this.positionScreen=new a.Vector3,this.customUpdateCallback=void 0,void 0!==b&&this.add(b,c,d,e,f)},a.LensFlare.prototype=Object.create(a.Object3D.prototype),
a.LensFlare.prototype.add=function(b,c,d,e,f,g){void 0===c&&(c=-1),void 0===d&&(d=0),void 0===g&&(g=1),void 0===f&&(f=new a.Color(16777215)),void 0===e&&(e=a.NormalBlending),d=Math.min(d,Math.max(0,d)),this.lensFlares.push({texture:b,size:c,distance:d,x:0,y:0,z:0,scale:1,rotation:1,opacity:g,color:f,blending:e})},a.LensFlare.prototype.updateLensFlares=function(){var a,b,c=this.lensFlares.length,d=2*-this.positionScreen.x,e=2*-this.positionScreen.y;for(a=0;c>a;a++)b=this.lensFlares[a],b.x=this.positionScreen.x+d*b.distance,b.y=this.positionScreen.y+e*b.distance,b.wantedRotation=b.x*Math.PI*.25,b.rotation+=.25*(b.wantedRotation-b.rotation)},a.Scene=function(){a.Object3D.call(this),this.type="Scene",this.fog=null,this.overrideMaterial=null,this.autoUpdate=!0},a.Scene.prototype=Object.create(a.Object3D.prototype),a.Scene.prototype.clone=function(b){return void 0===b&&(b=new a.Scene),a.Object3D.prototype.clone.call(this,b),null!==this.fog&&(b.fog=this.fog.clone()),null!==this.overrideMaterial&&(b.overrideMaterial=this.overrideMaterial.clone()),b.autoUpdate=this.autoUpdate,b.matrixAutoUpdate=this.matrixAutoUpdate,b},a.Fog=function(b,c,d){this.name="",this.color=new a.Color(b),this.near=void 0!==c?c:1,this.far=void 0!==d?d:1e3},a.Fog.prototype.clone=function(){return new a.Fog(this.color.getHex(),this.near,this.far)},a.FogExp2=function(b,c){this.name="",this.color=new a.Color(b),this.density=void 0!==c?c:25e-5},a.FogExp2.prototype.clone=function(){return new a.FogExp2(this.color.getHex(),this.density)},a.ShaderChunk={},a.ShaderChunk.alphatest_fragment="#ifdef ALPHATEST\n\n  if ( gl_FragColor.a < ALPHATEST ) discard;\n\n#endif\n",a.ShaderChunk.lights_lambert_vertex="vLightFront = vec3( 0.0 );\n\n#ifdef DOUBLE_SIDED\n\n  vLightBack = vec3( 0.0 );\n\n#endif\n\ntransformedNormal = normalize( transformedNormal );\n\n#if MAX_DIR_LIGHTS > 0\n\nfor( int i = 0; i < MAX_DIR_LIGHTS; i ++ ) {\n\n  vec4 lDirection = viewMatrix * vec4( directionalLightDirection[ i ], 0.0 );\n vec3 dirVector = normalize( lDirection.xyz );\n\n float dotProduct = dot( transformedNormal, dirVector );\n vec3 directionalLightWeighting = vec3( max( dotProduct, 0.0 ) );\n\n  #ifdef DOUBLE_SIDED\n\n   vec3 directionalLightWeightingBack = vec3( max( -dotProduct, 0.0 ) );\n\n   #ifdef WRAP_AROUND\n\n      vec3 directionalLightWeightingHalfBack = vec3( max( -0.5 * dotProduct + 0.5, 0.0 ) );\n\n   #endif\n\n  #endif\n\n  #ifdef WRAP_AROUND\n\n    vec3 directionalLightWeightingHalf = vec3( max( 0.5 * dotProduct + 0.5, 0.0 ) );\n    directionalLightWeighting = mix( directionalLightWeighting, directionalLightWeightingHalf, wrapRGB );\n\n   #ifdef DOUBLE_SIDED\n\n     directionalLightWeightingBack = mix( directionalLightWeightingBack, directionalLightWeightingHalfBack, wrapRGB );\n\n   #endif\n\n  #endif\n\n  vLightFront += directionalLightColor[ i ] * directionalLightWeighting;\n\n  #ifdef DOUBLE_SIDED\n\n   vLightBack += directionalLightColor[ i ] * directionalLightWeightingBack;\n\n #endif\n\n}\n\n#endif\n\n#if MAX_POINT_LIGHTS > 0\n\n for( int i = 0; i < MAX_POINT_LIGHTS; i ++ ) {\n\n    vec4 lPosition = viewMatrix * vec4( pointLightPosition[ i ], 1.0 );\n   vec3 lVector = lPosition.xyz - mvPosition.xyz;\n\n    float lDistance = 1.0;\n    if ( pointLightDistance[ i ] > 0.0 )\n      lDistance = 1.0 - min( ( length( lVector ) / pointLightDistance[ i ] ), 1.0 );\n\n    lVector = normalize( lVector );\n   float dotProduct = dot( transformedNormal, lVector );\n\n   vec3 pointLightWeighting = vec3( max( dotProduct, 0.0 ) );\n\n    #ifdef DOUBLE_SIDED\n\n     vec3 pointLightWeightingBack = vec3( max( -dotProduct, 0.0 ) );\n\n     #ifdef WRAP_AROUND\n\n        vec3 pointLightWeightingHalfBack = vec3( max( -0.5 * dotProduct + 0.5, 0.0 ) );\n\n     #endif\n\n    #endif\n\n    #ifdef WRAP_AROUND\n\n      vec3 pointLightWeightingHalf = vec3( max( 0.5 * dotProduct + 0.5, 0.0 ) );\n      pointLightWeighting = mix( pointLightWeighting, pointLightWeightingHalf, wrapRGB );\n\n     #ifdef DOUBLE_SIDED\n\n       pointLightWeightingBack = mix( pointLightWeightingBack, pointLightWeightingHalfBack, wrapRGB );\n\n     #endif\n\n    #endif\n\n    vLightFront += pointLightColor[ i ] * pointLightWeighting * lDistance;\n\n    #ifdef DOUBLE_SIDED\n\n     vLightBack += pointLightColor[ i ] * pointLightWeightingBack * lDistance;\n\n   #endif\n\n  }\n\n#endif\n\n#if MAX_SPOT_LIGHTS > 0\n\n  for( int i = 0; i < MAX_SPOT_LIGHTS; i ++ ) {\n\n   vec4 lPosition = viewMatrix * vec4( spotLightPosition[ i ], 1.0 );\n    vec3 lVector = lPosition.xyz - mvPosition.xyz;\n\n    float spotEffect = dot( spotLightDirection[ i ], normalize( spotLightPosition[ i ] - worldPosition.xyz ) );\n\n   if ( spotEffect > spotLightAngleCos[ i ] ) {\n\n      spotEffect = max( pow( max( spotEffect, 0.0 ), spotLightExponent[ i ] ), 0.0 );\n\n     float lDistance = 1.0;\n      if ( spotLightDistance[ i ] > 0.0 )\n       lDistance = 1.0 - min( ( length( lVector ) / spotLightDistance[ i ] ), 1.0 );\n\n     lVector = normalize( lVector );\n\n     float dotProduct = dot( transformedNormal, lVector );\n     vec3 spotLightWeighting = vec3( max( dotProduct, 0.0 ) );\n\n     #ifdef DOUBLE_SIDED\n\n       vec3 spotLightWeightingBack = vec3( max( -dotProduct, 0.0 ) );\n\n        #ifdef WRAP_AROUND\n\n          vec3 spotLightWeightingHalfBack = vec3( max( -0.5 * dotProduct + 0.5, 0.0 ) );\n\n        #endif\n\n      #endif\n\n      #ifdef WRAP_AROUND\n\n        vec3 spotLightWeightingHalf = vec3( max( 0.5 * dotProduct + 0.5, 0.0 ) );\n       spotLightWeighting = mix( spotLightWeighting, spotLightWeightingHalf, wrapRGB );\n\n        #ifdef DOUBLE_SIDED\n\n         spotLightWeightingBack = mix( spotLightWeightingBack, spotLightWeightingHalfBack, wrapRGB );\n\n        #endif\n\n      #endif\n\n      vLightFront += spotLightColor[ i ] * spotLightWeighting * lDistance * spotEffect;\n\n     #ifdef DOUBLE_SIDED\n\n       vLightBack += spotLightColor[ i ] * spotLightWeightingBack * lDistance * spotEffect;\n\n      #endif\n\n    }\n\n }\n\n#endif\n\n#if MAX_HEMI_LIGHTS > 0\n\n  for( int i = 0; i < MAX_HEMI_LIGHTS; i ++ ) {\n\n   vec4 lDirection = viewMatrix * vec4( hemisphereLightDirection[ i ], 0.0 );\n    vec3 lVector = normalize( lDirection.xyz );\n\n   float dotProduct = dot( transformedNormal, lVector );\n\n   float hemiDiffuseWeight = 0.5 * dotProduct + 0.5;\n   float hemiDiffuseWeightBack = -0.5 * dotProduct + 0.5;\n\n    vLightFront += mix( hemisphereLightGroundColor[ i ], hemisphereLightSkyColor[ i ], hemiDiffuseWeight );\n\n   #ifdef DOUBLE_SIDED\n\n     vLightBack += mix( hemisphereLightGroundColor[ i ], hemisphereLightSkyColor[ i ], hemiDiffuseWeightBack );\n\n    #endif\n\n  }\n\n#endif\n\nvLightFront = vLightFront * diffuse + ambient * ambientLightColor + emissive;\n\n#ifdef DOUBLE_SIDED\n\n vLightBack = vLightBack * diffuse + ambient * ambientLightColor + emissive;\n\n#endif",a.ShaderChunk.map_particle_pars_fragment="#ifdef USE_MAP\n\n  uniform sampler2D map;\n\n#endif",a.ShaderChunk.default_vertex="vec4 mvPosition;\n\n#ifdef USE_SKINNING\n\n mvPosition = modelViewMatrix * skinned;\n\n#endif\n\n#if !defined( USE_SKINNING ) && defined( USE_MORPHTARGETS )\n\n  mvPosition = modelViewMatrix * vec4( morphed, 1.0 );\n\n#endif\n\n#if !defined( USE_SKINNING ) && ! defined( USE_MORPHTARGETS )\n\n mvPosition = modelViewMatrix * vec4( position, 1.0 );\n\n#endif\n\ngl_Position = projectionMatrix * mvPosition;",a.ShaderChunk.map_pars_fragment="#if defined( USE_MAP ) || defined( USE_BUMPMAP ) || defined( USE_NORMALMAP ) || defined( USE_SPECULARMAP ) || defined( USE_ALPHAMAP )\n\n  varying vec2 vUv;\n\n#endif\n\n#ifdef USE_MAP\n\n uniform sampler2D map;\n\n#endif",a.ShaderChunk.skinnormal_vertex="#ifdef USE_SKINNING\n\n  mat4 skinMatrix = mat4( 0.0 );\n  skinMatrix += skinWeight.x * boneMatX;\n  skinMatrix += skinWeight.y * boneMatY;\n  skinMatrix += skinWeight.z * boneMatZ;\n  skinMatrix += skinWeight.w * boneMatW;\n  skinMatrix  = bindMatrixInverse * skinMatrix * bindMatrix;\n\n  #ifdef USE_MORPHNORMALS\n\n vec4 skinnedNormal = skinMatrix * vec4( morphedNormal, 0.0 );\n\n #else\n\n vec4 skinnedNormal = skinMatrix * vec4( normal, 0.0 );\n\n  #endif\n\n#endif\n",a.ShaderChunk.logdepthbuf_pars_vertex="#ifdef USE_LOGDEPTHBUF\n\n #ifdef USE_LOGDEPTHBUF_EXT\n\n    varying float vFragDepth;\n\n #endif\n\n  uniform float logDepthBufFC;\n\n#endif",a.ShaderChunk.lightmap_pars_vertex="#ifdef USE_LIGHTMAP\n\n varying vec2 vUv2;\n\n#endif",a.ShaderChunk.lights_phong_fragment="vec3 normal = normalize( vNormal );\nvec3 viewPosition = normalize( vViewPosition );\n\n#ifdef DOUBLE_SIDED\n\n  normal = normal * ( -1.0 + 2.0 * float( gl_FrontFacing ) );\n\n#endif\n\n#ifdef USE_NORMALMAP\n\n normal = perturbNormal2Arb( -vViewPosition, normal );\n\n#elif defined( USE_BUMPMAP )\n\n normal = perturbNormalArb( -vViewPosition, normal, dHdxy_fwd() );\n\n#endif\n\n#if MAX_POINT_LIGHTS > 0\n\n vec3 pointDiffuse = vec3( 0.0 );\n  vec3 pointSpecular = vec3( 0.0 );\n\n for ( int i = 0; i < MAX_POINT_LIGHTS; i ++ ) {\n\n   vec4 lPosition = viewMatrix * vec4( pointLightPosition[ i ], 1.0 );\n   vec3 lVector = lPosition.xyz + vViewPosition.xyz;\n\n   float lDistance = 1.0;\n    if ( pointLightDistance[ i ] > 0.0 )\n      lDistance = 1.0 - min( ( length( lVector ) / pointLightDistance[ i ] ), 1.0 );\n\n    lVector = normalize( lVector );\n\n       // diffuse\n\n    float dotProduct = dot( normal, lVector );\n\n    #ifdef WRAP_AROUND\n\n      float pointDiffuseWeightFull = max( dotProduct, 0.0 );\n      float pointDiffuseWeightHalf = max( 0.5 * dotProduct + 0.5, 0.0 );\n\n      vec3 pointDiffuseWeight = mix( vec3( pointDiffuseWeightFull ), vec3( pointDiffuseWeightHalf ), wrapRGB );\n\n   #else\n\n     float pointDiffuseWeight = max( dotProduct, 0.0 );\n\n    #endif\n\n    pointDiffuse += diffuse * pointLightColor[ i ] * pointDiffuseWeight * lDistance;\n\n        // specular\n\n   vec3 pointHalfVector = normalize( lVector + viewPosition );\n   float pointDotNormalHalf = max( dot( normal, pointHalfVector ), 0.0 );\n    float pointSpecularWeight = specularStrength * max( pow( pointDotNormalHalf, shininess ), 0.0 );\n\n    float specularNormalization = ( shininess + 2.0 ) / 8.0;\n\n    vec3 schlick = specular + vec3( 1.0 - specular ) * pow( max( 1.0 - dot( lVector, pointHalfVector ), 0.0 ), 5.0 );\n   pointSpecular += schlick * pointLightColor[ i ] * pointSpecularWeight * pointDiffuseWeight * lDistance * specularNormalization;\n\n }\n\n#endif\n\n#if MAX_SPOT_LIGHTS > 0\n\n  vec3 spotDiffuse = vec3( 0.0 );\n vec3 spotSpecular = vec3( 0.0 );\n\n  for ( int i = 0; i < MAX_SPOT_LIGHTS; i ++ ) {\n\n    vec4 lPosition = viewMatrix * vec4( spotLightPosition[ i ], 1.0 );\n    vec3 lVector = lPosition.xyz + vViewPosition.xyz;\n\n   float lDistance = 1.0;\n    if ( spotLightDistance[ i ] > 0.0 )\n     lDistance = 1.0 - min( ( length( lVector ) / spotLightDistance[ i ] ), 1.0 );\n\n   lVector = normalize( lVector );\n\n   float spotEffect = dot( spotLightDirection[ i ], normalize( spotLightPosition[ i ] - vWorldPosition ) );\n\n    if ( spotEffect > spotLightAngleCos[ i ] ) {\n\n      spotEffect = max( pow( max( spotEffect, 0.0 ), spotLightExponent[ i ] ), 0.0 );\n\n         // diffuse\n\n      float dotProduct = dot( normal, lVector );\n\n      #ifdef WRAP_AROUND\n\n        float spotDiffuseWeightFull = max( dotProduct, 0.0 );\n       float spotDiffuseWeightHalf = max( 0.5 * dotProduct + 0.5, 0.0 );\n\n       vec3 spotDiffuseWeight = mix( vec3( spotDiffuseWeightFull ), vec3( spotDiffuseWeightHalf ), wrapRGB );\n\n      #else\n\n       float spotDiffuseWeight = max( dotProduct, 0.0 );\n\n     #endif\n\n      spotDiffuse += diffuse * spotLightColor[ i ] * spotDiffuseWeight * lDistance * spotEffect;\n\n          // specular\n\n     vec3 spotHalfVector = normalize( lVector + viewPosition );\n      float spotDotNormalHalf = max( dot( normal, spotHalfVector ), 0.0 );\n      float spotSpecularWeight = specularStrength * max( pow( spotDotNormalHalf, shininess ), 0.0 );\n\n      float specularNormalization = ( shininess + 2.0 ) / 8.0;\n\n      vec3 schlick = specular + vec3( 1.0 - specular ) * pow( max( 1.0 - dot( lVector, spotHalfVector ), 0.0 ), 5.0 );\n      spotSpecular += schlick * spotLightColor[ i ] * spotSpecularWeight * spotDiffuseWeight * lDistance * specularNormalization * spotEffect;\n\n    }\n\n }\n\n#endif\n\n#if MAX_DIR_LIGHTS > 0\n\n vec3 dirDiffuse = vec3( 0.0 );\n  vec3 dirSpecular = vec3( 0.0 );\n\n for( int i = 0; i < MAX_DIR_LIGHTS; i ++ ) {\n\n    vec4 lDirection = viewMatrix * vec4( directionalLightDirection[ i ], 0.0 );\n   vec3 dirVector = normalize( lDirection.xyz );\n\n       // diffuse\n\n    float dotProduct = dot( normal, dirVector );\n\n    #ifdef WRAP_AROUND\n\n      float dirDiffuseWeightFull = max( dotProduct, 0.0 );\n      float dirDiffuseWeightHalf = max( 0.5 * dotProduct + 0.5, 0.0 );\n\n      vec3 dirDiffuseWeight = mix( vec3( dirDiffuseWeightFull ), vec3( dirDiffuseWeightHalf ), wrapRGB );\n\n   #else\n\n     float dirDiffuseWeight = max( dotProduct, 0.0 );\n\n    #endif\n\n    dirDiffuse += diffuse * directionalLightColor[ i ] * dirDiffuseWeight;\n\n    // specular\n\n   vec3 dirHalfVector = normalize( dirVector + viewPosition );\n   float dirDotNormalHalf = max( dot( normal, dirHalfVector ), 0.0 );\n    float dirSpecularWeight = specularStrength * max( pow( dirDotNormalHalf, shininess ), 0.0 );\n\n    /*\n    // fresnel term from skin shader\n    const float F0 = 0.128;\n\n   float base = 1.0 - dot( viewPosition, dirHalfVector );\n    float exponential = pow( base, 5.0 );\n\n   float fresnel = exponential + F0 * ( 1.0 - exponential );\n   */\n\n    /*\n    // fresnel term from fresnel shader\n   const float mFresnelBias = 0.08;\n    const float mFresnelScale = 0.3;\n    const float mFresnelPower = 5.0;\n\n    float fresnel = mFresnelBias + mFresnelScale * pow( 1.0 + dot( normalize( -viewPosition ), normal ), mFresnelPower );\n   */\n\n    float specularNormalization = ( shininess + 2.0 ) / 8.0;\n\n    //    dirSpecular += specular * directionalLightColor[ i ] * dirSpecularWeight * dirDiffuseWeight * specularNormalization * fresnel;\n\n    vec3 schlick = specular + vec3( 1.0 - specular ) * pow( max( 1.0 - dot( dirVector, dirHalfVector ), 0.0 ), 5.0 );\n   dirSpecular += schlick * directionalLightColor[ i ] * dirSpecularWeight * dirDiffuseWeight * specularNormalization;\n\n\n }\n\n#endif\n\n#if MAX_HEMI_LIGHTS > 0\n\n  vec3 hemiDiffuse = vec3( 0.0 );\n vec3 hemiSpecular = vec3( 0.0 );\n\n  for( int i = 0; i < MAX_HEMI_LIGHTS; i ++ ) {\n\n   vec4 lDirection = viewMatrix * vec4( hemisphereLightDirection[ i ], 0.0 );\n    vec3 lVector = normalize( lDirection.xyz );\n\n   // diffuse\n\n    float dotProduct = dot( normal, lVector );\n    float hemiDiffuseWeight = 0.5 * dotProduct + 0.5;\n\n   vec3 hemiColor = mix( hemisphereLightGroundColor[ i ], hemisphereLightSkyColor[ i ], hemiDiffuseWeight );\n\n   hemiDiffuse += diffuse * hemiColor;\n\n   // specular (sky light)\n\n   vec3 hemiHalfVectorSky = normalize( lVector + viewPosition );\n   float hemiDotNormalHalfSky = 0.5 * dot( normal, hemiHalfVectorSky ) + 0.5;\n    float hemiSpecularWeightSky = specularStrength * max( pow( max( hemiDotNormalHalfSky, 0.0 ), shininess ), 0.0 );\n\n    // specular (ground light)\n\n    vec3 lVectorGround = -lVector;\n\n    vec3 hemiHalfVectorGround = normalize( lVectorGround + viewPosition );\n    float hemiDotNormalHalfGround = 0.5 * dot( normal, hemiHalfVectorGround ) + 0.5;\n    float hemiSpecularWeightGround = specularStrength * max( pow( max( hemiDotNormalHalfGround, 0.0 ), shininess ), 0.0 );\n\n    float dotProductGround = dot( normal, lVectorGround );\n\n    float specularNormalization = ( shininess + 2.0 ) / 8.0;\n\n    vec3 schlickSky = specular + vec3( 1.0 - specular ) * pow( max( 1.0 - dot( lVector, hemiHalfVectorSky ), 0.0 ), 5.0 );\n    vec3 schlickGround = specular + vec3( 1.0 - specular ) * pow( max( 1.0 - dot( lVectorGround, hemiHalfVectorGround ), 0.0 ), 5.0 );\n    hemiSpecular += hemiColor * specularNormalization * ( schlickSky * hemiSpecularWeightSky * max( dotProduct, 0.0 ) + schlickGround * hemiSpecularWeightGround * max( dotProductGround, 0.0 ) );\n\n  }\n\n#endif\n\nvec3 totalDiffuse = vec3( 0.0 );\nvec3 totalSpecular = vec3( 0.0 );\n\n#if MAX_DIR_LIGHTS > 0\n\n  totalDiffuse += dirDiffuse;\n totalSpecular += dirSpecular;\n\n#endif\n\n#if MAX_HEMI_LIGHTS > 0\n\n  totalDiffuse += hemiDiffuse;\n  totalSpecular += hemiSpecular;\n\n#endif\n\n#if MAX_POINT_LIGHTS > 0\n\n  totalDiffuse += pointDiffuse;\n totalSpecular += pointSpecular;\n\n#endif\n\n#if MAX_SPOT_LIGHTS > 0\n\n  totalDiffuse += spotDiffuse;\n  totalSpecular += spotSpecular;\n\n#endif\n\n#ifdef METAL\n\n  gl_FragColor.xyz = gl_FragColor.xyz * ( emissive + totalDiffuse + ambientLightColor * ambient + totalSpecular );\n\n#else\n\n gl_FragColor.xyz = gl_FragColor.xyz * ( emissive + totalDiffuse + ambientLightColor * ambient ) + totalSpecular;\n\n#endif",a.ShaderChunk.fog_pars_fragment="#ifdef USE_FOG\n\n uniform vec3 fogColor;\n\n  #ifdef FOG_EXP2\n\n   uniform float fogDensity;\n\n #else\n\n   uniform float fogNear;\n    uniform float fogFar;\n #endif\n\n#endif",a.ShaderChunk.morphnormal_vertex="#ifdef USE_MORPHNORMALS\n\n vec3 morphedNormal = vec3( 0.0 );\n\n morphedNormal += ( morphNormal0 - normal ) * morphTargetInfluences[ 0 ];\n  morphedNormal += ( morphNormal1 - normal ) * morphTargetInfluences[ 1 ];\n  morphedNormal += ( morphNormal2 - normal ) * morphTargetInfluences[ 2 ];\n  morphedNormal += ( morphNormal3 - normal ) * morphTargetInfluences[ 3 ];\n\n  morphedNormal += normal;\n\n#endif",a.ShaderChunk.envmap_pars_fragment="#ifdef USE_ENVMAP\n\n uniform float reflectivity;\n uniform samplerCube envMap;\n uniform float flipEnvMap;\n uniform int combine;\n\n  #if defined( USE_BUMPMAP ) || defined( USE_NORMALMAP ) || defined( PHONG )\n\n    uniform bool useRefract;\n    uniform float refractionRatio;\n\n  #else\n\n   varying vec3 vReflect;\n\n  #endif\n\n#endif",a.ShaderChunk.logdepthbuf_fragment="#if defined(USE_LOGDEPTHBUF) && defined(USE_LOGDEPTHBUF_EXT)\n\n  gl_FragDepthEXT = log2(vFragDepth) * logDepthBufFC * 0.5;\n\n#endif",a.ShaderChunk.normalmap_pars_fragment="#ifdef USE_NORMALMAP\n\n uniform sampler2D normalMap;\n  uniform vec2 normalScale;\n\n     // Per-Pixel Tangent Space Normal Mapping\n     // http://hacksoflife.blogspot.ch/2009/11/per-pixel-tangent-space-normal-mapping.html\n\n vec3 perturbNormal2Arb( vec3 eye_pos, vec3 surf_norm ) {\n\n    vec3 q0 = dFdx( eye_pos.xyz );\n    vec3 q1 = dFdy( eye_pos.xyz );\n    vec2 st0 = dFdx( vUv.st );\n    vec2 st1 = dFdy( vUv.st );\n\n    vec3 S = normalize( q0 * st1.t - q1 * st0.t );\n    vec3 T = normalize( -q0 * st1.s + q1 * st0.s );\n   vec3 N = normalize( surf_norm );\n\n    vec3 mapN = texture2D( normalMap, vUv ).xyz * 2.0 - 1.0;\n    mapN.xy = normalScale * mapN.xy;\n    mat3 tsn = mat3( S, T, N );\n   return normalize( tsn * mapN );\n\n }\n\n#endif\n",a.ShaderChunk.lights_phong_pars_vertex="#if MAX_SPOT_LIGHTS > 0 || defined( USE_BUMPMAP ) || defined( USE_ENVMAP )\n\n  varying vec3 vWorldPosition;\n\n#endif\n",a.ShaderChunk.lightmap_pars_fragment="#ifdef USE_LIGHTMAP\n\n varying vec2 vUv2;\n  uniform sampler2D lightMap;\n\n#endif",a.ShaderChunk.shadowmap_vertex="#ifdef USE_SHADOWMAP\n\n  for( int i = 0; i < MAX_SHADOWS; i ++ ) {\n\n   vShadowCoord[ i ] = shadowMatrix[ i ] * worldPosition;\n\n  }\n\n#endif",a.ShaderChunk.lights_phong_vertex="#if MAX_SPOT_LIGHTS > 0 || defined( USE_BUMPMAP ) || defined( USE_ENVMAP )\n\n vWorldPosition = worldPosition.xyz;\n\n#endif",a.ShaderChunk.map_fragment="#ifdef USE_MAP\n\n  vec4 texelColor = texture2D( map, vUv );\n\n  #ifdef GAMMA_INPUT\n\n    texelColor.xyz *= texelColor.xyz;\n\n #endif\n\n  gl_FragColor = gl_FragColor * texelColor;\n\n#endif",a.ShaderChunk.lightmap_vertex="#ifdef USE_LIGHTMAP\n\n  vUv2 = uv2;\n\n#endif",a.ShaderChunk.map_particle_fragment="#ifdef USE_MAP\n\n gl_FragColor = gl_FragColor * texture2D( map, vec2( gl_PointCoord.x, 1.0 - gl_PointCoord.y ) );\n\n#endif",a.ShaderChunk.color_pars_fragment="#ifdef USE_COLOR\n\n varying vec3 vColor;\n\n#endif\n",a.ShaderChunk.color_vertex="#ifdef USE_COLOR\n\n  #ifdef GAMMA_INPUT\n\n    vColor = color * color;\n\n #else\n\n   vColor = color;\n\n #endif\n\n#endif",a.ShaderChunk.skinning_vertex="#ifdef USE_SKINNING\n\n  #ifdef USE_MORPHTARGETS\n\n vec4 skinVertex = bindMatrix * vec4( morphed, 1.0 );\n\n  #else\n\n vec4 skinVertex = bindMatrix * vec4( position, 1.0 );\n\n #endif\n\n  vec4 skinned = vec4( 0.0 );\n skinned += boneMatX * skinVertex * skinWeight.x;\n  skinned += boneMatY * skinVertex * skinWeight.y;\n  skinned += boneMatZ * skinVertex * skinWeight.z;\n  skinned += boneMatW * skinVertex * skinWeight.w;\n  skinned  = bindMatrixInverse * skinned;\n\n#endif\n",a.ShaderChunk.envmap_pars_vertex="#if defined( USE_ENVMAP ) && ! defined( USE_BUMPMAP ) && ! defined( USE_NORMALMAP ) && ! defined( PHONG )\n\n varying vec3 vReflect;\n\n  uniform float refractionRatio;\n  uniform bool useRefract;\n\n#endif\n",a.ShaderChunk.linear_to_gamma_fragment="#ifdef GAMMA_OUTPUT\n\n gl_FragColor.xyz = sqrt( gl_FragColor.xyz );\n\n#endif",a.ShaderChunk.color_pars_vertex="#ifdef USE_COLOR\n\n varying vec3 vColor;\n\n#endif",a.ShaderChunk.lights_lambert_pars_vertex="uniform vec3 ambient;\nuniform vec3 diffuse;\nuniform vec3 emissive;\n\nuniform vec3 ambientLightColor;\n\n#if MAX_DIR_LIGHTS > 0\n\n uniform vec3 directionalLightColor[ MAX_DIR_LIGHTS ];\n uniform vec3 directionalLightDirection[ MAX_DIR_LIGHTS ];\n\n#endif\n\n#if MAX_HEMI_LIGHTS > 0\n\n  uniform vec3 hemisphereLightSkyColor[ MAX_HEMI_LIGHTS ];\n  uniform vec3 hemisphereLightGroundColor[ MAX_HEMI_LIGHTS ];\n uniform vec3 hemisphereLightDirection[ MAX_HEMI_LIGHTS ];\n\n#endif\n\n#if MAX_POINT_LIGHTS > 0\n\n uniform vec3 pointLightColor[ MAX_POINT_LIGHTS ];\n uniform vec3 pointLightPosition[ MAX_POINT_LIGHTS ];\n  uniform float pointLightDistance[ MAX_POINT_LIGHTS ];\n\n#endif\n\n#if MAX_SPOT_LIGHTS > 0\n\n  uniform vec3 spotLightColor[ MAX_SPOT_LIGHTS ];\n uniform vec3 spotLightPosition[ MAX_SPOT_LIGHTS ];\n  uniform vec3 spotLightDirection[ MAX_SPOT_LIGHTS ];\n uniform float spotLightDistance[ MAX_SPOT_LIGHTS ];\n uniform float spotLightAngleCos[ MAX_SPOT_LIGHTS ];\n uniform float spotLightExponent[ MAX_SPOT_LIGHTS ];\n\n#endif\n\n#ifdef WRAP_AROUND\n\n uniform vec3 wrapRGB;\n\n#endif\n",a.ShaderChunk.map_pars_vertex="#if defined( USE_MAP ) || defined( USE_BUMPMAP ) || defined( USE_NORMALMAP ) || defined( USE_SPECULARMAP ) || defined( USE_ALPHAMAP )\n\n  varying vec2 vUv;\n uniform vec4 offsetRepeat;\n\n#endif\n",a.ShaderChunk.envmap_fragment="#ifdef USE_ENVMAP\n\n  vec3 reflectVec;\n\n  #if defined( USE_BUMPMAP ) || defined( USE_NORMALMAP ) || defined( PHONG )\n\n    vec3 cameraToVertex = normalize( vWorldPosition - cameraPosition );\n\n   // http://en.wikibooks.org/wiki/GLSL_Programming/Applying_Matrix_Transformations\n    // Transforming Normal Vectors with the Inverse Transformation\n\n    vec3 worldNormal = normalize( vec3( vec4( normal, 0.0 ) * viewMatrix ) );\n\n   if ( useRefract ) {\n\n     reflectVec = refract( cameraToVertex, worldNormal, refractionRatio );\n\n   } else { \n\n     reflectVec = reflect( cameraToVertex, worldNormal );\n\n    }\n\n #else\n\n   reflectVec = vReflect;\n\n  #endif\n\n  #ifdef DOUBLE_SIDED\n\n   float flipNormal = ( -1.0 + 2.0 * float( gl_FrontFacing ) );\n    vec4 cubeColor = textureCube( envMap, flipNormal * vec3( flipEnvMap * reflectVec.x, reflectVec.yz ) );\n\n  #else\n\n   vec4 cubeColor = textureCube( envMap, vec3( flipEnvMap * reflectVec.x, reflectVec.yz ) );\n\n #endif\n\n  #ifdef GAMMA_INPUT\n\n    cubeColor.xyz *= cubeColor.xyz;\n\n #endif\n\n  if ( combine == 1 ) {\n\n   gl_FragColor.xyz = mix( gl_FragColor.xyz, cubeColor.xyz, specularStrength * reflectivity );\n\n } else if ( combine == 2 ) {\n\n    gl_FragColor.xyz += cubeColor.xyz * specularStrength * reflectivity;\n\n  } else {\n\n    gl_FragColor.xyz = mix( gl_FragColor.xyz, gl_FragColor.xyz * cubeColor.xyz, specularStrength * reflectivity );\n\n  }\n\n#endif",a.ShaderChunk.specularmap_pars_fragment="#ifdef USE_SPECULARMAP\n\n uniform sampler2D specularMap;\n\n#endif",a.ShaderChunk.logdepthbuf_vertex="#ifdef USE_LOGDEPTHBUF\n\n  gl_Position.z = log2(max(1e-6, gl_Position.w + 1.0)) * logDepthBufFC;\n\n #ifdef USE_LOGDEPTHBUF_EXT\n\n    vFragDepth = 1.0 + gl_Position.w;\n\n#else\n\n    gl_Position.z = (gl_Position.z - 1.0) * gl_Position.w;\n\n  #endif\n\n#endif",a.ShaderChunk.morphtarget_pars_vertex="#ifdef USE_MORPHTARGETS\n\n  #ifndef USE_MORPHNORMALS\n\n  uniform float morphTargetInfluences[ 8 ];\n\n #else\n\n uniform float morphTargetInfluences[ 4 ];\n\n #endif\n\n#endif",a.ShaderChunk.specularmap_fragment="float specularStrength;\n\n#ifdef USE_SPECULARMAP\n\n vec4 texelSpecular = texture2D( specularMap, vUv );\n specularStrength = texelSpecular.r;\n\n#else\n\n  specularStrength = 1.0;\n\n#endif",a.ShaderChunk.fog_fragment="#ifdef USE_FOG\n\n  #ifdef USE_LOGDEPTHBUF_EXT\n\n    float depth = gl_FragDepthEXT / gl_FragCoord.w;\n\n #else\n\n   float depth = gl_FragCoord.z / gl_FragCoord.w;\n\n  #endif\n\n  #ifdef FOG_EXP2\n\n   const float LOG2 = 1.442695;\n    float fogFactor = exp2( - fogDensity * fogDensity * depth * depth * LOG2 );\n   fogFactor = 1.0 - clamp( fogFactor, 0.0, 1.0 );\n\n #else\n\n   float fogFactor = smoothstep( fogNear, fogFar, depth );\n\n #endif\n  \n  gl_FragColor = mix( gl_FragColor, vec4( fogColor, gl_FragColor.w ), fogFactor );\n\n#endif",a.ShaderChunk.bumpmap_pars_fragment="#ifdef USE_BUMPMAP\n\n uniform sampler2D bumpMap;\n  uniform float bumpScale;\n\n      // Derivative maps - bump mapping unparametrized surfaces by Morten Mikkelsen\n     //  http://mmikkelsen3d.blogspot.sk/2011/07/derivative-maps.html\n\n      // Evaluate the derivative of the height w.r.t. screen-space using forward differencing (listing 2)\n\n vec2 dHdxy_fwd() {\n\n    vec2 dSTdx = dFdx( vUv );\n   vec2 dSTdy = dFdy( vUv );\n\n   float Hll = bumpScale * texture2D( bumpMap, vUv ).x;\n    float dBx = bumpScale * texture2D( bumpMap, vUv + dSTdx ).x - Hll;\n    float dBy = bumpScale * texture2D( bumpMap, vUv + dSTdy ).x - Hll;\n\n    return vec2( dBx, dBy );\n\n  }\n\n vec3 perturbNormalArb( vec3 surf_pos, vec3 surf_norm, vec2 dHdxy ) {\n\n    vec3 vSigmaX = dFdx( surf_pos );\n    vec3 vSigmaY = dFdy( surf_pos );\n    vec3 vN = surf_norm;    // normalized\n\n   vec3 R1 = cross( vSigmaY, vN );\n   vec3 R2 = cross( vN, vSigmaX );\n\n   float fDet = dot( vSigmaX, R1 );\n\n    vec3 vGrad = sign( fDet ) * ( dHdxy.x * R1 + dHdxy.y * R2 );\n    return normalize( abs( fDet ) * surf_norm - vGrad );\n\n  }\n\n#endif",a.ShaderChunk.defaultnormal_vertex="vec3 objectNormal;\n\n#ifdef USE_SKINNING\n\n objectNormal = skinnedNormal.xyz;\n\n#endif\n\n#if !defined( USE_SKINNING ) && defined( USE_MORPHNORMALS )\n\n  objectNormal = morphedNormal;\n\n#endif\n\n#if !defined( USE_SKINNING ) && ! defined( USE_MORPHNORMALS )\n\n  objectNormal = normal;\n\n#endif\n\n#ifdef FLIP_SIDED\n\n objectNormal = -objectNormal;\n\n#endif\n\nvec3 transformedNormal = normalMatrix * objectNormal;",a.ShaderChunk.lights_phong_pars_fragment="uniform vec3 ambientLightColor;\n\n#if MAX_DIR_LIGHTS > 0\n\n uniform vec3 directionalLightColor[ MAX_DIR_LIGHTS ];\n uniform vec3 directionalLightDirection[ MAX_DIR_LIGHTS ];\n\n#endif\n\n#if MAX_HEMI_LIGHTS > 0\n\n  uniform vec3 hemisphereLightSkyColor[ MAX_HEMI_LIGHTS ];\n  uniform vec3 hemisphereLightGroundColor[ MAX_HEMI_LIGHTS ];\n uniform vec3 hemisphereLightDirection[ MAX_HEMI_LIGHTS ];\n\n#endif\n\n#if MAX_POINT_LIGHTS > 0\n\n uniform vec3 pointLightColor[ MAX_POINT_LIGHTS ];\n\n uniform vec3 pointLightPosition[ MAX_POINT_LIGHTS ];\n  uniform float pointLightDistance[ MAX_POINT_LIGHTS ];\n\n#endif\n\n#if MAX_SPOT_LIGHTS > 0\n\n  uniform vec3 spotLightColor[ MAX_SPOT_LIGHTS ];\n uniform vec3 spotLightPosition[ MAX_SPOT_LIGHTS ];\n  uniform vec3 spotLightDirection[ MAX_SPOT_LIGHTS ];\n uniform float spotLightAngleCos[ MAX_SPOT_LIGHTS ];\n uniform float spotLightExponent[ MAX_SPOT_LIGHTS ];\n\n uniform float spotLightDistance[ MAX_SPOT_LIGHTS ];\n\n#endif\n\n#if MAX_SPOT_LIGHTS > 0 || defined( USE_BUMPMAP ) || defined( USE_ENVMAP )\n\n varying vec3 vWorldPosition;\n\n#endif\n\n#ifdef WRAP_AROUND\n\n  uniform vec3 wrapRGB;\n\n#endif\n\nvarying vec3 vViewPosition;\nvarying vec3 vNormal;",a.ShaderChunk.skinbase_vertex="#ifdef USE_SKINNING\n\n  mat4 boneMatX = getBoneMatrix( skinIndex.x );\n mat4 boneMatY = getBoneMatrix( skinIndex.y );\n mat4 boneMatZ = getBoneMatrix( skinIndex.z );\n mat4 boneMatW = getBoneMatrix( skinIndex.w );\n\n#endif",a.ShaderChunk.map_vertex="#if defined( USE_MAP ) || defined( USE_BUMPMAP ) || defined( USE_NORMALMAP ) || defined( USE_SPECULARMAP ) || defined( USE_ALPHAMAP )\n\n vUv = uv * offsetRepeat.zw + offsetRepeat.xy;\n\n#endif",a.ShaderChunk.lightmap_fragment="#ifdef USE_LIGHTMAP\n\n  gl_FragColor = gl_FragColor * texture2D( lightMap, vUv2 );\n\n#endif",a.ShaderChunk.shadowmap_pars_vertex="#ifdef USE_SHADOWMAP\n\n varying vec4 vShadowCoord[ MAX_SHADOWS ];\n uniform mat4 shadowMatrix[ MAX_SHADOWS ];\n\n#endif",a.ShaderChunk.color_fragment="#ifdef USE_COLOR\n\n  gl_FragColor = gl_FragColor * vec4( vColor, 1.0 );\n\n#endif",a.ShaderChunk.morphtarget_vertex="#ifdef USE_MORPHTARGETS\n\n vec3 morphed = vec3( 0.0 );\n morphed += ( morphTarget0 - position ) * morphTargetInfluences[ 0 ];\n  morphed += ( morphTarget1 - position ) * morphTargetInfluences[ 1 ];\n  morphed += ( morphTarget2 - position ) * morphTargetInfluences[ 2 ];\n  morphed += ( morphTarget3 - position ) * morphTargetInfluences[ 3 ];\n\n  #ifndef USE_MORPHNORMALS\n\n  morphed += ( morphTarget4 - position ) * morphTargetInfluences[ 4 ];\n  morphed += ( morphTarget5 - position ) * morphTargetInfluences[ 5 ];\n  morphed += ( morphTarget6 - position ) * morphTargetInfluences[ 6 ];\n  morphed += ( morphTarget7 - position ) * morphTargetInfluences[ 7 ];\n\n  #endif\n\n  morphed += position;\n\n#endif",a.ShaderChunk.envmap_vertex="#if defined( USE_ENVMAP ) && ! defined( USE_BUMPMAP ) && ! defined( USE_NORMALMAP ) && ! defined( PHONG )\n\n  vec3 worldNormal = mat3( modelMatrix[ 0 ].xyz, modelMatrix[ 1 ].xyz, modelMatrix[ 2 ].xyz ) * objectNormal;\n worldNormal = normalize( worldNormal );\n\n vec3 cameraToVertex = normalize( worldPosition.xyz - cameraPosition );\n\n  if ( useRefract ) {\n\n   vReflect = refract( cameraToVertex, worldNormal, refractionRatio );\n\n } else {\n\n    vReflect = reflect( cameraToVertex, worldNormal );\n\n  }\n\n#endif",a.ShaderChunk.shadowmap_fragment="#ifdef USE_SHADOWMAP\n\n  #ifdef SHADOWMAP_DEBUG\n\n    vec3 frustumColors[3];\n    frustumColors[0] = vec3( 1.0, 0.5, 0.0 );\n   frustumColors[1] = vec3( 0.0, 1.0, 0.8 );\n   frustumColors[2] = vec3( 0.0, 0.5, 1.0 );\n\n #endif\n\n  #ifdef SHADOWMAP_CASCADE\n\n    int inFrustumCount = 0;\n\n #endif\n\n  float fDepth;\n vec3 shadowColor = vec3( 1.0 );\n\n for( int i = 0; i < MAX_SHADOWS; i ++ ) {\n\n   vec3 shadowCoord = vShadowCoord[ i ].xyz / vShadowCoord[ i ].w;\n\n       // if ( something && something ) breaks ATI OpenGL shader compiler\n        // if ( all( something, something ) ) using this instead\n\n    bvec4 inFrustumVec = bvec4 ( shadowCoord.x >= 0.0, shadowCoord.x <= 1.0, shadowCoord.y >= 0.0, shadowCoord.y <= 1.0 );\n    bool inFrustum = all( inFrustumVec );\n\n       // don't shadow pixels outside of light frustum\n       // use just first frustum (for cascades)\n        // don't shadow pixels behind far plane of light frustum\n\n    #ifdef SHADOWMAP_CASCADE\n\n      inFrustumCount += int( inFrustum );\n     bvec3 frustumTestVec = bvec3( inFrustum, inFrustumCount == 1, shadowCoord.z <= 1.0 );\n\n   #else\n\n     bvec2 frustumTestVec = bvec2( inFrustum, shadowCoord.z <= 1.0 );\n\n    #endif\n\n    bool frustumTest = all( frustumTestVec );\n\n   if ( frustumTest ) {\n\n      shadowCoord.z += shadowBias[ i ];\n\n     #if defined( SHADOWMAP_TYPE_PCF )\n\n           // Percentage-close filtering\n           // (9 pixel kernel)\n           // http://fabiensanglard.net/shadowmappingPCF/\n\n        float shadow = 0.0;\n\n   /*\n            // nested loops breaks shader compiler / validator on some ATI cards when using OpenGL\n            // must enroll loop manually\n\n        for ( float y = -1.25; y <= 1.25; y += 1.25 )\n         for ( float x = -1.25; x <= 1.25; x += 1.25 ) {\n\n           vec4 rgbaDepth = texture2D( shadowMap[ i ], vec2( x * xPixelOffset, y * yPixelOffset ) + shadowCoord.xy );\n\n                // doesn't seem to produce any noticeable visual difference compared to simple texture2D lookup\n               //vec4 rgbaDepth = texture2DProj( shadowMap[ i ], vec4( vShadowCoord[ i ].w * ( vec2( x * xPixelOffset, y * yPixelOffset ) + shadowCoord.xy ), 0.05, vShadowCoord[ i ].w ) );\n\n           float fDepth = unpackDepth( rgbaDepth );\n\n            if ( fDepth < shadowCoord.z )\n             shadow += 1.0;\n\n        }\n\n       shadow /= 9.0;\n\n    */\n\n        const float shadowDelta = 1.0 / 9.0;\n\n        float xPixelOffset = 1.0 / shadowMapSize[ i ].x;\n        float yPixelOffset = 1.0 / shadowMapSize[ i ].y;\n\n        float dx0 = -1.25 * xPixelOffset;\n       float dy0 = -1.25 * yPixelOffset;\n       float dx1 = 1.25 * xPixelOffset;\n        float dy1 = 1.25 * yPixelOffset;\n\n        fDepth = unpackDepth( texture2D( shadowMap[ i ], shadowCoord.xy + vec2( dx0, dy0 ) ) );\n       if ( fDepth < shadowCoord.z ) shadow += shadowDelta;\n\n        fDepth = unpackDepth( texture2D( shadowMap[ i ], shadowCoord.xy + vec2( 0.0, dy0 ) ) );\n       if ( fDepth < shadowCoord.z ) shadow += shadowDelta;\n\n        fDepth = unpackDepth( texture2D( shadowMap[ i ], shadowCoord.xy + vec2( dx1, dy0 ) ) );\n       if ( fDepth < shadowCoord.z ) shadow += shadowDelta;\n\n        fDepth = unpackDepth( texture2D( shadowMap[ i ], shadowCoord.xy + vec2( dx0, 0.0 ) ) );\n       if ( fDepth < shadowCoord.z ) shadow += shadowDelta;\n\n        fDepth = unpackDepth( texture2D( shadowMap[ i ], shadowCoord.xy ) );\n        if ( fDepth < shadowCoord.z ) shadow += shadowDelta;\n\n        fDepth = unpackDepth( texture2D( shadowMap[ i ], shadowCoord.xy + vec2( dx1, 0.0 ) ) );\n       if ( fDepth < shadowCoord.z ) shadow += shadowDelta;\n\n        fDepth = unpackDepth( texture2D( shadowMap[ i ], shadowCoord.xy + vec2( dx0, dy1 ) ) );\n       if ( fDepth < shadowCoord.z ) shadow += shadowDelta;\n\n        fDepth = unpackDepth( texture2D( shadowMap[ i ], shadowCoord.xy + vec2( 0.0, dy1 ) ) );\n       if ( fDepth < shadowCoord.z ) shadow += shadowDelta;\n\n        fDepth = unpackDepth( texture2D( shadowMap[ i ], shadowCoord.xy + vec2( dx1, dy1 ) ) );\n       if ( fDepth < shadowCoord.z ) shadow += shadowDelta;\n\n        shadowColor = shadowColor * vec3( ( 1.0 - shadowDarkness[ i ] * shadow ) );\n\n     #elif defined( SHADOWMAP_TYPE_PCF_SOFT )\n\n            // Percentage-close filtering\n           // (9 pixel kernel)\n           // http://fabiensanglard.net/shadowmappingPCF/\n\n        float shadow = 0.0;\n\n       float xPixelOffset = 1.0 / shadowMapSize[ i ].x;\n        float yPixelOffset = 1.0 / shadowMapSize[ i ].y;\n\n        float dx0 = -1.0 * xPixelOffset;\n        float dy0 = -1.0 * yPixelOffset;\n        float dx1 = 1.0 * xPixelOffset;\n       float dy1 = 1.0 * yPixelOffset;\n\n       mat3 shadowKernel;\n        mat3 depthKernel;\n\n       depthKernel[0][0] = unpackDepth( texture2D( shadowMap[ i ], shadowCoord.xy + vec2( dx0, dy0 ) ) );\n        depthKernel[0][1] = unpackDepth( texture2D( shadowMap[ i ], shadowCoord.xy + vec2( dx0, 0.0 ) ) );\n        depthKernel[0][2] = unpackDepth( texture2D( shadowMap[ i ], shadowCoord.xy + vec2( dx0, dy1 ) ) );\n        depthKernel[1][0] = unpackDepth( texture2D( shadowMap[ i ], shadowCoord.xy + vec2( 0.0, dy0 ) ) );\n        depthKernel[1][1] = unpackDepth( texture2D( shadowMap[ i ], shadowCoord.xy ) );\n       depthKernel[1][2] = unpackDepth( texture2D( shadowMap[ i ], shadowCoord.xy + vec2( 0.0, dy1 ) ) );\n        depthKernel[2][0] = unpackDepth( texture2D( shadowMap[ i ], shadowCoord.xy + vec2( dx1, dy0 ) ) );\n        depthKernel[2][1] = unpackDepth( texture2D( shadowMap[ i ], shadowCoord.xy + vec2( dx1, 0.0 ) ) );\n        depthKernel[2][2] = unpackDepth( texture2D( shadowMap[ i ], shadowCoord.xy + vec2( dx1, dy1 ) ) );\n\n        vec3 shadowZ = vec3( shadowCoord.z );\n       shadowKernel[0] = vec3(lessThan(depthKernel[0], shadowZ ));\n       shadowKernel[0] *= vec3(0.25);\n\n        shadowKernel[1] = vec3(lessThan(depthKernel[1], shadowZ ));\n       shadowKernel[1] *= vec3(0.25);\n\n        shadowKernel[2] = vec3(lessThan(depthKernel[2], shadowZ ));\n       shadowKernel[2] *= vec3(0.25);\n\n        vec2 fractionalCoord = 1.0 - fract( shadowCoord.xy * shadowMapSize[i].xy );\n\n       shadowKernel[0] = mix( shadowKernel[1], shadowKernel[0], fractionalCoord.x );\n       shadowKernel[1] = mix( shadowKernel[2], shadowKernel[1], fractionalCoord.x );\n\n       vec4 shadowValues;\n        shadowValues.x = mix( shadowKernel[0][1], shadowKernel[0][0], fractionalCoord.y );\n        shadowValues.y = mix( shadowKernel[0][2], shadowKernel[0][1], fractionalCoord.y );\n        shadowValues.z = mix( shadowKernel[1][1], shadowKernel[1][0], fractionalCoord.y );\n        shadowValues.w = mix( shadowKernel[1][2], shadowKernel[1][1], fractionalCoord.y );\n\n        shadow = dot( shadowValues, vec4( 1.0 ) );\n\n        shadowColor = shadowColor * vec3( ( 1.0 - shadowDarkness[ i ] * shadow ) );\n\n     #else\n\n       vec4 rgbaDepth = texture2D( shadowMap[ i ], shadowCoord.xy );\n       float fDepth = unpackDepth( rgbaDepth );\n\n        if ( fDepth < shadowCoord.z )\n\n   // spot with multiple shadows is darker\n\n         shadowColor = shadowColor * vec3( 1.0 - shadowDarkness[ i ] );\n\n    // spot with multiple shadows has the same color as single shadow spot\n\n    //          shadowColor = min( shadowColor, vec3( shadowDarkness[ i ] ) );\n\n      #endif\n\n    }\n\n\n   #ifdef SHADOWMAP_DEBUG\n\n      #ifdef SHADOWMAP_CASCADE\n\n        if ( inFrustum && inFrustumCount == 1 ) gl_FragColor.xyz *= frustumColors[ i ];\n\n     #else\n\n       if ( inFrustum ) gl_FragColor.xyz *= frustumColors[ i ];\n\n      #endif\n\n    #endif\n\n  }\n\n #ifdef GAMMA_OUTPUT\n\n   shadowColor *= shadowColor;\n\n #endif\n\n  gl_FragColor.xyz = gl_FragColor.xyz * shadowColor;\n\n#endif\n",
a.ShaderChunk.worldpos_vertex="#if defined( USE_ENVMAP ) || defined( PHONG ) || defined( LAMBERT ) || defined ( USE_SHADOWMAP )\n\n #ifdef USE_SKINNING\n\n   vec4 worldPosition = modelMatrix * skinned;\n\n #endif\n\n  #if defined( USE_MORPHTARGETS ) && ! defined( USE_SKINNING )\n\n    vec4 worldPosition = modelMatrix * vec4( morphed, 1.0 );\n\n  #endif\n\n  #if ! defined( USE_MORPHTARGETS ) && ! defined( USE_SKINNING )\n\n    vec4 worldPosition = modelMatrix * vec4( position, 1.0 );\n\n #endif\n\n#endif",a.ShaderChunk.shadowmap_pars_fragment="#ifdef USE_SHADOWMAP\n\n uniform sampler2D shadowMap[ MAX_SHADOWS ];\n uniform vec2 shadowMapSize[ MAX_SHADOWS ];\n\n  uniform float shadowDarkness[ MAX_SHADOWS ];\n  uniform float shadowBias[ MAX_SHADOWS ];\n\n  varying vec4 vShadowCoord[ MAX_SHADOWS ];\n\n float unpackDepth( const in vec4 rgba_depth ) {\n\n   const vec4 bit_shift = vec4( 1.0 / ( 256.0 * 256.0 * 256.0 ), 1.0 / ( 256.0 * 256.0 ), 1.0 / 256.0, 1.0 );\n    float depth = dot( rgba_depth, bit_shift );\n   return depth;\n\n }\n\n#endif",a.ShaderChunk.skinning_pars_vertex="#ifdef USE_SKINNING\n\n uniform mat4 bindMatrix;\n  uniform mat4 bindMatrixInverse;\n\n #ifdef BONE_TEXTURE\n\n   uniform sampler2D boneTexture;\n    uniform int boneTextureWidth;\n   uniform int boneTextureHeight;\n\n    mat4 getBoneMatrix( const in float i ) {\n\n      float j = i * 4.0;\n      float x = mod( j, float( boneTextureWidth ) );\n      float y = floor( j / float( boneTextureWidth ) );\n\n     float dx = 1.0 / float( boneTextureWidth );\n     float dy = 1.0 / float( boneTextureHeight );\n\n      y = dy * ( y + 0.5 );\n\n     vec4 v1 = texture2D( boneTexture, vec2( dx * ( x + 0.5 ), y ) );\n      vec4 v2 = texture2D( boneTexture, vec2( dx * ( x + 1.5 ), y ) );\n      vec4 v3 = texture2D( boneTexture, vec2( dx * ( x + 2.5 ), y ) );\n      vec4 v4 = texture2D( boneTexture, vec2( dx * ( x + 3.5 ), y ) );\n\n      mat4 bone = mat4( v1, v2, v3, v4 );\n\n     return bone;\n\n    }\n\n #else\n\n   uniform mat4 boneGlobalMatrices[ MAX_BONES ];\n\n   mat4 getBoneMatrix( const in float i ) {\n\n      mat4 bone = boneGlobalMatrices[ int(i) ];\n     return bone;\n\n    }\n\n #endif\n\n#endif\n",a.ShaderChunk.logdepthbuf_pars_fragment="#ifdef USE_LOGDEPTHBUF\n\n uniform float logDepthBufFC;\n\n  #ifdef USE_LOGDEPTHBUF_EXT\n\n    #extension GL_EXT_frag_depth : enable\n   varying float vFragDepth;\n\n #endif\n\n#endif",a.ShaderChunk.alphamap_fragment="#ifdef USE_ALPHAMAP\n\n  gl_FragColor.a *= texture2D( alphaMap, vUv ).g;\n\n#endif\n",a.ShaderChunk.alphamap_pars_fragment="#ifdef USE_ALPHAMAP\n\n uniform sampler2D alphaMap;\n\n#endif\n",a.UniformsUtils={merge:function(a){for(var b={},c=0;c<a.length;c++){var d=this.clone(a[c]);for(var e in d)b[e]=d[e]}return b},clone:function(b){var c={};for(var d in b){c[d]={};for(var e in b[d]){var f=b[d][e];f instanceof a.Color||f instanceof a.Vector2||f instanceof a.Vector3||f instanceof a.Vector4||f instanceof a.Matrix4||f instanceof a.Texture?c[d][e]=f.clone():f instanceof Array?c[d][e]=f.slice():c[d][e]=f}}return c}},a.UniformsLib={common:{diffuse:{type:"c",value:new a.Color(15658734)},opacity:{type:"f",value:1},map:{type:"t",value:null},offsetRepeat:{type:"v4",value:new a.Vector4(0,0,1,1)},lightMap:{type:"t",value:null},specularMap:{type:"t",value:null},alphaMap:{type:"t",value:null},envMap:{type:"t",value:null},flipEnvMap:{type:"f",value:-1},useRefract:{type:"i",value:0},reflectivity:{type:"f",value:1},refractionRatio:{type:"f",value:.98},combine:{type:"i",value:0},morphTargetInfluences:{type:"f",value:0}},bump:{bumpMap:{type:"t",value:null},bumpScale:{type:"f",value:1}},normalmap:{normalMap:{type:"t",value:null},normalScale:{type:"v2",value:new a.Vector2(1,1)}},fog:{fogDensity:{type:"f",value:25e-5},fogNear:{type:"f",value:1},fogFar:{type:"f",value:2e3},fogColor:{type:"c",value:new a.Color(16777215)}},lights:{ambientLightColor:{type:"fv",value:[]},directionalLightDirection:{type:"fv",value:[]},directionalLightColor:{type:"fv",value:[]},hemisphereLightDirection:{type:"fv",value:[]},hemisphereLightSkyColor:{type:"fv",value:[]},hemisphereLightGroundColor:{type:"fv",value:[]},pointLightColor:{type:"fv",value:[]},pointLightPosition:{type:"fv",value:[]},pointLightDistance:{type:"fv1",value:[]},spotLightColor:{type:"fv",value:[]},spotLightPosition:{type:"fv",value:[]},spotLightDirection:{type:"fv",value:[]},spotLightDistance:{type:"fv1",value:[]},spotLightAngleCos:{type:"fv1",value:[]},spotLightExponent:{type:"fv1",value:[]}},particle:{psColor:{type:"c",value:new a.Color(15658734)},opacity:{type:"f",value:1},size:{type:"f",value:1},scale:{type:"f",value:1},map:{type:"t",value:null},fogDensity:{type:"f",value:25e-5},fogNear:{type:"f",value:1},fogFar:{type:"f",value:2e3},fogColor:{type:"c",value:new a.Color(16777215)}},shadowmap:{shadowMap:{type:"tv",value:[]},shadowMapSize:{type:"v2v",value:[]},shadowBias:{type:"fv1",value:[]},shadowDarkness:{type:"fv1",value:[]},shadowMatrix:{type:"m4v",value:[]}}},a.ShaderLib={basic:{uniforms:a.UniformsUtils.merge([a.UniformsLib.common,a.UniformsLib.fog,a.UniformsLib.shadowmap]),vertexShader:[a.ShaderChunk.map_pars_vertex,a.ShaderChunk.lightmap_pars_vertex,a.ShaderChunk.envmap_pars_vertex,a.ShaderChunk.color_pars_vertex,a.ShaderChunk.morphtarget_pars_vertex,a.ShaderChunk.skinning_pars_vertex,a.ShaderChunk.shadowmap_pars_vertex,a.ShaderChunk.logdepthbuf_pars_vertex,"void main() {",a.ShaderChunk.map_vertex,a.ShaderChunk.lightmap_vertex,a.ShaderChunk.color_vertex,a.ShaderChunk.skinbase_vertex," #ifdef USE_ENVMAP",a.ShaderChunk.morphnormal_vertex,a.ShaderChunk.skinnormal_vertex,a.ShaderChunk.defaultnormal_vertex," #endif",a.ShaderChunk.morphtarget_vertex,a.ShaderChunk.skinning_vertex,a.ShaderChunk.default_vertex,a.ShaderChunk.logdepthbuf_vertex,a.ShaderChunk.worldpos_vertex,a.ShaderChunk.envmap_vertex,a.ShaderChunk.shadowmap_vertex,"}"].join("\n"),fragmentShader:["uniform vec3 diffuse;","uniform float opacity;",a.ShaderChunk.color_pars_fragment,a.ShaderChunk.map_pars_fragment,a.ShaderChunk.alphamap_pars_fragment,a.ShaderChunk.lightmap_pars_fragment,a.ShaderChunk.envmap_pars_fragment,a.ShaderChunk.fog_pars_fragment,a.ShaderChunk.shadowmap_pars_fragment,a.ShaderChunk.specularmap_pars_fragment,a.ShaderChunk.logdepthbuf_pars_fragment,"void main() {"," gl_FragColor = vec4( diffuse, opacity );",a.ShaderChunk.logdepthbuf_fragment,a.ShaderChunk.map_fragment,a.ShaderChunk.alphamap_fragment,a.ShaderChunk.alphatest_fragment,a.ShaderChunk.specularmap_fragment,a.ShaderChunk.lightmap_fragment,a.ShaderChunk.color_fragment,a.ShaderChunk.envmap_fragment,a.ShaderChunk.shadowmap_fragment,a.ShaderChunk.linear_to_gamma_fragment,a.ShaderChunk.fog_fragment,"}"].join("\n")},lambert:{uniforms:a.UniformsUtils.merge([a.UniformsLib.common,a.UniformsLib.fog,a.UniformsLib.lights,a.UniformsLib.shadowmap,{ambient:{type:"c",value:new a.Color(16777215)},emissive:{type:"c",value:new a.Color(0)},wrapRGB:{type:"v3",value:new a.Vector3(1,1,1)}}]),vertexShader:["#define LAMBERT","varying vec3 vLightFront;","#ifdef DOUBLE_SIDED"," varying vec3 vLightBack;","#endif",a.ShaderChunk.map_pars_vertex,a.ShaderChunk.lightmap_pars_vertex,a.ShaderChunk.envmap_pars_vertex,a.ShaderChunk.lights_lambert_pars_vertex,a.ShaderChunk.color_pars_vertex,a.ShaderChunk.morphtarget_pars_vertex,a.ShaderChunk.skinning_pars_vertex,a.ShaderChunk.shadowmap_pars_vertex,a.ShaderChunk.logdepthbuf_pars_vertex,"void main() {",a.ShaderChunk.map_vertex,a.ShaderChunk.lightmap_vertex,a.ShaderChunk.color_vertex,a.ShaderChunk.morphnormal_vertex,a.ShaderChunk.skinbase_vertex,a.ShaderChunk.skinnormal_vertex,a.ShaderChunk.defaultnormal_vertex,a.ShaderChunk.morphtarget_vertex,a.ShaderChunk.skinning_vertex,a.ShaderChunk.default_vertex,a.ShaderChunk.logdepthbuf_vertex,a.ShaderChunk.worldpos_vertex,a.ShaderChunk.envmap_vertex,a.ShaderChunk.lights_lambert_vertex,a.ShaderChunk.shadowmap_vertex,"}"].join("\n"),fragmentShader:["uniform float opacity;","varying vec3 vLightFront;","#ifdef DOUBLE_SIDED"," varying vec3 vLightBack;","#endif",a.ShaderChunk.color_pars_fragment,a.ShaderChunk.map_pars_fragment,a.ShaderChunk.alphamap_pars_fragment,a.ShaderChunk.lightmap_pars_fragment,a.ShaderChunk.envmap_pars_fragment,a.ShaderChunk.fog_pars_fragment,a.ShaderChunk.shadowmap_pars_fragment,a.ShaderChunk.specularmap_pars_fragment,a.ShaderChunk.logdepthbuf_pars_fragment,"void main() {"," gl_FragColor = vec4( vec3( 1.0 ), opacity );",a.ShaderChunk.logdepthbuf_fragment,a.ShaderChunk.map_fragment,a.ShaderChunk.alphamap_fragment,a.ShaderChunk.alphatest_fragment,a.ShaderChunk.specularmap_fragment," #ifdef DOUBLE_SIDED","   if ( gl_FrontFacing )","     gl_FragColor.xyz *= vLightFront;","   else","     gl_FragColor.xyz *= vLightBack;"," #else","   gl_FragColor.xyz *= vLightFront;"," #endif",a.ShaderChunk.lightmap_fragment,a.ShaderChunk.color_fragment,a.ShaderChunk.envmap_fragment,a.ShaderChunk.shadowmap_fragment,a.ShaderChunk.linear_to_gamma_fragment,a.ShaderChunk.fog_fragment,"}"].join("\n")},phong:{uniforms:a.UniformsUtils.merge([a.UniformsLib.common,a.UniformsLib.bump,a.UniformsLib.normalmap,a.UniformsLib.fog,a.UniformsLib.lights,a.UniformsLib.shadowmap,{ambient:{type:"c",value:new a.Color(16777215)},emissive:{type:"c",value:new a.Color(0)},specular:{type:"c",value:new a.Color(1118481)},shininess:{type:"f",value:30},wrapRGB:{type:"v3",value:new a.Vector3(1,1,1)}}]),vertexShader:["#define PHONG","varying vec3 vViewPosition;","varying vec3 vNormal;",a.ShaderChunk.map_pars_vertex,a.ShaderChunk.lightmap_pars_vertex,a.ShaderChunk.envmap_pars_vertex,a.ShaderChunk.lights_phong_pars_vertex,a.ShaderChunk.color_pars_vertex,a.ShaderChunk.morphtarget_pars_vertex,a.ShaderChunk.skinning_pars_vertex,a.ShaderChunk.shadowmap_pars_vertex,a.ShaderChunk.logdepthbuf_pars_vertex,"void main() {",a.ShaderChunk.map_vertex,a.ShaderChunk.lightmap_vertex,a.ShaderChunk.color_vertex,a.ShaderChunk.morphnormal_vertex,a.ShaderChunk.skinbase_vertex,a.ShaderChunk.skinnormal_vertex,a.ShaderChunk.defaultnormal_vertex," vNormal = normalize( transformedNormal );",a.ShaderChunk.morphtarget_vertex,a.ShaderChunk.skinning_vertex,a.ShaderChunk.default_vertex,a.ShaderChunk.logdepthbuf_vertex," vViewPosition = -mvPosition.xyz;",a.ShaderChunk.worldpos_vertex,a.ShaderChunk.envmap_vertex,a.ShaderChunk.lights_phong_vertex,a.ShaderChunk.shadowmap_vertex,"}"].join("\n"),fragmentShader:["#define PHONG","uniform vec3 diffuse;","uniform float opacity;","uniform vec3 ambient;","uniform vec3 emissive;","uniform vec3 specular;","uniform float shininess;",a.ShaderChunk.color_pars_fragment,a.ShaderChunk.map_pars_fragment,a.ShaderChunk.alphamap_pars_fragment,a.ShaderChunk.lightmap_pars_fragment,a.ShaderChunk.envmap_pars_fragment,a.ShaderChunk.fog_pars_fragment,a.ShaderChunk.lights_phong_pars_fragment,a.ShaderChunk.shadowmap_pars_fragment,a.ShaderChunk.bumpmap_pars_fragment,a.ShaderChunk.normalmap_pars_fragment,a.ShaderChunk.specularmap_pars_fragment,a.ShaderChunk.logdepthbuf_pars_fragment,"void main() {"," gl_FragColor = vec4( vec3( 1.0 ), opacity );",a.ShaderChunk.logdepthbuf_fragment,a.ShaderChunk.map_fragment,a.ShaderChunk.alphamap_fragment,a.ShaderChunk.alphatest_fragment,a.ShaderChunk.specularmap_fragment,a.ShaderChunk.lights_phong_fragment,a.ShaderChunk.lightmap_fragment,a.ShaderChunk.color_fragment,a.ShaderChunk.envmap_fragment,a.ShaderChunk.shadowmap_fragment,a.ShaderChunk.linear_to_gamma_fragment,a.ShaderChunk.fog_fragment,"}"].join("\n")},particle_basic:{uniforms:a.UniformsUtils.merge([a.UniformsLib.particle,a.UniformsLib.shadowmap]),vertexShader:["uniform float size;","uniform float scale;",a.ShaderChunk.color_pars_vertex,a.ShaderChunk.shadowmap_pars_vertex,a.ShaderChunk.logdepthbuf_pars_vertex,"void main() {",a.ShaderChunk.color_vertex," vec4 mvPosition = modelViewMatrix * vec4( position, 1.0 );"," #ifdef USE_SIZEATTENUATION","   gl_PointSize = size * ( scale / length( mvPosition.xyz ) );"," #else","   gl_PointSize = size;"," #endif"," gl_Position = projectionMatrix * mvPosition;",a.ShaderChunk.logdepthbuf_vertex,a.ShaderChunk.worldpos_vertex,a.ShaderChunk.shadowmap_vertex,"}"].join("\n"),fragmentShader:["uniform vec3 psColor;","uniform float opacity;",a.ShaderChunk.color_pars_fragment,a.ShaderChunk.map_particle_pars_fragment,a.ShaderChunk.fog_pars_fragment,a.ShaderChunk.shadowmap_pars_fragment,a.ShaderChunk.logdepthbuf_pars_fragment,"void main() {"," gl_FragColor = vec4( psColor, opacity );",a.ShaderChunk.logdepthbuf_fragment,a.ShaderChunk.map_particle_fragment,a.ShaderChunk.alphatest_fragment,a.ShaderChunk.color_fragment,a.ShaderChunk.shadowmap_fragment,a.ShaderChunk.fog_fragment,"}"].join("\n")},dashed:{uniforms:a.UniformsUtils.merge([a.UniformsLib.common,a.UniformsLib.fog,{scale:{type:"f",value:1},dashSize:{type:"f",value:1},totalSize:{type:"f",value:2}}]),vertexShader:["uniform float scale;","attribute float lineDistance;","varying float vLineDistance;",a.ShaderChunk.color_pars_vertex,a.ShaderChunk.logdepthbuf_pars_vertex,"void main() {",a.ShaderChunk.color_vertex," vLineDistance = scale * lineDistance;"," vec4 mvPosition = modelViewMatrix * vec4( position, 1.0 );"," gl_Position = projectionMatrix * mvPosition;",a.ShaderChunk.logdepthbuf_vertex,"}"].join("\n"),fragmentShader:["uniform vec3 diffuse;","uniform float opacity;","uniform float dashSize;","uniform float totalSize;","varying float vLineDistance;",a.ShaderChunk.color_pars_fragment,a.ShaderChunk.fog_pars_fragment,a.ShaderChunk.logdepthbuf_pars_fragment,"void main() {"," if ( mod( vLineDistance, totalSize ) > dashSize ) {","   discard;"," }"," gl_FragColor = vec4( diffuse, opacity );",a.ShaderChunk.logdepthbuf_fragment,a.ShaderChunk.color_fragment,a.ShaderChunk.fog_fragment,"}"].join("\n")},depth:{uniforms:{mNear:{type:"f",value:1},mFar:{type:"f",value:2e3},opacity:{type:"f",value:1}},vertexShader:[a.ShaderChunk.morphtarget_pars_vertex,a.ShaderChunk.logdepthbuf_pars_vertex,"void main() {",a.ShaderChunk.morphtarget_vertex,a.ShaderChunk.default_vertex,a.ShaderChunk.logdepthbuf_vertex,"}"].join("\n"),fragmentShader:["uniform float mNear;","uniform float mFar;","uniform float opacity;",a.ShaderChunk.logdepthbuf_pars_fragment,"void main() {",a.ShaderChunk.logdepthbuf_fragment," #ifdef USE_LOGDEPTHBUF_EXT","   float depth = gl_FragDepthEXT / gl_FragCoord.w;"," #else","   float depth = gl_FragCoord.z / gl_FragCoord.w;"," #endif"," float color = 1.0 - smoothstep( mNear, mFar, depth );"," gl_FragColor = vec4( vec3( color ), opacity );","}"].join("\n")},normal:{uniforms:{opacity:{type:"f",value:1}},vertexShader:["varying vec3 vNormal;",a.ShaderChunk.morphtarget_pars_vertex,a.ShaderChunk.logdepthbuf_pars_vertex,"void main() {"," vNormal = normalize( normalMatrix * normal );",a.ShaderChunk.morphtarget_vertex,a.ShaderChunk.default_vertex,a.ShaderChunk.logdepthbuf_vertex,"}"].join("\n"),fragmentShader:["uniform float opacity;","varying vec3 vNormal;",a.ShaderChunk.logdepthbuf_pars_fragment,"void main() {"," gl_FragColor = vec4( 0.5 * normalize( vNormal ) + 0.5, opacity );",a.ShaderChunk.logdepthbuf_fragment,"}"].join("\n")},normalmap:{uniforms:a.UniformsUtils.merge([a.UniformsLib.fog,a.UniformsLib.lights,a.UniformsLib.shadowmap,{enableAO:{type:"i",value:0},enableDiffuse:{type:"i",value:0},enableSpecular:{type:"i",value:0},enableReflection:{type:"i",value:0},enableDisplacement:{type:"i",value:0},tDisplacement:{type:"t",value:null},tDiffuse:{type:"t",value:null},tCube:{type:"t",value:null},tNormal:{type:"t",value:null},tSpecular:{type:"t",value:null},tAO:{type:"t",value:null},uNormalScale:{type:"v2",value:new a.Vector2(1,1)},uDisplacementBias:{type:"f",value:0},uDisplacementScale:{type:"f",value:1},diffuse:{type:"c",value:new a.Color(16777215)},specular:{type:"c",value:new a.Color(1118481)},ambient:{type:"c",value:new a.Color(16777215)},shininess:{type:"f",value:30},opacity:{type:"f",value:1},useRefract:{type:"i",value:0},refractionRatio:{type:"f",value:.98},reflectivity:{type:"f",value:.5},uOffset:{type:"v2",value:new a.Vector2(0,0)},uRepeat:{type:"v2",value:new a.Vector2(1,1)},wrapRGB:{type:"v3",value:new a.Vector3(1,1,1)}}]),fragmentShader:["uniform vec3 ambient;","uniform vec3 diffuse;","uniform vec3 specular;","uniform float shininess;","uniform float opacity;","uniform bool enableDiffuse;","uniform bool enableSpecular;","uniform bool enableAO;","uniform bool enableReflection;","uniform sampler2D tDiffuse;","uniform sampler2D tNormal;","uniform sampler2D tSpecular;","uniform sampler2D tAO;","uniform samplerCube tCube;","uniform vec2 uNormalScale;","uniform bool useRefract;","uniform float refractionRatio;","uniform float reflectivity;","varying vec3 vTangent;","varying vec3 vBinormal;","varying vec3 vNormal;","varying vec2 vUv;","uniform vec3 ambientLightColor;","#if MAX_DIR_LIGHTS > 0"," uniform vec3 directionalLightColor[ MAX_DIR_LIGHTS ];"," uniform vec3 directionalLightDirection[ MAX_DIR_LIGHTS ];","#endif","#if MAX_HEMI_LIGHTS > 0"," uniform vec3 hemisphereLightSkyColor[ MAX_HEMI_LIGHTS ];"," uniform vec3 hemisphereLightGroundColor[ MAX_HEMI_LIGHTS ];"," uniform vec3 hemisphereLightDirection[ MAX_HEMI_LIGHTS ];","#endif","#if MAX_POINT_LIGHTS > 0"," uniform vec3 pointLightColor[ MAX_POINT_LIGHTS ];"," uniform vec3 pointLightPosition[ MAX_POINT_LIGHTS ];"," uniform float pointLightDistance[ MAX_POINT_LIGHTS ];","#endif","#if MAX_SPOT_LIGHTS > 0"," uniform vec3 spotLightColor[ MAX_SPOT_LIGHTS ];"," uniform vec3 spotLightPosition[ MAX_SPOT_LIGHTS ];"," uniform vec3 spotLightDirection[ MAX_SPOT_LIGHTS ];"," uniform float spotLightAngleCos[ MAX_SPOT_LIGHTS ];"," uniform float spotLightExponent[ MAX_SPOT_LIGHTS ];"," uniform float spotLightDistance[ MAX_SPOT_LIGHTS ];","#endif","#ifdef WRAP_AROUND"," uniform vec3 wrapRGB;","#endif","varying vec3 vWorldPosition;","varying vec3 vViewPosition;",a.ShaderChunk.shadowmap_pars_fragment,a.ShaderChunk.fog_pars_fragment,a.ShaderChunk.logdepthbuf_pars_fragment,"void main() {",a.ShaderChunk.logdepthbuf_fragment," gl_FragColor = vec4( vec3( 1.0 ), opacity );"," vec3 specularTex = vec3( 1.0 );"," vec3 normalTex = texture2D( tNormal, vUv ).xyz * 2.0 - 1.0;"," normalTex.xy *= uNormalScale;"," normalTex = normalize( normalTex );"," if( enableDiffuse ) {","   #ifdef GAMMA_INPUT","     vec4 texelColor = texture2D( tDiffuse, vUv );","     texelColor.xyz *= texelColor.xyz;","     gl_FragColor = gl_FragColor * texelColor;","   #else","     gl_FragColor = gl_FragColor * texture2D( tDiffuse, vUv );","   #endif"," }"," if( enableAO ) {","   #ifdef GAMMA_INPUT","     vec4 aoColor = texture2D( tAO, vUv );","     aoColor.xyz *= aoColor.xyz;","     gl_FragColor.xyz = gl_FragColor.xyz * aoColor.xyz;","   #else","     gl_FragColor.xyz = gl_FragColor.xyz * texture2D( tAO, vUv ).xyz;","   #endif"," }",a.ShaderChunk.alphatest_fragment," if( enableSpecular )","   specularTex = texture2D( tSpecular, vUv ).xyz;"," mat3 tsb = mat3( normalize( vTangent ), normalize( vBinormal ), normalize( vNormal ) );"," vec3 finalNormal = tsb * normalTex;"," #ifdef FLIP_SIDED","   finalNormal = -finalNormal;"," #endif"," vec3 normal = normalize( finalNormal );"," vec3 viewPosition = normalize( vViewPosition );"," #if MAX_POINT_LIGHTS > 0","   vec3 pointDiffuse = vec3( 0.0 );","   vec3 pointSpecular = vec3( 0.0 );","   for ( int i = 0; i < MAX_POINT_LIGHTS; i ++ ) {","     vec4 lPosition = viewMatrix * vec4( pointLightPosition[ i ], 1.0 );","     vec3 pointVector = lPosition.xyz + vViewPosition.xyz;","     float pointDistance = 1.0;","     if ( pointLightDistance[ i ] > 0.0 )","       pointDistance = 1.0 - min( ( length( pointVector ) / pointLightDistance[ i ] ), 1.0 );","     pointVector = normalize( pointVector );","     #ifdef WRAP_AROUND","       float pointDiffuseWeightFull = max( dot( normal, pointVector ), 0.0 );","       float pointDiffuseWeightHalf = max( 0.5 * dot( normal, pointVector ) + 0.5, 0.0 );","       vec3 pointDiffuseWeight = mix( vec3( pointDiffuseWeightFull ), vec3( pointDiffuseWeightHalf ), wrapRGB );","     #else","       float pointDiffuseWeight = max( dot( normal, pointVector ), 0.0 );","     #endif","     pointDiffuse += pointDistance * pointLightColor[ i ] * diffuse * pointDiffuseWeight;","     vec3 pointHalfVector = normalize( pointVector + viewPosition );","     float pointDotNormalHalf = max( dot( normal, pointHalfVector ), 0.0 );","     float pointSpecularWeight = specularTex.r * max( pow( pointDotNormalHalf, shininess ), 0.0 );","     float specularNormalization = ( shininess + 2.0 ) / 8.0;","     vec3 schlick = specular + vec3( 1.0 - specular ) * pow( max( 1.0 - dot( pointVector, pointHalfVector ), 0.0 ), 5.0 );","     pointSpecular += schlick * pointLightColor[ i ] * pointSpecularWeight * pointDiffuseWeight * pointDistance * specularNormalization;","   }"," #endif"," #if MAX_SPOT_LIGHTS > 0","   vec3 spotDiffuse = vec3( 0.0 );","   vec3 spotSpecular = vec3( 0.0 );","   for ( int i = 0; i < MAX_SPOT_LIGHTS; i ++ ) {","     vec4 lPosition = viewMatrix * vec4( spotLightPosition[ i ], 1.0 );","     vec3 spotVector = lPosition.xyz + vViewPosition.xyz;","     float spotDistance = 1.0;","     if ( spotLightDistance[ i ] > 0.0 )","       spotDistance = 1.0 - min( ( length( spotVector ) / spotLightDistance[ i ] ), 1.0 );","     spotVector = normalize( spotVector );","     float spotEffect = dot( spotLightDirection[ i ], normalize( spotLightPosition[ i ] - vWorldPosition ) );","     if ( spotEffect > spotLightAngleCos[ i ] ) {","       spotEffect = max( pow( max( spotEffect, 0.0 ), spotLightExponent[ i ] ), 0.0 );","       #ifdef WRAP_AROUND","         float spotDiffuseWeightFull = max( dot( normal, spotVector ), 0.0 );","         float spotDiffuseWeightHalf = max( 0.5 * dot( normal, spotVector ) + 0.5, 0.0 );","         vec3 spotDiffuseWeight = mix( vec3( spotDiffuseWeightFull ), vec3( spotDiffuseWeightHalf ), wrapRGB );","       #else","         float spotDiffuseWeight = max( dot( normal, spotVector ), 0.0 );","       #endif","       spotDiffuse += spotDistance * spotLightColor[ i ] * diffuse * spotDiffuseWeight * spotEffect;","       vec3 spotHalfVector = normalize( spotVector + viewPosition );","       float spotDotNormalHalf = max( dot( normal, spotHalfVector ), 0.0 );","       float spotSpecularWeight = specularTex.r * max( pow( spotDotNormalHalf, shininess ), 0.0 );","       float specularNormalization = ( shininess + 2.0 ) / 8.0;","       vec3 schlick = specular + vec3( 1.0 - specular ) * pow( max( 1.0 - dot( spotVector, spotHalfVector ), 0.0 ), 5.0 );","       spotSpecular += schlick * spotLightColor[ i ] * spotSpecularWeight * spotDiffuseWeight * spotDistance * specularNormalization * spotEffect;","     }","   }"," #endif"," #if MAX_DIR_LIGHTS > 0","   vec3 dirDiffuse = vec3( 0.0 );","   vec3 dirSpecular = vec3( 0.0 );","   for( int i = 0; i < MAX_DIR_LIGHTS; i++ ) {","     vec4 lDirection = viewMatrix * vec4( directionalLightDirection[ i ], 0.0 );","     vec3 dirVector = normalize( lDirection.xyz );","     #ifdef WRAP_AROUND","       float directionalLightWeightingFull = max( dot( normal, dirVector ), 0.0 );","       float directionalLightWeightingHalf = max( 0.5 * dot( normal, dirVector ) + 0.5, 0.0 );","       vec3 dirDiffuseWeight = mix( vec3( directionalLightWeightingFull ), vec3( directionalLightWeightingHalf ), wrapRGB );","     #else","       float dirDiffuseWeight = max( dot( normal, dirVector ), 0.0 );","     #endif","     dirDiffuse += directionalLightColor[ i ] * diffuse * dirDiffuseWeight;","     vec3 dirHalfVector = normalize( dirVector + viewPosition );","     float dirDotNormalHalf = max( dot( normal, dirHalfVector ), 0.0 );","     float dirSpecularWeight = specularTex.r * max( pow( dirDotNormalHalf, shininess ), 0.0 );","     float specularNormalization = ( shininess + 2.0 ) / 8.0;","     vec3 schlick = specular + vec3( 1.0 - specular ) * pow( max( 1.0 - dot( dirVector, dirHalfVector ), 0.0 ), 5.0 );","     dirSpecular += schlick * directionalLightColor[ i ] * dirSpecularWeight * dirDiffuseWeight * specularNormalization;","   }"," #endif"," #if MAX_HEMI_LIGHTS > 0","   vec3 hemiDiffuse = vec3( 0.0 );","   vec3 hemiSpecular = vec3( 0.0 );","   for( int i = 0; i < MAX_HEMI_LIGHTS; i ++ ) {","     vec4 lDirection = viewMatrix * vec4( hemisphereLightDirection[ i ], 0.0 );","     vec3 lVector = normalize( lDirection.xyz );","     float dotProduct = dot( normal, lVector );","     float hemiDiffuseWeight = 0.5 * dotProduct + 0.5;","     vec3 hemiColor = mix( hemisphereLightGroundColor[ i ], hemisphereLightSkyColor[ i ], hemiDiffuseWeight );","     hemiDiffuse += diffuse * hemiColor;","     vec3 hemiHalfVectorSky = normalize( lVector + viewPosition );","     float hemiDotNormalHalfSky = 0.5 * dot( normal, hemiHalfVectorSky ) + 0.5;","     float hemiSpecularWeightSky = specularTex.r * max( pow( max( hemiDotNormalHalfSky, 0.0 ), shininess ), 0.0 );","     vec3 lVectorGround = -lVector;","     vec3 hemiHalfVectorGround = normalize( lVectorGround + viewPosition );","     float hemiDotNormalHalfGround = 0.5 * dot( normal, hemiHalfVectorGround ) + 0.5;","     float hemiSpecularWeightGround = specularTex.r * max( pow( max( hemiDotNormalHalfGround, 0.0 ), shininess ), 0.0 );","     float dotProductGround = dot( normal, lVectorGround );","     float specularNormalization = ( shininess + 2.0 ) / 8.0;","     vec3 schlickSky = specular + vec3( 1.0 - specular ) * pow( max( 1.0 - dot( lVector, hemiHalfVectorSky ), 0.0 ), 5.0 );","     vec3 schlickGround = specular + vec3( 1.0 - specular ) * pow( max( 1.0 - dot( lVectorGround, hemiHalfVectorGround ), 0.0 ), 5.0 );","     hemiSpecular += hemiColor * specularNormalization * ( schlickSky * hemiSpecularWeightSky * max( dotProduct, 0.0 ) + schlickGround * hemiSpecularWeightGround * max( dotProductGround, 0.0 ) );","   }"," #endif"," vec3 totalDiffuse = vec3( 0.0 );"," vec3 totalSpecular = vec3( 0.0 );"," #if MAX_DIR_LIGHTS > 0","   totalDiffuse += dirDiffuse;","   totalSpecular += dirSpecular;"," #endif"," #if MAX_HEMI_LIGHTS > 0","   totalDiffuse += hemiDiffuse;","   totalSpecular += hemiSpecular;"," #endif"," #if MAX_POINT_LIGHTS > 0","   totalDiffuse += pointDiffuse;","   totalSpecular += pointSpecular;"," #endif"," #if MAX_SPOT_LIGHTS > 0","   totalDiffuse += spotDiffuse;","   totalSpecular += spotSpecular;"," #endif"," #ifdef METAL","   gl_FragColor.xyz = gl_FragColor.xyz * ( totalDiffuse + ambientLightColor * ambient + totalSpecular );"," #else","   gl_FragColor.xyz = gl_FragColor.xyz * ( totalDiffuse + ambientLightColor * ambient ) + totalSpecular;"," #endif"," if ( enableReflection ) {","   vec3 vReflect;","   vec3 cameraToVertex = normalize( vWorldPosition - cameraPosition );","   if ( useRefract ) {","     vReflect = refract( cameraToVertex, normal, refractionRatio );","   } else {","     vReflect = reflect( cameraToVertex, normal );","   }","   vec4 cubeColor = textureCube( tCube, vec3( -vReflect.x, vReflect.yz ) );","   #ifdef GAMMA_INPUT","     cubeColor.xyz *= cubeColor.xyz;","   #endif","   gl_FragColor.xyz = mix( gl_FragColor.xyz, cubeColor.xyz, specularTex.r * reflectivity );"," }",a.ShaderChunk.shadowmap_fragment,a.ShaderChunk.linear_to_gamma_fragment,a.ShaderChunk.fog_fragment,"}"].join("\n"),vertexShader:["attribute vec4 tangent;","uniform vec2 uOffset;","uniform vec2 uRepeat;","uniform bool enableDisplacement;","#ifdef VERTEX_TEXTURES"," uniform sampler2D tDisplacement;"," uniform float uDisplacementScale;"," uniform float uDisplacementBias;","#endif","varying vec3 vTangent;","varying vec3 vBinormal;","varying vec3 vNormal;","varying vec2 vUv;","varying vec3 vWorldPosition;","varying vec3 vViewPosition;",a.ShaderChunk.skinning_pars_vertex,a.ShaderChunk.shadowmap_pars_vertex,a.ShaderChunk.logdepthbuf_pars_vertex,"void main() {",a.ShaderChunk.skinbase_vertex,a.ShaderChunk.skinnormal_vertex," #ifdef USE_SKINNING","   vNormal = normalize( normalMatrix * skinnedNormal.xyz );","   vec4 skinnedTangent = skinMatrix * vec4( tangent.xyz, 0.0 );","   vTangent = normalize( normalMatrix * skinnedTangent.xyz );"," #else","   vNormal = normalize( normalMatrix * normal );","   vTangent = normalize( normalMatrix * tangent.xyz );"," #endif"," vBinormal = normalize( cross( vNormal, vTangent ) * tangent.w );"," vUv = uv * uRepeat + uOffset;"," vec3 displacedPosition;"," #ifdef VERTEX_TEXTURES","   if ( enableDisplacement ) {","     vec3 dv = texture2D( tDisplacement, uv ).xyz;","     float df = uDisplacementScale * dv.x + uDisplacementBias;","     displacedPosition = position + normalize( normal ) * df;","   } else {","     #ifdef USE_SKINNING","       vec4 skinVertex = bindMatrix * vec4( position, 1.0 );","       vec4 skinned = vec4( 0.0 );","       skinned += boneMatX * skinVertex * skinWeight.x;","       skinned += boneMatY * skinVertex * skinWeight.y;","       skinned += boneMatZ * skinVertex * skinWeight.z;","       skinned += boneMatW * skinVertex * skinWeight.w;","       skinned  = bindMatrixInverse * skinned;","       displacedPosition = skinned.xyz;","     #else","       displacedPosition = position;","     #endif","   }"," #else","   #ifdef USE_SKINNING","     vec4 skinVertex = bindMatrix * vec4( position, 1.0 );","     vec4 skinned = vec4( 0.0 );","     skinned += boneMatX * skinVertex * skinWeight.x;","     skinned += boneMatY * skinVertex * skinWeight.y;","     skinned += boneMatZ * skinVertex * skinWeight.z;","     skinned += boneMatW * skinVertex * skinWeight.w;","     skinned  = bindMatrixInverse * skinned;","     displacedPosition = skinned.xyz;","   #else","     displacedPosition = position;","   #endif"," #endif"," vec4 mvPosition = modelViewMatrix * vec4( displacedPosition, 1.0 );"," vec4 worldPosition = modelMatrix * vec4( displacedPosition, 1.0 );"," gl_Position = projectionMatrix * mvPosition;",a.ShaderChunk.logdepthbuf_vertex," vWorldPosition = worldPosition.xyz;"," vViewPosition = -mvPosition.xyz;"," #ifdef USE_SHADOWMAP","   for( int i = 0; i < MAX_SHADOWS; i ++ ) {","     vShadowCoord[ i ] = shadowMatrix[ i ] * worldPosition;","   }"," #endif","}"].join("\n")},cube:{uniforms:{tCube:{type:"t",value:null},tFlip:{type:"f",value:-1}},vertexShader:["varying vec3 vWorldPosition;",a.ShaderChunk.logdepthbuf_pars_vertex,"void main() {"," vec4 worldPosition = modelMatrix * vec4( position, 1.0 );"," vWorldPosition = worldPosition.xyz;"," gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );",a.ShaderChunk.logdepthbuf_vertex,"}"].join("\n"),fragmentShader:["uniform samplerCube tCube;","uniform float tFlip;","varying vec3 vWorldPosition;",a.ShaderChunk.logdepthbuf_pars_fragment,"void main() {"," gl_FragColor = textureCube( tCube, vec3( tFlip * vWorldPosition.x, vWorldPosition.yz ) );",a.ShaderChunk.logdepthbuf_fragment,"}"].join("\n")},depthRGBA:{uniforms:{},vertexShader:[a.ShaderChunk.morphtarget_pars_vertex,a.ShaderChunk.skinning_pars_vertex,a.ShaderChunk.logdepthbuf_pars_vertex,"void main() {",a.ShaderChunk.skinbase_vertex,a.ShaderChunk.morphtarget_vertex,a.ShaderChunk.skinning_vertex,a.ShaderChunk.default_vertex,a.ShaderChunk.logdepthbuf_vertex,"}"].join("\n"),fragmentShader:[a.ShaderChunk.logdepthbuf_pars_fragment,"vec4 pack_depth( const in float depth ) {"," const vec4 bit_shift = vec4( 256.0 * 256.0 * 256.0, 256.0 * 256.0, 256.0, 1.0 );"," const vec4 bit_mask = vec4( 0.0, 1.0 / 256.0, 1.0 / 256.0, 1.0 / 256.0 );"," vec4 res = mod( depth * bit_shift * vec4( 255 ), vec4( 256 ) ) / vec4( 255 );"," res -= res.xxyz * bit_mask;"," return res;","}","void main() {",a.ShaderChunk.logdepthbuf_fragment," #ifdef USE_LOGDEPTHBUF_EXT","   gl_FragData[ 0 ] = pack_depth( gl_FragDepthEXT );"," #else","   gl_FragData[ 0 ] = pack_depth( gl_FragCoord.z );"," #endif","}"].join("\n")}},a.WebGLRenderer=function(b){function c(){La.clearColor(0,0,0,1),La.clearDepth(1),La.clearStencil(0),La.enable(La.DEPTH_TEST),La.depthFunc(La.LEQUAL),La.frontFace(La.CCW),La.cullFace(La.BACK),La.enable(La.CULL_FACE),La.enable(La.BLEND),La.blendEquation(La.FUNC_ADD),La.blendFunc(La.SRC_ALPHA,La.ONE_MINUS_SRC_ALPHA),La.viewport(eb,fb,gb,hb),La.clearColor(Ca.r,Ca.g,Ca.b,Da)}function d(a){
a.__webglVertexBuffer=La.createBuffer(),a.__webglColorBuffer=La.createBuffer(),Ma.info.memory.geometries++}function e(a){a.__webglVertexBuffer=La.createBuffer(),a.__webglColorBuffer=La.createBuffer(),a.__webglLineDistanceBuffer=La.createBuffer(),Ma.info.memory.geometries++}function f(a){a.__webglVertexBuffer=La.createBuffer(),a.__webglNormalBuffer=La.createBuffer(),a.__webglTangentBuffer=La.createBuffer(),a.__webglColorBuffer=La.createBuffer(),a.__webglUVBuffer=La.createBuffer(),a.__webglUV2Buffer=La.createBuffer(),a.__webglSkinIndicesBuffer=La.createBuffer(),a.__webglSkinWeightsBuffer=La.createBuffer(),a.__webglFaceBuffer=La.createBuffer(),a.__webglLineBuffer=La.createBuffer();var b,c;if(a.numMorphTargets)for(a.__webglMorphTargetsBuffers=[],b=0,c=a.numMorphTargets;c>b;b++)a.__webglMorphTargetsBuffers.push(La.createBuffer());if(a.numMorphNormals)for(a.__webglMorphNormalsBuffers=[],b=0,c=a.numMorphNormals;c>b;b++)a.__webglMorphNormalsBuffers.push(La.createBuffer());Ma.info.memory.geometries++}function g(a){var b=a.geometry,c=a.material,d=b.vertices.length;if(c.attributes){void 0===b.__webglCustomAttributesList&&(b.__webglCustomAttributesList=[]);for(var e in c.attributes){var f=c.attributes[e];if(!f.__webglInitialized||f.createUniqueBuffers){f.__webglInitialized=!0;var g=1;"v2"===f.type?g=2:"v3"===f.type?g=3:"v4"===f.type?g=4:"c"===f.type&&(g=3),f.size=g,f.array=new Float32Array(d*g),f.buffer=La.createBuffer(),f.buffer.belongsToAttribute=e,f.needsUpdate=!0}b.__webglCustomAttributesList.push(f)}}}function h(a,b){var c=a.vertices.length;a.__vertexArray=new Float32Array(3*c),a.__colorArray=new Float32Array(3*c),a.__sortArray=[],a.__webglParticleCount=c,g(b)}function i(a,b){var c=a.vertices.length;a.__vertexArray=new Float32Array(3*c),a.__colorArray=new Float32Array(3*c),a.__lineDistanceArray=new Float32Array(1*c),a.__webglLineCount=c,g(b)}function j(a,b){var c=b.geometry,d=a.faces3,e=3*d.length,f=1*d.length,g=3*d.length,h=k(b,a);a.__vertexArray=new Float32Array(3*e),a.__normalArray=new Float32Array(3*e),a.__colorArray=new Float32Array(3*e),a.__uvArray=new Float32Array(2*e),c.faceVertexUvs.length>1&&(a.__uv2Array=new Float32Array(2*e)),c.hasTangents&&(a.__tangentArray=new Float32Array(4*e)),b.geometry.skinWeights.length&&b.geometry.skinIndices.length&&(a.__skinIndexArray=new Float32Array(4*e),a.__skinWeightArray=new Float32Array(4*e));var i=null!==vb.get("OES_element_index_uint")&&f>21845?Uint32Array:Uint16Array;a.__typeArray=i,a.__faceArray=new i(3*f),a.__lineArray=new i(2*g);var j,l;if(a.numMorphTargets)for(a.__morphTargetsArrays=[],j=0,l=a.numMorphTargets;l>j;j++)a.__morphTargetsArrays.push(new Float32Array(3*e));if(a.numMorphNormals)for(a.__morphNormalsArrays=[],j=0,l=a.numMorphNormals;l>j;j++)a.__morphNormalsArrays.push(new Float32Array(3*e));if(a.__webglFaceCount=3*f,a.__webglLineCount=2*g,h.attributes){void 0===a.__webglCustomAttributesList&&(a.__webglCustomAttributesList=[]);for(var m in h.attributes){var n=h.attributes[m],o={};for(var p in n)o[p]=n[p];if(!o.__webglInitialized||o.createUniqueBuffers){o.__webglInitialized=!0;var q=1;"v2"===o.type?q=2:"v3"===o.type?q=3:"v4"===o.type?q=4:"c"===o.type&&(q=3),o.size=q,o.array=new Float32Array(e*q),o.buffer=La.createBuffer(),o.buffer.belongsToAttribute=m,n.needsUpdate=!0,o.__original=n}a.__webglCustomAttributesList.push(o)}}a.__inittedArrays=!0}function k(b,c){return b.material instanceof a.MeshFaceMaterial?b.material.materials[c.materialIndex]:b.material}function l(b){return b&&void 0!==b.shading&&b.shading===a.SmoothShading}function m(a,b,c){var d,e,f,g,h,i,j,k,l,m,n,o,p=a.vertices,q=p.length,r=a.colors,s=r.length,t=a.__vertexArray,u=a.__colorArray,v=a.__sortArray,w=a.verticesNeedUpdate,y=(a.elementsNeedUpdate,a.colorsNeedUpdate),z=a.__webglCustomAttributesList;if(c.sortParticles){for(ob.copy(nb),ob.multiply(c.matrixWorld),d=0;q>d;d++)f=p[d],pb.copy(f),pb.applyProjection(ob),v[d]=[pb.z,d];for(v.sort(x),d=0;q>d;d++)f=p[v[d][1]],g=3*d,t[g]=f.x,t[g+1]=f.y,t[g+2]=f.z;for(e=0;s>e;e++)g=3*e,i=r[v[e][1]],u[g]=i.r,u[g+1]=i.g,u[g+2]=i.b;if(z)for(j=0,k=z.length;k>j;j++)if(o=z[j],void 0===o.boundTo||"vertices"===o.boundTo)if(g=0,m=o.value.length,1===o.size)for(l=0;m>l;l++)h=v[l][1],o.array[l]=o.value[h];else if(2===o.size)for(l=0;m>l;l++)h=v[l][1],n=o.value[h],o.array[g]=n.x,o.array[g+1]=n.y,g+=2;else if(3===o.size)if("c"===o.type)for(l=0;m>l;l++)h=v[l][1],n=o.value[h],o.array[g]=n.r,o.array[g+1]=n.g,o.array[g+2]=n.b,g+=3;else for(l=0;m>l;l++)h=v[l][1],n=o.value[h],o.array[g]=n.x,o.array[g+1]=n.y,o.array[g+2]=n.z,g+=3;else if(4===o.size)for(l=0;m>l;l++)h=v[l][1],n=o.value[h],o.array[g]=n.x,o.array[g+1]=n.y,o.array[g+2]=n.z,o.array[g+3]=n.w,g+=4}else{if(w)for(d=0;q>d;d++)f=p[d],g=3*d,t[g]=f.x,t[g+1]=f.y,t[g+2]=f.z;if(y)for(e=0;s>e;e++)i=r[e],g=3*e,u[g]=i.r,u[g+1]=i.g,u[g+2]=i.b;if(z)for(j=0,k=z.length;k>j;j++)if(o=z[j],o.needsUpdate&&(void 0===o.boundTo||"vertices"===o.boundTo))if(m=o.value.length,g=0,1===o.size)for(l=0;m>l;l++)o.array[l]=o.value[l];else if(2===o.size)for(l=0;m>l;l++)n=o.value[l],o.array[g]=n.x,o.array[g+1]=n.y,g+=2;else if(3===o.size)if("c"===o.type)for(l=0;m>l;l++)n=o.value[l],o.array[g]=n.r,o.array[g+1]=n.g,o.array[g+2]=n.b,g+=3;else for(l=0;m>l;l++)n=o.value[l],o.array[g]=n.x,o.array[g+1]=n.y,o.array[g+2]=n.z,g+=3;else if(4===o.size)for(l=0;m>l;l++)n=o.value[l],o.array[g]=n.x,o.array[g+1]=n.y,o.array[g+2]=n.z,o.array[g+3]=n.w,g+=4}if((w||c.sortParticles)&&(La.bindBuffer(La.ARRAY_BUFFER,a.__webglVertexBuffer),La.bufferData(La.ARRAY_BUFFER,t,b)),(y||c.sortParticles)&&(La.bindBuffer(La.ARRAY_BUFFER,a.__webglColorBuffer),La.bufferData(La.ARRAY_BUFFER,u,b)),z)for(j=0,k=z.length;k>j;j++)o=z[j],(o.needsUpdate||c.sortParticles)&&(La.bindBuffer(La.ARRAY_BUFFER,o.buffer),La.bufferData(La.ARRAY_BUFFER,o.array,b))}function n(a,b){var c,d,e,f,g,h,i,j,k,l,m,n,o=a.vertices,p=a.colors,q=a.lineDistances,r=o.length,s=p.length,t=q.length,u=a.__vertexArray,v=a.__colorArray,w=a.__lineDistanceArray,x=a.verticesNeedUpdate,y=a.colorsNeedUpdate,z=a.lineDistancesNeedUpdate,A=a.__webglCustomAttributesList;if(x){for(c=0;r>c;c++)f=o[c],g=3*c,u[g]=f.x,u[g+1]=f.y,u[g+2]=f.z;La.bindBuffer(La.ARRAY_BUFFER,a.__webglVertexBuffer),La.bufferData(La.ARRAY_BUFFER,u,b)}if(y){for(d=0;s>d;d++)h=p[d],g=3*d,v[g]=h.r,v[g+1]=h.g,v[g+2]=h.b;La.bindBuffer(La.ARRAY_BUFFER,a.__webglColorBuffer),La.bufferData(La.ARRAY_BUFFER,v,b)}if(z){for(e=0;t>e;e++)w[e]=q[e];La.bindBuffer(La.ARRAY_BUFFER,a.__webglLineDistanceBuffer),La.bufferData(La.ARRAY_BUFFER,w,b)}if(A)for(i=0,j=A.length;j>i;i++)if(n=A[i],n.needsUpdate&&(void 0===n.boundTo||"vertices"===n.boundTo)){if(g=0,l=n.value.length,1===n.size)for(k=0;l>k;k++)n.array[k]=n.value[k];else if(2===n.size)for(k=0;l>k;k++)m=n.value[k],n.array[g]=m.x,n.array[g+1]=m.y,g+=2;else if(3===n.size)if("c"===n.type)for(k=0;l>k;k++)m=n.value[k],n.array[g]=m.r,n.array[g+1]=m.g,n.array[g+2]=m.b,g+=3;else for(k=0;l>k;k++)m=n.value[k],n.array[g]=m.x,n.array[g+1]=m.y,n.array[g+2]=m.z,g+=3;else if(4===n.size)for(k=0;l>k;k++)m=n.value[k],n.array[g]=m.x,n.array[g+1]=m.y,n.array[g+2]=m.z,n.array[g+3]=m.w,g+=4;La.bindBuffer(La.ARRAY_BUFFER,n.buffer),La.bufferData(La.ARRAY_BUFFER,n.array,b)}}function o(b,c,d,e,f){if(b.__inittedArrays){var g,h,i,j,k,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T,U,V,W,X=l(f),Y=0,Z=0,$=0,_=0,aa=0,ba=0,ca=0,da=0,ea=0,fa=0,ga=0,ha=0,ia=0,ja=b.__vertexArray,ka=b.__uvArray,la=b.__uv2Array,ma=b.__normalArray,na=b.__tangentArray,oa=b.__colorArray,pa=b.__skinIndexArray,qa=b.__skinWeightArray,ra=b.__morphTargetsArrays,sa=b.__morphNormalsArrays,ta=b.__webglCustomAttributesList,ua=b.__faceArray,va=b.__lineArray,wa=c.geometry,xa=wa.verticesNeedUpdate,ya=wa.elementsNeedUpdate,za=wa.uvsNeedUpdate,Aa=wa.normalsNeedUpdate,Ba=wa.tangentsNeedUpdate,Ca=wa.colorsNeedUpdate,Da=wa.morphTargetsNeedUpdate,Ea=wa.vertices,Fa=b.faces3,Ga=wa.faces,Ha=wa.faceVertexUvs[0],Ia=wa.faceVertexUvs[1],Ja=(wa.colors,wa.skinIndices),Ka=wa.skinWeights,Ma=wa.morphTargets,Na=wa.morphNormals;if(xa){for(g=0,h=Fa.length;h>g;g++)j=Ga[Fa[g]],s=Ea[j.a],t=Ea[j.b],u=Ea[j.c],ja[Z]=s.x,ja[Z+1]=s.y,ja[Z+2]=s.z,ja[Z+3]=t.x,ja[Z+4]=t.y,ja[Z+5]=t.z,ja[Z+6]=u.x,ja[Z+7]=u.y,ja[Z+8]=u.z,Z+=9;La.bindBuffer(La.ARRAY_BUFFER,b.__webglVertexBuffer),La.bufferData(La.ARRAY_BUFFER,ja,d)}if(Da)for(P=0,Q=Ma.length;Q>P;P++){for(ga=0,g=0,h=Fa.length;h>g;g++)T=Fa[g],j=Ga[T],s=Ma[P].vertices[j.a],t=Ma[P].vertices[j.b],u=Ma[P].vertices[j.c],R=ra[P],R[ga]=s.x,R[ga+1]=s.y,R[ga+2]=s.z,R[ga+3]=t.x,R[ga+4]=t.y,R[ga+5]=t.z,R[ga+6]=u.x,R[ga+7]=u.y,R[ga+8]=u.z,f.morphNormals&&(X?(U=Na[P].vertexNormals[T],y=U.a,z=U.b,A=U.c):(y=Na[P].faceNormals[T],z=y,A=y),S=sa[P],S[ga]=y.x,S[ga+1]=y.y,S[ga+2]=y.z,S[ga+3]=z.x,S[ga+4]=z.y,S[ga+5]=z.z,S[ga+6]=A.x,S[ga+7]=A.y,S[ga+8]=A.z),ga+=9;La.bindBuffer(La.ARRAY_BUFFER,b.__webglMorphTargetsBuffers[P]),La.bufferData(La.ARRAY_BUFFER,ra[P],d),f.morphNormals&&(La.bindBuffer(La.ARRAY_BUFFER,b.__webglMorphNormalsBuffers[P]),La.bufferData(La.ARRAY_BUFFER,sa[P],d))}if(Ka.length){for(g=0,h=Fa.length;h>g;g++)j=Ga[Fa[g]],E=Ka[j.a],F=Ka[j.b],G=Ka[j.c],qa[fa]=E.x,qa[fa+1]=E.y,qa[fa+2]=E.z,qa[fa+3]=E.w,qa[fa+4]=F.x,qa[fa+5]=F.y,qa[fa+6]=F.z,qa[fa+7]=F.w,qa[fa+8]=G.x,qa[fa+9]=G.y,qa[fa+10]=G.z,qa[fa+11]=G.w,H=Ja[j.a],I=Ja[j.b],J=Ja[j.c],pa[fa]=H.x,pa[fa+1]=H.y,pa[fa+2]=H.z,pa[fa+3]=H.w,pa[fa+4]=I.x,pa[fa+5]=I.y,pa[fa+6]=I.z,pa[fa+7]=I.w,pa[fa+8]=J.x,pa[fa+9]=J.y,pa[fa+10]=J.z,pa[fa+11]=J.w,fa+=12;fa>0&&(La.bindBuffer(La.ARRAY_BUFFER,b.__webglSkinIndicesBuffer),La.bufferData(La.ARRAY_BUFFER,pa,d),La.bindBuffer(La.ARRAY_BUFFER,b.__webglSkinWeightsBuffer),La.bufferData(La.ARRAY_BUFFER,qa,d))}if(Ca){for(g=0,h=Fa.length;h>g;g++)j=Ga[Fa[g]],n=j.vertexColors,o=j.color,3===n.length&&f.vertexColors===a.VertexColors?(B=n[0],C=n[1],D=n[2]):(B=o,C=o,D=o),oa[ea]=B.r,oa[ea+1]=B.g,oa[ea+2]=B.b,oa[ea+3]=C.r,oa[ea+4]=C.g,oa[ea+5]=C.b,oa[ea+6]=D.r,oa[ea+7]=D.g,oa[ea+8]=D.b,ea+=9;ea>0&&(La.bindBuffer(La.ARRAY_BUFFER,b.__webglColorBuffer),La.bufferData(La.ARRAY_BUFFER,oa,d))}if(Ba&&wa.hasTangents){for(g=0,h=Fa.length;h>g;g++)j=Ga[Fa[g]],p=j.vertexTangents,v=p[0],w=p[1],x=p[2],na[ca]=v.x,na[ca+1]=v.y,na[ca+2]=v.z,na[ca+3]=v.w,na[ca+4]=w.x,na[ca+5]=w.y,na[ca+6]=w.z,na[ca+7]=w.w,na[ca+8]=x.x,na[ca+9]=x.y,na[ca+10]=x.z,na[ca+11]=x.w,ca+=12;La.bindBuffer(La.ARRAY_BUFFER,b.__webglTangentBuffer),La.bufferData(La.ARRAY_BUFFER,na,d)}if(Aa){for(g=0,h=Fa.length;h>g;g++)if(j=Ga[Fa[g]],k=j.vertexNormals,m=j.normal,3===k.length&&X)for(K=0;3>K;K++)M=k[K],ma[ba]=M.x,ma[ba+1]=M.y,ma[ba+2]=M.z,ba+=3;else for(K=0;3>K;K++)ma[ba]=m.x,ma[ba+1]=m.y,ma[ba+2]=m.z,ba+=3;La.bindBuffer(La.ARRAY_BUFFER,b.__webglNormalBuffer),La.bufferData(La.ARRAY_BUFFER,ma,d)}if(za&&Ha){for(g=0,h=Fa.length;h>g;g++)if(i=Fa[g],q=Ha[i],void 0!==q)for(K=0;3>K;K++)N=q[K],ka[$]=N.x,ka[$+1]=N.y,$+=2;$>0&&(La.bindBuffer(La.ARRAY_BUFFER,b.__webglUVBuffer),La.bufferData(La.ARRAY_BUFFER,ka,d))}if(za&&Ia){for(g=0,h=Fa.length;h>g;g++)if(i=Fa[g],r=Ia[i],void 0!==r)for(K=0;3>K;K++)O=r[K],la[_]=O.x,la[_+1]=O.y,_+=2;_>0&&(La.bindBuffer(La.ARRAY_BUFFER,b.__webglUV2Buffer),La.bufferData(La.ARRAY_BUFFER,la,d))}if(ya){for(g=0,h=Fa.length;h>g;g++)ua[aa]=Y,ua[aa+1]=Y+1,ua[aa+2]=Y+2,aa+=3,va[da]=Y,va[da+1]=Y+1,va[da+2]=Y,va[da+3]=Y+2,va[da+4]=Y+1,va[da+5]=Y+2,da+=6,Y+=3;La.bindBuffer(La.ELEMENT_ARRAY_BUFFER,b.__webglFaceBuffer),La.bufferData(La.ELEMENT_ARRAY_BUFFER,ua,d),La.bindBuffer(La.ELEMENT_ARRAY_BUFFER,b.__webglLineBuffer),La.bufferData(La.ELEMENT_ARRAY_BUFFER,va,d)}if(ta)for(K=0,L=ta.length;L>K;K++)if(W=ta[K],W.__original.needsUpdate){if(ha=0,ia=0,1===W.size){if(void 0===W.boundTo||"vertices"===W.boundTo)for(g=0,h=Fa.length;h>g;g++)j=Ga[Fa[g]],W.array[ha]=W.value[j.a],W.array[ha+1]=W.value[j.b],W.array[ha+2]=W.value[j.c],ha+=3;else if("faces"===W.boundTo)for(g=0,h=Fa.length;h>g;g++)V=W.value[Fa[g]],W.array[ha]=V,W.array[ha+1]=V,W.array[ha+2]=V,ha+=3}else if(2===W.size){if(void 0===W.boundTo||"vertices"===W.boundTo)for(g=0,h=Fa.length;h>g;g++)j=Ga[Fa[g]],s=W.value[j.a],t=W.value[j.b],u=W.value[j.c],W.array[ha]=s.x,W.array[ha+1]=s.y,W.array[ha+2]=t.x,W.array[ha+3]=t.y,W.array[ha+4]=u.x,W.array[ha+5]=u.y,ha+=6;else if("faces"===W.boundTo)for(g=0,h=Fa.length;h>g;g++)V=W.value[Fa[g]],s=V,t=V,u=V,W.array[ha]=s.x,W.array[ha+1]=s.y,W.array[ha+2]=t.x,W.array[ha+3]=t.y,W.array[ha+4]=u.x,W.array[ha+5]=u.y,ha+=6}else if(3===W.size){var Oa;if(Oa="c"===W.type?["r","g","b"]:["x","y","z"],void 0===W.boundTo||"vertices"===W.boundTo)for(g=0,h=Fa.length;h>g;g++)j=Ga[Fa[g]],s=W.value[j.a],t=W.value[j.b],u=W.value[j.c],W.array[ha]=s[Oa[0]],W.array[ha+1]=s[Oa[1]],W.array[ha+2]=s[Oa[2]],W.array[ha+3]=t[Oa[0]],W.array[ha+4]=t[Oa[1]],W.array[ha+5]=t[Oa[2]],W.array[ha+6]=u[Oa[0]],W.array[ha+7]=u[Oa[1]],W.array[ha+8]=u[Oa[2]],ha+=9;else if("faces"===W.boundTo)for(g=0,h=Fa.length;h>g;g++)V=W.value[Fa[g]],s=V,t=V,u=V,W.array[ha]=s[Oa[0]],W.array[ha+1]=s[Oa[1]],W.array[ha+2]=s[Oa[2]],W.array[ha+3]=t[Oa[0]],W.array[ha+4]=t[Oa[1]],W.array[ha+5]=t[Oa[2]],W.array[ha+6]=u[Oa[0]],W.array[ha+7]=u[Oa[1]],W.array[ha+8]=u[Oa[2]],ha+=9;else if("faceVertices"===W.boundTo)for(g=0,h=Fa.length;h>g;g++)V=W.value[Fa[g]],s=V[0],t=V[1],u=V[2],W.array[ha]=s[Oa[0]],W.array[ha+1]=s[Oa[1]],W.array[ha+2]=s[Oa[2]],W.array[ha+3]=t[Oa[0]],W.array[ha+4]=t[Oa[1]],W.array[ha+5]=t[Oa[2]],W.array[ha+6]=u[Oa[0]],W.array[ha+7]=u[Oa[1]],W.array[ha+8]=u[Oa[2]],ha+=9}else if(4===W.size)if(void 0===W.boundTo||"vertices"===W.boundTo)for(g=0,h=Fa.length;h>g;g++)j=Ga[Fa[g]],s=W.value[j.a],t=W.value[j.b],u=W.value[j.c],W.array[ha]=s.x,W.array[ha+1]=s.y,W.array[ha+2]=s.z,W.array[ha+3]=s.w,W.array[ha+4]=t.x,W.array[ha+5]=t.y,W.array[ha+6]=t.z,W.array[ha+7]=t.w,W.array[ha+8]=u.x,W.array[ha+9]=u.y,W.array[ha+10]=u.z,W.array[ha+11]=u.w,ha+=12;else if("faces"===W.boundTo)for(g=0,h=Fa.length;h>g;g++)V=W.value[Fa[g]],s=V,t=V,u=V,W.array[ha]=s.x,W.array[ha+1]=s.y,W.array[ha+2]=s.z,W.array[ha+3]=s.w,W.array[ha+4]=t.x,W.array[ha+5]=t.y,W.array[ha+6]=t.z,W.array[ha+7]=t.w,W.array[ha+8]=u.x,W.array[ha+9]=u.y,W.array[ha+10]=u.z,W.array[ha+11]=u.w,ha+=12;else if("faceVertices"===W.boundTo)for(g=0,h=Fa.length;h>g;g++)V=W.value[Fa[g]],s=V[0],t=V[1],u=V[2],W.array[ha]=s.x,W.array[ha+1]=s.y,W.array[ha+2]=s.z,W.array[ha+3]=s.w,W.array[ha+4]=t.x,W.array[ha+5]=t.y,W.array[ha+6]=t.z,W.array[ha+7]=t.w,W.array[ha+8]=u.x,W.array[ha+9]=u.y,W.array[ha+10]=u.z,W.array[ha+11]=u.w,ha+=12;La.bindBuffer(La.ARRAY_BUFFER,W.buffer),La.bufferData(La.ARRAY_BUFFER,W.array,d)}e&&(delete b.__inittedArrays,delete b.__colorArray,delete b.__normalArray,delete b.__tangentArray,delete b.__uvArray,delete b.__uv2Array,delete b.__faceArray,delete b.__vertexArray,delete b.__lineArray,delete b.__skinIndexArray,delete b.__skinWeightArray)}}function p(a){for(var b=a.attributes,c=a.attributesKeys,d=0,e=c.length;e>d;d++){var f=c[d],g=b[f];if(void 0===g.buffer&&(g.buffer=La.createBuffer(),g.needsUpdate=!0),g.needsUpdate===!0){var h="index"===f?La.ELEMENT_ARRAY_BUFFER:La.ARRAY_BUFFER;La.bindBuffer(h,g.buffer),La.bufferData(h,g.array,La.STATIC_DRAW),g.needsUpdate=!1}}}function q(a,b,c,d){for(var e=c.attributes,f=b.attributes,g=b.attributesKeys,h=0,i=g.length;i>h;h++){var j=g[h],k=f[j];if(k>=0){var l=e[j];if(void 0!==l){var m=l.itemSize;La.bindBuffer(La.ARRAY_BUFFER,l.buffer),s(k),La.vertexAttribPointer(k,m,La.FLOAT,!1,0,d*m*4)}else void 0!==a.defaultAttributeValues&&(2===a.defaultAttributeValues[j].length?La.vertexAttrib2fv(k,a.defaultAttributeValues[j]):3===a.defaultAttributeValues[j].length&&La.vertexAttrib3fv(k,a.defaultAttributeValues[j]))}}t()}function r(){for(var a=0,b=kb.length;b>a;a++)kb[a]=0}function s(a){kb[a]=1,0===lb[a]&&(La.enableVertexAttribArray(a),lb[a]=1)}function t(){for(var a=0,b=lb.length;b>a;a++)lb[a]!==kb[a]&&(La.disableVertexAttribArray(a),lb[a]=0)}function u(a,b,c){var d=a.program.attributes;if(-1!==c.morphTargetBase&&d.position>=0?(La.bindBuffer(La.ARRAY_BUFFER,b.__webglMorphTargetsBuffers[c.morphTargetBase]),s(d.position),La.vertexAttribPointer(d.position,3,La.FLOAT,!1,0,0)):d.position>=0&&(La.bindBuffer(La.ARRAY_BUFFER,b.__webglVertexBuffer),s(d.position),La.vertexAttribPointer(d.position,3,La.FLOAT,!1,0,0)),c.morphTargetForcedOrder.length)for(var e=0,f=c.morphTargetForcedOrder,g=c.morphTargetInfluences;e<a.numSupportedMorphTargets&&e<f.length;)d["morphTarget"+e]>=0&&(La.bindBuffer(La.ARRAY_BUFFER,b.__webglMorphTargetsBuffers[f[e]]),s(d["morphTarget"+e]),La.vertexAttribPointer(d["morphTarget"+e],3,La.FLOAT,!1,0,0)),d["morphNormal"+e]>=0&&a.morphNormals&&(La.bindBuffer(La.ARRAY_BUFFER,b.__webglMorphNormalsBuffers[f[e]]),s(d["morphNormal"+e]),La.vertexAttribPointer(d["morphNormal"+e],3,La.FLOAT,!1,0,0)),c.__webglMorphTargetInfluences[e]=g[f[e]],e++;else{var h,i,j=[],g=c.morphTargetInfluences,k=g.length;for(i=0;k>i;i++)h=g[i],h>0&&j.push([h,i]);j.length>a.numSupportedMorphTargets?(j.sort(x),j.length=a.numSupportedMorphTargets):j.length>a.numSupportedMorphNormals?j.sort(x):0===j.length&&j.push([0,0]);for(var l,e=0;e<a.numSupportedMorphTargets;)j[e]?(l=j[e][1],d["morphTarget"+e]>=0&&(La.bindBuffer(La.ARRAY_BUFFER,b.__webglMorphTargetsBuffers[l]),s(d["morphTarget"+e]),La.vertexAttribPointer(d["morphTarget"+e],3,La.FLOAT,!1,0,0)),d["morphNormal"+e]>=0&&a.morphNormals&&(La.bindBuffer(La.ARRAY_BUFFER,b.__webglMorphNormalsBuffers[l]),s(d["morphNormal"+e]),La.vertexAttribPointer(d["morphNormal"+e],3,La.FLOAT,!1,0,0)),c.__webglMorphTargetInfluences[e]=g[l]):c.__webglMorphTargetInfluences[e]=0,e++}null!==a.program.uniforms.morphTargetInfluences&&La.uniform1fv(a.program.uniforms.morphTargetInfluences,c.__webglMorphTargetInfluences)}function v(a,b){return a.material.id!==b.material.id?b.material.id-a.material.id:a.z!==b.z?b.z-a.z:a.id-b.id}function w(a,b){return a.z!==b.z?a.z-b.z:a.id-b.id}function x(a,b){return b[0]-a[0]}function y(b,c){if(c.visible!==!1){if(c instanceof a.Scene||c instanceof a.Group);else if(D(c,b),c instanceof a.Light)Ea.push(c);else if(c instanceof a.Sprite)Ja.push(c);else if(c instanceof a.LensFlare)Ka.push(c);else{var d=Fa[c.id];if(d&&(c.frustumCulled===!1||mb.intersectsObject(c)===!0)){I(c,b);for(var e=0,f=d.length;f>e;e++){var g=d[e];C(g),g.render=!0,Ma.sortObjects===!0&&(null!==c.renderDepth?g.z=c.renderDepth:(pb.setFromMatrixPosition(c.matrixWorld),pb.applyProjection(nb),g.z=pb.z))}}}for(var e=0,f=c.children.length;f>e;e++)y(b,c.children[e])}}function z(b,c,d,e,f,g){for(var h,i=b.length-1;-1!==i;i--){var j=b[i],k=j.object,l=j.buffer;if(aa(k,c),g)h=g;else{if(h=j.material,!h)continue;f&&Ma.setBlending(h.blending,h.blendEquation,h.blendSrc,h.blendDst),Ma.setDepthTest(h.depthTest),Ma.setDepthWrite(h.depthWrite),fa(h.polygonOffset,h.polygonOffsetFactor,h.polygonOffsetUnits)}Ma.setMaterialFaces(h),l instanceof a.BufferGeometry?Ma.renderBufferDirect(c,d,e,h,l,k):Ma.renderBuffer(c,d,e,h,l,k)}}function A(a,b,c,d,e,f,g){for(var h,i=0,j=a.length;j>i;i++){var k=a[i],l=k.object;if(l.visible){if(g)h=g;else{if(h=k[b],!h)continue;f&&Ma.setBlending(h.blending,h.blendEquation,h.blendSrc,h.blendDst),Ma.setDepthTest(h.depthTest),Ma.setDepthWrite(h.depthWrite),fa(h.polygonOffset,h.polygonOffsetFactor,h.polygonOffsetUnits)}Ma.renderImmediateObject(c,d,e,h,l)}}}function B(a){var b=a.object,c=b.material;c.transparent?(a.transparent=c,a.opaque=null):(a.opaque=c,a.transparent=null)}function C(b){var c=b.object,d=b.buffer,e=c.geometry,f=c.material;if(f instanceof a.MeshFaceMaterial){var g=e instanceof a.BufferGeometry?0:d.materialIndex;f=f.materials[g],b.material=f,f.transparent?Ia.push(b):Ha.push(b)}else f&&(b.material=f,f.transparent?Ia.push(b):Ha.push(b))}function D(b,c){void 0===b.__webglInit&&(b.__webglInit=!0,b._modelViewMatrix=new a.Matrix4,b._normalMatrix=new a.Matrix3,b.addEventListener("removed",Mb));var f=b.geometry;if(void 0===f||void 0===f.__webglInit&&(f.__webglInit=!0,f.addEventListener("dispose",Nb),f instanceof a.BufferGeometry||(b instanceof a.Mesh?F(c,b,f):b instanceof a.Line?void 0===f.__webglVertexBuffer&&(e(f),i(f,b),f.verticesNeedUpdate=!0,f.colorsNeedUpdate=!0,f.lineDistancesNeedUpdate=!0):b instanceof a.PointCloud&&void 0===f.__webglVertexBuffer&&(d(f),h(f,b),f.verticesNeedUpdate=!0,f.colorsNeedUpdate=!0))),void 0===b.__webglActive)if(b.__webglActive=!0,b instanceof a.Mesh){if(f instanceof a.BufferGeometry)G(Fa,f,b);else if(f instanceof a.Geometry)for(var g=Wb[f.id],j=0,k=g.length;k>j;j++)G(Fa,g[j],b)}else b instanceof a.Line||b instanceof a.PointCloud?G(Fa,f,b):(b instanceof a.ImmediateRenderObject||b.immediateRenderCallback)&&H(Ga,b)}function E(a,b){for(var c,d,e=vb.get("OES_element_index_uint")?4294967296:65535,f={},g=a.morphTargets.length,h=a.morphNormals.length,i={},j=[],k=0,l=a.faces.length;l>k;k++){var m=a.faces[k],n=b?m.materialIndex:0;n in f||(f[n]={hash:n,counter:0}),c=f[n].hash+"_"+f[n].counter,c in i||(d={id:Xb++,faces3:[],materialIndex:n,vertices:0,numMorphTargets:g,numMorphNormals:h},i[c]=d,j.push(d)),i[c].vertices+3>e&&(f[n].counter+=1,c=f[n].hash+"_"+f[n].counter,c in i||(d={id:Xb++,faces3:[],materialIndex:n,vertices:0,numMorphTargets:g,numMorphNormals:h},i[c]=d,j.push(d))),i[c].faces3.push(k),i[c].vertices+=3}return j}function F(b,c,d){var e=c.material,g=!1;(void 0===Wb[d.id]||d.groupsNeedUpdate===!0)&&(delete Fa[c.id],Wb[d.id]=E(d,e instanceof a.MeshFaceMaterial),d.groupsNeedUpdate=!1);for(var h=Wb[d.id],i=0,k=h.length;k>i;i++){var l=h[i];void 0===l.__webglVertexBuffer?(f(l),j(l,c),d.verticesNeedUpdate=!0,d.morphTargetsNeedUpdate=!0,d.elementsNeedUpdate=!0,d.uvsNeedUpdate=!0,d.normalsNeedUpdate=!0,d.tangentsNeedUpdate=!0,d.colorsNeedUpdate=!0,g=!0):g=!1,(g||void 0===c.__webglActive)&&G(Fa,l,c)}c.__webglActive=!0}function G(a,b,c){var d=c.id;a[d]=a[d]||[],a[d].push({id:d,buffer:b,object:c,material:null,z:0})}function H(a,b){a.push({id:null,object:b,opaque:null,transparent:null,z:0})}function I(b,c){var d,e,f=b.geometry;if(f instanceof a.BufferGeometry)p(f);else if(b instanceof a.Mesh){f.groupsNeedUpdate===!0&&F(c,b,f);for(var g=Wb[f.id],h=0,i=g.length;i>h;h++){var l=g[h];e=k(b,l),f.groupsNeedUpdate===!0&&j(l,b),d=e.attributes&&J(e),(f.verticesNeedUpdate||f.morphTargetsNeedUpdate||f.elementsNeedUpdate||f.uvsNeedUpdate||f.normalsNeedUpdate||f.colorsNeedUpdate||f.tangentsNeedUpdate||d)&&o(l,b,La.DYNAMIC_DRAW,!f.dynamic,e)}f.verticesNeedUpdate=!1,f.morphTargetsNeedUpdate=!1,f.elementsNeedUpdate=!1,f.uvsNeedUpdate=!1,f.normalsNeedUpdate=!1,f.colorsNeedUpdate=!1,f.tangentsNeedUpdate=!1,e.attributes&&K(e)}else b instanceof a.Line?(e=k(b,f),d=e.attributes&&J(e),(f.verticesNeedUpdate||f.colorsNeedUpdate||f.lineDistancesNeedUpdate||d)&&n(f,La.DYNAMIC_DRAW),f.verticesNeedUpdate=!1,f.colorsNeedUpdate=!1,f.lineDistancesNeedUpdate=!1,e.attributes&&K(e)):b instanceof a.PointCloud&&(e=k(b,f),d=e.attributes&&J(e),(f.verticesNeedUpdate||f.colorsNeedUpdate||b.sortParticles||d)&&m(f,La.DYNAMIC_DRAW,b),f.verticesNeedUpdate=!1,f.colorsNeedUpdate=!1,e.attributes&&K(e))}function J(a){for(var b in a.attributes)if(a.attributes[b].needsUpdate)return!0;return!1}function K(a){for(var b in a.attributes)a.attributes[b].needsUpdate=!1}function L(b){b instanceof a.Mesh||b instanceof a.PointCloud||b instanceof a.Line?delete Fa[b.id]:(b instanceof a.ImmediateRenderObject||b.immediateRenderCallback)&&M(Ga,b),delete b.__webglInit,delete b._modelViewMatrix,delete b._normalMatrix,delete b.__webglActive}function M(a,b){for(var c=a.length-1;c>=0;c--)a[c].object===b&&a.splice(c,1)}function N(b,c,d,e){b.addEventListener("dispose",Qb);var f;if(b instanceof a.MeshDepthMaterial?f="depth":b instanceof a.MeshNormalMaterial?f="normal":b instanceof a.MeshBasicMaterial?f="basic":b instanceof a.MeshLambertMaterial?f="lambert":b instanceof a.MeshPhongMaterial?f="phong":b instanceof a.LineBasicMaterial?f="basic":b instanceof a.LineDashedMaterial?f="dashed":b instanceof a.PointCloudMaterial&&(f="particle_basic"),f){var g=a.ShaderLib[f];b.__webglShader={uniforms:a.UniformsUtils.clone(g.uniforms),vertexShader:g.vertexShader,fragmentShader:g.fragmentShader}}else b.__webglShader={uniforms:b.uniforms,vertexShader:b.vertexShader,fragmentShader:b.fragmentShader};var h=qa(c),i=ra(c),j=pa(e),k={precision:ua,supportsVertexTextures:Ab,map:!!b.map,envMap:!!b.envMap,lightMap:!!b.lightMap,bumpMap:!!b.bumpMap,normalMap:!!b.normalMap,specularMap:!!b.specularMap,alphaMap:!!b.alphaMap,vertexColors:b.vertexColors,fog:d,useFog:b.fog,fogExp:d instanceof a.FogExp2,sizeAttenuation:b.sizeAttenuation,logarithmicDepthBuffer:Ba,skinning:b.skinning,maxBones:j,useVertexTexture:Bb&&e&&e.skeleton&&e.skeleton.useVertexTexture,morphTargets:b.morphTargets,morphNormals:b.morphNormals,maxMorphTargets:Ma.maxMorphTargets,maxMorphNormals:Ma.maxMorphNormals,maxDirLights:h.directional,maxPointLights:h.point,maxSpotLights:h.spot,maxHemiLights:h.hemi,maxShadows:i,shadowMapEnabled:Ma.shadowMapEnabled&&e.receiveShadow&&i>0,shadowMapType:Ma.shadowMapType,shadowMapDebug:Ma.shadowMapDebug,shadowMapCascade:Ma.shadowMapCascade,alphaTest:b.alphaTest,metal:b.metal,wrapAround:b.wrapAround,doubleSided:b.side===a.DoubleSide,flipSided:b.side===a.BackSide},l=[];if(f?l.push(f):(l.push(b.fragmentShader),l.push(b.vertexShader)),void 0!==b.defines)for(var m in b.defines)l.push(m),l.push(b.defines[m]);for(var m in k)l.push(m),l.push(k[m]);for(var n,o=l.join(),p=0,q=Na.length;q>p;p++){var r=Na[p];if(r.code===o){n=r,n.usedTimes++;break}}void 0===n&&(n=new a.WebGLProgram(Ma,o,b,k),Na.push(n),Ma.info.memory.programs=Na.length),b.program=n;var s=n.attributes;if(b.morphTargets){b.numSupportedMorphTargets=0;for(var t,u="morphTarget",v=0;v<Ma.maxMorphTargets;v++)t=u+v,s[t]>=0&&b.numSupportedMorphTargets++}if(b.morphNormals){b.numSupportedMorphNormals=0;var t,u="morphNormal";for(v=0;v<Ma.maxMorphNormals;v++)t=u+v,s[t]>=0&&b.numSupportedMorphNormals++}b.uniformsList=[];for(var w in b.__webglShader.uniforms){var x=b.program.uniforms[w];x&&b.uniformsList.push([b.__webglShader.uniforms[w],x])}}function O(b,c,d,e,f){Ta=0,e.needsUpdate&&(e.program&&Vb(e),N(e,c,d,f),e.needsUpdate=!1),e.morphTargets&&(f.__webglMorphTargetInfluences||(f.__webglMorphTargetInfluences=new Float32Array(Ma.maxMorphTargets)));var g=!1,h=!1,i=!1,j=e.program,k=j.uniforms,l=e.__webglShader.uniforms;if(j.id!==Oa&&(La.useProgram(j.program),Oa=j.id,g=!0,h=!0,i=!0),e.id!==Qa&&(-1===Qa&&(i=!0),Qa=e.id,h=!0),(g||b!==Sa)&&(La.uniformMatrix4fv(k.projectionMatrix,!1,b.projectionMatrix.elements),Ba&&La.uniform1f(k.logDepthBufFC,2/(Math.log(b.far+1)/Math.LN2)),b!==Sa&&(Sa=b),(e instanceof a.ShaderMaterial||e instanceof a.MeshPhongMaterial||e.envMap)&&null!==k.cameraPosition&&(pb.setFromMatrixPosition(b.matrixWorld),La.uniform3f(k.cameraPosition,pb.x,pb.y,pb.z)),(e instanceof a.MeshPhongMaterial||e instanceof a.MeshLambertMaterial||e instanceof a.ShaderMaterial||e.skinning)&&null!==k.viewMatrix&&La.uniformMatrix4fv(k.viewMatrix,!1,b.matrixWorldInverse.elements)),e.skinning)if(f.bindMatrix&&null!==k.bindMatrix&&La.uniformMatrix4fv(k.bindMatrix,!1,f.bindMatrix.elements),f.bindMatrixInverse&&null!==k.bindMatrixInverse&&La.uniformMatrix4fv(k.bindMatrixInverse,!1,f.bindMatrixInverse.elements),Bb&&f.skeleton&&f.skeleton.useVertexTexture){if(null!==k.boneTexture){var m=$();La.uniform1i(k.boneTexture,m),Ma.setTexture(f.skeleton.boneTexture,m)}null!==k.boneTextureWidth&&La.uniform1i(k.boneTextureWidth,f.skeleton.boneTextureWidth),null!==k.boneTextureHeight&&La.uniform1i(k.boneTextureHeight,f.skeleton.boneTextureHeight)}else f.skeleton&&f.skeleton.boneMatrices&&null!==k.boneGlobalMatrices&&La.uniformMatrix4fv(k.boneGlobalMatrices,!1,f.skeleton.boneMatrices);return h&&(d&&e.fog&&T(l,d),(e instanceof a.MeshPhongMaterial||e instanceof a.MeshLambertMaterial||e.lights)&&(rb&&(i=!0,da(c),rb=!1),i?(W(l,sb),X(l,!0)):X(l,!1)),(e instanceof a.MeshBasicMaterial||e instanceof a.MeshLambertMaterial||e instanceof a.MeshPhongMaterial)&&P(l,e),e instanceof a.LineBasicMaterial?Q(l,e):e instanceof a.LineDashedMaterial?(Q(l,e),R(l,e)):e instanceof a.PointCloudMaterial?S(l,e):e instanceof a.MeshPhongMaterial?U(l,e):e instanceof a.MeshLambertMaterial?V(l,e):e instanceof a.MeshDepthMaterial?(l.mNear.value=b.near,l.mFar.value=b.far,l.opacity.value=e.opacity):e instanceof a.MeshNormalMaterial&&(l.opacity.value=e.opacity),f.receiveShadow&&!e._shadowPass&&Y(l,c),_(e.uniformsList)),Z(k,f),null!==k.modelMatrix&&La.uniformMatrix4fv(k.modelMatrix,!1,f.matrixWorld.elements),j}function P(b,c){b.opacity.value=c.opacity,Ma.gammaInput?b.diffuse.value.copyGammaToLinear(c.color):b.diffuse.value=c.color,b.map.value=c.map,b.lightMap.value=c.lightMap,b.specularMap.value=c.specularMap,b.alphaMap.value=c.alphaMap,c.bumpMap&&(b.bumpMap.value=c.bumpMap,b.bumpScale.value=c.bumpScale),c.normalMap&&(b.normalMap.value=c.normalMap,b.normalScale.value.copy(c.normalScale));var d;if(c.map?d=c.map:c.specularMap?d=c.specularMap:c.normalMap?d=c.normalMap:c.bumpMap?d=c.bumpMap:c.alphaMap&&(d=c.alphaMap),void 0!==d){var e=d.offset,f=d.repeat;b.offsetRepeat.value.set(e.x,e.y,f.x,f.y)}b.envMap.value=c.envMap,b.flipEnvMap.value=c.envMap instanceof a.WebGLRenderTargetCube?1:-1,Ma.gammaInput?b.reflectivity.value=c.reflectivity:b.reflectivity.value=c.reflectivity,b.refractionRatio.value=c.refractionRatio,b.combine.value=c.combine,b.useRefract.value=c.envMap&&c.envMap.mapping instanceof a.CubeRefractionMapping}function Q(a,b){a.diffuse.value=b.color,a.opacity.value=b.opacity}function R(a,b){a.dashSize.value=b.dashSize,a.totalSize.value=b.dashSize+b.gapSize,a.scale.value=b.scale}function S(a,b){a.psColor.value=b.color,a.opacity.value=b.opacity,a.size.value=b.size,a.scale.value=sa.height/2,a.map.value=b.map}function T(b,c){b.fogColor.value=c.color,c instanceof a.Fog?(b.fogNear.value=c.near,b.fogFar.value=c.far):c instanceof a.FogExp2&&(b.fogDensity.value=c.density)}function U(a,b){a.shininess.value=b.shininess,Ma.gammaInput?(a.ambient.value.copyGammaToLinear(b.ambient),a.emissive.value.copyGammaToLinear(b.emissive),a.specular.value.copyGammaToLinear(b.specular)):(a.ambient.value=b.ambient,a.emissive.value=b.emissive,a.specular.value=b.specular),b.wrapAround&&a.wrapRGB.value.copy(b.wrapRGB)}function V(a,b){Ma.gammaInput?(a.ambient.value.copyGammaToLinear(b.ambient),a.emissive.value.copyGammaToLinear(b.emissive)):(a.ambient.value=b.ambient,a.emissive.value=b.emissive),b.wrapAround&&a.wrapRGB.value.copy(b.wrapRGB)}function W(a,b){a.ambientLightColor.value=b.ambient,a.directionalLightColor.value=b.directional.colors,a.directionalLightDirection.value=b.directional.positions,a.pointLightColor.value=b.point.colors,a.pointLightPosition.value=b.point.positions,a.pointLightDistance.value=b.point.distances,a.spotLightColor.value=b.spot.colors,a.spotLightPosition.value=b.spot.positions,a.spotLightDistance.value=b.spot.distances,a.spotLightDirection.value=b.spot.directions,a.spotLightAngleCos.value=b.spot.anglesCos,a.spotLightExponent.value=b.spot.exponents,a.hemisphereLightSkyColor.value=b.hemi.skyColors,a.hemisphereLightGroundColor.value=b.hemi.groundColors,a.hemisphereLightDirection.value=b.hemi.positions}function X(a,b){a.ambientLightColor.needsUpdate=b,a.directionalLightColor.needsUpdate=b,a.directionalLightDirection.needsUpdate=b,a.pointLightColor.needsUpdate=b,a.pointLightPosition.needsUpdate=b,a.pointLightDistance.needsUpdate=b,a.spotLightColor.needsUpdate=b,a.spotLightPosition.needsUpdate=b,a.spotLightDistance.needsUpdate=b,a.spotLightDirection.needsUpdate=b,a.spotLightAngleCos.needsUpdate=b,a.spotLightExponent.needsUpdate=b,a.hemisphereLightSkyColor.needsUpdate=b,a.hemisphereLightGroundColor.needsUpdate=b,a.hemisphereLightDirection.needsUpdate=b}function Y(b,c){if(b.shadowMatrix)for(var d=0,e=0,f=c.length;f>e;e++){var g=c[e];g.castShadow&&(g instanceof a.SpotLight||g instanceof a.DirectionalLight&&!g.shadowCascade)&&(b.shadowMap.value[d]=g.shadowMap,b.shadowMapSize.value[d]=g.shadowMapSize,b.shadowMatrix.value[d]=g.shadowMatrix,b.shadowDarkness.value[d]=g.shadowDarkness,b.shadowBias.value[d]=g.shadowBias,d++)}}function Z(a,b){La.uniformMatrix4fv(a.modelViewMatrix,!1,b._modelViewMatrix.elements),a.normalMatrix&&La.uniformMatrix3fv(a.normalMatrix,!1,b._normalMatrix.elements)}function $(){var a=Ta;return a>=wb&&console.warn("WebGLRenderer: trying to use "+a+" texture units while this GPU supports only "+wb),
Ta+=1,a}function _(b){for(var c,d,e,f=0,g=b.length;g>f;f++){var h=b[f][0];if(h.needsUpdate!==!1){var i=h.type,j=h.value,k=b[f][1];switch(i){case"1i":La.uniform1i(k,j);break;case"1f":La.uniform1f(k,j);break;case"2f":La.uniform2f(k,j[0],j[1]);break;case"3f":La.uniform3f(k,j[0],j[1],j[2]);break;case"4f":La.uniform4f(k,j[0],j[1],j[2],j[3]);break;case"1iv":La.uniform1iv(k,j);break;case"3iv":La.uniform3iv(k,j);break;case"1fv":La.uniform1fv(k,j);break;case"2fv":La.uniform2fv(k,j);break;case"3fv":La.uniform3fv(k,j);break;case"4fv":La.uniform4fv(k,j);break;case"Matrix3fv":La.uniformMatrix3fv(k,!1,j);break;case"Matrix4fv":La.uniformMatrix4fv(k,!1,j);break;case"i":La.uniform1i(k,j);break;case"f":La.uniform1f(k,j);break;case"v2":La.uniform2f(k,j.x,j.y);break;case"v3":La.uniform3f(k,j.x,j.y,j.z);break;case"v4":La.uniform4f(k,j.x,j.y,j.z,j.w);break;case"c":La.uniform3f(k,j.r,j.g,j.b);break;case"iv1":La.uniform1iv(k,j);break;case"iv":La.uniform3iv(k,j);break;case"fv1":La.uniform1fv(k,j);break;case"fv":La.uniform3fv(k,j);break;case"v2v":void 0===h._array&&(h._array=new Float32Array(2*j.length));for(var l=0,m=j.length;m>l;l++)e=2*l,h._array[e]=j[l].x,h._array[e+1]=j[l].y;La.uniform2fv(k,h._array);break;case"v3v":void 0===h._array&&(h._array=new Float32Array(3*j.length));for(var l=0,m=j.length;m>l;l++)e=3*l,h._array[e]=j[l].x,h._array[e+1]=j[l].y,h._array[e+2]=j[l].z;La.uniform3fv(k,h._array);break;case"v4v":void 0===h._array&&(h._array=new Float32Array(4*j.length));for(var l=0,m=j.length;m>l;l++)e=4*l,h._array[e]=j[l].x,h._array[e+1]=j[l].y,h._array[e+2]=j[l].z,h._array[e+3]=j[l].w;La.uniform4fv(k,h._array);break;case"m3":La.uniformMatrix3fv(k,!1,j.elements);break;case"m3v":void 0===h._array&&(h._array=new Float32Array(9*j.length));for(var l=0,m=j.length;m>l;l++)j[l].flattenToArrayOffset(h._array,9*l);La.uniformMatrix3fv(k,!1,h._array);break;case"m4":La.uniformMatrix4fv(k,!1,j.elements);break;case"m4v":void 0===h._array&&(h._array=new Float32Array(16*j.length));for(var l=0,m=j.length;m>l;l++)j[l].flattenToArrayOffset(h._array,16*l);La.uniformMatrix4fv(k,!1,h._array);break;case"t":if(c=j,d=$(),La.uniform1i(k,d),!c)continue;c instanceof a.CubeTexture||c.image instanceof Array&&6===c.image.length?ia(c,d):c instanceof a.WebGLRenderTargetCube?ja(c,d):Ma.setTexture(c,d);break;case"tv":void 0===h._array&&(h._array=[]);for(var l=0,m=h.value.length;m>l;l++)h._array[l]=$();La.uniform1iv(k,h._array);for(var l=0,m=h.value.length;m>l;l++)c=h.value[l],d=h._array[l],c&&Ma.setTexture(c,d);break;default:console.warn("THREE.WebGLRenderer: Unknown uniform type: "+i)}}}}function aa(a,b){a._modelViewMatrix.multiplyMatrices(b.matrixWorldInverse,a.matrixWorld),a._normalMatrix.getNormalMatrix(a._modelViewMatrix)}function ba(a,b,c,d){a[b]=c.r*c.r*d,a[b+1]=c.g*c.g*d,a[b+2]=c.b*c.b*d}function ca(a,b,c,d){a[b]=c.r*d,a[b+1]=c.g*d,a[b+2]=c.b*d}function da(b){var c,d,e,f,g,h,i,j,k,l=0,m=0,n=0,o=sb,p=o.directional.colors,q=o.directional.positions,r=o.point.colors,s=o.point.positions,t=o.point.distances,u=o.spot.colors,v=o.spot.positions,w=o.spot.distances,x=o.spot.directions,y=o.spot.anglesCos,z=o.spot.exponents,A=o.hemi.skyColors,B=o.hemi.groundColors,C=o.hemi.positions,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0;for(c=0,d=b.length;d>c;c++)if(e=b[c],!e.onlyShadow)if(f=e.color,i=e.intensity,k=e.distance,e instanceof a.AmbientLight){if(!e.visible)continue;Ma.gammaInput?(l+=f.r*f.r,m+=f.g*f.g,n+=f.b*f.b):(l+=f.r,m+=f.g,n+=f.b)}else if(e instanceof a.DirectionalLight){if(H+=1,!e.visible)continue;qb.setFromMatrixPosition(e.matrixWorld),pb.setFromMatrixPosition(e.target.matrixWorld),qb.sub(pb),qb.normalize(),L=3*D,q[L]=qb.x,q[L+1]=qb.y,q[L+2]=qb.z,Ma.gammaInput?ba(p,L,f,i*i):ca(p,L,f,i),D+=1}else if(e instanceof a.PointLight){if(I+=1,!e.visible)continue;M=3*E,Ma.gammaInput?ba(r,M,f,i*i):ca(r,M,f,i),pb.setFromMatrixPosition(e.matrixWorld),s[M]=pb.x,s[M+1]=pb.y,s[M+2]=pb.z,t[E]=k,E+=1}else if(e instanceof a.SpotLight){if(J+=1,!e.visible)continue;N=3*F,Ma.gammaInput?ba(u,N,f,i*i):ca(u,N,f,i),qb.setFromMatrixPosition(e.matrixWorld),v[N]=qb.x,v[N+1]=qb.y,v[N+2]=qb.z,w[F]=k,pb.setFromMatrixPosition(e.target.matrixWorld),qb.sub(pb),qb.normalize(),x[N]=qb.x,x[N+1]=qb.y,x[N+2]=qb.z,y[F]=Math.cos(e.angle),z[F]=e.exponent,F+=1}else if(e instanceof a.HemisphereLight){if(K+=1,!e.visible)continue;qb.setFromMatrixPosition(e.matrixWorld),qb.normalize(),O=3*G,C[O]=qb.x,C[O+1]=qb.y,C[O+2]=qb.z,g=e.color,h=e.groundColor,Ma.gammaInput?(j=i*i,ba(A,O,g,j),ba(B,O,h,j)):(ca(A,O,g,i),ca(B,O,h,i)),G+=1}for(c=3*D,d=Math.max(p.length,3*H);d>c;c++)p[c]=0;for(c=3*E,d=Math.max(r.length,3*I);d>c;c++)r[c]=0;for(c=3*F,d=Math.max(u.length,3*J);d>c;c++)u[c]=0;for(c=3*G,d=Math.max(A.length,3*K);d>c;c++)A[c]=0;for(c=3*G,d=Math.max(B.length,3*K);d>c;c++)B[c]=0;o.directional.length=D,o.point.length=E,o.spot.length=F,o.hemi.length=G,o.ambient[0]=l,o.ambient[1]=m,o.ambient[2]=n}function ea(a){a!==db&&(La.lineWidth(a),db=a)}function fa(a,b,c){ab!==a&&(a?La.enable(La.POLYGON_OFFSET_FILL):La.disable(La.POLYGON_OFFSET_FILL),ab=a),!a||bb===b&&cb===c||(La.polygonOffset(b,c),bb=b,cb=c)}function ga(b,c,d){var e;d?(La.texParameteri(b,La.TEXTURE_WRAP_S,oa(c.wrapS)),La.texParameteri(b,La.TEXTURE_WRAP_T,oa(c.wrapT)),La.texParameteri(b,La.TEXTURE_MAG_FILTER,oa(c.magFilter)),La.texParameteri(b,La.TEXTURE_MIN_FILTER,oa(c.minFilter))):(La.texParameteri(b,La.TEXTURE_WRAP_S,La.CLAMP_TO_EDGE),La.texParameteri(b,La.TEXTURE_WRAP_T,La.CLAMP_TO_EDGE),La.texParameteri(b,La.TEXTURE_MAG_FILTER,na(c.magFilter)),La.texParameteri(b,La.TEXTURE_MIN_FILTER,na(c.minFilter))),e=vb.get("EXT_texture_filter_anisotropic"),e&&c.type!==a.FloatType&&(c.anisotropy>1||c.__oldAnisotropy)&&(La.texParameterf(b,e.TEXTURE_MAX_ANISOTROPY_EXT,Math.min(c.anisotropy,Ma.getMaxAnisotropy())),c.__oldAnisotropy=c.anisotropy)}function ha(a,b){if(a.width>b||a.height>b){var c=b/Math.max(a.width,a.height),d=document.createElement("canvas");d.width=Math.floor(a.width*c),d.height=Math.floor(a.height*c);var e=d.getContext("2d");return e.drawImage(a,0,0,a.width,a.height,0,0,d.width,d.height),console.log("THREE.WebGLRenderer:",a,"is too big ("+a.width+"x"+a.height+"). Resized to "+d.width+"x"+d.height+"."),d}return a}function ia(b,c){if(6===b.image.length)if(b.needsUpdate){b.image.__webglTextureCube||(b.addEventListener("dispose",Ob),b.image.__webglTextureCube=La.createTexture(),Ma.info.memory.textures++),La.activeTexture(La.TEXTURE0+c),La.bindTexture(La.TEXTURE_CUBE_MAP,b.image.__webglTextureCube),La.pixelStorei(La.UNPACK_FLIP_Y_WEBGL,b.flipY);for(var d=b instanceof a.CompressedTexture,e=b.image[0]instanceof a.DataTexture,f=[],g=0;6>g;g++)!Ma.autoScaleCubemaps||d||e?f[g]=e?b.image[g].image:b.image[g]:f[g]=ha(b.image[g],zb);var h=f[0],i=a.Math.isPowerOfTwo(h.width)&&a.Math.isPowerOfTwo(h.height),j=oa(b.format),k=oa(b.type);ga(La.TEXTURE_CUBE_MAP,b,i);for(var g=0;6>g;g++)if(d)for(var l,m=f[g].mipmaps,n=0,o=m.length;o>n;n++)l=m[n],b.format!==a.RGBAFormat&&b.format!==a.RGBFormat?Gb().indexOf(j)>-1?La.compressedTexImage2D(La.TEXTURE_CUBE_MAP_POSITIVE_X+g,n,j,l.width,l.height,0,l.data):console.warn("Attempt to load unsupported compressed texture format"):La.texImage2D(La.TEXTURE_CUBE_MAP_POSITIVE_X+g,n,j,l.width,l.height,0,j,k,l.data);else e?La.texImage2D(La.TEXTURE_CUBE_MAP_POSITIVE_X+g,0,j,f[g].width,f[g].height,0,j,k,f[g].data):La.texImage2D(La.TEXTURE_CUBE_MAP_POSITIVE_X+g,0,j,j,k,f[g]);b.generateMipmaps&&i&&La.generateMipmap(La.TEXTURE_CUBE_MAP),b.needsUpdate=!1,b.onUpdate&&b.onUpdate()}else La.activeTexture(La.TEXTURE0+c),La.bindTexture(La.TEXTURE_CUBE_MAP,b.image.__webglTextureCube)}function ja(a,b){La.activeTexture(La.TEXTURE0+b),La.bindTexture(La.TEXTURE_CUBE_MAP,a.__webglTexture)}function ka(a,b,c){La.bindFramebuffer(La.FRAMEBUFFER,a),La.framebufferTexture2D(La.FRAMEBUFFER,La.COLOR_ATTACHMENT0,c,b.__webglTexture,0)}function la(a,b){La.bindRenderbuffer(La.RENDERBUFFER,a),b.depthBuffer&&!b.stencilBuffer?(La.renderbufferStorage(La.RENDERBUFFER,La.DEPTH_COMPONENT16,b.width,b.height),La.framebufferRenderbuffer(La.FRAMEBUFFER,La.DEPTH_ATTACHMENT,La.RENDERBUFFER,a)):b.depthBuffer&&b.stencilBuffer?(La.renderbufferStorage(La.RENDERBUFFER,La.DEPTH_STENCIL,b.width,b.height),La.framebufferRenderbuffer(La.FRAMEBUFFER,La.DEPTH_STENCIL_ATTACHMENT,La.RENDERBUFFER,a)):La.renderbufferStorage(La.RENDERBUFFER,La.RGBA4,b.width,b.height)}function ma(b){b instanceof a.WebGLRenderTargetCube?(La.bindTexture(La.TEXTURE_CUBE_MAP,b.__webglTexture),La.generateMipmap(La.TEXTURE_CUBE_MAP),La.bindTexture(La.TEXTURE_CUBE_MAP,null)):(La.bindTexture(La.TEXTURE_2D,b.__webglTexture),La.generateMipmap(La.TEXTURE_2D),La.bindTexture(La.TEXTURE_2D,null))}function na(b){return b===a.NearestFilter||b===a.NearestMipMapNearestFilter||b===a.NearestMipMapLinearFilter?La.NEAREST:La.LINEAR}function oa(b){var c;if(b===a.RepeatWrapping)return La.REPEAT;if(b===a.ClampToEdgeWrapping)return La.CLAMP_TO_EDGE;if(b===a.MirroredRepeatWrapping)return La.MIRRORED_REPEAT;if(b===a.NearestFilter)return La.NEAREST;if(b===a.NearestMipMapNearestFilter)return La.NEAREST_MIPMAP_NEAREST;if(b===a.NearestMipMapLinearFilter)return La.NEAREST_MIPMAP_LINEAR;if(b===a.LinearFilter)return La.LINEAR;if(b===a.LinearMipMapNearestFilter)return La.LINEAR_MIPMAP_NEAREST;if(b===a.LinearMipMapLinearFilter)return La.LINEAR_MIPMAP_LINEAR;if(b===a.UnsignedByteType)return La.UNSIGNED_BYTE;if(b===a.UnsignedShort4444Type)return La.UNSIGNED_SHORT_4_4_4_4;if(b===a.UnsignedShort5551Type)return La.UNSIGNED_SHORT_5_5_5_1;if(b===a.UnsignedShort565Type)return La.UNSIGNED_SHORT_5_6_5;if(b===a.ByteType)return La.BYTE;if(b===a.ShortType)return La.SHORT;if(b===a.UnsignedShortType)return La.UNSIGNED_SHORT;if(b===a.IntType)return La.INT;if(b===a.UnsignedIntType)return La.UNSIGNED_INT;if(b===a.FloatType)return La.FLOAT;if(b===a.AlphaFormat)return La.ALPHA;if(b===a.RGBFormat)return La.RGB;if(b===a.RGBAFormat)return La.RGBA;if(b===a.LuminanceFormat)return La.LUMINANCE;if(b===a.LuminanceAlphaFormat)return La.LUMINANCE_ALPHA;if(b===a.AddEquation)return La.FUNC_ADD;if(b===a.SubtractEquation)return La.FUNC_SUBTRACT;if(b===a.ReverseSubtractEquation)return La.FUNC_REVERSE_SUBTRACT;if(b===a.ZeroFactor)return La.ZERO;if(b===a.OneFactor)return La.ONE;if(b===a.SrcColorFactor)return La.SRC_COLOR;if(b===a.OneMinusSrcColorFactor)return La.ONE_MINUS_SRC_COLOR;if(b===a.SrcAlphaFactor)return La.SRC_ALPHA;if(b===a.OneMinusSrcAlphaFactor)return La.ONE_MINUS_SRC_ALPHA;if(b===a.DstAlphaFactor)return La.DST_ALPHA;if(b===a.OneMinusDstAlphaFactor)return La.ONE_MINUS_DST_ALPHA;if(b===a.DstColorFactor)return La.DST_COLOR;if(b===a.OneMinusDstColorFactor)return La.ONE_MINUS_DST_COLOR;if(b===a.SrcAlphaSaturateFactor)return La.SRC_ALPHA_SATURATE;if(c=vb.get("WEBGL_compressed_texture_s3tc"),null!==c){if(b===a.RGB_S3TC_DXT1_Format)return c.COMPRESSED_RGB_S3TC_DXT1_EXT;if(b===a.RGBA_S3TC_DXT1_Format)return c.COMPRESSED_RGBA_S3TC_DXT1_EXT;if(b===a.RGBA_S3TC_DXT3_Format)return c.COMPRESSED_RGBA_S3TC_DXT3_EXT;if(b===a.RGBA_S3TC_DXT5_Format)return c.COMPRESSED_RGBA_S3TC_DXT5_EXT}if(c=vb.get("WEBGL_compressed_texture_pvrtc"),null!==c){if(b===a.RGB_PVRTC_4BPPV1_Format)return c.COMPRESSED_RGB_PVRTC_4BPPV1_IMG;if(b===a.RGB_PVRTC_2BPPV1_Format)return c.COMPRESSED_RGB_PVRTC_2BPPV1_IMG;if(b===a.RGBA_PVRTC_4BPPV1_Format)return c.COMPRESSED_RGBA_PVRTC_4BPPV1_IMG;if(b===a.RGBA_PVRTC_2BPPV1_Format)return c.COMPRESSED_RGBA_PVRTC_2BPPV1_IMG}if(c=vb.get("EXT_blend_minmax"),null!==c){if(b===a.MinEquation)return c.MIN_EXT;if(b===a.MaxEquation)return c.MAX_EXT}return 0}function pa(b){if(Bb&&b&&b.skeleton&&b.skeleton.useVertexTexture)return 1024;var c=La.getParameter(La.MAX_VERTEX_UNIFORM_VECTORS),d=Math.floor((c-20)/4),e=d;return void 0!==b&&b instanceof a.SkinnedMesh&&(e=Math.min(b.skeleton.bones.length,e),e<b.skeleton.bones.length&&console.warn("WebGLRenderer: too many bones - "+b.skeleton.bones.length+", this GPU supports just "+e+" (try OpenGL instead of ANGLE)")),e}function qa(b){for(var c=0,d=0,e=0,f=0,g=0,h=b.length;h>g;g++){var i=b[g];i.onlyShadow||i.visible===!1||(i instanceof a.DirectionalLight&&c++,i instanceof a.PointLight&&d++,i instanceof a.SpotLight&&e++,i instanceof a.HemisphereLight&&f++)}return{directional:c,point:d,spot:e,hemi:f}}function ra(b){for(var c=0,d=0,e=b.length;e>d;d++){var f=b[d];f.castShadow&&(f instanceof a.SpotLight&&c++,f instanceof a.DirectionalLight&&!f.shadowCascade&&c++)}return c}console.log("THREE.WebGLRenderer",a.REVISION),b=b||{};var sa=void 0!==b.canvas?b.canvas:document.createElement("canvas"),ta=void 0!==b.context?b.context:null,ua=void 0!==b.precision?b.precision:"highp",va=void 0!==b.alpha?b.alpha:!1,wa=void 0!==b.depth?b.depth:!0,xa=void 0!==b.stencil?b.stencil:!0,ya=void 0!==b.antialias?b.antialias:!1,za=void 0!==b.premultipliedAlpha?b.premultipliedAlpha:!0,Aa=void 0!==b.preserveDrawingBuffer?b.preserveDrawingBuffer:!1,Ba=void 0!==b.logarithmicDepthBuffer?b.logarithmicDepthBuffer:!1,Ca=new a.Color(0),Da=0,Ea=[],Fa={},Ga=[],Ha=[],Ia=[],Ja=[],Ka=[];this.domElement=sa,this.context=null,this.devicePixelRatio=void 0!==b.devicePixelRatio?b.devicePixelRatio:void 0!==self.devicePixelRatio?self.devicePixelRatio:1,this.autoClear=!0,this.autoClearColor=!0,this.autoClearDepth=!0,this.autoClearStencil=!0,this.sortObjects=!0,this.gammaInput=!1,this.gammaOutput=!1,this.shadowMapEnabled=!1,this.shadowMapType=a.PCFShadowMap,this.shadowMapCullFace=a.CullFaceFront,this.shadowMapDebug=!1,this.shadowMapCascade=!1,this.maxMorphTargets=8,this.maxMorphNormals=4,this.autoScaleCubemaps=!0,this.info={memory:{programs:0,geometries:0,textures:0},render:{calls:0,vertices:0,faces:0,points:0}};var La,Ma=this,Na=[],Oa=null,Pa=null,Qa=-1,Ra=-1,Sa=null,Ta=0,Ua=-1,Va=-1,Wa=-1,Xa=-1,Ya=-1,Za=-1,$a=-1,_a=-1,ab=null,bb=null,cb=null,db=null,eb=0,fb=0,gb=sa.width,hb=sa.height,ib=0,jb=0,kb=new Uint8Array(16),lb=new Uint8Array(16),mb=new a.Frustum,nb=new a.Matrix4,ob=new a.Matrix4,pb=new a.Vector3,qb=new a.Vector3,rb=!0,sb={ambient:[0,0,0],directional:{length:0,colors:[],positions:[]},point:{length:0,colors:[],positions:[],distances:[]},spot:{length:0,colors:[],positions:[],distances:[],directions:[],anglesCos:[],exponents:[]},hemi:{length:0,skyColors:[],groundColors:[],positions:[]}};try{var tb={alpha:va,depth:wa,stencil:xa,antialias:ya,premultipliedAlpha:za,preserveDrawingBuffer:Aa};if(La=ta||sa.getContext("webgl",tb)||sa.getContext("experimental-webgl",tb),null===La)throw null!==sa.getContext("webgl")?"Error creating WebGL context with your selected attributes.":"Error creating WebGL context."}catch(ub){console.error(ub)}void 0===La.getShaderPrecisionFormat&&(La.getShaderPrecisionFormat=function(){return{rangeMin:1,rangeMax:1,precision:1}});var vb=new a.WebGLExtensions(La);vb.get("OES_texture_float"),vb.get("OES_texture_float_linear"),vb.get("OES_standard_derivatives"),Ba&&vb.get("EXT_frag_depth"),c(),this.context=La;var wb=La.getParameter(La.MAX_TEXTURE_IMAGE_UNITS),xb=La.getParameter(La.MAX_VERTEX_TEXTURE_IMAGE_UNITS),yb=La.getParameter(La.MAX_TEXTURE_SIZE),zb=La.getParameter(La.MAX_CUBE_MAP_TEXTURE_SIZE),Ab=xb>0,Bb=Ab&&vb.get("OES_texture_float"),Cb=La.getShaderPrecisionFormat(La.VERTEX_SHADER,La.HIGH_FLOAT),Db=La.getShaderPrecisionFormat(La.VERTEX_SHADER,La.MEDIUM_FLOAT),Eb=(La.getShaderPrecisionFormat(La.VERTEX_SHADER,La.LOW_FLOAT),La.getShaderPrecisionFormat(La.FRAGMENT_SHADER,La.HIGH_FLOAT)),Fb=La.getShaderPrecisionFormat(La.FRAGMENT_SHADER,La.MEDIUM_FLOAT),Gb=(La.getShaderPrecisionFormat(La.FRAGMENT_SHADER,La.LOW_FLOAT),function(){var a;return function(){if(void 0!==a)return a;if(a=[],vb.get("WEBGL_compressed_texture_pvrtc")||vb.get("WEBGL_compressed_texture_s3tc"))for(var b=La.getParameter(La.COMPRESSED_TEXTURE_FORMATS),c=0;c<b.length;c++)a.push(b[c]);return a}}()),Hb=Cb.precision>0&&Eb.precision>0,Ib=Db.precision>0&&Fb.precision>0;"highp"!==ua||Hb||(Ib?(ua="mediump",console.warn("THREE.WebGLRenderer: highp not supported, using mediump.")):(ua="lowp",console.warn("THREE.WebGLRenderer: highp and mediump not supported, using lowp."))),"mediump"!==ua||Ib||(ua="lowp",console.warn("THREE.WebGLRenderer: mediump not supported, using lowp."));var Jb=new a.ShadowMapPlugin(this,Ea,Fa,Ga),Kb=new a.SpritePlugin(this,Ja),Lb=new a.LensFlarePlugin(this,Ka);this.getContext=function(){return La},this.supportsVertexTextures=function(){return Ab},this.supportsFloatTextures=function(){return vb.get("OES_texture_float")},this.supportsStandardDerivatives=function(){return vb.get("OES_standard_derivatives")},this.supportsCompressedTextureS3TC=function(){return vb.get("WEBGL_compressed_texture_s3tc")},this.supportsCompressedTexturePVRTC=function(){return vb.get("WEBGL_compressed_texture_pvrtc")},this.supportsBlendMinMax=function(){return vb.get("EXT_blend_minmax")},this.getMaxAnisotropy=function(){var a;return function(){if(void 0!==a)return a;var b=vb.get("EXT_texture_filter_anisotropic");return a=null!==b?La.getParameter(b.MAX_TEXTURE_MAX_ANISOTROPY_EXT):0}}(),this.getPrecision=function(){return ua},this.setSize=function(a,b,c){sa.width=a*this.devicePixelRatio,sa.height=b*this.devicePixelRatio,c!==!1&&(sa.style.width=a+"px",sa.style.height=b+"px"),this.setViewport(0,0,a,b)},this.setViewport=function(a,b,c,d){eb=a*this.devicePixelRatio,fb=b*this.devicePixelRatio,gb=c*this.devicePixelRatio,hb=d*this.devicePixelRatio,La.viewport(eb,fb,gb,hb)},this.setScissor=function(a,b,c,d){La.scissor(a*this.devicePixelRatio,b*this.devicePixelRatio,c*this.devicePixelRatio,d*this.devicePixelRatio)},this.enableScissorTest=function(a){a?La.enable(La.SCISSOR_TEST):La.disable(La.SCISSOR_TEST)},this.setClearColor=function(a,b){Ca.set(a),Da=void 0!==b?b:1,La.clearColor(Ca.r,Ca.g,Ca.b,Da)},this.setClearColorHex=function(a,b){console.warn("THREE.WebGLRenderer: .setClearColorHex() is being removed. Use .setClearColor() instead."),this.setClearColor(a,b)},this.getClearColor=function(){return Ca},this.getClearAlpha=function(){return Da},this.clear=function(a,b,c){var d=0;(void 0===a||a)&&(d|=La.COLOR_BUFFER_BIT),(void 0===b||b)&&(d|=La.DEPTH_BUFFER_BIT),(void 0===c||c)&&(d|=La.STENCIL_BUFFER_BIT),La.clear(d)},this.clearColor=function(){La.clear(La.COLOR_BUFFER_BIT)},this.clearDepth=function(){La.clear(La.DEPTH_BUFFER_BIT)},this.clearStencil=function(){La.clear(La.STENCIL_BUFFER_BIT)},this.clearTarget=function(a,b,c,d){this.setRenderTarget(a),this.clear(b,c,d)},this.resetGLState=function(){Oa=null,Sa=null,Wa=-1,$a=-1,_a=-1,Ua=-1,Va=-1,Ra=-1,Qa=-1,rb=!0};var Mb=function(a){var b=a.target;b.traverse(function(a){a.removeEventListener("remove",Mb),L(a)})},Nb=function(a){var b=a.target;b.removeEventListener("dispose",Nb),Sb(b)},Ob=function(a){var b=a.target;b.removeEventListener("dispose",Ob),Tb(b),Ma.info.memory.textures--},Pb=function(a){var b=a.target;b.removeEventListener("dispose",Pb),Ub(b),Ma.info.memory.textures--},Qb=function(a){var b=a.target;b.removeEventListener("dispose",Qb),Vb(b)},Rb=function(a){for(var b=["__webglVertexBuffer","__webglNormalBuffer","__webglTangentBuffer","__webglColorBuffer","__webglUVBuffer","__webglUV2Buffer","__webglSkinIndicesBuffer","__webglSkinWeightsBuffer","__webglFaceBuffer","__webglLineBuffer","__webglLineDistanceBuffer"],c=0,d=b.length;d>c;c++){var e=b[c];void 0!==a[e]&&(La.deleteBuffer(a[e]),delete a[e])}if(void 0!==a.__webglCustomAttributesList){for(var e in a.__webglCustomAttributesList)La.deleteBuffer(a.__webglCustomAttributesList[e].buffer);delete a.__webglCustomAttributesList}Ma.info.memory.geometries--},Sb=function(b){if(delete b.__webglInit,b instanceof a.BufferGeometry){for(var c in b.attributes){var d=b.attributes[c];void 0!==d.buffer&&(La.deleteBuffer(d.buffer),delete d.buffer)}Ma.info.memory.geometries--}else{var e=Wb[b.id];if(void 0!==e){for(var f=0,g=e.length;g>f;f++){var h=e[f];if(void 0!==h.numMorphTargets){for(var i=0,j=h.numMorphTargets;j>i;i++)La.deleteBuffer(h.__webglMorphTargetsBuffers[i]);delete h.__webglMorphTargetsBuffers}if(void 0!==h.numMorphNormals){for(var i=0,j=h.numMorphNormals;j>i;i++)La.deleteBuffer(h.__webglMorphNormalsBuffers[i]);delete h.__webglMorphNormalsBuffers}Rb(h)}delete Wb[b.id]}else Rb(b)}Ra=-1},Tb=function(a){if(a.image&&a.image.__webglTextureCube)La.deleteTexture(a.image.__webglTextureCube),delete a.image.__webglTextureCube;else{if(void 0===a.__webglInit)return;La.deleteTexture(a.__webglTexture),delete a.__webglTexture,delete a.__webglInit}},Ub=function(b){if(b&&void 0!==b.__webglTexture){if(La.deleteTexture(b.__webglTexture),delete b.__webglTexture,b instanceof a.WebGLRenderTargetCube)for(var c=0;6>c;c++)La.deleteFramebuffer(b.__webglFramebuffer[c]),La.deleteRenderbuffer(b.__webglRenderbuffer[c]);else La.deleteFramebuffer(b.__webglFramebuffer),La.deleteRenderbuffer(b.__webglRenderbuffer);delete b.__webglFramebuffer,delete b.__webglRenderbuffer}},Vb=function(a){var b=a.program.program;if(void 0!==b){a.program=void 0;var c,d,e,f=!1;for(c=0,d=Na.length;d>c;c++)if(e=Na[c],e.program===b){e.usedTimes--,0===e.usedTimes&&(f=!0);break}if(f===!0){var g=[];for(c=0,d=Na.length;d>c;c++)e=Na[c],e.program!==b&&g.push(e);Na=g,La.deleteProgram(b),Ma.info.memory.programs--}}};this.renderBufferImmediate=function(b,c,d){if(r(),b.hasPositions&&!b.__webglVertexBuffer&&(b.__webglVertexBuffer=La.createBuffer()),b.hasNormals&&!b.__webglNormalBuffer&&(b.__webglNormalBuffer=La.createBuffer()),b.hasUvs&&!b.__webglUvBuffer&&(b.__webglUvBuffer=La.createBuffer()),b.hasColors&&!b.__webglColorBuffer&&(b.__webglColorBuffer=La.createBuffer()),b.hasPositions&&(La.bindBuffer(La.ARRAY_BUFFER,b.__webglVertexBuffer),La.bufferData(La.ARRAY_BUFFER,b.positionArray,La.DYNAMIC_DRAW),s(c.attributes.position),La.vertexAttribPointer(c.attributes.position,3,La.FLOAT,!1,0,0)),b.hasNormals){if(La.bindBuffer(La.ARRAY_BUFFER,b.__webglNormalBuffer),d.shading===a.FlatShading){var e,f,g,h,i,j,k,l,m,n,o,p,q,u,v=3*b.count;for(u=0;v>u;u+=9)q=b.normalArray,h=q[u],k=q[u+1],n=q[u+2],i=q[u+3],l=q[u+4],o=q[u+5],j=q[u+6],m=q[u+7],p=q[u+8],e=(h+i+j)/3,f=(k+l+m)/3,g=(n+o+p)/3,q[u]=e,q[u+1]=f,q[u+2]=g,q[u+3]=e,q[u+4]=f,q[u+5]=g,q[u+6]=e,q[u+7]=f,q[u+8]=g}La.bufferData(La.ARRAY_BUFFER,b.normalArray,La.DYNAMIC_DRAW),s(c.attributes.normal),La.vertexAttribPointer(c.attributes.normal,3,La.FLOAT,!1,0,0)}b.hasUvs&&d.map&&(La.bindBuffer(La.ARRAY_BUFFER,b.__webglUvBuffer),La.bufferData(La.ARRAY_BUFFER,b.uvArray,La.DYNAMIC_DRAW),s(c.attributes.uv),La.vertexAttribPointer(c.attributes.uv,2,La.FLOAT,!1,0,0)),b.hasColors&&d.vertexColors!==a.NoColors&&(La.bindBuffer(La.ARRAY_BUFFER,b.__webglColorBuffer),La.bufferData(La.ARRAY_BUFFER,b.colorArray,La.DYNAMIC_DRAW),s(c.attributes.color),La.vertexAttribPointer(c.attributes.color,3,La.FLOAT,!1,0,0)),t(),La.drawArrays(La.TRIANGLES,0,b.count),b.count=0},this.renderBufferDirect=function(b,c,d,e,f,g){if(e.visible!==!1){var h=O(b,c,d,e,g),i=!1,j=e.wireframe?1:0,k=16777215*f.id+2*h.id+j;if(k!==Ra&&(Ra=k,i=!0),i&&r(),g instanceof a.Mesh){var l=e.wireframe===!0?La.LINES:La.TRIANGLES,m=f.attributes.index;if(m){var n,o;m.array instanceof Uint32Array&&vb.get("OES_element_index_uint")?(n=La.UNSIGNED_INT,o=4):(n=La.UNSIGNED_SHORT,o=2);var p=f.offsets;if(0===p.length)i&&(q(e,h,f,0),La.bindBuffer(La.ELEMENT_ARRAY_BUFFER,m.buffer)),La.drawElements(l,m.array.length,n,0),Ma.info.render.calls++,Ma.info.render.vertices+=m.array.length,Ma.info.render.faces+=m.array.length/3;else{i=!0;for(var s=0,t=p.length;t>s;s++){var u=p[s].index;i&&(q(e,h,f,u),La.bindBuffer(La.ELEMENT_ARRAY_BUFFER,m.buffer)),La.drawElements(l,p[s].count,n,p[s].start*o),Ma.info.render.calls++,Ma.info.render.vertices+=p[s].count,Ma.info.render.faces+=p[s].count/3}}}else{i&&q(e,h,f,0);var v=f.attributes.position;La.drawArrays(l,0,v.array.length/3),Ma.info.render.calls++,Ma.info.render.vertices+=v.array.length/3,Ma.info.render.faces+=v.array.length/9}}else if(g instanceof a.PointCloud){i&&q(e,h,f,0);var v=f.attributes.position;La.drawArrays(La.POINTS,0,v.array.length/3),Ma.info.render.calls++,Ma.info.render.points+=v.array.length/3}else if(g instanceof a.Line){var l=g.mode===a.LineStrip?La.LINE_STRIP:La.LINES;ea(e.linewidth);var m=f.attributes.index;if(m){var n,o;m.array instanceof Uint32Array?(n=La.UNSIGNED_INT,o=4):(n=La.UNSIGNED_SHORT,o=2);var p=f.offsets;if(0===p.length)i&&(q(e,h,f,0),La.bindBuffer(La.ELEMENT_ARRAY_BUFFER,m.buffer)),La.drawElements(l,m.array.length,n,0),Ma.info.render.calls++,Ma.info.render.vertices+=m.array.length;else{p.length>1&&(i=!0);for(var s=0,t=p.length;t>s;s++){var u=p[s].index;i&&(q(e,h,f,u),La.bindBuffer(La.ELEMENT_ARRAY_BUFFER,m.buffer)),La.drawElements(l,p[s].count,n,p[s].start*o),Ma.info.render.calls++,Ma.info.render.vertices+=p[s].count}}}else{i&&q(e,h,f,0);var v=f.attributes.position;La.drawArrays(l,0,v.array.length/3),Ma.info.render.calls++,Ma.info.render.points+=v.array.length/3}}}},this.renderBuffer=function(b,c,d,e,f,g){if(e.visible!==!1){var h=O(b,c,d,e,g),i=h.attributes,j=!1,k=e.wireframe?1:0,l=16777215*f.id+2*h.id+k;if(l!==Ra&&(Ra=l,j=!0),j&&r(),!e.morphTargets&&i.position>=0?j&&(La.bindBuffer(La.ARRAY_BUFFER,f.__webglVertexBuffer),s(i.position),La.vertexAttribPointer(i.position,3,La.FLOAT,!1,0,0)):g.morphTargetBase&&u(e,f,g),j){if(f.__webglCustomAttributesList)for(var m=0,n=f.__webglCustomAttributesList.length;n>m;m++){var o=f.__webglCustomAttributesList[m];i[o.buffer.belongsToAttribute]>=0&&(La.bindBuffer(La.ARRAY_BUFFER,o.buffer),s(i[o.buffer.belongsToAttribute]),La.vertexAttribPointer(i[o.buffer.belongsToAttribute],o.size,La.FLOAT,!1,0,0))}i.color>=0&&(g.geometry.colors.length>0||g.geometry.faces.length>0?(La.bindBuffer(La.ARRAY_BUFFER,f.__webglColorBuffer),s(i.color),La.vertexAttribPointer(i.color,3,La.FLOAT,!1,0,0)):void 0!==e.defaultAttributeValues&&La.vertexAttrib3fv(i.color,e.defaultAttributeValues.color)),i.normal>=0&&(La.bindBuffer(La.ARRAY_BUFFER,f.__webglNormalBuffer),s(i.normal),La.vertexAttribPointer(i.normal,3,La.FLOAT,!1,0,0)),i.tangent>=0&&(La.bindBuffer(La.ARRAY_BUFFER,f.__webglTangentBuffer),s(i.tangent),La.vertexAttribPointer(i.tangent,4,La.FLOAT,!1,0,0)),i.uv>=0&&(g.geometry.faceVertexUvs[0]?(La.bindBuffer(La.ARRAY_BUFFER,f.__webglUVBuffer),s(i.uv),La.vertexAttribPointer(i.uv,2,La.FLOAT,!1,0,0)):void 0!==e.defaultAttributeValues&&La.vertexAttrib2fv(i.uv,e.defaultAttributeValues.uv)),i.uv2>=0&&(g.geometry.faceVertexUvs[1]?(La.bindBuffer(La.ARRAY_BUFFER,f.__webglUV2Buffer),s(i.uv2),La.vertexAttribPointer(i.uv2,2,La.FLOAT,!1,0,0)):void 0!==e.defaultAttributeValues&&La.vertexAttrib2fv(i.uv2,e.defaultAttributeValues.uv2)),e.skinning&&i.skinIndex>=0&&i.skinWeight>=0&&(La.bindBuffer(La.ARRAY_BUFFER,f.__webglSkinIndicesBuffer),s(i.skinIndex),La.vertexAttribPointer(i.skinIndex,4,La.FLOAT,!1,0,0),La.bindBuffer(La.ARRAY_BUFFER,f.__webglSkinWeightsBuffer),s(i.skinWeight),La.vertexAttribPointer(i.skinWeight,4,La.FLOAT,!1,0,0)),i.lineDistance>=0&&(La.bindBuffer(La.ARRAY_BUFFER,f.__webglLineDistanceBuffer),s(i.lineDistance),La.vertexAttribPointer(i.lineDistance,1,La.FLOAT,!1,0,0))}if(t(),g instanceof a.Mesh){var p=f.__typeArray===Uint32Array?La.UNSIGNED_INT:La.UNSIGNED_SHORT;e.wireframe?(ea(e.wireframeLinewidth),j&&La.bindBuffer(La.ELEMENT_ARRAY_BUFFER,f.__webglLineBuffer),La.drawElements(La.LINES,f.__webglLineCount,p,0)):(j&&La.bindBuffer(La.ELEMENT_ARRAY_BUFFER,f.__webglFaceBuffer),La.drawElements(La.TRIANGLES,f.__webglFaceCount,p,0)),Ma.info.render.calls++,Ma.info.render.vertices+=f.__webglFaceCount,Ma.info.render.faces+=f.__webglFaceCount/3}else if(g instanceof a.Line){var q=g.mode===a.LineStrip?La.LINE_STRIP:La.LINES;ea(e.linewidth),La.drawArrays(q,0,f.__webglLineCount),Ma.info.render.calls++}else g instanceof a.PointCloud&&(La.drawArrays(La.POINTS,0,f.__webglParticleCount),Ma.info.render.calls++,Ma.info.render.points+=f.__webglParticleCount)}},this.render=function(b,c,d,e){if(c instanceof a.Camera==!1)return void console.error("THREE.WebGLRenderer.render: camera is not an instance of THREE.Camera.");var f=b.fog;Ra=-1,Qa=-1,Sa=null,rb=!0,b.autoUpdate===!0&&b.updateMatrixWorld(),void 0===c.parent&&c.updateMatrixWorld(),b.traverse(function(b){b instanceof a.SkinnedMesh&&b.skeleton.update()}),c.matrixWorldInverse.getInverse(c.matrixWorld),nb.multiplyMatrices(c.projectionMatrix,c.matrixWorldInverse),mb.setFromMatrix(nb),Ea.length=0,Ha.length=0,Ia.length=0,Ja.length=0,Ka.length=0,y(b,b),Ma.sortObjects===!0&&(Ha.sort(v),Ia.sort(w)),Jb.render(b,c),Ma.info.render.calls=0,Ma.info.render.vertices=0,Ma.info.render.faces=0,Ma.info.render.points=0,this.setRenderTarget(d),(this.autoClear||e)&&this.clear(this.autoClearColor,this.autoClearDepth,this.autoClearStencil);for(var g=0,h=Ga.length;h>g;g++){var i=Ga[g],j=i.object;j.visible&&(aa(j,c),B(i))}if(b.overrideMaterial){var k=b.overrideMaterial;this.setBlending(k.blending,k.blendEquation,k.blendSrc,k.blendDst),this.setDepthTest(k.depthTest),this.setDepthWrite(k.depthWrite),fa(k.polygonOffset,k.polygonOffsetFactor,k.polygonOffsetUnits),z(Ha,c,Ea,f,!0,k),z(Ia,c,Ea,f,!0,k),A(Ga,"",c,Ea,f,!1,k)}else{var k=null;this.setBlending(a.NoBlending),z(Ha,c,Ea,f,!1,k),A(Ga,"opaque",c,Ea,f,!1,k),z(Ia,c,Ea,f,!0,k),A(Ga,"transparent",c,Ea,f,!0,k)}Kb.render(b,c),Lb.render(b,c,ib,jb),d&&d.generateMipmaps&&d.minFilter!==a.NearestFilter&&d.minFilter!==a.LinearFilter&&ma(d),this.setDepthTest(!0),this.setDepthWrite(!0)},this.renderImmediateObject=function(a,b,c,d,e){var f=O(a,b,c,d,e);Ra=-1,Ma.setMaterialFaces(d),e.immediateRenderCallback?e.immediateRenderCallback(f,La,mb):e.render(function(a){Ma.renderBufferImmediate(a,f,d)})};var Wb={},Xb=0;this.setFaceCulling=function(b,c){b===a.CullFaceNone?La.disable(La.CULL_FACE):(c===a.FrontFaceDirectionCW?La.frontFace(La.CW):La.frontFace(La.CCW),b===a.CullFaceBack?La.cullFace(La.BACK):b===a.CullFaceFront?La.cullFace(La.FRONT):La.cullFace(La.FRONT_AND_BACK),La.enable(La.CULL_FACE))},this.setMaterialFaces=function(b){var c=b.side===a.DoubleSide,d=b.side===a.BackSide;Ua!==c&&(c?La.disable(La.CULL_FACE):La.enable(La.CULL_FACE),Ua=c),Va!==d&&(d?La.frontFace(La.CW):La.frontFace(La.CCW),Va=d)},this.setDepthTest=function(a){$a!==a&&(a?La.enable(La.DEPTH_TEST):La.disable(La.DEPTH_TEST),$a=a)},this.setDepthWrite=function(a){_a!==a&&(La.depthMask(a),_a=a)},this.setBlending=function(b,c,d,e){b!==Wa&&(b===a.NoBlending?La.disable(La.BLEND):b===a.AdditiveBlending?(La.enable(La.BLEND),La.blendEquation(La.FUNC_ADD),La.blendFunc(La.SRC_ALPHA,La.ONE)):b===a.SubtractiveBlending?(La.enable(La.BLEND),La.blendEquation(La.FUNC_ADD),La.blendFunc(La.ZERO,La.ONE_MINUS_SRC_COLOR)):b===a.MultiplyBlending?(La.enable(La.BLEND),La.blendEquation(La.FUNC_ADD),La.blendFunc(La.ZERO,La.SRC_COLOR)):b===a.CustomBlending?La.enable(La.BLEND):(La.enable(La.BLEND),La.blendEquationSeparate(La.FUNC_ADD,La.FUNC_ADD),La.blendFuncSeparate(La.SRC_ALPHA,La.ONE_MINUS_SRC_ALPHA,La.ONE,La.ONE_MINUS_SRC_ALPHA)),Wa=b),b===a.CustomBlending?(c!==Xa&&(La.blendEquation(oa(c)),Xa=c),(d!==Ya||e!==Za)&&(La.blendFunc(oa(d),oa(e)),Ya=d,Za=e)):(Xa=null,Ya=null,Za=null)},this.uploadTexture=function(b){void 0===b.__webglInit&&(b.__webglInit=!0,b.addEventListener("dispose",Ob),b.__webglTexture=La.createTexture(),Ma.info.memory.textures++),La.bindTexture(La.TEXTURE_2D,b.__webglTexture),La.pixelStorei(La.UNPACK_FLIP_Y_WEBGL,b.flipY),La.pixelStorei(La.UNPACK_PREMULTIPLY_ALPHA_WEBGL,b.premultiplyAlpha),La.pixelStorei(La.UNPACK_ALIGNMENT,b.unpackAlignment),b.image=ha(b.image,yb);var c=b.image,d=a.Math.isPowerOfTwo(c.width)&&a.Math.isPowerOfTwo(c.height),e=oa(b.format),f=oa(b.type);ga(La.TEXTURE_2D,b,d);var g,h=b.mipmaps;if(b instanceof a.DataTexture)if(h.length>0&&d){for(var i=0,j=h.length;j>i;i++)g=h[i],La.texImage2D(La.TEXTURE_2D,i,e,g.width,g.height,0,e,f,g.data);b.generateMipmaps=!1}else La.texImage2D(La.TEXTURE_2D,0,e,c.width,c.height,0,e,f,c.data);else if(b instanceof a.CompressedTexture)for(var i=0,j=h.length;j>i;i++)g=h[i],b.format!==a.RGBAFormat&&b.format!==a.RGBFormat?Gb().indexOf(e)>-1?La.compressedTexImage2D(La.TEXTURE_2D,i,e,g.width,g.height,0,g.data):console.warn("Attempt to load unsupported compressed texture format"):La.texImage2D(La.TEXTURE_2D,i,e,g.width,g.height,0,e,f,g.data);else if(h.length>0&&d){for(var i=0,j=h.length;j>i;i++)g=h[i],La.texImage2D(La.TEXTURE_2D,i,e,e,f,g);b.generateMipmaps=!1;
}else La.texImage2D(La.TEXTURE_2D,0,e,e,f,b.image);b.generateMipmaps&&d&&La.generateMipmap(La.TEXTURE_2D),b.needsUpdate=!1,b.onUpdate&&b.onUpdate()},this.setTexture=function(a,b){La.activeTexture(La.TEXTURE0+b),a.needsUpdate?Ma.uploadTexture(a):La.bindTexture(La.TEXTURE_2D,a.__webglTexture)},this.setRenderTarget=function(b){var c=b instanceof a.WebGLRenderTargetCube;if(b&&void 0===b.__webglFramebuffer){void 0===b.depthBuffer&&(b.depthBuffer=!0),void 0===b.stencilBuffer&&(b.stencilBuffer=!0),b.addEventListener("dispose",Pb),b.__webglTexture=La.createTexture(),Ma.info.memory.textures++;var d=a.Math.isPowerOfTwo(b.width)&&a.Math.isPowerOfTwo(b.height),e=oa(b.format),f=oa(b.type);if(c){b.__webglFramebuffer=[],b.__webglRenderbuffer=[],La.bindTexture(La.TEXTURE_CUBE_MAP,b.__webglTexture),ga(La.TEXTURE_CUBE_MAP,b,d);for(var g=0;6>g;g++)b.__webglFramebuffer[g]=La.createFramebuffer(),b.__webglRenderbuffer[g]=La.createRenderbuffer(),La.texImage2D(La.TEXTURE_CUBE_MAP_POSITIVE_X+g,0,e,b.width,b.height,0,e,f,null),ka(b.__webglFramebuffer[g],b,La.TEXTURE_CUBE_MAP_POSITIVE_X+g),la(b.__webglRenderbuffer[g],b);d&&La.generateMipmap(La.TEXTURE_CUBE_MAP)}else b.__webglFramebuffer=La.createFramebuffer(),b.shareDepthFrom?b.__webglRenderbuffer=b.shareDepthFrom.__webglRenderbuffer:b.__webglRenderbuffer=La.createRenderbuffer(),La.bindTexture(La.TEXTURE_2D,b.__webglTexture),ga(La.TEXTURE_2D,b,d),La.texImage2D(La.TEXTURE_2D,0,e,b.width,b.height,0,e,f,null),ka(b.__webglFramebuffer,b,La.TEXTURE_2D),b.shareDepthFrom?b.depthBuffer&&!b.stencilBuffer?La.framebufferRenderbuffer(La.FRAMEBUFFER,La.DEPTH_ATTACHMENT,La.RENDERBUFFER,b.__webglRenderbuffer):b.depthBuffer&&b.stencilBuffer&&La.framebufferRenderbuffer(La.FRAMEBUFFER,La.DEPTH_STENCIL_ATTACHMENT,La.RENDERBUFFER,b.__webglRenderbuffer):la(b.__webglRenderbuffer,b),d&&La.generateMipmap(La.TEXTURE_2D);c?La.bindTexture(La.TEXTURE_CUBE_MAP,null):La.bindTexture(La.TEXTURE_2D,null),La.bindRenderbuffer(La.RENDERBUFFER,null),La.bindFramebuffer(La.FRAMEBUFFER,null)}var h,i,j,k,l;b?(h=c?b.__webglFramebuffer[b.activeCubeFace]:b.__webglFramebuffer,i=b.width,j=b.height,k=0,l=0):(h=null,i=gb,j=hb,k=eb,l=fb),h!==Pa&&(La.bindFramebuffer(La.FRAMEBUFFER,h),La.viewport(k,l,i,j),Pa=h),ib=i,jb=j},this.initMaterial=function(){console.warn("THREE.WebGLRenderer: .initMaterial() has been removed.")},this.addPrePlugin=function(){console.warn("THREE.WebGLRenderer: .addPrePlugin() has been removed.")},this.addPostPlugin=function(){console.warn("THREE.WebGLRenderer: .addPostPlugin() has been removed.")},this.updateShadowMap=function(){console.warn("THREE.WebGLRenderer: .updateShadowMap() has been removed.")}},a.WebGLRenderTarget=function(b,c,d){this.width=b,this.height=c,d=d||{},this.wrapS=void 0!==d.wrapS?d.wrapS:a.ClampToEdgeWrapping,this.wrapT=void 0!==d.wrapT?d.wrapT:a.ClampToEdgeWrapping,this.magFilter=void 0!==d.magFilter?d.magFilter:a.LinearFilter,this.minFilter=void 0!==d.minFilter?d.minFilter:a.LinearMipMapLinearFilter,this.anisotropy=void 0!==d.anisotropy?d.anisotropy:1,this.offset=new a.Vector2(0,0),this.repeat=new a.Vector2(1,1),this.format=void 0!==d.format?d.format:a.RGBAFormat,this.type=void 0!==d.type?d.type:a.UnsignedByteType,this.depthBuffer=void 0!==d.depthBuffer?d.depthBuffer:!0,this.stencilBuffer=void 0!==d.stencilBuffer?d.stencilBuffer:!0,this.generateMipmaps=!0,this.shareDepthFrom=null},a.WebGLRenderTarget.prototype={constructor:a.WebGLRenderTarget,setSize:function(a,b){this.width=a,this.height=b},clone:function(){var b=new a.WebGLRenderTarget(this.width,this.height);return b.wrapS=this.wrapS,b.wrapT=this.wrapT,b.magFilter=this.magFilter,b.minFilter=this.minFilter,b.anisotropy=this.anisotropy,b.offset.copy(this.offset),b.repeat.copy(this.repeat),b.format=this.format,b.type=this.type,b.depthBuffer=this.depthBuffer,b.stencilBuffer=this.stencilBuffer,b.generateMipmaps=this.generateMipmaps,b.shareDepthFrom=this.shareDepthFrom,b},dispose:function(){this.dispatchEvent({type:"dispose"})}},a.EventDispatcher.prototype.apply(a.WebGLRenderTarget.prototype),a.WebGLRenderTargetCube=function(b,c,d){a.WebGLRenderTarget.call(this,b,c,d),this.activeCubeFace=0},a.WebGLRenderTargetCube.prototype=Object.create(a.WebGLRenderTarget.prototype),a.WebGLExtensions=function(a){var b={};this.get=function(c){if(void 0!==b[c])return b[c];var d;switch(c){case"OES_texture_float":d=a.getExtension("OES_texture_float");break;case"OES_texture_float_linear":d=a.getExtension("OES_texture_float_linear");break;case"OES_standard_derivatives":d=a.getExtension("OES_standard_derivatives");break;case"EXT_texture_filter_anisotropic":d=a.getExtension("EXT_texture_filter_anisotropic")||a.getExtension("MOZ_EXT_texture_filter_anisotropic")||a.getExtension("WEBKIT_EXT_texture_filter_anisotropic");break;case"WEBGL_compressed_texture_s3tc":d=a.getExtension("WEBGL_compressed_texture_s3tc")||a.getExtension("MOZ_WEBGL_compressed_texture_s3tc")||a.getExtension("WEBKIT_WEBGL_compressed_texture_s3tc");break;case"WEBGL_compressed_texture_pvrtc":d=a.getExtension("WEBGL_compressed_texture_pvrtc")||a.getExtension("WEBKIT_WEBGL_compressed_texture_pvrtc");break;case"OES_element_index_uint":d=a.getExtension("OES_element_index_uint");break;case"EXT_blend_minmax":d=a.getExtension("EXT_blend_minmax");break;case"EXT_frag_depth":d=a.getExtension("EXT_frag_depth")}return null===d&&console.log("THREE.WebGLRenderer: "+c+" extension not supported."),b[c]=d,d}},a.WebGLProgram=function(){var b=0,c=function(a){var b,c,d=[];for(var e in a)b=a[e],b!==!1&&(c="#define "+e+" "+b,d.push(c));return d.join("\n")},d=function(a,b,c){for(var d={},e=0,f=c.length;f>e;e++){var g=c[e];d[g]=a.getUniformLocation(b,g)}return d},e=function(a,b,c){for(var d={},e=0,f=c.length;f>e;e++){var g=c[e];d[g]=a.getAttribLocation(b,g)}return d};return function(f,g,h,i){var j=f,k=j.context,l=h.defines,m=h.__webglShader.uniforms,n=h.attributes,o=h.__webglShader.vertexShader,p=h.__webglShader.fragmentShader,q=h.index0AttributeName;void 0===q&&i.morphTargets===!0&&(q="position");var r="SHADOWMAP_TYPE_BASIC";i.shadowMapType===a.PCFShadowMap?r="SHADOWMAP_TYPE_PCF":i.shadowMapType===a.PCFSoftShadowMap&&(r="SHADOWMAP_TYPE_PCF_SOFT");var s,t,u=c(l),v=k.createProgram();h instanceof a.RawShaderMaterial?(s="",t=""):(s=["precision "+i.precision+" float;","precision "+i.precision+" int;",u,i.supportsVertexTextures?"#define VERTEX_TEXTURES":"",j.gammaInput?"#define GAMMA_INPUT":"",j.gammaOutput?"#define GAMMA_OUTPUT":"","#define MAX_DIR_LIGHTS "+i.maxDirLights,"#define MAX_POINT_LIGHTS "+i.maxPointLights,"#define MAX_SPOT_LIGHTS "+i.maxSpotLights,"#define MAX_HEMI_LIGHTS "+i.maxHemiLights,"#define MAX_SHADOWS "+i.maxShadows,"#define MAX_BONES "+i.maxBones,i.map?"#define USE_MAP":"",i.envMap?"#define USE_ENVMAP":"",i.lightMap?"#define USE_LIGHTMAP":"",i.bumpMap?"#define USE_BUMPMAP":"",i.normalMap?"#define USE_NORMALMAP":"",i.specularMap?"#define USE_SPECULARMAP":"",i.alphaMap?"#define USE_ALPHAMAP":"",i.vertexColors?"#define USE_COLOR":"",i.skinning?"#define USE_SKINNING":"",i.useVertexTexture?"#define BONE_TEXTURE":"",i.morphTargets?"#define USE_MORPHTARGETS":"",i.morphNormals?"#define USE_MORPHNORMALS":"",i.wrapAround?"#define WRAP_AROUND":"",i.doubleSided?"#define DOUBLE_SIDED":"",i.flipSided?"#define FLIP_SIDED":"",i.shadowMapEnabled?"#define USE_SHADOWMAP":"",i.shadowMapEnabled?"#define "+r:"",i.shadowMapDebug?"#define SHADOWMAP_DEBUG":"",i.shadowMapCascade?"#define SHADOWMAP_CASCADE":"",i.sizeAttenuation?"#define USE_SIZEATTENUATION":"",i.logarithmicDepthBuffer?"#define USE_LOGDEPTHBUF":"","uniform mat4 modelMatrix;","uniform mat4 modelViewMatrix;","uniform mat4 projectionMatrix;","uniform mat4 viewMatrix;","uniform mat3 normalMatrix;","uniform vec3 cameraPosition;","attribute vec3 position;","attribute vec3 normal;","attribute vec2 uv;","attribute vec2 uv2;","#ifdef USE_COLOR"," attribute vec3 color;","#endif","#ifdef USE_MORPHTARGETS"," attribute vec3 morphTarget0;"," attribute vec3 morphTarget1;"," attribute vec3 morphTarget2;"," attribute vec3 morphTarget3;"," #ifdef USE_MORPHNORMALS","   attribute vec3 morphNormal0;","   attribute vec3 morphNormal1;","   attribute vec3 morphNormal2;","   attribute vec3 morphNormal3;"," #else","   attribute vec3 morphTarget4;","   attribute vec3 morphTarget5;","   attribute vec3 morphTarget6;","   attribute vec3 morphTarget7;"," #endif","#endif","#ifdef USE_SKINNING"," attribute vec4 skinIndex;"," attribute vec4 skinWeight;","#endif",""].join("\n"),t=["precision "+i.precision+" float;","precision "+i.precision+" int;",i.bumpMap||i.normalMap?"#extension GL_OES_standard_derivatives : enable":"",u,"#define MAX_DIR_LIGHTS "+i.maxDirLights,"#define MAX_POINT_LIGHTS "+i.maxPointLights,"#define MAX_SPOT_LIGHTS "+i.maxSpotLights,"#define MAX_HEMI_LIGHTS "+i.maxHemiLights,"#define MAX_SHADOWS "+i.maxShadows,i.alphaTest?"#define ALPHATEST "+i.alphaTest:"",j.gammaInput?"#define GAMMA_INPUT":"",j.gammaOutput?"#define GAMMA_OUTPUT":"",i.useFog&&i.fog?"#define USE_FOG":"",i.useFog&&i.fogExp?"#define FOG_EXP2":"",i.map?"#define USE_MAP":"",i.envMap?"#define USE_ENVMAP":"",i.lightMap?"#define USE_LIGHTMAP":"",i.bumpMap?"#define USE_BUMPMAP":"",i.normalMap?"#define USE_NORMALMAP":"",i.specularMap?"#define USE_SPECULARMAP":"",i.alphaMap?"#define USE_ALPHAMAP":"",i.vertexColors?"#define USE_COLOR":"",i.metal?"#define METAL":"",i.wrapAround?"#define WRAP_AROUND":"",i.doubleSided?"#define DOUBLE_SIDED":"",i.flipSided?"#define FLIP_SIDED":"",i.shadowMapEnabled?"#define USE_SHADOWMAP":"",i.shadowMapEnabled?"#define "+r:"",i.shadowMapDebug?"#define SHADOWMAP_DEBUG":"",i.shadowMapCascade?"#define SHADOWMAP_CASCADE":"",i.logarithmicDepthBuffer?"#define USE_LOGDEPTHBUF":"","uniform mat4 viewMatrix;","uniform vec3 cameraPosition;",""].join("\n"));var w=new a.WebGLShader(k,k.VERTEX_SHADER,s+o),x=new a.WebGLShader(k,k.FRAGMENT_SHADER,t+p);k.attachShader(v,w),k.attachShader(v,x),void 0!==q&&k.bindAttribLocation(v,0,q),k.linkProgram(v),k.getProgramParameter(v,k.LINK_STATUS)===!1&&(console.error("THREE.WebGLProgram: Could not initialise shader."),console.error("gl.VALIDATE_STATUS",k.getProgramParameter(v,k.VALIDATE_STATUS)),console.error("gl.getError()",k.getError())),""!==k.getProgramInfoLog(v)&&console.warn("THREE.WebGLProgram: gl.getProgramInfoLog()",k.getProgramInfoLog(v)),k.deleteShader(w),k.deleteShader(x);var y=["viewMatrix","modelViewMatrix","projectionMatrix","normalMatrix","modelMatrix","cameraPosition","morphTargetInfluences","bindMatrix","bindMatrixInverse"];i.useVertexTexture?(y.push("boneTexture"),y.push("boneTextureWidth"),y.push("boneTextureHeight")):y.push("boneGlobalMatrices"),i.logarithmicDepthBuffer&&y.push("logDepthBufFC");for(var z in m)y.push(z);this.uniforms=d(k,v,y),y=["position","normal","uv","uv2","tangent","color","skinIndex","skinWeight","lineDistance"];for(var A=0;A<i.maxMorphTargets;A++)y.push("morphTarget"+A);for(var A=0;A<i.maxMorphNormals;A++)y.push("morphNormal"+A);for(var B in n)y.push(B);return this.attributes=e(k,v,y),this.attributesKeys=Object.keys(this.attributes),this.id=b++,this.code=g,this.usedTimes=1,this.program=v,this.vertexShader=w,this.fragmentShader=x,this}}(),a.WebGLShader=function(){var a=function(a){for(var b=a.split("\n"),c=0;c<b.length;c++)b[c]=c+1+": "+b[c];return b.join("\n")};return function(b,c,d){var e=b.createShader(c);return b.shaderSource(e,d),b.compileShader(e),b.getShaderParameter(e,b.COMPILE_STATUS)===!1&&console.error("THREE.WebGLShader: Shader couldn't compile."),""!==b.getShaderInfoLog(e)&&(console.warn("THREE.WebGLShader: gl.getShaderInfoLog()",b.getShaderInfoLog(e)),console.warn(a(d))),e}}(),a.LensFlarePlugin=function(b,c){function d(a){var c=m.createProgram(),d=m.createShader(m.FRAGMENT_SHADER),e=m.createShader(m.VERTEX_SHADER),f="precision "+b.getPrecision()+" float;\n";return m.shaderSource(d,f+a.fragmentShader),m.shaderSource(e,f+a.vertexShader),m.compileShader(d),m.compileShader(e),m.attachShader(c,d),m.attachShader(c,e),m.linkProgram(c),c}var e,f,g,h,i,j,k,l,m=b.context,n=function(){var a=new Float32Array([-1,-1,0,0,1,-1,1,0,1,1,1,1,-1,1,0,1]),b=new Uint16Array([0,1,2,0,2,3]);e=m.createBuffer(),f=m.createBuffer(),m.bindBuffer(m.ARRAY_BUFFER,e),m.bufferData(m.ARRAY_BUFFER,a,m.STATIC_DRAW),m.bindBuffer(m.ELEMENT_ARRAY_BUFFER,f),m.bufferData(m.ELEMENT_ARRAY_BUFFER,b,m.STATIC_DRAW),k=m.createTexture(),l=m.createTexture(),m.bindTexture(m.TEXTURE_2D,k),m.texImage2D(m.TEXTURE_2D,0,m.RGB,16,16,0,m.RGB,m.UNSIGNED_BYTE,null),m.texParameteri(m.TEXTURE_2D,m.TEXTURE_WRAP_S,m.CLAMP_TO_EDGE),m.texParameteri(m.TEXTURE_2D,m.TEXTURE_WRAP_T,m.CLAMP_TO_EDGE),m.texParameteri(m.TEXTURE_2D,m.TEXTURE_MAG_FILTER,m.NEAREST),m.texParameteri(m.TEXTURE_2D,m.TEXTURE_MIN_FILTER,m.NEAREST),m.bindTexture(m.TEXTURE_2D,l),m.texImage2D(m.TEXTURE_2D,0,m.RGBA,16,16,0,m.RGBA,m.UNSIGNED_BYTE,null),m.texParameteri(m.TEXTURE_2D,m.TEXTURE_WRAP_S,m.CLAMP_TO_EDGE),m.texParameteri(m.TEXTURE_2D,m.TEXTURE_WRAP_T,m.CLAMP_TO_EDGE),m.texParameteri(m.TEXTURE_2D,m.TEXTURE_MAG_FILTER,m.NEAREST),m.texParameteri(m.TEXTURE_2D,m.TEXTURE_MIN_FILTER,m.NEAREST),j=m.getParameter(m.MAX_VERTEX_TEXTURE_IMAGE_UNITS)>0;var c;c=j?{vertexShader:["uniform lowp int renderType;","uniform vec3 screenPosition;","uniform vec2 scale;","uniform float rotation;","uniform sampler2D occlusionMap;","attribute vec2 position;","attribute vec2 uv;","varying vec2 vUV;","varying float vVisibility;","void main() {","vUV = uv;","vec2 pos = position;","if( renderType == 2 ) {","vec4 visibility = texture2D( occlusionMap, vec2( 0.1, 0.1 ) );","visibility += texture2D( occlusionMap, vec2( 0.5, 0.1 ) );","visibility += texture2D( occlusionMap, vec2( 0.9, 0.1 ) );","visibility += texture2D( occlusionMap, vec2( 0.9, 0.5 ) );","visibility += texture2D( occlusionMap, vec2( 0.9, 0.9 ) );","visibility += texture2D( occlusionMap, vec2( 0.5, 0.9 ) );","visibility += texture2D( occlusionMap, vec2( 0.1, 0.9 ) );","visibility += texture2D( occlusionMap, vec2( 0.1, 0.5 ) );","visibility += texture2D( occlusionMap, vec2( 0.5, 0.5 ) );","vVisibility =        visibility.r / 9.0;","vVisibility *= 1.0 - visibility.g / 9.0;","vVisibility *=       visibility.b / 9.0;","vVisibility *= 1.0 - visibility.a / 9.0;","pos.x = cos( rotation ) * position.x - sin( rotation ) * position.y;","pos.y = sin( rotation ) * position.x + cos( rotation ) * position.y;","}","gl_Position = vec4( ( pos * scale + screenPosition.xy ).xy, screenPosition.z, 1.0 );","}"].join("\n"),fragmentShader:["uniform lowp int renderType;","uniform sampler2D map;","uniform float opacity;","uniform vec3 color;","varying vec2 vUV;","varying float vVisibility;","void main() {","if( renderType == 0 ) {","gl_FragColor = vec4( 1.0, 0.0, 1.0, 0.0 );","} else if( renderType == 1 ) {","gl_FragColor = texture2D( map, vUV );","} else {","vec4 texture = texture2D( map, vUV );","texture.a *= opacity * vVisibility;","gl_FragColor = texture;","gl_FragColor.rgb *= color;","}","}"].join("\n")}:{vertexShader:["uniform lowp int renderType;","uniform vec3 screenPosition;","uniform vec2 scale;","uniform float rotation;","attribute vec2 position;","attribute vec2 uv;","varying vec2 vUV;","void main() {","vUV = uv;","vec2 pos = position;","if( renderType == 2 ) {","pos.x = cos( rotation ) * position.x - sin( rotation ) * position.y;","pos.y = sin( rotation ) * position.x + cos( rotation ) * position.y;","}","gl_Position = vec4( ( pos * scale + screenPosition.xy ).xy, screenPosition.z, 1.0 );","}"].join("\n"),fragmentShader:["precision mediump float;","uniform lowp int renderType;","uniform sampler2D map;","uniform sampler2D occlusionMap;","uniform float opacity;","uniform vec3 color;","varying vec2 vUV;","void main() {","if( renderType == 0 ) {","gl_FragColor = vec4( texture2D( map, vUV ).rgb, 0.0 );","} else if( renderType == 1 ) {","gl_FragColor = texture2D( map, vUV );","} else {","float visibility = texture2D( occlusionMap, vec2( 0.5, 0.1 ) ).a;","visibility += texture2D( occlusionMap, vec2( 0.9, 0.5 ) ).a;","visibility += texture2D( occlusionMap, vec2( 0.5, 0.9 ) ).a;","visibility += texture2D( occlusionMap, vec2( 0.1, 0.5 ) ).a;","visibility = ( 1.0 - visibility / 4.0 );","vec4 texture = texture2D( map, vUV );","texture.a *= opacity * visibility;","gl_FragColor = texture;","gl_FragColor.rgb *= color;","}","}"].join("\n")},g=d(c),h={vertex:m.getAttribLocation(g,"position"),uv:m.getAttribLocation(g,"uv")},i={renderType:m.getUniformLocation(g,"renderType"),map:m.getUniformLocation(g,"map"),occlusionMap:m.getUniformLocation(g,"occlusionMap"),opacity:m.getUniformLocation(g,"opacity"),color:m.getUniformLocation(g,"color"),scale:m.getUniformLocation(g,"scale"),rotation:m.getUniformLocation(g,"rotation"),screenPosition:m.getUniformLocation(g,"screenPosition")}};this.render=function(d,o,p,q){if(0!==c.length){var r=new a.Vector3,s=q/p,t=.5*p,u=.5*q,v=16/q,w=new a.Vector2(v*s,v),x=new a.Vector3(1,1,0),y=new a.Vector2(1,1);void 0===g&&n(),m.useProgram(g),m.enableVertexAttribArray(h.vertex),m.enableVertexAttribArray(h.uv),m.uniform1i(i.occlusionMap,0),m.uniform1i(i.map,1),m.bindBuffer(m.ARRAY_BUFFER,e),m.vertexAttribPointer(h.vertex,2,m.FLOAT,!1,16,0),m.vertexAttribPointer(h.uv,2,m.FLOAT,!1,16,8),m.bindBuffer(m.ELEMENT_ARRAY_BUFFER,f),m.disable(m.CULL_FACE),m.depthMask(!1);for(var z=0,A=c.length;A>z;z++){v=16/q,w.set(v*s,v);var B=c[z];if(r.set(B.matrixWorld.elements[12],B.matrixWorld.elements[13],B.matrixWorld.elements[14]),r.applyMatrix4(o.matrixWorldInverse),r.applyProjection(o.projectionMatrix),x.copy(r),y.x=x.x*t+t,y.y=x.y*u+u,j||y.x>0&&y.x<p&&y.y>0&&y.y<q){m.activeTexture(m.TEXTURE1),m.bindTexture(m.TEXTURE_2D,k),m.copyTexImage2D(m.TEXTURE_2D,0,m.RGB,y.x-8,y.y-8,16,16,0),m.uniform1i(i.renderType,0),m.uniform2f(i.scale,w.x,w.y),m.uniform3f(i.screenPosition,x.x,x.y,x.z),m.disable(m.BLEND),m.enable(m.DEPTH_TEST),m.drawElements(m.TRIANGLES,6,m.UNSIGNED_SHORT,0),m.activeTexture(m.TEXTURE0),m.bindTexture(m.TEXTURE_2D,l),m.copyTexImage2D(m.TEXTURE_2D,0,m.RGBA,y.x-8,y.y-8,16,16,0),m.uniform1i(i.renderType,1),m.disable(m.DEPTH_TEST),m.activeTexture(m.TEXTURE1),m.bindTexture(m.TEXTURE_2D,k),m.drawElements(m.TRIANGLES,6,m.UNSIGNED_SHORT,0),B.positionScreen.copy(x),B.customUpdateCallback?B.customUpdateCallback(B):B.updateLensFlares(),m.uniform1i(i.renderType,2),m.enable(m.BLEND);for(var C=0,D=B.lensFlares.length;D>C;C++){var E=B.lensFlares[C];E.opacity>.001&&E.scale>.001&&(x.x=E.x,x.y=E.y,x.z=E.z,v=E.size*E.scale/q,w.x=v*s,w.y=v,m.uniform3f(i.screenPosition,x.x,x.y,x.z),m.uniform2f(i.scale,w.x,w.y),m.uniform1f(i.rotation,E.rotation),m.uniform1f(i.opacity,E.opacity),m.uniform3f(i.color,E.color.r,E.color.g,E.color.b),b.setBlending(E.blending,E.blendEquation,E.blendSrc,E.blendDst),b.setTexture(E.texture,1),m.drawElements(m.TRIANGLES,6,m.UNSIGNED_SHORT,0))}}}m.enable(m.CULL_FACE),m.enable(m.DEPTH_TEST),m.depthMask(!0),b.resetGLState()}}},a.ShadowMapPlugin=function(b,c,d,e){function f(a,b,c){if(b.visible){var e=d[b.id];if(e&&b.castShadow&&(b.frustumCulled===!1||p.intersectsObject(b)===!0))for(var g=0,h=e.length;h>g;g++){var i=e[g];b._modelViewMatrix.multiplyMatrices(c.matrixWorldInverse,b.matrixWorld),u.push(i)}for(var g=0,h=b.children.length;h>g;g++)f(a,b.children[g],c)}}function g(b,c){var d=new a.DirectionalLight;d.isVirtual=!0,d.onlyShadow=!0,d.castShadow=!0,d.shadowCameraNear=b.shadowCameraNear,d.shadowCameraFar=b.shadowCameraFar,d.shadowCameraLeft=b.shadowCameraLeft,d.shadowCameraRight=b.shadowCameraRight,d.shadowCameraBottom=b.shadowCameraBottom,d.shadowCameraTop=b.shadowCameraTop,d.shadowCameraVisible=b.shadowCameraVisible,d.shadowDarkness=b.shadowDarkness,d.shadowBias=b.shadowCascadeBias[c],d.shadowMapWidth=b.shadowCascadeWidth[c],d.shadowMapHeight=b.shadowCascadeHeight[c],d.pointsWorld=[],d.pointsFrustum=[];for(var e=d.pointsWorld,f=d.pointsFrustum,g=0;8>g;g++)e[g]=new a.Vector3,f[g]=new a.Vector3;var h=b.shadowCascadeNearZ[c],i=b.shadowCascadeFarZ[c];return f[0].set(-1,-1,h),f[1].set(1,-1,h),f[2].set(-1,1,h),f[3].set(1,1,h),f[4].set(-1,-1,i),f[5].set(1,-1,i),f[6].set(-1,1,i),f[7].set(1,1,i),d}function h(a,b){var c=a.shadowCascadeArray[b];c.position.copy(a.position),c.target.position.copy(a.target.position),c.lookAt(c.target),c.shadowCameraVisible=a.shadowCameraVisible,c.shadowDarkness=a.shadowDarkness,c.shadowBias=a.shadowCascadeBias[b];var d=a.shadowCascadeNearZ[b],e=a.shadowCascadeFarZ[b],f=c.pointsFrustum;f[0].z=d,f[1].z=d,f[2].z=d,f[3].z=d,f[4].z=e,f[5].z=e,f[6].z=e,f[7].z=e}function i(a,b){var c=b.shadowCamera,d=b.pointsFrustum,e=b.pointsWorld;r.set(1/0,1/0,1/0),s.set(-(1/0),-(1/0),-(1/0));for(var f=0;8>f;f++){var g=e[f];g.copy(d[f]),g.unproject(a),g.applyMatrix4(c.matrixWorldInverse),g.x<r.x&&(r.x=g.x),g.x>s.x&&(s.x=g.x),g.y<r.y&&(r.y=g.y),g.y>s.y&&(s.y=g.y),g.z<r.z&&(r.z=g.z),g.z>s.z&&(s.z=g.z)}c.left=r.x,c.right=s.x,c.top=s.y,c.bottom=r.y,c.updateProjectionMatrix()}function j(b){return b.material instanceof a.MeshFaceMaterial?b.material.materials[0]:b.material}var k,l,m,n,o=b.context,p=new a.Frustum,q=new a.Matrix4,r=new a.Vector3,s=new a.Vector3,t=new a.Vector3,u=[],v=a.ShaderLib.depthRGBA,w=a.UniformsUtils.clone(v.uniforms);k=new a.ShaderMaterial({uniforms:w,vertexShader:v.vertexShader,fragmentShader:v.fragmentShader}),l=new a.ShaderMaterial({uniforms:w,vertexShader:v.vertexShader,fragmentShader:v.fragmentShader,morphTargets:!0}),m=new a.ShaderMaterial({uniforms:w,vertexShader:v.vertexShader,fragmentShader:v.fragmentShader,skinning:!0}),n=new a.ShaderMaterial({uniforms:w,vertexShader:v.vertexShader,fragmentShader:v.fragmentShader,morphTargets:!0,skinning:!0}),k._shadowPass=!0,l._shadowPass=!0,m._shadowPass=!0,n._shadowPass=!0,this.render=function(d,r){if(b.shadowMapEnabled!==!1){var s,v,w,x,y,z,A,B,C,D,E,F,G,H=[],I=0,J=null;for(o.clearColor(1,1,1,1),o.disable(o.BLEND),o.enable(o.CULL_FACE),o.frontFace(o.CCW),b.shadowMapCullFace===a.CullFaceFront?o.cullFace(o.FRONT):o.cullFace(o.BACK),b.setDepthTest(!0),s=0,v=c.length;v>s;s++)if(G=c[s],G.castShadow)if(G instanceof a.DirectionalLight&&G.shadowCascade)for(y=0;y<G.shadowCascadeCount;y++){var K;if(G.shadowCascadeArray[y])K=G.shadowCascadeArray[y];else{K=g(G,y),K.originalCamera=r;var L=new a.Gyroscope;L.position.copy(G.shadowCascadeOffset),L.add(K),L.add(K.target),r.add(L),G.shadowCascadeArray[y]=K,console.log("Created virtualLight",K)}h(G,y),H[I]=K,I++}else H[I]=G,I++;for(s=0,v=H.length;v>s;s++){if(G=H[s],!G.shadowMap){var M=a.LinearFilter;b.shadowMapType===a.PCFSoftShadowMap&&(M=a.NearestFilter);var N={minFilter:M,magFilter:M,format:a.RGBAFormat};G.shadowMap=new a.WebGLRenderTarget(G.shadowMapWidth,G.shadowMapHeight,N),G.shadowMapSize=new a.Vector2(G.shadowMapWidth,G.shadowMapHeight),G.shadowMatrix=new a.Matrix4}if(!G.shadowCamera){if(G instanceof a.SpotLight)G.shadowCamera=new a.PerspectiveCamera(G.shadowCameraFov,G.shadowMapWidth/G.shadowMapHeight,G.shadowCameraNear,G.shadowCameraFar);else{if(!(G instanceof a.DirectionalLight)){console.error("Unsupported light type for shadow");continue}G.shadowCamera=new a.OrthographicCamera(G.shadowCameraLeft,G.shadowCameraRight,G.shadowCameraTop,G.shadowCameraBottom,G.shadowCameraNear,G.shadowCameraFar)}d.add(G.shadowCamera),d.autoUpdate===!0&&d.updateMatrixWorld()}G.shadowCameraVisible&&!G.cameraHelper&&(G.cameraHelper=new a.CameraHelper(G.shadowCamera),d.add(G.cameraHelper)),G.isVirtual&&K.originalCamera==r&&i(r,G),z=G.shadowMap,A=G.shadowMatrix,B=G.shadowCamera,B.position.setFromMatrixPosition(G.matrixWorld),t.setFromMatrixPosition(G.target.matrixWorld),B.lookAt(t),B.updateMatrixWorld(),B.matrixWorldInverse.getInverse(B.matrixWorld),G.cameraHelper&&(G.cameraHelper.visible=G.shadowCameraVisible),G.shadowCameraVisible&&G.cameraHelper.update(),A.set(.5,0,0,.5,0,.5,0,.5,0,0,.5,.5,0,0,0,1),A.multiply(B.projectionMatrix),A.multiply(B.matrixWorldInverse),q.multiplyMatrices(B.projectionMatrix,B.matrixWorldInverse),p.setFromMatrix(q),b.setRenderTarget(z),b.clear(),u.length=0,f(d,d,B);var O,P,Q;for(w=0,x=u.length;x>w;w++)E=u[w],F=E.object,C=E.buffer,O=j(F),P=void 0!==F.geometry.morphTargets&&F.geometry.morphTargets.length>0&&O.morphTargets,Q=F instanceof a.SkinnedMesh&&O.skinning,D=F.customDepthMaterial?F.customDepthMaterial:Q?P?n:m:P?l:k,b.setMaterialFaces(O),C instanceof a.BufferGeometry?b.renderBufferDirect(B,c,J,D,C,F):b.renderBuffer(B,c,J,D,C,F);for(w=0,x=e.length;x>w;w++)E=e[w],F=E.object,F.visible&&F.castShadow&&(F._modelViewMatrix.multiplyMatrices(B.matrixWorldInverse,F.matrixWorld),b.renderImmediateObject(B,c,J,k,F))}var R=b.getClearColor(),S=b.getClearAlpha();o.clearColor(R.r,R.g,R.b,S),o.enable(o.BLEND),b.shadowMapCullFace===a.CullFaceFront&&o.cullFace(o.BACK),b.resetGLState()}}},a.SpritePlugin=function(b,c){function d(){var a=l.createProgram(),c=l.createShader(l.VERTEX_SHADER),d=l.createShader(l.FRAGMENT_SHADER);return l.shaderSource(c,["precision "+b.getPrecision()+" float;","uniform mat4 modelViewMatrix;","uniform mat4 projectionMatrix;","uniform float rotation;","uniform vec2 scale;","uniform vec2 uvOffset;","uniform vec2 uvScale;","attribute vec2 position;","attribute vec2 uv;","varying vec2 vUV;","void main() {","vUV = uvOffset + uv * uvScale;","vec2 alignedPosition = position * scale;","vec2 rotatedPosition;","rotatedPosition.x = cos( rotation ) * alignedPosition.x - sin( rotation ) * alignedPosition.y;","rotatedPosition.y = sin( rotation ) * alignedPosition.x + cos( rotation ) * alignedPosition.y;","vec4 finalPosition;","finalPosition = modelViewMatrix * vec4( 0.0, 0.0, 0.0, 1.0 );","finalPosition.xy += rotatedPosition;","finalPosition = projectionMatrix * finalPosition;","gl_Position = finalPosition;","}"].join("\n")),l.shaderSource(d,["precision "+b.getPrecision()+" float;","uniform vec3 color;","uniform sampler2D map;","uniform float opacity;","uniform int fogType;","uniform vec3 fogColor;","uniform float fogDensity;","uniform float fogNear;","uniform float fogFar;","uniform float alphaTest;","varying vec2 vUV;","void main() {","vec4 texture = texture2D( map, vUV );","if ( texture.a < alphaTest ) discard;","gl_FragColor = vec4( color * texture.xyz, texture.a * opacity );","if ( fogType > 0 ) {","float depth = gl_FragCoord.z / gl_FragCoord.w;","float fogFactor = 0.0;","if ( fogType == 1 ) {","fogFactor = smoothstep( fogNear, fogFar, depth );","} else {","const float LOG2 = 1.442695;","float fogFactor = exp2( - fogDensity * fogDensity * depth * depth * LOG2 );","fogFactor = 1.0 - clamp( fogFactor, 0.0, 1.0 );","}","gl_FragColor = mix( gl_FragColor, vec4( fogColor, gl_FragColor.w ), fogFactor );","}","}"].join("\n")),l.compileShader(c),l.compileShader(d),l.attachShader(a,c),l.attachShader(a,d),l.linkProgram(a),a}function e(a,b){return a.z!==b.z?b.z-a.z:b.id-a.id}var f,g,h,i,j,k,l=b.context,m=function(){var b=new Float32Array([-.5,-.5,0,0,.5,-.5,1,0,.5,.5,1,1,-.5,.5,0,1]),c=new Uint16Array([0,1,2,0,2,3]);f=l.createBuffer(),g=l.createBuffer(),l.bindBuffer(l.ARRAY_BUFFER,f),l.bufferData(l.ARRAY_BUFFER,b,l.STATIC_DRAW),l.bindBuffer(l.ELEMENT_ARRAY_BUFFER,g),l.bufferData(l.ELEMENT_ARRAY_BUFFER,c,l.STATIC_DRAW),h=d(),i={position:l.getAttribLocation(h,"position"),uv:l.getAttribLocation(h,"uv")},j={uvOffset:l.getUniformLocation(h,"uvOffset"),uvScale:l.getUniformLocation(h,"uvScale"),rotation:l.getUniformLocation(h,"rotation"),scale:l.getUniformLocation(h,"scale"),color:l.getUniformLocation(h,"color"),map:l.getUniformLocation(h,"map"),opacity:l.getUniformLocation(h,"opacity"),modelViewMatrix:l.getUniformLocation(h,"modelViewMatrix"),projectionMatrix:l.getUniformLocation(h,"projectionMatrix"),fogType:l.getUniformLocation(h,"fogType"),fogDensity:l.getUniformLocation(h,"fogDensity"),fogNear:l.getUniformLocation(h,"fogNear"),fogFar:l.getUniformLocation(h,"fogFar"),fogColor:l.getUniformLocation(h,"fogColor"),alphaTest:l.getUniformLocation(h,"alphaTest")};var e=document.createElement("canvas");e.width=8,e.height=8;var m=e.getContext("2d");m.fillStyle="white",m.fillRect(0,0,8,8),k=new a.Texture(e),k.needsUpdate=!0};this.render=function(d,n){if(0!==c.length){void 0===h&&m(),l.useProgram(h),l.enableVertexAttribArray(i.position),l.enableVertexAttribArray(i.uv),l.disable(l.CULL_FACE),l.enable(l.BLEND),l.bindBuffer(l.ARRAY_BUFFER,f),l.vertexAttribPointer(i.position,2,l.FLOAT,!1,16,0),l.vertexAttribPointer(i.uv,2,l.FLOAT,!1,16,8),l.bindBuffer(l.ELEMENT_ARRAY_BUFFER,g),l.uniformMatrix4fv(j.projectionMatrix,!1,n.projectionMatrix.elements),l.activeTexture(l.TEXTURE0),l.uniform1i(j.map,0);var o=0,p=0,q=d.fog;q?(l.uniform3f(j.fogColor,q.color.r,q.color.g,q.color.b),q instanceof a.Fog?(l.uniform1f(j.fogNear,q.near),l.uniform1f(j.fogFar,q.far),l.uniform1i(j.fogType,1),o=1,p=1):q instanceof a.FogExp2&&(l.uniform1f(j.fogDensity,q.density),l.uniform1i(j.fogType,2),o=2,p=2)):(l.uniform1i(j.fogType,0),o=0,p=0);for(var r=0,s=c.length;s>r;r++){var t=c[r];t._modelViewMatrix.multiplyMatrices(n.matrixWorldInverse,t.matrixWorld),null===t.renderDepth?t.z=-t._modelViewMatrix.elements[14]:t.z=t.renderDepth}c.sort(e);for(var u=[],r=0,s=c.length;s>r;r++){var t=c[r],v=t.material;l.uniform1f(j.alphaTest,v.alphaTest),l.uniformMatrix4fv(j.modelViewMatrix,!1,t._modelViewMatrix.elements),u[0]=t.scale.x,u[1]=t.scale.y;var w=0;d.fog&&v.fog&&(w=p),o!==w&&(l.uniform1i(j.fogType,w),o=w),null!==v.map?(l.uniform2f(j.uvOffset,v.map.offset.x,v.map.offset.y),l.uniform2f(j.uvScale,v.map.repeat.x,v.map.repeat.y)):(l.uniform2f(j.uvOffset,0,0),l.uniform2f(j.uvScale,1,1)),l.uniform1f(j.opacity,v.opacity),l.uniform3f(j.color,v.color.r,v.color.g,v.color.b),l.uniform1f(j.rotation,v.rotation),l.uniform2fv(j.scale,u),b.setBlending(v.blending,v.blendEquation,v.blendSrc,v.blendDst),b.setDepthTest(v.depthTest),b.setDepthWrite(v.depthWrite),v.map&&v.map.image&&v.map.image.width?b.setTexture(v.map,0):b.setTexture(k,0),l.drawElements(l.TRIANGLES,6,l.UNSIGNED_SHORT,0)}l.enable(l.CULL_FACE),b.resetGLState()}}},a.GeometryUtils={merge:function(b,c,d){console.warn("THREE.GeometryUtils: .merge() has been moved to Geometry. Use geometry.merge( geometry2, matrix, materialIndexOffset ) instead.");var e;c instanceof a.Mesh&&(c.matrixAutoUpdate&&c.updateMatrix(),e=c.matrix,c=c.geometry),b.merge(c,e,d)},center:function(a){return console.warn("THREE.GeometryUtils: .center() has been moved to Geometry. Use geometry.center() instead."),a.center()}},a.ImageUtils={crossOrigin:void 0,loadTexture:function(b,c,d,e){var f=new a.ImageLoader;f.crossOrigin=this.crossOrigin;var g=new a.Texture(void 0,c);return f.load(b,function(a){g.image=a,g.needsUpdate=!0,d&&d(g)},void 0,function(a){e&&e(a)}),g.sourceFile=b,g},loadTextureCube:function(b,c,d,e){var f=[],g=new a.ImageLoader;g.crossOrigin=this.crossOrigin;var h=new a.CubeTexture(f,c);h.flipY=!1;for(var i=0,j=function(a){g.load(b[a],function(b){h.images[a]=b,i+=1,6===i&&(h.needsUpdate=!0,d&&d(h))})},k=0,l=b.length;l>k;++k)j(k);return h},loadCompressedTexture:function(){console.error("THREE.ImageUtils.loadCompressedTexture has been removed. Use THREE.DDSLoader instead.")},loadCompressedTextureCube:function(){console.error("THREE.ImageUtils.loadCompressedTextureCube has been removed. Use THREE.DDSLoader instead.")},getNormalMap:function(a,b){var c=function(a,b){return[a[1]*b[2]-a[2]*b[1],a[2]*b[0]-a[0]*b[2],a[0]*b[1]-a[1]*b[0]]},d=function(a,b){return[a[0]-b[0],a[1]-b[1],a[2]-b[2]]},e=function(a){var b=Math.sqrt(a[0]*a[0]+a[1]*a[1]+a[2]*a[2]);return[a[0]/b,a[1]/b,a[2]/b]};b=1|b;var f=a.width,g=a.height,h=document.createElement("canvas");h.width=f,h.height=g;var i=h.getContext("2d");i.drawImage(a,0,0);for(var j=i.getImageData(0,0,f,g).data,k=i.createImageData(f,g),l=k.data,m=0;f>m;m++)for(var n=0;g>n;n++){var o=0>n-1?0:n-1,p=n+1>g-1?g-1:n+1,q=0>m-1?0:m-1,r=m+1>f-1?f-1:m+1,s=[],t=[0,0,j[4*(n*f+m)]/255*b];s.push([-1,0,j[4*(n*f+q)]/255*b]),s.push([-1,-1,j[4*(o*f+q)]/255*b]),s.push([0,-1,j[4*(o*f+m)]/255*b]),s.push([1,-1,j[4*(o*f+r)]/255*b]),s.push([1,0,j[4*(n*f+r)]/255*b]),s.push([1,1,j[4*(p*f+r)]/255*b]),s.push([0,1,j[4*(p*f+m)]/255*b]),s.push([-1,1,j[4*(p*f+q)]/255*b]);for(var u=[],v=s.length,w=0;v>w;w++){var x=s[w],y=s[(w+1)%v];x=d(x,t),y=d(y,t),u.push(e(c(x,y)))}for(var z=[0,0,0],w=0;w<u.length;w++)z[0]+=u[w][0],z[1]+=u[w][1],z[2]+=u[w][2];
z[0]/=u.length,z[1]/=u.length,z[2]/=u.length;var A=4*(n*f+m);l[A]=(z[0]+1)/2*255|0,l[A+1]=(z[1]+1)/2*255|0,l[A+2]=255*z[2]|0,l[A+3]=255}return i.putImageData(k,0,0),h},generateDataTexture:function(b,c,d){for(var e=b*c,f=new Uint8Array(3*e),g=Math.floor(255*d.r),h=Math.floor(255*d.g),i=Math.floor(255*d.b),j=0;e>j;j++)f[3*j]=g,f[3*j+1]=h,f[3*j+2]=i;var k=new a.DataTexture(f,b,c,a.RGBFormat);return k.needsUpdate=!0,k}},a.SceneUtils={createMultiMaterialObject:function(b,c){for(var d=new a.Object3D,e=0,f=c.length;f>e;e++)d.add(new a.Mesh(b,c[e]));return d},detach:function(a,b,c){a.applyMatrix(b.matrixWorld),b.remove(a),c.add(a)},attach:function(b,c,d){var e=new a.Matrix4;e.getInverse(d.matrixWorld),b.applyMatrix(e),c.remove(b),d.add(b)}},a.FontUtils={faces:{},face:"helvetiker",weight:"normal",style:"normal",size:150,divisions:10,getFace:function(){try{return this.faces[this.face][this.weight][this.style]}catch(a){throw"The font "+this.face+" with "+this.weight+" weight and "+this.style+" style is missing."}},loadFace:function(a){var b=a.familyName.toLowerCase(),c=this;c.faces[b]=c.faces[b]||{},c.faces[b][a.cssFontWeight]=c.faces[b][a.cssFontWeight]||{},c.faces[b][a.cssFontWeight][a.cssFontStyle]=a;c.faces[b][a.cssFontWeight][a.cssFontStyle]=a;return a},drawText:function(b){var c,d=this.getFace(),e=this.size/d.resolution,f=0,g=String(b).split(""),h=g.length,i=[];for(c=0;h>c;c++){var j=new a.Path,k=this.extractGlyphPoints(g[c],d,e,f,j);f+=k.offset,i.push(k.path)}var l=f/2;return{paths:i,offset:l}},extractGlyphPoints:function(b,c,d,e,f){var g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z=[],A=c.glyphs[b]||c.glyphs["?"];if(A){if(A.o)for(j=A._cachedOutline||(A._cachedOutline=A.o.split(" ")),l=j.length,m=d,n=d,g=0;l>g;)switch(k=j[g++]){case"m":o=j[g++]*m+e,p=j[g++]*n,f.moveTo(o,p);break;case"l":o=j[g++]*m+e,p=j[g++]*n,f.lineTo(o,p);break;case"q":if(q=j[g++]*m+e,r=j[g++]*n,u=j[g++]*m+e,v=j[g++]*n,f.quadraticCurveTo(u,v,q,r),y=z[z.length-1])for(s=y.x,t=y.y,h=1,i=this.divisions;i>=h;h++){var B=h/i;a.Shape.Utils.b2(B,s,u,q),a.Shape.Utils.b2(B,t,v,r)}break;case"b":if(q=j[g++]*m+e,r=j[g++]*n,u=j[g++]*m+e,v=j[g++]*n,w=j[g++]*m+e,x=j[g++]*n,f.bezierCurveTo(u,v,w,x,q,r),y=z[z.length-1])for(s=y.x,t=y.y,h=1,i=this.divisions;i>=h;h++){var B=h/i;a.Shape.Utils.b3(B,s,u,w,q),a.Shape.Utils.b3(B,t,v,x,r)}}return{offset:A.ha*d,path:f}}}},a.FontUtils.generateShapes=function(b,c){c=c||{};var d=void 0!==c.size?c.size:100,e=void 0!==c.curveSegments?c.curveSegments:4,f=void 0!==c.font?c.font:"helvetiker",g=void 0!==c.weight?c.weight:"normal",h=void 0!==c.style?c.style:"normal";a.FontUtils.size=d,a.FontUtils.divisions=e,a.FontUtils.face=f,a.FontUtils.weight=g,a.FontUtils.style=h;for(var i=a.FontUtils.drawText(b),j=i.paths,k=[],l=0,m=j.length;m>l;l++)Array.prototype.push.apply(k,j[l].toShapes());return k},function(a){var b=1e-10,c=function(a,b){var c=a.length;if(3>c)return null;var f,g,h,i=[],j=[],k=[];if(d(a)>0)for(g=0;c>g;g++)j[g]=g;else for(g=0;c>g;g++)j[g]=c-1-g;var l=c,m=2*l;for(g=l-1;l>2;){if(m--<=0)return console.log("Warning, unable to triangulate polygon!"),b?k:i;if(f=g,f>=l&&(f=0),g=f+1,g>=l&&(g=0),h=g+1,h>=l&&(h=0),e(a,f,g,h,l,j)){var n,o,p,q,r;for(n=j[f],o=j[g],p=j[h],i.push([a[n],a[o],a[p]]),k.push([j[f],j[g],j[h]]),q=g,r=g+1;l>r;q++,r++)j[q]=j[r];l--,m=2*l}}return b?k:i},d=function(a){for(var b=a.length,c=0,d=b-1,e=0;b>e;d=e++)c+=a[d].x*a[e].y-a[e].x*a[d].y;return.5*c},e=function(a,c,d,e,f,g){var h,i,j,k,l,m,n,o,p;if(i=a[g[c]].x,j=a[g[c]].y,k=a[g[d]].x,l=a[g[d]].y,m=a[g[e]].x,n=a[g[e]].y,b>(k-i)*(n-j)-(l-j)*(m-i))return!1;var q,r,s,t,u,v,w,x,y,z,A,B,C,D,E;for(q=m-k,r=n-l,s=i-m,t=j-n,u=k-i,v=l-j,h=0;f>h;h++)if(o=a[g[h]].x,p=a[g[h]].y,!(o===i&&p===j||o===k&&p===l||o===m&&p===n)&&(w=o-i,x=p-j,y=o-k,z=p-l,A=o-m,B=p-n,E=q*z-r*y,C=u*x-v*w,D=s*B-t*A,E>=-b&&D>=-b&&C>=-b))return!1;return!0};return a.Triangulate=c,a.Triangulate.area=d,a}(a.FontUtils),self._typeface_js={faces:a.FontUtils.faces,loadFace:a.FontUtils.loadFace},a.typeface_js=self._typeface_js,a.Audio=function(b){a.Object3D.call(this),this.type="Audio",this.context=b.context,this.source=this.context.createBufferSource(),this.gain=this.context.createGain(),this.gain.connect(this.context.destination),this.panner=this.context.createPanner(),this.panner.connect(this.gain)},a.Audio.prototype=Object.create(a.Object3D.prototype),a.Audio.prototype.load=function(a){var b=this,c=new XMLHttpRequest;return c.open("GET",a,!0),c.responseType="arraybuffer",c.onload=function(a){b.context.decodeAudioData(this.response,function(a){b.source.buffer=a,b.source.connect(b.panner),b.source.start(0)})},c.send(),this},a.Audio.prototype.setLoop=function(a){this.source.loop=a},a.Audio.prototype.setRefDistance=function(a){this.panner.refDistance=a},a.Audio.prototype.setRolloffFactor=function(a){this.panner.rolloffFactor=a},a.Audio.prototype.updateMatrixWorld=function(){var b=new a.Vector3;return function(c){a.Object3D.prototype.updateMatrixWorld.call(this,c),b.setFromMatrixPosition(this.matrixWorld),this.panner.setPosition(b.x,b.y,b.z)}}(),a.AudioListener=function(){a.Object3D.call(this),this.type="AudioListener",this.context=new(window.AudioContext||window.webkitAudioContext)},a.AudioListener.prototype=Object.create(a.Object3D.prototype),a.AudioListener.prototype.updateMatrixWorld=function(){var b=new a.Vector3,c=new a.Quaternion,d=new a.Vector3,e=new a.Vector3,f=new a.Vector3,g=new a.Vector3;return function(h){a.Object3D.prototype.updateMatrixWorld.call(this,h);var i=this.context.listener;this.matrixWorld.decompose(b,c,d),e.set(0,0,-1).applyQuaternion(c),f.subVectors(b,g),i.setPosition(b.x,b.y,b.z),i.setOrientation(e.x,e.y,e.z,this.up.x,this.up.y,this.up.z),i.setVelocity(f.x,f.y,f.z),g.copy(b)}}(),a.Curve=function(){},a.Curve.prototype.getPoint=function(a){return console.log("Warning, getPoint() not implemented!"),null},a.Curve.prototype.getPointAt=function(a){var b=this.getUtoTmapping(a);return this.getPoint(b)},a.Curve.prototype.getPoints=function(a){a||(a=5);var b,c=[];for(b=0;a>=b;b++)c.push(this.getPoint(b/a));return c},a.Curve.prototype.getSpacedPoints=function(a){a||(a=5);var b,c=[];for(b=0;a>=b;b++)c.push(this.getPointAt(b/a));return c},a.Curve.prototype.getLength=function(){var a=this.getLengths();return a[a.length-1]},a.Curve.prototype.getLengths=function(a){if(a||(a=this.__arcLengthDivisions?this.__arcLengthDivisions:200),this.cacheArcLengths&&this.cacheArcLengths.length==a+1&&!this.needsUpdate)return this.cacheArcLengths;this.needsUpdate=!1;var b,c,d=[],e=this.getPoint(0),f=0;for(d.push(0),c=1;a>=c;c++)b=this.getPoint(c/a),f+=b.distanceTo(e),d.push(f),e=b;return this.cacheArcLengths=d,d},a.Curve.prototype.updateArcLengths=function(){this.needsUpdate=!0,this.getLengths()},a.Curve.prototype.getUtoTmapping=function(a,b){var c,d=this.getLengths(),e=0,f=d.length;c=b?b:a*d[f-1];for(var g,h=0,i=f-1;i>=h;)if(e=Math.floor(h+(i-h)/2),g=d[e]-c,0>g)h=e+1;else{if(!(g>0)){i=e;break}i=e-1}if(e=i,d[e]==c){var j=e/(f-1);return j}var k=d[e],l=d[e+1],m=l-k,n=(c-k)/m,j=(e+n)/(f-1);return j},a.Curve.prototype.getTangent=function(a){var b=1e-4,c=a-b,d=a+b;0>c&&(c=0),d>1&&(d=1);var e=this.getPoint(c),f=this.getPoint(d),g=f.clone().sub(e);return g.normalize()},a.Curve.prototype.getTangentAt=function(a){var b=this.getUtoTmapping(a);return this.getTangent(b)},a.Curve.Utils={tangentQuadraticBezier:function(a,b,c,d){return 2*(1-a)*(c-b)+2*a*(d-c)},tangentCubicBezier:function(a,b,c,d,e){return-3*b*(1-a)*(1-a)+3*c*(1-a)*(1-a)-6*a*c*(1-a)+6*a*d*(1-a)-3*a*a*d+3*a*a*e},tangentSpline:function(a,b,c,d,e){var f=6*a*a-6*a,g=3*a*a-4*a+1,h=-6*a*a+6*a,i=3*a*a-2*a;return f+g+h+i},interpolate:function(a,b,c,d,e){var f=.5*(c-a),g=.5*(d-b),h=e*e,i=e*h;return(2*b-2*c+f+g)*i+(-3*b+3*c-2*f-g)*h+f*e+b}},a.Curve.create=function(b,c){return b.prototype=Object.create(a.Curve.prototype),b.prototype.getPoint=c,b},a.CurvePath=function(){this.curves=[],this.bends=[],this.autoClose=!1},a.CurvePath.prototype=Object.create(a.Curve.prototype),a.CurvePath.prototype.add=function(a){this.curves.push(a)},a.CurvePath.prototype.checkConnection=function(){},a.CurvePath.prototype.closePath=function(){var b=this.curves[0].getPoint(0),c=this.curves[this.curves.length-1].getPoint(1);b.equals(c)||this.curves.push(new a.LineCurve(c,b))},a.CurvePath.prototype.getPoint=function(a){for(var b,c,d=a*this.getLength(),e=this.getCurveLengths(),f=0;f<e.length;){if(e[f]>=d){b=e[f]-d,c=this.curves[f];var g=1-b/c.getLength();return c.getPointAt(g)}f++}return null},a.CurvePath.prototype.getLength=function(){var a=this.getCurveLengths();return a[a.length-1]},a.CurvePath.prototype.getCurveLengths=function(){if(this.cacheLengths&&this.cacheLengths.length==this.curves.length)return this.cacheLengths;var a,b=[],c=0,d=this.curves.length;for(a=0;d>a;a++)c+=this.curves[a].getLength(),b.push(c);return this.cacheLengths=b,b},a.CurvePath.prototype.getBoundingBox=function(){var b,c,d,e,f,g,h=this.getPoints();b=c=Number.NEGATIVE_INFINITY,e=f=Number.POSITIVE_INFINITY;var i,j,k,l,m=h[0]instanceof a.Vector3;for(l=m?new a.Vector3:new a.Vector2,j=0,k=h.length;k>j;j++)i=h[j],i.x>b?b=i.x:i.x<e&&(e=i.x),i.y>c?c=i.y:i.y<f&&(f=i.y),m&&(i.z>d?d=i.z:i.z<g&&(g=i.z)),l.add(i);var n={minX:e,minY:f,maxX:b,maxY:c};return m&&(n.maxZ=d,n.minZ=g),n},a.CurvePath.prototype.createPointsGeometry=function(a){var b=this.getPoints(a,!0);return this.createGeometry(b)},a.CurvePath.prototype.createSpacedPointsGeometry=function(a){var b=this.getSpacedPoints(a,!0);return this.createGeometry(b)},a.CurvePath.prototype.createGeometry=function(b){for(var c=new a.Geometry,d=0;d<b.length;d++)c.vertices.push(new a.Vector3(b[d].x,b[d].y,b[d].z||0));return c},a.CurvePath.prototype.addWrapPath=function(a){this.bends.push(a)},a.CurvePath.prototype.getTransformedPoints=function(a,b){var c,d,e=this.getPoints(a);for(b||(b=this.bends),c=0,d=b.length;d>c;c++)e=this.getWrapPoints(e,b[c]);return e},a.CurvePath.prototype.getTransformedSpacedPoints=function(a,b){var c,d,e=this.getSpacedPoints(a);for(b||(b=this.bends),c=0,d=b.length;d>c;c++)e=this.getWrapPoints(e,b[c]);return e},a.CurvePath.prototype.getWrapPoints=function(a,b){var c,d,e,f,g,h,i=this.getBoundingBox();for(c=0,d=a.length;d>c;c++){e=a[c],f=e.x,g=e.y,h=f/i.maxX,h=b.getUtoTmapping(h,f);var j=b.getPoint(h),k=b.getTangent(h);k.set(-k.y,k.x).multiplyScalar(g),e.x=j.x+k.x,e.y=j.y+k.y}return a},a.Gyroscope=function(){a.Object3D.call(this)},a.Gyroscope.prototype=Object.create(a.Object3D.prototype),a.Gyroscope.prototype.updateMatrixWorld=function(){var b=new a.Vector3,c=new a.Quaternion,d=new a.Vector3,e=new a.Vector3,f=new a.Quaternion,g=new a.Vector3;return function(a){this.matrixAutoUpdate&&this.updateMatrix(),(this.matrixWorldNeedsUpdate||a)&&(this.parent?(this.matrixWorld.multiplyMatrices(this.parent.matrixWorld,this.matrix),this.matrixWorld.decompose(e,f,g),this.matrix.decompose(b,c,d),this.matrixWorld.compose(e,c,g)):this.matrixWorld.copy(this.matrix),this.matrixWorldNeedsUpdate=!1,a=!0);for(var h=0,i=this.children.length;i>h;h++)this.children[h].updateMatrixWorld(a)}}(),a.Path=function(b){a.CurvePath.call(this),this.actions=[],b&&this.fromPoints(b)},a.Path.prototype=Object.create(a.CurvePath.prototype),a.PathActions={MOVE_TO:"moveTo",LINE_TO:"lineTo",QUADRATIC_CURVE_TO:"quadraticCurveTo",BEZIER_CURVE_TO:"bezierCurveTo",CSPLINE_THRU:"splineThru",ARC:"arc",ELLIPSE:"ellipse"},a.Path.prototype.fromPoints=function(a){this.moveTo(a[0].x,a[0].y);for(var b=1,c=a.length;c>b;b++)this.lineTo(a[b].x,a[b].y)},a.Path.prototype.moveTo=function(b,c){var d=Array.prototype.slice.call(arguments);this.actions.push({action:a.PathActions.MOVE_TO,args:d})},a.Path.prototype.lineTo=function(b,c){var d=Array.prototype.slice.call(arguments),e=this.actions[this.actions.length-1].args,f=e[e.length-2],g=e[e.length-1],h=new a.LineCurve(new a.Vector2(f,g),new a.Vector2(b,c));this.curves.push(h),this.actions.push({action:a.PathActions.LINE_TO,args:d})},a.Path.prototype.quadraticCurveTo=function(b,c,d,e){var f=Array.prototype.slice.call(arguments),g=this.actions[this.actions.length-1].args,h=g[g.length-2],i=g[g.length-1],j=new a.QuadraticBezierCurve(new a.Vector2(h,i),new a.Vector2(b,c),new a.Vector2(d,e));this.curves.push(j),this.actions.push({action:a.PathActions.QUADRATIC_CURVE_TO,args:f})},a.Path.prototype.bezierCurveTo=function(b,c,d,e,f,g){var h=Array.prototype.slice.call(arguments),i=this.actions[this.actions.length-1].args,j=i[i.length-2],k=i[i.length-1],l=new a.CubicBezierCurve(new a.Vector2(j,k),new a.Vector2(b,c),new a.Vector2(d,e),new a.Vector2(f,g));this.curves.push(l),this.actions.push({action:a.PathActions.BEZIER_CURVE_TO,args:h})},a.Path.prototype.splineThru=function(b){var c=Array.prototype.slice.call(arguments),d=this.actions[this.actions.length-1].args,e=d[d.length-2],f=d[d.length-1],g=[new a.Vector2(e,f)];Array.prototype.push.apply(g,b);var h=new a.SplineCurve(g);this.curves.push(h),this.actions.push({action:a.PathActions.CSPLINE_THRU,args:c})},a.Path.prototype.arc=function(a,b,c,d,e,f){var g=this.actions[this.actions.length-1].args,h=g[g.length-2],i=g[g.length-1];this.absarc(a+h,b+i,c,d,e,f)},a.Path.prototype.absarc=function(a,b,c,d,e,f){this.absellipse(a,b,c,c,d,e,f)},a.Path.prototype.ellipse=function(a,b,c,d,e,f,g){var h=this.actions[this.actions.length-1].args,i=h[h.length-2],j=h[h.length-1];this.absellipse(a+i,b+j,c,d,e,f,g)},a.Path.prototype.absellipse=function(b,c,d,e,f,g,h){var i=Array.prototype.slice.call(arguments),j=new a.EllipseCurve(b,c,d,e,f,g,h);this.curves.push(j);var k=j.getPoint(1);i.push(k.x),i.push(k.y),this.actions.push({action:a.PathActions.ELLIPSE,args:i})},a.Path.prototype.getSpacedPoints=function(a,b){a||(a=40);for(var c=[],d=0;a>d;d++)c.push(this.getPoint(d/a));return c},a.Path.prototype.getPoints=function(b,c){if(this.useSpacedPoints)return console.log("tata"),this.getSpacedPoints(b,c);b=b||12;var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v=[];for(d=0,e=this.actions.length;e>d;d++)switch(f=this.actions[d],g=f.action,h=f.args,g){case a.PathActions.MOVE_TO:v.push(new a.Vector2(h[0],h[1]));break;case a.PathActions.LINE_TO:v.push(new a.Vector2(h[0],h[1]));break;case a.PathActions.QUADRATIC_CURVE_TO:for(i=h[2],j=h[3],m=h[0],n=h[1],v.length>0?(q=v[v.length-1],o=q.x,p=q.y):(q=this.actions[d-1].args,o=q[q.length-2],p=q[q.length-1]),r=1;b>=r;r++)s=r/b,t=a.Shape.Utils.b2(s,o,m,i),u=a.Shape.Utils.b2(s,p,n,j),v.push(new a.Vector2(t,u));break;case a.PathActions.BEZIER_CURVE_TO:for(i=h[4],j=h[5],m=h[0],n=h[1],k=h[2],l=h[3],v.length>0?(q=v[v.length-1],o=q.x,p=q.y):(q=this.actions[d-1].args,o=q[q.length-2],p=q[q.length-1]),r=1;b>=r;r++)s=r/b,t=a.Shape.Utils.b3(s,o,m,k,i),u=a.Shape.Utils.b3(s,p,n,l,j),v.push(new a.Vector2(t,u));break;case a.PathActions.CSPLINE_THRU:q=this.actions[d-1].args;var w=new a.Vector2(q[q.length-2],q[q.length-1]),x=[w],y=b*h[0].length;x=x.concat(h[0]);var z=new a.SplineCurve(x);for(r=1;y>=r;r++)v.push(z.getPointAt(r/y));break;case a.PathActions.ARC:var A,B=h[0],C=h[1],D=h[2],E=h[3],F=h[4],G=!!h[5],H=F-E,I=2*b;for(r=1;I>=r;r++)s=r/I,G||(s=1-s),A=E+s*H,t=B+D*Math.cos(A),u=C+D*Math.sin(A),v.push(new a.Vector2(t,u));break;case a.PathActions.ELLIPSE:var A,B=h[0],C=h[1],J=h[2],K=h[3],E=h[4],F=h[5],G=!!h[6],H=F-E,I=2*b;for(r=1;I>=r;r++)s=r/I,G||(s=1-s),A=E+s*H,t=B+J*Math.cos(A),u=C+K*Math.sin(A),v.push(new a.Vector2(t,u))}var L=v[v.length-1],M=1e-10;return Math.abs(L.x-v[0].x)<M&&Math.abs(L.y-v[0].y)<M&&v.splice(v.length-1,1),c&&v.push(v[0]),v},a.Path.prototype.toShapes=function(b,c){function d(b){var c,d,e,f,g,h=[],i=new a.Path;for(c=0,d=b.length;d>c;c++)e=b[c],g=e.args,f=e.action,f==a.PathActions.MOVE_TO&&0!=i.actions.length&&(h.push(i),i=new a.Path),i[f].apply(i,g);return 0!=i.actions.length&&h.push(i),h}function e(b){for(var c=[],d=0,e=b.length;e>d;d++){var f=b[d],g=new a.Shape;g.actions=f.actions,g.curves=f.curves,c.push(g)}return c}function f(a,b){for(var c=1e-10,d=b.length,e=!1,f=d-1,g=0;d>g;f=g++){var h=b[f],i=b[g],j=i.x-h.x,k=i.y-h.y;if(Math.abs(k)>c){if(0>k&&(h=b[g],j=-j,i=b[f],k=-k),a.y<h.y||a.y>i.y)continue;if(a.y==h.y){if(a.x==h.x)return!0}else{var l=k*(a.x-h.x)-j*(a.y-h.y);if(0==l)return!0;if(0>l)continue;e=!e}}else{if(a.y!=h.y)continue;if(i.x<=a.x&&a.x<=h.x||h.x<=a.x&&a.x<=i.x)return!0}}return e}var g=d(this.actions);if(0==g.length)return[];if(c===!0)return e(g);var h,i,j,k=[];if(1==g.length)return i=g[0],j=new a.Shape,j.actions=i.actions,j.curves=i.curves,k.push(j),k;var l=!a.Shape.Utils.isClockWise(g[0].getPoints());l=b?!l:l;var m,n=[],o=[],p=[],q=0;o[q]=void 0,p[q]=[];var r,s;for(r=0,s=g.length;s>r;r++)i=g[r],m=i.getPoints(),h=a.Shape.Utils.isClockWise(m),h=b?!h:h,h?(!l&&o[q]&&q++,o[q]={s:new a.Shape,p:m},o[q].s.actions=i.actions,o[q].s.curves=i.curves,l&&q++,p[q]=[]):p[q].push({h:i,p:m[0]});if(!o[0])return e(g);if(o.length>1){for(var t=!1,u=[],v=0,w=o.length;w>v;v++)n[v]=[];for(var v=0,w=o.length;w>v;v++)for(var x=(o[v],p[v]),y=0;y<x.length;y++){for(var z=x[y],A=!0,B=0;B<o.length;B++)f(z.p,o[B].p)&&(v!=B&&u.push({froms:v,tos:B,hole:y}),A?(A=!1,n[B].push(z)):t=!0);A&&n[v].push(z)}u.length>0&&(t||(p=n))}var C,D,E;for(r=0,s=o.length;s>r;r++)for(j=o[r].s,k.push(j),C=p[r],D=0,E=C.length;E>D;D++)j.holes.push(C[D].h);return k},a.Shape=function(){a.Path.apply(this,arguments),this.holes=[]},a.Shape.prototype=Object.create(a.Path.prototype),a.Shape.prototype.extrude=function(b){var c=new a.ExtrudeGeometry(this,b);return c},a.Shape.prototype.makeGeometry=function(b){var c=new a.ShapeGeometry(this,b);return c},a.Shape.prototype.getPointsHoles=function(a){var b,c=this.holes.length,d=[];for(b=0;c>b;b++)d[b]=this.holes[b].getTransformedPoints(a,this.bends);return d},a.Shape.prototype.getSpacedPointsHoles=function(a){var b,c=this.holes.length,d=[];for(b=0;c>b;b++)d[b]=this.holes[b].getTransformedSpacedPoints(a,this.bends);return d},a.Shape.prototype.extractAllPoints=function(a){return{shape:this.getTransformedPoints(a),holes:this.getPointsHoles(a)}},a.Shape.prototype.extractPoints=function(a){return this.useSpacedPoints?this.extractAllSpacedPoints(a):this.extractAllPoints(a)},a.Shape.prototype.extractAllSpacedPoints=function(a){return{shape:this.getTransformedSpacedPoints(a),holes:this.getSpacedPointsHoles(a)}},a.Shape.Utils={triangulateShape:function(b,c){function d(a,b,c){return a.x!=b.x?a.x<b.x?a.x<=c.x&&c.x<=b.x:b.x<=c.x&&c.x<=a.x:a.y<b.y?a.y<=c.y&&c.y<=b.y:b.y<=c.y&&c.y<=a.y}function e(a,b,c,e,f){var g=1e-10,h=b.x-a.x,i=b.y-a.y,j=e.x-c.x,k=e.y-c.y,l=a.x-c.x,m=a.y-c.y,n=i*j-h*k,o=i*l-h*m;if(Math.abs(n)>g){var p;if(n>0){if(0>o||o>n)return[];if(p=k*l-j*m,0>p||p>n)return[]}else{if(o>0||n>o)return[];if(p=k*l-j*m,p>0||n>p)return[]}if(0==p)return!f||0!=o&&o!=n?[a]:[];if(p==n)return!f||0!=o&&o!=n?[b]:[];if(0==o)return[c];if(o==n)return[e];var q=p/n;return[{x:a.x+q*h,y:a.y+q*i}]}if(0!=o||k*l!=j*m)return[];var r=0==h&&0==i,s=0==j&&0==k;if(r&&s)return a.x!=c.x||a.y!=c.y?[]:[a];if(r)return d(c,e,a)?[a]:[];if(s)return d(a,b,c)?[c]:[];var t,u,v,w,x,y,z,A;return 0!=h?(a.x<b.x?(t=a,v=a.x,u=b,w=b.x):(t=b,v=b.x,u=a,w=a.x),c.x<e.x?(x=c,z=c.x,y=e,A=e.x):(x=e,z=e.x,y=c,A=c.x)):(a.y<b.y?(t=a,v=a.y,u=b,w=b.y):(t=b,v=b.y,u=a,w=a.y),c.y<e.y?(x=c,z=c.y,y=e,A=e.y):(x=e,z=e.y,y=c,A=c.y)),z>=v?z>w?[]:w==z?f?[]:[x]:A>=w?[x,u]:[x,y]:v>A?[]:v==A?f?[]:[t]:A>=w?[t,u]:[t,y]}function f(a,b,c,d){var e=1e-10,f=b.x-a.x,g=b.y-a.y,h=c.x-a.x,i=c.y-a.y,j=d.x-a.x,k=d.y-a.y,l=f*i-g*h,m=f*k-g*j;if(Math.abs(l)>e){var n=j*i-k*h;return l>0?m>=0&&n>=0:m>=0||n>=0}return m>0}function g(a,b){function c(a,b){var c=s.length-1,d=a-1;0>d&&(d=c);var e=a+1;e>c&&(e=0);var g=f(s[a],s[d],s[e],h[b]);if(!g)return!1;var i=h.length-1,j=b-1;0>j&&(j=i);var k=b+1;return k>i&&(k=0),g=f(h[b],h[j],h[k],s[a]),g?!0:!1}function d(a,b){var c,d,f;for(c=0;c<s.length;c++)if(d=c+1,d%=s.length,f=e(a,b,s[c],s[d],!0),f.length>0)return!0;return!1}function g(a,c){var d,f,g,h,i;for(d=0;d<t.length;d++)for(f=b[t[d]],g=0;g<f.length;g++)if(h=g+1,h%=f.length,i=e(a,c,f[g],f[h],!0),i.length>0)return!0;return!1}for(var h,i,j,k,l,m,n,o,p,q,r,s=a.concat(),t=[],u=[],v=0,w=b.length;w>v;v++)t.push(v);for(var x=0,y=2*t.length;t.length>0;){if(y--,0>y){console.log("Infinite Loop! Holes left:"+t.length+", Probably Hole outside Shape!");break}for(j=x;j<s.length;j++){k=s[j],i=-1;for(var v=0;v<t.length;v++)if(m=t[v],n=k.x+":"+k.y+":"+m,void 0===u[n]){h=b[m];for(var z=0;z<h.length;z++)if(l=h[z],c(j,z)&&!d(k,l)&&!g(k,l)){i=z,t.splice(v,1),o=s.slice(0,j+1),p=s.slice(j),q=h.slice(i),r=h.slice(0,i+1),s=o.concat(q).concat(r).concat(p),x=j;break}if(i>=0)break;u[n]=!0}if(i>=0)break}}return s}for(var h,i,j,k,l,m,n={},o=b.concat(),p=0,q=c.length;q>p;p++)Array.prototype.push.apply(o,c[p]);for(h=0,i=o.length;i>h;h++)l=o[h].x+":"+o[h].y,void 0!==n[l]&&console.log("Duplicate point",l),n[l]=h;var r=g(b,c),s=a.FontUtils.Triangulate(r,!1);for(h=0,i=s.length;i>h;h++)for(k=s[h],j=0;3>j;j++)l=k[j].x+":"+k[j].y,m=n[l],void 0!==m&&(k[j]=m);return s.concat()},isClockWise:function(b){return a.FontUtils.Triangulate.area(b)<0},b2p0:function(a,b){var c=1-a;return c*c*b},b2p1:function(a,b){return 2*(1-a)*a*b},b2p2:function(a,b){return a*a*b},b2:function(a,b,c,d){return this.b2p0(a,b)+this.b2p1(a,c)+this.b2p2(a,d)},b3p0:function(a,b){var c=1-a;return c*c*c*b},b3p1:function(a,b){var c=1-a;return 3*c*c*a*b},b3p2:function(a,b){var c=1-a;return 3*c*a*a*b},b3p3:function(a,b){return a*a*a*b},b3:function(a,b,c,d,e){return this.b3p0(a,b)+this.b3p1(a,c)+this.b3p2(a,d)+this.b3p3(a,e)}},a.LineCurve=function(a,b){this.v1=a,this.v2=b},a.LineCurve.prototype=Object.create(a.Curve.prototype),a.LineCurve.prototype.getPoint=function(a){var b=this.v2.clone().sub(this.v1);return b.multiplyScalar(a).add(this.v1),b},a.LineCurve.prototype.getPointAt=function(a){return this.getPoint(a)},a.LineCurve.prototype.getTangent=function(a){var b=this.v2.clone().sub(this.v1);return b.normalize()},a.QuadraticBezierCurve=function(a,b,c){this.v0=a,this.v1=b,this.v2=c},a.QuadraticBezierCurve.prototype=Object.create(a.Curve.prototype),a.QuadraticBezierCurve.prototype.getPoint=function(b){var c=new a.Vector2;return c.x=a.Shape.Utils.b2(b,this.v0.x,this.v1.x,this.v2.x),c.y=a.Shape.Utils.b2(b,this.v0.y,this.v1.y,this.v2.y),c},a.QuadraticBezierCurve.prototype.getTangent=function(b){var c=new a.Vector2;return c.x=a.Curve.Utils.tangentQuadraticBezier(b,this.v0.x,this.v1.x,this.v2.x),c.y=a.Curve.Utils.tangentQuadraticBezier(b,this.v0.y,this.v1.y,this.v2.y),c.normalize()},a.CubicBezierCurve=function(a,b,c,d){this.v0=a,this.v1=b,this.v2=c,this.v3=d},a.CubicBezierCurve.prototype=Object.create(a.Curve.prototype),a.CubicBezierCurve.prototype.getPoint=function(b){var c,d;return c=a.Shape.Utils.b3(b,this.v0.x,this.v1.x,this.v2.x,this.v3.x),d=a.Shape.Utils.b3(b,this.v0.y,this.v1.y,this.v2.y,this.v3.y),new a.Vector2(c,d)},a.CubicBezierCurve.prototype.getTangent=function(b){var c,d;c=a.Curve.Utils.tangentCubicBezier(b,this.v0.x,this.v1.x,this.v2.x,this.v3.x),d=a.Curve.Utils.tangentCubicBezier(b,this.v0.y,this.v1.y,this.v2.y,this.v3.y);var e=new a.Vector2(c,d);return e.normalize(),e},a.SplineCurve=function(a){this.points=void 0==a?[]:a},a.SplineCurve.prototype=Object.create(a.Curve.prototype),a.SplineCurve.prototype.getPoint=function(b){var c=this.points,d=(c.length-1)*b,e=Math.floor(d),f=d-e,g=c[0==e?e:e-1],h=c[e],i=c[e>c.length-2?c.length-1:e+1],j=c[e>c.length-3?c.length-1:e+2],k=new a.Vector2;return k.x=a.Curve.Utils.interpolate(g.x,h.x,i.x,j.x,f),k.y=a.Curve.Utils.interpolate(g.y,h.y,i.y,j.y,f),k},a.EllipseCurve=function(a,b,c,d,e,f,g){this.aX=a,this.aY=b,this.xRadius=c,this.yRadius=d,this.aStartAngle=e,this.aEndAngle=f,this.aClockwise=g},a.EllipseCurve.prototype=Object.create(a.Curve.prototype),a.EllipseCurve.prototype.getPoint=function(b){var c=this.aEndAngle-this.aStartAngle;0>c&&(c+=2*Math.PI),c>2*Math.PI&&(c-=2*Math.PI);var d;d=this.aClockwise===!0?this.aEndAngle+(1-b)*(2*Math.PI-c):this.aStartAngle+b*c;var e=new a.Vector2;return e.x=this.aX+this.xRadius*Math.cos(d),e.y=this.aY+this.yRadius*Math.sin(d),e},a.ArcCurve=function(b,c,d,e,f,g){a.EllipseCurve.call(this,b,c,d,d,e,f,g)},a.ArcCurve.prototype=Object.create(a.EllipseCurve.prototype),a.LineCurve3=a.Curve.create(function(a,b){this.v1=a,this.v2=b},function(b){var c=new a.Vector3;return c.subVectors(this.v2,this.v1),c.multiplyScalar(b),c.add(this.v1),c}),a.QuadraticBezierCurve3=a.Curve.create(function(a,b,c){this.v0=a,this.v1=b,this.v2=c},function(b){var c=new a.Vector3;return c.x=a.Shape.Utils.b2(b,this.v0.x,this.v1.x,this.v2.x),c.y=a.Shape.Utils.b2(b,this.v0.y,this.v1.y,this.v2.y),c.z=a.Shape.Utils.b2(b,this.v0.z,this.v1.z,this.v2.z),c}),a.CubicBezierCurve3=a.Curve.create(function(a,b,c,d){this.v0=a,this.v1=b,this.v2=c,this.v3=d},function(b){var c=new a.Vector3;return c.x=a.Shape.Utils.b3(b,this.v0.x,this.v1.x,this.v2.x,this.v3.x),c.y=a.Shape.Utils.b3(b,this.v0.y,this.v1.y,this.v2.y,this.v3.y),c.z=a.Shape.Utils.b3(b,this.v0.z,this.v1.z,this.v2.z,this.v3.z),c}),a.SplineCurve3=a.Curve.create(function(a){this.points=void 0==a?[]:a},function(b){var c=this.points,d=(c.length-1)*b,e=Math.floor(d),f=d-e,g=c[0==e?e:e-1],h=c[e],i=c[e>c.length-2?c.length-1:e+1],j=c[e>c.length-3?c.length-1:e+2],k=new a.Vector3;return k.x=a.Curve.Utils.interpolate(g.x,h.x,i.x,j.x,f),k.y=a.Curve.Utils.interpolate(g.y,h.y,i.y,j.y,f),k.z=a.Curve.Utils.interpolate(g.z,h.z,i.z,j.z,f),k}),a.ClosedSplineCurve3=a.Curve.create(function(a){this.points=void 0==a?[]:a},function(b){var c=this.points,d=(c.length-0)*b,e=Math.floor(d),f=d-e;e+=e>0?0:(Math.floor(Math.abs(e)/c.length)+1)*c.length;var g=c[(e-1)%c.length],h=c[e%c.length],i=c[(e+1)%c.length],j=c[(e+2)%c.length],k=new a.Vector3;return k.x=a.Curve.Utils.interpolate(g.x,h.x,i.x,j.x,f),k.y=a.Curve.Utils.interpolate(g.y,h.y,i.y,j.y,f),k.z=a.Curve.Utils.interpolate(g.z,h.z,i.z,j.z,f),k}),a.AnimationHandler={LINEAR:0,CATMULLROM:1,CATMULLROM_FORWARD:2,add:function(){console.warn("THREE.AnimationHandler.add() has been deprecated.")},get:function(){console.warn("THREE.AnimationHandler.get() has been deprecated.")},remove:function(){console.warn("THREE.AnimationHandler.remove() has been deprecated.")},animations:[],init:function(b){if(b.initialized!==!0){for(var c=0;c<b.hierarchy.length;c++){for(var d=0;d<b.hierarchy[c].keys.length;d++)if(b.hierarchy[c].keys[d].time<0&&(b.hierarchy[c].keys[d].time=0),void 0!==b.hierarchy[c].keys[d].rot&&!(b.hierarchy[c].keys[d].rot instanceof a.Quaternion)){var e=b.hierarchy[c].keys[d].rot;b.hierarchy[c].keys[d].rot=(new a.Quaternion).fromArray(e)}if(b.hierarchy[c].keys.length&&void 0!==b.hierarchy[c].keys[0].morphTargets){for(var f={},d=0;d<b.hierarchy[c].keys.length;d++)for(var g=0;g<b.hierarchy[c].keys[d].morphTargets.length;g++){var h=b.hierarchy[c].keys[d].morphTargets[g];f[h]=-1}b.hierarchy[c].usedMorphTargets=f;for(var d=0;d<b.hierarchy[c].keys.length;d++){var i={};for(var h in f){for(var g=0;g<b.hierarchy[c].keys[d].morphTargets.length;g++)if(b.hierarchy[c].keys[d].morphTargets[g]===h){i[h]=b.hierarchy[c].keys[d].morphTargetsInfluences[g];break}g===b.hierarchy[c].keys[d].morphTargets.length&&(i[h]=0)}b.hierarchy[c].keys[d].morphTargetsInfluences=i}}for(var d=1;d<b.hierarchy[c].keys.length;d++)b.hierarchy[c].keys[d].time===b.hierarchy[c].keys[d-1].time&&(b.hierarchy[c].keys.splice(d,1),d--);for(var d=0;d<b.hierarchy[c].keys.length;d++)b.hierarchy[c].keys[d].index=d}return b.initialized=!0,b}},parse:function(b){var c=function(a,b){b.push(a);for(var d=0;d<a.children.length;d++)c(a.children[d],b)},d=[];if(b instanceof a.SkinnedMesh)for(var e=0;e<b.skeleton.bones.length;e++)d.push(b.skeleton.bones[e]);else c(b,d);return d},play:function(a){-1===this.animations.indexOf(a)&&this.animations.push(a)},stop:function(a){var b=this.animations.indexOf(a);-1!==b&&this.animations.splice(b,1)},update:function(a){for(var b=0;b<this.animations.length;b++)this.animations[b].resetBlendWeights();for(var b=0;b<this.animations.length;b++)this.animations[b].update(a)}},a.Animation=function(b,c){this.root=b,this.data=a.AnimationHandler.init(c),this.hierarchy=a.AnimationHandler.parse(b),this.currentTime=0,this.timeScale=1,this.isPlaying=!1,this.loop=!0,this.weight=0,this.interpolationType=a.AnimationHandler.LINEAR},a.Animation.prototype.keyTypes=["pos","rot","scl"],a.Animation.prototype.play=function(b,c){this.currentTime=void 0!==b?b:0,this.weight=void 0!==c?c:1,this.isPlaying=!0,this.reset(),a.AnimationHandler.play(this)},a.Animation.prototype.stop=function(){this.isPlaying=!1,a.AnimationHandler.stop(this)},a.Animation.prototype.reset=function(){for(var a=0,b=this.hierarchy.length;b>a;a++){var c=this.hierarchy[a];c.matrixAutoUpdate=!0,void 0===c.animationCache&&(c.animationCache={animations:{},blending:{positionWeight:0,quaternionWeight:0,scaleWeight:0}}),void 0===c.animationCache.animations[this.data.name]&&(c.animationCache.animations[this.data.name]={},c.animationCache.animations[this.data.name].prevKey={pos:0,rot:0,scl:0},c.animationCache.animations[this.data.name].nextKey={pos:0,rot:0,scl:0},c.animationCache.animations[this.data.name].originalMatrix=c.matrix);for(var d=c.animationCache.animations[this.data.name],e=0;3>e;e++){for(var f=this.keyTypes[e],g=this.data.hierarchy[a].keys[0],h=this.getNextKeyWith(f,a,1);h.time<this.currentTime&&h.index>g.index;)g=h,h=this.getNextKeyWith(f,a,h.index+1);d.prevKey[f]=g,d.nextKey[f]=h}}},a.Animation.prototype.resetBlendWeights=function(){for(var a=0,b=this.hierarchy.length;b>a;a++){var c=this.hierarchy[a];void 0!==c.animationCache&&(c.animationCache.blending.positionWeight=0,c.animationCache.blending.quaternionWeight=0,c.animationCache.blending.scaleWeight=0)}},a.Animation.prototype.update=function(){var b=[],c=new a.Vector3,d=new a.Vector3,e=new a.Quaternion,f=function(a,b){var c,d,e,f,h,i,j,k,l,m=[],n=[];return c=(a.length-1)*b,d=Math.floor(c),e=c-d,m[0]=0===d?d:d-1,m[1]=d,m[2]=d>a.length-2?d:d+1,m[3]=d>a.length-3?d:d+2,i=a[m[0]],j=a[m[1]],k=a[m[2]],l=a[m[3]],f=e*e,h=e*f,n[0]=g(i[0],j[0],k[0],l[0],e,f,h),n[1]=g(i[1],j[1],k[1],l[1],e,f,h),n[2]=g(i[2],j[2],k[2],l[2],e,f,h),n},g=function(a,b,c,d,e,f,g){var h=.5*(c-a),i=.5*(d-b);return(2*(b-c)+h+i)*g+(-3*(b-c)-2*h-i)*f+h*e+b};return function(g){if(this.isPlaying!==!1&&(this.currentTime+=g*this.timeScale,0!==this.weight)){var h=this.data.length;if(this.currentTime>h||this.currentTime<0){if(!this.loop)return void this.stop();this.currentTime%=h,this.currentTime<0&&(this.currentTime+=h),this.reset()}for(var i=0,j=this.hierarchy.length;j>i;i++)for(var k=this.hierarchy[i],l=k.animationCache.animations[this.data.name],m=k.animationCache.blending,n=0;3>n;n++){var o=this.keyTypes[n],p=l.prevKey[o],q=l.nextKey[o];if(this.timeScale>0&&q.time<=this.currentTime||this.timeScale<0&&p.time>=this.currentTime){for(p=this.data.hierarchy[i].keys[0],q=this.getNextKeyWith(o,i,1);q.time<this.currentTime&&q.index>p.index;)p=q,q=this.getNextKeyWith(o,i,q.index+1);l.prevKey[o]=p,l.nextKey[o]=q}k.matrixAutoUpdate=!0,k.matrixWorldNeedsUpdate=!0;var r=(this.currentTime-p.time)/(q.time-p.time),s=p[o],t=q[o];if(0>r&&(r=0),r>1&&(r=1),"pos"===o){if(this.interpolationType===a.AnimationHandler.LINEAR){d.x=s[0]+(t[0]-s[0])*r,d.y=s[1]+(t[1]-s[1])*r,d.z=s[2]+(t[2]-s[2])*r;var u=this.weight/(this.weight+m.positionWeight);k.position.lerp(d,u),m.positionWeight+=this.weight}else if(this.interpolationType===a.AnimationHandler.CATMULLROM||this.interpolationType===a.AnimationHandler.CATMULLROM_FORWARD){b[0]=this.getPrevKeyWith("pos",i,p.index-1).pos,b[1]=s,b[2]=t,b[3]=this.getNextKeyWith("pos",i,q.index+1).pos,r=.33*r+.33;var v=f(b,r),u=this.weight/(this.weight+m.positionWeight);m.positionWeight+=this.weight;var w=k.position;if(w.x=w.x+(v[0]-w.x)*u,w.y=w.y+(v[1]-w.y)*u,w.z=w.z+(v[2]-w.z)*u,this.interpolationType===a.AnimationHandler.CATMULLROM_FORWARD){var x=f(b,1.01*r);c.set(x[0],x[1],x[2]),c.sub(w),c.y=0,c.normalize();var y=Math.atan2(c.x,c.z);k.rotation.set(0,y,0)}}}else if("rot"===o)if(a.Quaternion.slerp(s,t,e,r),0===m.quaternionWeight)k.quaternion.copy(e),m.quaternionWeight=this.weight;else{var u=this.weight/(this.weight+m.quaternionWeight);a.Quaternion.slerp(k.quaternion,e,k.quaternion,u),m.quaternionWeight+=this.weight}else if("scl"===o){d.x=s[0]+(t[0]-s[0])*r,d.y=s[1]+(t[1]-s[1])*r,d.z=s[2]+(t[2]-s[2])*r;
var u=this.weight/(this.weight+m.scaleWeight);k.scale.lerp(d,u),m.scaleWeight+=this.weight}}return!0}}}(),a.Animation.prototype.getNextKeyWith=function(b,c,d){var e=this.data.hierarchy[c].keys;for(this.interpolationType===a.AnimationHandler.CATMULLROM||this.interpolationType===a.AnimationHandler.CATMULLROM_FORWARD?d=d<e.length-1?d:e.length-1:d%=e.length;d<e.length;d++)if(void 0!==e[d][b])return e[d];return this.data.hierarchy[c].keys[0]},a.Animation.prototype.getPrevKeyWith=function(b,c,d){var e=this.data.hierarchy[c].keys;for(d=this.interpolationType===a.AnimationHandler.CATMULLROM||this.interpolationType===a.AnimationHandler.CATMULLROM_FORWARD?d>0?d:0:d>=0?d:d+e.length;d>=0;d--)if(void 0!==e[d][b])return e[d];return this.data.hierarchy[c].keys[e.length-1]},a.KeyFrameAnimation=function(b){this.root=b.node,this.data=a.AnimationHandler.init(b),this.hierarchy=a.AnimationHandler.parse(this.root),this.currentTime=0,this.timeScale=.001,this.isPlaying=!1,this.isPaused=!0,this.loop=!0;for(var c=0,d=this.hierarchy.length;d>c;c++){var e=this.data.hierarchy[c].keys,f=this.data.hierarchy[c].sids,g=this.hierarchy[c];if(e.length&&f){for(var h=0;h<f.length;h++){var i=f[h],j=this.getNextKeyWith(i,c,0);j&&j.apply(i)}g.matrixAutoUpdate=!1,this.data.hierarchy[c].node.updateMatrix(),g.matrixWorldNeedsUpdate=!0}}},a.KeyFrameAnimation.prototype.play=function(b){if(this.currentTime=void 0!==b?b:0,this.isPlaying===!1){this.isPlaying=!0;var c,d,e,f=this.hierarchy.length;for(c=0;f>c;c++){d=this.hierarchy[c],e=this.data.hierarchy[c],void 0===e.animationCache&&(e.animationCache={},e.animationCache.prevKey=null,e.animationCache.nextKey=null,e.animationCache.originalMatrix=d.matrix);var g=this.data.hierarchy[c].keys;g.length&&(e.animationCache.prevKey=g[0],e.animationCache.nextKey=g[1],this.startTime=Math.min(g[0].time,this.startTime),this.endTime=Math.max(g[g.length-1].time,this.endTime))}this.update(0)}this.isPaused=!1,a.AnimationHandler.play(this)},a.KeyFrameAnimation.prototype.stop=function(){this.isPlaying=!1,this.isPaused=!1,a.AnimationHandler.stop(this);for(var b=0;b<this.data.hierarchy.length;b++){var c=this.hierarchy[b],d=this.data.hierarchy[b];if(void 0!==d.animationCache){var e=d.animationCache.originalMatrix;e.copy(c.matrix),c.matrix=e,delete d.animationCache}}},a.KeyFrameAnimation.prototype.update=function(a){if(this.isPlaying!==!1){this.currentTime+=a*this.timeScale;var b=this.data.length;this.loop===!0&&this.currentTime>b&&(this.currentTime%=b),this.currentTime=Math.min(this.currentTime,b);for(var c=0,d=this.hierarchy.length;d>c;c++){var e=this.hierarchy[c],f=this.data.hierarchy[c],g=f.keys,h=f.animationCache;if(g.length){var i=h.prevKey,j=h.nextKey;if(j.time<=this.currentTime){for(;j.time<this.currentTime&&j.index>i.index;)i=j,j=g[i.index+1];h.prevKey=i,h.nextKey=j}j.time>=this.currentTime?i.interpolate(j,this.currentTime):i.interpolate(j,j.time),this.data.hierarchy[c].node.updateMatrix(),e.matrixWorldNeedsUpdate=!0}}}},a.KeyFrameAnimation.prototype.getNextKeyWith=function(a,b,c){var d=this.data.hierarchy[b].keys;for(c%=d.length;c<d.length;c++)if(d[c].hasTarget(a))return d[c];return d[0]},a.KeyFrameAnimation.prototype.getPrevKeyWith=function(a,b,c){var d=this.data.hierarchy[b].keys;for(c=c>=0?c:c+d.length;c>=0;c--)if(d[c].hasTarget(a))return d[c];return d[d.length-1]},a.MorphAnimation=function(a){this.mesh=a,this.frames=a.morphTargetInfluences.length,this.currentTime=0,this.duration=1e3,this.loop=!0,this.isPlaying=!1},a.MorphAnimation.prototype={play:function(){this.isPlaying=!0},pause:function(){this.isPlaying=!1},update:function(){var a=0,b=0;return function(c){if(this.isPlaying!==!1){this.currentTime+=c,this.loop===!0&&this.currentTime>this.duration&&(this.currentTime%=this.duration),this.currentTime=Math.min(this.currentTime,this.duration);var d=this.duration/this.frames,e=Math.floor(this.currentTime/d);e!=b&&(this.mesh.morphTargetInfluences[a]=0,this.mesh.morphTargetInfluences[b]=1,this.mesh.morphTargetInfluences[e]=0,a=b,b=e),this.mesh.morphTargetInfluences[e]=this.currentTime%d/d,this.mesh.morphTargetInfluences[a]=1-this.mesh.morphTargetInfluences[e]}}}()},a.BoxGeometry=function(b,c,d,e,f,g){function h(b,c,d,e,f,g,h,j){var k,l,m,n=i.widthSegments,o=i.heightSegments,p=f/2,q=g/2,r=i.vertices.length;"x"===b&&"y"===c||"y"===b&&"x"===c?k="z":"x"===b&&"z"===c||"z"===b&&"x"===c?(k="y",o=i.depthSegments):("z"===b&&"y"===c||"y"===b&&"z"===c)&&(k="x",n=i.depthSegments);var s=n+1,t=o+1,u=f/n,v=g/o,w=new a.Vector3;for(w[k]=h>0?1:-1,m=0;t>m;m++)for(l=0;s>l;l++){var x=new a.Vector3;x[b]=(l*u-p)*d,x[c]=(m*v-q)*e,x[k]=h,i.vertices.push(x)}for(m=0;o>m;m++)for(l=0;n>l;l++){var y=l+s*m,z=l+s*(m+1),A=l+1+s*(m+1),B=l+1+s*m,C=new a.Vector2(l/n,1-m/o),D=new a.Vector2(l/n,1-(m+1)/o),E=new a.Vector2((l+1)/n,1-(m+1)/o),F=new a.Vector2((l+1)/n,1-m/o),G=new a.Face3(y+r,z+r,B+r);G.normal.copy(w),G.vertexNormals.push(w.clone(),w.clone(),w.clone()),G.materialIndex=j,i.faces.push(G),i.faceVertexUvs[0].push([C,D,F]),G=new a.Face3(z+r,A+r,B+r),G.normal.copy(w),G.vertexNormals.push(w.clone(),w.clone(),w.clone()),G.materialIndex=j,i.faces.push(G),i.faceVertexUvs[0].push([D.clone(),E,F.clone()])}}a.Geometry.call(this),this.type="BoxGeometry",this.parameters={width:b,height:c,depth:d,widthSegments:e,heightSegments:f,depthSegments:g},this.widthSegments=e||1,this.heightSegments=f||1,this.depthSegments=g||1;var i=this,j=b/2,k=c/2,l=d/2;h("z","y",-1,-1,d,c,j,0),h("z","y",1,-1,d,c,-j,1),h("x","z",1,1,b,d,k,2),h("x","z",1,-1,b,d,-k,3),h("x","y",1,-1,b,c,l,4),h("x","y",-1,-1,b,c,-l,5),this.mergeVertices()},a.BoxGeometry.prototype=Object.create(a.Geometry.prototype),a.CircleGeometry=function(b,c,d,e){a.Geometry.call(this),this.type="CircleGeometry",this.parameters={radius:b,segments:c,thetaStart:d,thetaLength:e},b=b||50,c=void 0!==c?Math.max(3,c):8,d=void 0!==d?d:0,e=void 0!==e?e:2*Math.PI;var f,g=[],h=new a.Vector3,i=new a.Vector2(.5,.5);for(this.vertices.push(h),g.push(i),f=0;c>=f;f++){var j=new a.Vector3,k=d+f/c*e;j.x=b*Math.cos(k),j.y=b*Math.sin(k),this.vertices.push(j),g.push(new a.Vector2((j.x/b+1)/2,(j.y/b+1)/2))}var l=new a.Vector3(0,0,1);for(f=1;c>=f;f++)this.faces.push(new a.Face3(f,f+1,0,[l.clone(),l.clone(),l.clone()])),this.faceVertexUvs[0].push([g[f].clone(),g[f+1].clone(),i.clone()]);this.computeFaceNormals(),this.boundingSphere=new a.Sphere(new a.Vector3,b)},a.CircleGeometry.prototype=Object.create(a.Geometry.prototype),a.CubeGeometry=function(b,c,d,e,f,g){return console.warn("THREE.CubeGeometry has been renamed to THREE.BoxGeometry."),new a.BoxGeometry(b,c,d,e,f,g)},a.CylinderGeometry=function(b,c,d,e,f,g){a.Geometry.call(this),this.type="CylinderGeometry",this.parameters={radiusTop:b,radiusBottom:c,height:d,radialSegments:e,heightSegments:f,openEnded:g},b=void 0!==b?b:20,c=void 0!==c?c:20,d=void 0!==d?d:100,e=e||8,f=f||1,g=void 0!==g?g:!1;var h,i,j=d/2,k=[],l=[];for(i=0;f>=i;i++){var m=[],n=[],o=i/f,p=o*(c-b)+b;for(h=0;e>=h;h++){var q=h/e,r=new a.Vector3;r.x=p*Math.sin(q*Math.PI*2),r.y=-o*d+j,r.z=p*Math.cos(q*Math.PI*2),this.vertices.push(r),m.push(this.vertices.length-1),n.push(new a.Vector2(q,1-o))}k.push(m),l.push(n)}var s,t,u=(c-b)/d;for(h=0;e>h;h++)for(0!==b?(s=this.vertices[k[0][h]].clone(),t=this.vertices[k[0][h+1]].clone()):(s=this.vertices[k[1][h]].clone(),t=this.vertices[k[1][h+1]].clone()),s.setY(Math.sqrt(s.x*s.x+s.z*s.z)*u).normalize(),t.setY(Math.sqrt(t.x*t.x+t.z*t.z)*u).normalize(),i=0;f>i;i++){var v=k[i][h],w=k[i+1][h],x=k[i+1][h+1],y=k[i][h+1],z=s.clone(),A=s.clone(),B=t.clone(),C=t.clone(),D=l[i][h].clone(),E=l[i+1][h].clone(),F=l[i+1][h+1].clone(),G=l[i][h+1].clone();this.faces.push(new a.Face3(v,w,y,[z,A,C])),this.faceVertexUvs[0].push([D,E,G]),this.faces.push(new a.Face3(w,x,y,[A.clone(),B,C.clone()])),this.faceVertexUvs[0].push([E.clone(),F,G.clone()])}if(g===!1&&b>0)for(this.vertices.push(new a.Vector3(0,j,0)),h=0;e>h;h++){var v=k[0][h],w=k[0][h+1],x=this.vertices.length-1,z=new a.Vector3(0,1,0),A=new a.Vector3(0,1,0),B=new a.Vector3(0,1,0),D=l[0][h].clone(),E=l[0][h+1].clone(),F=new a.Vector2(E.x,0);this.faces.push(new a.Face3(v,w,x,[z,A,B])),this.faceVertexUvs[0].push([D,E,F])}if(g===!1&&c>0)for(this.vertices.push(new a.Vector3(0,-j,0)),h=0;e>h;h++){var v=k[i][h+1],w=k[i][h],x=this.vertices.length-1,z=new a.Vector3(0,-1,0),A=new a.Vector3(0,-1,0),B=new a.Vector3(0,-1,0),D=l[i][h+1].clone(),E=l[i][h].clone(),F=new a.Vector2(E.x,1);this.faces.push(new a.Face3(v,w,x,[z,A,B])),this.faceVertexUvs[0].push([D,E,F])}this.computeFaceNormals()},a.CylinderGeometry.prototype=Object.create(a.Geometry.prototype),a.ExtrudeGeometry=function(b,c){return"undefined"==typeof b?void(b=[]):(a.Geometry.call(this),this.type="ExtrudeGeometry",b=b instanceof Array?b:[b],this.addShapeList(b,c),void this.computeFaceNormals())},a.ExtrudeGeometry.prototype=Object.create(a.Geometry.prototype),a.ExtrudeGeometry.prototype.addShapeList=function(a,b){for(var c=a.length,d=0;c>d;d++){var e=a[d];this.addShape(e,b)}},a.ExtrudeGeometry.prototype.addShape=function(b,c){function d(a,b,c){return b||console.log("die"),b.clone().multiplyScalar(c).add(a)}function e(b,c,d){var e,f,g=1e-10,h=1,i=b.x-c.x,j=b.y-c.y,k=d.x-b.x,l=d.y-b.y,m=i*i+j*j,n=i*l-j*k;if(Math.abs(n)>g){var o=Math.sqrt(m),p=Math.sqrt(k*k+l*l),q=c.x-j/o,r=c.y+i/o,s=d.x-l/p,t=d.y+k/p,u=((s-q)*l-(t-r)*k)/(i*l-j*k);e=q+i*u-b.x,f=r+j*u-b.y;var v=e*e+f*f;if(2>=v)return new a.Vector2(e,f);h=Math.sqrt(v/2)}else{var w=!1;i>g?k>g&&(w=!0):-g>i?-g>k&&(w=!0):Math.sign(j)==Math.sign(l)&&(w=!0),w?(e=-j,f=i,h=Math.sqrt(m)):(e=i,f=j,h=Math.sqrt(m/2))}return new a.Vector2(e/h,f/h)}function f(){if(u){var a=0,b=T*a;for(W=0;U>W;W++)S=L[W],j(S[2]+b,S[1]+b,S[0]+b);for(a=w+2*t,b=T*a,W=0;U>W;W++)S=L[W],j(S[0]+b,S[1]+b,S[2]+b)}else{for(W=0;U>W;W++)S=L[W],j(S[2],S[1],S[0]);for(W=0;U>W;W++)S=L[W],j(S[0]+T*w,S[1]+T*w,S[2]+T*w)}}function g(){var a=0;for(h(M,a),a+=M.length,D=0,E=J.length;E>D;D++)C=J[D],h(C,a),a+=C.length}function h(a,b){var c,d;for(W=a.length;--W>=0;){c=W,d=W-1,0>d&&(d=a.length-1);var e=0,f=w+2*t;for(e=0;f>e;e++){var g=T*e,h=T*(e+1),i=b+c+g,j=b+d+g,l=b+d+h,m=b+c+h;k(i,j,l,m,a,e,f,c,d)}}}function i(b,c,d){F.vertices.push(new a.Vector3(b,c,d))}function j(b,c,d){b+=G,c+=G,d+=G,F.faces.push(new a.Face3(b,c,d,null,null,z));var e=B.generateTopUV(F,b,c,d);F.faceVertexUvs[0].push(e)}function k(b,c,d,e,f,g,h,i,j){b+=G,c+=G,d+=G,e+=G,F.faces.push(new a.Face3(b,c,e,null,null,A)),F.faces.push(new a.Face3(c,d,e,null,null,A));var k=B.generateSideWallUV(F,b,c,d,e);F.faceVertexUvs[0].push([k[0],k[1],k[3]]),F.faceVertexUvs[0].push([k[1],k[2],k[3]])}var l,m,n,o,p,q=void 0!==c.amount?c.amount:100,r=void 0!==c.bevelThickness?c.bevelThickness:6,s=void 0!==c.bevelSize?c.bevelSize:r-2,t=void 0!==c.bevelSegments?c.bevelSegments:3,u=void 0!==c.bevelEnabled?c.bevelEnabled:!0,v=void 0!==c.curveSegments?c.curveSegments:12,w=void 0!==c.steps?c.steps:1,x=c.extrudePath,y=!1,z=c.material,A=c.extrudeMaterial,B=void 0!==c.UVGenerator?c.UVGenerator:a.ExtrudeGeometry.WorldUVGenerator;x&&(l=x.getSpacedPoints(w),y=!0,u=!1,m=void 0!==c.frames?c.frames:new a.TubeGeometry.FrenetFrames(x,w,!1),n=new a.Vector3,o=new a.Vector3,p=new a.Vector3),u||(t=0,r=0,s=0);var C,D,E,F=this,G=this.vertices.length,H=b.extractPoints(v),I=H.shape,J=H.holes,K=!a.Shape.Utils.isClockWise(I);if(K){for(I=I.reverse(),D=0,E=J.length;E>D;D++)C=J[D],a.Shape.Utils.isClockWise(C)&&(J[D]=C.reverse());K=!1}var L=a.Shape.Utils.triangulateShape(I,J),M=I;for(D=0,E=J.length;E>D;D++)C=J[D],I=I.concat(C);for(var N,O,P,Q,R,S,T=I.length,U=L.length,V=(M.length,180/Math.PI,[]),W=0,X=M.length,Y=X-1,Z=W+1;X>W;W++,Y++,Z++){Y===X&&(Y=0),Z===X&&(Z=0);M[W],M[Y],M[Z];V[W]=e(M[W],M[Y],M[Z])}var $,_=[],aa=V.concat();for(D=0,E=J.length;E>D;D++){for(C=J[D],$=[],W=0,X=C.length,Y=X-1,Z=W+1;X>W;W++,Y++,Z++)Y===X&&(Y=0),Z===X&&(Z=0),$[W]=e(C[W],C[Y],C[Z]);_.push($),aa=aa.concat($)}for(N=0;t>N;N++){for(P=N/t,Q=r*(1-P),O=s*Math.sin(P*Math.PI/2),W=0,X=M.length;X>W;W++)R=d(M[W],V[W],O),i(R.x,R.y,-Q);for(D=0,E=J.length;E>D;D++)for(C=J[D],$=_[D],W=0,X=C.length;X>W;W++)R=d(C[W],$[W],O),i(R.x,R.y,-Q)}for(O=s,W=0;T>W;W++)R=u?d(I[W],aa[W],O):I[W],y?(o.copy(m.normals[0]).multiplyScalar(R.x),n.copy(m.binormals[0]).multiplyScalar(R.y),p.copy(l[0]).add(o).add(n),i(p.x,p.y,p.z)):i(R.x,R.y,0);var ba;for(ba=1;w>=ba;ba++)for(W=0;T>W;W++)R=u?d(I[W],aa[W],O):I[W],y?(o.copy(m.normals[ba]).multiplyScalar(R.x),n.copy(m.binormals[ba]).multiplyScalar(R.y),p.copy(l[ba]).add(o).add(n),i(p.x,p.y,p.z)):i(R.x,R.y,q/w*ba);for(N=t-1;N>=0;N--){for(P=N/t,Q=r*(1-P),O=s*Math.sin(P*Math.PI/2),W=0,X=M.length;X>W;W++)R=d(M[W],V[W],O),i(R.x,R.y,q+Q);for(D=0,E=J.length;E>D;D++)for(C=J[D],$=_[D],W=0,X=C.length;X>W;W++)R=d(C[W],$[W],O),y?i(R.x,R.y+l[w-1].y,l[w-1].x+Q):i(R.x,R.y,q+Q)}f(),g()},a.ExtrudeGeometry.WorldUVGenerator={generateTopUV:function(b,c,d,e){var f=b.vertices,g=f[c],h=f[d],i=f[e];return[new a.Vector2(g.x,g.y),new a.Vector2(h.x,h.y),new a.Vector2(i.x,i.y)]},generateSideWallUV:function(b,c,d,e,f){var g=b.vertices,h=g[c],i=g[d],j=g[e],k=g[f];return Math.abs(h.y-i.y)<.01?[new a.Vector2(h.x,1-h.z),new a.Vector2(i.x,1-i.z),new a.Vector2(j.x,1-j.z),new a.Vector2(k.x,1-k.z)]:[new a.Vector2(h.y,1-h.z),new a.Vector2(i.y,1-i.z),new a.Vector2(j.y,1-j.z),new a.Vector2(k.y,1-k.z)]}},a.ShapeGeometry=function(b,c){a.Geometry.call(this),this.type="ShapeGeometry",b instanceof Array==!1&&(b=[b]),this.addShapeList(b,c),this.computeFaceNormals()},a.ShapeGeometry.prototype=Object.create(a.Geometry.prototype),a.ShapeGeometry.prototype.addShapeList=function(a,b){for(var c=0,d=a.length;d>c;c++)this.addShape(a[c],b);return this},a.ShapeGeometry.prototype.addShape=function(b,c){void 0===c&&(c={});var d,e,f,g=void 0!==c.curveSegments?c.curveSegments:12,h=c.material,i=void 0===c.UVGenerator?a.ExtrudeGeometry.WorldUVGenerator:c.UVGenerator,j=this.vertices.length,k=b.extractPoints(g),l=k.shape,m=k.holes,n=!a.Shape.Utils.isClockWise(l);if(n){for(l=l.reverse(),d=0,e=m.length;e>d;d++)f=m[d],a.Shape.Utils.isClockWise(f)&&(m[d]=f.reverse());n=!1}var o=a.Shape.Utils.triangulateShape(l,m),p=l;for(d=0,e=m.length;e>d;d++)f=m[d],l=l.concat(f);var q,r,s=l.length,t=o.length;p.length;for(d=0;s>d;d++)q=l[d],this.vertices.push(new a.Vector3(q.x,q.y,0));for(d=0;t>d;d++){r=o[d];var u=r[0]+j,v=r[1]+j,w=r[2]+j;this.faces.push(new a.Face3(u,v,w,null,null,h)),this.faceVertexUvs[0].push(i.generateTopUV(this,u,v,w))}},a.LatheGeometry=function(b,c,d,e){a.Geometry.call(this),this.type="LatheGeometry",this.parameters={points:b,segments:c,phiStart:d,phiLength:e},c=c||12,d=d||0,e=e||2*Math.PI;for(var f=1/(b.length-1),g=1/c,h=0,i=c;i>=h;h++)for(var j=d+h*g*e,k=Math.cos(j),l=Math.sin(j),m=0,n=b.length;n>m;m++){var o=b[m],p=new a.Vector3;p.x=k*o.x-l*o.y,p.y=l*o.x+k*o.y,p.z=o.z,this.vertices.push(p)}for(var q=b.length,h=0,i=c;i>h;h++)for(var m=0,n=b.length-1;n>m;m++){var r=m+q*h,s=r,t=r+q,k=r+1+q,u=r+1,v=h*g,w=m*f,x=v+g,y=w+f;this.faces.push(new a.Face3(s,t,u)),this.faceVertexUvs[0].push([new a.Vector2(v,w),new a.Vector2(x,w),new a.Vector2(v,y)]),this.faces.push(new a.Face3(t,k,u)),this.faceVertexUvs[0].push([new a.Vector2(x,w),new a.Vector2(x,y),new a.Vector2(v,y)])}this.mergeVertices(),this.computeFaceNormals(),this.computeVertexNormals()},a.LatheGeometry.prototype=Object.create(a.Geometry.prototype),a.PlaneGeometry=function(b,c,d,e){console.info("THREE.PlaneGeometry: Consider using THREE.PlaneBufferGeometry for lower memory footprint."),a.Geometry.call(this),this.type="PlaneGeometry",this.parameters={width:b,height:c,widthSegments:d,heightSegments:e},this.fromBufferGeometry(new a.PlaneBufferGeometry(b,c,d,e))},a.PlaneGeometry.prototype=Object.create(a.Geometry.prototype),a.PlaneBufferGeometry=function(b,c,d,e){a.BufferGeometry.call(this),this.type="PlaneBufferGeometry",this.parameters={width:b,height:c,widthSegments:d,heightSegments:e};for(var f=b/2,g=c/2,h=d||1,i=e||1,j=h+1,k=i+1,l=b/h,m=c/i,n=new Float32Array(j*k*3),o=new Float32Array(j*k*3),p=new Float32Array(j*k*2),q=0,r=0,s=0;k>s;s++)for(var t=s*m-g,u=0;j>u;u++){var v=u*l-f;n[q]=v,n[q+1]=-t,o[q+2]=1,p[r]=u/h,p[r+1]=1-s/i,q+=3,r+=2}q=0;for(var w=new(n.length/3>65535?Uint32Array:Uint16Array)(h*i*6),s=0;i>s;s++)for(var u=0;h>u;u++){var x=u+j*s,y=u+j*(s+1),z=u+1+j*(s+1),A=u+1+j*s;w[q]=x,w[q+1]=y,w[q+2]=A,w[q+3]=y,w[q+4]=z,w[q+5]=A,q+=6}this.addAttribute("index",new a.BufferAttribute(w,1)),this.addAttribute("position",new a.BufferAttribute(n,3)),this.addAttribute("normal",new a.BufferAttribute(o,3)),this.addAttribute("uv",new a.BufferAttribute(p,2))},a.PlaneBufferGeometry.prototype=Object.create(a.BufferGeometry.prototype),a.RingGeometry=function(b,c,d,e,f,g){a.Geometry.call(this),this.type="RingGeometry",this.parameters={innerRadius:b,outerRadius:c,thetaSegments:d,phiSegments:e,thetaStart:f,thetaLength:g},b=b||0,c=c||50,f=void 0!==f?f:0,g=void 0!==g?g:2*Math.PI,d=void 0!==d?Math.max(3,d):8,e=void 0!==e?Math.max(1,e):8;var h,i,j=[],k=b,l=(c-b)/e;for(h=0;e+1>h;h++){for(i=0;d+1>i;i++){var m=new a.Vector3,n=f+i/d*g;m.x=k*Math.cos(n),m.y=k*Math.sin(n),this.vertices.push(m),j.push(new a.Vector2((m.x/c+1)/2,(m.y/c+1)/2))}k+=l}var o=new a.Vector3(0,0,1);for(h=0;e>h;h++){var p=h*(d+1);for(i=0;d>i;i++){var n=i+p,q=n,r=n+d+1,s=n+d+2;this.faces.push(new a.Face3(q,r,s,[o.clone(),o.clone(),o.clone()])),this.faceVertexUvs[0].push([j[q].clone(),j[r].clone(),j[s].clone()]),q=n,r=n+d+2,s=n+1,this.faces.push(new a.Face3(q,r,s,[o.clone(),o.clone(),o.clone()])),this.faceVertexUvs[0].push([j[q].clone(),j[r].clone(),j[s].clone()])}}this.computeFaceNormals(),this.boundingSphere=new a.Sphere(new a.Vector3,k)},a.RingGeometry.prototype=Object.create(a.Geometry.prototype),a.SphereGeometry=function(b,c,d,e,f,g,h){a.Geometry.call(this),this.type="SphereGeometry",this.parameters={radius:b,widthSegments:c,heightSegments:d,phiStart:e,phiLength:f,thetaStart:g,thetaLength:h},b=b||50,c=Math.max(3,Math.floor(c)||8),d=Math.max(2,Math.floor(d)||6),e=void 0!==e?e:0,f=void 0!==f?f:2*Math.PI,g=void 0!==g?g:0,h=void 0!==h?h:Math.PI;var i,j,k=[],l=[];for(j=0;d>=j;j++){var m=[],n=[];for(i=0;c>=i;i++){var o=i/c,p=j/d,q=new a.Vector3;q.x=-b*Math.cos(e+o*f)*Math.sin(g+p*h),q.y=b*Math.cos(g+p*h),q.z=b*Math.sin(e+o*f)*Math.sin(g+p*h),this.vertices.push(q),m.push(this.vertices.length-1),n.push(new a.Vector2(o,1-p))}k.push(m),l.push(n)}for(j=0;d>j;j++)for(i=0;c>i;i++){var r=k[j][i+1],s=k[j][i],t=k[j+1][i],u=k[j+1][i+1],v=this.vertices[r].clone().normalize(),w=this.vertices[s].clone().normalize(),x=this.vertices[t].clone().normalize(),y=this.vertices[u].clone().normalize(),z=l[j][i+1].clone(),A=l[j][i].clone(),B=l[j+1][i].clone(),C=l[j+1][i+1].clone();Math.abs(this.vertices[r].y)===b?(z.x=(z.x+A.x)/2,this.faces.push(new a.Face3(r,t,u,[v,x,y])),this.faceVertexUvs[0].push([z,B,C])):Math.abs(this.vertices[t].y)===b?(B.x=(B.x+C.x)/2,this.faces.push(new a.Face3(r,s,t,[v,w,x])),this.faceVertexUvs[0].push([z,A,B])):(this.faces.push(new a.Face3(r,s,u,[v,w,y])),this.faceVertexUvs[0].push([z,A,C]),this.faces.push(new a.Face3(s,t,u,[w.clone(),x,y.clone()])),this.faceVertexUvs[0].push([A.clone(),B,C.clone()]))}this.computeFaceNormals(),this.boundingSphere=new a.Sphere(new a.Vector3,b)},a.SphereGeometry.prototype=Object.create(a.Geometry.prototype),a.TextGeometry=function(b,c){c=c||{};var d=a.FontUtils.generateShapes(b,c);c.amount=void 0!==c.height?c.height:50,void 0===c.bevelThickness&&(c.bevelThickness=10),void 0===c.bevelSize&&(c.bevelSize=8),void 0===c.bevelEnabled&&(c.bevelEnabled=!1),a.ExtrudeGeometry.call(this,d,c),this.type="TextGeometry"},a.TextGeometry.prototype=Object.create(a.ExtrudeGeometry.prototype),a.TorusGeometry=function(b,c,d,e,f){a.Geometry.call(this),this.type="TorusGeometry",this.parameters={radius:b,tube:c,radialSegments:d,tubularSegments:e,arc:f},b=b||100,c=c||40,d=d||8,e=e||6,f=f||2*Math.PI;for(var g=new a.Vector3,h=[],i=[],j=0;d>=j;j++)for(var k=0;e>=k;k++){var l=k/e*f,m=j/d*Math.PI*2;g.x=b*Math.cos(l),g.y=b*Math.sin(l);var n=new a.Vector3;n.x=(b+c*Math.cos(m))*Math.cos(l),n.y=(b+c*Math.cos(m))*Math.sin(l),n.z=c*Math.sin(m),this.vertices.push(n),h.push(new a.Vector2(k/e,j/d)),i.push(n.clone().sub(g).normalize())}for(var j=1;d>=j;j++)for(var k=1;e>=k;k++){var o=(e+1)*j+k-1,p=(e+1)*(j-1)+k-1,q=(e+1)*(j-1)+k,r=(e+1)*j+k,s=new a.Face3(o,p,r,[i[o].clone(),i[p].clone(),i[r].clone()]);this.faces.push(s),this.faceVertexUvs[0].push([h[o].clone(),h[p].clone(),h[r].clone()]),s=new a.Face3(p,q,r,[i[p].clone(),i[q].clone(),i[r].clone()]),this.faces.push(s),this.faceVertexUvs[0].push([h[p].clone(),h[q].clone(),h[r].clone()])}this.computeFaceNormals()},a.TorusGeometry.prototype=Object.create(a.Geometry.prototype),a.TorusKnotGeometry=function(b,c,d,e,f,g,h){function i(b,c,d,e,f){var g=Math.cos(b),h=Math.sin(b),i=c/d*b,j=Math.cos(i),k=e*(2+j)*.5*g,l=e*(2+j)*h*.5,m=f*e*Math.sin(i)*.5;return new a.Vector3(k,l,m)}a.Geometry.call(this),this.type="TorusKnotGeometry",this.parameters={radius:b,tube:c,radialSegments:d,tubularSegments:e,p:f,q:g,heightScale:h},b=b||100,c=c||40,d=d||64,e=e||8,f=f||2,g=g||3,h=h||1;for(var j=new Array(d),k=new a.Vector3,l=new a.Vector3,m=new a.Vector3,n=0;d>n;++n){j[n]=new Array(e);var o=n/d*2*f*Math.PI,p=i(o,g,f,b,h),q=i(o+.01,g,f,b,h);k.subVectors(q,p),l.addVectors(q,p),m.crossVectors(k,l),l.crossVectors(m,k),m.normalize(),l.normalize();for(var r=0;e>r;++r){var s=r/e*2*Math.PI,t=-c*Math.cos(s),u=c*Math.sin(s),v=new a.Vector3;v.x=p.x+t*l.x+u*m.x,v.y=p.y+t*l.y+u*m.y,v.z=p.z+t*l.z+u*m.z,j[n][r]=this.vertices.push(v)-1}}for(var n=0;d>n;++n)for(var r=0;e>r;++r){var w=(n+1)%d,x=(r+1)%e,y=j[n][r],z=j[w][r],A=j[w][x],B=j[n][x],C=new a.Vector2(n/d,r/e),D=new a.Vector2((n+1)/d,r/e),E=new a.Vector2((n+1)/d,(r+1)/e),F=new a.Vector2(n/d,(r+1)/e);this.faces.push(new a.Face3(y,z,B)),this.faceVertexUvs[0].push([C,D,F]),this.faces.push(new a.Face3(z,A,B)),this.faceVertexUvs[0].push([D.clone(),E,F.clone()])}this.computeFaceNormals(),this.computeVertexNormals()},a.TorusKnotGeometry.prototype=Object.create(a.Geometry.prototype),a.TubeGeometry=function(b,c,d,e,f){function g(b,c,d){return C.vertices.push(new a.Vector3(b,c,d))-1}a.Geometry.call(this),this.type="TubeGeometry",this.parameters={path:b,segments:c,radius:d,radialSegments:e,closed:f},c=c||64,d=d||1,e=e||8,f=f||!1;var h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B=[],C=this,D=c+1,E=new a.Vector3,F=new a.TubeGeometry.FrenetFrames(b,c,f),G=F.tangents,H=F.normals,I=F.binormals;for(this.tangents=G,this.normals=H,this.binormals=I,p=0;D>p;p++)for(B[p]=[],k=p/(D-1),o=b.getPointAt(k),h=G[p],i=H[p],j=I[p],q=0;e>q;q++)l=q/e*2*Math.PI,m=-d*Math.cos(l),n=d*Math.sin(l),E.copy(o),E.x+=m*i.x+n*j.x,E.y+=m*i.y+n*j.y,E.z+=m*i.z+n*j.z,B[p][q]=g(E.x,E.y,E.z);for(p=0;c>p;p++)for(q=0;e>q;q++)r=f?(p+1)%c:p+1,s=(q+1)%e,t=B[p][q],u=B[r][q],v=B[r][s],w=B[p][s],x=new a.Vector2(p/c,q/e),y=new a.Vector2((p+1)/c,q/e),z=new a.Vector2((p+1)/c,(q+1)/e),A=new a.Vector2(p/c,(q+1)/e),this.faces.push(new a.Face3(t,u,w)),this.faceVertexUvs[0].push([x,y,A]),this.faces.push(new a.Face3(u,v,w)),this.faceVertexUvs[0].push([y.clone(),z,A.clone()]);this.computeFaceNormals(),this.computeVertexNormals()},a.TubeGeometry.prototype=Object.create(a.Geometry.prototype),a.TubeGeometry.FrenetFrames=function(b,c,d){function e(){o[0]=new a.Vector3,p[0]=new a.Vector3,g=Number.MAX_VALUE,h=Math.abs(n[0].x),i=Math.abs(n[0].y),j=Math.abs(n[0].z),g>=h&&(g=h,m.set(1,0,0)),g>=i&&(g=i,m.set(0,1,0)),g>=j&&m.set(0,0,1),q.crossVectors(n[0],m).normalize(),o[0].crossVectors(n[0],q),p[0].crossVectors(n[0],o[0])}var f,g,h,i,j,k,l,m=(new a.Vector3,new a.Vector3),n=(new a.Vector3,[]),o=[],p=[],q=new a.Vector3,r=new a.Matrix4,s=c+1,t=1e-4;for(this.tangents=n,this.normals=o,this.binormals=p,k=0;s>k;k++)l=k/(s-1),n[k]=b.getTangentAt(l),n[k].normalize();for(e(),k=1;s>k;k++)o[k]=o[k-1].clone(),p[k]=p[k-1].clone(),q.crossVectors(n[k-1],n[k]),q.length()>t&&(q.normalize(),f=Math.acos(a.Math.clamp(n[k-1].dot(n[k]),-1,1)),o[k].applyMatrix4(r.makeRotationAxis(q,f))),p[k].crossVectors(n[k],o[k]);if(d)for(f=Math.acos(a.Math.clamp(o[0].dot(o[s-1]),-1,1)),f/=s-1,n[0].dot(q.crossVectors(o[0],o[s-1]))>0&&(f=-f),k=1;s>k;k++)o[k].applyMatrix4(r.makeRotationAxis(n[k],f*k)),p[k].crossVectors(n[k],o[k])},a.PolyhedronGeometry=function(b,c,d,e){function f(b){var c=b.normalize().clone();c.index=l.vertices.push(c)-1;var d=i(b)/2/Math.PI+.5,e=j(b)/Math.PI+.5;return c.uv=new a.Vector2(d,1-e),c}function g(b,c,d){var e=new a.Face3(b.index,c.index,d.index,[b.clone(),c.clone(),d.clone()]);l.faces.push(e),u.copy(b).add(c).add(d).divideScalar(3);var f=i(u);l.faceVertexUvs[0].push([k(b.uv,b,f),k(c.uv,c,f),k(d.uv,d,f)])}function h(a,b){for(var c=Math.pow(2,b),d=(Math.pow(4,b),f(l.vertices[a.a])),e=f(l.vertices[a.b]),h=f(l.vertices[a.c]),i=[],j=0;c>=j;j++){i[j]=[];for(var k=f(d.clone().lerp(h,j/c)),m=f(e.clone().lerp(h,j/c)),n=c-j,o=0;n>=o;o++)0==o&&j==c?i[j][o]=k:i[j][o]=f(k.clone().lerp(m,o/n))}for(var j=0;c>j;j++)for(var o=0;2*(c-j)-1>o;o++){var p=Math.floor(o/2);o%2==0?g(i[j][p+1],i[j+1][p],i[j][p]):g(i[j][p+1],i[j+1][p+1],i[j+1][p])}}function i(a){return Math.atan2(a.z,-a.x)}function j(a){return Math.atan2(-a.y,Math.sqrt(a.x*a.x+a.z*a.z))}function k(b,c,d){return 0>d&&1===b.x&&(b=new a.Vector2(b.x-1,b.y)),0===c.x&&0===c.z&&(b=new a.Vector2(d/2/Math.PI+.5,b.y)),b.clone()}a.Geometry.call(this),this.type="PolyhedronGeometry",this.parameters={vertices:b,indices:c,radius:d,detail:e},d=d||1,e=e||0;for(var l=this,m=0,n=b.length;n>m;m+=3)f(new a.Vector3(b[m],b[m+1],b[m+2]));for(var o=this.vertices,p=[],m=0,q=0,n=c.length;n>m;m+=3,q++){var r=o[c[m]],s=o[c[m+1]],t=o[c[m+2]];p[q]=new a.Face3(r.index,s.index,t.index,[r.clone(),s.clone(),t.clone()])}for(var u=new a.Vector3,m=0,n=p.length;n>m;m++)h(p[m],e);for(var m=0,n=this.faceVertexUvs[0].length;n>m;m++){var v=this.faceVertexUvs[0][m],w=v[0].x,x=v[1].x,y=v[2].x,z=Math.max(w,Math.max(x,y)),A=Math.min(w,Math.min(x,y));z>.9&&.1>A&&(.2>w&&(v[0].x+=1),.2>x&&(v[1].x+=1),.2>y&&(v[2].x+=1))}for(var m=0,n=this.vertices.length;n>m;m++)this.vertices[m].multiplyScalar(d);this.mergeVertices(),this.computeFaceNormals(),this.boundingSphere=new a.Sphere(new a.Vector3,d)},a.PolyhedronGeometry.prototype=Object.create(a.Geometry.prototype),a.DodecahedronGeometry=function(b,c){this.parameters={radius:b,detail:c};var d=(1+Math.sqrt(5))/2,e=1/d,f=[-1,-1,-1,-1,-1,1,-1,1,-1,-1,1,1,1,-1,-1,1,-1,1,1,1,-1,1,1,1,0,-e,-d,0,-e,d,0,e,-d,0,e,d,-e,-d,0,-e,d,0,e,-d,0,e,d,0,-d,0,-e,d,0,-e,-d,0,e,d,0,e],g=[3,11,7,3,7,15,3,15,13,7,19,17,7,17,6,7,6,15,17,4,8,17,8,10,17,10,6,8,0,16,8,16,2,8,2,10,0,12,1,0,1,18,0,18,16,6,10,2,6,2,13,6,13,15,2,16,18,2,18,3,2,3,13,18,1,9,18,9,11,18,11,3,4,14,12,4,12,0,4,0,8,11,9,5,11,5,19,11,19,7,19,5,14,19,14,4,19,4,17,1,12,14,1,14,5,1,5,9];a.PolyhedronGeometry.call(this,f,g,b,c)},a.DodecahedronGeometry.prototype=Object.create(a.Geometry.prototype),a.IcosahedronGeometry=function(b,c){var d=(1+Math.sqrt(5))/2,e=[-1,d,0,1,d,0,-1,-d,0,1,-d,0,0,-1,d,0,1,d,0,-1,-d,0,1,-d,d,0,-1,d,0,1,-d,0,-1,-d,0,1],f=[0,11,5,0,5,1,0,1,7,0,7,10,0,10,11,1,5,9,5,11,4,11,10,2,10,7,6,7,1,8,3,9,4,3,4,2,3,2,6,3,6,8,3,8,9,4,9,5,2,4,11,6,2,10,8,6,7,9,8,1];a.PolyhedronGeometry.call(this,e,f,b,c),this.type="IcosahedronGeometry",this.parameters={radius:b,detail:c}},a.IcosahedronGeometry.prototype=Object.create(a.Geometry.prototype),a.OctahedronGeometry=function(b,c){this.parameters={radius:b,detail:c};var d=[1,0,0,-1,0,0,0,1,0,0,-1,0,0,0,1,0,0,-1],e=[0,2,4,0,4,3,0,3,5,0,5,2,1,2,5,1,5,3,1,3,4,1,4,2];a.PolyhedronGeometry.call(this,d,e,b,c),this.type="OctahedronGeometry",this.parameters={radius:b,detail:c}},a.OctahedronGeometry.prototype=Object.create(a.Geometry.prototype),a.TetrahedronGeometry=function(b,c){var d=[1,1,1,-1,-1,1,-1,1,-1,1,-1,-1],e=[2,1,0,0,3,2,1,3,0,2,3,1];a.PolyhedronGeometry.call(this,d,e,b,c),this.type="TetrahedronGeometry",this.parameters={radius:b,detail:c}},a.TetrahedronGeometry.prototype=Object.create(a.Geometry.prototype),a.ParametricGeometry=function(b,c,d){a.Geometry.call(this),this.type="ParametricGeometry",this.parameters={func:b,slices:c,stacks:d};var e,f,g,h,i,j=this.vertices,k=this.faces,l=this.faceVertexUvs[0],m=c+1;for(e=0;d>=e;e++)for(i=e/d,f=0;c>=f;f++)h=f/c,g=b(h,i),j.push(g);var n,o,p,q,r,s,t,u;for(e=0;d>e;e++)for(f=0;c>f;f++)n=e*m+f,o=e*m+f+1,p=(e+1)*m+f+1,q=(e+1)*m+f,r=new a.Vector2(f/c,e/d),s=new a.Vector2((f+1)/c,e/d),t=new a.Vector2((f+1)/c,(e+1)/d),u=new a.Vector2(f/c,(e+1)/d),k.push(new a.Face3(n,o,q)),l.push([r,s,u]),k.push(new a.Face3(o,p,q)),l.push([s.clone(),t,u.clone()]);this.computeFaceNormals(),this.computeVertexNormals()},a.ParametricGeometry.prototype=Object.create(a.Geometry.prototype),a.AxisHelper=function(b){b=b||1;var c=new Float32Array([0,0,0,b,0,0,0,0,0,0,b,0,0,0,0,0,0,b]),d=new Float32Array([1,0,0,1,.6,0,0,1,0,.6,1,0,0,0,1,0,.6,1]),e=new a.BufferGeometry;e.addAttribute("position",new a.BufferAttribute(c,3)),e.addAttribute("color",new a.BufferAttribute(d,3));var f=new a.LineBasicMaterial({vertexColors:a.VertexColors});a.Line.call(this,e,f,a.LinePieces)},a.AxisHelper.prototype=Object.create(a.Line.prototype),a.ArrowHelper=function(){var b=new a.Geometry;b.vertices.push(new a.Vector3(0,0,0),new a.Vector3(0,1,0));var c=new a.CylinderGeometry(0,.5,1,5,1);return c.applyMatrix((new a.Matrix4).makeTranslation(0,-.5,0)),function(d,e,f,g,h,i){a.Object3D.call(this),void 0===g&&(g=16776960),void 0===f&&(f=1),void 0===h&&(h=.2*f),void 0===i&&(i=.2*h),this.position.copy(e),this.line=new a.Line(b,new a.LineBasicMaterial({color:g})),this.line.matrixAutoUpdate=!1,this.add(this.line),this.cone=new a.Mesh(c,new a.MeshBasicMaterial({color:g})),this.cone.matrixAutoUpdate=!1,this.add(this.cone),this.setDirection(d),this.setLength(f,h,i)}}(),a.ArrowHelper.prototype=Object.create(a.Object3D.prototype),a.ArrowHelper.prototype.setDirection=function(){var b,c=new a.Vector3;return function(a){a.y>.99999?this.quaternion.set(0,0,0,1):a.y<-.99999?this.quaternion.set(1,0,0,0):(c.set(a.z,0,-a.x).normalize(),b=Math.acos(a.y),this.quaternion.setFromAxisAngle(c,b))}}(),a.ArrowHelper.prototype.setLength=function(a,b,c){void 0===b&&(b=.2*a),void 0===c&&(c=.2*b),this.line.scale.set(1,a,1),this.line.updateMatrix(),this.cone.scale.set(c,b,c),this.cone.position.y=a,this.cone.updateMatrix()},a.ArrowHelper.prototype.setColor=function(a){this.line.material.color.set(a),this.cone.material.color.set(a)},a.BoxHelper=function(b){var c=new a.BufferGeometry;c.addAttribute("position",new a.BufferAttribute(new Float32Array(72),3)),a.Line.call(this,c,new a.LineBasicMaterial({color:16776960}),a.LinePieces),void 0!==b&&this.update(b)},a.BoxHelper.prototype=Object.create(a.Line.prototype),a.BoxHelper.prototype.update=function(a){var b=a.geometry;null===b.boundingBox&&b.computeBoundingBox();var c=b.boundingBox.min,d=b.boundingBox.max,e=this.geometry.attributes.position.array;e[0]=d.x,e[1]=d.y,e[2]=d.z,e[3]=c.x,e[4]=d.y,e[5]=d.z,e[6]=c.x,e[7]=d.y,e[8]=d.z,e[9]=c.x,e[10]=c.y,e[11]=d.z,e[12]=c.x,e[13]=c.y,e[14]=d.z,e[15]=d.x,e[16]=c.y,e[17]=d.z,e[18]=d.x,e[19]=c.y,e[20]=d.z,e[21]=d.x,e[22]=d.y,e[23]=d.z,e[24]=d.x,e[25]=d.y,e[26]=c.z,e[27]=c.x,e[28]=d.y,e[29]=c.z,e[30]=c.x,e[31]=d.y,e[32]=c.z,e[33]=c.x,e[34]=c.y,e[35]=c.z,e[36]=c.x,e[37]=c.y,e[38]=c.z,e[39]=d.x,e[40]=c.y,e[41]=c.z,e[42]=d.x,e[43]=c.y,e[44]=c.z,e[45]=d.x,e[46]=d.y,e[47]=c.z,e[48]=d.x,e[49]=d.y,e[50]=d.z,e[51]=d.x,e[52]=d.y,e[53]=c.z,e[54]=c.x,e[55]=d.y,e[56]=d.z,e[57]=c.x,e[58]=d.y,e[59]=c.z,e[60]=c.x,e[61]=c.y,e[62]=d.z,e[63]=c.x,e[64]=c.y,e[65]=c.z,e[66]=d.x,e[67]=c.y,e[68]=d.z,e[69]=d.x,e[70]=c.y,e[71]=c.z,this.geometry.attributes.position.needsUpdate=!0,this.geometry.computeBoundingSphere(),this.matrix=a.matrixWorld,this.matrixAutoUpdate=!1},a.BoundingBoxHelper=function(b,c){var d=void 0!==c?c:8947848;this.object=b,this.box=new a.Box3,a.Mesh.call(this,new a.BoxGeometry(1,1,1),new a.MeshBasicMaterial({color:d,wireframe:!0}))},a.BoundingBoxHelper.prototype=Object.create(a.Mesh.prototype),a.BoundingBoxHelper.prototype.update=function(){this.box.setFromObject(this.object),this.box.size(this.scale),this.box.center(this.position)},a.CameraHelper=function(b){function c(a,b,c){d(a,c),d(b,c)}function d(b,c){e.vertices.push(new a.Vector3),e.colors.push(new a.Color(c)),
void 0===g[b]&&(g[b]=[]),g[b].push(e.vertices.length-1)}var e=new a.Geometry,f=new a.LineBasicMaterial({color:16777215,vertexColors:a.FaceColors}),g={},h=16755200,i=16711680,j=43775,k=16777215,l=3355443;c("n1","n2",h),c("n2","n4",h),c("n4","n3",h),c("n3","n1",h),c("f1","f2",h),c("f2","f4",h),c("f4","f3",h),c("f3","f1",h),c("n1","f1",h),c("n2","f2",h),c("n3","f3",h),c("n4","f4",h),c("p","n1",i),c("p","n2",i),c("p","n3",i),c("p","n4",i),c("u1","u2",j),c("u2","u3",j),c("u3","u1",j),c("c","t",k),c("p","c",l),c("cn1","cn2",l),c("cn3","cn4",l),c("cf1","cf2",l),c("cf3","cf4",l),a.Line.call(this,e,f,a.LinePieces),this.camera=b,this.matrix=b.matrixWorld,this.matrixAutoUpdate=!1,this.pointMap=g,this.update()},a.CameraHelper.prototype=Object.create(a.Line.prototype),a.CameraHelper.prototype.update=function(){var b,c,d=new a.Vector3,e=new a.Camera,f=function(a,f,g,h){d.set(f,g,h).unproject(e);var i=c[a];if(void 0!==i)for(var j=0,k=i.length;k>j;j++)b.vertices[i[j]].copy(d)};return function(){b=this.geometry,c=this.pointMap;var a=1,d=1;e.projectionMatrix.copy(this.camera.projectionMatrix),f("c",0,0,-1),f("t",0,0,1),f("n1",-a,-d,-1),f("n2",a,-d,-1),f("n3",-a,d,-1),f("n4",a,d,-1),f("f1",-a,-d,1),f("f2",a,-d,1),f("f3",-a,d,1),f("f4",a,d,1),f("u1",.7*a,1.1*d,-1),f("u2",.7*-a,1.1*d,-1),f("u3",0,2*d,-1),f("cf1",-a,0,1),f("cf2",a,0,1),f("cf3",0,-d,1),f("cf4",0,d,1),f("cn1",-a,0,-1),f("cn2",a,0,-1),f("cn3",0,-d,-1),f("cn4",0,d,-1),b.verticesNeedUpdate=!0}}(),a.DirectionalLightHelper=function(b,c){a.Object3D.call(this),this.light=b,this.light.updateMatrixWorld(),this.matrix=b.matrixWorld,this.matrixAutoUpdate=!1,c=c||1;var d=new a.Geometry;d.vertices.push(new a.Vector3(-c,c,0),new a.Vector3(c,c,0),new a.Vector3(c,-c,0),new a.Vector3(-c,-c,0),new a.Vector3(-c,c,0));var e=new a.LineBasicMaterial({fog:!1});e.color.copy(this.light.color).multiplyScalar(this.light.intensity),this.lightPlane=new a.Line(d,e),this.add(this.lightPlane),d=new a.Geometry,d.vertices.push(new a.Vector3,new a.Vector3),e=new a.LineBasicMaterial({fog:!1}),e.color.copy(this.light.color).multiplyScalar(this.light.intensity),this.targetLine=new a.Line(d,e),this.add(this.targetLine),this.update()},a.DirectionalLightHelper.prototype=Object.create(a.Object3D.prototype),a.DirectionalLightHelper.prototype.dispose=function(){this.lightPlane.geometry.dispose(),this.lightPlane.material.dispose(),this.targetLine.geometry.dispose(),this.targetLine.material.dispose()},a.DirectionalLightHelper.prototype.update=function(){var b=new a.Vector3,c=new a.Vector3,d=new a.Vector3;return function(){b.setFromMatrixPosition(this.light.matrixWorld),c.setFromMatrixPosition(this.light.target.matrixWorld),d.subVectors(c,b),this.lightPlane.lookAt(d),this.lightPlane.material.color.copy(this.light.color).multiplyScalar(this.light.intensity),this.targetLine.geometry.vertices[1].copy(d),this.targetLine.geometry.verticesNeedUpdate=!0,this.targetLine.material.color.copy(this.lightPlane.material.color)}}(),a.EdgesHelper=function(b,c){var d=void 0!==c?c:16777215,e=[0,0],f={},g=function(a,b){return a-b},h=["a","b","c"],i=new a.BufferGeometry,j=b.geometry.clone();j.mergeVertices(),j.computeFaceNormals();for(var k=j.vertices,l=j.faces,m=0,n=0,o=l.length;o>n;n++)for(var p=l[n],q=0;3>q;q++){e[0]=p[h[q]],e[1]=p[h[(q+1)%3]],e.sort(g);var r=e.toString();void 0===f[r]?(f[r]={vert1:e[0],vert2:e[1],face1:n,face2:void 0},m++):f[r].face2=n}var s=new Float32Array(2*m*3),t=0;for(var r in f){var u=f[r];if(void 0===u.face2||l[u.face1].normal.dot(l[u.face2].normal)<.9999){var v=k[u.vert1];s[t++]=v.x,s[t++]=v.y,s[t++]=v.z,v=k[u.vert2],s[t++]=v.x,s[t++]=v.y,s[t++]=v.z}}i.addAttribute("position",new a.BufferAttribute(s,3)),a.Line.call(this,i,new a.LineBasicMaterial({color:d}),a.LinePieces),this.matrix=b.matrixWorld,this.matrixAutoUpdate=!1},a.EdgesHelper.prototype=Object.create(a.Line.prototype),a.FaceNormalsHelper=function(b,c,d,e){this.object=b,this.size=void 0!==c?c:1;for(var f=void 0!==d?d:16776960,g=void 0!==e?e:1,h=new a.Geometry,i=this.object.geometry.faces,j=0,k=i.length;k>j;j++)h.vertices.push(new a.Vector3,new a.Vector3);a.Line.call(this,h,new a.LineBasicMaterial({color:f,linewidth:g}),a.LinePieces),this.matrixAutoUpdate=!1,this.normalMatrix=new a.Matrix3,this.update()},a.FaceNormalsHelper.prototype=Object.create(a.Line.prototype),a.FaceNormalsHelper.prototype.update=function(){var a=this.geometry.vertices,b=this.object,c=b.geometry.vertices,d=b.geometry.faces,e=b.matrixWorld;b.updateMatrixWorld(!0),this.normalMatrix.getNormalMatrix(e);for(var f=0,g=0,h=d.length;h>f;f++,g+=2){var i=d[f];a[g].copy(c[i.a]).add(c[i.b]).add(c[i.c]).divideScalar(3).applyMatrix4(e),a[g+1].copy(i.normal).applyMatrix3(this.normalMatrix).normalize().multiplyScalar(this.size).add(a[g])}return this.geometry.verticesNeedUpdate=!0,this},a.GridHelper=function(b,c){var d=new a.Geometry,e=new a.LineBasicMaterial({vertexColors:a.VertexColors});this.color1=new a.Color(4473924),this.color2=new a.Color(8947848);for(var f=-b;b>=f;f+=c){d.vertices.push(new a.Vector3(-b,0,f),new a.Vector3(b,0,f),new a.Vector3(f,0,-b),new a.Vector3(f,0,b));var g=0===f?this.color1:this.color2;d.colors.push(g,g,g,g)}a.Line.call(this,d,e,a.LinePieces)},a.GridHelper.prototype=Object.create(a.Line.prototype),a.GridHelper.prototype.setColors=function(a,b){this.color1.set(a),this.color2.set(b),this.geometry.colorsNeedUpdate=!0},a.HemisphereLightHelper=function(b,c,d,e){a.Object3D.call(this),this.light=b,this.light.updateMatrixWorld(),this.matrix=b.matrixWorld,this.matrixAutoUpdate=!1,this.colors=[new a.Color,new a.Color];var f=new a.SphereGeometry(c,4,2);f.applyMatrix((new a.Matrix4).makeRotationX(-Math.PI/2));for(var g=0,h=8;h>g;g++)f.faces[g].color=this.colors[4>g?0:1];var i=new a.MeshBasicMaterial({vertexColors:a.FaceColors,wireframe:!0});this.lightSphere=new a.Mesh(f,i),this.add(this.lightSphere),this.update()},a.HemisphereLightHelper.prototype=Object.create(a.Object3D.prototype),a.HemisphereLightHelper.prototype.dispose=function(){this.lightSphere.geometry.dispose(),this.lightSphere.material.dispose()},a.HemisphereLightHelper.prototype.update=function(){var b=new a.Vector3;return function(){this.colors[0].copy(this.light.color).multiplyScalar(this.light.intensity),this.colors[1].copy(this.light.groundColor).multiplyScalar(this.light.intensity),this.lightSphere.lookAt(b.setFromMatrixPosition(this.light.matrixWorld).negate()),this.lightSphere.geometry.colorsNeedUpdate=!0}}(),a.PointLightHelper=function(b,c){this.light=b,this.light.updateMatrixWorld();var d=new a.SphereGeometry(c,4,2),e=new a.MeshBasicMaterial({wireframe:!0,fog:!1});e.color.copy(this.light.color).multiplyScalar(this.light.intensity),a.Mesh.call(this,d,e),this.matrix=this.light.matrixWorld,this.matrixAutoUpdate=!1},a.PointLightHelper.prototype=Object.create(a.Mesh.prototype),a.PointLightHelper.prototype.dispose=function(){this.geometry.dispose(),this.material.dispose()},a.PointLightHelper.prototype.update=function(){this.material.color.copy(this.light.color).multiplyScalar(this.light.intensity)},a.SkeletonHelper=function(b){this.bones=this.getBoneList(b);for(var c=new a.Geometry,d=0;d<this.bones.length;d++){var e=this.bones[d];e.parent instanceof a.Bone&&(c.vertices.push(new a.Vector3),c.vertices.push(new a.Vector3),c.colors.push(new a.Color(0,0,1)),c.colors.push(new a.Color(0,1,0)))}var f=new a.LineBasicMaterial({vertexColors:a.VertexColors,depthTest:!1,depthWrite:!1,transparent:!0});a.Line.call(this,c,f,a.LinePieces),this.root=b,this.matrix=b.matrixWorld,this.matrixAutoUpdate=!1,this.update()},a.SkeletonHelper.prototype=Object.create(a.Line.prototype),a.SkeletonHelper.prototype.getBoneList=function(b){var c=[];b instanceof a.Bone&&c.push(b);for(var d=0;d<b.children.length;d++)c.push.apply(c,this.getBoneList(b.children[d]));return c},a.SkeletonHelper.prototype.update=function(){for(var b=this.geometry,c=(new a.Matrix4).getInverse(this.root.matrixWorld),d=new a.Matrix4,e=0,f=0;f<this.bones.length;f++){var g=this.bones[f];g.parent instanceof a.Bone&&(d.multiplyMatrices(c,g.matrixWorld),b.vertices[e].setFromMatrixPosition(d),d.multiplyMatrices(c,g.parent.matrixWorld),b.vertices[e+1].setFromMatrixPosition(d),e+=2)}b.verticesNeedUpdate=!0,b.computeBoundingSphere()},a.SpotLightHelper=function(b){a.Object3D.call(this),this.light=b,this.light.updateMatrixWorld(),this.matrix=b.matrixWorld,this.matrixAutoUpdate=!1;var c=new a.CylinderGeometry(0,1,1,8,1,!0);c.applyMatrix((new a.Matrix4).makeTranslation(0,-.5,0)),c.applyMatrix((new a.Matrix4).makeRotationX(-Math.PI/2));var d=new a.MeshBasicMaterial({wireframe:!0,fog:!1});this.cone=new a.Mesh(c,d),this.add(this.cone),this.update()},a.SpotLightHelper.prototype=Object.create(a.Object3D.prototype),a.SpotLightHelper.prototype.dispose=function(){this.cone.geometry.dispose(),this.cone.material.dispose()},a.SpotLightHelper.prototype.update=function(){var b=new a.Vector3,c=new a.Vector3;return function(){var a=this.light.distance?this.light.distance:1e4,d=a*Math.tan(this.light.angle);this.cone.scale.set(d,d,a),b.setFromMatrixPosition(this.light.matrixWorld),c.setFromMatrixPosition(this.light.target.matrixWorld),this.cone.lookAt(c.sub(b)),this.cone.material.color.copy(this.light.color).multiplyScalar(this.light.intensity)}}(),a.VertexNormalsHelper=function(b,c,d,e){this.object=b,this.size=void 0!==c?c:1;for(var f=void 0!==d?d:16711680,g=void 0!==e?e:1,h=new a.Geometry,i=(b.geometry.vertices,b.geometry.faces),j=0,k=i.length;k>j;j++)for(var l=i[j],m=0,n=l.vertexNormals.length;n>m;m++)h.vertices.push(new a.Vector3,new a.Vector3);a.Line.call(this,h,new a.LineBasicMaterial({color:f,linewidth:g}),a.LinePieces),this.matrixAutoUpdate=!1,this.normalMatrix=new a.Matrix3,this.update()},a.VertexNormalsHelper.prototype=Object.create(a.Line.prototype),a.VertexNormalsHelper.prototype.update=function(b){var c=new a.Vector3;return function(a){var b=["a","b","c","d"];this.object.updateMatrixWorld(!0),this.normalMatrix.getNormalMatrix(this.object.matrixWorld);for(var d=this.geometry.vertices,e=this.object.geometry.vertices,f=this.object.geometry.faces,g=this.object.matrixWorld,h=0,i=0,j=f.length;j>i;i++)for(var k=f[i],l=0,m=k.vertexNormals.length;m>l;l++){var n=k[b[l]],o=e[n],p=k.vertexNormals[l];d[h].copy(o).applyMatrix4(g),c.copy(p).applyMatrix3(this.normalMatrix).normalize().multiplyScalar(this.size),c.add(d[h]),h+=1,d[h].copy(c),h+=1}return this.geometry.verticesNeedUpdate=!0,this}}(),a.VertexTangentsHelper=function(b,c,d,e){this.object=b,this.size=void 0!==c?c:1;for(var f=void 0!==d?d:255,g=void 0!==e?e:1,h=new a.Geometry,i=(b.geometry.vertices,b.geometry.faces),j=0,k=i.length;k>j;j++)for(var l=i[j],m=0,n=l.vertexTangents.length;n>m;m++)h.vertices.push(new a.Vector3),h.vertices.push(new a.Vector3);a.Line.call(this,h,new a.LineBasicMaterial({color:f,linewidth:g}),a.LinePieces),this.matrixAutoUpdate=!1,this.update()},a.VertexTangentsHelper.prototype=Object.create(a.Line.prototype),a.VertexTangentsHelper.prototype.update=function(b){var c=new a.Vector3;return function(a){var b=["a","b","c","d"];this.object.updateMatrixWorld(!0);for(var d=this.geometry.vertices,e=this.object.geometry.vertices,f=this.object.geometry.faces,g=this.object.matrixWorld,h=0,i=0,j=f.length;j>i;i++)for(var k=f[i],l=0,m=k.vertexTangents.length;m>l;l++){var n=k[b[l]],o=e[n],p=k.vertexTangents[l];d[h].copy(o).applyMatrix4(g),c.copy(p).transformDirection(g).multiplyScalar(this.size),c.add(d[h]),h+=1,d[h].copy(c),h+=1}return this.geometry.verticesNeedUpdate=!0,this}}(),a.WireframeHelper=function(b,c){var d=void 0!==c?c:16777215,e=[0,0],f={},g=function(a,b){return a-b},h=["a","b","c"],i=new a.BufferGeometry;if(b.geometry instanceof a.Geometry){for(var j=b.geometry.vertices,k=b.geometry.faces,l=0,m=new Uint32Array(6*k.length),n=0,o=k.length;o>n;n++)for(var p=k[n],q=0;3>q;q++){e[0]=p[h[q]],e[1]=p[h[(q+1)%3]],e.sort(g);var r=e.toString();void 0===f[r]&&(m[2*l]=e[0],m[2*l+1]=e[1],f[r]=!0,l++)}for(var s=new Float32Array(2*l*3),n=0,o=l;o>n;n++)for(var q=0;2>q;q++){var t=j[m[2*n+q]],u=6*n+3*q;s[u+0]=t.x,s[u+1]=t.y,s[u+2]=t.z}i.addAttribute("position",new a.BufferAttribute(s,3))}else if(b.geometry instanceof a.BufferGeometry)if(void 0!==b.geometry.attributes.index){var j=b.geometry.attributes.position.array,v=b.geometry.attributes.index.array,w=b.geometry.drawcalls,l=0;0===w.length&&(w=[{count:v.length,index:0,start:0}]);for(var m=new Uint32Array(2*v.length),x=0,y=w.length;y>x;++x)for(var z=w[x].start,A=w[x].count,u=w[x].index,n=z,B=z+A;B>n;n+=3)for(var q=0;3>q;q++){e[0]=u+v[n+q],e[1]=u+v[n+(q+1)%3],e.sort(g);var r=e.toString();void 0===f[r]&&(m[2*l]=e[0],m[2*l+1]=e[1],f[r]=!0,l++)}for(var s=new Float32Array(2*l*3),n=0,o=l;o>n;n++)for(var q=0;2>q;q++){var u=6*n+3*q,C=3*m[2*n+q];s[u+0]=j[C],s[u+1]=j[C+1],s[u+2]=j[C+2]}i.addAttribute("position",new a.BufferAttribute(s,3))}else{for(var j=b.geometry.attributes.position.array,l=j.length/3,D=l/3,s=new Float32Array(2*l*3),n=0,o=D;o>n;n++)for(var q=0;3>q;q++){var u=18*n+6*q,E=9*n+3*q;s[u+0]=j[E],s[u+1]=j[E+1],s[u+2]=j[E+2];var C=9*n+3*((q+1)%3);s[u+3]=j[C],s[u+4]=j[C+1],s[u+5]=j[C+2]}i.addAttribute("position",new a.BufferAttribute(s,3))}a.Line.call(this,i,new a.LineBasicMaterial({color:d}),a.LinePieces),this.matrix=b.matrixWorld,this.matrixAutoUpdate=!1},a.WireframeHelper.prototype=Object.create(a.Line.prototype),a.ImmediateRenderObject=function(){a.Object3D.call(this),this.render=function(a){}},a.ImmediateRenderObject.prototype=Object.create(a.Object3D.prototype),a.MorphBlendMesh=function(b,c){a.Mesh.call(this,b,c),this.animationsMap={},this.animationsList=[];var d=this.geometry.morphTargets.length,e="__default",f=0,g=d-1,h=d/1;this.createAnimation(e,f,g,h),this.setAnimationWeight(e,1)},a.MorphBlendMesh.prototype=Object.create(a.Mesh.prototype),a.MorphBlendMesh.prototype.createAnimation=function(a,b,c,d){var e={startFrame:b,endFrame:c,length:c-b+1,fps:d,duration:(c-b)/d,lastFrame:0,currentFrame:0,active:!1,time:0,direction:1,weight:1,directionBackwards:!1,mirroredLoop:!1};this.animationsMap[a]=e,this.animationsList.push(e)},a.MorphBlendMesh.prototype.autoCreateAnimations=function(a){for(var b,c=/([a-z]+)_?(\d+)/,d={},e=this.geometry,f=0,g=e.morphTargets.length;g>f;f++){var h=e.morphTargets[f],i=h.name.match(c);if(i&&i.length>1){var j=i[1];i[2];d[j]||(d[j]={start:1/0,end:-(1/0)});var k=d[j];f<k.start&&(k.start=f),f>k.end&&(k.end=f),b||(b=j)}}for(var j in d){var k=d[j];this.createAnimation(j,k.start,k.end,a)}this.firstAnimation=b},a.MorphBlendMesh.prototype.setAnimationDirectionForward=function(a){var b=this.animationsMap[a];b&&(b.direction=1,b.directionBackwards=!1)},a.MorphBlendMesh.prototype.setAnimationDirectionBackward=function(a){var b=this.animationsMap[a];b&&(b.direction=-1,b.directionBackwards=!0)},a.MorphBlendMesh.prototype.setAnimationFPS=function(a,b){var c=this.animationsMap[a];c&&(c.fps=b,c.duration=(c.end-c.start)/c.fps)},a.MorphBlendMesh.prototype.setAnimationDuration=function(a,b){var c=this.animationsMap[a];c&&(c.duration=b,c.fps=(c.end-c.start)/c.duration)},a.MorphBlendMesh.prototype.setAnimationWeight=function(a,b){var c=this.animationsMap[a];c&&(c.weight=b)},a.MorphBlendMesh.prototype.setAnimationTime=function(a,b){var c=this.animationsMap[a];c&&(c.time=b)},a.MorphBlendMesh.prototype.getAnimationTime=function(a){var b=0,c=this.animationsMap[a];return c&&(b=c.time),b},a.MorphBlendMesh.prototype.getAnimationDuration=function(a){var b=-1,c=this.animationsMap[a];return c&&(b=c.duration),b},a.MorphBlendMesh.prototype.playAnimation=function(a){var b=this.animationsMap[a];b?(b.time=0,b.active=!0):console.warn("animation["+a+"] undefined")},a.MorphBlendMesh.prototype.stopAnimation=function(a){var b=this.animationsMap[a];b&&(b.active=!1)},a.MorphBlendMesh.prototype.update=function(b){for(var c=0,d=this.animationsList.length;d>c;c++){var e=this.animationsList[c];if(e.active){var f=e.duration/e.length;e.time+=e.direction*b,e.mirroredLoop?(e.time>e.duration||e.time<0)&&(e.direction*=-1,e.time>e.duration&&(e.time=e.duration,e.directionBackwards=!0),e.time<0&&(e.time=0,e.directionBackwards=!1)):(e.time=e.time%e.duration,e.time<0&&(e.time+=e.duration));var g=e.startFrame+a.Math.clamp(Math.floor(e.time/f),0,e.length-1),h=e.weight;g!==e.currentFrame&&(this.morphTargetInfluences[e.lastFrame]=0,this.morphTargetInfluences[e.currentFrame]=1*h,this.morphTargetInfluences[g]=0,e.lastFrame=e.currentFrame,e.currentFrame=g);var i=e.time%f/f;e.directionBackwards&&(i=1-i),this.morphTargetInfluences[e.currentFrame]=i*h,this.morphTargetInfluences[e.lastFrame]=(1-i)*h}}}}(),BrainBrowser.SurfaceViewer.modules.annotations=function(a){"use strict";function b(b){b=b||{};var c=b.model_name||null;return!c&&a.model.children[0]&&(c=a.model.children[0].userData.model_name),c}var c=BrainBrowser.createTreeStore(),d=.5,e=65280,f=16711680;a.annotations={add:function(e,g,h){h=h||{};var i,j,k=b(h);k&&(j=a.getVertex(e,{model_name:h.model_name}),i={data:g,model_name:k,vertex:e,position:j,marker:a.drawDot(j.x,j.y,j.z,d,f)},i.marker.userData.annotation_info=i,c.set(k,e,i),h.activate!==!1&&a.annotations.activate(e,h))},get:function(d,e){e=e||{};var f,g=b(e);return g?(f=c.get(g,d),e.activate!==!1&&a.annotations.activate(d,e),f):null},remove:function(d,e){e=e||{};var f,g=b(e);return g?(f=c.remove(g,d),a.model.remove(f.marker),f.marker=null,a.updated=!0,f):null},reset:function(){a.annotations.forEach(function(b){a.annotations.remove(b.vertex)})},activate:function(b,c){c=c||{};var d=a.annotations.get(b,{model_name:c.model_name,activate:!1});d&&(a.annotations.forEach(function(a){a===d?a.marker.material.color.setHex(e):a.marker.material.color.setHex(f)}),a.updated=!0)},forEach:function(a){c.forEach(2,a)},setMarkerOnColor:function(a){e=a},setMarkerOffColor:function(a){f=a},setMarkerRadius:function(a){d=a}}},BrainBrowser.SurfaceViewer.modules.color=function(a){"use strict";function b(a,b){var c,d,e,f,g,h,i,j,k,l,m,n,o,p;for(i=0,k=b.length;k>i;i++){if(d=b[i],l=d.getObjectByName("__WIREFRAME__"),p=!!l,p&&(m=l.geometry.attributes.color.array),c=d.geometry,e=d.userData.original_data.indices,f=c.attributes.color,g=f.array,BrainBrowser.WEBGL_UINT_INDEX_ENABLED){for(h=0;h<g.length;h++)g[h]=a[h];p&&l.geometry.attributes.color.array.set(a)}else for(h=0,j=e.length;j>h;h+=3)n=4*h,o=2*n,g[n]=a[4*e[h]],g[n+1]=a[4*e[h]+1],g[n+2]=a[4*e[h]+2],g[n+3]=1,g[n+4]=a[4*e[h+1]],g[n+5]=a[4*e[h+1]+1],g[n+6]=a[4*e[h+1]+2],g[n+7]=1,g[n+8]=a[4*e[h+2]],g[n+9]=a[4*e[h+2]+1],g[n+10]=a[4*e[h+2]+2],g[n+11]=1,p&&(m[o]=g[n],m[o+1]=g[n+1],m[o+2]=g[n+2],m[o+3]=g[n+3],m[o+4]=g[n+4],m[o+5]=g[n+5],m[o+6]=g[n+6],m[o+7]=g[n+7],m[o+8]=g[n+4],m[o+9]=g[n+5],m[o+10]=g[n+6],m[o+11]=g[n+7],m[o+12]=g[n+8],m[o+13]=g[n+9],m[o+14]=g[n+10],m[o+15]=g[n+11],m[o+16]=g[n+8],m[o+17]=g[n+9],m[o+18]=g[n+10],m[o+19]=g[n+11],m[o+20]=g[n],m[o+21]=g[n+1],m[o+22]=g[n+2],m[o+23]=g[n+3]);f.needsUpdate=!0,p&&(l.geometry.attributes.color.needsUpdate=!0)}}function c(b,c){var d,e,f,g,h,i,j,k=a.model_data.get(c),l=[],m=[];for(b.forEach(function(b){l.push(a.color_map.mapColors(b.values,{min:b.range_min,max:b.range_max,default_colors:k.colors})),m.push(b.alpha)}),d=new Float32Array(l[0].length),e=0,i=l[0].length/4;i>e;e++)for(f=0,h=l.length;h>f;f++)g=4*e,j=m[f],d[g]+=l[f][g]*j,d[g+1]+=l[f][g+1]*j,d[g+2]+=l[f][g+2]*j,d[g+3]+=j;return d}var d=null,e=[];a.updateColors=function(f){function g(c){var d,f=a.model.getObjectByName(l,!0);d=f?[f]:a.model.children.filter(function(a){return!!a.userData.original_data}),b(c,d),e.forEach(function(a){a()}),e.length=0,a.triggerEvent("updatecolors",{model_data:m,intensity_data:h,colors:c,blend:i}),i&&a.triggerEvent("blendcolors",{model_data:m,intensity_data:h,colors:c})}f=f||{};var h,i,j=f.complete,k=f.model_name,l=f.shape_name||k+"_1",m=a.model_data.get(k);BrainBrowser.utils.isFunction(j)&&e.push(j),m.intensity_data.length>1?(h=m.intensity_data,i=!0):(h=m.intensity_data[0],i=!1),clearTimeout(d),d=setTimeout(function(){g(i?c(h,f.model_name):a.color_map.mapColors(h.values,{min:h.range_min,max:h.range_max,default_colors:a.model_data.get(f.model_name).colors}))},0)},a.setIntensity=function(){var b,c,d,e,f,g=Array.prototype.slice.call(arguments);b=BrainBrowser.utils.isNumeric(g[0])?a.model_data.getDefaultIntensityData():g.shift(),f=b.model_data,c=g[0],d=g[1],e=g[2]||{},b&&c>=0&&c<b.values.length&&(b.values[c]=d,a.updateColors({model_name:f.name,complete:e.complete}),a.triggerEvent("updateintensitydata",{model_data:f,intensity_data:b,index:c,value:d}))},a.setIntensityRange=function(){var b,c,d,e,f,g=Array.prototype.slice.call(arguments);b=BrainBrowser.utils.isNumeric(g[0])?a.model_data.getDefaultIntensityData():g.shift(),f=b.model_data,c=g[0],d=g[1],e=g[2]||{},b.range_min=c,b.range_max=d,a.updateColors({model_name:f.name,complete:e.complete}),a.triggerEvent("changeintensityrange",{model_data:f,intensity_data:b,min:c,max:d})},a.blend=function(){var b,c,d=Array.prototype.slice.call(arguments).filter(function(a){return void 0!==a});"string"==typeof d[0]&&(b=d.shift()),BrainBrowser.utils.isFunction(d[d.length-1])&&(c=d.pop());var e=a.model_data.get(b).intensity_data,f=(1-d.reduce(function(a,b){return a+b},0))/(e.length-d.length);e.forEach(function(a,b){b<d.length?a.alpha=d[b]:a.alpha=f}),a.updateColors({model_name:b,complete:c})}},BrainBrowser.SurfaceViewer.modules.loading=function(a){"use strict";function b(a,b,c){c=c||{};var d=c.format||"mniobj",f=c.parse||{};e(a,d,f,function(a){BrainBrowser.loader.checkCancel(c.cancel)||h(a,b,c)})}function c(b,c,e){e=e||{};var f=e.name||c,g=e.format||"text",h=e.blend,i=e.model_name,j=a.model_data.get(i),k=j.intensity_data[0],l={};i=i||j.name,a.getAttribute("fix_color_range")&&k&&(l={min:k.range_min,max:k.range_max}),n.parseIntensityData(b,g,function(b){var c,g;b.colors&&d(BrainBrowser.createColorMap(b.colors)),a.getAttribute("fix_color_range")&&void 0!==l.min&&void 0!==l.max?(c=l.min,g=l.max):(c=void 0===e.min?b.min:e.min,g=void 0===e.max?b.max:e.max),b.name=f,h||(j.intensity_data.length=0),j.intensity_data.push(b),b.model_data=j,b.range_min=c,b.range_max=g,j.intensity_data.length>1?a.blend(e.complete):a.updateColors({model_name:i,complete:e.complete}),a.triggerEvent("loadintensitydata",{model_data:j,intensity_data:b})})}function d(b){a.color_map=b,a.triggerEvent("loadcolormap",{color_map:b}),a.model_data.forEach(function(b){b.intensity_data[0]&&a.updateColors({model_name:b.name})})}function e(a,b,c,d){var e,g=b+"_model";if(!n.worker_urls[g])throw e="error in SurfaceViewer configuration.\nModel worker URL for "+b+" not defined.\nUse 'BrainBrowser.config.set(\"model_types."+b+".worker\", ...)' to set it.",BrainBrowser.events.triggerEvent("error",{message:e}),new Error(e);var h,i=new Worker(n.worker_urls[g]);i.addEventListener("message",function(a){var g,j=a.data;if(j.error)throw e="error parsing model.\n"+j.error_message+"\nFile type: "+b+"\nOptions: "+JSON.stringify(c),BrainBrowser.events.triggerEvent("error",{message:e}),new Error(e);j.colors=j.colors||new Float32Array([.7,.7,.7,1]),BrainBrowser.WEBGL_UINT_INDEX_ENABLED?f(j,d):(h=new Worker(n.worker_urls.deindex),h.addEventListener("message",function(a){d(a.data)}),g=[j.vertices.buffer],j.normals&&g.push(j.normals.buffer),j.colors&&g.push(j.colors.buffer),j.shapes.forEach(function(a){g.push(a.indices.buffer)}),h.postMessage(j,g)),i.terminate()});var j=BrainBrowser.utils.getWorkerImportURL();i.postMessage({data:a,options:c,url:j})}function f(a,b){var c=a.vertices;4===a.colors.length&&(a.colors=g(a.colors,c.length/3)),k(a),b(a)}function g(a,b){var c,d,e,f,g,h,i;for(g=new Float32Array(4*b),c=a[0],d=a[1],e=a[2],f=a[3],h=0,i=g.length;i>h;h+=4)g[h]=c,g[h+1]=d,g[h+2]=e,g[h+3]=f;return g}function h(b,c,d){d=d||{};var e=d.complete,f=i(b,c,d);a.triggerEvent("displaymodel",{model:a.model,model_data:b,new_shapes:f}),e&&e()}function i(b,c,d){var e,f,g,h,i,k,m,n=a.model,p=b.shapes,q="line"===b.type,r=d.render_depth,s=d.pick_ignore,t=d.recenter||b.split,u=[],v={is_line:q};if(BrainBrowser.WEBGL_UINT_INDEX_ENABLED&&(i=new o.BufferAttribute(new Float32Array(b.vertices),3),b.normals&&(k=new o.BufferAttribute(new Float32Array(b.normals),3)),b.colors&&(m=new o.BufferAttribute(new Float32Array(b.colors),4))),b.name=b.name||c,a.model_data.add(b.name,b),p){for(g=0,h=p.length;h>g;g++)f=b.shapes[g],0!==f.indices.length&&(BrainBrowser.WEBGL_UINT_INDEX_ENABLED?(l(m.array,f.color,f.indices),v={position:i,normal:k,color:m,index:new o.BufferAttribute(new Uint32Array(f.indices),1)}):(i=k=m=null,i=new o.BufferAttribute(new Float32Array(f.unindexed.position),3),f.unindexed.normal&&(k=new o.BufferAttribute(new Float32Array(f.unindexed.normal),3)),f.unindexed.color&&(m=new o.BufferAttribute(new Float32Array(f.unindexed.color),4)),v={position:i,normal:k,color:m}),v.is_line=q,v.centroid=f.centroid,v.recenter=t,e=j(v),e.name=f.name||c+"_"+(g+1),e.userData.model_name=b.name,e.userData.original_data={vertices:b.vertices,indices:f.indices,normals:b.normals,colors:b.colors},e.userData.pick_ignore=s,r&&(e.renderDepth=r),u.push(e),n.add(e));b.split&&(n.children[0].name="left",n.children[1].name="right")}return u}function j(a){var b,c,d,e,f,g,h,i=a.position,j=i.array,k=a.normal,l=a.color,m=a.index,n=a.centroid,p=a.is_line,q=a.recenter,r=new o.BufferGeometry;if(r.dynamic=!0,q)if(m)for(b=m.array,c=new Float32Array(j),g=0,h=b.length;h>g;g++)d=3*b[g],j[d]=c[d]-n.x,j[d+1]=c[d+1]-n.y,j[d+2]=c[d+2]-n.z;else for(g=0,h=j.length;h>g;g+=3)j[g]-=n.x,j[g+1]-=n.y,j[g+2]-=n.z;return r.addAttribute("position",i),m&&r.addAttribute("index",m),k?r.addAttribute("normal",k):r.computeVertexNormals(),l&&r.addAttribute("color",l),p?(e=new o.LineBasicMaterial({vertexColors:o.VertexColors}),f=new o.Line(r,e,o.LinePieces)):(e=new o.MeshPhongMaterial({color:16777215,ambient:16777215,specular:1052688,shininess:150,vertexColors:o.VertexColors}),f=new o.Mesh(r,e),f.userData.has_wireframe=!0),f.userData.centroid=n,q&&(f.userData.recentered=!0,f.position.set(n.x,n.y,n.z)),f}function k(a){var b,c,d,e,f,g,h=a.vertices;b=d=f=Number.POSITIVE_INFINITY,c=e=g=Number.NEGATIVE_INFINITY,a.shapes.forEach(function(a){var i,j,k,l,m,n,o,p,q,r,s,t,u=a.indices;for(o=q=s=Number.POSITIVE_INFINITY,p=r=t=Number.NEGATIVE_INFINITY,m=0,n=u.length;n>m;m++)i=u[m],j=h[3*i],k=h[3*i+1],l=h[3*i+2],o=Math.min(o,j),q=Math.min(q,k),s=Math.min(s,l),p=Math.max(p,j),r=Math.max(r,k),t=Math.max(t,l);a.bounding_box={min_x:o,min_y:q,min_z:s,max_x:p,max_y:r,max_z:t},a.centroid={x:o+(p-o)/2,y:q+(r-q)/2,z:s+(t-s)/2},b=Math.min(b,o),d=Math.min(d,q),f=Math.min(f,s),c=Math.max(c,p),e=Math.max(e,r),g=Math.max(g,t)}),a.bounding_box={min_x:b,min_y:d,min_z:f,max_x:c,max_y:e,max_z:g},a.size={x:c-b,y:e-d,z:g-f}}function l(a,b,c){if(b){var d,e,f,g,h,i,j,k=4===b.length;for(d=b[0],e=b[1],f=b[2],g=b[3],h=0,j=c.length;j>h;h++)k||(i=4*h,d=b[i],e=b[i+1],f=b[i+2],g=b[i+3]),i=4*c[h],a[i]=d,a[i+1]=e,a[i+2]=f,a[i+3]=g}}function m(a,b){b=b||{};var c=b.format||"mniobj",d=BrainBrowser.config.get(a+"."+c);return d&&d.binary&&(b.result_type=b.result_type||"arraybuffer"),b}var n=BrainBrowser.SurfaceViewer,o=n.THREE,p=BrainBrowser.loader,q={};a.model_data={add:function(a,b){q[a]=b,b.intensity_data=[]},get:function(a){return a=a||Object.keys(q)[0],q[a]||null},getDefaultIntensityData:function(a){var b,c,d,e;if(a)b=this.get(a),c=b?b.intensity_data[0]:null;else for(b=Object.keys(q).map(function(a){return q[a]}),d=0,e=b.length;e>d&&!(c=b[d].intensity_data[0]);d++);return c||null},count:function(){return Object.keys(q).length},clear:function(){q={}},forEach:function(a){Object.keys(q).forEach(function(b){a(q[b],b)})}},a.loadModelFromURL=function(a,c){c=m("model_types",c),p.loadFromURL(a,b,c)},a.loadModelFromFile=function(a,c){c=m("model_types",c),p.loadFromFile(a,b,c)},a.loadIntensityDataFromURL=function(a,b){b=m("intensity_data_types",b),p.loadFromURL(a,c,b)},a.loadIntensityDataFromFile=function(a,b){b=m("intensity_data_types",b),p.loadFromFile(a,c,b)},a.loadColorMapFromURL=function(a,b){p.loadColorMapFromURL(a,d,b)},a.loadColorMapFromFile=function(a,b){p.loadColorMapFromFile(a,d,b)},a.clearScreen=function(){for(var b=a.model.children;b.length>0;)a.model.remove(b[0]);a.model_data.clear(),a.resetView(),a.triggerEvent("clearscreen")}},BrainBrowser.SurfaceViewer.modules.rendering=function(a){"use strict";function b(f){var g,m,n=a.model,o=i.position,p=j/a.zoom;window.requestAnimationFrame(b),d=c||f,c=f,g=c-d,m=15e-5*g,a.autorotate.x&&(n.rotation.x+=m,a.updated=!0),a.autorotate.y&&(n.rotation.y+=m,a.updated=!0),a.autorotate.z&&(n.rotation.z+=m,a.updated=!0),e!==a.zoom&&(e=a.zoom,a.updated=!0,a.triggerEvent("zoom",{zoom:a.zoom})),a.updated&&(p>i.near&&p<.9*i.far&&(o.z=p,k.position.z=p),l.render(h,i),a.triggerEvent("draw",{renderer:l,scene:h,camera:i}),a.updated=!1)}var c,d,e,f=BrainBrowser.SurfaceViewer.THREE,g=new f.WebGLRenderer({preserveDrawingBuffer:!0,alpha:!0,autoClear:!1}),h=new f.Scene,i=new f.PerspectiveCamera(30,a.dom_element.offsetWidth/a.dom_element.offsetHeight,1,3e3),j=500,k=new f.PointLight(16777215),l=g,m={},n=g.domElement;a.model=new f.Object3D,h.add(a.model),a.render=function(){var c=a.dom_element;g.setClearColor(0),c.appendChild(g.domElement),i.position.z=j,k.position.set(0,0,j),h.add(k),a.updateViewport(),b()},a.updateViewport=function(){var b=a.dom_element;l.setSize(b.offsetWidth,b.offsetHeight),i.aspect=b.offsetWidth/b.offsetHeight,i.updateProjectionMatrix(),a.updated=!0},a.canvasDataURL=function(){return g.domElement.toDataURL()},a.addEffect=function(b){var c;BrainBrowser.utils.isFunction(f[b])&&(c=new f[b](g),c.setSize(a.dom_element.offsetWidth,a.dom_element.offsetHeight),m[b]=c)},a.setEffect=function(b){l=m[b]?m[b]:g,l.setSize(a.dom_element.offsetWidth,a.dom_element.offsetHeight),l.render(h,i),a.updated=!0},a.setCameraPosition=function(b,c,d){i.position.set(b,c,d),k.position.set(b,c,d),a.updated=!0},a.getCameraPosition=function(){return i.position},a.resetView=function(){var b=a.model,c=new f.Matrix4;c.getInverse(b.matrix),b.applyMatrix(c),i.position.set(0,0,j),k.position.set(0,0,j);var d=b.userData.model_center_offset||new f.Vector3(0,0,0);b.children.forEach(function(a){var b=a.userData.centroid,c=a.userData.recentered;a.userData.original_data&&(b&&c?a.position.set(b.x+d.x,b.y+d.y,b.z+d.z):a.position.set(d.x,d.y,d.z),a.rotation.set(0,0,0),a.material.opacity=1)}),b.rotation.set(0,0,0),a.zoom=1,a.updated=!0},a.setClearColor=function(b,c){void 0===c&&(c=1),g.setClearColor(b,c),a.updated=!0},a.drawDot=function(b,c,d,e,g){e=e||2,e=e>=0?e:0,g=g>=0?g:16711680;var i=new f.SphereGeometry(e),j=new f.MeshBasicMaterial({color:g}),k=new f.Mesh(i,j);if(k.position.set(b,c,d),a.model){var l=a.model.userData.model_center_offset;void 0!==l&&(k.translateX(-l.x),k.translateY(-l.y),k.translateZ(-l.z)),a.model.add(k)}else h.add(k);return a.updated=!0,k},a.drawGrid=function(b,c,d){d=d||{};var e=d.name,g=d.color_center_line,i=d.color_grid,j=d.x||0,k=d.y||0,l=d.z||0,m=d.euler_rotation;(void 0===b||0>=b)&&(b=100),(void 0===c||0>=c)&&(c=10),g=g>=0?g:4473924,i=i>=0?i:8947848;var n=new f.GridHelper(b,c);return n.name=e,n.setColors(g,i),n.position.set(j,k,l),void 0!==m&&n.setRotationFromEuler(m),a.model?a.model.add(n):h.add(n),a.updated=!0,n},a.gridHelper=function(a,b,c,d,e,g,h,i,j,k,l){for(var m=new f.Geometry,n=new f.LineBasicMaterial({vertexColors:f.VertexColors}),o=Object.create(f.GridHelper.prototype),p=a;b>=p;p+=l)m.vertices.push(new f.Vector3(c,0,p),new f.Vector3(d,0,p)),m.colors.push(e,e);for(var q=g;h>=q;q+=l)m.vertices.push(new f.Vector3(q,0,i),new f.Vector3(q,0,j)),m.colors.push(k,k);return f.Line.call(o,m,n,f.LinePieces),o},a.drawLine=function(b,c,d){d=d||{};var e=d.color>=0?d.color:4473924,g=new f.Geometry;g.vertices.push(b.clone()),g.vertices.push(c.clone()),g.computeLineDistances();var i=d.dashed===!0?new f.LineDashedMaterial({linewidth:3,color:e,gapSize:3}):new f.LineBasicMaterial({linewidth:3,color:e}),j=new f.Line(g,i,f.LinePieces);return d.draw===!1?j:(a.model?a.model.add(j):h.add(j),a.updated=!0,j)},a.drawAxes=function(b,c){c=c||{};var d=c.name||"axes",e=c.center||new f.Vector3(0,0,0),g=c.x_color>=0?c.x_color:16711680,i=c.y_color>=0?c.y_color:65280,j=c.z_color>=0?c.z_color:255,k=c.complete===!0;if(void 0===b){
var l=a.model_data.get().size,m=Math.max(l.x,l.y,l.z);b=m/2*1.2}var n=new f.Object3D;return n.name=d,n.add(a.drawLine(e,new f.Vector3(e.x+b,e.y,e.z),{color:g,dashed:!1,draw:!1})),k&&n.add(a.drawLine(e,new f.Vector3(-b+e.x,e.y,e.z),{color:g,dashed:!0,draw:!1})),n.add(a.drawLine(e,new f.Vector3(e.x,e.y+b,e.z),{color:i,dashed:!1,draw:!1})),k&&n.add(a.drawLine(e,new f.Vector3(e.x,-b+e.y,e.z),{color:i,dashed:!0,draw:!1})),n.add(a.drawLine(e,new f.Vector3(e.x,e.y,e.z+b),{color:j,dashed:!1,draw:!1})),k&&n.add(a.drawLine(e,new f.Vector3(e.x,e.y,-b+e.z),{color:j,dashed:!0,draw:!1})),a.model?a.model.add(n):h.add(n),a.updated=!0,n},a.pick=function(b,c,d){b=void 0===b?a.mouse.x:b,c=void 0===c?a.mouse.y:c,d=void 0===d?.25:d/100,b=b/a.dom_element.offsetWidth*2-1,c=-c/a.dom_element.offsetHeight*2+1;var e,g,h,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A=a.model,B=new f.Raycaster,C=new f.Vector3(b,c,i.near),D=null,E=new f.Matrix4;for(C.unproject(i),B.set(i.position,C.sub(i.position).normalize()),e=B.intersectObject(A,!0),u=0;u<e.length;u++)if(e[u].object.userData.pick_ignore=e[u].object.material.opacity<d,!e[u].object.userData.pick_ignore){D=e[u];break}if(null!==D)if(h=D.object,l=D.face,k=[l.a,l.b,l.c],h.userData.annotation_info)g={index:h.userData.annotation_info.vertex,point:h.userData.annotation_info.position,object:h};else{for(h.userData.recentered?(w=h.userData.centroid,x=w.x,y=w.y,z=w.z):x=y=z=0,E.getInverse(h.matrixWorld),j=D.point.applyMatrix4(E),p=h.userData.original_data.vertices,q=h.userData.original_data.indices,r=k[0],BrainBrowser.WEBGL_UINT_INDEX_ENABLED||(r=q[r]),m=r,n=new f.Vector3(p[3*r],p[3*r+1],p[3*r+2]),o=j.distanceTo(new f.Vector3(n.x-x,n.y-y,n.z-z)),u=1,v=k.length;v>u;u++)r=k[u],BrainBrowser.WEBGL_UINT_INDEX_ENABLED||(r=q[r]),s=new f.Vector3(p[3*r],p[3*r+1],p[3*r+2]),t=j.distanceTo(new f.Vector3(s.x-x,s.y-y,s.z-z)),o>t&&(m=r,n=s,o=t);g={index:m,point:n,object:h}}else g=null;return g},a.pickByVertex=function(b,c){var d=a.model;if(c=c||{},void 0===b)return null;var e,f,g=a.getVertex(b,{model_name:c.model_name});return d.children.forEach(function(a){if(!(0===Object.keys(a.userData).length&&a.userData.constructor===Object||0!==Object.keys(a.userData).length&&a.userData.model_name!==c.model_name)){var d,h=a.geometry.attributes.index.array;for(b=parseInt(b,0),d=0;d<h.length;d++)if(h[d]===b){e=a,f={index:b,point:g,object:e};break}}}),f},a.changeCenterRotation=function(b){var c=new f.Vector3(0,0,0);c.copy(b);var d=a.model,e=d.userData.model_center_offset||new f.Vector3(0,0,0);c.x=-e.x-c.x,c.y=-e.y-c.y,c.z=-e.z-c.z,c.negate(),d.children.forEach(function(a){(0!==Object.keys(a.userData).length||a.userData.constructor!==Object)&&(a.translateX(e.x-c.x),a.translateY(e.y-c.y),a.translateZ(e.z-c.z))});var g=(new f.Matrix4).getInverse(d.matrix);d.parent.position.applyMatrix4(g),d.parent.translateX(-e.x),d.parent.translateY(-e.y),d.parent.translateZ(-e.z),d.parent.translateX(c.x),d.parent.translateY(c.y),d.parent.translateZ(c.z),g=(new f.Matrix4).getInverse(d.matrix),d.parent.position.applyMatrix4(d.matrix),a.model.userData.model_center_offset=c,a.updated=!0},a.modelCentric=function(){var b=a.model;a.findUserDataCentroid(b);var c=b.userData.model_center||new f.Vector3(0,0,0);a.setCameraPosition(c.x,c.y,c.z),a.changeCenterRotation(c),a.updated=!0},a.findUserDataCentroid=function(b){if(void 0===b.userData.model_center_offset){var c,d,e,g,h,i;c=e=h=Number.POSITIVE_INFINITY,d=g=i=Number.NEGATIVE_INFINITY,b.children.forEach(function(j){var k=j.userData.model_name,l=a.model_data.get(k),m=j.name;l.shapes.forEach(function(a){if(a.name===m){var b=a.bounding_box;c=Math.min(c,b.min_x),e=Math.min(e,b.min_y),h=Math.min(h,b.min_z),d=Math.max(d,b.max_x),g=Math.max(g,b.max_y),i=Math.max(i,b.max_z)}});var n=new f.Vector3;n.x=c+(d-c)/2,n.y=e+(g-e)/2,n.z=h+(i-h)/2,b.userData.model_center=new f.Vector3(n.x,n.y,n.z)})}},function(){function b(b,c){var d,e,g=new f.Matrix4,h=b.x,l=b.y;if(null!==p)if(d=h-p,e=l-q,"rotate"===o){g.getInverse(m.matrix);var n=new f.Vector3(1,0,0).applyMatrix4(g).normalize();m.rotateOnAxis(n,e/150),g.getInverse(m.matrix),n=new f.Vector3(0,1,0).applyMatrix4(g).normalize(),m.rotateOnAxis(n,d/150),void 0!==a.model_data.related_models&&a.model_data.related_models.forEach(function(a){a.rotation.x=m.rotation.x,a.rotation.y=m.rotation.y,a.rotation.z=m.rotation.z})}else c=c||1,c*=i.position.z/j,i.position.x-=d*c*.25,k.position.x-=d*c*.25,i.position.y+=e*c*.25,k.position.y+=e*c*.25;p=h,q=l,a.updated=!0}function c(){var b,c=a.touches[0].x-a.touches[1].x,d=a.touches[0].y-a.touches[1].y,e=Math.sqrt(c*c+d*d);null!==r&&(b=e-r,a.zoom*=1+.01*b),r=e}function d(c){c.preventDefault(),b(a.mouse,1.1)}function e(d){d.preventDefault(),"zoom"===o?c():b(a.touches[0],2)}function g(){document.removeEventListener("mousemove",d,!1),document.removeEventListener("mouseup",g,!1),p=null,q=null}function h(){document.removeEventListener("touchmove",e,!1),document.removeEventListener("touchend",h,!1),p=null,q=null,r=null}function l(b){var c=Math.max(-1,Math.min(1,b.wheelDelta||-b.detail));b.preventDefault(),a.zoom*=1+.05*c}var m=a.model,o="rotate",p=null,q=null,r=null;n.addEventListener("mousedown",function(a){document.addEventListener("mousemove",d,!1),document.addEventListener("mouseup",g,!1),o=1===a.which?"rotate":"translate"},!1),n.addEventListener("touchstart",function(a){document.addEventListener("touchmove",e,!1),document.addEventListener("touchend",h,!1),o=1===a.touches.length?"rotate":2===a.touches.length?"zoom":"translate"},!1),n.addEventListener("mousewheel",l,!1),n.addEventListener("DOMMouseScroll",l,!1),n.addEventListener("contextmenu",function(a){a.preventDefault()},!1)}()},BrainBrowser.SurfaceViewer.modules.views=function(a){"use strict";function b(a,d){a.userData.creating_wireframe=!0,f>g?c(a,d):setTimeout(function(){b(a,d)},0)}function c(a,b){var c,d=new Worker(BrainBrowser.SurfaceViewer.worker_urls.wireframe),f=a.geometry.attributes;d.addEventListener("message",function(c){var h,i,j,k,l=new e.BufferGeometry;j=c.data.positions?new e.BufferAttribute(c.data.positions,3):f.position,k=c.data.colors?new e.BufferAttribute(c.data.colors,4):f.color,l.addAttribute("position",j),l.addAttribute("color",k),c.data.indices&&l.addAttribute("index",new e.BufferAttribute(c.data.indices,1)),l.attributes.color.needsUpdate=!0,h=new e.LineBasicMaterial({vertexColors:e.VertexColors}),i=new e.Line(l,h,e.LinePieces),i.name="__WIREFRAME__",i.material.visible=!1,a.add(i),a.creating_wireframe=!1,g--,b(i),d.terminate()}),c=BrainBrowser.WEBGL_UINT_INDEX_ENABLED?{indices:f.index.array}:{positions:f.position.array,colors:f.color.array},d.postMessage(c),g++}function d(b,c,d,e){b.material.visible=e||!d,c.material.visible=d,a.updated=!0}var e=BrainBrowser.SurfaceViewer.THREE,f=20,g=0,h={medialView:function(b){var c=a.model;b.split?(c.getObjectByName("left").position.x-=100,c.getObjectByName("left").rotation.z-=Math.PI/2,c.getObjectByName("right").position.x+=100,c.getObjectByName("right").rotation.z+=Math.PI/2,c.rotation.x-=Math.PI/2):(c.rotation.x+=Math.PI/2,c.rotation.y+=Math.PI,c.rotation.z+=Math.PI/2)},lateralView:function(b){var c,d,e=a.model;b.split?(c=e.getObjectByName("left"),d=e.getObjectByName("right"),c.position.x-=100,c.rotation.z-=Math.PI/2,d.position.x+=100,d.rotation.z+=Math.PI/2,e.rotation.x+=Math.PI/2,e.rotation.y+=Math.PI):(e.rotation.x+=Math.PI/2,e.rotation.y+=Math.PI,e.rotation.z-=Math.PI/2)},inferiorView:function(){a.model.rotation.y+=Math.PI},anteriorView:function(){a.resetView(),a.model.rotation.x-=Math.PI/2,a.model.rotation.z+=Math.PI},posteriorView:function(){a.resetView(),a.model.rotation.x-=Math.PI/2}};a.setTransparency=function(b,c){c=c||{};var d,e,f,g=c.shape_name,h=a.model.getObjectByName(g);d=h?[h]:a.model.children||[],d.forEach(function(a){e=a.material,e.opacity=b,1===b||b>1?e.transparent=!1:e.transparent=!0,f=a.getObjectByName("__WIREFRAME__"),f&&(f.material.opacity=e.opacity,f.material.transparent=e.transparent)}),a.updated=!0},a.setWireframe=function(c,e){e=e||{};var f,g,h=e.keep_surface!==!0?!1:!0,i=e.shape_name,j=a.model.getObjectByName(i);f=j?[j]:a.model.children||[],f.forEach(function(a){g=a.getObjectByName("__WIREFRAME__"),g?d(a,g,c,h):a.userData.has_wireframe&&!a.userData.creating_wireframe&&b(a,function(b){d(a,b,c,h)})})},a.setView=function(b,c){var d=b+"View",e=a.model_data.get(c);a.resetView(),e&&BrainBrowser.utils.isFunction(h[d])&&h[d](e),a.updated=!0},a.separateHalves=function(b,c){b=b||1,c=c||{},a.model_data.get(c.model_name).split&&(a.model.children[0].position.x-=b,a.model.children[1].position.x+=b),a.updated=!0}};