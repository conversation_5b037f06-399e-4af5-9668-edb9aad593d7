const CryptoJS = require('crypto-js');
const fs = require('fs');
const version = '1.0.1.0.1';
const products = [
  {
    name: '210',
    version: version,
    power: 50,
    hasTherapy: true,
    appImage: 'nav-appimage',
    extList: ['.ng'],
  },
  {
    name: '210 Pro',
    version: version,
    power: 70,
    hasTherapy: true,
    appImage: 'nav-appimage',
    extList: ['.ng'],
  },
  {
    name: '260',
    version: version,
    power: 50,
    hasTherapy: true,
    appImage: 'nav-appimage',
    extList: ['.ng'],
  },
  {
    name: '260 Pro',
    version: version,
    power: 70,
    hasTherapy: true,
    appImage: 'nav-appimage',
    extList: ['.ng'],
  },
  {
    name: '280',
    version: version,
    power: 75,
    hasTherapy: true,
    appImage: 'nav-appimage',
    extList: ['.ng'],
  },
];

products.forEach(item => {
  const ciphertext = CryptoJS.AES.encrypt(JSON.stringify(item), 'neuralgalaxy_product').toString();
  fs.writeFileSync(`./product/product_${item.name.replace(' ', '')}`, ciphertext);
});
