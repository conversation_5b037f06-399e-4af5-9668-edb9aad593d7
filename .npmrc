#二方包地址
@ngiq:registry=https://nexus.ngdevops.cn/repository/ngiq-npm

#三方包主要镜像地址
registry=https://nexus.ngdevops.cn/repository/npm-all

#特定三方包镜像属性地址
chromedriver_cdnurl=https://cdn.npmmirror.com/binaries/chromedriver
keytar_binary_host=https://npmmirror.com/mirrors/keytar
puppeteer_download_host=https://cdn.npmmirror.com/binaries/chrome-for-testing
sass_binary_site=https://cdn.npmmirror.com/binaries/node-sass
robotjs-binary-host=https://cdn.npmmirror.com/binaries/robotj
sharp-binary-host=https://cdn.npmmirror.com/binaries/sharp
sharp-libvips-binary-host=https://cdn.npmmirror.com/binaries/sharp-libvips

#npm的缓存目录
cache=.cache/npm
npmRebuild=false



