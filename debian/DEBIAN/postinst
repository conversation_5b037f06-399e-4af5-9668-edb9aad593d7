#!/bin/sh
USER_NAME=nguser
if ! id -u ${USER_NAME} >/dev/null 2>&1; then
    echo "user ${USER_NAME} not exist"
    exit 1
fi
if [ -f "/home/<USER>/Desktop/neuralgalaxy.desktop" ];then
    chmod +x /home/<USER>/Desktop/neuralgalaxy.desktop
    gio set /home/<USER>/Desktop/neuralgalaxy.desktop "metadata::trusted" true
    chown ${USER_NAME}:${USER_NAME} /home/<USER>/Desktop/neuralgalaxy.desktop

fi
if [ -f "/home/<USER>/.config/autostart/neuralgalaxy.desktop" ];then
    chmod +x /home/<USER>/.config/autostart/neuralgalaxy.desktop
    chown -R ${USER_NAME}:${USER_NAME} /home/<USER>/.config/autostart
fi
if [ -d "/home/<USER>/.config/%REPO_NAME%" ];then
    chown -R ${USER_NAME}:${USER_NAME} /home/<USER>/.config/%REPO_NAME%
fi

if [ -d /opt/%REPO_NAME% ];then
    chown -R ${USER_NAME}:${USER_NAME} /opt/%REPO_NAME%
fi

if [ -f /opt/%REPO_NAME%/app.AppImage ];then
    chmod +x /opt/%REPO_NAME%/app.AppImage
    # /opt/%REPO_NAME%/app.AppImage --no-sandbox
fi
