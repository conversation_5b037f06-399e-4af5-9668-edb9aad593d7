import { defineConfig, splitVendorChunkPlugin } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';
import Inspect from 'vite-plugin-inspect';
import svgr from 'vite-plugin-svgr';

export default defineConfig({
  resolve: {
    // 别名配置
    alias: {
      '@/renderer': path.join(__dirname, '../src/renderer/'),
      '@/common': path.join(__dirname, '../src/common/'),
    },
    // alias: [{ find: "@/renderer", replacement: "/src/renderer/" }],
  },
  css: {
    modules: {
      generateScopedName: '[folder]__[local]___[hash:base64:5]',
    },
  },
  worker: {
    format: 'es',
    plugins: [],
  },
  optimizeDeps: {
    // 为一个字符串数组
    entries: ['./index.html'],
    include: [
      // 按需加载的依赖都可以声明到这个数组里
    ],
    esbuildOptions: {
      plugins: [
        // 加入 Esbuild 插件
      ],
    },
  },
  plugins: [
    Inspect({
      build: true,
      outputDir: '.vite-inspect',
    }),
    svgr(),
    react(),
    splitVendorChunkPlugin(),
  ],
  base: './',
  build: {
    // 默认:
    // 如果静态资源体积 >= 4KB，则提取成单独的文件
    // 如果静态资源体积 < 4KB，则作为 base64 格式的字符串内联
    // 这里设置为 8 KB
    // assetsInlineLimit: 8 * 1024,
    // minify: false,
    target: 'modules',
    sourcemap: true,
    chunkSizeWarningLimit: 5120,
  },
  server: {
    port: 4001,
  },
});
