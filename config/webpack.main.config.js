const webpack = require('webpack');
// const { merge } = require('webpack-merge');
const webpackMerge = require('webpack-merge');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const paths = require('./paths');
const baseConfig = require('./webpack.base.config');
const getClientEnvironment = require('./env');
const publicUrl = '';
const env = getClientEnvironment(publicUrl);

module.exports = webpackMerge.merge(baseConfig, {
  target: 'electron-main',
  entry: {
    preload: './src/common/ipc/preload.ts',
    main: './src/main/main.ts',
  },
  module: {
    rules: [
      {
        test: /\.(ts|tsx)$/,
        exclude: /node_modules/,
        use: [
          {
            loader: 'esbuild-loader',
          }
        ],
      },
      {
        test: /\.node$/,
        loader: "native-ext-loader",
      }
    ]
  },
  plugins: [
    new webpack.DefinePlugin(env.envParames),
    new CopyWebpackPlugin({
      patterns: [
        {
          from: `${paths.appSrc}/main/i18n/locales/en/translation.json`,
          to: `${paths.appDist}/locales/en`,
        },
        {
          from: `${paths.appSrc}/main/i18n/locales/zh/translation.json`,
          to: `${paths.appDist}/locales/zh`,
        }
      ]
    }),
  ]
});
