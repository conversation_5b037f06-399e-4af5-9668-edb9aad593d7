'use strict';
const paths = require('./paths');
// eslint-disable-next-line @typescript-eslint/no-var-requires
const path = require('path');
const fs = require("fs");

const setNgiqAlias = (aliasName,repo) => {
  if(!repo || !aliasName) return {};
  const ngPath = path.join(process.cwd(), 'src', repo);
  try {
    const isDirectory = fs.statSync(ngPath).isDirectory();
    if (!isDirectory) return {};
    return { [aliasName]: ngPath };
  } catch (err) {
    return {};
  }
};
module.exports = {
  mode: 'development',
  output: {
    path: path.resolve(__dirname, '../dist'),
    filename: '[name].js'
  },
  node: {
    __dirname: false,
    __filename: false
  },
  resolve: {
    extensions: ['.js', '.ts', '.tsx', '.json'],
    alias:{
      '@': paths.appSrc,
      ...setNgiqAlias('@ngiq/brainbrowser','@ngiq/brainbrowser'),
    },
  },
  devtool: 'source-map',
  plugins: [
  ]
};
