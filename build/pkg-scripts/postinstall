#!/bin/sh

echo "unInstall............................."
cd ~/Library/Application\ Support

APPPATH="ngiq-pro-desktop"
if [[ -d $APPPATH ]]; then
  sudo rm -r $APPPATH
fi

echo "install............................."

mkdir -p ~/Library/Application\ Support/ngiq-pro-desktop/ng_preferences

catalogue=$1
echo 1 ${catalogue%/*} >> /dev/null

if [[ -n $catalogue ]] ; then
  echo 2 >> /dev/null
  cd ${catalogue%/*}
  echo 3 >> /dev/null
  if [[ ! -f "./product.json" ]]; then
    echo "no existing the product.json" >> /dev/null
  fi
  cat ./product.json >> /dev/null
  echo 4 >> /dev/null
  product=$(sudo cat ./product.json | awk -v FS=':' '{print $2}' | sed s/}//g | sed 's/ //g')

  echo 5 >> /dev/null
  if [[ "$product" = "1" ]]; then
      echo 6 >> /dev/null
      sudo cp -R product.json  ~/Library/Application\ Support/ngiq-pro-desktop/ng_preferences/
      ngbox="./ngbox.json"
      if [[ -f "$ngbox" ]]; then
          echo 7 >> /dev/null
          sudo cp -R ngbox.json  ~/Library/Application\ Support/ngiq-pro-desktop/ng_preferences/
          cd /Applications
          sudo mv Neural\ Galaxy.app* BrainSector\ Analyzer.app
          echo "Analyzer"
      else
          echo 8 >> /dev/null
          cd /Applications
          sudo mv Neural\ Galaxy.app* BrainSector\ Cloud.app
          echo "Cloud"
      fi
  else
      echo 9 >> /dev/null
      sudo cp -R product.json  ~/Library/Application\ Support/ngiq-pro-desktop/ng_preferences/
      cd /Applications
      sudo mv Neural\ Galaxy.app* PreSurge.app
  fi
fi

sudo chown -R $USER:staff ~/Library/Application\ Support/ngiq-pro-desktop
