!macro customInit
  ; Workaround for installer handing when the app directory is removed manually
  ${ifNot} ${FileExists} "$INSTDIR"
    DeleteRegKey HKCU "Software\Microsoft\Windows\CurrentVersion\Uninstall\${UNINSTALL_APP_KEY}"
    DeleteRegKey HKCU "Software\Microsoft\Windows\CurrentVersion\Uninstall\{${UNINSTALL_APP_KEY}}"
  ${EndIf}
!macroend

!macro customInstall
  IfFileExists $EXEDIR\product.json  file_found
  file_found:
    ; Try to copy ngbox.json to install directory
    CopyFiles "$EXEDIR\product.json" "$APPDATA\ngiq-pro-desktop\ng_preferences\product.json"
    FileOpen $4 "$EXEDIR\product.json" r
    FileRead $4 $1
    StrCpy $0 $1  1 -3
    ${If} $0 == 1
      IfFileExists $EXEDIR\ngbox.json  box  not_box
      box:
        CopyFiles "$EXEDIR\ngbox.json" "$APPDATA\ngiq-pro-desktop\ng_preferences\ngbox.json"
        CreateShortCut "$DESKTOP\BrainSector Analyzer.lnk" "$INSTDIR\Neural Galaxy.exe"
        goto end_of_test ;<== important for not continuing on the else branch
      not_box:
        CreateShortCut "$DESKTOP\BrainSector Cloud.lnk" "$INSTDIR\Neural Galaxy.exe"
        end_of_test:

      Delete "$DESKTOP\Neural Galaxy.lnk"
    ${EndIf}
    ${If} $0 == 2
        CopyFiles "$EXEDIR\ngbox.json" "$APPDATA\ngiq-pro-desktop\ng_preferences\ngbox.json"
        CreateShortCut "$DESKTOP\PreSurge.lnk" "$INSTDIR\Neural Galaxy.exe"
        Delete "$DESKTOP\Neural Galaxy.lnk"
    ${EndIf}
!macroend

