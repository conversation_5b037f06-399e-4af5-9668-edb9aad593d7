{"name": "ngiq-pro-desktop", "version": "1.5.4", "loginVersion": "1.0", "private": true, "sideEffects": false, "description": "Neural Galaxy Control Station", "main": "./dist/main.js", "scripts": {"test-ui": "playwright test", "vite-start-renderer-dev": "export ELECTRON_DISABLE_SECURITY_WARNINGS=true && NODE_ENV=development vite --mode=dev --c  config/vite.config.ts", "vite-start": "export ELECTRON_DISABLE_SECURITY_WARNINGS=true && NODE_ENV=development vite --mode=dev --c  config/vite.config.prod.ts", "build-main": "cross-env NODE_ENV=production webpack --config config/webpack.main.prod.config.js", "build-main-nav": "cross-env NODE_ENV=production --NAV=true webpack --config config/webpack.main.prod.config.js", "build-renderer": "cross-env NODE_OPTIONS=--max_old_space_size=6192 NODE_ENV=production webpack --config config/webpack.renderer.prod.config.js", "build-renderer-vite": "NODE_OPTIONS=--max_old_space_size=6192 NODE_ENV=production vite --mode=prod --c config/vite.config.ts build", "build-renderer-vite-nav": "NODE_OPTIONS=--max_old_space_size=6192 NAV=true NODE_ENV=production vite --mode=prod --c config/vite.config.ts build", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "npm run build-renderer-vite && npm run build-main", "build-nav": "npm run build-renderer-vite-nav && npm run build-main-nav", "local-build": "npm i @ngiq/pnt-component && npm install && npm run build-main && npm run build-renderer", "prelight-build": "<PERSON><PERSON><PERSON> dist", "light-build": "npm install && npm run build-main && npm run build-renderer", "start-renderer-dev": "cross-env NODE_OPTIONS=--max_old_space_size=4840 NODE_ENV=development webpack serve --config config/webpack.renderer.dev.config.js", "prestart-main-dev": "npm run prebuild", "start-main-dev": "cross-env NODE_ENV=development --NAV=true webpack --config config/webpack.main.config.js && electron --no-sandbox --inspect=5859 ./dist/main.js", "start-dev": "cross-env NODE_ENV=development START_HOT=1 npm run start-renderer-dev", "prestart-dev": "npm run build", "prestart": "npm run light-build", "start": "electron .", "lint": "eslint -c .eslintrc.prod.json -f scripts/eslint-formatter.js --ext .ts,.tsx,.js src --format json --output-file reports/eslint-result.json", "lint-check": "eslint -c .eslintrc.prod.json -f scripts/eslint-formatter.js --ext .ts,.tsx,.js src", "lint-fix": "eslint -c .eslintrc.prod.json -f scripts/eslint-formatter.js --ext .ts,.tsx,.js src --fix", "check-ts": "tsc -p tsconfig.json --noEmit", "test": "TZ=America/New_York NODE_ENV=test jest --env=jsdom --config ./jest.config.json --watch", "coverage": "TZ=America/New_York NODE_ENV=test jest --env=jsdom --config ./jest.config.json --maxWorkers=1 --coverage --silent", "check": "npm run check-ts && npm run lint && npm run coverage && npm audit", "check-t": "npm run check-ts && npm run lint", "test:ui": "npm run cache-backup && NODE_ENV=test jest '(\\/test\\/autoTest_UI\\/testcases/).*' --detectOpenHandles --reporters='jest-html-reporter'&& npm run cache-restore", "pack": "npm run build && electron-builder --dir", "postinstall": "electron-builder install-app-deps", "test:report": "jest --reporters='jest-html-reporter'", "ngy-link-brainbrowser": "ngy link @ngiq/brainbrowser", "i18n-scan": "i18n scan", "i18n-cover": "i18n cover"}, "build": {"appId": "com.neuralgalaxy.m200.app.Desktop", "productName": "m200", "copyright": "Copyright © 2021 Neural Galaxy", "asarUnpack": ["**/node_modules/**/*"], "directories": {"output": "release/${os}"}, "files": ["dist/", "node_modules/", "package.json"], "linux": {"icon": "config/app-logo.png", "target": "AppImage", "category": "Application", "artifactName": "${name}-${buildNumber}.${ext}"}}, "repository": {"type": "git", "url": "git+ssh://***********************:device/m200/ngiq-pro-desktop.git"}, "author": {"name": "Neural Galaxy", "email": "<EMAIL>"}, "license": "SEE LICENSE IN LICENSE.TXT", "bugs": {"url": "https://git.neuralgalaxy.cn/device/m200/ngiq-pro-desktop/-/issues"}, "homepage": "https://www.neuralgalaxy.com", "devDependencies": {"@antv/data-set": "^0.11.8", "@playwright/test": "^1.40.0", "@svgr/webpack": "^6.5.1", "@testing-library/react": "^13.4.0", "@types/ali-oss": "^6.16.7", "@types/archiver": "^5.3.1", "@types/async": "^3.2.16", "@types/bluebird": "^3.5.38", "@types/bluebird-retry": "^0.11.5", "@types/classnames": "^2.3.0", "@types/d3-geo": "^3.0.3", "@types/d3-sankey": "^0.12.0", "@types/electron-devtools-installer": "^2.2.2", "@types/enzyme": "^3.10.12", "@types/eventsource": "^1.1.11", "@types/fs-extra": "^8.1.0", "@types/i18next": "^13.0.0", "@types/i18next-node-fs-backend": "^2.1.1", "@types/jest": "^29.4.0", "@types/jsonwebtoken": "^9.0.1", "@types/lodash": "^4.14.191", "@types/mathjs": "^9.4.2", "@types/md5": "^2.3.2", "@types/mock-fs": "^4.13.1", "@types/mockdate": "^2.0.0", "@types/nock": "^11.1.0", "@types/node": "^18.11.18", "@types/papaparse": "5.3.7", "@types/randomstring": "^1.1.8", "@types/react": "18.0.27", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-copy-to-clipboard": "^5.0.4", "@types/react-custom-scrollbars": "^4.0.10", "@types/react-dom": "18.0.10", "@types/react-intl": "3.0.0", "@types/react-loadable": "^5.5.6", "@types/react-medium-image-zoom": "^3.0.1", "@types/react-resizable": "^3.0.3", "@types/react-router": "5.1.20", "@types/react-router-dom": "5.3.3", "@types/react-syntax-highlighter": "^15.5.6", "@types/react-test-renderer": "^18.0.0", "@types/request": "^2.48.8", "@types/three": "0.125.0", "@types/uuid": "^9.0.0", "@types/webdriverio": "^4.8.7", "@types/webpack": "^5.28.0", "@types/webpack-env": "^1.18.0", "@types/websocket": "^1.0.5", "@types/ws": "^8.5.10", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-react": "^3.1.0", "adm-zip": "^0.5.10", "analysis-i18n": "^1.1.1", "antd": "^5.2.0", "archiver": "^5.3.1", "bizcharts": "^4.1.22", "chalk": "^4.1.2", "commander": "^10.0.0", "copy-webpack-plugin": "^11.0.0", "cornerstone-core": "^2.6.1", "cross-env": "^7.0.3", "download": "^8.0.0", "electron": "22.2.0", "electron-builder": "24.9.1", "electron-devtools-installer": "^3.2.0", "enzyme": "^3.11.0", "enzyme-to-json": "^3.6.2", "esbuild-loader": "^3.0.1", "eslint": "^7.32.0", "eslint-config-prettier": "^8.2.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jest": "^24.3.5", "eslint-plugin-jsdoc": "^32.3.0", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-no-null": "^1.0.2", "eslint-plugin-prefer-arrow": "^1.2.3", "eslint-plugin-react": "^7.23.2", "eslint-plugin-security": "^1.4.0", "eslint-plugin-unicorn": "^29.0.0", "export-from-json": "^1.7.0", "fork-ts-checker-webpack-plugin": "^7.3.0", "fs-extra": "^8.1.0", "idb-kv-store": "^4.5.0", "jest": "^29.4.1", "jest-css-modules": "^2.1.0", "jest-html-reporter": "^3.7.0", "less": "^4.2.0", "lib-r-math.js": "^1.0.94", "lint-staged": "^13.1.0", "load-json-file": "^6.2.0", "memfs": "^3.4.13", "mockdate": "^3.0.5", "native-ext-loader": "^2.3.0", "nock": "^13.3.0", "playwright": "^1.40.0", "prettier": "^2.8.3", "randomstring": "^1.2.3", "react-copy-to-clipboard": "^5.1.0", "react-draggable": "^4.4.5", "react-loadable": "^5.5.0", "react-resizable": "^3.0.5", "react-rnd": "^10.4.1", "react-syntax-highlighter": "^15.5.0", "react-test-renderer": "^18.2.0", "rimraf": "^4.1.2", "speed-measure-webpack-plugin": "^1.5.0", "tar": "^6.1.13", "ts-jest": "^29.0.5", "typescript": "^5.3.3", "vite": "^4.5.1", "vite-plugin-inspect": "^0.8.3", "vite-plugin-optimizer": "^1.4.3", "vite-plugin-svg-icons": "^2.0.1", "vite-svg-loader": "^5.1.0", "webpack": "^5.75.0", "webpack-bundle-analyzer": "^4.8.0", "webpack-cli": "^5.0.1", "webpack-dev-server": "^4.11.1", "webpack-merge": "^5.8.0"}, "dependencies": {"@ant-design/icons": "^5.3.0", "@electron/remote": "^2.0.9", "@ngiq/brainbrowser": "^3.0.1", "@sentry/react": "^7.66.0", "ahooks": "^3.7.4", "axios": "^1.3.1", "blob-util": "^2.0.2", "check-disk-space": "3.3.1", "classnames": "2.3.2", "crypto-js": "^4.1.1", "dayjs": "^1.11.7", "dexie": "^3.2.3", "dexie-react-hooks": "^1.1.1", "docxtemplater": "^3.22.5", "dotenv": "^16.0.3", "dotenv-expand": "^10.0.0", "electron-log": "4.4.8", "electron-store": "^8.1.0", "eventsource": "^2.0.2", "history": "^4.7.2", "i18next": "^22.4.9", "i18next-fs-backend": "^2.3.1", "jsonwebtoken": "^9.0.0", "jszip": "^3.10.1", "jszip-utils": "^0.1.0", "lodash": "^4.17.21", "mathjs": "^11.5.1", "mathlab": "0.0.14", "md5": "^2.3.0", "moment": "^2.29.4", "ngy": "0.0.6", "node-machine-id": "^1.1.12", "papaparse": "5.3.2", "pinyin-pro": "^3.18.2", "pizzip": "^3.1.4", "python-shell": "^3.0.1", "query-string": "^8.1.0", "raf": "^3.4.1", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.2.0", "react-intl": "^6.2.7", "react-router": "^6.8.0", "react-router-dom": "^6.8.0", "react-timing-hooks": "^4.0.2", "recoil": "^0.7.7", "request": "^2.88.2", "retry-axios": "^3.0.0", "spawn-sync": "^2.0.0", "three": "0.125.0", "type-fest": "^3.6.1", "uuid": "^9.0.0", "vite-plugin-electron": "^0.15.6", "vite-plugin-electron-renderer": "^0.14.5", "vite-plugin-svgr": "^2.4.0", "websocket": "^1.0.34", "whatwg-fetch": "^3.6.2", "wifi-name": "^3.1.1", "ws": "^8.12.0"}, "overrides": {"bin-wrapper": "npm:bin-wrapper-china"}, "jest-html-reporter": {"outputPath": "./test/autoTest_UI/testreport/index.html", "includeFailureMsg": true, "includeConsoleLog": true}}