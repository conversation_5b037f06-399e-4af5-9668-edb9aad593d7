@import '@/renderer/static/style/baseColor.module.less';
.chart_container {
  display: flex;
  width: 100%;
  flex-direction: column;
  color: @colorC2;
  font-size: 14px;
  margin-bottom: 20px;
  height: 150px;

  .empty_content {
    position: relative;
    width: 100%;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    padding-top: 21px;

    .empty_ng_content {
      font-size: 14px;
      color: @colorC2 !important;
      // margin-top: 0px !important;
      margin: 12px 0 !important;
      line-height: 17px;
    }
  }
  .first_row {
    display: flex;
    width: 132px;
    height: 25px;
    justify-content: space-between;
    .left_hz {
      width: 212px;
      height: 26px;
      text-align: center;

      .top_line {
        width: 131px;
        height: 6px;
        background: bottom;
        background: url('../../../renderer/static/images/topLeftLine.png') no-repeat;
        background-size: contain;
      }

      .text {
        width: auto;
        padding-left: 6px;
        padding-right: 6px;
      }
    }
    .right_mt {
      width: 100px;
      text-align: right;
      font-size: 18px;
    }
  }

  .first_row_one_child {
    width: 100%;
    display: flex;
    flex-direction: row-reverse;

    .right_mt {
      width: 100px;
      text-align: right;
      font-size: 18px;
    }
  }

  .second_row {
    margin-top: 2px;
    margin-bottom: 2px;
    .left_hz {
      width: 248px;
      text-align: left;
      padding-left: 6px;
    }
  }

  .three_row {
    display: flex;
    flex-direction: row;

    .left_chart {
      width: 212px;
      height: 48px;
      flex-shrink: 0;
    }

    .normal_chart {
      background: url('../../../renderer/static/images/rtmschart.png') no-repeat;
      background-size: contain;
    }
    .normal_chart_e1 {
      background: url('../../../renderer/static/images/repeatChart.png') no-repeat;
      background-size: contain;
    }
    .tbs_chart {
      background: url('../../../renderer/static/images/tbsChart.png') no-repeat;
      background-size: contain;
    }
    .no_chart {
      // background: url('../../../../renderer/static/images/no_chart.png') no-repeat ;
      background-size: contain;
    }
    .right_num {
      flex: 1 1;
      min-width: 60px;
      align-items: center;
      display: flex;
      padding-left: 6px;
      white-space: nowrap;
    }
  }

  .four_row {
    display: flex;
    height: 25px;
    align-items: flex-start;
    margin-bottom: 6px;
    .left_second {
      width: 130.5px;
      height: 26px;
      text-align: center;
      margin-right: 2px;

      .back_line {
        width: 130px;
        height: 6px;
        background: #bfbfbf;
        background: url('../../../renderer/static/images/bottomLeftLine.png') no-repeat;
        background-size: contain;
      }

      .text {
        width: auto;
        padding-left: 6px;
        padding-right: 6px;
      }
    }

    .left_second_long {
      width: 211.5px;
      height: 26px;
      text-align: center;

      .back_line {
        width: 212px;
        height: 6px;
        background: #bfbfbf;
        background: url('../../../renderer/static/images/bottomLine.png') no-repeat;
        background-size: contain;
      }
      .text {
        width: auto;
        padding-left: 6px;
        padding-right: 6px;
      }
    }
    .right_second {
      width: 82px;
      height: 26px;
      text-align: center;

      .back_line {
        width: 100%;
        height: 6px;
        background: #bfbfbf;
        background: url('../../../renderer/static/images/bottomRightLine.png') no-repeat;
        background-size: contain;
      }

      .text {
        width: auto;
        padding-left: 6px;
        padding-right: 6px;
      }
    }
  }

  .five_row {
    display: flex;
    justify-content: space-between;
    height: 25px;
    align-items: center;
    .left_num {
      flex: 1;

      .overValue {
        color: @colorB3;
      }
    }
    .right_num {
      text-align: right;
      .overValue {
        color: @colorB3;
      }
    }
  }
}
.fontSize14 {
  font-size: 14px;
}
