import React from 'react';
import classnames from 'classnames';
import { injectIntl } from 'react-intl';
import styles from './index.module.less';
import { PlanStimulusModel, EnumPlanStimulusType } from '@/common/types';
import { IntlPropType } from '@/common/types/propTypes';
import { calTbsChartData, getTmsTotalTime } from '@/renderer/component/template/calTemplate';
import NgEmpty from '@/renderer/uiComponent/NgEmpty';
import { EmptyTbsChart } from '@/renderer/uiComponent/SvgGather';
import { TbsFieldType } from '@/renderer/component/template';
import { getRenderFields } from '@/renderer/container/stimulateTemplate/component/editTemplateCard';
// import { calRules } from '@/renderer/component/template/calRules';

type TBSChartPropType = IntlPropType & {
  template: PlanStimulusModel;
  isActive?: boolean;
  isNotLastRow?: boolean;
  className?: CSSModuleClasses[string];
  isPreviewTreat?: boolean;
  motionThreshold?: number;
};
type TBSChartStateType = {
  isNotComplete?: boolean;
  isError?: boolean;
};

export const checkISComplete = (values: any, type: EnumPlanStimulusType) => {
  let renderFields: TbsFieldType[] = getRenderFields(type);
  const fieldObj = renderFields
    .map(v => v.key)
    .filter((v: any) => {
      // if(v === 'relative_strength') return false;
      if (values.strand_pulse_count === 1) {
        return v !== 'intermission_time';
      } else {
        return true;
      }
    });

  return fieldObj.every(v => {
    return values[v] && values[v] > 0;
  });
};

export class InnerTBSChart extends React.Component<TBSChartPropType, TBSChartStateType> {
  constructor(props: TBSChartPropType) {
    super(props);
    this.state = {
      isNotComplete: !checkISComplete(props.template, props?.template?.type),
      isError: false,
    };
  }
  public componentDidUpdate(prevProps: Readonly<TBSChartPropType>, prevState: Readonly<TBSChartStateType>, snapshot?: any) {
    if (prevProps.template !== this.props.template) {
      const isComplete = checkISComplete(this.props.template, this.props.template.type);
      // const tempFields = calRules(this.props.template, this.props.motionThreshold ? this.props.motionThreshold : 0);
      // const hasError = tempFields.some(item => item.status === 'error');
      this.setState({ isNotComplete: !isComplete });
    }
  }

  private getStyleContainer = (type: EnumPlanStimulusType, count: number): string => {
    if (this.state.isNotComplete) return styles.no_chart;
    switch (type) {
      case EnumPlanStimulusType.RTMS: {
        if (count === 1) {
          return styles.normal_chart_e1;
        } else {
          return styles.normal_chart;
        }
      }
      case EnumPlanStimulusType.TBS:
        return styles.tbs_chart;
      default:
        return styles.no_chart; // undefined
    }
  };

  private dealUndefine: (obj: { [name: string]: any }) => { [name: string]: number } = obj => {
    // eslint-disable-next-line radix
    return Object.keys(obj).reduce((pre, cur) => ({ ...pre, [cur]: isNaN(parseInt(obj[cur])) || obj[cur] === null ? '--' : obj[cur] }), {});
  };
  private renderPulseTotal = (pulse_total: number) => {
    if (pulse_total === 0) return '--';
    if (pulse_total > 65535) return '超限';

    return pulse_total;
  };
  private getLeftSecondClass = (e: number) => {
    return e !== 1 ? styles.left_second : styles.left_second_long;
  };
  private getFirstRowClass = (isNormal: boolean) => {
    return isNormal ? styles.first_row_one_child : styles.first_row;
  };

  private formatBValue = (b: string | number) => {
    return typeof b === 'number' ? b.toFixed(1) : 1;
  };

  private checkIsOverTreatmentTime = (treatmentTime: number) => {
    return treatmentTime > 65535 || (treatmentTime <= 3 && treatmentTime >= 0);
  };

  public render() {
    const { template, isActive, intl, className, isNotLastRow, isPreviewTreat } = this.props;
    const { isError, isNotComplete } = this.state;
    if (isNotComplete || isError) {
      return (
        <>
          <div className={classnames(styles.chart_container, className)}>
            <div className={styles.empty_content}>
              <NgEmpty
                key={'custom'}
                emptyType={'custom'}
                customDesc={isNotComplete ? intl.formatMessage({ id: '填写参数后展示' }) : intl.formatMessage({ id: '修正参数后展示' })}
                customSvg={<EmptyTbsChart />}
                contentClasName={styles.empty_ng_content}
              />
            </div>
            <div className={styles.five_row}>
              <div className={styles.left_num}>{intl.formatMessage({ id: 'tbs_pulse_total' })}：--</div>
              <div className={styles.right_num}>{intl.formatMessage({ id: 'tbs_treatment_time' })}：--:--:--</div>
            </div>
          </div>
        </>
      );
    }
    const isNormal = template?.type === EnumPlanStimulusType.RTMS;
    // @ts-ignore
    const { a, b, c, d, e, f, T1, T2, T4, pulse_total, treatment_time } = this.dealUndefine(calTbsChartData(template));
    const isOverLimit = pulse_total > 65535;
    const isOverTreatmentTime = this.checkIsOverTreatmentTime(treatment_time);
    let chartImgClass = this.getStyleContainer(template?.type, e);
    let leftSecondClass = this.getLeftSecondClass(e);
    let secondLabel = isNormal ? `${c} x ${a}Hz` : `${c}(${T1}s) x ${a}Hz`;
    let fistRowClass = this.getFirstRowClass(isNormal);
    let newB = this.formatBValue(b);

    return (
      <>
        <div className={classnames(styles.chart_container, className)}>
          <div className={classnames(fistRowClass)}>
            {!isNormal && !isPreviewTreat && (
              <div className={styles.left_hz}>
                <div className={classnames(styles.text, isActive ? styles.active_text : '')}>{`${d} x ${newB}Hz`}</div>
                <div className={styles.top_line} />
              </div>
            )}
            <div className={styles.right_mt} />
          </div>
          <div className={styles.second_row}>
            <div className={styles.left_hz}>{secondLabel}</div>
          </div>
          <div className={styles.three_row}>
            <div className={classnames(styles.left_chart, chartImgClass)} />
            <div className={styles.right_num}>{`x ${e}`}</div>
          </div>
          <div className={styles.four_row}>
            <div className={classnames(leftSecondClass)}>
              <div className={styles.back_line} />
              <div className={classnames(styles.text, isActive ? styles.active_text : '')}>{`${isNormal ? T4 : T2}s`}</div>
            </div>
            {e !== 1 && (
              <div className={styles.right_second}>
                <div className={styles.back_line} />
                <div className={classnames(styles.text, isActive ? styles.active_text : '')}>
                  {`${isNaN(template.intermission_time as any) ? '--' : f}s`}
                </div>
              </div>
            )}
          </div>
          {!isNotLastRow && (
            <div className={styles.five_row}>
              <div className={styles.left_num}>
                {intl.formatMessage({ id: 'tbs_pulse_total' })}：
                <span className={classnames(isOverLimit ? styles.overValue : '')}>{`${this.renderPulseTotal(pulse_total)}`}</span>
              </div>
              <div className={styles.right_num}>
                {intl.formatMessage({ id: 'tbs_treatment_time' })}：
                <span className={classnames(isOverTreatmentTime ? styles.overValue : '')}>{`${getTmsTotalTime(treatment_time)}`}</span>
              </div>
            </div>
          )}
        </div>
      </>
    );
  }
}

export const TBSChart = injectIntl(InnerTBSChart);
