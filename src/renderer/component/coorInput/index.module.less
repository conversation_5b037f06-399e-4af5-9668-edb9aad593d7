@import '../../static/style/baseColor.module.less';

.input_container {
  display: flex;

  :global {
    .ant-input-status-error {
      border-color: #ba6058 !important;
    }
  }

  &:nth-child(n):not(&:last-child) {
    width: 33.3%;
  }

  .input_x {
    border-radius: 6px 0 0 6px;
  }

  .input_y {
    border-radius: 0;
  }
  .input_z {
    border-radius: 0 6px 6px 0;
  }

  .input_x,
  .input_y {
    :global {
      .ant-input-status-error {
        border-right-color: transparent !important;
      }
    }
  }
}
