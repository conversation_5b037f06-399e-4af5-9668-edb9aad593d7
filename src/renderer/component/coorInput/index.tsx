import React from 'react';
import styles from './index.module.less';
import { NgInput } from '../../uiComponent/NgInput';
import { InputProps } from 'antd';

type Props = InputProps & {
  value?: { x: string; y: string; z: string };
  onChange?(val: any): void;
};

export const CoorInput = (props: Props) => {
  const { value, onChange } = props;

  const handleChange = (val: string, type: string) => {
    const pre_val = value ? value[type] : '';
    const curValue = value || {};
    onChange!({
      ...curValue,
      [type]: !val || /^-?(\d{1,3}(\.\d{0,2})?)?$/.test(val) ? val : pre_val,
    });
  };

  return (
    <div className={styles.input_container}>
      <NgInput disabled={props.disabled} onChange={e => handleChange(e.target.value, 'x')} value={value?.x} className={styles.input_x} />
      <NgInput disabled={props.disabled} onChange={e => handleChange(e.target.value, 'y')} value={value?.y} className={styles.input_y} />
      <NgInput disabled={props.disabled} onChange={e => handleChange(e.target.value, 'z')} value={value?.z} className={styles.input_z} />
    </div>
  );
};
