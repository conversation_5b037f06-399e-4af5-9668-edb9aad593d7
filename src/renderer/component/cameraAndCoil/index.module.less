@import '@/renderer/static/style/baseColor.module.less';

.container {
  display: flex;
  align-items: center;
  font-family: normal-font, serif !important;
  position: absolute;
  left: 50%;
  justify-content: center;

  .error_info{
    position: relative;
    padding: 0 17px;
    height: 46px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 30px;
    background-color: @colorA5;
    margin-left: 20px;
    cursor: url('@/renderer/static/svg/cursor.cur'), pointer;
    .count{
      position: absolute;
      right: -8px;
      top: 0;
      width: 24px;
      height: 14px;
      border-radius: 10px;
      line-height: 13px;
      background: @colorB5;
      color: @colorA12;
      font-size: 10px;
      text-align: center;
    }
  }

  .licenseContainer {
    height: 46px;
    background: @colorA5;
    color: @colorA11;
    border-radius: 6px;
    display: flex;
    align-items: center;
    padding: 15px;
    box-sizing: border-box;
    white-space: nowrap;
    font-size: @font-size-base;
    margin-right: 24px;

    & svg {
      margin-right: 10px;
    }
  }

  .pstAndCoilStatus {
    height: 46px;
    border-radius: 90px;
    opacity: 1;
    background: @colorA5;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px !important;

    .photoContainer {
      display: flex;
      align-items: center;
      margin-left: 20px;
      margin-right: 20px;
      height: 46px;

      & p {
        font-family: normal-font, serif !important;
        white-space: nowrap;
        color: @colorA12;
      }
    }

    .coilContainer {
      display: flex;
      align-items: center;
      margin: 0 20px;
      height: 46px;

      & p {
        font-family: normal-font, serif !important;
        white-space: nowrap;
        color: @colorA12;
      }
    }

    .errorRound {
      border-radius: 50%;
      width: 14px;
      height: 14px;
      background: linear-gradient(180deg, @colorD3_end 3%, @colorD3_start 100%);
      animation: fadeInOut 1s infinite;
      -webkit-backface-visibility: hidden;
      -moz-backface-visibility: hidden;
      -ms-backface-visibility: hidden;
      backface-visibility: hidden;
    
      -webkit-perspective: 1000;
      -moz-perspective: 1000;
      -ms-perspective: 1000;
      perspective: 1000;
    }

    .successRound {
      border-radius: 50%;
      width: 14px;
      height: 14px;
      background: linear-gradient(2deg, #125c2f -23%, @colorD2_end 2%, #29f479 98%);
    }

    .split {
      width: 2px;
      height: 20px;
      background: @colorA8;
    }

    .photo {
      margin: 0 10px;

      & + p {
        margin: 0;
      }
    }

    .temperature {
      font-family: normal-font, serif !important;
      margin-left: 10px;
    }
    .warningTemperature {
      color: @colorB1 !important;
    }
    .errorTemperature {
      color: @colorB5 !important;
    }
  }
}
@keyframes fadeInOut {
  0% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
.title {
  color: @colorA12 !important;
}
.errorTips {
  :global {
    & .ant-popover-arrow::before {
      background-image: url('@/renderer/static/svg/tips_arrow.svg') !important;
      transform: rotate(0deg) !important;
      background-position: 2px 4px;
    }
    .ant-popover-inner {
      background-color: @colorA5 !important;
    }
    .ant-popover-inner-content {
      text-align: right;
      & div {
        line-height: 24px;
        font-family: Arial, Helvetica, sans-serif, Serif !important;
      }
    }
  }
}
