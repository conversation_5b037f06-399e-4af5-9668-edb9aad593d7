import React, { useEffect, useMemo, useRef, useState } from 'react';
import { ReactComponent as Photo } from '@/renderer/static/svg/photo.svg';
import { ReactComponent as Coil } from '@/renderer/static/svg/coil.svg';
import styles from './index.module.less';
import classnames from 'classnames';
import { ErrorMiddle, SuccessMiddle, WarnMiddle, YellowError } from '@/renderer/uiComponent/SvgGather';
import { connSocket, GetStatusCallbackParam } from '@/renderer/utils/imgSocket';
import { getM200ApiInstance } from '@/common/api/ngApiAgent';
import { useAsyncEffect, useMount } from 'ahooks';
import { useRecoilState } from 'recoil';
import { useLicenseAtom } from '@/renderer/recoil/license';
import { RoleEnumList, SimpleStatus } from '@/common/types';
import NgPopover from '@/renderer/uiComponent/NgPopover';
import { tmsCoilSelector } from '@/renderer/recoil/tmsError';
import { useIntl } from 'react-intl';
import { UserSessionProps, withUserSession } from '@/renderer/hocComponent/withUserSession';
import { product } from '../../constant/product';
import { FaultEnum, FaultKeyEnum, FaultLevelEnum, FaultMapItemType, FaultStatusEnum } from '../../../common/systemFault/type';
import { faultAtom, getFaultByType, getFaultByTypeList, getFaultWithoutType, getHasFaultByKeys } from '../../recoil/fault';

type Props = {
  container?: string;
  className?: CSSModuleClasses[string];
  notShowCameraModal?: boolean;
};

const getDaysDifference = (timestamp1: number, timestamp2: number) => {
  if (!timestamp1 || !timestamp2) return -1;

  let millisecondsPerDay = 24 * 60 * 60 * 1000; // 一天的毫秒数

  let date1 = new Date(timestamp1);
  let date2 = new Date(timestamp2);

  let timeDifference = Math.abs(date1.getTime() - date2.getTime());

  // 计算并返回天数差异
  return Math.floor(timeDifference / millisecondsPerDay);
};

const CameraAndCoil = (props: Props & UserSessionProps) => {
  const intl = useIntl();
  const [fault] = useRecoilState(faultAtom);
  const [systemError, setSystemList] = useState<FaultMapItemType[]>([]);
  const [cameraError, setCameraList] = useState<FaultMapItemType[]>([]);
  const [coilSelector] = useRecoilState(tmsCoilSelector);
  const m200Api = getM200ApiInstance();
  const [license, setLicense] = useRecoilState(useLicenseAtom);
  const containerRef = useRef<any>();
  const [tmsHasFault, setTmsHasFault] = useState<boolean>(false);
  const [temperatureHasFault, setTemperatureHasFault] = useState<boolean>(false);

  const licenseMsg = useMemo(() => {
    if (!Object.keys(license).length) return null;
    const offsetDay = getDaysDifference(license?.simple_end as number, new Date().getTime());
    //  是否是试用许可
    const isTrial = license.simple_status && +license.simple_status === 100;
    if (offsetDay === 0) {
      return (
        <>
          <WarnMiddle />
          {isTrial ? intl.formatMessage({ id: '试用许可认证信息明日失效' }) : intl.formatMessage({ id: '许可认证信息明日失效' })}
        </>
      );
    }
    if (offsetDay >= 0 && offsetDay <= 7) {
      return (
        <>
          <WarnMiddle />
          {isTrial
            ? intl.formatMessage({ id: '试用许可信息{ offsetDay }天后失效' }, { offsetDay })
            : intl.formatMessage({ id: '许可认证信息{ offsetDay }天后失效' }, { offsetDay })}
        </>
      );
    }
    if (offsetDay <= 0 || isNaN(offsetDay)) {
      return (
        <>
          <ErrorMiddle />
          {intl.formatMessage({ id: '许可认证信息已失效' })}
        </>
      );
    }

    return isTrial ? (
      <>
        <SuccessMiddle />
        {intl.formatMessage({ id: '试用许可认证信息生效中' })}
      </>
    ) : null;
  }, [license]);

  const generateCameraError = (status: GetStatusCallbackParam) => {
    if (!status.open) {
      window.systemAPI.pushSystemFault(
        { '0A080001': FaultStatusEnum.abnormal, '0A080002': FaultStatusEnum.abnormal },
        'cameraAndCoil component 错误, status.open 为false, 同时设置0A080002'
      );

      return;
    } else if (status.open) {
      window.systemAPI.pushSystemFault({ '0A080001': FaultStatusEnum.normal }, 'cameraAndCoil component 清除错误, status.open 为true');
    }
    if (!status.cameraStatus) {
      window.systemAPI.pushSystemFault({ '0A080002': FaultStatusEnum.abnormal }, 'cameraAndCoil component 错误, status.cameraStatus 为false');
    } else if (status.cameraStatus) {
      window.systemAPI.pushSystemFault({ '0A080002': FaultStatusEnum.normal }, 'cameraAndCoil component 清除错误, status.cameraStatus 为true');
    }
  };

  useMount(async () => {
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    // window.tmsAPI.clear_data_pool();
    connSocket.listenStatus('CameraAndCoil', generateCameraError);
  });

  useEffect(() => {
    setSystemList(getFaultWithoutType(FaultEnum.imageFault));
    setCameraList(getFaultByType(FaultEnum.imageFault));
    setTmsHasFault(getHasFaultByKeys([FaultKeyEnum.A030001, FaultKeyEnum.A040001]));
    setTemperatureHasFault(getHasFaultByKeys([FaultKeyEnum.A040006, FaultKeyEnum.A040007, FaultKeyEnum.A040011, FaultKeyEnum.A040012]));
  }, [fault]);

  useAsyncEffect(async () => {
    let res = await m200Api.getLicense();
    setLicense({
      ...res,
      hasLicenseError: isLicenseExpire(res.simple_status!),
    });
  }, []);

  // 证书是否过期或异常
  const isLicenseExpire = (status: SimpleStatus) => {
    const trialList = [SimpleStatus.The99];
    // 如果状态是99代表证书异常
    if (trialList.includes(status)) {
      return true;
    }

    return false;
  };

  useEffect(() => {
    // console.log(containerRef)
    if (containerRef) {
      const message: any = document.getElementById('camera-message') || {};
      const content: any = document.getElementById('camera-content') || {};
      let margin: string = '0px';
      try {
        margin = window.getComputedStyle(message)?.marginRight || '0px';
      } catch (error) {
        //
      }
      containerRef.current.style.transform = `translate(calc(-${message.offsetWidth || 0}px - ${margin} - ${(content.offsetWidth || 0) / 2}px))`;
    }
  }, [props.userSession?.role_id, licenseMsg]);

  const renderError = () => {
    const errors = [...systemError];
    if (product.isNav) {
      errors.push(...cameraError);
    }

    return (
      <>
        {errors.map(item => {
          return (
            <div key={item.key}>
              {item.content}：{item.key}
            </div>
          );
        })}
      </>
    );
  };

  const CameraContainer = () => (
    <div className={styles.photoContainer}>
      <div
        className={classnames({
          [styles.errorRound]: !!getFaultByType(FaultEnum.imageFault).length,
          [styles.successRound]: !getFaultByType(FaultEnum.imageFault).length,
        })}
      />
      <Photo className={styles.photo} />
      <p>{intl.formatMessage({ id: '相机' })}</p>
    </div>
  );
  const CoilContainer = () => (
    <div className={styles.coilContainer}>
      <div
        className={classnames({
          [styles.errorRound]: !!getFaultByTypeList([FaultEnum.tmsFault, FaultEnum.coilFault, FaultEnum.coolingFault], fault[FaultLevelEnum.error])
            .length,
          [styles.successRound]: !getFaultByTypeList([FaultEnum.tmsFault, FaultEnum.coilFault, FaultEnum.coolingFault], fault[FaultLevelEnum.error])
            .length,
        })}
      />
      <Coil className={styles.photo} />
      <p>{intl.formatMessage({ id: '线圈' })}</p>
      <p
        className={classnames(styles.temperature, {
          [styles.warningTemperature]:
            coilSelector?.coil_max_temperature >= 36 && coilSelector?.coil_max_temperature < 41 && !tmsHasFault && !temperatureHasFault,
          [styles.errorTemperature]: coilSelector?.coil_max_temperature >= 41 && !tmsHasFault && !temperatureHasFault,
        })}
      >
        {`${!tmsHasFault && !temperatureHasFault && coilSelector?.render_temperature ? coilSelector?.render_temperature : '- -'}℃`}
      </p>
    </div>
  );

  const showError = product.isNav ? !!(cameraError.length || systemError.length) : !!systemError.length;

  return (
    <div className={classnames(styles.container, props.container, props.className)} ref={containerRef}>
      {
        // 技术支持不展示license信息
        `${props.userSession?.role_id}` !== RoleEnumList.TechSupport && licenseMsg && (
          <div id="camera-message" className={styles.licenseContainer}>
            {licenseMsg}
          </div>
        )
      }
      <div className={styles.pstAndCoilStatus} id="camera-content">
        {product.isNav && <CameraContainer />}
        {product.isNav && <span className={styles.split} />}
        <CoilContainer />
      </div>
      {showError && (
        <NgPopover overlayClassName={styles.errorTips} placement={'bottom'} content={renderError()}>
          <div className={styles.error_info} id="camera-error">
            <YellowError />
            <div className={styles.count}>{product.isNav ? [...cameraError, ...systemError].length : [...systemError].length}</div>
          </div>
        </NgPopover>
      )}
    </div>
  );
};
export default React.memo(withUserSession(CameraAndCoil));
