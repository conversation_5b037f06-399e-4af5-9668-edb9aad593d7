/* eslint import/no-unassigned-import: "off" */
/* eslint import/no-internal-modules: "off" */
import '@ngiq/brainbrowser/examples/js/jquery-1.6.4.min.js';
import '@ngiq/brainbrowser/examples/js/jquery-ui-1.8.10.custom.min.js';

import '@ngiq/brainbrowser/src/brainbrowser/brainbrowser.js';
import '@ngiq/brainbrowser/src/brainbrowser/core/tree-store.js';
import '@ngiq/brainbrowser/src/brainbrowser/lib/config.js';
import '@ngiq/brainbrowser/src/brainbrowser/lib/utils.js';
import '@ngiq/brainbrowser/src/brainbrowser/lib/events.js';
import '@ngiq/brainbrowser/src/brainbrowser/lib/loader.js';
import '@ngiq/brainbrowser/src/brainbrowser/lib/color-map.js';
import '@ngiq/brainbrowser/src/brainbrowser/lib/pako.js';

import '@ngiq/brainbrowser/src/brainbrowser/surface-viewer.js';
import '@ngiq/brainbrowser/src/brainbrowser/surface-viewer/lib/three.js';
import '@ngiq/brainbrowser/src/brainbrowser/surface-viewer/lib/parse-intensity-data.js';
import '@ngiq/brainbrowser/src/brainbrowser/surface-viewer/modules/annotations.js';
import '@ngiq/brainbrowser/src/brainbrowser/surface-viewer/modules/color.js';
import '@ngiq/brainbrowser/src/brainbrowser/surface-viewer/modules/loading.js';
import '@ngiq/brainbrowser/src/brainbrowser/surface-viewer/modules/rendering.js';
import '@ngiq/brainbrowser/src/brainbrowser/surface-viewer/modules/views.js';

import '@ngiq/brainbrowser/src/brainbrowser/volume-viewer.js';
import '@ngiq/brainbrowser/src/brainbrowser/volume-viewer/lib/display.js';
import '@ngiq/brainbrowser/src/brainbrowser/volume-viewer/lib/panel.js';
import '@ngiq/brainbrowser/src/brainbrowser/volume-viewer/lib/utils.js';
import '@ngiq/brainbrowser/src/brainbrowser/volume-viewer/modules/loading.js';
import '@ngiq/brainbrowser/src/brainbrowser/volume-viewer/modules/rendering.js';
import '@ngiq/brainbrowser/src/brainbrowser/volume-viewer/volume-loaders/overlay.js';
import '@ngiq/brainbrowser/src/brainbrowser/volume-viewer/volume-loaders/overlay-aligned.js';
import '@ngiq/brainbrowser/src/brainbrowser/volume-viewer/volume-loaders/minc.js';
import '@ngiq/brainbrowser/src/brainbrowser/volume-viewer/volume-loaders/nifti1.js';
import '@ngiq/brainbrowser/src/brainbrowser/volume-viewer/volume-loaders/mgh.js';
import '@ngiq/brainbrowser/src/brainbrowser/volume-viewer/volume-loaders/hdf5.js';
import '@ngiq/brainbrowser/src/brainbrowser/volume-viewer/volume-loaders/netcdf.js';

// import '@ngiq/brainbrowser/examples/js/AnaglyphEffect.js';

export const BrainBrowser = (window as any).BrainBrowser;
