import React, { useEffect, useState } from 'react';
import { useRecoilState, useRecoilValue, useSetRecoilState } from 'recoil';
import { safetyBtnStatus, safetyBtnStatusEnum, useSafetyBtnStatus } from '@/renderer/recoil/safetyBtn';
import { SimpleErrorModel } from './simpleErrorModel';
import { useModalStatusAtom } from '@/renderer/recoil/modalStatus';
import { faultAtom, getFaultByTypeList, getFaultLevel } from '../../recoil/fault';
import { FaultEnum, FaultLevelEnum } from '../../../common/systemFault/type';
import { TMSScreenState } from '../../../common/constant/tms';

type Props = {
  isStimulate?: boolean;
  faultTypeList: FaultEnum[];
  onOk(): void;
  /** 故障消失回调 */
  onNormal?(): void;
  onOpen?(): void;
};

export enum ShutdownStatusEnum {
  init = 1,
  loading = 2,
  error = 4,
}

export const ErrorModel = (props: Props) => {
  const { onOk, onOpen, onNormal, faultTypeList, isStimulate = false } = props;
  const [faultLevel, setFaultLevel] = React.useState<FaultLevelEnum | undefined>();
  const [shutdownStatus, setShutdownStatus] = useState<ShutdownStatusEnum>(ShutdownStatusEnum.init);
  const [fault] = useRecoilState(faultAtom);
  const [faultList, setFaultList] = useState<string[]>([]);
  const [visible, setVisible] = useState(false);
  const safetyBtnStatusValue = useRecoilValue(safetyBtnStatus);
  const setSafetyBtnStatus = useSetRecoilState(useSafetyBtnStatus);
  const setModalStatus = useSetRecoilState(useModalStatusAtom);
  useEffect(() => {
    setShutdownStatus(ShutdownStatusEnum.init);
    setModalStatus(visible);

    return () => {
      setModalStatus(false);
    };
  }, [visible]);

  useEffect(() => {
    const listenFault = getFaultByTypeList(faultTypeList, fault[FaultLevelEnum.error]);
    const level = getFaultLevel(faultTypeList, fault);
    setFaultLevel(level);
    setVisible(listenFault.length !== 0);
    setFaultList(listenFault.map(v => `${v.content}: ${v.key}`));

    // 本次没错&&上次有错
    if (listenFault.length === 0 && visible) {
      if (onNormal) {
        onNormal();
      } else {
        onOk();
      }
    }
  }, [fault[FaultLevelEnum.error]]);

  useEffect(() => {
    if (visible && onOpen) {
      onOpen();
    }
    // 出现异常弹窗
    if (visible) {
      window.tmsAPI.set_beat_screen(TMSScreenState.NotStarted);
    }
  }, [visible]);

  const handleOk = () => {
    if (faultLevel === FaultLevelEnum.shutdown) {
      setShutdownStatus(ShutdownStatusEnum.loading);
      setTimeout(() => {
        setShutdownStatus(ShutdownStatusEnum.error);
        setSafetyBtnStatus(safetyBtnStatusEnum.error);
      }, 60000);
      window.systemAPI.shutdown();
    } else {
      onOk();
      setVisible(false);
    }
  };

  return (
    <SimpleErrorModel
      isStimulate={isStimulate}
      isErrorModal
      errorList={faultList}
      btnLabel={faultLevel === FaultLevelEnum.shutdown ? '关机' : '我知道了'}
      onOk={handleOk}
      visible={visible && safetyBtnStatusValue === safetyBtnStatusEnum.normal}
      btnLoading={shutdownStatus === ShutdownStatusEnum.loading}
    />
  );
};
