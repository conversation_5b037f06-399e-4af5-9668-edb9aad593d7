@import '../../static/style/baseColor.module.less';
.errorModel {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: @colorA11;
  .warningIcon {
    width: 50px;
    height: 50px;
    margin-bottom: 8px;
    background: url('../../static/images/triangleWarning.png') no-repeat;
  }
  .errorTitle {
    font-size: 16px;
    line-height: 28px;
    font-weight: 500;
  }
  .isStimulate {
    font-size: 16px;
    font-weight: 500;
  }
  .errList {
    margin-top: 12px;
    text-align: right;
    .errorItem {
      font-size: 12px;
      height: 24px;
      margin-bottom: 2px;
    }
  }
  .errorReason {
    margin-top: 6px;
    font-size: 12px;
    padding-left: 12px;
  }
  .btnContainer {
    display: flex;
    flex-direction: row;
    margin-top: 20px;
    button {
      height: 32px;
      font-size: 16px;
      font-weight: 350;
    }
    .cancleBtn {
      color: #d0d0d4 !important;
      margin-right: 12px;
    }
  }
  .okButton {
    margin-top: 20px;
    button {
      height: 32px;
    }
  }
}

:global {
  .errmodal_container {
    z-index: 10000 !important;
  }
}
