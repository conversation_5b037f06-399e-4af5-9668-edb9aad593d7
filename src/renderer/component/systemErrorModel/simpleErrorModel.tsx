import React, { useEffect } from 'react';
import NgModal from '@/renderer/uiComponent/NgModal';
import NgButton from '@/renderer/uiComponent/NgButton';
import styles from './index.module.less';
import { useIntl } from 'react-intl';
import { Button } from 'antd';
import { sendRenderLog } from '../../utils/renderLogger';
import { useRecoilState, useSetRecoilState } from 'recoil';
import { tmsCoilSelector } from '../../recoil/tmsError';
import { useSimpleModalStatusAtom } from '@/renderer/recoil/simpleModalStatus';

type Props = {
  visible: boolean;
  isClearRegistCoilInfo?: boolean;
  isStimulate: boolean;
  errorList: string[];
  onOk(): void;
  onCancle?(): void;
  btnLabel?: string;
  cancleLabel?: string;
  title?: string;
  btnLoading?: boolean;
  isHiddenBtn?: boolean;
  isErrorModal?: boolean;
};

export const SimpleErrorModel = (props: Props) => {
  const [coilSelector, setCoilSelector] = useRecoilState(tmsCoilSelector);
  const setSimpleModalStatus = useSetRecoilState(useSimpleModalStatusAtom);
  const intl = useIntl();
  const {
    errorList,
    onOk,
    onCancle,
    isStimulate,
    visible = false,
    btnLabel,
    cancleLabel,
    isClearRegistCoilInfo = false,
    title = '异常',
    isHiddenBtn = false,
    btnLoading = false,
    isErrorModal = false,
  } = props;

  useEffect(() => {
    if (!isErrorModal) setSimpleModalStatus(visible);

    return () => {
      setSimpleModalStatus(false);
    };
  }, [visible]);

  const handleOk = async () => {
    onOk();
    await clearRegistCoilInfo();
  };
  const handleCancle = async () => {
    onCancle?.();
    await clearRegistCoilInfo();
  };
  const clearRegistCoilInfo = async () => {
    if (!isClearRegistCoilInfo) return;
    window.fileAPI.setBatData('coil_id_empty');
    sendRenderLog.info('imgsocket矩阵缺失报错，抹掉拍子ID');
    // 设置isRegisterCoil为false
    const coilInfo = coilSelector;
    coilInfo.isRegisterCoil = false;
    await window.systemAPI.setStore('isRegisterCoil', coilInfo.isRegisterCoil);
    sendRenderLog.info('imgsocket矩阵缺失报错，更改拍子状态\n', JSON.stringify(coilInfo));
    setCoilSelector(coilInfo);
  };

  return (
    <NgModal title="" closable={false} open={visible} width={400} wrapClassName={'errmodal_container'} footer={<> </>}>
      <div className={styles.errorModel}>
        <div className={styles.warningIcon} />
        <div className={styles.errorTitle}>{intl.formatMessage({ id: title })}</div>
        {isStimulate && <div className={styles.isStimulate}>{intl.formatMessage({ id: '已停止脉冲治疗' })}</div>}
        <div className={styles.errList}>
          {errorList.map(item => {
            return (
              <div key={item} className={styles.errorItem}>
                {item}
              </div>
            );
          })}
        </div>
        <div className={cancleLabel ? styles.btnContainer : styles.okButton}>
          {cancleLabel && (
            <Button className={styles.cancleBtn} type="link" onClick={handleCancle}>
              {intl.formatMessage({ id: cancleLabel })}
            </Button>
          )}
          {!isHiddenBtn && (
            <NgButton loading={btnLoading} onClick={handleOk}>
              {intl.formatMessage({ id: btnLabel })}
            </NgButton>
          )}
        </div>
      </div>
    </NgModal>
  );
};
