import React from 'react';
import { <PERSON><PERSON>rowser } from '../brainbrowser/imports';
import { clearThreeData, viewerLoadColorMapFromURL, viewerLoadModelFromLocalPath } from './utils';
import { Coordinate } from '../../container/previewPlan/component/surface/utils';
import { sendRenderLog } from '../../utils/renderLogger';
import { Setting } from '../../../common/types';

type Props = {
  setSurfaceViewer(viewer: any): void;
  colorMap?: string;
  pialUrl: string;
  scalpMaskUrl: string;
  volumeFiles: string[];
  pialOpacity?: number;
  scalpOpacity?: number;
  loadComplete?(): void;
  onClickChangePoint?(coords: Coordinate, index: number): void;
};
type State = {
  viewer: any;
};

let isUnmount = false;

export class Surface extends React.Component<Props, State> {
  private brainBrowserRef: React.RefObject<HTMLDivElement>;
  constructor(props: Props) {
    super(props);
    this.state = {
      viewer: null,
    };
    this.brainBrowserRef = React.createRef<HTMLDivElement>();
  }

  public componentDidMount() {
    this.initBrain();
    isUnmount = false;
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    this.startSurface();
  }

  public componentWillUnmount(): void {
    isUnmount = true;
    this.clearSurface();
  }

  private clearSurface = () => {
    const { viewer } = this.state;
    if (viewer) {
      viewer.dom_element = undefined;
      viewer.drawTrajectory = undefined;
      viewer.clearLines = undefined;
      viewer.updateLineText = undefined;
      (window as any).cancelAnimationFrame(viewer.requestAnimationFrame);
      clearThreeData(viewer);
      if (this.brainBrowserRef.current) {
        const canvas = this.brainBrowserRef.current.childNodes[0] as HTMLCanvasElement;
        if (!canvas) return;
        const context = (canvas.getContext('webgl') || canvas.getContext('experimental-webgl')) as any;
        if (context) {
          context.clearColor(1.0, 1.0, 0.0, 1.0);
          context.clear(context.COLOR_BUFFER_BIT);
          context.clear(context.DEPTH_BUFFER_BIT);
          context.clear(context.STENCIL_BUFFER_BIT);
          const webglContext = context.getExtension('WEBGL_lose_context');
          if (webglContext) webglContext.loseContext();
        }
      }
      viewer.clearScreen();
    }
    BrainBrowser.events.clearAllListeners();
  };

  private initBrain = () => {
    BrainBrowser.SurfaceViewer.cachedLoader = undefined;
    BrainBrowser.SurfaceViewer.canCached = true;
    BrainBrowser.config.set('worker_dir', 'brainbrowser-2.5.5/workers');
  };

  private startSurface = async () => {
    BrainBrowser.SurfaceViewer.start(
      this.brainBrowserRef.current,
      async (viewer: any) => {
        this.setState({ viewer });
        this.props.setSurfaceViewer(viewer);
        viewer.render();
        // eslint-disable-next-line @typescript-eslint/no-floating-promises
        this.loadModel(viewer);
      },
      undefined,
      false,
      0x141426
    );
  };

  private loadModel = async (viewer: any) => {
    const { colorMap, pialUrl, scalpMaskUrl, pialOpacity, scalpOpacity } = this.props;
    viewer.annotations.setMarkerRadius(3);
    const configUrl: Setting = await window.systemAPI.getSetting();
    const promiseList = [];
    if (colorMap) {
      promiseList.push(viewerLoadColorMapFromURL(viewer, `colormap/${colorMap}`));
    }
    if (pialUrl) {
      promiseList.push(
        viewerLoadModelFromLocalPath(viewer, pialUrl, {
          format: 'gifti',
          modelName: 'pial_gii',
          contentType: 'text',
          opacity: pialOpacity,
        })
      );
    }
    if (scalpMaskUrl) {
      promiseList.push(
        viewerLoadModelFromLocalPath(viewer, scalpMaskUrl, {
          format: 'mniobj',
          modelName: 'scalp_mask',
          contentType: 'text',
          opacity: scalpOpacity,
          scalp_mask_options: {
            color: 0xaeaeae,
            emissive: 0xa3a3a3,
          },
        })
      );
    }
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    Promise.all(promiseList).then(() => {
      this.props.loadComplete?.();
      viewer.zoom = 0.85;
      const canvas = this.brainBrowserRef.current?.children[0];
      if (canvas) {
        canvas.addEventListener('webglcontextlost', e => {
          if (!isUnmount) {
            sendRenderLog.error('webgl销毁', 'surface');
            this.initBrain();
            // eslint-disable-next-line @typescript-eslint/no-floating-promises
            this.startSurface();
          }
        });
      }
    });
    if (configUrl.SURFACE_GRID === 'show') {
      ['gridX', 'gridY', 'gridZ'].forEach(l => {
        viewer.drawGridXYZ(l, true, { x: 0, y: 0, z: 0 });
      });
    }
  };

  private handleOnMouseUp = () => {
    const { viewer } = this.state;
    if (viewer.moveFlag) return;
    if (viewer.model.children.length === 0) {
      return;
    }
    const pickInfo = viewer.pick(viewer.mouse.x, viewer.mouse.y, 100);
    if (pickInfo) {
      let x = pickInfo.point.x.toFixed(2);
      let y = pickInfo.point.y.toFixed(2);
      let z = pickInfo.point.z.toFixed(2);

      if (this.props.onClickChangePoint) {
        this.props.onClickChangePoint({ x: Number(x), y: Number(y), z: Number(z) }, pickInfo.index);
      }
    }
  };

  render(): React.ReactNode {
    return <div ref={this.brainBrowserRef} style={{ width: 989, height: 989 }} onMouseUp={this.handleOnMouseUp} />;
  }
}

export default Surface;
