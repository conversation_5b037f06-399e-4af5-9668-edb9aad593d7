import * as THREE from 'three';
import { BrainBrowser } from '../brainbrowser/imports';

export const viewerLoadColorMapFromURL = async (viewer: any, url: string) => {
  return new Promise((resolve: any) => {
    viewer.loadColorMapFromURL(url, () => {
      resolve();
    });
  });
};

type LoadOptions = {
  format: string;
  modelName: string;
  resultType?: string;
  contentType?: string;
  opacity?: number;
  getFileBuffer?(buffer: Buffer): void;
  scalp_mask_options?: {
    color?: number;
    emissive?: number;
    emissiveIntensity?: number;
  };
};

// 通过本地路径加载surface
export const viewerLoadModelFromLocalPath = async (viewer: any, url: string, options?: LoadOptions) => {
  const buffer: Buffer | string = await window.fileAPI.getFile(url);
  if (typeof buffer === 'string') return Promise.reject();

  return new Promise<void>(resolve => {
    viewer.loadModelFromLocalFilePath(buffer.buffer, {
      ...options,
      model_name: options?.modelName,
      complete: () => {
        options?.getFileBuffer?.(buffer);
        resolve();
      },
    });
  });
};

// 通过recoil缓存中的buffer加载本地文件
export const viewerLoadModelFromCache = async (viewer: any, bufferCache: Buffer, options?: LoadOptions) => {
  return new Promise<void>(resolve => {
    viewer.loadModelFromLocalFilePath(bufferCache.buffer, {
      ...options,
      complete: () => {
        resolve();
      },
    });
  });
};

// 拍子透明
export const setBatOpacity = (batObj: any, opacity = 0.7, shininess = 10) => {
  batObj.children.forEach((item: any) => {
    // eslint-disable-next-line @typescript-eslint/no-unused-expressions
    item.children.length &&
      item.children.forEach((batMesh: any) => {
        batMesh.material.transparent = true;
        batMesh.material.opacity = opacity;
        batMesh.material.shininess = shininess;
      });
  });

  return batObj;
};

// 绘制中心点
export const setBatCenterPoint = (batObj: any, batNum = 1) => {
  batObj.children = batObj.children.filter((item: any) => item.name !== 'batCenter');

  const geometry = new THREE.SphereGeometry(1, 32, 32);
  const material = new THREE.MeshBasicMaterial({
    color: 0xd36f66,
  });

  const sphere = new THREE.Mesh(geometry, material);
  sphere.position.set(0, 0, -8.2);
  // sphere.position.set(0, 0, batNum ===1 ? -12 : -59);
  sphere.name = 'batCenter';
  sphere.parent = batObj;
  batObj.children.push(sphere);

  return batObj;
};

// 绘制拍子法线
export const setBatCenterLine = (batObj: any) => {
  batObj.children = batObj.children.filter((item: any) => item.name !== 'batCenterLine');
  const material = new THREE.LineBasicMaterial({
    color: 0xff851b,
    linewidth: 2,
  });
  const points = [];
  points.push(new THREE.Vector3(0, 0, -120));
  points.push(new THREE.Vector3(0, 0, 0));
  const geometry = new THREE.BufferGeometry().setFromPoints(points);

  const line = new THREE.Line(geometry, material);

  line.name = 'batCenterLine';
  line.visible = false;

  batObj.add(line);

  return batObj;
};

// 拍子视角 绘制拍子上的1cm直径圆
export const setBatCircle = (batObj: any, batNum = 1) => {
  const batCircleObjs = batObj.children.filter((item: any) => item.name === 'batCircle');

  freeBufferList(batCircleObjs);

  batObj.children = batObj.children.filter((item: any) => item.name !== 'batCircle');

  const geometry = new THREE.RingGeometry(10 * 0.6 - 0.5, 11 * 0.6 - 0.5, 30);
  const material = new THREE.MeshBasicMaterial({
    color: 0x646464,
    side: THREE.DoubleSide,
  });

  const sphere = new THREE.Mesh(geometry, material);
  sphere.position.set(0, 0, -8.2);
  sphere.name = 'batCircle';
  sphere.parent = batObj;
  batObj.children.push(sphere);

  return batObj;
};

export const clearThreeData = (viewer: any) => {
  if (!viewer) return;
  if (!(viewer && viewer.model)) return;
  const masks = viewer.model.children || [];

  // clear threejs memory
  masks.forEach((mask: any) => {
    if (!mask) return;
    if (mask.geometry) {
      mask.geometry.dispose();
      mask.geometry = undefined;
      delete mask.geometry;
    }
    if (mask.material) {
      mask.material.dispose();
      mask.material = undefined;
      delete mask.material;
    }
    if (mask.userData) {
      mask.userData = undefined;
    }
  });
  let { cacheSurfaceXHRs } = BrainBrowser.loader;
  if (cacheSurfaceXHRs && cacheSurfaceXHRs.length) {
    cacheSurfaceXHRs.forEach((e: any, index: number) => (cacheSurfaceXHRs[index] = undefined));
    cacheSurfaceXHRs = [];
  }
  if (viewer.clearCachedWebgl) {
    viewer.clearCachedWebgl();
  }
};

export const freeBuffer = (obj3d: any) => {
  if (!obj3d) return;

  if (obj3d.children && obj3d.children.length) {
    freeBufferList(obj3d.children);
  }

  if (obj3d.geometry) {
    obj3d.geometry.dispose();
  }
  if (obj3d.material) {
    obj3d.material.dispose();
  }
};

export const freeBufferList = (objList: any[] = []) => {
  objList.forEach((obj: any) => freeBuffer(obj));
};

// obj格式拍子透明
export const setBatOpacityOfObj = (batObj: any, opacity = 0.7, shininess = 10) => {
  batObj.children.forEach((batMesh: any) => {
    batMesh.material.transparent = true;
    batMesh.material.opacity = opacity;
    batMesh.material.shininess = shininess;
  });

  return batObj;
};
