import { N<PERSON>rrowHelper } from './ngArrowHelper';

type Coordinate = {
  x: number;
  y: number;
  z: number;
};
// @ts-ignore
// eslint-disable-next-line import/no-internal-modules
import * as THREE from 'three';

export type IndicatorType = {
  line1StartPoint: Coordinate;
  line1EndPoint: Coordinate;
  line2StartPoint: Coordinate;
  line2EndPoint: Coordinate;
  groupName: string;
  line1Color?: number;
  line2Color?: number;
  arcColor?: number;
};
export const drawHorizontalOffsetIndicator = (options: IndicatorType, viewer: any) => {
  const { line1StartPoint, line1EndPoint, line2EndPoint, line2StartPoint, line1Color, line2Color, arcColor, groupName } = options;
  let line1 = drawDottedLine(line1StartPoint, line1EndPoint, viewer, line1Color);
  let line2 = drawSolidLine(line2StartPoint, line2EndPoint, viewer, line2Color);
  const group = new THREE.Group();
  group.add(line1);
  group.add(line2);

  const angle = getAngle(
    { x: line2EndPoint.x - line1StartPoint.x, y: line2EndPoint.y - line1StartPoint.y, z: line2EndPoint.z - line1StartPoint.z },
    { x: line1EndPoint.x - line1StartPoint.x, y: line1EndPoint.y - line1StartPoint.y, z: line1EndPoint.z - line1StartPoint.z }
  );
  if (Math.abs(angle) > Math.PI / 180) {
    let arc = drawDottedDirection(line1StartPoint, line1EndPoint, line2EndPoint, angle, arcColor);
    group.add(arc);
  }
  group.name = groupName;

  viewer.model.add(group);
  viewer.updated = true;
};

/**
 * 绘制虚线
 * @param start
 * @param end
 * @param viewer
 * @param color
 * @description 通过绘制多段线来实现虚线
 */
const drawMultiLine = (start: Coordinate, end: Coordinate, viewer: any, color: number) => {
  let tempLine = new THREE.LineCurve3(new THREE.Vector3(start.x, start.y, start.z), new THREE.Vector3(end.x, end.y, end.z));
  const points = tempLine.getPoints(50);
  const groups = new THREE.Group();
  for (let i = 0; i < points.length - 1; i++) {
    if (i % 5 === 0) {
      const segmentLine = viewer.drawLine(points[i], points[i + 3], { color: color, draw: false, lineWidth: 10 });
      groups.add(segmentLine);
    }
  }

  return groups;
};
/**
 * 绘制虚线  开始线
 * @param startParams
 * @param endParams
 * @param viewer
 * @param color
 */
const drawDottedLine = (startParams: Coordinate, endParams: Coordinate, viewer: any, color = 0x3662ec) => {
  let end = new THREE.Vector3(endParams.x, endParams.y, endParams.z);
  const groupLines = drawMultiLine(startParams, endParams, viewer, color);
  let group = new THREE.Group();
  group.add(groupLines);
  const dir = new THREE.Vector3(endParams.x - startParams.x, endParams.y - startParams.y, endParams.z - startParams.z);
  dir.normalize();
  const arrow = new NGArrowHelper(dir, end, 1, color, 8, 6) as any;
  group.add(arrow);

  return group;
};

/**
 * 绘制实线  终止线
 * @param startParams
 * @param endParams
 * @param viewer
 * @param color
 */
const drawSolidLine = (startParams: Coordinate, endParams: Coordinate, viewer: any, color = 0x3662ec) => {
  let start = new THREE.Vector3(startParams.x, startParams.y, startParams.z);
  let end = new THREE.Vector3(endParams.x, endParams.y, endParams.z);
  const endLine = viewer.drawLine(start, end, { color: color, lineWidth: 10, draw: false });
  let group = new THREE.Group();
  group.add(endLine);
  const dir = new THREE.Vector3(endParams.x - startParams.x, endParams.y - startParams.y, endParams.z - startParams.z);
  dir.normalize();
  // dir 是向量的方向
  // end 绘制箭头的位置
  const arrow = new NGArrowHelper(dir, end, 1, color, 8, 6) as any;
  group.add(arrow);

  return group;
};
/**
 * 计算两个向量的夹角
 * @param vector1
 * @param vector2
 * @return 返回的是弧度
 */
const getAngle = (vector1: Coordinate, vector2: Coordinate) => {
  const v1 = new THREE.Vector3(vector1.x, vector1.y, vector1.z);
  const v2 = new THREE.Vector3(vector2.x, vector2.y, vector2.z);

  return v1.angleTo(v2);
};

/**
 * 计算弧度对应的系数 0-30:0.1; 30-60:0.2; 60-90:0.3; 90-130:0.7; 130-180:1;
 * @param angle
 * @return 返回的是系数
 */
const getAngleCoefficient = (angle: number) => {
  angle = (angle * 180) / Math.PI;
  if (angle <= 30) {
    return 0.1;
  } else if (angle > 30 && angle <= 60) {
    return 0.2;
  } else if (angle > 60 && angle <= 90) {
    return 0.3;
  } else if (angle > 90 && angle <= 130) {
    return 0.7;
  } else {
    return 1;
  }
};

/**
 * 计算中心点
 * @param intersectionPoint
 * @param startParams
 * @param endParams
 * @return 返回的中心点，是两个点的中心点，然后再加上偏移量， 用于贝塞尔曲线的控制点
 */
const calCenterPoint = (intersectionPoint: Coordinate, startParams: Coordinate, endParams: Coordinate) => {
  const x = (startParams.x + endParams.x) / 2;
  const y = (startParams.y + endParams.y) / 2;
  const z = (startParams.z + endParams.z) / 2;

  const offsetX = x - intersectionPoint.x;
  const offsetY = y - intersectionPoint.y;
  const offsetZ = z - intersectionPoint.z;

  const angle = getAngle(
    { x: endParams.x - intersectionPoint.x, y: endParams.y - intersectionPoint.y, z: endParams.z - intersectionPoint.z },
    { x: startParams.x - intersectionPoint.x, y: startParams.y - intersectionPoint.y, z: startParams.z - intersectionPoint.z }
  );
  const coefficient = getAngleCoefficient(angle);

  return { x: x + offsetX * coefficient, y: y + offsetY * coefficient, z: z + offsetZ * coefficient };
};

/**
 * 绘制弧线
 * @param intersectionPoint 交点
 * @param line1EndPoint 第一条线段的终点
 * @param line2EndPoint  第二条线段的终点
 * @param angle 两条线段的夹角
 * @param arcColor
 */
const drawDottedDirection = (
  intersectionPoint: Coordinate,
  line1EndPoint: Coordinate,
  line2EndPoint: Coordinate,
  angle: number,
  arcColor = 0x3662ec
) => {
  // 利用curve 来获取线段70% 位置的点，然后这两个点 画弧线
  let startLine = new THREE.LineCurve3(
    new THREE.Vector3(intersectionPoint.x, intersectionPoint.y, intersectionPoint.z),
    new THREE.Vector3(line1EndPoint.x, line1EndPoint.y, line1EndPoint.z)
  );
  let startPoint = startLine.getPoints(10)[7];
  let endLine = new THREE.LineCurve3(
    new THREE.Vector3(intersectionPoint.x, intersectionPoint.y, intersectionPoint.z),
    new THREE.Vector3(line2EndPoint.x, line2EndPoint.y, line2EndPoint.z)
  );
  let endPoint = endLine.getPoints(10)[7];
  // 贝塞尔曲线需要一个 额外的点，这个点去两个线的中心点，与两线交点 连线外延0.2个单位
  const centerPoint = calCenterPoint(intersectionPoint, startPoint, endPoint);

  const curve = new THREE.QuadraticBezierCurve3(
    new THREE.Vector3(startPoint.x, startPoint.y, startPoint.z),
    new THREE.Vector3(centerPoint.x, centerPoint.y, centerPoint.z),
    new THREE.Vector3(endPoint.x, endPoint.y, endPoint.z)
  );

  const points = curve.getPoints(50);
  // 由于产品设计，要掐头去尾，只取中间的点
  const newPoints = points.slice(3, 47);
  const geometry = new THREE.BufferGeometry().setFromPoints(newPoints);

  const material = new THREE.LineDashedMaterial({
    color: arcColor,
    scale: 1,
    dashSize: 2,
    gapSize: 1,
  });

  // Create the final object to add to the scene
  const curveObject = new THREE.Line(geometry, material);

  let dir = new THREE.Vector3(points[43].x - points[40].x, points[43].y - points[40].y, points[43].z - points[40].z);
  dir.normalize();
  const { headLength, headWidth, start: startIndex } = getArrowSize(angle);
  const arrow = new NGArrowHelper(dir, newPoints[startIndex], 1, arcColor, headLength, headWidth) as any;

  let group = new THREE.Group();
  group.add(curveObject);
  group.add(arrow);

  return group;
};

/**
 * 计算箭头的大小
 */
const getArrowSize = (angle: number) => {
  angle = (angle * 180) / Math.PI;
  if (angle < 10) {
    return { headLength: 2, headWidth: 1, start: 41 };
  } else if (angle <= 30) {
    return { headLength: 3, headWidth: 1.5, start: 42 };
  } else {
    return { headLength: 5, headWidth: 3, start: 43 };
  }
};
