@import '../../static/style/baseColor.module.less';

.spot_container {
  padding: 10px;
  background-color: @colorA3;
  font-size: 12px;
  margin-bottom: 12px;
  border: 1px solid @colorA3;
  border-radius: 6px;
  font-family: sans-serif !important;

  &.container_active {
    border-color: @colorC4;
  }

  &.container_error {
    border-color: @colorB3;
  }

  &.container_disable {
    cursor: url('@/renderer/static/svg/disableMouse.cur'), pointer !important;
  }

  p {
    margin: 0;
  }

  span {
    margin: 0;
  }

  .title_container {
    display: flex;

    .title_item {
      display: flex;
      align-items: baseline;
      flex-grow: 1;
    }

    .dot {
      width: 12px;
      height: 12px;
      border-radius: 100%;
    }

    .title {
      font-size: 14px;
      margin-left: 8px;
      max-width: 100px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .spot_type {
      border-radius: 4px;
      background-color: @colorA4;
      margin-left: 10px;
      line-height: 20px;
      padding: 0 8px;
    }
  }

  .coord_item {
    line-height: 20px;
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    margin-top: 12px;
    margin-bottom: 4px;

    .label {
      color: @colorA9;
    }

    .value {
      color: @colorA12;
      max-width: 140px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      text-align: right;
    }
  }
}
