import React, { MouseEvent, useCallback, useEffect, useRef, useState } from 'react';
import { useIntl } from 'react-intl';
import { Spin, Tooltip } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';
import classnames from 'classnames';
import styles from './index.module.less';
import { colors } from '../../container/previewPlan/config';
import { NgIcon } from '../../uiComponent/NgIcon';
import { Delete } from '../../uiComponent/SvgGather';
import { NgPopconfirm } from '../../uiComponent/NgPopconfirm';
import { AuthEnum } from '../../utils/authConfig';
import { useRecoilState } from 'recoil';
import { authTypeState } from '../../recoil/license';
import { SpotList } from '../../container/previewTreat';

const antIcon = <LoadingOutlined style={{ fontSize: 24, color: 'rgba(41,177,203)' }} spin rev="" />;

type Spot = {
  spotInfo: SpotList;
  activeId: number | null;
  onSelect(id: number): void;
  onDelete?(id: number): void;
  isDelete?: boolean;
  index: number;
  itemStatus?: AuthEnum;
  isTreat?: boolean;
  refreshOpen?: number;
  isBatVerification?: boolean;
};

export const SpotItem = (props: Spot) => {
  const { spotInfo, onSelect, onDelete, itemStatus, isBatVerification = false } = props;
  const intl = useIntl();
  const [authState] = useRecoilState(authTypeState);
  const [open, setOpen] = useState(false);
  const [coordinate, setCoordinate] = useState<string>();
  const [coordOverFlow, setCoordOverFlow] = useState(false);
  const [nameOverFlow, setNameOverFlow] = useState(false);
  const coordSpanRef = useRef<HTMLSpanElement | null>(null);
  const nameSpanRef = useRef<HTMLSpanElement | null>(null);

  const intlMessage = useCallback((value: string, options?: { [prop: string]: any }) => {
    return intl.formatMessage({ id: value, ...options });
  }, []);

  const handleDelete = (e?: MouseEvent<HTMLElement>) => {
    e?.preventDefault();
    e?.stopPropagation();
    if (onDelete) {
      onDelete(spotInfo.key!);
    }
  };

  const handleClick = () => {
    if (spotInfo.is_active || props.activeId === spotInfo.key || props.activeId === spotInfo.id) return;
    if (!isNaN(spotInfo.key!)) {
      onSelect(spotInfo.key!);
    } else {
      onSelect(spotInfo.id!);
    }
  };

  const spotItemRender = () => {
    return (
      <div
        className={classnames(
          styles.spot_container,
          props.activeId === spotInfo.key || props.activeId === spotInfo.id ? styles.container_active : '',
          (spotInfo.is_error || spotInfo.is_range_error) && styles.container_error,
          spotInfo.normal_line || !props.isTreat ? '' : styles.container_disable
        )}
        onClick={handleClick}
      >
        <div className={styles.title_container}>
          <div className={styles.title_item}>
            <div className={styles.dot} style={{ backgroundColor: spotInfo.color || colors[props.index] }} />
            {nameOverFlow ? (
              <Tooltip title={spotInfo.name}>
                <span className={styles.title} ref={nameSpanRef}>
                  {spotInfo.name}
                </span>
              </Tooltip>
            ) : (
              <span className={styles.title} ref={nameSpanRef}>
                {spotInfo.name}
              </span>
            )}
            <div className={styles.spot_type}>{spotInfo.has_mep ? intlMessage('MEP参考点') : intlMessage('治疗靶点')}</div>
          </div>
          {props.isDelete && (
            <NgPopconfirm
              title=""
              description={intlMessage('是否删除靶点')}
              onConfirm={handleDelete}
              onCancel={(e: any) => {
                e.preventDefault();
                e.stopPropagation();
              }}
              okText={intlMessage('确认')}
              cancelText={intlMessage('取消')}
              open={open}
              onOpenChange={a => {
                setOpen(a);
              }}
              disabled={itemStatus === AuthEnum.disable}
            >
              <NgIcon
                everAllowClick
                disabled={authState['previewPlan.spotDelete'] === 1}
                onClick={e => {
                  e.preventDefault();
                  e.stopPropagation();
                }}
                iconClass={styles.iconClass}
                fontSize={20}
                iconSvg={Delete}
                tooltip={{ placement: 'left', title: '删除靶点', color: '#434351' }}
              />
            </NgPopconfirm>
          )}
          {props.isTreat && !spotInfo.normal_line && <Spin indicator={antIcon} />}
        </div>
        <div className={styles.coord_item}>
          <p className={styles.label}>{intlMessage('靶点坐标（volRAS）：')}</p>
          {coordOverFlow ? (
            <Tooltip title={coordinate}>
              <span className={styles.value} ref={coordSpanRef}>
                {coordinate}
              </span>
            </Tooltip>
          ) : (
            <span className={styles.value} ref={coordSpanRef}>
              {coordinate}
            </span>
          )}
        </div>
        <div className={styles.coord_item}>
          <p className={styles.label}>{intlMessage('顶角编号：')}</p>
          <p className={styles.value}>{`${spotInfo.hemi}${spotInfo.vertex_index}`}</p>
        </div>
      </div>
    );
  };

  const techSupportSpotItemRender = () => {
    return (
      <div
        className={classnames(
          styles.spot_container,
          props.activeId === spotInfo.key || props.activeId === spotInfo.id ? styles.container_active : '',
          (spotInfo.is_error || spotInfo.is_range_error) && styles.container_error,
          spotInfo.normal_line || !props.isTreat ? '' : styles.container_disable
        )}
        onClick={handleClick}
      >
        <div className={styles.title_container}>
          <div className={styles.title_item}>
            <div className={styles.dot} style={{ backgroundColor: spotInfo.color || colors[props.index] }} />
            <p className={styles.title}>{spotInfo.name}</p>
          </div>
        </div>
      </div>
    );
  };

  useEffect(() => {
    setOpen(false);
  }, [props.refreshOpen]);

  useEffect(() => {
    const coord = [(+spotInfo?.vol_ras?.x)?.toFixed?.(2), (+spotInfo?.vol_ras?.y)?.toFixed?.(2), (+spotInfo?.vol_ras?.z)?.toFixed?.(2)].join(',');
    setCoordinate(coord);
    if (coordSpanRef.current) {
      const isOverF = coordSpanRef.current.offsetWidth >= 140;
      setCoordOverFlow(isOverF);
    }
  }, [spotInfo, coordinate]);

  useEffect(() => {
    if (nameSpanRef.current) {
      const isOverF = nameSpanRef.current.offsetWidth >= 100;
      setNameOverFlow(isOverF);
    }
  }, [spotInfo.name]);

  return <>{isBatVerification ? techSupportSpotItemRender() : spotItemRender()}</>;
};
