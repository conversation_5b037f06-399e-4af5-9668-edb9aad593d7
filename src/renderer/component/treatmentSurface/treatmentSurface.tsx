import React from 'react';
import { flatten, isNumber } from 'lodash';
import * as THREE from 'three';
// eslint-disable-next-line import/no-internal-modules
import * as mathjs from 'mathjs';
import * as mathlab from 'mathlab';
import classNames from 'classnames';
import Surface from '../surface';
import { PlanModel, PlanStimulusModel, PlanTargetModel } from '../../../common/types';
import { ImmediateView } from '../../container/previewTreat/component/immediateView';
import { PreviewVolume } from '../../container/previewPlan/component/volume';
import { volumeXYZtoSurface } from '../../container/previewPlan/utils';
import { GetStatusCallbackParam, connSocket, personBatDistanceSocket } from '../../utils/imgSocket';
import { freeBufferList } from '../surface/utils';
import { CalculateDirection } from '../../utils/armCalculate';
import { M200Api } from '../../../common/api/ng/m200Api';
import { getM200ApiInstance } from '../../../common/api/ngApiAgent';
import { colors } from '../../container/previewPlan/config';
import { Coordinate, removeDot, updateSurfaceCursorByWord } from '../../container/previewPlan/component/surface/utils';
import { batInfoByMatrix, bat } from '../../utils/treat';
import { arrowGroup } from './indicator';
import { drawHorizontalOffsetIndicator } from '../surface/drawHorizontalOffsetIndicator';
import { movingAverage } from '../../utils/movingAverage';
import { sendRenderLog, sendTreatMatrixLog } from '../../utils/renderLogger';
import { InstructionInfo, VisibleInfoType, initInstructionInfo, initVisibleInfo } from '../../container/manage/components/manageDevice/config';
import { isIdentityMatrix } from '../../utils/mathUtil';

type SpotList = PlanTargetModel & { is_active?: boolean };

type Props = {
  pialUrl: string;
  scalpMaskUrl: string;
  volumeFiles: string[];
  spotList: (SpotList | PlanTargetModel)[];
  targetStimulate?: PlanStimulusModel;
  activeSpot?: PlanTargetModel;
  isTreating: boolean;
  isBatVerifiCation?: boolean;
  planId: string;
  subjectId: string;
  planInfo: PlanModel | null;
  children: any;
  SpotList?: any;
  activeId: number | null;
  computedNormalCallback?(data?: any): void;
  horizontal: number;
  setLoading?(loadings: any): void;
  surfaceLoadComplete?(): void;
  isEmg?: boolean;
  immediateViewClass?: string;
  setVolumeLoading?(loading: boolean): void;
  setVolumeViewer(volumeViewer: any): void;
  getVolumeScalpIndex(index: number): void;
  horizontalDisable: boolean;
  onClickChangePoint?(coords: Coordinate): void;
  coilPanelMissingCallback?(): void;
  helper: boolean;
};

export type State = {
  surfaceViewer: any;
  vizArray: string[];
  volumeViewer: any;
  tkras2ras: any;
  normalLine: any;
  diff: number;
  fps: number;
  visiblePart: VisibleInfoType;
  instructionPart?: InstructionInfo;
  mainControl: boolean;
};

export class TreatmentSurface extends React.PureComponent<Props, State> {
  private personBatInterval: any;
  private m200Api: M200Api;
  private coilMatrix: any[];
  private headMatrix: any[];
  private coilMatrixDisplay: any[];
  private headMatrixDisplay: any[];
  private endTarget: Coordinate | undefined;
  // private horizontalBat: any;
  private animationCoil: any;
  private loadBatSuccess: boolean;
  private transformMatrix: any;
  private treatboardError: boolean;
  private isUpdating: boolean;
  private computedRotateMatrixValue: any;
  // private prevBatVol: number[];
  private prevLineVol: number[];
  private faceError: boolean;
  private last: number;
  constructor(props: Props) {
    super(props);
    this.state = {
      surfaceViewer: null,
      volumeViewer: null,
      vizArray: [],
      tkras2ras: null,
      normalLine: null,
      diff: 0,
      fps: 20,
      visiblePart: initVisibleInfo,
      instructionPart: undefined,
      mainControl: false,
    };
    this.personBatInterval = React.createRef();
    this.m200Api = getM200ApiInstance();
    this.coilMatrix = [];
    this.headMatrix = [];
    this.endTarget = undefined;
    this.animationCoil = null;
    this.loadBatSuccess = false;
    this.transformMatrix = null;

    this.treatboardError = false;
    this.faceError = false;
    this.isUpdating = false;
    this.computedRotateMatrixValue = null;
    // this.prevBatVol = [0,0,0];
    this.prevLineVol = [0, 0, 0];
    this.last = new Date().getTime();
    this.coilMatrixDisplay = [];
    this.headMatrixDisplay = [];
  }

  componentDidMount() {
    // connSocket.setState();
    this.props?.setVolumeLoading?.(true);
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    this.getTreatConfig();
    this.personBatInterval = setInterval(personBatDistanceSocket.createSocket, 2000);
    connSocket.listenStatus('TreatmentSurface_error', this.listenerErrors);
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    this.setVolumeVizArray();
    personBatDistanceSocket.listenMessage('TreatmentSurface', this.getCoil);
  }

  componentWillUnmount(): void {
    // connSocket.clearStateInterval();
    clearInterval(this.personBatInterval);
    cancelAnimationFrame(this.animationCoil);
    connSocket.clearListenStatusByKey('TreatmentSurface_error');
    personBatDistanceSocket.clearListenMessageByKey('TreatmentSurface');
  }

  componentDidUpdate(prevProps: Readonly<Props>, prevState: Readonly<State>, snapshot?: any): void {
    const { activeSpot } = this.props;
    if (
      (prevProps.activeId !== this.props.activeId && this.props.activeId) ||
      (activeSpot?.normal_line && activeSpot?.normal_line !== prevProps.activeSpot?.normal_line)
    ) {
      this.computedEnd();
      this.computedRotateMatrix();
      this.rotateToTarget();
      // 切换靶点，新靶点旋转角度算不出来的情况下，移除小拍子
      if (!this.isValidComputedRotateMatrix()) {
        this.state.surfaceViewer.model.children = this.state.surfaceViewer.model.children.filter((child: any) => !child.name.includes('_preview'));
      }
    }
    if (prevProps.isTreating !== this.props.isTreating) {
      this.coilMatrixDisplay = [];
      this.headMatrixDisplay = [];
      this.loadBat();
      this.startRenderCoil();
      this.clearHorizontalBat();
      this.drawLine();
      this.clearDot();
    }
    if (prevProps.horizontal !== this.props.horizontal) {
      this.rotateBat(this.props.horizontal);
    }
    if (prevProps.horizontalDisable !== this.props.horizontalDisable) {
      this.setHorizontalBat();
    }
  }

  private getTreatConfig = async () => {
    const { horizontalDisable, isBatVerifiCation } = this.props;
    const [techSupportInfo, visiblePart, instructionPart] = await this.m200Api.getControlConfig();
    const { calInfo, accuracy } = visiblePart;
    if (isBatVerifiCation) {
      techSupportInfo.mainControl = false;
    }
    this.setState({
      mainControl: techSupportInfo.mainControl,
      visiblePart: techSupportInfo.mainControl ? visiblePart : initVisibleInfo,
      instructionPart: techSupportInfo.mainControl ? instructionPart : initInstructionInfo,
    });
    if (techSupportInfo.mainControl) {
      connSocket.setConfig(44, visiblePart.timeSlot * 1000);
    }
    connSocket.accuracy = {
      ...connSocket.accuracy,
      target_distance_threshold: calInfo.absolute,
      translation_distance_threshold: calInfo.translate,
      normal_angle_threshold: calInfo.angle,
      horizontal_rotation_angle_threshold: calInfo.rotate,
      accuracy_switch_rotation_angle: horizontalDisable ? 0 : 1,
      accuracy_switch_translation_distance: accuracy.translate ? 1 : 0,
      accuracy_switch_target_distance: accuracy.absolute ? 1 : 0,
      accuracy_switch_normal_angle: accuracy.angle ? 1 : 0,
    };
  };

  private setHorizontalBat = () => {
    if (this.props.isTreating || this.props.isEmg) return;
    if (this.props.horizontalDisable || !this.isValidComputedRotateMatrix()) {
      this.state.surfaceViewer.model.children = this.state.surfaceViewer.model.children.filter((child: any) => !child.name.includes('_preview'));
    } else {
      const batModal = this.state.surfaceViewer?.model?.children?.find((child: any) => child.name.includes('_preview'));
      if (!batModal) {
        this.state.surfaceViewer.model.add(bat.horizontalBat);
        this.rotateBat(this.props.horizontal, false);
      }
    }
    this.state.surfaceViewer.updated = true;
    connSocket.accuracy.accuracy_switch_rotation_angle = this.props.horizontalDisable ? 0 : 1;
  };

  private clearDot = () => {
    const { isTreating, spotList, activeId, isEmg } = this.props;
    const { surfaceViewer } = this.state;
    const m = new THREE.Matrix4();
    if (!this.computedRotateMatrixValue) return;
    // @ts-ignore
    m.set(...flatten(this.computedRotateMatrixValue));
    if (isTreating || isEmg) {
      spotList.forEach((spot, index) => {
        if (spot.id === activeId) {
          updateSurfaceCursorByWord(surfaceViewer, spot.surf_ras, parseInt((spot.color || colors[index]).slice(1), 16), `${spot.id}`, 3, false);
        } else {
          removeDot(surfaceViewer, `${spot.id}`);
        }
      });
    } else {
      spotList.forEach((spot, index) => {
        let size = 2;
        if (spot.id === activeId) {
          size = 3;
        }
        updateSurfaceCursorByWord(surfaceViewer, spot.surf_ras, parseInt((spot.color || colors[index]).slice(1), 16), `${spot.id}`, size, false);
      });
    }
    this.updateMatrix([...spotList.map(spot => `Dot${spot.id}`)], m);
  };

  private listenerErrors = (status: GetStatusCallbackParam) => {
    if (!status.cameraErrors || !this.props.isTreating) return;
    this.treatboardError = status.cameraErrors.treatboard !== 1;
    this.faceError = status.cameraErrors.facepointcloud !== 1 || status.cameraErrors.facedetection !== 1;
    if (!this.treatboardError) {
      const batModal = this.state.surfaceViewer?.model?.children?.find((child: any) => child.name.includes('_treat'));
      if (!batModal && this.props.isTreating) {
        this.state.surfaceViewer.model.add(bat.targetBat);
        this.state.surfaceViewer.updated = true;
      }
      this.loadBatSuccess = true;
    } else {
      if (this.state.surfaceViewer) {
        this.loadBatSuccess = false;
        const moveObj = this.state.surfaceViewer?.model?.children?.find((child: any) => child.name.includes('_treat'));
        moveObj?.position?.set?.(2500, 2500, 2500);
        const deleteObj = this.state.surfaceViewer?.model?.children?.find((child: any) => 'horizontalOffsetIndicator' === child.name);
        freeBufferList([deleteObj]);
        this.state.surfaceViewer.model.children = this.state.surfaceViewer.model.children.filter(
          (child: any) => !['horizontalOffsetIndicator'].includes(child.name)
        );
        this.state.surfaceViewer.updated = true;
      }
    }
  };

  private clearHorizontalBat = () => {
    if (!this.state.surfaceViewer) return;
    if (this.props.isTreating) {
      this.state.surfaceViewer.model.children = this.state.surfaceViewer.model.children.filter((child: any) => !child.name.includes('_preview'));
    } else {
      if (!this.props.horizontalDisable) {
        // eslint-disable-next-line @typescript-eslint/no-floating-promises
        this.loadHorizontalBat();
      }
    }
  };

  private startRenderCoil = () => {
    const { isTreating } = this.props;
    const { fps } = this.state;
    if (!isTreating) {
      this.coilMatrix = [];
      this.headMatrix = [];
      cancelAnimationFrame(this.animationCoil);

      return;
    }
    const now = new Date().getTime();
    const elapsed = now - this.last;
    if (elapsed > 1000 / fps) {
      this.last = now - (elapsed % 1000) / fps;
      if (this.isUpdating) return;
      this.isUpdating = true;
      try {
        this.updateBat();
        this.countBatTranslationDisplay();
        this.countBatTranslationCalc();
        this.countBatAngleDisplay();
        this.countBatAngleCalc();
        this.updateMinDistanceDisplay();
        this.updateMinDistanceCalc();
        this.computedHorizontalDisplay();
        this.computedHorizontalCalc();
      } catch (err: any) {
        sendRenderLog.error('startRenderCoil', err);
      }
      this.isUpdating = false;
    }
    this.animationCoil = requestAnimationFrame(this.startRenderCoil);
  };

  private calculatePoint = (arrPoint: number[], intK: number): number[] => {
    const intNorm: any = mathjs.norm(arrPoint);
    const arrReturn = mathjs.multiply(arrPoint, intK / intNorm);

    return (arrReturn as any).slice(0, 3);
  };

  // 靶点距离-展示矩阵
  private updateMinDistanceDisplay = () => {
    if (!this.coilMatrixDisplay.length || !this.headMatrixDisplay.length) return;
    const { minPoint, matrix } = this.getCountBatParam(this.headMatrixDisplay,this.coilMatrixDisplay);
    if (!minPoint || !matrix) return;
    const minArr = [minPoint.x, minPoint.y, minPoint.z, 1];
    const minMatrix = mathjs.multiply(matrix, minArr);
    const minDistance = Number.parseFloat(((minMatrix[0] ** 2 + minMatrix[1] ** 2 + minMatrix[2] ** 2) ** 0.5).toFixed(2));
    batInfoByMatrix.minDistanceDisplay = minDistance;
  };

  // 靶点距离-计算矩阵
  private updateMinDistanceCalc = () => {
    if (!this.coilMatrix.length || !this.headMatrix.length) return;
    const { minPoint, matrix } = this.getCountBatParam(this.headMatrix, this.coilMatrix);
    if (!minPoint || !matrix) return;
    const minArr = [minPoint.x, minPoint.y, minPoint.z, 1];
    const minMatrix = mathjs.multiply(matrix, minArr);
    const minDistance = Number.parseFloat(((minMatrix[0] ** 2 + minMatrix[1] ** 2 + minMatrix[2] ** 2) ** 0.5).toFixed(2));
    batInfoByMatrix.minDistanceCalc = minDistance;
  };

  private getEndPoint = (
    start: { x: number; y: number; z: number },
    end: { x: number; y: number; z: number }
  ): { x: number; y: number; z: number } => {
    const gapX = start.x - end.x;
    const gapY = start.y - end.y;
    const gapZ = start.z - end.z;
    const m = Math.sqrt(Math.pow(start.x - end.x, 2) + Math.pow(start.y - end.y, 2) + Math.pow(start.z - end.z, 2));
    const k = 40 / m;
    const x = end.x + gapX * k;
    const y = end.y + gapY * k;
    const z = end.z + gapZ * k;

    return { x, y, z };
  };

  // 计算水平旋转角的误差-计算矩阵
  private computedHorizontalCalc = () => {
    const { horizontal, spotList, activeId } = this.props;
    if (this.headMatrix.length && this.coilMatrix.length && this.transformMatrix) {
      const surfTarget = spotList.find(spot => spot.id === activeId);
      if (surfTarget && typeof horizontal === 'number' && horizontal >= -145 && horizontal <= 145) {
        const t = Math.PI / 2 + (horizontal / 180) * Math.PI;
        if (!this.computedRotateMatrixValue) return;
        const vecZ = [0, 0, 1, 1];
        const vecX = [0, -1, 0, 1];
        const vecX_R = [0, 1, 0, 1];
        const vecY = [1, 0, 0, 1];
        const vecO = [0, 0, 0, 1];
        let matrix = mathjs.multiply(mathjs.multiply(this.transformMatrix, this.headMatrix), this.coilMatrix);
        matrix = mathjs.multiply(this.computedRotateMatrixValue, matrix);
        const vecNowCoilDir = mathlab.sub(mathjs.multiply(matrix, vecO), mathjs.multiply(matrix, vecZ));
        const vecNowCoilDirX = mathlab.sub(mathjs.multiply(matrix, vecY), mathjs.multiply(matrix, vecO));
        const matrixCoilTrans = CalculateDirection(
          [vecNowCoilDir[0], vecNowCoilDir[1], vecNowCoilDir[2]],
          [matrix[0][3], matrix[1][3], matrix[2][3]],
          0,
          t
        );
        if (!matrixCoilTrans.length) return;
        let vecTransZ = mathlab.sub(mathlab.dot(matrixCoilTrans[0], vecX), mathlab.dot(matrixCoilTrans[0], vecO));
        if (surfTarget.surf_ras.x > 0) {
          vecTransZ = mathlab.sub(mathlab.dot(matrixCoilTrans[0], vecX_R), mathlab.dot(matrixCoilTrans[0], vecO));
        }
        const rotate = mathjs.acos(mathlab.dot(vecTransZ, vecNowCoilDirX) / mathlab.norm2(vecTransZ) / mathlab.norm2(vecNowCoilDirX));
        batInfoByMatrix.horizontalCalc = (Number(rotate) / Math.PI) * 180;
      }
    }
  };

  // 计算水平旋转角的误差-展示矩阵
  private computedHorizontalDisplay = () => {
    const { surfaceViewer, visiblePart } = this.state;
    const { horizontal, spotList, activeId, horizontalDisable } = this.props;
    if (this.headMatrixDisplay.length && this.coilMatrixDisplay.length && this.transformMatrix) {
      const surfTarget = spotList.find(spot => spot.id === activeId);
      if (surfTarget && typeof horizontal === 'number' && horizontal >= -145 && horizontal <= 145) {
        const t = Math.PI / 2 + (horizontal / 180) * Math.PI;
        if (!this.computedRotateMatrixValue) return;
        const vecZ = [0, 0, 1, 1];
        const vecX = [0, -1, 0, 1];
        const vecX_R = [0, 1, 0, 1];
        const vecY = [1, 0, 0, 1];
        const vecO = [0, 0, 0, 1];
        let matrix = mathjs.multiply(mathjs.multiply(this.transformMatrix, this.headMatrixDisplay), this.coilMatrixDisplay);
        matrix = mathjs.multiply(this.computedRotateMatrixValue, matrix);
        const vecNowCoilDir = mathlab.sub(mathjs.multiply(matrix, vecO), mathjs.multiply(matrix, vecZ));
        const vecNowCoilDirX = mathlab.sub(mathjs.multiply(matrix, vecY), mathjs.multiply(matrix, vecO));
        const vecXLine1 = [1, 0, -8.2, 1];
        const vecXLine2 = [0, 0, -8.2, 1];
        const line1 = mathjs.multiply(matrix, vecXLine1);
        const line2 = mathjs.multiply(matrix, vecXLine2);
        const matrixCoilTrans = CalculateDirection(
          [vecNowCoilDir[0], vecNowCoilDir[1], vecNowCoilDir[2]],
          [matrix[0][3], matrix[1][3], matrix[2][3]],
          0,
          t
        );
        const moveObj = surfaceViewer.model.children.filter((obj: any) => obj.name === 'horizontalOffsetIndicator');
        freeBufferList(moveObj);
        surfaceViewer.model.children = surfaceViewer.model.children.filter((obj: any) => obj.name !== 'horizontalOffsetIndicator');
        if (!matrixCoilTrans.length) return;
        let vecTransZ = mathlab.sub(mathlab.dot(matrixCoilTrans[0], vecX), mathlab.dot(matrixCoilTrans[0], vecO));
        let vecXLine3 = [0, -1, -8.2, 1];
        let vecXLine4 = [0, 0, -8.2, 1];
        if (surfTarget.surf_ras.x > 0) {
          vecXLine3 = [0, 1, -8.2, 1];
          vecTransZ = mathlab.sub(mathlab.dot(matrixCoilTrans[0], vecX_R), mathlab.dot(matrixCoilTrans[0], vecO));
        }
        const line3 = mathlab.dot(matrixCoilTrans[0], vecXLine3);
        const line4 = mathlab.dot(matrixCoilTrans[0], vecXLine4);
        const rotate = mathjs.acos(mathlab.dot(vecTransZ, vecNowCoilDirX) / mathlab.norm2(vecTransZ) / mathlab.norm2(vecNowCoilDirX));
        batInfoByMatrix.horizontalDisplay = (Number(rotate) / Math.PI) * 180;
        const diff = mathjs.norm([
          this.prevLineVol[0] - matrixCoilTrans[0][0][3],
          this.prevLineVol[1] - matrixCoilTrans[0][1][3],
          this.prevLineVol[2] - matrixCoilTrans[0][2][3],
        ]);
        if (Math.abs(diff as number) < this.state.diff) {
          return;
        }
        this.prevLineVol = [matrixCoilTrans[0][0][3], matrixCoilTrans[0][1][3], matrixCoilTrans[0][2][3]];
        if (this.loadBatSuccess && !this.treatboardError && !horizontalDisable && !this.faceError && visiblePart.calInfo.rotate < batInfoByMatrix.horizontalDisplay) {
          const start1 = { x: line1[0], y: line1[1], z: line1[2] };
          const end1 = this.getEndPoint(start1, { x: line2[0], y: line2[1], z: line2[2] });
          const start2 = { x: line3[0], y: line3[1], z: line3[2] };
          const end2 = this.getEndPoint(start2, { x: line4[0], y: line4[1], z: line4[2] });

          drawHorizontalOffsetIndicator(
            {
              line1StartPoint: start1,
              line1EndPoint: end1,
              line2StartPoint: start2,
              line2EndPoint: end2,
              line1Color: 0x3662ec,
              line2Color: 0x3662ec,
              arcColor: 0x0028A4,
              groupName: 'horizontalOffsetIndicator',
            },
            surfaceViewer
          );
        }
      }
    }
  };

  private getCountBatParam = (headMatrix: number[],coilMatrix: number[]) => {
    const { isTreating, spotList, activeId } = this.props;
    const { tkras2ras } = this.state;
    if (!isTreating || !tkras2ras) return {};
    const matrix = mathjs.multiply(headMatrix, coilMatrix);
    const target = spotList.find(spot => spot.id === activeId);
    const minPoint = target?.normal_line;
    const vol_ras = target?.vol_ras;
    if (!minPoint || !vol_ras) return {};

    return { minPoint, vol_ras, matrix: mathjs.inv(matrix) };
  };

  // 法线夹角-使用计算矩阵
  private countBatAngleCalc = () => {
    if (!this.headMatrix.length || !this.coilMatrix.length || !this.transformMatrix) return;
    let matrix = mathjs.multiply(mathjs.inv(this.coilMatrix), mathjs.inv(this.headMatrix));
    const { minPoint, vol_ras } = this.getCountBatParam(this.headMatrix, this.coilMatrix);
    if (!minPoint || !vol_ras || !matrix) return;
    const minArr = [minPoint.x, minPoint.y, minPoint.z, 1];
    const targetArr = [vol_ras.x, vol_ras.y, vol_ras.z, 1];
    const minMatrix = mathjs.multiply(matrix, minArr);
    const targetMatrix = mathjs.multiply(matrix, targetArr);
    const arrPoint = [minMatrix[0] - targetMatrix[0], minMatrix[1] - targetMatrix[1], minMatrix[2] - targetMatrix[2]];
    const [x, y, z] = this.calculatePoint(arrPoint, 150);
    const xzVal = (x ** 2 + y ** 2) ** 0.5;
    const angle = Number.parseFloat(`${90 + (Math.atan2(z, xzVal) * 180) / Math.PI}`);
    const horizontalAngle = (Math.atan2(y, x) * 180) / Math.PI;
    batInfoByMatrix.batAngleCalc = { angle, horizontalAngle };
  };

  // 法线夹角-使用展示矩阵
  private countBatAngleDisplay = () => {
    if (!this.headMatrixDisplay.length || !this.coilMatrixDisplay.length || !this.transformMatrix) return;
    let matrix = mathjs.multiply(mathjs.inv(this.coilMatrixDisplay), mathjs.inv(this.headMatrixDisplay));
    const { minPoint, vol_ras } = this.getCountBatParam(this.headMatrixDisplay,this.coilMatrixDisplay);
    if (!minPoint || !vol_ras || !matrix) return;
    const minArr = [minPoint.x, minPoint.y, minPoint.z, 1];
    const targetArr = [vol_ras.x, vol_ras.y, vol_ras.z, 1];
    const minMatrix = mathjs.multiply(matrix, minArr);
    const targetMatrix = mathjs.multiply(matrix, targetArr);
    const arrPoint = [minMatrix[0] - targetMatrix[0], minMatrix[1] - targetMatrix[1], minMatrix[2] - targetMatrix[2]];
    const [x, y, z] = this.calculatePoint(arrPoint, 150);
    const xzVal = (x ** 2 + y ** 2) ** 0.5;
    const angle = Number.parseFloat(`${90 + (Math.atan2(z, xzVal) * 180) / Math.PI}`);
    const horizontalAngle = (Math.atan2(y, x) * 180) / Math.PI;
    batInfoByMatrix.batAngleDisplay = { angle, horizontalAngle };
  };

  // 平移距离-使用计算矩阵
  private countBatTranslationCalc = () => {
    if (!this.headMatrix.length || !this.coilMatrix.length || !this.transformMatrix) return;
    let { minPoint, vol_ras } = this.getCountBatParam(this.headMatrix,this.coilMatrix);
    let matrix = mathjs.multiply(mathjs.inv(this.coilMatrix), mathjs.inv(this.headMatrix));
    if (!minPoint || !vol_ras || !matrix) return;
    const tempMatrix = flatten(matrix);
    const nextMatrix: any = [tempMatrix.slice(0, 4), tempMatrix.slice(4, 8), tempMatrix.slice(8, 12), tempMatrix.slice(12, 16)];
    const resTarget = mathjs.multiply(nextMatrix, [vol_ras.x, vol_ras.y, vol_ras.z, 1]);
    const resPoint = mathjs.multiply(nextMatrix, [minPoint.x, minPoint.y, minPoint.z, 1]);
    const pointMatrix = [resTarget[0] - resPoint[0], resTarget[1] - resPoint[1], resTarget[2] - resPoint[2]];

    const [pointX, pointY, pointZ] = pointMatrix;
    const x = +resTarget[0] + (pointZ ? (-1 * resTarget[2] * pointX) / pointZ : 0);
    const y = +resTarget[1] + (pointZ ? (-1 * resTarget[2] * pointY) / pointZ : 0);

    const distance = (x ** 2 + y ** 2) ** 0.5;
    const angle = (Math.atan2(y, x) * 180) / Math.PI;
    batInfoByMatrix.batTranslationCalc = { angle, distance, x, z: y };
  };

  // 平移距离-使用展示矩阵
  private countBatTranslationDisplay = () => {
    if (!this.headMatrixDisplay.length || !this.coilMatrixDisplay.length || !this.transformMatrix) return;
    let { minPoint, vol_ras } = this.getCountBatParam(this.headMatrixDisplay,this.coilMatrixDisplay);
    let matrix = mathjs.multiply(mathjs.inv(this.coilMatrixDisplay), mathjs.inv(this.headMatrixDisplay));
    if (!minPoint || !vol_ras || !matrix) return;
    const tempMatrix = flatten(matrix);
    const nextMatrix: any = [tempMatrix.slice(0, 4), tempMatrix.slice(4, 8), tempMatrix.slice(8, 12), tempMatrix.slice(12, 16)];
    const resTarget = mathjs.multiply(nextMatrix, [vol_ras.x, vol_ras.y, vol_ras.z, 1]);
    const resPoint = mathjs.multiply(nextMatrix, [minPoint.x, minPoint.y, minPoint.z, 1]);
    const pointMatrix = [resTarget[0] - resPoint[0], resTarget[1] - resPoint[1], resTarget[2] - resPoint[2]];

    const [pointX, pointY, pointZ] = pointMatrix;
    const x = +resTarget[0] + (pointZ ? (-1 * resTarget[2] * pointX) / pointZ : 0);
    const y = +resTarget[1] + (pointZ ? (-1 * resTarget[2] * pointY) / pointZ : 0);

    const distance = (x ** 2 + y ** 2) ** 0.5;
    const angle = (Math.atan2(y, x) * 180) / Math.PI;
    batInfoByMatrix.batTranslationDisplay = { angle, distance, x, z: y };
  };

  private setAngle = (batObj: any) => {
    const { instructionPart, visiblePart, mainControl } = this.state;
    const batAngleObjs = batObj.children.filter((obj: any) => obj.name === 'batAngle');
    freeBufferList(batAngleObjs);
    batObj.children = batObj.children.filter((obj: any) => obj.name !== 'batAngle');
    const { angle, horizontalAngle } = batInfoByMatrix.batAngleDisplay;
    if (!isNumber(angle) || !isNumber(horizontalAngle)) return batObj;
    const canFlash = angle <= 150;
    const conditions = [!this.faceError, instructionPart?.instruction.angleTip, angle > visiblePart.calInfo.angle];
    if (!mainControl) {
      conditions.push(canFlash);
    }
    if (conditions.every(condition => condition)) {
      const batAngleObj = arrowGroup('batAngle',angle, horizontalAngle, 10);
      batAngleObj.name = 'batAngle';
      batAngleObj.position.set(0, 0, -8.2);
      batAngleObj.parent = batObj;
      batObj.children.push(batAngleObj);
    }

    return batObj;
  };

  private judgeValue = (num: number, value?: number) => {
    if (!isNumber(value)) return false;

    return Math.ceil(value) <= num;
  };

  private setTranslation = (batObj: any) => {
    const { horizontalDisable } = this.props;
    const { visiblePart, instructionPart, mainControl } = this.state;
    const batTranslationName = 'batTranslation';
    const safeDistance = visiblePart.calInfo.translate;
    const translationObjs = batObj.children.filter((obj: any) => obj.name === batTranslationName);
    freeBufferList(translationObjs);
    batObj.children = batObj.children.filter((obj: any) => obj.name !== batTranslationName);
    if (this.faceError) return batObj;
    const { distance, angle, x, z: y } = batInfoByMatrix.batTranslationDisplay;
    if (!isNumber(distance) || !isNumber(angle) || !isNumber(x) || !isNumber(y)) return batObj;
    const conditions = [instructionPart?.instruction.translateTip, distance > safeDistance];
    if (!mainControl) {
      conditions.push(distance <= 150);
    }
    if (conditions.every(condition => condition)) {
      const batTranslationObj = arrowGroup('batTranslation',0, angle, distance);
      batTranslationObj.name = batTranslationName;
      batTranslationObj.position.set(0, 0, 5.1);
      batTranslationObj.parent = batObj;
      batObj.children.push(batTranslationObj);
    }
    // 所有条件都满足才变绿
    const showGreenArg = [
      this.judgeValue(visiblePart.calInfo.angle,batInfoByMatrix.batAngleCalc.angle),
      this.judgeValue(visiblePart.calInfo.translate, batInfoByMatrix.batTranslationCalc.distance),
      this.judgeValue(visiblePart.calInfo.rotate, batInfoByMatrix.horizontalCalc),
      this.judgeValue(visiblePart.calInfo.absolute, batInfoByMatrix.minDistanceCalc),
    ];
    if (horizontalDisable) {
      showGreenArg[2] = true;
    }
    // const showGreen = distance < safeDistance;
    const showGreen = showGreenArg.every(condition => !!condition);
    const color = !showGreen ? 0xd36f66 : 0x8cab39;
    const geometry = new THREE.SphereGeometry(4, 32, 32);
    const material = new THREE.MeshBasicMaterial({
      color,
    });
    const sphere = new THREE.Mesh(geometry, material);
    sphere.position.set(x, y, -8.2);
    sphere.name = batTranslationName;
    sphere.parent = batObj;
    if (isNumber(batInfoByMatrix.batTranslationCalc.distance) && batInfoByMatrix.batTranslationCalc.distance < 150) {
      batObj.children.push(sphere);
    }

    return batObj;
  };

  private setAngleAndTranslation = (batObj: any) => {
    batObj = this.setAngle(batObj);
    batObj = this.setTranslation(batObj);

    return batObj;
  };

  private process = (matrix: any) => {
    const vol = [matrix[0][3], matrix[1][3], matrix[2][3]];
    movingAverage.add(vol);

    return movingAverage.computedAverage();
  };

  private updateBat = () => {
    const { surfaceViewer } = this.state;
    if (!this.coilMatrixDisplay.length || !this.headMatrixDisplay.length || !this.transformMatrix || this.treatboardError) return;
    const m = new THREE.Matrix4();
    if (!this.computedRotateMatrixValue) return;
    let matrix = mathjs.multiply(mathjs.multiply(this.transformMatrix, this.headMatrixDisplay), this.coilMatrixDisplay);
    matrix = mathjs.multiply(this.computedRotateMatrixValue, matrix);
    const average = this.process(matrix);
    const diff = mathjs.norm([average[0] - matrix[0][3], average[1] - matrix[1][3], average[2] - matrix[2][3]]);
    if (Math.abs(diff as number) < this.state.diff) {
      return;
    }
    // @ts-ignore
    m.set(...flatten(matrix));
    let batObj = surfaceViewer.model.children.find((item: any) => item.name.includes('_treat'));
    const horizontalLine = surfaceViewer.model.children.find((obj: any) => obj.name === 'horizontalOffsetIndicator');
    if (!batObj) return;
    batObj.position.set(0, 0, 0);
    batObj.rotation.set(0, 0, 0);
    batObj.scale.set(1, 1, 1);
    batObj = this.setAngleAndTranslation(batObj);
    batObj.applyMatrix4(m);
    batObj.updateMatrix();
    if (horizontalLine) {
      horizontalLine.applyMatrix4(m);
      horizontalLine.updateMatrix();
    }
    surfaceViewer.updated = true;
  };

  private getCoil = (value: any) => {
    if (!isIdentityMatrix(value.head)) {
      this.headMatrix = value.head;
    }
    if (!isIdentityMatrix(value.coil)) {
      this.coilMatrix = value.coil;
    }
    if (!isIdentityMatrix(value.head_display)) {
      this.headMatrixDisplay = value.head_display;
    }
    if (!isIdentityMatrix(value.coil_display)) {
      this.coilMatrixDisplay = value.coil_display;
    }
    sendTreatMatrixLog.debug(JSON.stringify(value));
  };

  private getPrePath = async () => {
    const prePath = (await this.m200Api.getConfig({ group_name: 'init', name: 'storagePath' }))?.find(item => item.name === 'storagePath')?.value;
    if (!prePath) return '';

    return prePath;
  };

  private setVolumeVizArray = async () => {
    const { planInfo } = this.props;
    if (!planInfo) return;
    const prePath = await this.getPrePath();
    const tkPath = planInfo.plan_file_model_list?.find(item => item.name === 'tkras2ras_matrices.json')?.relative_path;
    const tkras = JSON.parse(await window.fileAPI.getFileJson(`${prePath}/${tkPath}`));
    const vizArray = ['T1.mgz', 'scalp_mask.nii.gz'].map(fileName => {
      const path = planInfo.plan_file_model_list?.find(item => item.name === fileName)?.relative_path;

      return `${prePath}/${path}`;
    });
    this.transformMatrix = mathjs.multiply(tkras.vox2ras_tkr, tkras.vox2ras_inv);
    connSocket.sur2vol = mathjs.inv(this.transformMatrix);
    this.setState({ vizArray, tkras2ras: tkras });
  };

  private bindUpdateVolumeViewer = async (viewer: any) => {
    viewer.showMark = true;
    this.setState({ volumeViewer: viewer });
    this.props.setVolumeViewer(viewer);
    this.props?.setVolumeLoading?.(false);
  };

  private getVolumeScalpIndex = (index: number) => {
    this.props.getVolumeScalpIndex(index);
  };

  // 计算绘制法线的端点
  private computedEnd = () => {
    const { activeId, spotList } = this.props;
    const { tkras2ras } = this.state;
    const surfTarget = spotList.find(spot => spot.id === activeId);
    const minPoint = surfTarget?.normal_line;
    const end = volumeXYZtoSurface(minPoint, tkras2ras);
    this.endTarget = end;
  };

  private computedBatPos = (horizontal: number) => {
    const { activeId, spotList } = this.props;
    const surfTarget = spotList.find(spot => spot.id === activeId);
    const end = this.endTarget;
    if (!end || !surfTarget) return;
    const vector = [end?.x - surfTarget.surf_ras.x, end?.y - surfTarget.surf_ras.y, end?.z - surfTarget.surf_ras.z];
    const t = Math.PI / 2 + (horizontal / 180) * Math.PI;
    const pos: any[] = CalculateDirection(vector, [end?.x, end?.y, end?.z], 0, t);

    return pos;
  };

  private computedRotateMatrix = () => {
    const { activeId, spotList } = this.props;
    if (!activeId || !spotList) return;
    const surfTarget = spotList.find(spot => spot.id === activeId);
    const end = this.endTarget;
    if (!end || !surfTarget) return;
    const vector = [end?.x - surfTarget.surf_ras.x, end?.y - surfTarget.surf_ras.y, end?.z - surfTarget.surf_ras.z];
    const t = surfTarget.surf_ras.x > 0 ? Math.PI / 2 : -Math.PI / 2;
    const pos: any[] = CalculateDirection(vector, [0, 0, 0], 0, t);
    if (!pos.length) {
      // #M200-1570 在某些特定靶点无法不显示距离和角度示意图，很难找到靶点所在位置进行治疗
      this.computedRotateMatrixValue = [
        [1, 0, 0, 0],
        [0, 1, 0, 0],
        [0, 0, 1, 0],
        [0, 0, 0, 1],
      ];

      return;
    }

    let matrixT: number[][] = [
      [-1, 0, 0, 0],
      [0, 1, 0, 0],
      [0, 0, -1, 0],
      [0, 0, 0, 1],
    ];
    let ms = mathlab.dot(mathlab.dot(matrixT, mathlab.inv(pos[0])), [end.x, end.y, end.z, 1]);
    let matrixT1: number[][] = [
      [1, 0, 0, -ms[0]],
      [0, 1, 0, -ms[1]],
      [0, 0, 1, 0],
      [0, 0, 0, 1],
    ];
    this.computedRotateMatrixValue = mathlab.dot(matrixT1, mathlab.dot(matrixT, mathlab.inv(pos[0])));
  };

  private updateMatrix = (subjectNames: string[], matrix4: THREE.Matrix4) => {
    const { surfaceViewer } = this.state;
    subjectNames.forEach(objName => {
      let obj3d = surfaceViewer.model.children.filter((item: any) => item.name.includes(objName));
      if (!obj3d.length) return;
      obj3d.forEach((obj: any) => {
        obj.applyMatrix4(matrix4);
        obj.updateMatrix();
      });
    });
  };

  /**
   * 判断两个矩阵是否相等
   */
  private matrixIsEqual = (matrix1: number[][], matrix2: number[][]) => {
    if (matrix1.length !== matrix2.length) return false;
    for (let i = 0; i < matrix1.length; i++) {
      if (matrix1[i].length !== matrix2[i].length) return false;

      for (let j = 0; j < matrix1[i].length; j++) {
        if (matrix1[i][j] !== matrix2[i][j]) return false;
      }
    }

    return true;
  };

  /**
   * 判断旋转矩阵是否有效
   */
  private isValidComputedRotateMatrix = () => {
    if (this.matrixIsEqual(this.computedRotateMatrixValue, [
      [1, 0, 0, 0],
      [0, 1, 0, 0],
      [0, 0, 1, 0],
      [0, 0, 0, 1],
    ])) return false;

    return true;
  };

  private rotateBat = (horizontal?: number, init?: boolean) => {
    if (!this.isValidComputedRotateMatrix()) return;

    if (typeof horizontal === 'number' && horizontal <= 145 && horizontal >= -145) {
      const { surfaceViewer } = this.state;
      const pos = this.computedBatPos(horizontal);
      if (!pos?.length) return;
      // this.amfObject
      const horizontalBat = surfaceViewer.model.children.find((item: any) => item.name.includes('_preview'));
      if (!horizontalBat) return;
      horizontalBat.position.set(0, 0, 0);
      horizontalBat.rotation.set(0, 0, 0);
      const batM = new THREE.Matrix4();
      // @ts-ignore
      batM.set(...flatten(pos[0]));
      this.updateMatrix(['_preview'], batM);
      if (!init) {
        const m = new THREE.Matrix4();
        // @ts-ignore
        m.set(...flatten(this.computedRotateMatrixValue));
        this.updateMatrix(['_preview'], m);
      }

      surfaceViewer.updated = true;
    }
  };

  private drawLine = () => {
    const { surfaceViewer } = this.state;
    const { spotList, activeId, isTreating } = this.props;
    if (!isTreating) {
      surfaceViewer.model.children = surfaceViewer.model.children.filter((child: any) => !['horizontalOffsetIndicator', 'Line'].includes(child.name));
    } else {
      const surfaceTarget = spotList.find(spot => spot.id === activeId);
      if (!surfaceTarget || !surfaceViewer || !this.endTarget) return;
      surfaceViewer.model.children = surfaceViewer.model.children.filter((obj: any) => obj.name !== 'Line');
      surfaceViewer.drawTrajectory(surfaceTarget.surf_ras, this.endTarget, {
        color: 0x32689a,
      });
      const m = new THREE.Matrix4();
      // const v = this.computedRotateMatrix();
      if (!this.computedRotateMatrixValue) return;
      // @ts-ignore
      m.set(...flatten(this.computedRotateMatrixValue));
      this.updateMatrix(['Line'], m);
    }
  };

  // 将靶点旋转于正对头部
  private rotateToTarget = () => { // NOSONAR
    const { activeId, spotList, horizontal, isTreating, isEmg } = this.props;
    const { surfaceViewer, tkras2ras } = this.state;
    const surfaceTarget = spotList.find(spot => spot.id === activeId);
    const minPoint = surfaceTarget?.normal_line;
    if (!minPoint || !tkras2ras || !surfaceTarget || !surfaceViewer) return;
    surfaceViewer.resetView();
    const m = new THREE.Matrix4();
    if (!this.computedRotateMatrixValue) return;
    // @ts-ignore
    m.set(...flatten(this.computedRotateMatrixValue));
    // 旋转模型 -> 清除法线 -> 绘制法线 -> 绘制靶点 -> 更新法线和靶点 -> 循环
    queueMicrotask(async () => {
      this.updateMatrix(['pial_gii_1', 'scalp_mask_1'], m);
      await this.loadHorizontalBat();
      if (!isTreating) {
        surfaceViewer.model.children = surfaceViewer.model.children.filter((obj: any) => obj.name !== 'Line');
      }
      if (isEmg) {
        spotList.forEach((spot, index) => {
          if (spot.id !== activeId) return;
          updateSurfaceCursorByWord(surfaceViewer, spot.surf_ras, parseInt((spot.color || colors[index]).slice(1), 16), `${spot.id}`, 3, false);
        });
      } else {
        let size;
        spotList.forEach((spot, index) => {
          if (spot.id === activeId) {
            size = 3;
          } else {
            size = 2;
          }
          updateSurfaceCursorByWord(surfaceViewer, spot.surf_ras, parseInt((spot.color || colors[index]).slice(1), 16), `${spot.id}`, size, false);
        });
      }
      this.updateMatrix([...spotList.map(spot => `Dot${spot.id}`), 'Line'], m);
      if (!isTreating && horizontal >= -145 && horizontal <= 145) {
        this.rotateBat(horizontal, true);
        this.updateMatrix(['_preview'], m);
      }
      surfaceViewer.zoom = 0.85;
      surfaceViewer.updated = true;
    });
  };

  private loadHorizontalBat = async () => {
    const { isTreating, isEmg, horizontalDisable } = this.props;
    const { surfaceViewer } = this.state;
    if (isEmg || !this.isValidComputedRotateMatrix() || horizontalDisable) return;
    if (isTreating) {
      surfaceViewer.model.children = surfaceViewer.model.children.filter((child: any) => !child.name.includes('_preview'));

      return Promise.resolve();
    }

    return new Promise<void>(resolve => {
      surfaceViewer.model.add(bat.horizontalBat);
      resolve();
    });
  };

  private loadBat = () => {
    const { isTreating, isEmg } = this.props;
    const { surfaceViewer } = this.state;
    if (isEmg && !this.treatboardError && !this.faceError) {
      surfaceViewer.model.add(bat.targetBat);
      this.loadBatSuccess = true;
      surfaceViewer.updated = true;

      return;
    }
    if (!isTreating || this.treatboardError || this.faceError) {
      bat.targetBat.position.set(2500, 2500, 2500);
      const moveObj = surfaceViewer?.model?.children?.filter((child: any) => child.name.includes('_treat'));
      freeBufferList(moveObj);
      surfaceViewer.model.children = surfaceViewer?.model?.children?.filter((child: any) => !child.name.includes('_treat'));
    } else {
      const batModel = surfaceViewer?.model?.children?.find((child: any) => child.name.includes('_treat'));
      if (!batModel) {
        surfaceViewer.model.add(bat.targetBat);
      }
      this.loadBatSuccess = true;
    }
    surfaceViewer.updated = true;
  };

  private onClickChangePoint = (coords: Coordinate) => {
    if (this.props.onClickChangePoint) {
      this.props.onClickChangePoint(coords);
    }
  };

  public render() {
    const { vizArray } = this.state;
    const { planInfo, isBatVerifiCation = false } = this.props;

    return (
      <div className="surface_spot_view">
        <div className="patient_name">{isBatVerifiCation ? `患者ID：${planInfo?.subject_model.code}` : `患者姓名：${planInfo?.subject_model.name}`}</div>
        <div className={classNames(this.props.immediateViewClass,'immediate_view_box')}>
          {this.renderImmediateView()}
        </div>
        <div className="surface_box">
          {this.props.pialUrl && (
            <Surface
              setSurfaceViewer={viewer => this.setState({ surfaceViewer: viewer })}
              colorMap={'white.txt'}
              pialUrl={this.props.pialUrl}
              scalpMaskUrl={this.props.scalpMaskUrl}
              volumeFiles={this.props.volumeFiles}
              onClickChangePoint={this.onClickChangePoint}
              pialOpacity={100}
              scalpOpacity={20}
              loadComplete={this.surfaceLoadComplete}
            />
          )}
        </div>
        {vizArray.length > 0 && (
          <PreviewVolume
            getVolumeScalpIndex={this.getVolumeScalpIndex}
            vizArray={vizArray}
            updateViewer={this.bindUpdateVolumeViewer}
            onClickChangePoint={() => 1}
            style={{ display: 'none' }}
          />
        )}
        <div>
          {(this.props.isBatVerifiCation || !this.props.isTreating) && this.props.SpotList}
        </div>
        {this.props.children}
      </div>
    );
  }

  private renderImmediateView = () => {
    const { visiblePart, instructionPart, mainControl } = this.state;
    const { horizontalDisable, horizontal, helper, isBatVerifiCation } = this.props;
    if (this.props.isEmg || this.props.isTreating) {
      return <ImmediateView
        coilPanelMissingCallback={this.props.coilPanelMissingCallback}
        horizontalDisable={horizontalDisable}
        horizontal={horizontal}
        visiblePart={visiblePart}
        instructionPart={instructionPart}
        helper={helper}
        isBatVerifiCation={isBatVerifiCation}
        mainControl={mainControl}
        isEmg={this.props.isEmg || false}
      />;
    }

    return <React.Fragment />;
  };

  private surfaceLoadComplete = () => {
    if (!this.state.surfaceViewer) return;
    this.loadBat();
    this.props.setLoading?.({ surfaceLoading: false });
    this.computedEnd();
    if (this.props.isEmg) {
      this.drawLine();
      this.clearDot();
    }
    this.computedRotateMatrix();
    this.rotateToTarget();
    this.props.surfaceLoadComplete?.();
  };
}
