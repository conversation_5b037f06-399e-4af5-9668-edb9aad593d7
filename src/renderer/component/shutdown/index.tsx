// @flow
import * as React from 'react';
import { IntlPropType } from '@/common/types/propTypes';
import { injectIntl } from 'react-intl';
import { NgIcon } from '@/renderer/uiComponent/NgIcon';
import { Shutdown, WarnMessage } from '@/renderer/uiComponent/SvgGather';
import NgModal from '@/renderer/uiComponent/NgModal';
import styles from '@/renderer/uiComponent/NgModal/index.module.less';
import NgButtonText from '@/renderer/uiComponent/NgButtonText';
import NgButton from '@/renderer/uiComponent/NgButton';
import Icon from '@ant-design/icons';
import { ShutdownStatusEnum } from '../systemErrorModel/errorModel';
import { safetyBtnStatus, safetyBtnStatusEnum, useSafetyBtnStatus } from '../../recoil/safetyBtn';
import { useRecoilValue, useSetRecoilState } from 'recoil';
type Props = IntlPropType;
export const InnerShutDown = (props: Props) => {
  const { intl } = props;
  const [showEmergencyModel, setShowEmergencyModel] = React.useState(false);
  const [shutdownStatus, setShutdownStatus] = React.useState<ShutdownStatusEnum>(ShutdownStatusEnum.init);
  const timerRef = React.useRef<NodeJS.Timeout | undefined>();
  const safetyBtnStatusValue = useRecoilValue(safetyBtnStatus);
  const setSafetyBtnStatus = useSetRecoilState(useSafetyBtnStatus);
  const sendShutdown = () => {
    setShutdownStatus(ShutdownStatusEnum.loading);
    timerRef.current = setTimeout(() => {
      setShutdownStatus(ShutdownStatusEnum.error);
      setSafetyBtnStatus(safetyBtnStatusEnum.error);
    }, 60000);
    window.systemAPI.shutdown();
  };

  React.useEffect(() => {
    setShutdownStatus(ShutdownStatusEnum.init);

    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, [showEmergencyModel]);

  return (
    <div style={{ display: 'inline-flex', position: 'relative' }}>
      <NgIcon id="shutdown-icon" iconSvg={Shutdown} onClick={() => setShowEmergencyModel(true)} fontSize={28} />
      <NgModal
        open={showEmergencyModel && safetyBtnStatusValue === safetyBtnStatusEnum.normal}
        closable={false}
        styles={{ body: {padding: '12px 0'} }}
        width={460}
        footer={
          <>
            <div className={styles.footer}>
              <NgButtonText style={{ display: 'inline-block' }} onClick={() => setShowEmergencyModel(false)}>
                取消
              </NgButtonText>
              <NgButton loading={shutdownStatus === ShutdownStatusEnum.loading} onClick={() => sendShutdown()}>
                关机
              </NgButton>
            </div>
          </>
        }
      >
        <div style={{ display: 'flex', alignItems: 'center', color: '#E8E8EA', fontSize: 16 }}>
          <Icon component={WarnMessage} style={{ marginRight: 12, width: 22, height: 22, fontSize: 18 }} rev="" />
          {intl.formatMessage({ id: '您确定现在关机么?' })}
        </div>
      </NgModal>
    </div>
  );
};

export const ShutDown = injectIntl(InnerShutDown);
