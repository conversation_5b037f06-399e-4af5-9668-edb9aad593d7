import React from 'react';
import NgDrawer from '@/renderer/uiComponent/NgDrawer';
import styles from './noticeDrawer.module.less';
import { useRecoilState } from 'recoil';
import NgEmpty from '@/renderer/uiComponent/NgEmpty';
import { UserSessionProps, withUserSession } from '@/renderer/hocComponent/withUserSession';
import moment from 'moment';
import { sortBy } from 'lodash';
import classnames from 'classnames';
import { faultAtom } from '../../../recoil/fault';
import { FaultLevelEnum, FaultMapItemType } from '../../../../common/systemFault/type';

type Props = {
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
};

const NoticeItem = (
  props: FaultMapItemType & {
    itemKey: string;
  }
) => {
  return (
    <div className={styles.noticeItem}>
      <p>
        {props.content}：{props.itemKey}
      </p>
      <span>{moment(props.createAt).format('YYYY.MM.DD HH:mm:ss')}</span>
    </div>
  );
};
const NoticeDrawer = (props: Props & UserSessionProps) => {
  const [fault] = useRecoilState(faultAtom);

  return (
    <div className={styles.container}>
      <NgDrawer
        rootClassName={styles.noticeRoot}
        title={
          <div className={styles.noticeTitle}>
            <p>通知</p>
            {fault[FaultLevelEnum.warning].length > 0 && <span>{fault[FaultLevelEnum.warning].length}</span>}
          </div>
        }
        {...props}
        open={props.open}
        onClose={() => props.setOpen(false)}
      >
        <div
          className={classnames(styles.drawerContainer, {
            //  @ts-ignore
            [styles.hideScroll]: fault[FaultLevelEnum.warning].length <= 11,
          })}
        >
          {fault[FaultLevelEnum.warning].length <= 0 ? (
            <div className={styles.empty}>
              <NgEmpty emptyType={'noNotice'} />
            </div>
          ) : (
            <div className={styles.warningContainer}>
              <p className={styles.contact}>请联系技术支持，修复以下问题，避免影响设备使用。</p>
              <div className={classnames(styles.list, 'hide-list')}>
                {sortBy(fault[FaultLevelEnum.warning], obj => new Date(obj.createAt).getTime())
                  .reverse()
                  .map(warning => {
                    return (
                      <div key={warning.key}>
                        <NoticeItem {...warning} itemKey={warning.key} />
                      </div>
                    );
                  })}
              </div>
            </div>
          )}
        </div>
      </NgDrawer>
    </div>
  );
};
export default withUserSession(NoticeDrawer);
