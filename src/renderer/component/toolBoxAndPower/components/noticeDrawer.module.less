@import '@/renderer/static/style/baseColor.module.less';

.container {
  position: relative;
}

.noticeRoot {
  .hideScroll {
    :global {
      & .hide-list {
        &::-webkit-scrollbar {
          background-color: transparent;
        }
      }
    }
  }

  :global {
    .ant-drawer-body {
      overflow: hidden;
    }
  }
}

.empty {
  height: calc(100vh - 90px);
  display: flex;
  align-items: center;
  justify-content: center;
}

.warningContainer {
  position: relative;
  overflow: hidden;

  & > p:first-child {
    position: fixed;
    margin: 0;
  }

  .list {
    margin-top: 48px;
    height: calc(100vh - 136px);
    overflow-y: scroll;
    overflow-x: hidden;
    padding-right: 10px;
    &::-webkit-scrollbar {
      width: 6px; //y轴滚动条粗细
      height: 0; //x轴滚动条粗细
      background-color: @colorA3;
      border-radius: 0 0 0 0;
      padding-bottom: 20px;
    }

    &::-webkit-scrollbar-corner {
      background: @colorA1;
    }

    &::-webkit-scrollbar-thumb {
      background: @colorA5;
      border-radius: 6px;
      height: 6px !important;
      min-height: 54px;
      position: absolute;
      right: 20px;
    }
  }
}

.drawerContainer {
  font-size: 14px;
  font-family: normal-font, serif;
  color: @colorA12;

  & p {
    margin: 0;
  }

  .contact {
    margin-bottom: 24px;
  }
}

.noticeTitle {
  display: flex;
  align-items: center;

  & p {
    margin: 0;
  }

  & span {
    display: inline-block;
    width: 24px;
    height: 14px;
    line-height: 13px;
    border-radius: 10px;
    background: @colorB5;
    font-size: 10px;
    text-align: center;
    margin-left: 8px;
  }
}

.noticeItem {
  height: 57px;
  margin-bottom: 20px;
  border-radius: 6px;
  background: @colorA13;
  padding: 8px 16px;
  box-sizing: border-box;
  color: @colorA12;
  font-family: normal-font, serif;
  font-size: 14px;

  & p {
    font-family: Arial, Helvetica, sans-serif, Serif !important;
  }

  & span {
    color: @colorA8;
    font-size: 12px;
  }
}
