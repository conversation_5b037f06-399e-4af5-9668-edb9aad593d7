@import '@/renderer/static/style/baseColor.module.less';

.container {
  width: auto;
  display: inline-flex;
  justify-content: space-between;
  align-items: center;
  .ng_alert {
    width: auto;
    height: 46px;
    margin-right: 16px;
    display: flex;
    align-items: center;

    :global {
      .ant-alert-icon {
        margin-top: 0;
        align-self: normal;
      }
      .ant-alert-close-icon {
        height: 16px;
      }
    }
  }
  .setting {
    width: 164px;
    display: flex;
    align-items: center;
    height: 46px;
    background: @colorA4;
    border-radius: 6px;

    .settingIcon {
      margin-left: 20px !important;
    }
    .power {
      cursor: url('@/renderer/static/svg/cursor.cur'), pointer;
      border-radius: 2px;
      margin-left: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 36px;
      height: 36px;
      &:hover {
        background: @colorA5;
      }
    }

    .warningCount {
      position: relative;

      .count {
        position: absolute;
        right: -8px;
        top: 0;
        width: 24px;
        height: 14px;
        border-radius: 10px;
        line-height: 14px;
        background: @colorB5;
        color: @colorA12;
        font-size: 10px;
        text-align: center;
      }
    }
  }
}
.settingHover {
  background: @colorA5;
}
.hideText {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.popoverInner {
  :global {
    .ant-popover-arrow {
      &::before {
        background: @colorA5;
      }
    }
    .ant-popover-inner {
      height: 32px;
      padding: 2px 16px;
      border-radius: 6px;
      background: @colorA5 !important;
    }
    .ant-popover-title {
      color: @colorA12 !important;
      margin-bottom: 0;
      line-height: 26px;
    }
  }
}
:global {
  #home-nickname,
  #home-username {
    display: flex;
    justify-content: center;
    overflow: hidden;
    height: 100%;
    & > div:first-child {
      line-height: 32px;
      width: fit-content;
      max-width: 116px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .ant-table-cell-row-hover {
    background: transparent !important;
  }
  .ng_home_drop_setting {
    z-index: 70 !important;
    .ant-dropdown-menu {
      background: @colorA4 !important;
      padding: 10px !important;
      right: 10%;
      top: 12px;
    }
    .ant-dropdown-menu-title-content {
      width: 100%;
      height: 100%;
      line-height: 32px;
      & > div:first-child {
        width: 100%;
      }
    }

    .ant-dropdown-menu .ant-dropdown-menu-item {
      padding: 0 !important;
      font-family:
        normal-font -apple-system,
        BlinkMacSystemFont,
        'Segoe UI',
        Roboto,
        'Helvetica Neue',
        Arial,
        'Noto Sans',
        sans-serif,
        'Apple Color Emoji',
        'Segoe UI Emoji',
        'Segoe UI Symbol',
        'Noto Color Emoji';
      width: 140px;
      height: 32px;
      text-align: center;
      color: @colorA12 !important;
      border-radius: 6px !important;
      cursor: url('@/renderer/static/svg/cursor.cur'), pointer;
      &:hover {
        background: @colorC4;
      }
      &:nth-child(1) {
        cursor: url('@/renderer/static/svg/defaultMouse.cur'), pointer;
        &:hover {
          background: @colorA4 !important;
        }
      }
      &:nth-child(2) {
        margin-bottom: 40px !important;
        position: relative;
        cursor: url('@/renderer/static/svg/defaultMouse.cur'), pointer;
        &::after {
          content: '';
          display: block;
          position: absolute;
          bottom: -20px;
          height: 1px;
          width: 138px;
          background: @colorA5;
        }
        &:hover {
          background: @colorA4 !important;
        }
      }
      &:not(&:last-child):not(:nth-child(1)):not(:nth-child(2)) {
        margin-bottom: 13px;
      }
      &:last-child {
        margin-bottom: 8px !important;
      }
    }
  }
}

.changePWDContainer {
  .outsideInput {
    position: relative;
    .tips {
      position: absolute;
      width: 28px;
      height: 28px;
      display: flex;
      justify-content: center;
      align-items: center;
      right: 8px;
      top: 2px;
      color: @colorA9;
    }
  }
  :global {
    .ant-input-affix-wrapper-disabled {
      background: @colorA6 !important;
    }
    .ant-form-item-label {
      & label {
        &::before {
          display: none !important;
        }
        color: @colorA9 !important;
      }
    }
  }
}
