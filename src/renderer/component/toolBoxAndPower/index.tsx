import React, { useState } from 'react';
import classnames from 'classnames';
import { Dropdown, Form, MenuProps, Popover } from 'antd';
import styles from './index.module.less';
import { useNavigate } from 'react-router-dom';
import { ShutDown } from '@/renderer/component/shutdown';
import { UserSessionProps, withUserSession } from '@/renderer/hocComponent/withUserSession';
import { getM200ApiInstance } from '@/common/api/ngApiAgent';
import { Notice, SystemSetting } from '@/renderer/uiComponent/SvgGather';
import { axiosHttpClient } from '@/common/api/httpClient/axiosHttpClientImplMain';
import { IntlShape, useIntl } from 'react-intl';
import NoticeDrawer from '@/renderer/component/toolBoxAndPower/components/noticeDrawer';
import { useRecoilState } from 'recoil';
import ChangePasswordModal from '@/renderer/container/home/<USER>/ChangePasswordModal';
import { useAsyncEffect } from 'ahooks';
import { connSocket } from '../../utils/imgSocket';
import { faultAtom } from '../../recoil/fault';
import { FaultLevelEnum } from '../../../common/systemFault/type';

type Props = {
  // 用于传入自定义回调，用于解决管理按钮与页面交互冲突
  callBackMap?: {
    [key: string]: any;
  };
};
export const validatePassword = async (pwd: string, intl: IntlShape) => {
  return new Promise((resolve, reject) => {
    if (pwd.length === 0) {
      reject(intl.formatMessage({ id: '密码不可为空' }));
    } else if (pwd.length < 8 || pwd.length > 20) {
      let num = 8;
      reject(intl.formatMessage({ id: '密码长度须为8至20位' }, { num }));
    } else if (!PWD_REG_NOT_ALL.test(pwd)) {
      reject(intl.formatMessage({ id: '必须包含大小写字母、数字组合' }));
    } else if (!PWD_REG.test(pwd)) {
      reject('仅允许输入字母，数字');
    }
    resolve('');
  });
};

export const PWD_REG = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]+$/;
export const PWD_REG_NOT_ALL = /(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]/;

const Index = (props: Props & UserSessionProps) => {
  const navigate = useNavigate();
  const [openDrawer, setOpenDrawer] = useState(false);
  const [fault] = useRecoilState(faultAtom);
  const intl = useIntl();
  const [form] = Form.useForm();
  const [open, setOpen] = useState(false);
  const [openSetting, setOpenSetting] = useState(false);
  const [isShowNickName, setIsShowNickName] = useState(false);
  const [isShowUserName, setIsShowUserName] = useState(false);

  useAsyncEffect(async () => {
    await window.systemAPI.checkDisk80Percent();
    connSocket.accuracy.usr_name = props.userSession?.username;
  }, []);
  const handleOk = async () => {
    await form.validateFields();
    let formResult = form.getFieldsValue();
    let requestBody = {
      password: formResult.origin,
      new_password: formResult.confirm,
    };
    let isSuccess = await getM200ApiInstance()
      .changePWD(requestBody)
      .catch(error => {
        if (error.code === 'SU10104') {
          form.setFields([
            {
              name: 'origin',
              errors: [intl.formatMessage({ id: '密码错误' })],
            },
          ]);
        }
      });
    if (isSuccess) {
      setOpen(false);
      navigate('/logout');
      await window.authAPI.logout();
      axiosHttpClient.resetAuthToken();
      form.resetFields();
    }
  };

  const items: MenuProps['items'] = [
    {
      key: '0',
      label: (
        <div className={styles.hideText} id={'home-nickname'}>
          {isShowNickName ? (
            <Popover overlayClassName={styles.popoverInner} placement={'left'} title={props.userSession?.nickname}>
              <div>{props.userSession?.nickname}</div>
            </Popover>
          ) : (
            <div>{props.userSession?.nickname}</div>
          )}
        </div>
      ),
    },
    {
      key: '1',
      label: (
        <div className={styles.hideText} id={'home-username'}>
          {isShowUserName ? (
            <Popover overlayClassName={styles.popoverInner} placement={'left'} title={props.userSession?.username}>
              <div>{props.userSession?.username}</div>
            </Popover>
          ) : (
            <div>{props.userSession?.username}</div>
          )}
        </div>
      ),
    },
    {
      key: '2',
      label: <div onClick={() => { setOpenSetting(false); navigate('/manage'); }}>{intl.formatMessage({ id: '管理' })}</div>,
    },
    {
      key: '3',
      label: <div id='toolBoxAndPower-about' onClick={() => navigate('/about')}>{intl.formatMessage({ id: '关于我们' })}</div>,
    },
    ...[
      props.userSession?.role_id && +props.userSession.role_id === 1001
        ? null
        : {
            key: '4',
            label: <div onClick={() => { setOpenSetting(false); setOpen(true); }}>{intl.formatMessage({ id: '修改密码' })}</div>,
          },
    ],
    {
      key: '5',
      label: <div onClick={() => { setOpenSetting(false); navigate('/logout'); }}>{intl.formatMessage({ id: '登出' })}</div>,
    },
  ];

  const handleOpenChange = (isOpen: boolean) => {
    setOpenSetting(isOpen);
    props.callBackMap?.hideTreatmentRecord(false);
    let timer: any;
    timer = setTimeout(() => {
      if ((document.querySelector('#home-nickname div:first-child') as HTMLElement)?.offsetWidth >= 116) {
        setIsShowNickName(true);
      } else {
        setIsShowNickName(false);
      }
      if ((document.querySelector('#home-username div:first-child') as HTMLElement)?.offsetWidth >= 116) {
        setIsShowUserName(true);
      } else {
        setIsShowUserName(false);
      }
      clearTimeout(timer);
    }, 100);
  };

  return (
    <div className={styles.container}>
      <ChangePasswordModal
        title={intl.formatMessage({ id: '修改密码' })}
        form={form}
        onOk={handleOk}
        onCancel={() => {
          form.resetFields();
          setOpen(false);
        }}
        open={open}
      />
      <div className={classnames(styles.setting)}>
        <Dropdown
          forceRender
          open={openSetting}
          onOpenChange={isOpen => handleOpenChange(isOpen)}
          menu={{ items }}
          trigger={['click']}
          overlayClassName={'ng_home_drop_setting'}
        >
          <div
            id='toolBoxAndPower-settingIcon'
            className={classnames(styles.power, styles.settingIcon, {
              [styles.settingHover]: openSetting,
            })}
          >
            <SystemSetting />
          </div>
        </Dropdown>
        <div className={classnames(styles.power, styles.warningCount)} onClick={() => setOpenDrawer(true)}>
          <Notice />
          {fault[FaultLevelEnum.warning].length > 0 && <div className={styles.count}>{fault[FaultLevelEnum.warning].length}</div>}
        </div>
        <div className={styles.power}>
          <ShutDown />
        </div>
      </div>
      <NoticeDrawer open={openDrawer} setOpen={setOpenDrawer} />
    </div>
  );
};
export default withUserSession(Index);
