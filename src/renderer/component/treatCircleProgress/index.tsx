import React from 'react';
import { NgProgress } from '@/renderer/uiComponent/NgProgress';
import styles from './index.module.less';

interface Props {
  percent: number;
  strokeWidth: number;
  size: number;
  isTreating: boolean;
  fontSize?: number;
}

const TreatCircleProgress: React.FC<Props> = (props: Props) => {
  const { percent, strokeWidth, size, isTreating, fontSize = 32 } = props;

  const pauseGradient = 'pauseGradient';
  const treatingGradient = 'treatingGradient';
  const gradientId = isTreating ? treatingGradient : pauseGradient;

  return (
    <div className={styles.container} style={{ width: `${size}px`, height: `${size}px` }}>
      <svg width="0" height="0">
        <defs>
          <linearGradient id={treatingGradient} x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#246465" />
            <stop offset="70%" stopColor="#4EB7B9" />
            <stop offset="100%" stopColor="#4EB7B9" />
          </linearGradient>
          <linearGradient id={pauseGradient} x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#72727D" />
            <stop offset="100%" stopColor="#72727D" />
          </linearGradient>
        </defs>
      </svg>
      <NgProgress
        type="circle"
        percent={percent}
        strokeWidth={strokeWidth}
        size={size}
        strokeColor={`url(#${gradientId})`}
        status={'normal'}
        style={{ fontSize: `${fontSize}px` }}
      />
    </div>
  );
};

export default TreatCircleProgress;
