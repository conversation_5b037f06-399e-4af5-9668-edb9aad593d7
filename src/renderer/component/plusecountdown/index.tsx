import React, { useEffect } from 'react';
import styles from './index.module.less';
import { NgIcon } from '../../uiComponent/NgIcon';
import { Hourglass } from '../../uiComponent/SvgGather';

type PluseProps = {
  total: number;
  count: number;
  status: string;
};

type CountdownProps = {
  time: string;
  status: string;
};
export const Pluse: React.FC<PluseProps> = ({ total, count, status }) => {
  return (
    <div data-status={status} className={styles.pluse}>
      脉冲数：{count}/{total}
    </div>
  );
};

export const Countdown: React.FC<CountdownProps> = ({ time, status }) => {
  const [parseTime, setParseTime] = React.useState({
    hour: '00',
    min: '00',
    sec: '00',
  });
  useEffect(() => {
    const [hour, min, sec] = time.split(':');
    setParseTime({
      hour,
      min,
      sec,
    });
  }, [time]);

  return (
    <div data-status={status} className={styles.countdown}>
      <NgIcon style={{ width: '21px', height: '21px' }} fontSize={21} iconSvg={Hourglass} />
      <span className="hour">{parseTime.hour}</span>
      <span className="colon">:</span>
      <span className="min">{parseTime.min}</span>
      <span className="colon">:</span>
      <span className="sec">{parseTime.sec}</span>
    </div>
  );
};
