import { EnumPlanStimulusType, StimulusTemplatePageQueryModel } from '@/common/types';
import { ParamsType } from '@/renderer/container/stimulateTemplate/component/filterCondition';

export const setFilterTemplateParams = (filter: StimulusTemplatePageQueryModel, params: ParamsType): StimulusTemplatePageQueryModel => {
  let newParams = filter;
  newParams = Object.assign({}, newParams, { name: params.keyword });
  if (params.type) {
    const getType = (type: string) => {
      if (type === 'all') {
        return [EnumPlanStimulusType.TBS, EnumPlanStimulusType.RTMS];
      } else if (type === 'tbs') {
        return [EnumPlanStimulusType.TBS];
      } else {
        return [EnumPlanStimulusType.RTMS];
      }
    };

    newParams = Object.assign({}, newParams, { stimulus_type_enum_list: getType(params.type) });
  }

  if (params.sort) {
    newParams = Object.assign({}, newParams, { sort_by: params.sort });
  }

  newParams.page_num = 1;

  return newParams;
};
