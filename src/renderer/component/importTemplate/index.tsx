import React, { useState, useEffect, useRef } from 'react';
import styles from './index.module.less';
import { EnumPlanStimulusType, EnumSortType, PlanStimulusModel, StimulusTemplatePageQueryModel } from '@/common/types';
import { getM200ApiInstance } from '@/common/api/ngApiAgent';
import { FilterCondition, ParamsType } from '@/renderer/container/stimulateTemplate/component/filterCondition';
import classnames from 'classnames';
import NgEmpty from '@/renderer/uiComponent/NgEmpty';
import { EmptyUnCheckTask } from '@/renderer/uiComponent/SvgGather';
import { PreviewTemplateCard } from '@/renderer/container/stimulateTemplate/component/previewTemplateCard';
import NgModal from '@/renderer/uiComponent/NgModal';
import NgButton from '@/renderer/uiComponent/NgButton';
import { useIntl } from 'react-intl';
import { calTbsChartData } from '@/renderer/component/template/calTemplate';
import NgButtonText from '@/renderer/uiComponent/NgButtonText';
import { setFilterTemplateParams } from '@/renderer/component/importTemplate/setFilterTemplateParams';
import _ from 'lodash';
import { useAsyncEffect } from 'ahooks';
import NgListItem from '../../uiComponent/NgListItem';

type Props = {
  visible: boolean;
  onCancel(): void;
  onOk(target: PlanStimulusModel): void;
};

let isCal: boolean = false;

export const ImportTemplate = (props: Props) => {
  const intl = useIntl();
  const [targetStimulate, setTargetStimulate] = useState<PlanStimulusModel | undefined>(undefined);
  const [stimulateList, setStimulateList] = useState<PlanStimulusModel[]>([]);
  const [importFilters, setImportFilters] = useState<StimulusTemplatePageQueryModel>({
    page_num: 1,
    page_size: 200,
    stimulus_type_enum_list: [EnumPlanStimulusType.TBS, EnumPlanStimulusType.RTMS],
    sort_by: EnumSortType.DESC,
  });
  const filterRef = useRef();
  const [isFetchDataScroll, setIsFetchDataScroll] = useState(false);
  const [total, setTotal] = useState<number>(0);
  const m200Api = getM200ApiInstance();
  // 监听滚动条
  const scrollRef = useRef<HTMLDivElement>(null);
  useAsyncEffect(async () => {
    if (!props.visible) {
      setTargetStimulate(undefined);
      setStimulateList([]);
      setImportFilters({
        page_num: 1,
        page_size: 200,
        stimulus_type_enum_list: [EnumPlanStimulusType.TBS, EnumPlanStimulusType.RTMS],
        sort_by: EnumSortType.DESC,
      });
      setTotal(0);
    } else {
      // @ts-ignore
      filterRef.current!.reset();
      await getTemplateList(importFilters);
    }
  }, [props.visible]);
  // 过滤条件变化
  useAsyncEffect(async () => {
    await getTemplateList(importFilters);
    isCal = false;
  }, [JSON.stringify(importFilters)]);
  const getTemplateList = async (filter: StimulusTemplatePageQueryModel) => {
    let res = await m200Api.getStimulusTemplateList(filter);
    if (isFetchDataScroll) {
      setStimulateList(prevStimulateList => {
        return _.uniqWith([...prevStimulateList, ...res.records], (a, b) => {
          return a.id === b.id;
        });
      });
    } else {
      setStimulateList(res.records);
    }
    setTotal(res.total);
  };
  // 顶部参数
  const controlParams = async (params: ParamsType) => {
    let newParams = setFilterTemplateParams(importFilters, params);
    setImportFilters(newParams);
    setIsFetchDataScroll(false);
    setTargetStimulate(undefined);
  };

  const clickTemplate = async (params: PlanStimulusModel) => {
    setTargetStimulate(params);
  };

  const handleOk = () => {
    if (!targetStimulate) return;
    const { pulse_total, treatment_time } = calTbsChartData(targetStimulate);
    props.onOk({
      ...targetStimulate,
      pulse_total,
      treatment_time,
    });
  };

  useEffect(() => {
    const handleScroll = async () => {
      if (isCal) return;
      if (total <= stimulateList.length) return;
      const { scrollTop, scrollHeight, clientHeight } = scrollRef.current!;
      if (scrollTop + clientHeight + 50 >= scrollHeight) {
        isCal = true;
        setImportFilters(prevFilters => {
          return {
            ...prevFilters,
            page_num: prevFilters.page_num + 1,
          };
        });
        setIsFetchDataScroll(true);
      }
    };
    if (!scrollRef.current) return;
    scrollRef.current.addEventListener('scroll', handleScroll);

    return () => {
      if (!scrollRef.current) return;
      scrollRef.current.removeEventListener('scroll', handleScroll);
    };
  }, [props.visible, total]);
  const renderEmpty = (stimulateListParams: PlanStimulusModel[], name: string, targetStimulateParam: PlanStimulusModel | undefined) => {
    if (stimulateListParams.length === 0 && !!name) {
      return (
        <div className={styles.empty_content}>
          <NgEmpty key={'custom'} emptyType={'custom'} customDesc={intl.formatMessage({ id: '未搜索到相关内容' })} customSvg={<EmptyUnCheckTask />} />
        </div>
      );
    } else if (!targetStimulateParam) {
      return (
        <div className={styles.empty_content}>
          <NgEmpty key={'custom'} emptyType={'custom'} customDesc={intl.formatMessage({ id: '请选择脉冲模板' })} customSvg={<EmptyUnCheckTask />} />
        </div>
      );
    }

    return <></>;
  };

  return (
    <NgModal
      title={intl.formatMessage({ id: '脉冲模板' })}
      open={props.visible}
      width={916}
      footer={
        <>
          <div className={styles.import_footer}>
            <NgButton disabled={!targetStimulate} onClick={handleOk}>
              {intl.formatMessage({ id: '使用' })}
            </NgButton>
            <NgButtonText onClick={props.onCancel} className={styles.mr24}>
              {intl.formatMessage({ id: '取消' })}
            </NgButtonText>
          </div>
        </>
      }
      onCancel={props.onCancel}
    >
      <div className={styles.import_container}>
        <div className={styles.filter_container}>
          <FilterCondition
            haveAddBtn={false}
            ref={filterRef}
            handleAddTemplate={() => {
              return;
            }}
            handleFilter={controlParams}
          />
        </div>
        <div className={styles.bottom_container}>
          <div className={styles.name_list} id={'nameList'} ref={scrollRef}>
            {stimulateList.map(item => {
              return (
                <NgListItem
                  key={item.id}
                  className={classnames(styles.name_item, targetStimulate?.id === item.id ? styles.is_active : '')}
                  title={item.name}
                  maxWidth={100}
                  onClick={async () => clickTemplate(item)}
                />
              );
            })}
          </div>
          <div className={styles.template_content}>
            {renderEmpty(stimulateList, importFilters.name ? importFilters.name : '', targetStimulate)}
            {!!targetStimulate && targetStimulate.id && (
              <PreviewTemplateCard
                haveFooter={false}
                template={targetStimulate}
                handleDelete={() => {
                  return;
                }}
                handleEdit={() => {
                  return;
                }}
              />
            )}
          </div>
        </div>
      </div>
    </NgModal>
  );
};
