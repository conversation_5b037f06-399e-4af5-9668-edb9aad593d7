@import '../../static/style/baseColor.module.less';

.import_container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  .filter_container {
    width: 100%;
    display: inline-flex;
    justify-content: flex-start;
    margin-bottom: 20px;
  }
  .bottom_container {
    display: inline-flex;
    justify-content: space-between;
    flex-direction: row;
    height: 560px;
    .name_list {
      width: 240px;
      height: 100%;
      background: @colorA3;
      color: @colorA9;
      margin-right: 16px;
      padding: 20px 20px;
      overflow-y: auto;
      .name_item {
        width: 100%;
        height: 32px;
        line-height: 32px;
        padding: 0px 20px 0px 20px;
        margin-bottom: 8px;
        color: @colorA9;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .name_item:hover {
        border-radius: 8px;
        background: @colorA4;
      }
      .is_active {
        color: @colorC1;
      }
      &::-webkit-scrollbar {
        width: 6px;
      }
      &::-webkit-scrollbar-thumb {
        background: @colorA6;
        border-radius: 10px;
      }
      &::-webkit-scrollbar-thumb:vertical {
        width: 10px;
        height: 10px;
      }
    }
    .template_content {
      flex: 1;
      background: @colorA3;

      .empty_content {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        color: @colorA9;
      }
    }
  }
}

.import_footer {
  display: inline-flex;
  flex-direction: row-reverse;

  .mr24 {
    margin-right: 24px;
  }
}
