@import '@/renderer/static/style/baseColor.module.less';
.container {
  .pstAndCoilStatus {
    width: 290px;
    height: 46px;
    border-radius: 90px;
    opacity: 1;
    background: @colorA4;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px !important;

    .photoContainer {
      width: 125px;
      display: flex;
      align-items: center;
      margin-left: 20px;
    }
    .coilContainer {
      width: 165px;
      display: flex;
      align-items: center;
      margin: 0 20px;
    }

    .errorRound {
      border-radius: 50%;
      width: 14px;
      height: 14px;
      background: linear-gradient(180deg, @colorD3_end 3%, @colorD3_start 100%);
    }
    .successRound {
      border-radius: 50%;
      width: 14px;
      height: 14px;
      background: linear-gradient(2deg, #125c2f -23%, @colorD2_end 2%, #29f479 98%);
    }

    .split {
      width: 2px;
      height: 20px;
      background: @colorA8;
    }

    .photo {
      margin: 0 10px;
      & + p {
        margin: 0;
      }
    }

    .temperature {
      margin-left: 10px;
    }
  }
}
