import React from 'react';
import { ReactComponent as Photo } from '@/renderer/static/svg/photo.svg';
import { ReactComponent as Coil } from '@/renderer/static/svg/coil.svg';
import styles from './index.module.less';
import classnames from 'classnames';
type Props = {
  isConnectPhoto: boolean; // 是否连接相机
  isConnectCoil: boolean; // 是否连接线圈
  coilTemperature: number; // 线圈温度
};
const Index = (props: Props) => {
  const { isConnectPhoto, isConnectCoil, coilTemperature = 17 } = props;

  return (
    <div className={styles.container}>
      <div className={styles.pstAndCoilStatus}>
        <div className={styles.photoContainer}>
          <div
            className={classnames({
              [styles.errorRound]: !isConnectPhoto,
              [styles.successRound]: isConnectPhoto,
            })}
          />
          <Photo className={styles.photo} />
          <p>相机</p>
        </div>

        <span className={styles.split} />

        <div className={styles.coilContainer}>
          <div
            className={classnames({
              [styles.errorRound]: !isConnectCoil,
              [styles.successRound]: isConnectCoil,
            })}
          />
          <Coil className={styles.photo} />
          <p>线圈</p>
          <p className={styles.temperature}>{`${coilTemperature}℃`}</p>
        </div>
      </div>
    </div>
  );
};
export default Index;
