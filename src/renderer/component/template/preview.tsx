import * as React from 'react';
import { useEffect } from 'react';
import { EnumPlanStimulusType, PlanStimulusModel } from '@/common/types';
import { TBSChart } from '@/renderer/component/tbsChart';
import { injectIntl, IntlShape } from 'react-intl';
import styles from './previewTemplate.module.less';
import { IntlPropType } from '@/common/types/propTypes';
import { calRules } from '@/renderer/component/template/calRules';
import classnames from 'classnames';

type Props = IntlPropType & {
  showName: boolean;
  template: PlanStimulusModel;
  isSmall?: boolean;
};

const renderField = (intl: IntlShape, field: string, param: PlanStimulusModel, isSmall: boolean) => {
  let typeLabel = param.type === EnumPlanStimulusType.TBS ? 'tbs_' : 'rtms_';
  let label = intl.formatMessage({ id: `${typeLabel}${field}` });
  let value = param[field];
  if (field === 'stimulateType') {
    label = intl.formatMessage({ id: '刺激类型' });
    value = param.type === EnumPlanStimulusType.RTMS ? intl.formatMessage({ id: 'rTMS' }) : intl.formatMessage({ id: 'TBS' });
  }

  return (
    <div className={classnames(styles.field, isSmall ? styles.field_small : '')}>
      <span className={styles.label}>{label} ：</span>
      <span className={styles.value}>{value}</span>
    </div>
  );
};
const renderTemplate = (intl: IntlShape, fields: string[], template: PlanStimulusModel, isSmall: boolean) => {
  return fields.map((item: string) => {
    return <React.Fragment key={item}>{renderField(intl, item, template, isSmall)}</React.Fragment>;
  });
};
export const InnerPreviewTemplate = (props: Props) => {
  const { isSmall } = props;
  const [renderFields, setRenderFields] = React.useState<string[]>(() => {
    return ['stimulateType'].concat(
      calRules(props.template, 0).map(item => {
        return item.key;
      })
    );
  });

  useEffect(() => {
    let newRenderFields = calRules(props.template, 0).map(item => {
      return item.key;
    });
    newRenderFields = newRenderFields.filter(item => {
      if (props.template.strand_pulse_count === 1) {
        return item !== 'intermission_time';
      }

      return true;
    });
    setRenderFields(['stimulateType'].concat(newRenderFields));
  }, [props.template]);

  return (
    <div className={styles.previewTemplate}>
      {props.showName && <div className={classnames(styles.name, isSmall ? styles.name_small : '')}>{props.template.name}</div>}
      <div className={styles.chart}>
        <TBSChart template={props.template} />
      </div>
      <div className={styles.fields}>{renderTemplate(props.intl, renderFields, props.template, !!isSmall)}</div>
    </div>
  );
};
export const PreviewTemplate = injectIntl(InnerPreviewTemplate);
