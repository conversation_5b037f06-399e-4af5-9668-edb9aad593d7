import React, { useEffect, useState } from 'react';
import classnames from 'classnames';
import { EnumPlanStimulusType } from '@/common/types';
import { ColProps, Form } from 'antd';
import { IntlPropType } from '@/common/types/propTypes';
import { NgSelect } from '@/renderer/uiComponent/NgSelect';
import { NgInputNumber } from '@/renderer/uiComponent/NgInputNumber';
import { injectIntl } from 'react-intl';
import { calRules, FieldType } from '@/renderer/component/template/calRules';
import { cloneDeep, isNumber } from 'lodash';
import { flatObj } from '../../container/previewPlan/utils';
import styles from './index.module.less';

type Props = IntlPropType & {
  stimulate: any;
  formRef: any;
  motionThreshold: number;
  preName?: string;
  onError?(isError: boolean): void;
  itemCol?: { labelCol?: ColProps; wrapperCol?: ColProps };
  itemDisabled?: boolean;
  inputClass?: any;
  AllNullSueecss?: boolean;
};

export type TbsFieldType = {
  key: string;
  rules: {
    min: number;
    max: number;
    step: number;
    precision: number;
  };
  name?: string;
};

// eslint-disable-next-line max-lines-per-function
export const InnerEditStimulateTemplate = (props: Props) => {
  const { intl, motionThreshold, formRef, preName = '', stimulate, onError, itemDisabled, AllNullSueecss = false } = props;
  const [fields, setFields] = useState<FieldType[]>(() => {
    return calRules(stimulate, motionThreshold);
  });
  const stimulateOptions = [
    { value: EnumPlanStimulusType.TBS, label: intl.formatMessage({ id: 'TBS' }) },
    { value: EnumPlanStimulusType.RTMS, label: intl.formatMessage({ id: 'rTMS' }) },
  ];

  useEffect(() => {
    let tempStimulate = cloneDeep(stimulate);
    if (tempStimulate.strand_pulse_count === 1) {
      tempStimulate.intermission_time = undefined;
    }
    const value_keys = Object.keys(stimulate);
    let is_empty: boolean = false;
    if (value_keys.filter(v => !isNumber(stimulate[v])).length === value_keys.length - 1) {
      is_empty = true;
    }

    formRef?.setFieldsValue({ ...flatObj({ [preName]: tempStimulate }), type: tempStimulate.type });
    const newFields = calRules(tempStimulate, motionThreshold, preName);
    setFields(
      newFields.map(v => {
        if(AllNullSueecss && is_empty) {
          return {
            ...v,
            status: undefined,
            validator: async (_: any, val: string) => {
              return new Promise((res, rej) => {
                return res('');
              });
            },
          };
        }
        if (v.status === 'error') {
          return v;
        }

        return {
          ...v,
          validator: async (_: any, val: string) => {
            return new Promise((res, rej) => {
              if (val === null) return rej('不可为空');

              return res('');
            });
          },
        };
      })
    );
    const hasError = newFields.some(item => item.status === 'error');

    sendErrorToParent(hasError);

    setTimeout(() => {
      const values = formRef?.getFieldsValue();
      formRef
        ?.validateFields(Object.keys(values).filter(v => v.startsWith(preName)))
        .then(() => {
          //
        })
        .catch(() => {
          //
        });
    }, 100);
  }, [JSON.stringify(stimulate), AllNullSueecss]);

  const sendErrorToParent = (isError: boolean) => {
    if (onError) {
      onError(isError);
    }
  };
  const renderLabel = (key: string) => {
    let label = '';
    if (stimulate.type === EnumPlanStimulusType.RTMS) {
      label = intl.formatMessage({ id: `rtms_${key}` });
    } else {
      label = intl.formatMessage({ id: `tbs_${key}` });
    }

    return <span>{label}</span>;
  };

  const renderExtra = (key: string, fieldsParams: TbsFieldType[]) => {
    const target = fieldsParams.find(item => item.key === key);

    return `${target!.rules.min}-${target!.rules.max}`;
  };

  // eslint-disable-next-line @typescript-eslint/no-shadow
  const renderFields = () => {
    return fields.map(item => {
      return (
        <Form.Item
          className={classnames(item.key,styles.template)}
          name={item.name}
          wrapperCol={props.itemCol?.wrapperCol}
          labelCol={props.itemCol?.labelCol || { span: 12 }}
          key={item.key}
          extra={renderExtra(item.key, fields)}
          label={renderLabel(item.key)}
          rules={[{ validator: async (_, val) => item.validator(_, val) }]}
        >
          <NgInputNumber
            status={item.disabled ? '' : item.status}
            disabled={item.disabled || itemDisabled}
            overlayclass={props.inputClass}
            isInvalid={!stimulate[item.key]}
            step={item.rules.step}
          />
        </Form.Item>
      );
    });
  };

  return (
    <>
      <Form.Item
        name="type"
        labelAlign={'left'}
        wrapperCol={props.itemCol?.wrapperCol}
        labelCol={props.itemCol?.labelCol || { span: 12 }}
        label={<span>{`${intl.formatMessage({ id: '类型' })}`}</span>}
      >
        <NgSelect disabled={itemDisabled} size={'middle'} options={stimulateOptions} />
      </Form.Item>
      {renderFields()}
    </>
  );
};

export const EditStimulateTemplate = injectIntl(InnerEditStimulateTemplate);
