# 使用刺激模板参数组件
## 使用方法
```javascript
 <NgForm
    props
    onValuesChange={handleChangeValues}
    >
    <EditStimulateTemplate motionThreshold={100} formRef={form} stimulate={formValues}/>
</NgForm>
```
## 参数说明
| 参数              | 说明      | 类型                         | 默认值                |
|-----------------|---------|----------------------------|--------------------|
| motionThreshold | 运动阈值    | number                     | 0                  |
| formRef         | 表单引用    | any                        | antdForm.useForm() |
| stimulate       | 刺激模板    | StimulateModel             | 刺激参数               |
| preName         | 前置key   | string                     | ?                  |
| onError         | 表单错误时回调 | (isError: boolean) => void | ?                  |

## 原理
通过监听，stimulate的变化，重新生成一份 fields,每一个field中都带有自己的验证规则，然后渲染刷新

## 问题
### 1. 外部点击保存时，为什么没有验证红字和 红框
> 数据的值 为 undefined，不会验证，应该将其设置为 null，然后必须设置 stimulate, 否则 无法触发组件中useEffect     
> 故无法更新 组件的验证规则，组件的验证规则 依赖 calRule() 方法， calRule() 方法 依赖 stimulate
### 2. 为什么设置为null时，只有报错，而没有红框
> 在组件外部通过 form.setFieldsValue(), 无法触发组件的 useEffect(), 无法更新组件的验证规则
### 3. 关于表单的的验证规则
> 1. 表单验证采用   广度优先  验证逻辑，即一个错误类型，将所有的属性都验证一遍。antd.form 默认是一个属性，将所有的错误类型都验证一遍
> 2. 第一步验证空，第二步验证范围，第三步验证脉冲超限，第四步验证时长超限，第五验证功率超限
> 3. 前一个验证失败直接跳出，不再验证后面的
> 4. 运动阈值为 0 时，不验证功率超限. [功率超限文档](https://mqhfidmks7.feishu.cn/wiki/K1FqwEOUsi59VVkXglOcG8esnpe)

## 刺刺激仪常识
[磁刺激仪的基本原理](https://zhuanlan.zhihu.com/p/385904697)     
[脉冲刺激的区别](https://zhuanlan.zhihu.com/p/401288533)

## 本系统刺激参数的说明
![img.png](img.png)

## 关于rtms 和 tbs 的特殊说明
基于上面的刺激参数说明，rtms 和 tbs 的刺激参数，需要特殊说明。在优脑磁刺激中，下位机没有刺激类型的区分，只有刺激参数的区分，    
故rmts实际下发给下位机的刺激参数应该需要转义一下。
```js
const param = {
    action: 'set_treatment_plan',
    level: params.active_strength,   // 绝对强度
    frequency: params.strand_pulse_frequency * 100, // 串脉冲频率  =》 丛内频率
    intensity: params.strand_pulse_frequency * 100,  // 串脉冲频率  =》 从间频率
    count: 1,              // 丛内脉冲数
    bunch: params.inner_strand_pulse_count,   // 串内脉冲数  => 刺激丛数
    series: params.strand_pulse_count,        // 串数    =>  刺激串数
    pause: params.strand_pulse_count === 1 ? 1 : params.intermission_time,          // 刺激间隔  间歇时间
    sum: params.pulse_total,                // 总脉冲数
    time: params.treatment_time,          // 总刺激时间
}
```
## 关于 脉冲数 和刺激时间的计算
src/renderer/component/template/calTemplate.ts
> 1. 总刺激时间有两个， 一个展示使用需要向上取整，一个系统计算可以保留小数
> 2. 
## 关于 温升比 计算
> 1. 本质就是比较 （1 / (temperature - 20 ) / 16 ）和 安全基线的比较
> 2. 小于20度，不计算温升比
> 3. 非技术支持可以到36度，技术支持可以到41度
> 4. 暂停后的再次开始，需要前端跳过刺激间隔来计算温升比
## 关于 功率超限 的计算
> 1. 没有阈值不计算功率超限
> 2. 技术支持不计算功率超限
> 3. 参照功率超限文档，和ctbsParamsRule.ts 文件中的注释
