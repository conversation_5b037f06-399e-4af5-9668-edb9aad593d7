@import '../../static/style/baseColor.module.less';
.previewTemplate {
  width: 262px;
  height: auto;
  display: flex;
  flex-direction: column;
  font-size: 14px;

  .name {
    margin-bottom: 20px;
    color: @colorA12;
  }
  .name_small {
    margin-bottom: 8px;
    white-space: nowrap;
  }
  .chart {
    position: relative;
  }
  .fields {
    width: 100%;
    height: auto;
    display: inline-flex;
    flex-direction: column;
    .field {
      width: 100%;
      margin-bottom: 32px;
      display: inline-flex;
      color: @colorA9;
      .label {
        margin-right: 14px;
      }
      .value {
        width: auto;
      }
    }

    .field_small {
      margin-bottom: 14px;
    }
  }
}
