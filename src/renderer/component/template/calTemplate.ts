import { EnumPlanStimulusType, PlanStimulusModel } from '@/common/types';
import { getEquivalentFrequency, getTreatLimitJson } from './ctbsParamsRules';
import { product } from '@/renderer/constant/product';

export const calTBSParamOfAMin = (b: number) => {
  let temp = b * 2;

  return Math.ceil(temp);
};

export const calTBSParamOfCMax = (a: number, b: number) => {
  let temp = a / b;

  return Math.floor(temp) - 1;
};

export const calTBSParamOfT1 = (a: number, c: number) => {
  return c / a;
};
const calTBSParamOfT2 = (b: number, d: number, T1: number) => {
  return d / b;
};
// 时长公式 = (脉冲串数-1)*((刺激从数/从间频率)+刺激间隔) + (刺激从数-1)/从间频率 + (从内脉冲数-1)/从内频率，向上取整；
// const calTBSParamOfT3 = (a: number, b: number, c: number, d: number, e: number, f: number) => {
//   // a:从内频率 b:从间频率 c:从内脉冲数 d:刺激丛数 e:脉冲串数 f:刺激间隔 T2:(d/b)
//   if (e === 1) {
//     return Math.ceil(d / b);
//   }

//   return Math.ceil((e - 1) * (d / b + f) + (d - 1) / b + (c - 1) / a);
// };
// 这是给计算功率超限使用的 总时长，不要向上取整。
const calTBSParamOfT3_1 = (a: number, b: number, c: number, d: number, e: number, f: number) => {
  // a:从内频率 b:从间频率 c:从内脉冲数 d:刺激丛数 e:脉冲串数 f:刺激间隔 T2:(d/b)

  return (d / b + f) * e;
};

// 功率计算时常
const calTBSParamOfT4 = (a: number, c: number) => {
  return (1 / a) * (c - 1);
};
const calTBSParamOfT5 = (a: number, d: number, e: number, f: number) => {
  // a 串内脉冲频率     ===  tbs 下的丛内频率
  // b tbs下的从间频率   这里等于a
  // c tbs下的从内脉冲数  这里永远等于1
  // d 串内脉冲数  === tbs 下的刺激丛数
  // e 脉冲串数
  // f 刺激间隔
  const m = 1000000;

  return Math.ceil((e - 1) * (Math.floor((d * m) / a) / m + f) + Math.floor(((d - 1) * m) / a) / m);
};

const checkHaveInvalidData = (templates: PlanStimulusModel) => {
  if (templates.type === EnumPlanStimulusType.RTMS) {
    const { strand_pulse_frequency: a, inner_strand_pulse_count: d, strand_pulse_count: e, intermission_time: f = 1 } = templates;
    const newF = e === 1 ? 1 : f;

    return { isInvalid: !a || !d || !e || !newF, type: templates.type, a, b: 0, c: 0, d, e, f: newF };
  } else {
    const {
      plexus_inner_frequency: a,
      plexus_inter_frequency: b,
      plexus_inner_pulse_count: c,
      plexus_count: d,
      strand_pulse_count: e,
      intermission_time: f = 1,
    } = templates;
    const newF = e === 1 ? 1 : f;

    return { isInvalid: !a || !b || !c || !d || !e || !newF, type: templates.type, a, b, c, d, e, f: newF };
  }
};

const renderErrorData = (
  templates: PlanStimulusModel,
  params: {
    a?: number;
    b?: number;
    c?: number;
    d?: number;
    e?: number;
    f?: number;
  }
) => {
  const { a, b, c, d, e, f } = params;

  return {
    type: templates.type,
    relative_strength: templates.relative_strength ? templates.relative_strength : 100,
    a,
    b: b ? Number(b.toFixed(1)) : b,
    c,
    d,
    e,
    f: e === 1 ? 1 : f,
    aMin: 0.1,
    cMax: 32767,
    T1: 0,
    T2: 0,
    T4: 0,
    pulse_total: 0,
    treatment_time: 0,
    treatment_time_noCeil: 0,
  };
};
const calRTMSData = (templates: PlanStimulusModel) => {
  const { a, d, e, f } = checkHaveInvalidData(templates);
  const b = a; // rtms 丛间频率 = 丛内频率
  const c = 1; // rtms 丛内脉冲数 = 1
  const T4 = calTBSParamOfT4(a!, d!);
  const T5 = calTBSParamOfT5(a!, d!, e!, f);
  const T5_1 = calTBSParamOfT3_1(a!, b!, c, d!, e!, f);

  return {
    type: EnumPlanStimulusType.RTMS,
    relative_strength: templates.relative_strength ? templates.relative_strength : 100,
    a,
    b: a,
    c: 1,
    d,
    e,
    f: e === 1 ? 1 : f,
    aMin: 0.1,
    cMax: 32767,
    T1: 0,
    T2: 0,
    T4: T4.toFixed(2),
    pulse_total: d! * e!,
    treatment_time: T5,
    treatment_time_noCeil: T5_1,
  };
};

// 刺激时长用这个公式
const calTBSParamOfT3_2 = (a: number, b: number, c: number, d: number, e: number, f: number) => {
  // a:从内频率 b:从间频率 c:从内脉冲数 d:刺激丛数 e:脉冲串数 f:刺激间隔 T2:(d/b)

  return Math.ceil((e - 1) * (d / b + f) + (d - 1) / b + (c - 1) / a);
};

const calTBSData = (templates: PlanStimulusModel) => {
  const { a, b, c, d, e, f } = checkHaveInvalidData(templates);
  // a:从内频率 b:从间频率 c:从内脉冲数 d:刺激丛数 e:脉冲串数 f:刺激间隔
  const T1 = calTBSParamOfT1(a!, c!);
  const T2 = calTBSParamOfT2(b!, d!, T1);
  const T3 = calTBSParamOfT3_2(a!, b!, c!, d!, e!, f);
  const T3_1 = calTBSParamOfT3_1(a!, b!, c!, d!, e!, f);

  return {
    type: EnumPlanStimulusType.TBS,
    relative_strength: templates.relative_strength ? templates.relative_strength : 100,
    a,
    b: Number(b!.toFixed(1)),
    c,
    d,
    e,
    f: e === 1 ? 1 : f,
    aMin: calTBSParamOfAMin(b!),
    cMax: calTBSParamOfCMax(a!, b!),
    T1: T1.toFixed(3),
    T2: T2.toFixed(2),
    T4: 0,
    pulse_total: c! * d! * e!, // 总脉冲串数
    treatment_time: T3,
    treatment_time_noCeil: T3_1, // 扣除刺激间隔的时长
  };
};
export const calTbsChartData = (templates: PlanStimulusModel) => {
  const { isInvalid, type, a, b, c, d, e, f } = checkHaveInvalidData(templates);

  if (isInvalid) {
    return renderErrorData(templates, { a, b, c, d, e, f });
  }
  if (type === EnumPlanStimulusType.RTMS) {
    return calRTMSData(templates);
  } else {
    return calTBSData(templates);
  }
};

export const secToTime = (time: number) => {
  let hours = Math.floor(time / 60 / 60); // 时
  let minutes = Math.floor((time / 60) % 60); // 分
  let seconds = Math.floor(time % 60); // 秒
  if (isNaN(time) || time < 0) {
    return '--';
  }

  return `${hours > 9 ? hours : `0${hours}`}:${minutes > 9 ? minutes : `0${minutes}`}:${seconds > 9 ? seconds : `0${seconds}`}  `;
};

export const getTmsTotalTime = (treatment_time: number) => {
  if (treatment_time > 65535 || (treatment_time <= 3 && treatment_time >= 0)) {
    return '超限';
  }

  return `${secToTime(treatment_time)}`;
};

export const getMathRoundMotionThreshold = (motion_threshold: number, stimulateTemplate: PlanStimulusModel) => {
  const { relative_strength } = stimulateTemplate;

  if (relative_strength && motion_threshold) {
    const activeStrength = (motion_threshold * relative_strength) / 100;

    return Math.round(activeStrength) > 100 ? 100 : Math.round(activeStrength);
  }

  return 1;
};

// 温升剩余百分比
export const temperaturePercentage = (temperature: number) => {
  return 1 - (temperature - 22) / 16;
};

// 当前强度百分比
export const strengthPercentage = (equivalent: number, contrast: number) => {
  return equivalent / contrast;
};

/**
 * 高功率模式下的系数, 只能给CTBS 模式下使用
 * @param power <number>
 */
const calCoefficientOfHighPower = () => {
  const { power } = product;
  const isHighPower = power === 70;
  const isSuperHighPower = power === 75;
  let LT40CoefficientOfCtbs = 1;
  let GT40CoefficientOfCtbs = 1;

  if (isHighPower) {
    LT40CoefficientOfCtbs = 2.56;
    GT40CoefficientOfCtbs = 1.93;
  }

  if (isSuperHighPower) {
    LT40CoefficientOfCtbs = 3.06;
    GT40CoefficientOfCtbs = 1.93;
  }

  return {
    LT40CoefficientOfCtbs,
    GT40CoefficientOfCtbs,
  };
};

/**
 * 计算安全基值，用于控制 功率超限和  温升超限
 * @param duration  治疗时间
 * @param continued  // 可持续刺激
 * @param brevity    // 可短时间刺激
 * @param values     // 刺激模板
 * @return number
 */
export const calBase = (duration: number, continued: number, brevity: number, values: any) => {
  let isItbs = values.type === EnumPlanStimulusType.TBS && values.strand_pulse_count !== 1;
  if (isItbs) {
    return duration > 120 ? continued * 1.3 : brevity * 1.3;
  }
  // ctbs
  if (values.type === EnumPlanStimulusType.TBS && values.strand_pulse_count === 1) {
    const { LT40CoefficientOfCtbs, GT40CoefficientOfCtbs } = calCoefficientOfHighPower();
    if (duration <= 41) {
      brevity = brevity * LT40CoefficientOfCtbs;
    }
    if (duration > 41 && duration <= 121) {
      brevity = brevity * GT40CoefficientOfCtbs;
    }
  }
  // ctbs,且 低功率;  rtms

  return duration > 121 ? continued : brevity;
};

/**
 * @description  计算安全基值， 可持续刺激或者可长时间刺激
 * @param values 刺激模板
 * @param activeStrength 实际刺激强度
 * @param duration 治疗时间
 */
export const calSafeBaseValue = (values: PlanStimulusModel, activeStrength: number, duration: number) => {
  const strengthLimit = getTreatLimitJson(activeStrength - 1);
  let { continued, brevity } = strengthLimit; // 长时间 短时间

  return calBase(duration, continued, brevity, values);
};
/**
 * 获取当前温升下的治疗限制
 * @param temperature 当前温升
 * @param activeStrength 实际刺激强度
 * @param template  刺激模板
 * @param isTechSupport 是否是技术支持
 * @link [CTBS 刺激类型区分](https://zhuanlan.zhihu.com/p/401288533)
 */
export const disableBeatOfTemp = (temperature: number, activeStrength: number, template: any, isTechSupport?: boolean) => {
  if (temperature <= 26) return false;
  if ((temperature > 36 && !isTechSupport) || temperature > 41) {
    return true;
  }
  // duration: 治疗时间
  // equivalent: 等效刺激频率
  const { duration, equivalent } = getEquivalentFrequency(template);
  let contrast = calSafeBaseValue(template, (activeStrength * 1.15) | 0, duration);

  // 温升剩余百分比 <= 当前强度百分比 不允许刺激
  return temperaturePercentage(temperature) <= strengthPercentage(equivalent, contrast);
};

/**
 * 暂停后，获取温升比限制
 * @param temperature 当前温度
 * @param activeStrength 实际强度
 * @param template 刺激模板
 * @param residualPulseTotal 剩余脉冲数
 * @param residualTreatmentTime 剩余治疗时间
 * @param isTechSupport 是否是技术支持
 */
export const disableBeatOfPause = (
  temperature: number,
  activeStrength: number,
  template: any,
  residualPulseTotal: number,
  residualTreatmentTime: number,
  isTechSupport?: boolean
) => {
  // NOSONAR
  return false;
};
