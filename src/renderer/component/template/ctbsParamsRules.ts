// eslint-disable-next-line max-lines-per-function
import { EnumPlanStimulusType, PlanStimulusModel } from '@/common/types';
import _ from 'lodash';
import { calSafeBaseValue, calTbsChartData } from '@/renderer/component/template/calTemplate';

const treatLimitList = [
  [1, 100, 100, 100],
  [2, 100, 100, 100],
  [3, 100, 100, 100],
  [4, 100, 100, 100],
  [5, 100, 96.5, 100],
  [6, 100, 79.83, 100],
  [7, 100, 67.93, 100],
  [8, 100, 59, 100],
  [9, 100, 52.06, 100],
  [10, 100, 46.5, 100],
  [11, 100, 41.95, 100],
  [12, 100, 38.17, 91.6],
  [13, 100, 34.96, 83.91],
  [14, 100, 32.21, 77.31],
  [15, 100, 29.83, 71.6],
  [16, 100, 27.75, 66.6],
  [17, 100, 25.91, 62.19],
  [18, 100, 24.28, 58.27],
  [19, 100, 22.82, 54.76],
  [20, 100, 21.5, 51.6],
  [21, 100, 20.31, 48.74],
  [22, 100, 19.23, 46.15],
  [23, 100, 18.24, 43.77],
  [24, 100, 17.33, 41.6],
  [25, 100, 16.5, 39.6],
  [26, 100, 15.73, 37.75],
  [27, 100, 15.02, 36.04],
  [28, 100, 14.36, 34.46],
  [29, 100, 13.74, 32.98],
  [30, 100, 13.17, 31.6],
  [31, 100, 12.63, 30.31],
  [32, 100, 12.13, 29.1],
  [33, 100, 11.65, 27.96],
  [34, 100, 11.21, 26.89],
  [35, 100, 10.79, 25.89],
  [36, 100, 10.39, 24.93],
  [37, 100, 10.01, 24.03],
  [38, 100, 9.66, 23.18],
  [39, 100, 9.32, 22.37],
  [40, 99, 9, 21.6],
  [41, 98, 8.7, 20.87],
  [42, 97, 8.4, 20.17],
  [43, 96, 8.13, 19.51],
  [44, 95, 7.86, 18.87],
  [45, 94, 7.61, 18.27],
  [46, 93, 7.37, 17.69],
  [47, 92, 7.14, 17.13],
  [48, 91, 6.92, 16.6],
  [49, 90, 6.7, 16.09],
  [50, 89, 6.5, 15.6],
  [51, 88, 6.3, 12.54],
  [52, 87, 6.12, 11.31],
  [53, 86, 5.93, 10.9],
  [54, 85, 5.76, 10.51],
  [55, 84, 5.59, 10.13],
  [56, 83, 5.43, 9.77],
  [57, 82, 5.27, 9.42],
  [58, 81, 5.12, 9.09],
  [59, 80, 4.97, 8.77],
  [60, 79, 4.83, 8.46],
  [61, 78, 4.7, 8.16],
  [62, 77, 4.56, 7.87],
  [63, 76, 4.44, 7.6],
  [64, 75, 4.31, 7.33],
  [65, 74, 4.19, 7.07],
  [66, 73, 4.08, 6.83],
  [67, 72, 3.96, 6.59],
  [68, 71, 3.85, 6.36],
  [69, 70, 3.75, 6.13],
  [70, 69, 3.64, 5.92],
  [71, 68, 3.54, 5.71],
  [72, 67, 3.44, 5.51],
  [73, 66, 3.35, 5.32],
  [74, 65, 3.26, 5.13],
  [75, 64, 3.17, 4.95],
  [76, 63, 3.08, 4.77],
  [77, 62, 2.99, 4.6],
  [78, 61, 2.91, 4.44],
  [79, 60, 2.83, 4.28],
  [80, 59, 2.75, 4.13],
  [81, 58, 2.67, 3.98],
  [82, 57, 2.6, 3.83],
  [83, 56, 2.52, 3.69],
  [84, 55, 2.45, 3.56],
  [85, 54, 2.38, 3.42],
  [86, 53, 2.31, 3.3],
  [87, 52, 2.25, 3.17],
  [88, 51, 2.18, 3.05],
  [89, 50, 2.12, 2.94],
  [90, 49, 2.06, 2.83],
  [91, 48, 1.99, 2.72],
  [92, 47, 1.93, 2.61],
  [93, 46, 1.88, 2.51],
  [94, 45, 1.82, 2.41],
  [95, 44, 1.76, 2.31],
  [96, 43, 1.71, 2.22],
  [97, 42, 1.65, 2.13],
  [98, 41, 1.6, 2.04],
  [99, 40, 1.55, 1.96],
  [100, 39, 1.5, 1.88],
];

export const getTreatLimitJson = (index: number) => {
  index = index > 99 ? 99 : index;
  let target = treatLimitList[index];

  return {
    apex: target[0],
    frequency: target[1], // 最大丛内频率
    continued: target[2], // 可持续刺激
    brevity: target[3], // 可短时间刺激
  };
};

export enum ParamRulesType {
  MaximumTimeError = 1,
  ParamRulesError = 2,
  ParamRulesTrue = 3,
  MotionThresholdError = 4,
  ExecuteTimeError = 5,
}

export const getEquivalentFrequency = (values: PlanStimulusModel) => {
  let ruleValues = _.cloneDeep(values);
  if ((ruleValues.intermission_time || 0) > 15) {
    ruleValues.intermission_time = 15;
  }
  // @ts-ignore
  const { pulse_total, treatment_time_noCeil } = calTbsChartData(ruleValues);

  return { duration: treatment_time_noCeil, equivalent: pulse_total / treatment_time_noCeil };
};

const checkFrequency = (values: PlanStimulusModel, safetyFrequency: number): boolean => {
  if (values.type === EnumPlanStimulusType.RTMS) {
    // @ts-ignore
    return values.strand_pulse_frequency > safetyFrequency;
  }

  // @ts-ignore
  return values.plexus_inner_frequency > safetyFrequency;
};

// 计算 tbs的参数 安全规则。
// 第一步验证频率
// 第二步验证安全基值
export const cTBSParamRules = (values: PlanStimulusModel, activeStrength: number): ParamRulesType => {
  let ruleValues = _.cloneDeep(values);
  if (activeStrength < 1 || activeStrength > 100) {
    return ParamRulesType.MotionThresholdError;
  }
  let strengthLimit = getTreatLimitJson(activeStrength - 1);
  const { frequency } = strengthLimit; // 最大丛内频率 可持续刺激 可短时间刺激
  // @ts-ignore
  // 检查丛内频率是否超限
  if (checkFrequency(ruleValues, frequency)) {
    return ParamRulesType.ParamRulesError;
  }

  const { duration, equivalent } = getEquivalentFrequency(ruleValues);
  const safeBaseValue = calSafeBaseValue(ruleValues, activeStrength, duration);

  return equivalent <= safeBaseValue ? ParamRulesType.ParamRulesTrue : ParamRulesType.ParamRulesError;
};

// 判断功率超限   true 可以执行  false 不可以执行
export const isPowerOver = (values: PlanStimulusModel, activeStrength: number): boolean =>
  cTBSParamRules(values, activeStrength) === ParamRulesType.ParamRulesTrue ? true : false;
