import { EnumPlanStimulusType, PlanStimulusModel } from '@/common/types';
import { calTbsChartData, calTBSParamOfAMin, calTBSParamOfCMax, getMathRoundMotionThreshold } from '@/renderer/component/template/calTemplate';
import { checkNumberTail, errorCount65535, getErrorTimeLabel, getParamsRuleFields } from '@/renderer/component/template/validatorFun';
import { cTBSParamRules, ParamRulesType } from '@/renderer/component/template/ctbsParamsRules';
import { changeISComplete } from '@/renderer/container/stimulateTemplate/component/editTemplateCard';
import _ from 'lodash';

export type FieldType = {
  key: string;
  rules: {
    min: number;
    max: number;
    step: number;
    precision: number;
  };
  name?: string;
  disabled: boolean;
  status: '' | 'error' | 'warning' | undefined;
  validator(_: any, val: string): Promise<string>;
};
export const tbsFields = [
  { key: 'relative_strength', rules: { min: 1, max: 150, step: 1, precision: 0 } },
  { key: 'plexus_inner_frequency', rules: { min: 1, max: 100, step: 1, precision: 0 } }, // a
  { key: 'plexus_inter_frequency', rules: { min: 0.1, max: 25, step: 0.1, precision: 1 } }, // b
  { key: 'plexus_inner_pulse_count', rules: { min: 1, max: 10, step: 1, precision: 0 } }, // c
  { key: 'plexus_count', rules: { min: 1, max: 32767, step: 1, precision: 0 } }, // d
  { key: 'strand_pulse_count', rules: { min: 1, max: 32767, step: 1, precision: 0 } }, // e
  { key: 'intermission_time', rules: { min: 1, max: 600, step: 1, precision: 0 } }, // f
];

export const normalFields = [
  { key: 'relative_strength', rules: { min: 1, max: 150, step: 1, precision: 0 } },
  { key: 'strand_pulse_frequency', rules: { min: 0.1, max: 100, step: 0.1, precision: 1 } }, // a
  { key: 'inner_strand_pulse_count', rules: { min: 2, max: 32767, step: 1, precision: 0 } }, // c
  { key: 'strand_pulse_count', rules: { min: 1, max: 32767, step: 1, precision: 0 } }, // e
  { key: 'intermission_time', rules: { min: 1, max: 600, step: 1, precision: 0 } }, // f
];

const pickFields = (values: PlanStimulusModel): PlanStimulusModel => {
  const { type } = values;
  const fieldsList = type === EnumPlanStimulusType.RTMS ? normalFields : tbsFields;
  const fields = fieldsList.map(v => v.key).concat(['type']);

  return _.pick(values, fields) as PlanStimulusModel;
};
export const calRelativeStrength = (motionScope: number) => {
  if (!motionScope) {
    return { min: 1, max: 150, step: 1, precision: 0 };
  } else {
    let newMax = Math.ceil(10000 / motionScope);
    if (newMax > 150) {
      newMax = 150;
    }
    let newMin = Math.ceil((100 / motionScope) * 0.5);
    if (newMin < 1) {
      newMin = 1;
    }

    return { min: newMin, max: newMax, step: 1, precision: 0 };
  }
};

export const calStrengthRange = (activeStrength: number, relativeStrength: number, powerOverValue: number, motionThreshold = 1) => {
  if (activeStrength * 0.15 < 1) {
    return { min: relativeStrength, max: relativeStrength };
  }

  const { min: relativeMin, max: relativeMax } = calRelativeStrength(motionThreshold);

  return {
    max: Math.min(Math.floor((activeStrength + activeStrength * 0.15) / (motionThreshold / 100)), powerOverValue, relativeMax),
    min: Math.min(Math.max(Math.floor((activeStrength - activeStrength * 0.15) / (motionThreshold / 100)), relativeMin), 150),
  };
};
const calTBSRules = (templates: PlanStimulusModel, motionScope: number): FieldType[] => {
  const { plexus_inner_frequency: a, plexus_inter_frequency: b } = templates;
  const { min, max } = calRelativeStrength(motionScope);

  let minA = b ? calTBSParamOfAMin(b) : 1;
  minA = minA < 1 ? 1 : minA;
  minA = minA > 50 ? 50 : minA;

  let maxC = a && b ? calTBSParamOfCMax(a, b) : 10;
  maxC = maxC > 10 ? 10 : maxC;
  maxC = maxC < 0 ? 0 : maxC;

  return [
    {
      key: 'relative_strength',
      rules: { min: min, max: max, step: 1, precision: 0 },
      disabled: false,
      status: '',
      validator: async () => Promise.resolve(''),
    },
    {
      key: 'plexus_inner_frequency',
      rules: { min: minA, max: 100, step: 1, precision: 0 },
      disabled: false,
      status: '',
      validator: async () => Promise.resolve(''),
    }, // a
    {
      key: 'plexus_inter_frequency',
      rules: { min: 0.1, max: 25, step: 0.1, precision: 1 },
      disabled: false,
      status: '',
      validator: async () => Promise.resolve(''),
    }, // b
    {
      key: 'plexus_inner_pulse_count',
      rules: { min: maxC < 1 ? 0 : 1, max: maxC, step: 1, precision: 0 },
      disabled: false,
      status: '',
      validator: async () => Promise.resolve(''),
    }, // c
    {
      key: 'plexus_count',
      rules: { min: 1, max: 32767, step: 1, precision: 0 },
      disabled: false,
      status: '',
      validator: async () => Promise.resolve(''),
    }, // d
    {
      key: 'strand_pulse_count',
      rules: { min: 1, max: 32767, step: 1, precision: 0 },
      disabled: false,
      status: '',
      validator: async () => Promise.resolve(''),
    }, // e
    {
      key: 'intermission_time',
      rules: { min: 1, max: 600, step: 1, precision: 0 },
      disabled: false,
      status: '',
      validator: async () => Promise.resolve(''),
    }, // f
  ];
};

const calNormalRules = (motionScope: number): FieldType[] => {
  const { min, max } = calRelativeStrength(motionScope);

  return [
    {
      key: 'relative_strength',
      rules: { min: min, max: max, step: 1, precision: 0 },
      disabled: false,
      status: '',
      validator: async () => Promise.resolve(''),
    },
    {
      key: 'strand_pulse_frequency',
      rules: { min: 0.1, max: 100, step: 0.1, precision: 1 },
      disabled: false,
      status: '',
      validator: async () => Promise.resolve(''),
    }, // a
    {
      key: 'inner_strand_pulse_count',
      rules: { min: 2, max: 32767, step: 1, precision: 0 },
      disabled: false,
      status: '',
      validator: async () => Promise.resolve(''),
    }, // c
    {
      key: 'strand_pulse_count',
      rules: { min: 1, max: 32767, step: 1, precision: 0 },
      disabled: false,
      status: '',
      validator: async () => Promise.resolve(''),
    }, // e
    {
      key: 'intermission_time',
      rules: { min: 1, max: 600, step: 1, precision: 0 },
      disabled: false,
      status: '',
      validator: async () => Promise.resolve(''),
    }, // f
  ];
};

const setDisabled = (fields: FieldType[], templates: PlanStimulusModel) => {
  return fields.map(v => {
    return {
      ...v,
      disabled: v.key === 'intermission_time' && templates.strand_pulse_count === 1,
    };
  });
};

const setEmpty = (fields: FieldType[], templates: PlanStimulusModel): { newFields: FieldType[]; isError: boolean } => {
  let isEmpty = false;
  let newFields = fields.map(v => {
    let currentEmpty = !v.disabled && templates[v.key] === null;
    if (currentEmpty) {
      isEmpty = true;
    }

    return {
      ...v,
      status: currentEmpty ? 'error' : '',
      validator: async () => (currentEmpty ? Promise.reject('不可为空') : Promise.resolve('')),
    };
  });

  return {
    newFields: newFields as FieldType[],
    isError: isEmpty,
  };
};

const setRange = (fields: FieldType[], templates: PlanStimulusModel): { newFields: FieldType[]; isError: boolean } => {
  let isRangeError = false;
  let newFields = fields.map(v => {
    const { min, max, precision } = v.rules;
    const value = templates[v.key];
    let currentIsRangeError = false;
    if (value === undefined || value === null) {
      currentIsRangeError = false;
    } else {
      currentIsRangeError = !v.disabled && (value < min || value > max);
      if (checkNumberTail(value) > precision) {
        currentIsRangeError = true;
      }
      if (currentIsRangeError) {
        isRangeError = true;
      }
    }

    return {
      ...v,
      status: currentIsRangeError ? 'error' : '',
      validator: async () => (currentIsRangeError ? Promise.reject('不符合限制') : Promise.resolve('')),
    };
  });

  return {
    newFields: newFields as FieldType[],
    isError: isRangeError,
  };
};

const setPulseTotalRange = (
  fields: FieldType[],
  templates: PlanStimulusModel
): {
  newFields: FieldType[];
  isError: boolean;
} => {
  const { pulse_total } = calTbsChartData(templates);
  const typeLabel = templates.type === EnumPlanStimulusType.TBS ? 'TBS' : 'RTMS';

  let isRangeError = false;
  let newFields = fields.map(v => {
    let isCurrentError = pulse_total > 65535 && errorCount65535[typeLabel].includes(v.key);
    if (isCurrentError) {
      isRangeError = true;
    }

    return {
      ...v,
      status: isCurrentError ? 'error' : '',
      validator: async () => (isCurrentError ? Promise.reject('脉冲超限') : Promise.resolve('')),
    };
  });

  return {
    newFields: newFields as FieldType[],
    isError: isRangeError,
  };
};

const setTreatmentTimeRange = (
  fields: FieldType[],
  templates: PlanStimulusModel
): {
  newFields: FieldType[];
  isError: boolean;
} => {
  const { treatment_time } = calTbsChartData(templates);
  let isRangeError = false;
  let newFields = fields.map(v => {
    let isCurrentError = (treatment_time > 65535 || treatment_time <= 3) && getErrorTimeLabel(templates).includes(v.key);
    if (isCurrentError) {
      isRangeError = true;
    }

    return {
      ...v,
      status: isCurrentError ? 'error' : '',
      validator: async () => (isCurrentError ? Promise.reject('时长超限') : Promise.resolve('')),
    };
  });

  return {
    newFields: newFields as FieldType[],
    isError: isRangeError,
  };
};
/**
 * 验证功率超限
 * @param fields 前面步骤加工过的字段，不过既然走到这一步了，那其实这里肯定是正确的数据
 * @param templates  刺激参数
 * @param motionThreshold  阈值
 */
const setPowerRange = (
  fields: FieldType[],
  templates: PlanStimulusModel,
  motionThreshold: number
): {
  newFields: FieldType[];
  isError: boolean;
} => {
  if (motionThreshold === 0) {
    return {
      newFields: fields,
      isError: false,
    };
  }
  const activeStrength = motionThreshold ? getMathRoundMotionThreshold(motionThreshold, templates) : 1;

  let isPowerError = cTBSParamRules(templates, activeStrength) === ParamRulesType.ParamRulesError;
  let newFields = fields.map(v => {
    let isCurrentError = false;
    if (isPowerError && getParamsRuleFields(templates).includes(v.key)) {
      isCurrentError = true;
    }
    if (isCurrentError) {
      isPowerError = true;
    }

    return {
      ...v,
      status: isCurrentError ? 'error' : '',
      validator: async () => (isCurrentError ? Promise.reject('功率超限') : Promise.resolve('')),
    };
  });

  return {
    newFields: newFields as FieldType[],
    isError: isPowerError,
  };
};

export const getFields = (templates: PlanStimulusModel, motionScope: number): FieldType[] => {
  const { type } = templates;
  let fields = [];
  if (type === EnumPlanStimulusType.RTMS) {
    fields = calNormalRules(motionScope);
  } else {
    fields = calTBSRules(templates, motionScope);
  }

  return fields;
};

const setName = (key: string, preName?: string) => {
  return preName ? `${preName}.${key}` : key;
};
export const calRules = (templates: PlanStimulusModel, motionScope: number, preName?: string, isTechSupport?: boolean): FieldType[] => {
  let fields = getFields(templates, motionScope);
  fields = setDisabled(fields, templates);

  // 验证空逻辑
  const { newFields, isError } = setEmpty(fields, templates);
  if (isError) {
    return newFields.map(v => ({ ...v, name: setName(v.key, preName) }));
  }

  // 验证范围逻辑
  const { newFields: newFieldsRange, isError: isErrorRange } = setRange(newFields, templates);
  if (isErrorRange) {
    return newFieldsRange.map(v => ({ ...v, name: setName(v.key, preName) }));
  }
  const isComplete = changeISComplete(templates, templates.type);
  if (!isComplete) {
    return fields.map(v => ({ ...v, name: setName(v.key, preName) }));
  }
  // 验证脉冲超限逻辑
  const { newFields: newFieldsPulseTotal, isError: isErrorPulseTotal } = setPulseTotalRange(fields, templates);
  if (isErrorPulseTotal) {
    return newFieldsPulseTotal.map(v => ({ ...v, name: setName(v.key, preName) }));
  }

  // 验证时长超限逻辑
  const { newFields: newFieldsTreatmentTime, isError: isErrorTreatmentTime } = setTreatmentTimeRange(fields, templates);
  if (isErrorTreatmentTime) {
    return newFieldsTreatmentTime.map(v => ({ ...v, name: setName(v.key, preName) }));
  }

  // 这里的逻辑冗余了，如果非技术支持不计算功率超限，那么可以将技术支持的阈值在这里设置为 0更好
  if (!isTechSupport) {
    // 验证功率超限,由于RTMS 和 TBS 中公用一套逻辑，而参数中存在，串脉冲频率和 丛内频率同时传的情况，所以数据清洗一次
    const newTemplate = pickFields(templates);
    const { newFields: newFieldsPower, isError: isErrorPower } = setPowerRange(fields, newTemplate, motionScope);
    if (isErrorPower) {
      return newFieldsPower.map(v => ({ ...v, name: setName(v.key, preName) }));
    }
  }

  return fields.map(v => ({ ...v, name: setName(v.key, preName) }));
};

export const calRelativeStrengthRangeByActive = (active_strength: number, powerOverValue: number) => {
  const activeStrengthMin = Math.floor(Math.max(active_strength - active_strength * 0.15, 1));
  const activeStrengthMax = Math.floor(Math.min(active_strength + active_strength * 0.15, 150, powerOverValue));

  return { min: Math.floor(activeStrengthMin), max: Math.floor(activeStrengthMax) };
};
