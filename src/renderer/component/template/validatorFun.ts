import { EnumPlanStimulusType } from '@/common/types';
import { calTbsChartData, getMathRoundMotionThreshold } from '@/renderer/component/template/calTemplate';
import { IntlShape } from 'react-intl';
import { cTBSParamRules, ParamRulesType } from '@/renderer/component/template/ctbsParamsRules';
import { calRules } from '@/renderer/component/template/calRules';
import { TbsFieldType } from '@/renderer/component/template/index';

export const errorCount65535 = {
  TBS: ['plexus_count', 'strand_pulse_count', 'plexus_inner_pulse_count'],
  RTMS: ['inner_strand_pulse_count', 'strand_pulse_count'],
};
export const errorTime65535 = {
  TBS: ['plexus_inner_frequency', 'plexus_inter_frequency', 'plexus_inner_pulse_count', 'plexus_count', 'strand_pulse_count', 'intermission_time'],
  RTMS: ['strand_pulse_frequency', 'inner_strand_pulse_count', 'strand_pulse_count', 'intermission_time'],
};

export const getErrorTimeLabel = (stimulate: any) => {
  const typeLabel = stimulate.type === EnumPlanStimulusType.TBS ? 'TBS' : 'RTMS';
  let labels = errorTime65535[typeLabel];
  if (stimulate.strand_pulse_count === 1) {
    return labels.filter((item: string) => item !== 'intermission_time');
  }

  return labels;
};

export const validator65535 = async (stimulate: any, keyParam: string, intl: IntlShape) => {
  const { pulse_total, treatment_time } = calTbsChartData(stimulate);
  const typeLabel = stimulate.type === EnumPlanStimulusType.TBS ? 'TBS' : 'RTMS';

  if (pulse_total > 65535 && errorCount65535[typeLabel].includes(keyParam)) {
    return new Promise((resolve, reject) => {
      return reject(intl.formatMessage({ id: '脉冲超限' }));
    });
  } else if ((treatment_time > 65535 || treatment_time < 3) && getErrorTimeLabel(stimulate).includes(keyParam)) {
    return new Promise((resolve, reject) => {
      return reject(intl.formatMessage({ id: '时长超限' }));
    });
  }

  return Promise.resolve();
};

export const validatorStrength = async (motionThreshold: number, stimulate: any, keyParam: string, intl: IntlShape) => {
  const mathRoundMotionThreshold = motionThreshold ? getMathRoundMotionThreshold(motionThreshold, stimulate) : 1;
  if ((mathRoundMotionThreshold > 100 || mathRoundMotionThreshold < 1) && keyParam === 'relative_strength') {
    return new Promise((resolve, reject) => {
      return reject(intl.formatMessage({ id: '不符合限制' }));
    });
  } else {
    return Promise.resolve();
  }
};

export const paramsRulesFields = [
  'plexus_count',
  'active_strength',
  'relative_strength',
  'plexus_inner_frequency',
  'plexus_inner_pulse_count',
  'inner_strand_pulse_count',
  'plexus_inter_frequency',
  'strand_pulse_frequency',
  'strand_pulse_count',
  'intermission_time',
];

export const getParamsRuleFields = (stimulate: any) => {
  if (stimulate.strand_pulse_count === 1) {
    return paramsRulesFields.filter((item: string) => item !== 'intermission_time');
  }

  return paramsRulesFields;
};
export const validatorCTBSRules = async (motionThreshold: number, stimulate: any, keyParam: string, intl: IntlShape) => {
  if (motionThreshold === 0) {
    return Promise.resolve();
  }
  const mathRoundMotionThreshold = motionThreshold ? getMathRoundMotionThreshold(motionThreshold, stimulate) : 1;
  if (cTBSParamRules(stimulate, mathRoundMotionThreshold) === ParamRulesType.MotionThresholdError) {
    return new Promise((resolve, reject) => {
      return reject(intl.formatMessage({ id: '绝对强度超限' }));
    });
  }

  if (cTBSParamRules(stimulate, mathRoundMotionThreshold) === ParamRulesType.ParamRulesError) {
    return new Promise((resolve, reject) => {
      return reject(intl.formatMessage({ id: '功率超限' }));
    });
  }

  return Promise.resolve();
};
export const checkNumberTail = (value: number) => {
  const parts = value.toString().split('.');

  return parts.length > 1 ? parts[1].length : 0;
};
export const validatorCommonRules = async (
  value: any,
  minValue: number,
  maxValue: number,
  precision: number,
  intl: IntlShape
): Promise<string | void> => {
  if (value === '' || value === null || value === undefined) {
    return Promise.reject(intl.formatMessage({ id: '不可为空' }));
  }
  const numberValue = Number(value);

  return new Promise((resolve, reject) => {
    if (checkNumberTail(numberValue) > precision) {
      return reject(intl.formatMessage({ id: '不符合限制' }));
    }
    if (numberValue < minValue) {
      return reject(intl.formatMessage({ id: '不符合限制' }));
    }
    if (numberValue > maxValue) {
      return reject(intl.formatMessage({ id: '不符合限制' }));
    }

    resolve();
  });
};
// 脉冲串数为1时，刺激间隔时间为空，不为1是，刺激间隔时间不为空
export const validatorIntermissionTime = async (stimulateTemplate: any, keyParam: string, intl: IntlShape): Promise<string | void> => {
  const standPulseCount_1 = stimulateTemplate.strand_pulse_count === 1;
  if (standPulseCount_1) {
    return Promise.resolve();
  }
  if (!stimulateTemplate.intermission_time) {
    return Promise.reject(intl.formatMessage({ id: '不可为空' }));
  }

  return Promise.resolve();
};

export const validatorOnlyIntermissionTimeNull = (stimulateTemplate: any, intl: IntlShape): boolean => {
  let renderFields: TbsFieldType[] = calRules(stimulateTemplate, 0);
  const fieldObj = renderFields
    .map(v => v.key)
    .filter((v: any) => {
      return v !== 'intermission_time';
    });

  return (
    fieldObj.every(v => {
      return stimulateTemplate[v] && stimulateTemplate[v] > 0;
    }) && !stimulateTemplate.intermission_time
  );
};
