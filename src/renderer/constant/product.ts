import { isDev } from '@/common/lib/env/nodeEnv';

// 需要注意，main 和 render 是两个线程，所以在main 中能直接读的数据，在render 中需要通过ipc 来读
export let product = {
  isNav: true,
  power: 50,
};
export type ProductType = {
  name: string;
  version: string;
  power: boolean;
  hasTherapy: boolean;
  appImage: string;
  extList: string[];
};
export const initProduct = async () => {
  if (isDev) {
    product.power = 70;
  } else {
    let power = await window.systemAPI.getEnv('power');
    product.power = power;
  }
};
const getNAV = async () => {
  if (isDev) return;
  const isNav = await window.systemAPI.getNAV();
  if (isNav) {
    product.isNav = true;
  } else {
    product.isNav = false;
  }
};
// eslint-disable-next-line @typescript-eslint/no-floating-promises
getNAV();
