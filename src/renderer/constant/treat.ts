export const warnKeys = ['upper_limit', 'lower_limit', 'water_pump', 'liquid_level', 'cooling_sheet', 'fan'];
export const faultKeys = ['coil_id', 'overheat', 'temperature_probe', 'timeout', 'turn_on', 'charging'];

export const warnText = {
  upper_limit: '水冷温度超上限',
  lower_limit: '水冷温度超下限',
  water_pump: '水泵问题',
  liquid_level: '液压问题',
  cooling_sheet: '制冷片问题',
  fan: '风扇问题',
};

export const faultText = {
  coil_id: '无线圈',
  overheat: '拍子超温',
  temperature_probe: '温度探头故障',
  timeout: '水冷超时',
  turn_on: '充电箱故障',
  charging: '放电箱故障',
};

export const pickParam = [
  'target_id',
  'horizontal',
  'type',
  'relative_strength',
  'strand_pulse_count',
  'strand_pulse_frequency',
  'pulse_total',
  'treatment_time',
  'inner_strand_pulse_count',
  'plexus_inner_frequency',
  'plexus_inter_frequency',
  'plexus_count',
  'intermission_time',
  'plexus_inner_pulse_count',
];
