import { Setting } from '@/common/types';

export const cameraInfoParams = {
  id: 11310,
};

export const paramsSet = {
  id: 10200,
  cx: 26.5,
  cy: 126.84,
  cz: 10.32,
  msg: 'start',
};

export const registerParams = {
  id: 10214,
  opt: 1,
  uid: 'img2',
  width: 520,
  height: 500,
  framer: 15,
  quality: 90,
  msg: 'start',
};

export const stopRegisterParams = {
  id: 10214,
  opt: 0,
  uid: 'img2',
  width: 520,
  height: 500,
  framer: 15,
  quality: 90,
  msg: 'end',
};

// 接受图片socket param
export const getImageParams = {
  id: 10203,
  opt: 1,
  uid: 'img2',
  width: 410,
  height: 230,
  framer: 15,
  quality: 90,
  msg: 'start',
};
// 停止图片socket param
export const stopGetImageParams = {
  id: 10203,
  opt: 0,
  uid: 'img2',
  width: 410,
  height: 306,
  framer: 15,
  quality: 90,
  msg: 'stop',
};

export const getDistanceParams = {
  id: 10207,
  opt: 1,
  uid: 'img2',
  framer: 15,
  msg: 'start',
};
export const stopDistanceParams = {
  id: 10207,
  opt: 0,
  uid: 'img2',
  framer: 15,
  msg: 'end',
};

export const imgSocketConfig = {
  URL: '',
  TOOL_BECAXYZ: [
    [0, 75, 0],
    [15, 0, 0],
    [0, -80, 0],
    [30, 0, 0],
    [0, 0, 0],
  ], // 拍子上四个点，离被标定点空间坐标值,
  TOOL_CAXYZ: [
    [-29.04, 57.43, 14.5],
    [45.96, 57.43, 29.5],
    [125.96, 57.43, 14.5],
    [45.96, 57.43, 44.5],
    [45.96, 57.43, 14.5],
  ], // 标定工具，针脚空间坐标值
};

/* 10200的请求响应体*/
export const enum Response_10202_Enum {
  id = 10202, // 响应id
  success = 1, // 注册成功
  panelMissing = 2 // 面板矩阵缺失（黑白缺失都报此错）
}

const getImgSocketURL = async () => {
  const configUrl: Setting = await window.systemAPI.getSetting();
  imgSocketConfig.URL = configUrl.IMAGE_SOCKET_URL;
  if (configUrl?.TOOL_BECAXYZ) {
    imgSocketConfig.TOOL_BECAXYZ = configUrl?.TOOL_BECAXYZ;
  }
  if (configUrl?.TOOL_CAXYZ) {
    imgSocketConfig.TOOL_CAXYZ = configUrl?.TOOL_CAXYZ;
  }
};
// eslint-disable-next-line @typescript-eslint/no-floating-promises
getImgSocketURL();

export enum IPC_MODULE{
  CUT = 0,  // 切割
  NATIVE = 1, // 原始
  COMPLETION = 2, // 补全
  NONE = 3, // 无
}
