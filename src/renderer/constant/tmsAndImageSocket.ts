export const CAMERA_STATUS_KEYS = [
  'camerargbstatus',
  'cameradepthstatus',
  'camerairstatus',
  'facedetection',
  'facekeypoints',
  'treatboard',
  'caboard',
  'facepointcloud',
];

export const treatResultMap = {
  0x01: {
    key: 0x01,
    content: '无效响应',
    science: '用户请求后未处理该指令返回（无效，时序不对）',
  },
  0xa0: {
    key: 0xa0,
    content: '强度超限',
    science: '范围：1~100',
  },
  0xa1: {
    key: 0xa1,
    content: '从内频率超限',
    science: '范围：1~100Hz（协议中100~10000）',
  },
  0xa2: {
    key: 0xa2,
    content: '从间频率超限',
    science: '范围：0.1~100Hz（协议中10~10000）',
  },
  0xa3: {
    key: 0xa3,
    content: '从内个数超限',
    science: '范围：1~10',
  },
  0xa4: {
    key: 0xa4,
    content: '串内从数超限',
    science: '范围：1~32767',
  },
  0xa5: {
    key: 0xa5,
    content: '串数超限',
    science: '范围：1~32767',
  },
  0xa6: {
    key: 0xa6,
    content: '间歇时间超限',
    science: '范围：1~600S',
  },
  0xa7: {
    key: 0xa7,
    content: '方案参数不匹配',
    science: '从内频率、从内个数、从间频率关系：从内个数  < 从内频率 / 从间频率',
  },
  0xa8: {
    key: 0xa8,
    content: '方案个数不匹配',
    science: '总个数 = 串数*串内从数*从内个数',
  },
  0xa9: {
    key: 0xa9,
    content: '方案时长不匹配',
    science: '总时长 = （串数-1）*（（串内从数*从间周期）+间歇时间）+（串内从数-1）*从间周期+（从内个数-1）*从内周期',
  },
  0xaa: {
    key: 0xa8,
    content: '线圈页面超限',
    science: '范围：0~4',
  },
  0xe1: {
    key: 0xe1,
    content: '帧头错误',
    science: '0XEE错误帧相应',
  },
  0xe2: {
    key: 0xe2,
    content: '帧长错误',
    science: '0XEE错误帧相应',
  },
  0xe3: {
    key: 0xe3,
    content: '索引错误',
    science: '0XEE错误帧相应',
  },
  0xe4: {
    key: 0xe4,
    content: '指令错误',
    science: '0XEE错误帧相应',
  },
  0xe5: {
    key: 0xe5,
    content: '帧尾错误',
    science: '0XEE错误帧相应',
  },
  0xe6: {
    key: 0xe6,
    content: 'CRC错误',
    science: '0XEE错误帧相应',
  },
};
