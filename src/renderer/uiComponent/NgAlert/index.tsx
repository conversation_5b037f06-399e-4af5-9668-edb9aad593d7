import React, { PropsWithChildren } from 'react';
import { Alert, AlertProps } from 'antd';
import styles from './index.module.less';
import { ReactComponent as Close } from '@/renderer/static/svg/close.svg';
import classnames from 'classnames';

type Props = Omit<AlertProps, 'description'> & PropsWithChildren & {bizClass?: string};
const NgAlert = ({ children, bizClass, ...res }: Props) => {
  return (
    <div className={classnames(styles.ng_alert, res.className, bizClass)}>
      <Alert closeIcon={<Close />} {...res} />
    </div>
  );
};
export default NgAlert;
