@import '../../static/style/baseColor.module.less';
.ng_alert {
  :global {
    .ant-alert-info {
      background: @colorA5;
      border: 0;
      color: @colorA11;
      font-family: normal-font;
    }
    .ant-alert-icon {
      margin-top: 5px;
      font-size: 14px;
      margin-inline-end: 10px;
      align-self: flex-start;
    }
    .ant-alert-message {
      color: @colorA12;
      font-size: 16px;
      font-family: normal-font;
      line-height: 1.7;
    }
    .ant-alert {
      padding: 14px;
      box-sizing: border-box;
      max-width: 400px;
    }
    .ant-alert-warning {
      background: fade(@colorB1, 40%);
      border-color: @colorB1;
      & .ant-alert-close-icon {
        &:hover {
          background: fade(@colorB1, 40%);
        }
      }
    }
    .ant-alert-error {
      background: fade(@colorB3, 40%);
      border-color: @colorB3;
      & .ant-alert-close-icon {
        &:hover {
          background: fade(@colorB3, 40%);
        }
      }
    }
    .ant-alert-success {
      background: fade(@colorB2, 40%);
      border-color: @colorB2;
      & .ant-alert-close-icon {
        &:hover {
          background: fade(@colorB2, 40%);
        }
      }
    }
    .ant-alert-close-icon {
      cursor: url('@/renderer/static/svg/cursor.cur'), pointer;

      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      width: 22px;
      height: 22px;
      border-radius: 4px;
      align-self: flex-start;
      & svg {
        position: absolute;
        top: 50%;
        bottom: 50%;
        transform: translate(0, -50%);
        font-size: 12px !important;
      }
    }
  }
}
