@import '../../static/style/baseColor.module.less';

.ng_inputNumber {
  user-select: none;
  width: 100%;
  border: 1px solid @colorA5;
  background: @colorA4_1;
  padding: 2px 4px;
  border-radius: 4px;
  height: 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  &:hover {
    background: @colorA4;
  }
  &:focus-visible {
    outline: 1px solid @colorC4;
  }
  &_focus {
    border: 1px solid @colorC4;
    background-color: @colorA5;
  }
  &_error {
    border: 1px solid @colorB3;
  }

  .left_minus {
    display: inline-flex;
    width: 24px;
    height: 24px;
    background-image: url('../../static/images/minus.png');
    background-repeat: no-repeat;
    background-color: transparent;
    background-position: center;
    border: none;
    border-radius: 3px;
    cursor: url('@/renderer/static/svg/cursor.cur'), pointer;

    &:hover {
      background-color: @colorA13;
    }
  }
  .right_plus {
    display: inline-flex;
    width: 24px;
    height: 24px;
    background-image: url('../../static/images/plus.png');
    background-repeat: no-repeat;
    background-color: transparent;
    background-position: center;
    border: none;
    border-radius: 3px;
    cursor: url('@/renderer/static/svg/cursor.cur'), pointer;

    &:hover {
      background-color: @colorA13;
    }
  }

  .input_container {
    flex: 1;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    border: none;

    :global {
      .ant-input-number {
        width: 100%;
      }
      .ant-input-number-input {
        color: @colorA12;
        text-align: center;
      }
      .ant-input-number-input-wrap {
        border: none;
        background: transparent;
      }

      .ant-input-number-disabled .ant-input-number-input {
        color: @colorA9;
      }
    }
  }

  &_disabled {
    background-color: @colorA6;

    &:hover {
      background-color: @colorA6;
    }

    .left_minus {
      &:hover {
        background-color: @colorA6;
        cursor: not-allowed;
      }
    }
    .right_plus {
      &:hover {
        cursor: not-allowed;
        background-color: @colorA6;
      }
    }
  }
}
