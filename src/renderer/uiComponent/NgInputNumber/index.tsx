import React, { useEffect, useState } from 'react';
import { InputNumber, InputNumberProps } from 'antd';
import { useMount, useUpdateEffect } from 'ahooks';
import classnames from 'classnames';
import styles from './index.module.less';
import _ from 'lodash';

type Props = InputNumberProps & {
  min?: number;
  max?: number;
  isInvalid?: boolean;
  overlayclass?: any;
  value?: number;
};
export const NgInputNumber = (props: Props) => {
  const [isFocus, setIsFocus] = useState(false);
  const { step = 1, min = Number.MIN_SAFE_INTEGER, max = Number.MAX_SAFE_INTEGER, onChange } = props;
  const [currentValue, setCurrentValue] = useState<number>(() => {
    if (props.defaultValue) {
      return props.defaultValue as number;
    }
    if (props.value) {
      return props.value;
    }

    return 0;
  });
  useEffect(() => {
    if (props.disabled) {
      setCurrentValue(0);
    } else {
      setCurrentValue(props.value || 0);
    }
  }, [props.disabled]);
  useEffect(() => {
    if (props.isInvalid) {
      setCurrentValue(0);
    } else {
      setCurrentValue(props.value || 0);
    }
  }, [props.isInvalid]);
  const onMinus = () => {
    if (props.disabled) return;
    setIsFocus(true);
    let cloneCurrentValue = _.clone(currentValue);
    if (!cloneCurrentValue) {
      cloneCurrentValue = 0;
    }
    if (cloneCurrentValue - Number(step) < min) {
      return;
    }
    let newValue = Number((cloneCurrentValue - Number(step)).toFixed(step === 0.1 ? 1 : 0));
    setCurrentValue(newValue);
    onChange?.(newValue);
  };
  const onPlus = () => {
    if (props.disabled) return;
    setIsFocus(true);
    let cloneCurrentValue = _.clone(currentValue);
    if (!cloneCurrentValue) {
      cloneCurrentValue = 0;
    }
    if (cloneCurrentValue + Number(step) > max) {
      return;
    }
    let newValue = Number((cloneCurrentValue + Number(step)).toFixed(step === 0.1 ? 1 : 0));
    setCurrentValue(newValue);
    onChange?.(newValue);
  };

  const onPressEnter = (e: React.KeyboardEvent<HTMLInputElement>) => {
    // @ts-ignore
    let value = e.target.value;
    if (value === '') {
      setCurrentValue(0);
      onChange?.(0);

      return;
    }
    value = Number(value);
    if (isNaN(value)) {
      setCurrentValue(0);
      onChange?.(null);

      return;
    }
    if (value < min) {
      setCurrentValue(min ? min : Number.MIN_SAFE_INTEGER);
      onChange?.(min);

      return;
    }
    if (value > max) {
      setCurrentValue(max ? max : Number.MAX_SAFE_INTEGER);
      onChange?.(max);

      return;
    }
    setCurrentValue(value);
    onChange?.(value);
  };

  useMount(() => {
    if (props.defaultValue) {
      setCurrentValue(props.defaultValue as number);
    }
  });

  useUpdateEffect(() => {
    setCurrentValue(props.value || 0);
  }, [props.value]);

  return (
    <div
      // @ts-ignore
      // eslint-disable-next-line jsx-a11y/tabindex-no-positive
      tabIndex={'1'}
      className={classnames(
        styles.ng_inputNumber,
        props.overlayclass,
        isFocus ? styles.ng_inputNumber_focus : '',
        props.status === 'error' ? styles.ng_inputNumber_error : '',
        props.disabled ? styles.ng_inputNumber_disabled : '',
        props.className,
        'ng-number-input'
      )}
      onBlur={() => setIsFocus(false)}
    >
      <div className={styles.left_minus} onFocus={() => setIsFocus(true)} onClick={onMinus} />
      <div className={styles.input_container} onFocus={() => setIsFocus(true)} onBlur={() => setIsFocus(false)}>
        <InputNumber variant='borderless' controls={false} onPressEnter={onPressEnter} {..._.omit(props, ['isInvalid'])} />
      </div>
      <div className={styles.right_plus} onFocus={() => setIsFocus(true)} onClick={onPlus} />
    </div>
  );
};
