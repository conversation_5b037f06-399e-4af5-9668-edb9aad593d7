@import '../../static/style/baseColor.module.less';
.ng_progress {
  :global {
    .ant-progress-line {
      display: flex;
      flex-direction: column-reverse;

      .ant-progress-outer {
        margin-inline-end: unset;
        padding-inline-end: unset;
      }
    }
    .ant-progress .ant-progress-text {
      color: @colorA12;
      width: 100%;
      text-align: end;
      padding-inline-end: 8px;
    }
    .ant-progress.ant-progress-circle .ant-progress-text {
      color: @colorA12;
      text-align: center;
      padding-inline-end: unset;
      font-size: 32px;
    }
    .ant-progress-circle{
      .ant-progress-circle-trail, .ant-progress-circle-path{
        stroke-linecap: round !important;
      }
    }
  }
}

.ng_progress_upload {
  height: auto;
  background: @colorA4;
  border-radius: 4px;
  :global {
    .ant-progress-line {
      display: flex;
      flex-direction: column-reverse;

      .ant-progress-outer {
        margin-inline-end: unset;
        padding-inline-end: unset;
        line-height: 8px;
      }
    }
    .ant-progress .ant-progress-text {
      color: @colorA12;
      width: 100%;
      text-align: start;
      height: 32px;
      line-height: 32px;
      font-size: 32px;
    }
  }

  .ng_progress_upload_content {
    display: flex;
    flex-direction: row;
    font-family: normal-font !important;
    .ng_progress_upload_content_percent {
      color: @colorC2;
      width: auto;
    }
    .ng_progress_upload_content_fileName {
      color: @colorA12;
      width: auto;
      margin-inline-start: 8px;
    }
  }
}

.ng_strength_progress {
  position: relative;
  width: 120px;
  height: 60px;
  transform: translate(0px, 30px);
  .footer_container {
    display: flex;
    flex-direction: column-reverse;
    background: @colorA5;

    .overlay {
      position: absolute;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: @colorC1;
      background-image: linear-gradient(90deg, #4eb7b9, #426d77);
      transform-origin: top center;
      z-index: 1;
    }
    .center_container {
      background: @colorA3;
      display: flex;
      justify-content: center;
      padding-top: 13px;
      margin-left: 6px;
      font-size: 20px;
      font-weight: bold;
      color: @colorC2;
      z-index: 10;
    }
  }
  .min_max_container {
    position: relative;
    background: @colorA3;
    z-index: 20;
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    .min_value {
      padding-top: 2px;
    }
    .max_value {
      padding-top: 2px;
    }
  }
}
