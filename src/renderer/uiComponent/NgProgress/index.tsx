import React, { useEffect } from 'react';
import { Progress, ProgressProps } from 'antd';
import styles from './index.module.less';
import _ from 'lodash';
import classNames from 'classnames';
import { useMount } from 'ahooks';
type Props = ProgressProps & { uploadFileName?: string };
export const NgProgress = (props: Props) => {
  useMount(() => {
    const progress = document.querySelector('.ant-progress-text');
    progress?.setAttribute('title', '');
  });

  useEffect(() => {
    const progress = document.querySelector('.ant-progress-text');
    progress?.setAttribute('title', '');
  }, [props.percent]);

  return (
    <div className={styles.ng_progress}>
      <Progress strokeColor={'#4EB7B9'} trailColor={'#2C2C3C'} {...props} />
    </div>
  );
};

export const NgProgressUpload = (props: Props) => {
  const progressProps = _.omit(props, ['uploadFileName']);
  const renderContent = (percent?: number): React.ReactNode => {
    if (percent === undefined) {
      return (
        <div className={styles.ng_progress_upload_content}>
          <div className={styles.ng_progress_upload_content_percent}>0%</div>
          <div className={styles.ng_progress_upload_content_fileName}>{props.uploadFileName}</div>
        </div>
      );
    }

    return (
      <div className={styles.ng_progress_upload_content}>
        <div className={styles.ng_progress_upload_content_percent}>{percent}%</div>
        <div className={styles.ng_progress_upload_content_fileName}>{props.uploadFileName}</div>
      </div>
    );
  };

  return (
    <div className={styles.ng_progress_upload}>
      <Progress strokeColor={'#4EB7B9'} trailColor={'#8C8C8C'} format={renderContent} {...progressProps} />
    </div>
  );
};

type StrengthProgressProps = {
  percent: number;
  radius: number;
  ng_strength_progress?: string;
  customBackgroundColorClass?: string;
  min_value?: number;
  max_value?: number;
};
export const NgStrengthProgress = (props: StrengthProgressProps) => {
  const strength_status = `rotate(${((props.percent - (props.min_value || 1)) * (180 / ((props.max_value || 1) - (props.min_value || 1))))}deg)`;
  const common_status = `rotate(${props.percent * 1.8}deg)`;

  return (
    <div className={classNames(styles.ng_strength_progress, props.ng_strength_progress)}>
      <div
        className={styles.footer_container}
        style={{
          width: props.radius * 2,
          height: props.radius,
          borderRadius: `${props.radius}px ${props.radius}px 0 0`,
        }}
      >
        <div
          className={styles.overlay}
          style={{
            top: props.radius,
            borderRadius: `0 0 ${props.radius}px ${props.radius}px `,
            transform: props.min_value && props.max_value ? strength_status : common_status,
          }}
        />
        <div
          className={classNames(styles.center_container, props.customBackgroundColorClass)}
          style={{
            width: props.radius * 1.2,
            height: (props.radius * 1.2) / 2,
            borderRadius: `${(props.radius * 1.2) / 2}px ${(props.radius * 1.2) / 2}px 0 0`,
          }}
        >
          {props.percent}%
        </div>
      </div>
      <div
        className={classNames(styles.min_max_container, props.customBackgroundColorClass)}
        style={{
          width: props.radius * 2,
          height: props.radius,
        }}
      >
        <div className={styles.min_value}>{props.min_value || 1}</div>
        <div className={styles.max_value}>{props.max_value || 100}</div>
      </div>
    </div>
  );
};
