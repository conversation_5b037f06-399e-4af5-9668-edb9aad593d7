import React, { PropsWithChildren } from 'react';
import { ButtonProps } from 'antd';
import NgButton from '@/renderer/uiComponent/NgButton';
import styles from './index.module.less';
import classnames from 'classnames';
type Props = ButtonProps & PropsWithChildren & { authName?: string };
const NgDarkButton = ({ children, ...res }: Props) => {
  return (
    <div className={classnames(styles.ng_dark_button, res.className || '')}>
      <NgButton {...res}>{children}</NgButton>
    </div>
  );
};
export default NgDarkButton;
