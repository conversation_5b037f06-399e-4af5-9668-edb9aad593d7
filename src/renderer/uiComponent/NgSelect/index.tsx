import React, { useMemo } from 'react';
import { Select, SelectProps } from 'antd';
import classnames from 'classnames';
import styles from './index.module.less';
export const NgSelect = (props: SelectProps) => {
  const memoOptions = useMemo(() => {
    // 应设计需要，隐藏所有option原生的title属性
    return props?.options?.map(option => {
      option.title = '';

      return { ...option };
    });
  }, [props.options]);

  return (
    <Select
      {...props}
      options={memoOptions}
      className={classnames(props.className, styles.ng_select)}
      popupClassName={classnames(props.popupClassName, 'ng_select_popup_name')}
    />
  );
};
