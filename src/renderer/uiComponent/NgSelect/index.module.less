@import '../../static/style/baseColor.module.less';

.ng_select {
  width: 100%;
  background-color: @colorA5 !important;
  border-radius: 6px;
  cursor: url('@/renderer/static/svg/cursor.cur'), pointer !important;
  &:hover {
    background-color: @colorA4 !important;
  }
  :global {
    .ant-select-selector {
      box-shadow: unset !important;
      background-color: transparent !important;
      border-color: @colorA5 !important;
      padding: 0 16px !important;
      font-family: normal-font, sans-serif !important;
      .ant-select-selection-search {
        inset-inline-start: unset;
        inset-inline-end: unset;
      }
      input#rc_select_0 {
        color: #ffffff;
      }
    }
    .ant-select-arrow {
      background: url('../../static/images/arrow.png') no-repeat center center;
      cursor: url('@/renderer/static/svg/cursor.cur'), pointer !important;
      width: 20px;
      height: 20px;
      margin-top: initial;
      transform: translateY(-50%);
      inset-inline-start: unset;
      right: 6px;
      &:hover {
        background-color: @colorA4 !important;
      }
      & > span {
        display: none;
      }
    }
  }
}
:global {
  .ant-select-disabled {
    background-color: @colorA6 !important;

    .ant-select-selection-item {
      color: @colorA9 !important;
    }

    &:hover {
      background-color: @colorA6 !important;
    }
  }
  .ant-select-item-option-state {
    .anticon-check {
      display: none !important;
    }
  }
}
