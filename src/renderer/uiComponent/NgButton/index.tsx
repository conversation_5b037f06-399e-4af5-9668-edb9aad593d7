import React, { useRef } from 'react';
import { Button, ButtonProps } from 'antd';
import styles from './index.module.less';
import classnames from 'classnames';
import { ReactComponent as Loading } from '@/renderer/static/svg/loading.svg';
import { AuthProps, withAuth } from '../../hocComponent/withAuth';
import { sendRenderLog } from '../../utils/renderLogger';

type Props = ButtonProps &
AuthProps & {
  // default：页面展示 confirm：在modal弹窗 popover：比如删除的操作在当前按钮上弹出的提示
  buttonMode?: 'default' | 'popover';
  authName?: string;
  isNothrottle?: boolean;
};

const executeOnClick = (e: any, onClick: (React.MouseEventHandler<HTMLAnchorElement> & React.MouseEventHandler<HTMLButtonElement>) | undefined) => {
  if (!onClick) return;
  try {
    onClick(e);
  } catch (err) {
    sendRenderLog.error(err as string);
  }
};
export const NgButton = withAuth(
  ({ isNothrottle, children, buttonMode = 'default', loading, icon, onClick, authName, disabled, authState = {}, ...rest }: Props) => {
    const disable = !!(authName && authState[authName] === 1);
    const none = !!(authName && authState[authName] === 0);
    const abortClickRef = useRef<boolean>(false);

    const handleClick = async (e: any) => {
      if (!onClick) return;
      if (isNothrottle) {
        return executeOnClick(e, onClick);
      }
      if (abortClickRef.current) return;
      abortClickRef.current = true;
      try {
        await Promise.resolve(onClick(e));
      } catch (error: any) {
        sendRenderLog.error(error);
      } finally {
        abortClickRef.current = false;
      }
    };

    return (
      <div
        className={classnames(styles.ng_btn, rest.className, {
          [styles.display_none]: none,
        })}
      >
        <Button
          icon={loading ? <Loading /> : icon}
          {...rest}
          disabled={disable || disabled}
          className={classnames({
            [styles.defaultBtn]: buttonMode === 'default',
            [styles.popoverBtn]: buttonMode === 'popover',
            [styles.rotateLoading]: loading,
          })}
          onClick={handleClick}
        >
          {/*  包两层span可以避免antd 5 Button中文中多出一个空格的问题*/}
          <span style={{ marginLeft: loading ? 8 : 0 }}>{children}</span>
        </Button>
      </div>
    );
  }
);
export default NgButton;
