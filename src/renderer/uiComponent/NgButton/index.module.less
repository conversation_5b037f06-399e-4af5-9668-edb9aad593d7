@import '../../static/style/baseColor.module.less';
.ng_btn {
  width: fit-content;
  .defaultBtn {
    padding: 4px 12px;
    min-width: 80px;
    height: 32px;
    box-sizing: border-box;
  }
  .popoverBtn {
    min-width: 60px;
    height: 28px;
    padding: 4px 8px;
    line-height: 0;
    box-sizing: border-box;
  }
  .rotateLoading {
    & svg {
      animation: rotate 1s linear infinite;
    }
  }

  :global {
    // 删除按钮点击时的动画，剔除添加动画的div
    // 过滤掉button children为div的情况 <NgButton><div>OK</div></NgButton>
    & button div:not(span div) {
      display: none !important;
    }
    .ant-btn:not(:disabled):focus-visible {
      outline: none !important;
    }
    .ant-btn {
      outline: none !important;
      cursor: url('@/renderer/static/svg/cursor.cur'), pointer;
      padding: 4px 12px !important;
    }
    // 为了和NgButton样式统一，当NgPopover组件children为NgButton时，强制指定padding
    .ant-btn:hover {
      cursor: url('@/renderer/static/svg/cursor.cur'), pointer;
      padding: 4px 12px !important;
    }

    .ant-btn-default {
      font-family: normal-font, serif !important;
      border-radius: 6px;
      background: @colorC4;
      border: 0;
      color: @colorA1;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .ant-btn-default:not(:disabled):hover {
      background: @colorC1;
      color: @colorA1;
      cursor: url('../../static/svg/cursor.cur'), pointer !important;
    }
    .ant-btn-default:disabled {
      color: @colorA9;
      background: @colorA6;
      background-color: @colorA6;
      border: 0;
      cursor: url('../../static/svg/disableMouse.cur'), not-allowed !important;
    }
    .ant-btn-default.ant-btn-background-ghost:not(:disabled):hover {
      color: @colorC4;
      border-color: @colorC4;
      background: transparent;
    }
    .ant-btn-background-ghost {
      border: 1px solid @colorA10;
      color: @colorA10;
      background: transparent;
    }
    .ant-btn.ant-btn-loading {
      opacity: 1;
      background: @colorC2;
    }
  }

  &.display_none {
    display: none;
  }
}
@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(90deg);
  }
  50% {
    transform: rotate(180deg);
  }
  75% {
    transform: rotate(270deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
