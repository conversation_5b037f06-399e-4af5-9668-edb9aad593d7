@import '../../static/style/baseColor.module.less';

.ng_steps {
  padding-top: 16px;
  cursor: default;
  :global {
    // .ant-steps-item-wait > .ant-steps-item-container:hover .ant-steps-item-content > .ant-steps-item-title {
    //   color: #A1A1A8;
    // }
    .ant-steps-label-vertical .ant-steps-item-content {
      margin-top: 8px;
      cursor: default;
    }
    .ant-steps-item-title {
      font-size: 14px;
      line-height: 22px;
      height: 22px;
    }
    .ant-steps-item-icon {
      width: 22px;
      height: 22px;
      line-height: 22px;
      border-radius: 22px;
      font-size: 12px;
      cursor: default;
    }
    .ant-steps.ant-steps-label-vertical .ant-steps-item-tail {
      margin-inline-start: 53px;
      padding: 0px 8px;
      top: 10px;
    }
    .ant-steps.ant-steps-label-vertical .ant-steps-item-tail::after {
      height: 2px;
    }
    .ant-steps-item-container {
      cursor: default;
    }

    .ant-steps-item {
      &-finish > .ant-steps-item-container > .ant-steps-item-content > .ant-steps-item-title {
        color: @colorA11;
        // :hover{
        //   color: @colorC4;
        // }
      }

      &-finish > .ant-steps-item-container > .ant-steps-item-content > .ant-steps-item-description {
        color: @colorA11;
        // :hover{
        //   color: @colorC4;
        // }
      }

      &-finish > .ant-steps-item-container > .ant-steps-item-tail::after {
        background-color: @colorC4;
      }

      // &-finish > .ant-steps-item-container[role='button']:hover {
      //   .ant-steps-item-icon{
      //     border-color: @colorC4;
      //   }
      //   .ant-steps-item-content > .ant-steps-item-title{
      //     color: @colorC4;
      //   }
      // }

      // &-finish > .ant-steps-item-container:hover {
      //   .ant-steps-item-icon{
      //     background-color: @colorC4;
      //     border-color: @colorC4;
      //   }
      //   .ant-steps-item-content > .ant-steps-item-title{
      //     color: @colorC4;
      //   }
      // }

      &-finish > .ant-steps-item-container > .ant-steps-item-icon {
        background-color: @colorC4;
        border-color: @colorC4;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        .ant-steps-icon {
          color: @colorA12;
          .ant-steps-finish-icon {
            svg {
              display: none;
            }
            background-image: url('../../static/images/symbols/success.png');
            background-size: contain;
            width: 12px;
            height: 12px;
          }
        }
      }

      &-process {
        & > .ant-steps-item-container > .ant-steps-item-content {
          cursor: default;
        }
        & > .ant-steps-item-container > .ant-steps-item-content > .ant-steps-item-title {
          color: @colorA9;
        }
        & > .ant-steps-item-container > .ant-steps-item-content > .ant-steps-item-description {
          color: @colorA9;
        }
        & > .ant-steps-item-container > .ant-steps-item-tail::after {
          background-color: @colorC2;
        }
        & > .ant-steps-item-container > .ant-steps-item-icon {
          background-color: @colorC3;
          border-color: @colorC3;
          cursor: default;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          .ant-steps-icon {
            color: @colorA1;
          }
          ::after {
            content: '';
            position: absolute;
            width: 22px;
            height: 16px;
            background-image: url('../../static/images/symbols/invertTriangle.png');
            background-repeat: no-repeat;
            background-position-x: center;
            top: -24px;
            left: -7px;
          }
        }
      }

      &-wait {
        & > .ant-steps-item-container > .ant-steps-item-content > .ant-steps-item-title {
          color: @colorA8;
        }
        & > .ant-steps-item-container > .ant-steps-item-content > .ant-steps-item-description {
          color: @colorA8;
        }
        & > .ant-steps-item-container > .ant-steps-item-tail::after {
          background-color: @colorA6;
        }
        & > .ant-steps-item-container > .ant-steps-item-icon {
          background-color: @colorA5;
          border-color: @colorA6;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          .ant-steps-icon {
            color: @colorA9;
          }
        }

        // & > .ant-steps-item-container[role='button']:hover {
        //   .ant-steps-item-icon{
        //     border-color: @colorA9;
        //     .ant-steps-icon{
        //       color: @colorA12;
        //     }
        //   }
        //   .ant-steps-item-content > .ant-steps-item-title{
        //     color: @colorA8;
        //   }
        // }

        // & > .ant-steps-item-container:hover {
        //   .ant-steps-item-icon{
        //     background-color: @colorA9;
        //     border-color: @colorA9;
        //     .ant-steps-icon{
        //       color: @colorA12;
        //     }
        //   }
        //   .ant-steps-item-content > .ant-steps-item-title{
        //     color: @colorA8;
        //   }
        // }
      }
    }

    .ant-steps-item:not(.ant-steps-item-active):not(.ant-steps-item-process) > .ant-steps-item-container[role='button']:hover .ant-steps-item-icon {
      border-color: unset;
    }
    .ant-steps-item:not(.ant-steps-item-active):not(.ant-steps-item-process)
      > .ant-steps-item-container[role='button']:hover
      .ant-steps-item-icon
      .ant-steps-icon {
      color: @colorA11;
    }
  }
}
