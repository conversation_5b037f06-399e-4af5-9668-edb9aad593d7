import React from 'react';
import { Steps, StepsProps } from 'antd';
import styles from './index.module.less';
import classnames from 'classnames';

type Props = StepsProps & { overlayClassName?: string };
export const NgSteps = (props: Props) => {
  const { overlayClassName, ...stepProps } = props;

  return (
    <div className={classnames(styles.ng_steps, overlayClassName)}>
      <Steps labelPlacement={'vertical'} {...stepProps} />
    </div>
  );
};
