import React, { useCallback, useRef, useState } from 'react';
import type { DraggableData, DraggableEvent } from 'react-draggable';
// eslint-disable-next-line no-duplicate-imports
import Draggable from 'react-draggable';
import { NgIcon } from '@/renderer/uiComponent/NgIcon';
import { BrainBarOpen } from '@/renderer/uiComponent/SvgGather';
import styles from './index.module.less';
import { brainConfig, BrainConfigType, zoomInfo } from './config';
import { NgSlider } from '@/renderer/uiComponent/NgSlider';
import _ from 'lodash';
import classnames from 'classnames';
type Props = {
  location: string;
  iconClick(field: string): void;
  onChangeScalpOpacity(value: number): void;
  onChangeScalpZoom(value: number): void;
};

// eslint-disable-next-line max-lines-per-function
const NgBrain = (props: Props) => {
  const [currentBrainConfig, setCurrentBrainConfig] = useState<BrainConfigType[]>(brainConfig);
  const [showLocation, setShowLocation] = useState(false);
  const [showScalpOpacity, setShowScalpOpacity] = useState(false);
  const [showVolumeGray, setShowVolumeGray] = useState(false);
  const [bounds, setBounds] = useState({ left: 0, top: 0, bottom: 0, right: 0 });
  const draggleRef = useRef<HTMLDivElement>(null);

  const onStart = (_event: DraggableEvent, uiData: DraggableData) => {
    const { clientWidth, clientHeight } = window.document.documentElement;
    const targetRect = draggleRef.current?.getBoundingClientRect();
    if (!targetRect) {
      return;
    }
    setBounds({
      left: -targetRect.left + uiData.x,
      right: clientWidth - (targetRect.right - uiData.x),
      top: -targetRect.top + uiData.y,
      bottom: clientHeight - (targetRect.bottom - uiData.y),
    });
  };

  const changeActiveChildren = useCallback(
    (field: string) => {
      let newBrainConfig = _.cloneDeep(currentBrainConfig);
      props.iconClick(field);
      newBrainConfig[0].children = currentBrainConfig[0].children!.map((item: BrainConfigType) => {
        return {
          ...item,
          isActive: item.field === field,
        };
      });
      setCurrentBrainConfig(newBrainConfig);
    },
    [currentBrainConfig]
  );

  const changeActive = useCallback((field: string) => {
    let newBrainConfig = currentBrainConfig.map((item: BrainConfigType) => {
      if (item.hasOwnProperty('isActive')) {
        return {
          ...item,
          isActive: item.field === field,
        };
      }

      return item;
    });
    setCurrentBrainConfig(newBrainConfig);
  }, []);

  const handleIconClick = useCallback(
    (e: React.MouseEvent<HTMLSpanElement>, field: string) => {
      switch (field) {
        case 'BrainDirectionGather':
          changeActive(field);
          setShowLocation(!showLocation);
          setShowScalpOpacity(false);
          break;
        case 'BrainOpacity':
          changeActive(field);
          setShowLocation(false);
          setShowScalpOpacity(!showScalpOpacity);
          break;
        case 'BrainVolumeGray':
          changeActive(field);
          setShowLocation(false);
          setShowScalpOpacity(false);
          setShowVolumeGray(!showVolumeGray);
          break;
        default:
          changeActive(field);
          props.iconClick(field);
          closeTooltip();
      }
    },
    [showScalpOpacity, showLocation, showVolumeGray]
  );

  const closeTooltip = useCallback(() => {
    setShowLocation(false);
    setShowScalpOpacity(false);
    setShowVolumeGray(false);
  }, []);

  const handleScalpOpacity = useCallback((value: number) => {
    props.onChangeScalpOpacity(value);
  }, []);

  const handleScalpZoom = useCallback((value: number) => {
    props.onChangeScalpZoom(value);
  }, []);

  const renderLocation = () => {
    return (
      <div className={styles.location_wrap} style={{ display: showLocation ? 'block' : 'none' }}>
        <div className={styles.location_content}>
          {currentBrainConfig[0].children?.map((item: BrainConfigType, index) => {
            return (
              <div
                key={`${item.field}_${index}`}
                onClick={e => changeActiveChildren(item.field)}
                className={classnames(styles.location_content_item, item.isActive ? styles.location_content_item_active : '')}
              >
                <NgIcon iconSvg={item.iconSvg} />
                <span className={styles.location_content_item_field}>{item.tooltip?.title as string}</span>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  const renderScalpOpacity = () => {
    return (
      <div
        className={styles.scalp_opacity_wrap}
        onBlur={() => setShowScalpOpacity(false)}
        onFocus={() => setShowScalpOpacity(true)}
        style={{ display: showScalpOpacity ? 'block' : 'none' }}
      >
        <div className={styles.scalp_opacity_container}>
          <NgSlider min={0} max={100} defaultValue={50} isGray onChange={handleScalpOpacity} overlayClassName={styles.ng_brain_slider} />
        </div>
      </div>
    );
  };

  const renderVolumeGray = () => {
    return (
      <div
        className={styles.volume_gray_wrap}
        onBlur={() => setShowVolumeGray(false)}
        onFocus={() => setShowVolumeGray(true)}
        style={{ display: showVolumeGray ? 'block' : 'none' }}
      >
        <div className={styles.volume_gray_container}>
          <NgSlider min={0} max={100} defaultValue={50} isGray onChange={handleScalpZoom} overlayClassName={styles.ng_brain_slider} />
        </div>
      </div>
    );
  };

  return (
    <Draggable bounds={bounds} onStart={(event, uiData) => onStart(event, uiData)} defaultClassNameDragging={styles.dragging_wrap}>
      <div className={styles.ng_brain_modal}>
        <div
          ref={draggleRef}
          className={styles.header}
          // onMouseOver={() => {
          //   if (disabled) {
          //     setDisabled(false);
          //   }
          // }}
          // onMouseOut={() => {
          //   setDisabled(true);
          // }}
        >
          <BrainBarOpen fontSize={14} />
        </div>
        <div className={styles.content}>
          {currentBrainConfig.map((item: BrainConfigType) => {
            return (
              <NgIcon
                key={item.field}
                fontSize={18}
                iconSvg={item.iconSvg}
                isActive={item.isActive}
                tooltip={item.tooltip}
                onClick={e => handleIconClick(e, item.field)}
              />
            );
          })}
        </div>
        <div className={styles.divider}>
          {[1, 2, 3, 4, 5, 6, 7, 8].map(node => {
            return <div key={node} className={styles.divider_item} />;
          })}
        </div>
        <div className={styles.zoom}>
          <NgIcon iconSvg={zoomInfo.iconSvg} fontSize={18} tooltip={zoomInfo.tooltip} onClick={e => handleIconClick(e, 'ZoomInfo')} />
        </div>
        <div className={styles.float}>
          {renderLocation()}
          {renderScalpOpacity()}
          {renderVolumeGray()}
        </div>
      </div>
    </Draggable>
  );
};

export default NgBrain;
