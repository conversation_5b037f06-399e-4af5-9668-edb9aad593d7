@import '../../static/style/baseColor.module.less';
.ng_brain_modal {
  width: 32px;
  height: auto;
  border: 1px solid @colorA5;
  border-radius: 6px 6px 6px 6px;
  display: flex;
  flex-direction: column;
  .header {
    height: 14px;
    line-height: 10px;
    padding-left: 4px;
    cursor: move;
    background: @colorA2;
    border-bottom: 1px solid @colorA5;
  }
  .content {
    display: flex;
    flex-direction: column;
    padding: 4px 4px;
    background: @colorA13;
    position: relative;
    :nth-child(-n + 3) {
      margin-bottom: 4px;
      :hover {
        background: @colorA2;
      }
    }

    :last-child {
      margin-bottom: 0;
    }

    .is_active {
      background: @colorA2;
    }
  }
  .divider {
    padding: 2px 0px 1px 0px;
    width: 100%;
    display: inline-flex;
    justify-content: center;
    background: @colorA13;
    .divider_item {
      height: 3px;
      width: 1px;
      border: 1px solid @colorA3;
    }
  }

  .zoom {
    padding: 4px 4px;
    background: @colorA13;

    :first-child {
      :hover {
        background: @colorA2;
      }
    }
  }

  .scalp_opacity_wrap {
    position: absolute;
    width: 150px;
    height: auto;
    top: 50px;
    left: 40px;

    .scalp_opacity_container {
      .ng_brain_slider {
        font-size: 12px;
        .start {
          font-size: 12px;
        }
      }
    }
  }

  .volume_gray_wrap {
    position: absolute;
    width: 150px;
    height: auto;
    top: 80px;
    left: 40px;

    .volume_gray_container {
      .ng_brain_slider {
        font-size: 12px;
        .start {
          font-size: 12px;
        }
      }
    }
  }
  .location_wrap {
    position: absolute;
    width: auto;
    height: auto;
    border: 1px solid @colorA5;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    top: 24px;
    left: 40px;

    .location_content {
      display: flex;
      flex-direction: column;
      padding: 2px 2px;
      background: @colorA13;
      position: relative;

      .location_content_item {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        height: 24px;
        line-height: 24px;
        padding: 0 6px 0px 2px;
        cursor: pointer;
        border-radius: 2px;
        margin-top: 2px;
        margin-bottom: 2px;
        :hover {
          background: @colorA3;
        }

        &_field {
          font-size: 12px;
          height: 24px;
          line-height: 21px;
          background: transparent;
        }

        &_active {
          background: @colorA3;
        }
      }

      .location_content_item:hover {
        background: @colorA3;
      }
    }
  }
}

.ng_brain_bar_tooltip {
  background: @colorA13;
  border-radius: 4px;

  :global {
    .ant-tooltip-arrow {
      display: none;
    }
    .ant-tooltip .ant-tooltip-inner {
      border-radius: 4px;
    }
    .ant-tooltip-inner {
      background-color: @colorA13;
      font-size: 12px;
      min-height: 20px;
      height: 20px;
      line-height: 16px;
      padding: 2px 4px;
      color: @colorA12;
    }
  }
}
