import {
  BrainVolumeGray,
  BrainOpacity,
  BrainDirectionGather,
  BrainLocationBottom,
  BrainLocationLeft,
  BrainLocationRight,
  BrainLocationTop,
  BrainLocationFront,
  BrainLocationBack,
  CircleDoubt,
} from '@/renderer/uiComponent/SvgGather';
import { TooltipPlacement } from 'antd/es/tooltip';
import styles from './index.module.less';
import React from 'react';
import { CustomIconComponentProps } from '@ant-design/icons/lib/components/Icon';
import { TooltipProps } from 'antd';

export type BrainConfigType = {
  field: string;
  iconSvg: React.ComponentType<CustomIconComponentProps | React.SVGProps<SVGSVGElement>>;
  tooltip?: TooltipProps;
  children?: BrainConfigType[];
  isActive?: boolean;
};
export const brainConfig = [
  {
    field: 'BrainDirectionGather',
    iconSvg: BrainDirectionGather,
    isActive: false,
    tooltip: {
      title: '大脑分区',
      placement: 'right' as TooltipPlacement,
      overlayClassName: styles.ng_brain_bar_tooltip,
    },
    children: [
      {
        field: 'BrainLocationFront',
        iconSvg: BrainLocationFront,
        isActive: false,
        tooltip: {
          title: '前',
          placement: 'right' as TooltipPlacement,
          overlayClassName: styles.ng_brain_bar_tooltip,
        },
      },
      {
        field: 'BrainLocationBack',
        iconSvg: BrainLocationBack,
        isActive: false,
        tooltip: {
          title: '后',
          placement: 'right' as TooltipPlacement,
          overlayClassName: styles.ng_brain_bar_tooltip,
        },
      },
      {
        field: 'BrainLocationTop',
        iconSvg: BrainLocationTop,
        isActive: false,
        tooltip: {
          title: '上',
          placement: 'right' as TooltipPlacement,
          overlayClassName: styles.ng_brain_bar_tooltip,
        },
      },
      {
        field: 'BrainLocationBottom',
        iconSvg: BrainLocationBottom,
        isActive: false,
        tooltip: {
          title: '下',
          placement: 'right' as TooltipPlacement,
          overlayClassName: styles.ng_brain_bar_tooltip,
        },
      },
      {
        field: 'BrainLocationLeft',
        iconSvg: BrainLocationLeft,
        isActive: false,
        tooltip: {
          title: '左',
          placement: 'right' as TooltipPlacement,
          overlayClassName: styles.ng_brain_bar_tooltip,
        },
      },
      {
        field: 'BrainLocationRight',
        iconSvg: BrainLocationRight,
        isActive: false,
        tooltip: {
          title: '右',
          placement: 'right' as TooltipPlacement,
          overlayClassName: styles.ng_brain_bar_tooltip,
        },
      },
    ],
  },
  {
    field: 'BrainOpacity',
    iconSvg: BrainOpacity,
    tooltip: {
      title: '调节不透明度',
      placement: 'right' as TooltipPlacement,
      overlayClassName: styles.ng_brain_bar_tooltip,
    },
  },
  {
    field: 'BrainVolumeGray',
    iconSvg: BrainVolumeGray,
    tooltip: {
      title: '调节灰度',
      placement: 'right' as TooltipPlacement,
      overlayClassName: styles.ng_brain_bar_tooltip,
    },
  },
];

export const zoomInfo = {
  field: 'BrainZoomInfo',
  iconSvg: CircleDoubt,
  tooltip: {
    title: '缩放提示',
    placement: 'right' as TooltipPlacement,
    overlayClassName: styles.ng_brain_bar_tooltip,
  },
};
