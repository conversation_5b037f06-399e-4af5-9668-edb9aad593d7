import React from 'react';
import { Drawer, DrawerProps } from 'antd';
import styles from './index.module.less';
import classNames from 'classnames';

type Props = {};
const NgDrawer = (props: Props & DrawerProps) => {
  return (
    <div className={styles.ng_drawer}>
      <Drawer {...props} className={classNames(styles.drawer, props.className)} rootClassName={classNames(styles.rootDrawer, props.rootClassName)}>
        {props.children}
      </Drawer>
    </div>
  );
};
export default NgDrawer;
