@import '@/renderer/static/style/baseColor.module.less';
.ng_drawer {
  position: relative;
}
.rootDrawer {
  :global {
    .ant-drawer-content-wrapper {
      min-width: 400px;
    }
  }
}
.drawer {
  :global {
    .ant-drawer-title {
      color: @colorA12;
      font-weight: 500;
      font-family: normal-font, serif;
      font-size: 16px;
    }
    .ant-drawer-header,
    .ant-drawer-body {
      padding: 20px 20px 20px 40px;
      background: @colorA4;
      border: 0;
    }
    .ant-drawer-body {
      padding: 5px 20px 20px 40px;
    }
    .ant-drawer-close {
      position: absolute;
      right: 0;
      cursor: url('@/renderer/static/svg/cursor.cur'), pointer;
      & span {
        width: 20px;
        height: 20px;
        background: url('@/renderer/static/svg/close.svg');
        cursor: url('@/renderer/static/svg/cursor.cur'), pointer;
        &:hover {
          background: @colorA5 url('@/renderer/static/svg/close.svg') no-repeat center center;
        }
        & svg {
          display: none;
        }
      }
    }
  }
}
