import { ReactComponent as AllowClear } from '@/renderer/static/svg/allowClear.svg';
import { ReactComponent as Bat } from '@/renderer/static/svg/Bat.svg';
import { ReactComponent as BrainAxesX } from '@/renderer/static/svg/BrainAxesX.svg';
import { ReactComponent as BrainAxesY } from '@/renderer/static/svg/BrainAxesY.svg';
import { ReactComponent as BrainAxesZ } from '@/renderer/static/svg/BrainAxesZ.svg';
import { ReactComponent as BrainBarClose } from '@/renderer/static/svg/BrainBarClose.svg';
import { ReactComponent as <PERSON>BarOpen } from '@/renderer/static/svg/BrainBarOpen.svg';
import { ReactComponent as BrainDirectionGather } from '@/renderer/static/svg/BrainDirectionGather.svg';
import { ReactComponent as BrainLayout } from '@/renderer/static/svg/BrainLayout.svg';
import { ReactComponent as BrainLocationBack } from '@/renderer/static/svg/BrainLocationBack.svg';
import { ReactComponent as BrainLocationFront } from '@/renderer/static/svg/BrainLocationFront.svg';
import { ReactComponent as BrainLocationLeft } from '@/renderer/static/svg/BrainLocationLeft.svg';
import { ReactComponent as BrainLocationRight } from '@/renderer/static/svg/BrainLocationRight.svg';
import { ReactComponent as BrainLocationTop } from '@/renderer/static/svg/BrainLocationTop.svg';
import { ReactComponent as BrainLocationBottom } from '@/renderer/static/svg/BrainLocationBottom.svg';
import { ReactComponent as BrainVolumeGray } from '@/renderer/static/svg/BrainVolumeGray.svg';
import { ReactComponent as BrainReset } from '@/renderer/static/svg/BrainReset.svg';
import { ReactComponent as BrainSagittal } from '@/renderer/static/svg/BrainSagittal.svg';
import { ReactComponent as BrainCoronal } from '@/renderer/static/svg/BrainCoronal.svg';
import { ReactComponent as BrainAxial } from '@/renderer/static/svg/BrainAxial.svg';
import { ReactComponent as BrainScalpSmooth } from '@/renderer/static/svg/BrainScalpSmooth.svg';
import { ReactComponent as BrainOpacity } from '@/renderer/static/svg/BrainOpacity.svg';
import { ReactComponent as BrainZoomReset } from '@/renderer/static/svg/BrainZoomReset.svg';
import { ReactComponent as Camera } from '@/renderer/static/svg/Camera.svg';
import { ReactComponent as CalendarBlank } from '@/renderer/static/svg/CalendarBlank.svg';
import { ReactComponent as CalendarCheck } from '@/renderer/static/svg/CalendarCheck.svg';
import { ReactComponent as CalendarPlus } from '@/renderer/static/svg/CalendarPlus.svg';
import { ReactComponent as CircleDoubt } from '@/renderer/static/svg/CircleDoubt.svg';
import { ReactComponent as CircleDotsThree } from '@/renderer/static/svg/CircleDotsThree.svg';
import { ReactComponent as CircleDotsThreeVertical } from '@/renderer/static/svg/CircleDotsThreeVertical.svg';
import { ReactComponent as CircleMinus } from '@/renderer/static/svg/CircleMinus.svg';
import { ReactComponent as CirclePlus } from '@/renderer/static/svg/CirclePlus.svg';
import { ReactComponent as CirclePlusBack } from '@/renderer/static/svg/circlePlusBack.svg';
import { ReactComponent as CloseLight } from '@/renderer/static/svg/CloseLight.svg';
import { ReactComponent as CloseGray } from '@/renderer/static/svg/CloseGray.svg';
import { ReactComponent as ContainerMax } from '@/renderer/static/svg/ContainerMax.svg';
import { ReactComponent as Control } from '@/renderer/static/svg/Control.svg';
import { ReactComponent as Copy } from '@/renderer/static/svg/Copy.svg';
import { ReactComponent as Cursor } from '@/renderer/static/svg/cursor.svg';
import { ReactComponent as Delete } from '@/renderer/static/svg/Delete.svg';
import { ReactComponent as Edit } from '@/renderer/static/svg/Edit.svg';
import { ReactComponent as EditUnderline } from '@/renderer/static/svg/EditUnderline.svg';
import { ReactComponent as Eye } from '@/renderer/static/svg/Eye.svg';
import { ReactComponent as EyeSlash } from '@/renderer/static/svg/EyeSlash.svg';
import { ReactComponent as Filter } from '@/renderer/static/svg/Filter.svg';
import { ReactComponent as Folder } from '@/renderer/static/svg/Folder.svg';
import { ReactComponent as FolderClose } from '@/renderer/static/svg/FolderClose.svg';
import { ReactComponent as FolderOpen } from '@/renderer/static/svg/FolderOpen.svg';
import { ReactComponent as ImportTemplate } from '@/renderer/static/svg/ImportTemplate.svg';
import { ReactComponent as loading } from '@/renderer/static/svg/loading.svg';
import { ReactComponent as Lock } from '@/renderer/static/svg/Lock.svg';
import { ReactComponent as CircleGlassMinus } from '@/renderer/static/svg/CircleGlassMinus.svg';
import { ReactComponent as CircleGlassPlus } from '@/renderer/static/svg/CircleGlassPlus.svg';
import { ReactComponent as Patient } from '@/renderer/static/svg/Patient.svg';
import { ReactComponent as Pulse } from '@/renderer/static/svg/Pulse.svg';
import { ReactComponent as Rect } from '@/renderer/static/svg/Rect.svg';
import { ReactComponent as Setting } from '@/renderer/static/svg/Setting.svg';
import { ReactComponent as Setting24 } from '@/renderer/static/svg/setting_24.svg';
import { ReactComponent as Shutdown } from '@/renderer/static/svg/Shutdown.svg';
import { ReactComponent as ToggleLeft } from '@/renderer/static/svg/ToggleLeft.svg';
import { ReactComponent as ToggleRight } from '@/renderer/static/svg/ToggleRight.svg';
import { ReactComponent as TriangleWarn } from '@/renderer/static/svg/TriangleWarn.svg';
import { ReactComponent as Upload } from '@/renderer/static/svg/Upload.svg';
import { ReactComponent as WifiHigh } from '@/renderer/static/svg/WifiHigh.svg';
import { ReactComponent as WifiSlash } from '@/renderer/static/svg/WifiSlash.svg';
import { ReactComponent as EmptyNoPatient } from '@/renderer/static/svg/emptyNoPatient.svg';
import { ReactComponent as EmptyNoData } from '@/renderer/static/svg/emptyNoData.svg';
import { ReactComponent as EmptyNoTask } from '@/renderer/static/svg/emptyNoTask.svg';
import { ReactComponent as EmptyPageFail } from '@/renderer/static/svg/emptyPageFail.svg';
import { ReactComponent as EmptyTaskFail } from '@/renderer/static/svg/emptyTaskFail.svg';
import { ReactComponent as EmptyLoadingFail } from '@/renderer/static/svg/emptyLoadingFail.svg';
import { ReactComponent as EmptyAddTask } from '@/renderer/static/svg/emptyAddTask.svg';
import { ReactComponent as EmptyUnCheckTask } from '@/renderer/static/svg/EmptyUnCheckTask.svg';
import { ReactComponent as EmptyTbsChart } from '@/renderer/static/svg/EmptyTbsChart.svg';
import { ReactComponent as Minus } from '@/renderer/static/svg/minus.svg';
import { ReactComponent as Plus } from '@/renderer/static/svg/plus.svg';
import { ReactComponent as InfoMessage } from '@/renderer/static/svg/infoMessage.svg';
import { ReactComponent as ErrorMessage } from '@/renderer/static/svg/errorMessage.svg';
import { ReactComponent as SuccessMessage } from '@/renderer/static/svg/successMessage.svg';
import { ReactComponent as WarnMessage } from '@/renderer/static/svg/warnMessage.svg';
import { ReactComponent as SystemPower } from '@/renderer/static/svg/system_power.svg';
import { ReactComponent as SystemSetting } from '@/renderer/static/svg/system_setting.svg';
import { ReactComponent as StartTreatment } from '@/renderer/static/svg/start_treatment.svg';
import { ReactComponent as StartTreatmentDisable } from '@/renderer/static/svg/start_treatment_disable.svg';
import { ReactComponent as Measure } from '@/renderer/static/svg/measure.svg';
import { ReactComponent as MeasureDisable } from '@/renderer/static/svg/measure_disable.svg';
import { ReactComponent as EditPlan } from '@/renderer/static/svg/edit_plan.svg';
import { ReactComponent as EditPlanDisable } from '@/renderer/static/svg/edit_plan_disable.svg';
import { ReactComponent as Report } from '@/renderer/static/svg/report.svg';
import { ReactComponent as ReportDisable } from '@/renderer/static/svg/report_diable.svg';
import { ReactComponent as SortAsc } from '@/renderer/static/svg/SortAsc.svg';
import { ReactComponent as SortDefault } from '@/renderer/static/svg/SortDefault.svg';
import { ReactComponent as SortDesc } from '@/renderer/static/svg/SortDesc.svg';
import { ReactComponent as Search } from '@/renderer/static/svg/Search.svg';
import { ReactComponent as Person } from '@/renderer/static/svg/person.svg';
import { ReactComponent as BatArrow } from '@/renderer/static/svg/bat_arrow.svg';
import { ReactComponent as ResetDisable } from '@/renderer/static/svg/reset_disable.svg';
import { ReactComponent as EditDisable } from '@/renderer/static/svg/edit_disable.svg';
import { ReactComponent as InfoMiddle } from '@/renderer/static/svg/info_middle.svg';
import { ReactComponent as SuccessMiddle } from '@/renderer/static/svg/success_middle.svg';
import { ReactComponent as WarnMiddle } from '@/renderer/static/svg/warn_middle.svg';
import { ReactComponent as ErrorMiddle } from '@/renderer/static/svg/error_middle.svg';
import { ReactComponent as StimulateImportDisabled } from '@/renderer/static/svg/stimulateImportDisabled.svg';
import { ReactComponent as PreviewPlan } from '@/renderer/static/svg/preview_plan.svg';
import { ReactComponent as Filed } from '@/renderer/static/svg/filed.svg';
import { ReactComponent as DisableField } from '@/renderer/static/svg/disableField.svg';
import { ReactComponent as Restore } from '@/renderer/static/svg/restore.svg';
import { ReactComponent as Notice } from '@/renderer/static/svg/notice.svg';
import { ReactComponent as BrainBottomIcon } from '@/renderer/static/svg/brain_bottom_icon.svg';
import { ReactComponent as EmptyNotice } from '@/renderer/static/svg/empty_notice.svg';
import { ReactComponent as NgLogoIcon } from '@/renderer/static/svg/ng_logo_icon.svg';
import { ReactComponent as HomeToolBox } from '@/renderer/static/svg/new_toolbox.svg';
import { ReactComponent as DisableRestore } from '@/renderer/static/svg/disableRestore.svg';
import { ReactComponent as RefreshData } from '@/renderer/static/svg/refresh.svg';
import { ReactComponent as Hourglass } from '@/renderer/static/svg/hourglass.svg';
import { ReactComponent as YellowError } from '@/renderer/static/svg/yellow_error.svg';
import { ReactComponent as EllipsisIcon } from '@/renderer/static/svg/ellipsis.svg';
import { ReactComponent as YellowWarning } from '@/renderer/static/svg/yellow_warning.svg';
import { ReactComponent as SelectedSuccess } from '@/renderer/static/svg/selected_success.svg';
import { ReactComponent as WarningIcon } from '@/renderer/static/svg/warning.svg';
import { ReactComponent as SuccessIcon } from '@/renderer/static/svg/success.svg';
import { ReactComponent as ErrorIcon } from '@/renderer/static/svg/error.svg';
import { ReactComponent as EmptyNoInfo } from '@/renderer/static/svg/emptyNoInfo.svg';
import { ReactComponent as CreateCircle } from '@/renderer/static/svg/create_circle.svg';
import { ReactComponent as CrashIcon } from '@/renderer/static/svg/CrashIcon.svg';

export {
  RefreshData,
  DisableRestore,
  DisableField,
  HomeToolBox,
  NgLogoIcon,
  EmptyNotice,
  Notice,
  Restore,
  Filed,
  PreviewPlan,
  AllowClear,
  InfoMiddle,
  SuccessMiddle,
  WarnMiddle,
  ErrorMiddle,
  EditDisable,
  ResetDisable,
  StartTreatment,
  StartTreatmentDisable,
  Measure,
  MeasureDisable,
  EditPlan,
  EditPlanDisable,
  Report,
  ReportDisable,
  SystemPower,
  SystemSetting,
  Bat,
  BrainAxesX,
  BrainAxesY,
  BrainAxesZ,
  BrainBarClose,
  BrainBarOpen,
  BrainDirectionGather,
  BrainLayout,
  BrainLocationBack,
  BrainLocationFront,
  BrainLocationLeft,
  BrainLocationRight,
  BrainLocationTop,
  BrainLocationBottom,
  BrainVolumeGray,
  BrainReset,
  BrainSagittal,
  BrainCoronal,
  BrainAxial,
  BrainScalpSmooth,
  BrainOpacity,
  BrainZoomReset,
  Camera,
  CalendarBlank,
  CalendarCheck,
  CalendarPlus,
  CircleDoubt, // 圆形 问号
  CircleDotsThree, // 圆形 三个点
  CircleDotsThreeVertical, // 圆形 三个点 竖向
  CircleMinus, // 圆形 减号
  CirclePlus, // 圆形 加号
  CirclePlusBack, // 圆形 加号 黑色
  CloseLight, // 圆形 关闭
  CloseGray,
  ContainerMax,
  Control, // 控制器
  Copy,
  Cursor,
  Delete,
  Edit,
  EditUnderline,
  Eye,
  EyeSlash,
  Filter,
  Folder,
  FolderClose,
  FolderOpen,
  loading,
  ImportTemplate, // 导入刺激模板
  Lock,
  CircleGlassMinus,
  CircleGlassPlus,
  Patient,
  Pulse, // 脉搏 脉冲刺激
  Rect,
  Setting,
  Setting24,
  Shutdown, // 关机
  ToggleLeft, // 开关 =》 关
  ToggleRight, // 开关 =》 开
  TriangleWarn, // 三角形 警告
  Upload, // 上传
  WifiHigh, // wifi 高
  WifiSlash,
  EmptyNoPatient,
  EmptyNoData,
  EmptyNoTask,
  EmptyPageFail,
  EmptyTaskFail,
  EmptyLoadingFail,
  EmptyAddTask,
  EmptyUnCheckTask,
  EmptyTbsChart,
  Minus,
  Plus,
  InfoMessage,
  ErrorMessage,
  SuccessMessage,
  WarnMessage,
  SortAsc,
  SortDefault,
  SortDesc,
  Search,
  Person,
  BatArrow,
  StimulateImportDisabled,
  BrainBottomIcon,
  Hourglass,
  YellowError,  // 黄色背景黑色感叹号的警告提示
  EllipsisIcon,
  YellowWarning,
  SelectedSuccess,
  WarningIcon,
  SuccessIcon,
  ErrorIcon,
  EmptyNoInfo,
  CreateCircle,
  CrashIcon,
};
