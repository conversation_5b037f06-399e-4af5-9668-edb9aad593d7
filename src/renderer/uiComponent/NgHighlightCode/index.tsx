import React, { useState } from 'react';
// eslint-disable-next-line import/no-extraneous-dependencies
import { Prism as Syntax<PERSON>ighlighter, SyntaxHighlighterProps } from 'react-syntax-highlighter';
// eslint-disable-next-line import/no-internal-modules,import/no-extraneous-dependencies
import { vscDarkPlus, darcula } from 'react-syntax-highlighter/dist/cjs/styles/prism'; // 代码高亮主题风格
// eslint-disable-next-line import/no-internal-modules,import/no-extraneous-dependencies
import { a11yLight } from 'react-syntax-highlighter/dist/esm/styles/hljs'; // 代码高亮主题风格
// eslint-disable-next-line import/no-extraneous-dependencies
import { CopyToClipboard } from 'react-copy-to-clipboard';
import styles from './index.module.less';
import classnames from 'classnames';

const them = {
  dark: vscDarkPlus,
  light: a11yLight,
};

const NgHighlightCode = (props: SyntaxHighlighterProps & { title?: string }) => {
  const { children, darkMode, language = 'txt' } = props;
  const [isCopied, setIsCopied] = useState(false);
  if (typeof darkMode === 'undefined') {
    them.light = darcula;
  }
  if (typeof darkMode === 'boolean') {
    them.light = a11yLight;
  }
  const handleCopy = () => {
    setIsCopied(true);
    setTimeout(() => {
      setIsCopied(false);
    }, 1500);
  };

  return (
    <div className={styles.outside}>
      <div className={styles['code-snippet']}>
        <p className={styles['code-title']}>{props.title}</p>
        <SyntaxHighlighter
          {...props}
          showLineNumbers={props.showLineNumbers ?? true} // 是否展示左侧行数
          lineNumberStyle={{ color: '#ddd', fontSize: 10, ...props.style }} // 左侧行数的样式
          style={darkMode ? them.dark : them.light} // 主题风格
          language={language} // 需要语言类型 如css, jsx , javascript 等
          PreTag="div"
        >
          {String(children).replace(/\n$/, '')}
        </SyntaxHighlighter>
        <CopyToClipboard text={children as string} onCopy={handleCopy}>
          <button
            onClick={handleCopy}
            className={classnames(styles['copy-button'], {
              [styles['copied-text']]: isCopied,
            })}
          >
            {isCopied ? 'Copied!' : 'Copy'}
          </button>
        </CopyToClipboard>
      </div>
    </div>
  );
};

export default NgHighlightCode;
