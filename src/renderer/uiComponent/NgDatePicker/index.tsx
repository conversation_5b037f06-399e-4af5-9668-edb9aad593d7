import React from 'react';
import { DatePicker, DatePickerProps } from 'antd';
import styles from './index.module.less';
import classnames from 'classnames';
import { NgIcon } from '../NgIcon';
import { CalendarBlank } from '../SvgGather';

type Props = DatePickerProps;
export const NGDatePicker = (props: Props) => {
  return (
    <div className={classnames(styles.ng_date_picker, props.className)}>
      <DatePicker
        getPopupContainer={(triggerNode: any) => triggerNode.parentNode}
        suffixIcon={<NgIcon iconSvg={CalendarBlank} fontSize={20} />}
        allowClear={false}
        {...props}
      />
    </div>
  );
};
