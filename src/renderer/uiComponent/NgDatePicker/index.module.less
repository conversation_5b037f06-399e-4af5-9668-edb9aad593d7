@import '../../static/style/baseColor.module.less';
.ng_date_picker {
  cursor: url('@/renderer/static/svg/cursor.cur'), pointer !important;

  :global {
    .ant-picker {
      display: block;
      background-color: @colorA5;
      border-color: rgba(0, 0, 0, 0);
      cursor: url('@/renderer/static/svg/cursor.cur'), pointer !important;

      &:hover:not(.ant-picker-disabled) {
        border-color: @colorC4;
      }

      .ant-picker-input > input {
        color: #fff;
        cursor: url('@/renderer/static/svg/cursor.cur'), pointer !important;

        &::-webkit-input-placeholder {
          color: @colorA7;
        }
      }

      &.ant-picker-status-error {
        background-color: @colorA5 !important;
        border-color: @colorB3 !important;
      }

      &.ant-picker-disabled {
        background-color: @colorA6 !important;

        .ant-picker-input > input {
          color: @colorA9 !important;
        }
      }
    }

    a.ant-picker-now-btn {
      color: @colorC4 !important;
      cursor: url('@/renderer/static/svg/cursor.cur'), pointer !important;
    }

    .ant-picker-outlined:focus,
    .ant-picker-outlined:focus-within {
      border-color: @colorC4 !important;
      box-shadow: 0 0 0 0px @colorC4 !important;
    }

    .ant-picker-dropdown {
      cursor: url('@/renderer/static/svg/cursor.cur'), pointer !important;

      .ant-picker-cell-in-view.ant-picker-cell-selected:not(.ant-picker-cell-disabled) .ant-picker-cell-inner,
      .ant-picker-cell-in-view.ant-picker-cell-range-start:not(.ant-picker-cell-disabled) .ant-picker-cell-inner,
      .ant-picker-cell-in-view.ant-picker-cell-range-end:not(.ant-picker-cell-disabled) .ant-picker-cell-inner {
        background: @colorC4 !important;
      }

      .ant-picker-header,
      .ant-picker-body,
      .ant-picker-footer,
      .ant-picker-header > button {
        background-color: @colorA5;
        color: #fff;
        cursor: url('@/renderer/static/svg/cursor.cur'), pointer !important;

        & > a {
          cursor: url('@/renderer/static/svg/cursor.cur'), pointer !important;
        }
      }

      .ant-picker-cell-disabled {
        &::before {
          background-color: @colorA6;
        }

        .ant-picker-cell-inner {
          color: @colorA9;
        }
      }
      .ant-picker-header-view {
        button {
          cursor: url('@/renderer/static/svg/cursor.cur'), pointer !important;
        }
      }
      .ant-picker-today-btn,
      .ant-picker-content th,
      .ant-picker-cell-inner,
      [class^='ant-picker-header-'],
      .ant-picker-header-view [class^='ant-picker-'] {
        color: #fff;

        &:hover {
          color: #fff;
        }
      }

      .ant-picker-cell:hover {
        .ant-picker-cell-inner {
          background-color: @colorA6 !important;
        }
      }

      .ant-picker-cell-selected {
        .ant-picker-cell-inner {
          color: @colorC1;
          background-color: @colorA5;
        }
      }

      .ant-picker-cell-today .ant-picker-cell-inner {
        &::before {
          border-color: @colorC3;
        }
      }
    }

    .ant-picker-dropdown .ant-picker-cell {
      cursor: url('@/renderer/static/svg/cursor.cur'), pointer !important;
    }
  }
}
