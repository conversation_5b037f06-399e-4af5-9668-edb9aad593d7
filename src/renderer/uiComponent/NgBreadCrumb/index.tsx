import React from 'react';
import { Breadcrumb, BreadcrumbProps } from 'antd';
import styles from './index.module.less';
import classnames from 'classnames';
import _ from 'lodash';
import { useLocation, useNavigate } from 'react-router-dom';
import { formatBreadcrumb, routers, routerTitleMap } from '@/renderer/router/routers';
import { withUserSession } from '../../hocComponent/withUserSession';
import { UserSessionState } from '../../recoil/user';
import { EnumUserPageQueryModelRoleEnumList } from '@/common/types';
import { BreadcrumbItemType } from 'antd/es/breadcrumb/Breadcrumb';

type Props = BreadcrumbProps & {
  isGray: boolean;
  disable?: boolean;
  contentClassName?: string;
  supplement?: breadCrumbType;
  userSession?: UserSessionState;
};
export type breadCrumbType = BreadcrumbItemType & {
  path?: string;
  breadcrumbName?: string;
  disable?: boolean;
};

export const NgBreadcrumb = withUserSession((props: Props) => { // NOSONAR
  const navigate = useNavigate();
  const routesMap = formatBreadcrumb(routers);
  const breadCrumbProps = _.omit(props, [ 'isGray', 'contentClassName', 'userSession', 'setUserSession' ]);
  const home = {
    disable:props.disable,
    path:'/home',
    breadcrumbName:'首页',
  };

  const itemRender = (item: breadCrumbType, __: any, items: breadCrumbType[], ___: string[]) => {
    const last: boolean = items.findIndex(v => (v.path === item.path && v.breadcrumbName === item.breadcrumbName)) === items.length - 1;

    return last ? (
      <span>{item.breadcrumbName}</span>
    ) : (
      <span
        className={classnames(item.disable ? styles.disable : '', 'normal')}
        onClick={(e: any) => {
          if (!item.disable) {
            if (props.userSession && +props.userSession?.role_id === EnumUserPageQueryModelRoleEnumList.TechSupport) {
              navigate('/techsupport');
            } else if (item.onClick) {
              item.onClick(e);
            } else {
              navigate(item.path!);
            }
          }
        }}
      >{item.breadcrumbName}</span>
    );
  };

  const pathname: string = useLocation().pathname;
  const renderRoutes: breadCrumbType[] = props.supplement ?
      [
      home,
      {
        disable:props.disable,
        path:pathname,
        breadcrumbName:routesMap.find(v => v.path === pathname)?.breadcrumbName || routerTitleMap[pathname] || '',
      },
      props.supplement,
      ]
    : [
      home,
      {
        disable:props.disable,
        path:pathname,
        breadcrumbName:routesMap.find(v => v.path === pathname)?.breadcrumbName || routerTitleMap[pathname] || '',
      },
      ];

  return (
    <div className={classnames(styles.ng_breadcrumb, props.isGray ? styles.ng_breadcrumb_gray : '', props.contentClassName)}>
      <Breadcrumb separator={<span className={styles.ng_separator}/>} items={renderRoutes} itemRender={itemRender} {...breadCrumbProps} />
    </div>
  );
});
