@import '../../static/style/baseColor.module.less';

.ng_breadcrumb {
  color: @colorA8;
  display: inline-flex;
  width: 100%;
  align-items: center;
  padding-left: 8px;

  .ng_separator {
    display: inline-block;
    width: 12px;
    height: 12px;
    background: url('../../static/images/breadcrumbIcon.png') no-repeat;
  }

  :global {
    .ant-breadcrumb {
      font-size: 16px;
      color: @colorA9;
    }
    a {
      color: @colorA9;
      height: 24px;
      line-height: 24px;
    }
    .ant-breadcrumb a:hover {
      color: @colorA12;
    }

    .ant-breadcrumb .normal {
      display: inline-flex;
      height: 24px;
      line-height: 24px;
      align-items: center;
      cursor: url('@/renderer/static/svg/cursor.cur'), pointer;
      padding: 2px 8px;
      background: none;
    }

    .ant-breadcrumb .normal:hover {
      background-color: @colorA13;
      border-radius: 2px;
    }
    .ant-breadcrumb-separator {
      display: inline-flex;
      align-items: center;
      height: 24px;
      color: @colorA8;
      margin: 0px 8px 0px 8px;
    }
    .ant-breadcrumb li:last-child {
      color: @colorA12;
      font-weight: 500;
      span {
        padding: 0px 8px;
      }

      :first-child {
        display: inline-flex;
        align-items: center;
      }
      :hover {
        color: @colorA12;
        background: none;
      }
    }
  }

  &_gray {
    background: @colorA4;
    height: 32px;

    :global {
      .ant-breadcrumb .normal {
        cursor: url('@/renderer/static/svg/cursor.cur'), pointer;
      }
      .ant-breadcrumb .normal:hover {
        background-color: @colorA2;
      }
      .ant-breadcrumb a:hover {
        color: @colorA12;
      }
    }
  }
}

.disable {
  cursor: url('@/renderer/static/svg/disableMouse.cur'), pointer !important;
}
