@import '@/renderer/static/style/baseColor.module.less';

.container {
  :global {
    .ant-switch {
      cursor: url('@/renderer/static/svg/cursor.cur'), pointer;
      background-color: @colorC3;
    }

    .ant-switch:hover:not(.ant-switch-disabled) {
      background: @colorC3;
    }

    .ant-switch.ant-switch-checked {
      background: @colorC4;
    }

    .ant-switch.ant-switch-checked:hover:not(.ant-switch-disabled) {
      background: @colorC4;
    }

    & button div:not(.ant-switch-handle) {
      display: none !important;
    }

    .ant-btn:not(:disabled):focus-visible {
      outline: none !important;
    }

    .ant-switch.ant-switch-small {
      min-width: 18px;
      height: 12px;

      .ant-switch-handle {
        top: 3px;
        width: 6px;
        height: 6px;

        &::before {
          background-color: @colorA3;
        }
      }

      .ant-switch-inner {
        padding-inline-start: initial;
        padding-inline-end: initial;
      }

      &.ant-switch-checked {
        .ant-switch-handle {
          inset-inline-start: 10px !important;
        }
      }
    }
  }
}