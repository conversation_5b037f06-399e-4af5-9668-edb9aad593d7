import React from 'react';
import { Radio, RadioProps, RadioGroupProps } from 'antd';
import styles from './index.module.less';
import classnames from 'classnames';

export const NgRadio = (props: RadioProps) => {
  return <Radio {...props} className={classnames(styles.ng_radio, props.className)} />;
};

NgRadio.Group = (props: RadioGroupProps) => {
  return <Radio.Group {...props} className={classnames(styles.ng_radio_group, props.className)} />;
};
