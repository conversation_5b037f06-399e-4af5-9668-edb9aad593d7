@import '../../static/style/baseColor.module.less';

.ng_radio {
  border-color: @colorA13;
  font-size: 16px;
  cursor: url('@/renderer/static/svg/cursor.cur'), pointer !important;
  &:hover {
    :global {
      .ant-radio-inner {
        border-color: @colorC4 !important;
      }
    }
  }
  :global {
    .ant-radio {
      cursor: url('@/renderer/static/svg/cursor.cur'), pointer !important;
      .ant-radio-input {
        cursor: url('@/renderer/static/svg/cursor.cur'), pointer !important;
      }
    }
    .ant-radio-inner {
      background-color: @colorA13;
      border-color: @colorA13;
    }
    .ant-radio-checked {
      .ant-radio-inner {
        background-color: @colorA1;
        border-color: @colorA1;
        &::after {
          background-color: @colorC4;
          transform: scale(0.5);
        }
      }
    }
    .ant-radio + span {
      padding-inline-start: 12px;
    }
  }
}

.ng_radio_group {
  display: inline-flex;
  border-radius: 6px;
  :global {
    .ant-radio-button-wrapper {
      color: @colorA12;
      background: @colorA4;
      padding: 12px 40px;
      display: inline-flex;
      font-size: @font-size-base;
      align-items: center;
      justify-content: center;
      border: none;

      &.ant-radio-button-wrapper-disabled {
        // background-color: @colorA4_1;
        cursor: url('@/renderer/static/svg/disableMouse.cur'), not-allowed !important;
      }
    }
    .ant-radio-button-wrapper:hover {
      background-color: @colorA4_1;
    }
    .ant-radio-wrapper .ant-radio-checked::after {
      border: 1px solid @colorC4;
    }
    .ant-radio-button-wrapper-checked {
      border: none;
    }

    .ant-radio-button-wrapper-checked:first-child {
      background: @colorA5;
      border-radius: 6px 0 0 6px;
      -webkit-backface-visibility: hidden;
      .ant-radio-button {
        -webkit-backface-visibility: hidden;
        border-radius: 6px 0 0 6px;
      }
    }
    .ant-radio-button-wrapper-checked:last-child {
      border-radius: 0 6px 6px 0;
      -webkit-backface-visibility: hidden;
      .ant-radio-button {
        border-radius: 0 6px 6px 0;
        -webkit-backface-visibility: hidden;
      }
    }

    .ant-radio-button-wrapper:before {
      background-color: inherit;
      display: none;
    }

    .ant-radio-button-wrapper-checked:hover::before {
      background-color: inherit;
      width: 0px;
    }

    .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
      color: #fff;
      background: @colorA5;
    }
    .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):hover {
      border-color: transparent !important;
      color: #fff;
      background: @colorA5;
    }

    .ant-radio-wrapper-disabled {
      cursor: url('@/renderer/static/svg/disableMouse.cur'), not-allowed !important;

      .ant-radio,
      .ant-radio-input {
        cursor: url('@/renderer/static/svg/disableMouse.cur'), not-allowed !important;
      }

      .ant-radio-disabled .ant-radio-inner {
        border-color: transparent;
        background-color: @colorA4_1;
      }

      &:hover .ant-radio .ant-radio-inner {
        border-color: transparent !important;
      }
    }
  }
}
