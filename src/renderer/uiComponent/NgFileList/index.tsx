import React, { CSSProperties, MouseEvent, useMemo, useRef, useState } from 'react';
import { useAsyncEffect } from 'ahooks';
import { useClick } from '@/renderer/hooks/useClick';
import styles from './index.module.less';
import classnames from 'classnames';
import { ReactComponent as FolderIcon } from '@/renderer/static/svg/file_folder.svg';
import { ReactComponent as ZipIcon } from '@/renderer/static/svg/file_zip.svg';
import { ReactComponent as DocIcon } from '@/renderer/static/svg/file_doc.svg';
import { ReactComponent as LicenseIcon } from '@/renderer/static/svg/license_icon.svg';
import { ReactComponent as NgIcon } from '@/renderer/static/svg/ng_file.svg';
import { fileNameOverflow, isDirectory, transformFileSize } from '@/renderer/utils';
import { Tooltip } from 'antd';
import { UploadType } from '../NgSelectFileModal';

export type FileInfo = {
  path: string;
  name: string;
  type: 'file' | 'directory'; // 文件 ｜ 路径
  size: number; // B
  extname: string; // 后缀名
  isSelected?: boolean;
};

type Props = {
  exclude?: string;
  include?: string;
  filePath: string;
  style?: CSSProperties;
  className?: string;
  selectedFiles: FileInfo[];
  setSelectedFiles: React.Dispatch<React.SetStateAction<FileInfo[] | undefined>>;
  setShowLoading: React.Dispatch<React.SetStateAction<boolean>>;
  isLicense?: boolean;
  handleError?(): void;
  onOk?(fileInfo?: any): void;
  setCurrentPath?(val: string): void;
  entryKey: number;
  uploadType: UploadType;
  extList?: string[];
};

// eslint-disable-next-line max-lines-per-function
const NgFileList = (props: Props) => {
  const { setSelectedFiles, setShowLoading, extList = [] } = props;
  const [currentFile, setCurrentFile] = useState<FileInfo>();
  const [currentPath, setCurrentPath] = useState<FileInfo>();
  const [prevFile, setPrevFile] = useState<FileInfo[]>([]);
  const [nextFile, setNextFile] = useState<FileInfo[]>([]);
  const [fileList, setFileList] = useState<FileInfo[] | undefined>();
  const pickFile = useRef<FileInfo>();
  const defaultName = useMemo(() => {
    return props.filePath.substring(props.filePath.lastIndexOf('/') + 1) || props.filePath;
  }, [props.filePath]);
  const getFileList = async (dirPath?: FileInfo) => {
    try {
      setShowLoading(true);
      let res = await window.fileAPI.getFolderInfo(dirPath?.path ?? props.filePath, extList);
      setShowLoading(false);

      if (props.uploadType === UploadType.download) {
        res = res.filter(v => isDirectory(v));
      }

      return res;
    } catch (error) {
      if (props.handleError) {
        props.handleError();
      }

      return [];
    }
  };
  useAsyncEffect(async () => {
    await initList();
  }, []);
  const initList = async () => {
    let result = await getFileList();
    setFileList(
      result?.map(item => {
        item.isSelected = false;

        return item;
      })
    );
  };
  React.useEffect(() => {
    if (!props.entryKey) return;
    setCurrentFile(undefined);
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    handleDoubleClick(currentFile);
  }, [props.entryKey]);
  React.useEffect(() => {
    if (props.setCurrentPath) {
      if (currentFile) {
        props.setCurrentPath(currentFile.path);
      } else if (currentPath) {
        props.setCurrentPath(currentPath.path);
      }
    }
  }, [currentPath, currentFile]);
  React.useEffect(() => {
    if (fileList) {
      setSelectedFiles(fileList?.filter(item => item.isSelected));
    }
  }, [fileList]);
  const handleClick = useClick(
    async (dirInfo: FileInfo, e: MouseEvent<HTMLElement>) => { // NOSONAR
      e.stopPropagation();
      e.nativeEvent.stopImmediatePropagation();
      if (dirInfo.name === currentFile?.name) {
        setCurrentFile(undefined);
        setFileList(v => {
          const res = v?.map(item => {
            if (item.name === dirInfo.name) {
              item.isSelected = false;
            }

            return { ...item };
          });

          return [...res!];
        });

        return;
      }

      // shift和command同时按，只保留shift
      if (e.ctrlKey || (e.metaKey && !e.shiftKey)) {
        setFileList(v => {
          const res = v?.map(item => {
            if (item.name === dirInfo.name) {
              // command 再次点击取消选择
              item.isSelected = !(item.isSelected && dirInfo.isSelected);
            }

            return { ...item };
          });

          return [...res!];
        });

        return;
      } else if (e.shiftKey) {
        let lastSelectIndex = fileList?.findIndex(item => item.name === dirInfo.name);
        let currentIndex = fileList?.findIndex(item => item.name === currentFile?.name);
        // 反向shift选择
        if (currentIndex! > lastSelectIndex!) {
          [currentIndex, lastSelectIndex] = [lastSelectIndex, currentIndex];
        }

        if (lastSelectIndex !== -1 && currentIndex !== -1) {
          setFileList(v => {
            const res = v?.map((item, index) => {
              item.isSelected = index >= currentIndex! && index <= lastSelectIndex!;

              return { ...item };
            });

            return [...res!];
          });
        }

        return;
      } else {
        // 单击事件
        setCurrentFile({
          ...dirInfo,
        });
        setFileList(v => {
          const res = v?.map(item => {
            item.isSelected = item.name === dirInfo.name;

            return { ...item };
          });

          return [...res!];
        });
      }
    },
    async (dirInfo: FileInfo, e: MouseEvent<HTMLElement>) => {
      e.stopPropagation();
      e.nativeEvent.stopImmediatePropagation();
      await handleDoubleClick(dirInfo);
    }
  );
  // 渲染文件图标
  const renderFileType = (ext: string, file: FileInfo) => {
    if (isDirectory(file)) return <FolderIcon />;

    switch (ext.replace('.', '')) {
      case 'zip':
        return <ZipIcon />;
      case 'doc':
        return <DocIcon />;
      case 'docx':
        return <DocIcon />;
      case 'lic':
        return <LicenseIcon />;
      case 'ng':
        return <NgIcon />;
      default:
        return <FolderIcon />;
    }
  };

  const handleDoubleClick = async (dirInfo?: FileInfo) => {
    if (!dirInfo) return;
    if (dirInfo?.type === 'file') {
      // 双击选择文件
      if (props.onOk) {
        props.onOk([dirInfo]);
      }

      return;
    }
    setNextFile([]);
    let res = await getFileList(dirInfo);
    setCurrentPath(v => {
      if (!v) {
        pickFile.current = {
          extname: '',
          isSelected: false,
          name: defaultName,
          size: 0,
          type: 'directory',
          path: props.filePath,
        };
      } else {
        pickFile.current = v;
      }

      return dirInfo;
    });
    setCurrentFile(undefined);
    setPrevFile(prev => {
      if (pickFile.current) {
        prev.push(pickFile.current);
      }

      return [...prev];
    });
    setFileList(res);
  };

  const handleCancelSelected = (e: any) => {
    // 合成事件，所以dataset要在parentNode上获取
    if (e?.target?.parentNode?.dataset?.name) {
      setFileList(v => {
        const res = v?.map(item => {
          item.isSelected = false;

          return item;
        });

        return [...res!];
      });
      setCurrentFile(undefined);
    }
  };

  const handleChangeCurrentFile = async (arrow: 'left' | 'right') => {
    let resultList: any;
    if (arrow === 'left') {
      if (!prevFile.length) return;
      let updateItem: any;
      setPrevFile(v => {
        if (v.length <= 0) return [...v];
        updateItem = v.pop();
        setNextFile(next => {
          next.push(currentPath!);

          return [...next];
        });

        return [...v];
      });
      setCurrentPath(updateItem);
      resultList = await getFileList(updateItem);
    } else {
      if (!nextFile.length) return;

      let updateItem: any;
      setNextFile(v => {
        if (v.length <= 0) return [...v];
        updateItem = v.pop();
        setPrevFile(prev => {
          prev.push(currentPath as FileInfo);

          return [...prev];
        });

        return [...v];
      });
      setCurrentPath(updateItem);
      resultList = await getFileList(updateItem);
    }
    setFileList(resultList);
  };

  return (
    <div
      style={{ ...props.style }}
      data-name={'container'}
      onClick={handleCancelSelected}
      className={classnames(styles.ng_file_list, props.className)}
    >
      <div className={styles.btn_group}>
        <span
          className={classnames(styles.disable, {
            [styles.active_left]: prevFile.length !== 0,
          })}
          onClick={async () => handleChangeCurrentFile('left')}
        />
        <span
          className={classnames(styles.disable, {
            [styles.active_right]: nextFile.length !== 0,
          })}
          onClick={async () => handleChangeCurrentFile('right')}
        />

        <div>{currentPath?.name || defaultName}</div>
      </div>
      <div className={styles.file_list}>
        {fileList?.map((item: FileInfo) => {
          return (
            <React.Fragment key={item.name}>
              <div
                className={classnames(styles.file_item, {
                  [styles.file_selected]: item.isSelected,
                })}
                onClick={async e => handleClick(item, e)}
              >
                <div className={classnames(styles.file_type)}>{renderFileType(item.extname, item)}</div>

                <div
                  className={classnames(styles.file_name, 'file-name')}
                  ref={ele => {
                    // 如果文本没超过两行，替换\r
                    if (!ele) return;
                    const style = window.getComputedStyle(ele);

                    // 获取元素的高度和行高
                    const height = ele.clientHeight;
                    const lineHeight = parseInt(style.lineHeight, 10);

                    // 计算元素中文本的行数
                    const numLines = Math.round(height / lineHeight);
                    if (numLines === 1) {
                      ele.innerHTML = ele?.innerText.split('\n')[0].replace(/\s+/, '');
                    }
                  }}
                >
                  <Tooltip overlayClassName={styles.name_tips} placement="bottom" title={item.name}>
                    {fileNameOverflow(item.name)}
                  </Tooltip>
                </div>

                <span className={styles.file_size}>{!isDirectory(item) ? transformFileSize(item.size) : ''}</span>
              </div>
            </React.Fragment>
          );
        })}
      </div>
    </div>
  );
};
export default NgFileList;
