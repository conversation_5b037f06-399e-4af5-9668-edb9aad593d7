@import '@/renderer/static/style/baseColor.module.less';
.ng_file_list {
  .btn_group {
    display: flex;
    // position: fixed;
    margin-bottom: 32px;
    align-items: flex-start;
    cursor: url('@/renderer/static/svg/move-arrow.cur'), pointer;
    font-family: normal-font, serif;
    & div {
      color: @colorA12;
      font-family: normal-font, serif;
      font-size: 14px;
    }
    .disable {
      background-position: center center;
      background-size: 12px 12px;
      background-repeat: no-repeat;
      cursor: url('@/renderer/static/svg/disableMouse.cur'), not-allowed;
    }
    .active_left {
      background-color: @colorA6 !important;
      background-image: url('@/renderer/static/images/active_left.png') !important;
      cursor: url('@/renderer/static/svg/cursor.cur'), pointer;
    }
    .active_right {
      background-color: @colorA6 !important;
      background-image: url('@/renderer/static/images/active_right.png') !important;
      cursor: url('@/renderer/static/svg/cursor.cur'), pointer;
    }
    & span {
      display: inline-block;
      width: 20px;
      height: 20px;
      margin: 0;
      background-color: @colorA13;
      border-radius: 2px;
      &:first-child {
        margin-right: 4px;
        background-image: url('@/renderer/static/images/disable_left.png');
      }
      &:nth-child(2) {
        margin-right: 12px;
        background-image: url('@/renderer/static/images/disable_right.png');
      }
    }
  }
  .file_item {
    user-select: none;
    width: 98px;
    display: flex;
    flex-direction: column;
    align-items: center;
    font-family: normal-font, serif;
    font-size: 14px;
    color: @colorA12;
    align-content: flex-start;
    // margin-bottom: 40px;
    box-sizing: border-box;
    padding: 4px 5px;
    height: 130px;
    margin: 0 10px 0px;
    border: 1px solid transparent;
    border-radius: 6px;

    &:hover {
      background-color: @colorA5;
    }

    &.file_selected {
      background-color: @colorA5;
      border-color: @colorA6;
    }

    .file_name {
      width: 100%;
      text-align: center;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      font-family: normal-font, serif;
      color: @colorA12;
      line-height: 20px;
      margin: 6px 0 4px;
    }
  }
  .file_list {
    display: flex;
    flex-wrap: wrap;
    font-family: normal-font, serif;
    align-content: flex-start;
    width: 100%;
    overflow-y: scroll;
    max-width: 770px;
    max-height: 300px;
    &::-webkit-scrollbar {
      width: 6px;
    }
    &::-webkit-scrollbar-thumb {
      background: @colorA6;
      border-radius: 10px;
    }
    &::-webkit-scrollbar-thumb:vertical {
      width: 10px;
      height: 10px;
    }

    :global {
      .ant-tooltip-open {
        font-family: normal-font, serif;
      }
    }
  }
  .file_size {
    color: @colorA8;
    font-size: 12px;
    font-family: normal-font, serif;
    line-height: 17px;
  }
  .next {
    width: 12px;
    height: 12px;
    margin-left: 12px;
    background: url('../../static/images/breadcrumbIcon.png');
  }

  .file_type {
    background: transparent;
    border-radius: 6px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.name_tips {
  :global {
    .ant-tooltip-inner {
      box-shadow: none;
    }
  }
}
