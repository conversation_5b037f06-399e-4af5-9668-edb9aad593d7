import React, { memo, MouseEvent } from 'react';
import styles from './index.module.less';
import NgButton from '@/renderer/uiComponent/NgButton';
import {
  EmptyNoTask,
  EmptyNoPatient,
  EmptyNoData,
  EmptyPageFail,
  EmptyTaskFail,
  EmptyLoadingFail,
  EmptyAddTask,
  EmptyNotice,
  EmptyNoInfo,
} from '../SvgGather';
import classnames from 'classnames';

/* noTask: 无任务; noPatient: 无患者; noData: 无数据; pageFail: 页面加载失败;
 taskFail: 任务加载失败; loadingFail: 加载失败; addTask: 无任务且新建治疗任务;*/
type EmptyType = 'noTask' | 'noPatient' | 'noData' | 'pageFail' | 'taskFail' | 'loadingFail' | 'addTask' | 'custom' | 'noNotice' | 'noInfo';

interface IProps {
  emptyType?: EmptyType;
  onAddTask?: ((e: MouseEvent<any, any>) => void) | undefined;
  className?: string;
  customSvg?: React.ReactNode;
  customDesc?: string;
  contentClasName?: string;
}

const matchEelWithType = (emptyType: EmptyType) => {
  switch (emptyType) {
    case 'noPatient':
      return { svg: <EmptyNoPatient />, desc: '未查询到患者信息' };
    case 'noTask':
      return { svg: <EmptyNoTask />, desc: '未查询到治疗任务' };
    case 'pageFail':
      return { svg: <EmptyPageFail />, desc: 'oops！页面加载失败。' };
    case 'taskFail':
      return { svg: <EmptyTaskFail />, desc: '任务加载失败' };
    case 'loadingFail':
      return { svg: <EmptyLoadingFail />, desc: '加载失败' };
    case 'addTask':
      return { svg: <EmptyAddTask />, desc: '无治疗任务，点击新建治疗任务。' };
    case 'noNotice':
      return { svg: <EmptyNotice />, desc: '暂无通知' };
    case 'noInfo':
      return { svg: <EmptyNoInfo />, desc: '暂无数据' };
    default:
      return { svg: <EmptyNoData />, desc: '未搜索到相关数据' };
  }
};

const NgEmpty = ({ emptyType = 'noData', onAddTask, className, customDesc, customSvg, contentClasName }: IProps) => {
  const { svg, desc } = emptyType !== 'custom' ? matchEelWithType(emptyType) : { svg: customSvg, desc: customDesc };

  return (
    <div className={classnames(styles.ng_empty, className)}>
      {svg}
      <div className={classnames(styles.content, contentClasName)}>{customDesc ? customDesc : desc}</div>
      {emptyType === 'addTask' && (
        <NgButton className={styles.button} onClick={onAddTask}>
          新建治疗任务
        </NgButton>
      )}
    </div>
  );
};

export default memo(NgEmpty);
