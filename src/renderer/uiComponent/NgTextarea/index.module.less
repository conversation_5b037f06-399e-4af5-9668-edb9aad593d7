@import '../../static/style/baseColor.module.less';
.ng_input {
  :global {
    .ant-input,
    .ant-input-affix-wrapper {
      border-radius: 6px;
      background-color: @colorA5 !important;
      border-color: @colorA5 !important;

      &:not(.ant-input-status-error):hover,
      &:not(.ant-input-status-error):focus {
        border-color: @colorC4 !important;
      }
      &::-webkit-input-placeholder {
        color: @colorA7 !important;
      }

      .ant-input-show-count-suffix {
        color: @colorA9 !important;
      }
    }
    .ant-input-disabled {
      cursor: url('@/renderer/static/svg/disableMouse.cur'), not-allowed;
      background-color: @colorA6 !important;
      border-color: @colorA6 !important;
      color: @colorA7 !important;
    }

    .ant-input-textarea-affix-wrapper.ant-input-status-error:not(.ant-input-disabled) {
      border-color: @colorB3 !important;
    }

    .ant-input {
      &::-webkit-scrollbar {
        width: 4px;
      }
      &::-webkit-scrollbar-thumb {
        background: @colorA5 !important;
        border-radius: 10px;
      }
      &::-webkit-scrollbar-thumb:vertical {
        width: 10px;
        height: 10px;
      }
    }
  }
}
