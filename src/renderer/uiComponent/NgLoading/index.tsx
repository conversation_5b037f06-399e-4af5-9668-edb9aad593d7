import React, { ReactElement } from 'react';
import styles from './index.module.less';

type Props = {
  loadingText?: string;
};
export const NgLoading = (props: Props): ReactElement => {
  const [loadingText] = React.useState(props.loadingText || '加载中...' || 'Loading...' || 'Loading');

  return (
    <div className={styles.ng_loading}>
      <div className={styles.top_image}>
        <div className={styles.left_circle_container}>
          <div className={styles.left_circle} />
        </div>
        <div className={styles.right_circle_container}>
          <div className={styles.right_circle} />
        </div>
      </div>
      <div className={styles.bottom_text}>{loadingText}</div>
    </div>
  );
};
