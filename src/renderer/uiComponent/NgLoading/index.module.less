@import '../../static/style/baseColor.module.less';

.ng_loading {
  width: 200px;
  height: auto;
  display: flex;
  flex-direction: column;
  position: relative;
  align-items: center;
  margin-left: -100px;
  margin-top: -50px;
  .top_image {
    width: 200px;
    height: 80px;
    background: url('../../static/images/loading_brain.png') no-repeat;
    background-position-x: center;
    background-position-y: center;
    display: flex;
    justify-content: center;
    position: relative;

    &_2x {
      width: 140px;
      height: 140px;
      background: url('../../static/images/loading_brain_2x.png') no-repeat;
    }

    .left_circle_container {
      width: 80px;
      height: 80px;
      animation: round 2s ease-in-out infinite alternate;
      position: absolute;
      .left_circle {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: @colorC4;
        position: relative;
        -webkit-transform: translateZ(0);
        -ms-transform: translateZ(0);
        transform: translateZ(0);
      }
    }

    .right_circle_container {
      position: absolute;
      width: 80px;
      height: 80px;
      animation: roundRight 2s ease-in-out infinite alternate;
      .right_circle {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: @colorC4;
        position: relative;
        -webkit-transform: translateZ(0);
        -ms-transform: translateZ(0);
        transform: translateZ(0);
      }
    }
  }
  .bottom_text {
    width: 100%;
    height: 24px;
    line-height: 24px;
    margin-top: 24px;
    font-size: 16px;
    color: @colorC4;
    text-align: center;
    font-family: normal-font;
  }
}

@keyframes round {
  .round;
}
@keyframes roundRight {
  .roundRight;
}

.round() {
  0% {
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
  }
  100% {
    -webkit-transform: rotate(-314deg);
    transform: rotate(-314deg);
  }
}

.roundRight() {
  0% {
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
  }
  100% {
    -webkit-transform: rotate(405deg);
    transform: rotate(405deg);
  }
}
