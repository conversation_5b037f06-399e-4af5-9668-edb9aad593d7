import React from 'react';
import classnames from 'classnames';
import { Checkbox, CheckboxProps } from 'antd';
import { CheckboxGroupProps } from 'antd/es/checkbox';
import styles from './index.module.less';

export const NgCheckbox = (props: CheckboxProps) => {
  return <Checkbox {...props} className={classnames(props.className, styles.ng_checkbox)} />;
};

NgCheckbox.Group = (props: CheckboxGroupProps) => {
  return <Checkbox.Group {...props} className={classnames(props.className, styles.ng_checkbox_group)} />;
};
