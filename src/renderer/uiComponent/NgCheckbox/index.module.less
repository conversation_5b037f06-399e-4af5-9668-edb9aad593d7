@import '../../static/style/baseColor.module.less';

.ng_checkbox {
  border-color: @colorA13;
  font-size: 16px;
  display: flex;
  align-items: center;
  margin-inline-start: unset !important;
  line-height: unset;
  cursor: url('@/renderer/static/svg/cursor.cur'), pointer !important;
  color: @colorA9  !important;

  &>span {
    line-height: unset;
  }

  &:hover {
    :global {
      .ant-checkbox:not(.ant-checkbox-disabled) {
        cursor: url('@/renderer/static/svg/cursor.cur'), pointer !important;

        .ant-checkbox-input {
          cursor: url('@/renderer/static/svg/cursor.cur'), pointer !important;
        }

        .ant-checkbox-inner {
          border-color: @colorC4  !important;
          cursor: url('@/renderer/static/svg/cursor.cur'), pointer !important;
        }

        &.ant-checkbox-checked {
          .ant-checkbox-inner {
            background-color: @colorC4  !important;
            border-color: @colorC4  !important;
          }

          &::after {
            border-color: @colorC4  !important;
          }
        }
      }
    }
  }

  :global {
    .ant-checkbox-disabled+span {
      color: @colorA6;
    }

    .ant-checkbox {
      top: unset;

      .ant-checkbox-inner {
        background-color: @colorA13;
        border-color: @colorA13;
      }

      &::after {
        border-color: @colorC4  !important;
      }
    }

    .ant-checkbox+span {
      padding-inline-start: 12px;
    }

    .ant-checkbox-checked {
      .ant-checkbox-inner {
        background-color: @colorC4;
        border-color: @colorC4  !important;

        &::after {
          background: url('../../static/images/checkbox.png') no-repeat;
          width: 11px;
          height: 9px;
          animation: initial;
          border: none;
          inset-inline-start: initial;
          left: 50%;
          transform: translate(-50%, -50%);
        }
      }

      &.ant-checkbox-disabled {
        .ant-checkbox-inner {
          background-color: @colorA13;
          border-color: rgba(0, 0, 0, 0) !important;

          &::after {
            filter: brightness(0.5);
          }
        }
      }
    }

    .ant-checkbox-indeterminate {
      .ant-checkbox-inner {
        background: @colorC4;
        border-color: @colorC4;

        &::after {
          width: 12px;
          height: 1px;
          background-color: #ffffff;
          border-radius: 10px 10px 10px 10px;
        }
      }
    }
  }
}

.ng_checkbox_group {
  display: unset;

  .ng_checkbox {
    margin-bottom: 24px;
  }
}