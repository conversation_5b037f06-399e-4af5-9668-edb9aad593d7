import React, { CSSProperties, useEffect, useState } from 'react';
import Icon from '@ant-design/icons';
import { CustomIconComponentProps } from '@ant-design/icons/lib/components/Icon';
import styles from './index.module.less';
import { PopconfirmProps, TooltipProps, Tooltip } from 'antd';
import classnames from 'classnames';
import NgPopover from '@/renderer/uiComponent/NgPopover';
import { AuthProps, withAuth } from '../../hocComponent/withAuth';

export type NgIconProps = {
  id?: string;
  style?: CSSProperties;
  tooltip?: TooltipProps;
  popConfirm?: PopconfirmProps;
  fontSize?: number;
  isActive?: boolean;
  iconSvg: React.ComponentType<CustomIconComponentProps | React.SVGProps<SVGSVGElement>>;
  onClick?(event: React.MouseEvent<HTMLSpanElement>): void;
  iconClass?: any;
  disabled?: boolean;
  authName?: string;
  isPreview?: boolean;
  everAllowClick?: boolean; // 是否始终可点击，应对取消冒泡等情况
} & AuthProps;
export const NgIcon = withAuth((props: NgIconProps) => {
  const { tooltip, popConfirm, authName, authState = {}, everAllowClick, fontSize = 20 } = props;
  const [isTooltip, setIsTooltip] = useState(props.hasOwnProperty('tooltip'));
  const [isPopConfirm, setIsPopConfirm] = useState(props.hasOwnProperty('popConfirm'));
  const disable = !!(authName && authState[authName] === 1) || props.disabled;
  const none = !!(authName && authState[authName] === 0);

  useEffect(() => {
    setIsTooltip(props.hasOwnProperty('tooltip'));
  }, [tooltip]);
  useEffect(() => {
    setIsPopConfirm(props.hasOwnProperty('popConfirm'));
  }, [popConfirm]);

  const renderIcon = () => {
    const getWidth = props.style?.width || fontSize + 4;
    const getHeight = props.style?.height || fontSize + 4;

    return (
      <Icon
        className={classnames(styles.ng_icon, props.iconClass, {
          [styles.icon_disabled]: disable,
          [styles.ng_icon_active]: props.isActive,
          [styles.icon_preview]: props.isPreview,
        })}
        id={props.id}
        component={props.iconSvg}
        disabled={disable}
        onClick={
          everAllowClick || !disable
            ? props.onClick
            : () => {
                //
              }
        }
        style={{ ...props.style, cursor: 'pointer', width: getWidth, height: getHeight, fontSize: fontSize }}
        rev=""
      />
    );
  };

  const renderPopConfirm = () => {
    return (
      <>
        {isPopConfirm && popConfirm && (
          <NgPopover
            showOperation={{
              onOk: popConfirm.onConfirm!,
              onCancel: popConfirm.onCancel!,
            }}
            content={popConfirm.description}
            {...popConfirm}
          >
            {renderIcon()}
          </NgPopover>
        )}
        {!popConfirm && renderIcon()}
      </>
    );
  };

  const renderTooltip = () => {
    return <>{isTooltip ? <Tooltip {...tooltip}>{renderPopConfirm()}</Tooltip> : renderPopConfirm()}</>;
  };

  return <>{none ? <></> : renderTooltip()}</>;
});
