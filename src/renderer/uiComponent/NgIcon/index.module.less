@import '../../static/style/baseColor.module.less';

.ng_icon {
  display: inline-flex !important;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  border-radius: 2px 2px 2px 2px;
  padding: 1px 1px;

  &_active {
    border-radius: 2px 2px 2px 2px;
    background: @colorA2;
  }
}
.ng_icon:not(.icon_disabled, .icon_preview) {
  cursor: url('@/renderer/static/svg/cursor.cur'), pointer !important;

  &:hover {
    border-radius: 2px 2px 2px 2px;
    background: @colorA5;
  }
}

.icon_preview {
  cursor: url('@/renderer/static/svg/defaultMouse.cur'), pointer !important;
}

.ng_icon.icon_disabled {
  cursor: url('@/renderer/static/svg/disableMouse.cur'), pointer !important;
  svg {
    path {
      fill: @colorA6;
    }
  }
}
