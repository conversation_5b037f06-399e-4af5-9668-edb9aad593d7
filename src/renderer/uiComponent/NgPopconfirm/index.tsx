import React, { PropsWithChildren } from 'react';
import { Popconfirm, PopconfirmProps } from 'antd';
import classNames from 'classnames';
import styles from './index.module.less';

export const NgPopconfirm = (props: PopconfirmProps & React.RefAttributes<unknown> & PropsWithChildren) => {
  const { children, overlayClassName = {}, ...resetProps } = props;

  return (
    <Popconfirm
      getPopupContainer={(triggerNode: any) => triggerNode.parentNode}
      {...resetProps}
      overlayClassName={classNames(overlayClassName, styles.popconfirm, resetProps.title ? '' : styles.without_title)}
    >
      {props.children}
    </Popconfirm>
  );
};
