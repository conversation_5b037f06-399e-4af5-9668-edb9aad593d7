@import '../../static/style/baseColor.module.less';

.popconfirm {
  color: red;

  :global {
    .ant-popover-inner {
      background-color: @colorA4_1;
      padding: 14px;
      min-width: 208px;

      .ant-popconfirm-description {
        color: #fff;
        margin: 0 0 20px;
      }

      .ant-btn {
        width: 60px;
        cursor: url('@/renderer/static/svg/cursor.cur'), pointer;

        &.ant-btn-default {
          // font-size: 14px;
          color: @colorA10;
          align-items: center;
          background-color: rgba(0, 0, 0, 0);
          border-color: rgba(0, 0, 0, 0);
          &:hover {
            color: @colorC4;
          }
        }

        &.ant-btn-primary {
          color: @colorA1;
          background-color: @colorC4;

          &:hover {
            background-color: @colorC1;
          }
        }
      }
    }

    .ant-popover-arrow::after,
    .ant-popover-arrow::before {
      background-color: @colorA4_1;
    }
  }

  &.without_title {
    :global {
      .ant-popconfirm-message-icon {
        display: none;
      }
    }
  }
}
