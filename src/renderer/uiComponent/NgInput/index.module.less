@import '../../static/style/baseColor.module.less';
.ng_input {
  :global {
    .ant-input,
    .ant-input-affix-wrapper {
      border-radius: 6px;
      background-color: @colorA5 !important;
      border-color: @colorA5 !important;

      &:not(.ant-input-status-error):hover,
      &:not(.ant-input-status-error):focus {
        border-color: @colorC4 !important;
      }
      &::-webkit-input-placeholder {
        color: @colorA7 !important;
      }

      .ant-input-show-count-suffix {
        color: @colorA9 !important;
      }
    }
    .ant-input-affix-wrapper-focused {
      border-color: @colorC4 !important;
    }
    .ant-input-clear-icon {
      cursor: url('@/renderer/static/svg/cursor.cur'), pointer;
    }
    .ant-input-affix-wrapper-disabled {
      background-color: @colorA6 !important;
    }
    .ant-input-disabled {
      cursor: url('@/renderer/static/svg/disableMouse.cur'), not-allowed;
      background-color: @colorA6 !important;
      border-color: @colorA6 !important;
      color: @colorA9 !important;
    }
    .ant-input-affix-wrapper .ant-input-clear-icon-has-suffix {
      margin-top: 4px;
    }
    .ant-input-affix-wrapper.ant-input-status-error:not(.ant-input-disabled) {
      border-color: @colorB3 !important;
    }
    .ant-input-password {
      border-radius: 6px;
      background-color: #434351 !important;
      border-color: #434351 !important;
      padding: 7px 11px;
    }
  }
}
