import React from 'react';
import { Input, InputProps } from 'antd';
import styles from './index.module.less';
import classnames from 'classnames';
import { PasswordProps } from 'antd/es/input';

type Props = InputProps;
export const NgInput = (props: Props) => {
  return (
    <div className={classnames(styles.ng_input, props.className)}>
      <Input {...props} />
    </div>
  );
};
export const NgInputPassword = (props: PasswordProps) => {
  return (
    <div className={classnames(styles.ng_input, props.className)}>
      <Input.Password {...props} />
    </div>
  );
};
