@import '@/renderer/static/style/baseColor.module.less';
.ng_file_modal {
  position: relative;
  .demo {
    width: 100%;
    height: 100%;
    background: fade(@colorA1, 80%);
    position: absolute;
    left: 50%;
    top: 50%;
    z-index: 99;
    transform: translate(-50%, -50%);
    & svg {
      width: 32px;
      height: 32px;
      & image {
        width: 32px;
        height: 32px;
      }
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      animation: loading linear 1s infinite;
    }
  }

  .operationBtn {
    position: absolute;
    bottom: 0px;
    right: 0px;
    background: @colorA4;
    margin: 0;
    margin-top: 24px !important;
    display: flex;
    justify-content: flex-end;
    .disabled {
      width: 80px;
      height: 32px;
      background: @colorA5 !important;
      color: @colorA6 !important;
      cursor: url('../../static/svg/disableMouse.cur'), not-allowed !important;
    }
    .openBtn {
      width: 80px;
      height: 32px;
      background: @colorC4;
      border-radius: 4px 4px 4px 4px;
      text-align: center;
      line-height: 32px;
      cursor: url('../../static/svg/cursor.cur'), pointer;
      color: @colorA1;
    }

    & div {
      &:first-child {
        margin-right: 12px;
        width: 80px;
        height: 32px;
        background: @colorA6;
        border-radius: 4px 4px 4px 4px;
        opacity: 1;
        justify-content: center;
        color: @colorA12;
      }
    }
  }

  :global {
    .react-draggable {
      min-height: 440px;
      min-width: 770px;
      max-width: 1080px !important;
      max-height: 440px !important;
      overflow: hidden;
      border-radius: 8px;
      &::-webkit-scrollbar {
        display: none;
      }
      & > div:last-child {
        & > div:nth-child(1) {
          cursor: url('@/renderer/static/svg/top-bottom-arrow.cur'), pointer !important;
        }
        & > div:nth-child(2) {
          cursor: url('@/renderer/static/svg/left-right-arrow.cur'), pointer !important;
        }
        & > div:nth-child(3) {
          cursor: url('@/renderer/static/svg/top-bottom-arrow.cur'), pointer !important;
        }
        & > div:nth-child(4) {
          cursor: url('@/renderer/static/svg/left-right-arrow.cur'), pointer !important;
        }
        & > div:nth-child(5) {
          cursor: url('@/renderer/static/svg/top-right-left-arrow.cur'), pointer !important;
        }
        & > div:nth-child(6) {
          cursor: url('@/renderer/static/svg/top-left-right-arrow.cur'), pointer !important;
        }
        & > div:nth-child(7) {
          cursor: url('@/renderer/static/svg/top-right-left-arrow.cur'), pointer !important;
        }
        & > div:nth-child(8) {
          cursor: url('@/renderer/static/svg/top-left-right-arrow.cur'), pointer !important;
        }
      }
    }
    .ant-modal {
      pointer-events: unset;
      padding: 0 !important;
    }
    .ant-modal-footer {
      position: relative;
      margin: 0;
      // margin-top: 24px;

      display: flex;
      justify-content: flex-end;
    }
    .ant-modal-body {
      height: 100%;
      width: 100%;
      max-width: 1080px;
      max-height: 366px;
    }
    .ant-modal-content {
      min-height: 440px;
      min-width: 770px;
      max-width: 1080px;
      // max-height: 440px;
      cursor: url('@/renderer/static/svg/defaultMouse.cur'), pointer;
      color: @colorA12 !important;
      width: 100%;
      height: 100%;
      background: @colorA4;
      padding: 24px !important;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      & .ant-modal-close-x {
        width: 20px;
        height: 20px;
        cursor: url('@/renderer/static/svg/cursor.cur'), pointer;
        &:hover {
          background: @colorA5;
          border-radius: 4px;
        }
      }
    }
    .ant-modal-close {
      top: 24px;
      right: 24px;
      width: 20px;
      height: 20px;
    }
  }
}

@keyframes loading {
  0% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(90deg);
  }
  50% {
    transform: rotate(180deg);
  }
  75% {
    transform: rotate(270deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
