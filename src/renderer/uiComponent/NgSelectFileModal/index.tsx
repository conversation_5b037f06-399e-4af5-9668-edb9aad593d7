import React, { useEffect, useState } from 'react';
import NgModal, { IModalProps } from '../NgModal';
import { ModalProps, message } from 'antd';
import styles from './index.module.less';
import listStyles from '@/renderer/uiComponent/NgFileList/index.module.less';
import classnames from 'classnames';
// eslint-disable-next-line import/no-extraneous-dependencies
import { Rnd, Props as RndProps } from 'react-rnd';
import NgFileList, { FileInfo } from '@/renderer/uiComponent/NgFileList';
import NgButtonText from '@/renderer/uiComponent/NgButtonText';
import { ReactComponent as LoadingIcon } from '@/renderer/static/svg/modal-loading.svg';

export enum UploadType {
  uplaod = 'uplaod',
  download = 'download',
}

type Props = {
  modalMinWidth?: number;
  filepath: string;
  width?: number;
  modalMinHeight?: number;
  height?: number;
  controlLoading?: boolean;
  isLicense?: boolean;
  handleError?(): void;
  hideRnd?: boolean;
  uploadType?: UploadType;
  extList?: string[];
};

const NgSelectFileModal = (props: Props & IModalProps & ModalProps & RndProps) => {
  const { hideRnd = true, uploadType = UploadType.uplaod } = props;
  const [selectedFiles, setSelectedFiles] = useState<FileInfo[] | undefined>([]);
  const [currentPath, setCurrentPath] = useState<string>(props.filepath);
  const [showLoading, setShowLoading] = useState(false);
  const [entryKey, setEntryKey] = useState(0);
  const [messageApi, contextHolder] = message.useMessage();

  useEffect(() => {
    setCurrentPath(props.filepath);
    setSelectedFiles([]);
  }, [props.open]);
  const handleOpen = (path?: FileInfo[]) => {
    if (!selectedFiles?.length) return;
    if (selectedFiles[0].type === 'directory') {
      setEntryKey(Date.now());
    } else {
      props.onOk?.(selectedFiles as any);
    }
  };

  const noFileMessage = () => {
    // eslint-disable-next-line no-void, @typescript-eslint/no-floating-promises
    messageApi.open({
      type: 'error',
      content: '未检测到移动设备',
    });
  };

  const handleChoseError = () => {
    if (props.handleError) {
      props.handleError();
    } else {
      noFileMessage();
    }
    props.onCancel!('' as any);
  };

  const handleOk = () => {
    const isDownload = uploadType === UploadType.download;
    if (isDownload ? props.filepath === currentPath : !selectedFiles?.length) {
      return;
    }
    if (isDownload) {
      props.onOk?.([{ path: currentPath }] as any);
    } else {
      handleOpen();
    }
  };

  return (
    <div className={classnames(props.className, styles.ng_file_modal)}>
      {contextHolder}
      <NgModal
        {...props}
        // 避免二次打开modal时，列表还停留在上一次的文件路径
        destroyOnClose={props.destroyOnClose || true}
        wrapClassName={classnames(props.wrapClassName, styles.ng_file_modal)}
        modalRender={modal => {
          return (
            <>
              {hideRnd ? (
                modal
              ) : (
                <Rnd dragHandleClassName={props.dragHandleClassName ?? listStyles.nav} className={styles.loading}>
                  {modal}
                </Rnd>
              )}
            </>
          );
        }}
        footer={
          <div className={classnames('ant-modal-footer', styles.operationBtn)}>
            <NgButtonText onClick={props.onCancel}>{props.cancelText || '取消'}</NgButtonText>
            <div
              className={classnames(
                {
                  [styles.disabled]: uploadType === UploadType.download ? props.filepath === currentPath : !selectedFiles?.length,
                  // @ts-ignore
                },
                styles.openBtn
              )}
              onClick={handleOk}
            >
              {props.okText || '选择'}
            </div>
          </div>
        }
      >
        {(showLoading || props.controlLoading) && (
          <div className={styles.demo}>
            <LoadingIcon />
          </div>
        )}
        <NgFileList
          isLicense={props.isLicense}
          setShowLoading={setShowLoading}
          filePath={props.filepath}
          handleError={handleChoseError}
          // @ts-ignore
          selectedFiles={selectedFiles}
          setSelectedFiles={setSelectedFiles}
          onOk={props.onOk}
          setCurrentPath={setCurrentPath}
          entryKey={entryKey}
          uploadType={uploadType}
          extList={props.extList}
        />
      </NgModal>
    </div>
  );
};
export default NgSelectFileModal;
