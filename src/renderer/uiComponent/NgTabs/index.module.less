@import '../../static/style/baseColor.module.less';

.ng_tabs {
  display: inline-flex;
  border-radius: 6px;
  cursor: url('@/renderer/static/svg/cursor.cur'), pointer !important;

  :global {
    .ant-tabs-nav {
      background-color: transparent;
      height: 32px;
      margin-bottom: unset;

      &::before {
        border: 0 !important;
      }

      .ant-tabs-nav-list {
        .ant-tabs-tab {
          padding: 0 20px;
          margin: initial;
          color: @colorA9;
          cursor: url('@/renderer/static/svg/cursor.cur'), pointer !important;

          &:hover {
            color: unset;
          }
        }

        .ant-tabs-tab-active {
          color: @colorA12;
          background-color: @colorA4;

          .ant-tabs-tab-btn {
            color: @colorA12 !important;
          }
        }
      }

      .ant-tabs-ink-bar {
        top: 0;
        background-color: @colorC4;
      }
    }

    .ant-tabs-content-holder {
      border-radius: 6px;

      .ant-tabs-tabpane {
        border-radius: 6px;
        background-color: @colorA4;

        &:nth-child(1) {
          border-radius: 0 6px 6px 6px;
        }
      }
    }
  }
}

.ng_tabs_card {
  display: inline-flex;
  width: 100%;
  // cursor: url('@/renderer/static/svg/cursor.cur'), pointer !important;

  :global {
    .ant-tabs-nav {
      height: 32px;
      background-color: @colorA5;
      border-radius: 7px;
      border-bottom: unset !important;

      &::before {
        border: 0 !important;
      }

      .ant-tabs-nav-list {
        .ant-tabs-tab {
          margin: initial;
          margin-left: initial !important;
          padding: 0 20px;
          border: none;
          border-radius: 6px;
          cursor: url('@/renderer/static/svg/cursor.cur'), pointer !important;

          &:hover {
            color: unset;
            background-color: @colorA4_1;
          }

          .ant-tabs-tab-btn {
            color: @colorA12;
            border: unset;
          }
        }

        .ant-tabs-tab-active {
          background-color: @colorA6;
          border: none;
          color: @colorA12;
          -webkit-backface-visibility: hidden;
          transform: translateZ(0);

          .ant-tabs-tab-btn {
            color: unset;
            border: unset;
          }
        }

        .ant-tabs-tab[data-node-key='1']:hover {
          border-radius: 6px 0 0 6px;
          -webkit-backface-visibility: hidden;
        }

        .ant-tabs-tab[data-node-key='2']:hover {
          border-radius: 0 6px 6px 0;
          -webkit-backface-visibility: hidden;
        }

        .ant-tabs-tab-active[data-node-key='1'] {
          border-radius: 6px 0 0 6px;
        }

        .ant-tabs-tab-active[data-node-key='2'] {
          border-radius: 0 6px 6px 0;
        }

        .ant-tabs-ink-bar {
          display: none;
        }
      }
    }
  }
}

.ng_tabs_line {
  display: inline-flex;
  border-radius: 6px;
  cursor: url('@/renderer/static/svg/cursor.cur'), pointer !important;

  :global {
    .ant-tabs-nav {
      height: 32px;
      background-color: @colorA5;
      border-radius: 6px;
      border: 1px solid @colorA6 !important;

      &::before {
        border: 0 !important;
      }

      .ant-tabs-nav-list {
        .ant-tabs-tab {
          margin: initial;
          margin-left: initial !important;
          padding: 0 20px;
          border: none;
          border-left: 1px solid @colorA6;
          cursor: url('@/renderer/static/svg/cursor.cur'), pointer !important;

          &:nth-child(1) {
            border-radius: 6px 0 0 6px;
            border-left: none;
          }

          &:nth-last-child(2) {
            border-radius: 0 6px 6px 0;
          }

          &:hover {
            color: unset;
            background-color: @colorA3;
          }

          .ant-tabs-tab-btn {
            color: unset;
            border: unset;
          }
        }

        .ant-tabs-tab-active {
          background-color: @colorA6;
          color: @colorA12;

          .ant-tabs-tab-btn {
            color: unset;
            border: unset;
          }
        }

        .ant-tabs-ink-bar {
          display: none;
        }
      }
    }

    .ant-tabs-content-holder {
      display: none;
    }
  }
}
