import React from 'react';
import { Pagination, PaginationProps } from 'antd';
import styles from './index.module.less';

type Props = PaginationProps & { isShowTotal?: boolean };
export const NgPagination = (props: Props) => {
  const { className, total: customTotal, current, pageSize, onChange, onShowSizeChange, isShowTotal } = props;

  return (
    <Pagination
      total={customTotal}
      current={current ? current : 1}
      pageSize={pageSize ? pageSize : 10}
      showQuickJumper={!!(customTotal && customTotal > 50)}
      showSizeChanger={!!(customTotal && customTotal > 20)}
      defaultPageSize={10}
      onChange={onChange}
      onShowSizeChange={onShowSizeChange}
      pageSizeOptions={[10, 20, 50, 100]}
      size={'small'}
      showTitle={false}
      prevIcon={<span className={styles.prev_icon} />}
      nextIcon={<span className={styles.next_icon} />}
      className={className ? className : styles.ng_pagination}
      showTotal={isShowTotal ? (total, range) => `${range[0]}-${range[1]} of ${total} items` : undefined}
    />
  );
};
