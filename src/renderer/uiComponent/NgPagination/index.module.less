@import '../../static/style/baseColor.module.less';
.ng_pagination {
  color: @colorA12;
  font-size: 14px;
  display: flex;

  .prev_icon {
    display: inline-block;
    width: 24px;
    height: 24px;
    background: @colorA5 url('../../static/images/prev_icon.png') no-repeat center;
    cursor: url('@/renderer/static/svg/cursor.cur'), pointer !important;
    border-radius: 4px;
  }
  .next_icon {
    display: inline-block;
    width: 24px;
    height: 24px;
    background: @colorA5 url('../../static/images/next_icon.png') no-repeat center;
    cursor: url('@/renderer/static/svg/cursor.cur'), pointer !important;
    border-radius: 4px;
  }
  :global {
    .ant-pagination-prev {
      color: @colorA12;
      min-width: unset;
      background: @colorA5;
      border-color: @colorA5;
      vertical-align: unset;
      border-radius: 3px;
      margin-left: 4px !important;
    }
    .ant-pagination-next {
      color: @colorA12;
      min-width: unset;
      background: @colorA5;
      border-color: @colorA5;
      vertical-align: unset;
      border-radius: 3px;
      margin-left: 4px !important;
      margin-right: 12px !important;
    }
  }

  :global {
    .ant-select-dropdown {
      background-color: @colorA5;
      border-radius: 2px;
    }
    .ant-select-dropdown .ant-select-item {
      font-size: 14px;
      height: 24px;
      line-height: 18px;
      //padding: 0px 12px;
      min-height: unset;
      margin: 2px 0px;
      font-weight: normal;
      cursor: url('@/renderer/static/svg/cursor.cur'), pointer !important;
      padding: 1px 0 1px 6px;
      border-radius: 2px;
    }

    .ant-select-dropdown .ant-select-item-option-active:not(.ant-select-item-option-disabled) {
      background-color: @colorA4;
    }
    .ant-select-dropdown .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
      background-color: @colorA6;
    }
    .ant-select-open {
      .ant-select-arrow {
        top: 14px;
        right: 4px;
        background: url('../../static/images/pagination_arrow_bottom.png') no-repeat;
      }
    }

    .ant-select-selector {
      box-shadow: unset !important;
      background-color: @colorA5 !important;
      border-color: @colorA5 !important;
      padding: 0 4px !important;
      font-size: 14px;
      cursor: url('@/renderer/static/svg/cursor.cur'), pointer !important;
      border-radius: 2px !important;
      .ant-select-selection-search {
        inset-inline-start: unset;
        inset-inline-end: unset;
      }
      .ant-select-selection-item {
        font-size: 14px;
        color: @colorA10 !important;
      }
      //&:hover {
      //  border-color: @colorA8 !important;
      //  background-color: @colorA6;
      //}
    }
    .ant-select-arrow {
      background: url('../../static/images/pagination_arrow_top.png') no-repeat center center;
      width: 20px;
      height: 20px;
      margin-top: initial;
      transform: translateY(-50%);
      inset-inline-start: unset;
      right: 6px;
      cursor: url('@/renderer/static/svg/cursor.cur'), pointer !important;
      &:hover {
        background-color: @colorA5 !important;
      }
      & > span {
        display: none;
      }
    }
    .ant-select .ant-select-arrow {
      top: 12px;
      right: 4px;
    }
  }
}
:global {
  .ant-pagination .ant-pagination-item {
    color: @colorA12;
    min-width: unset;
    margin-inline-end: 4px;
    background: @colorA5;
    border-color: @colorA5;
    padding: 0px 4px;
    vertical-align: unset;
    border-radius: 2px;

    a {
      color: @colorA10;
      padding: 0px 4px;
    }
    &-active {
      color: @colorA10;
      background: @colorA4;
      border-color: @colorA6;

      a {
        color: @colorA10;
      }
    }
    &-hover {
      color: @colorA10;
      border-color: @colorA6;
    }
  }
  .ant-pagination.ant-pagination-mini .ant-pagination-item {
    margin-left: 6px;
    cursor: url('@/renderer/static/svg/cursor.cur'), pointer !important;
    & a {
      cursor: url('@/renderer/static/svg/cursor.cur'), pointer !important;
    }
  }

  .ant-pagination .ant-pagination-item-active:hover {
    border-color: @colorA8;
    a {
      color: @colorA12;
    }
  }
  .ant-pagination.ant-pagination-mini .ant-pagination-item:not(.ant-pagination-item-active) {
    background-color: @colorA5;
  }
  .ant-pagination .ant-pagination-item:not(.ant-pagination-item-active):hover {
    background-color: @colorA5;
    border-color: @colorA6;
  }
  .ant-pagination.ant-pagination-mini .ant-pagination-options-quick-jumper {
    display: inline-flex;
    align-items: center;
    vertical-align: unset;
  }
  .ant-pagination.ant-pagination-mini .ant-pagination-options-quick-jumper input {
    background-color: @colorA5;
    border-color: @colorA5;
    color: @colorA12;
    font-size: 14px;
    border-radius: 2px;
  }
  .ant-pagination .ant-pagination-options-quick-jumper input:hover {
    border-color: @colorA6;
  }
  .rc-virtual-list-holder-inner {
    cursor: url('@/renderer/static/svg/cursor.cur'), pointer !important;
  }
  .ant-pagination .ant-pagination-options-quick-jumper input:focus {
    border-color: @colorA6;
  }
  .ant-pagination.ant-pagination-mini .ant-pagination-jump-next {
    margin-left: 6px;
  }
  .ant-pagination .ant-pagination-jump-next .ant-pagination-item-container .ant-pagination-item-ellipsis {
    color: @colorA12;
    letter-spacing: 6px;
  }
}
