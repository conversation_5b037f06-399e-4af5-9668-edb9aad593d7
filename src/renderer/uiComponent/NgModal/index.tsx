import React, { forwardRef, ReactNode } from 'react';
// eslint-disable-next-line import/no-internal-modules
import ReactDOM<PERSON>lient from 'react-dom/client';
import { Modal as AntdModal, ModalFuncProps, ModalProps } from 'antd';
import styles from './index.module.less';
import NgButtonText from '@/renderer/uiComponent/NgButtonText';
import NgButton from '@/renderer/uiComponent/NgButton';
import { ReactComponent as Close } from '@/renderer/static/svg/close.svg';
import classnames from 'classnames';

export interface IModalProps {
  content?: React.ReactNode;
  headerIcon?: ReactNode;
  autoClose?: boolean;
}
interface IComponentRef extends React.ForwardRefExoticComponent<IModalProps & ModalProps> {
  open(props: ModalProps & IModalProps): { hide(): void };
  confirm(props: ModalFuncProps & IModalProps): { hide(): void };
  info(props: ModalProps & IModalProps): { hide(): void };
}

const Modal = (props: ModalProps & IModalProps, _ref: any) => {
  const { open, children, footer, closeIcon, headerIcon, ...rest } = props;

  return (
    <>
      <AntdModal
        /* 目前系统中所有的弹窗都需要居中*/
        {...rest}
        centered
        wrapClassName={classnames(styles.ng_modal, props.wrapClassName)}
        open={open}
        closeIcon={closeIcon ? closeIcon : <Close />}
        footer={
          <>
            {footer ? (
              footer
            ) : (
              <div className={styles.footer}>
                <NgButtonText style={{ display: 'inline-block' }} onClick={props.onCancel}>
                  取消
                </NgButtonText>
                <NgButton onClick={props.onOk as any}>{props.okText || '确定'}</NgButton>
              </div>
            )}
          </>
        }
      >
        {headerIcon ? (
          <div className={styles.messageBox}>
            <div className={styles.headerIcon}>{headerIcon}</div>
            <div>{children}</div>
          </div>
        ) : (
          <> {children}</>
        )}
      </AntdModal>
    </>
  );
};

const NgModal = forwardRef(Modal) as unknown as IComponentRef;
export default NgModal;
NgModal.confirm = (props: ModalFuncProps & Omit<IModalProps, 'content'>) => {
  const handleClose = () => hide();

  const handleOk = () => {
    handleClose();
    if (props.onOk) {
      props.onOk();
    }
  };

  const { hide } = NgModal.open({
    ...props,
    title: '',
    closeIcon: <></>,
    footer: (
      <>
        {props.footer ? (
          props.footer
        ) : (
          <div className={styles.confirmBtn}>
            {props.cancelText && (
              <NgButtonText style={{ marginRight: 32 }} onClick={() => hide()}>
                {props.cancelText || '取消'}
              </NgButtonText>
            )}
            <NgButton onClick={handleOk}>{props.okText || '我知道了'}</NgButton>
          </div>
        )}
      </>
    ),
    wrapClassName: styles.confirmBox,
  });

  return {
    hide,
  };
};
// info只用于信息展示，不提供取消、确定操作，只允许关闭
NgModal.info = (props: ModalFuncProps & IModalProps) => {
  const { hide } = NgModal.open({
    ...props,
    wrapClassName: classnames(styles.infoBox, props.wrapClassName),
    footer: <></>,
  });

  return {
    hide,
  };
};

NgModal.open = (props: ModalProps & IModalProps) => {
  const { title, content, autoClose = false, ...rest } = props;
  let element: HTMLElement | null = document.createElement('div');
  element.id = 'modalBox';
  document.body.appendChild(element);
  const onClose = () => {
    root.render(getModalNode(false));
    // @ts-ignore
    props.onCancel?.();
  };
  const onOk = (e: any) => {
    if (props.onOk && typeof props.onOk === 'function') {
      const maybePromise: any = props.onOk(e);
      if (maybePromise instanceof Promise) {
        // 处理onOk为Promise的情况
        // eslint-disable-next-line no-void
        void maybePromise.then(() => {
          if (autoClose) return;
          onClose();
        });
      } else {
        onClose();
      }

      return;
    }
    onClose();
  };
  // 在关闭前移除dom
  const afterClose = () => {
    element?.remove();
    element = null;
    if (props.afterClose && typeof props.afterClose === 'function') {
      props.afterClose();
    }
  };

  const getModalNode = (open: boolean) => {
    let modalNode = null;
    modalNode = (
      <Modal {...rest} open={open} title={title} onOk={onOk} onCancel={onClose} getContainer={props.getContainer || element!} afterClose={afterClose}>
        {content}
      </Modal>
    );

    return modalNode;
  };
  const root = ReactDOMClient.createRoot(element);
  root.render(getModalNode(true));

  return {
    hide: onClose,
  };
};
