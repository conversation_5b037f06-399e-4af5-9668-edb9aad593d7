@import '../../static/style/baseColor.module.less';
.ng_modal {
  .footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    & div:first-child {
      margin-right: 32px;
    }
  }
  :global {
    .ant-modal {
      width: 500px;
    }
    .ant-modal-content {
      width: 100%;
      padding: 30px;
    }
    .ant-modal-content,
    .ant-modal-title {
      font-family: medium-font, serif;
      background: @colorA4;
      color: @colorA12;
    }
    .ant-modal-header {
      margin-bottom: 20px;
    }
    .ant-modal-body {
      color: @colorA11;
      font-family: normal-font, serif;
      line-height: 1.7 !important;
    }
    .ant-modal-footer {
      margin-top: 20px;
    }
    .ant-modal-close {
      display: flex;
      justify-content: center;
      align-items: center;
      top: 30px;
      right: 30px;
      cursor: url('@/renderer/static/svg/cursor.cur'), pointer;
      &:hover {
        background: @colorA5 !important;
      }
      & .ant-modal-close-x {
        & svg {
          position: absolute;
          top: 50%;
          bottom: 50%;
          transform: translate(0%, -50%);
          font-size: 12px !important;
        }
      }
    }
  }
}

.infoBox {
  :global {
    .ant-modal-content {
      box-sizing: border-box;
      padding: 24px 20px;
    }
    .ant-modal-content,
    .ant-modal-title {
      font-family: medium-font, serif;
      background: @colorA4;
      color: @colorA12;
    }
    .ant-modal-body {
      color: @colorA11;
      font-family: normal-font, serif;
      line-height: 1.7 !important;
    }
    .ant-modal-close {
      top: 24px;
      cursor: url('@/renderer/static/svg/cursor.cur'), pointer;
      &:hover {
        background: @colorA5 !important;
      }
      & .ant-modal-close-x {
        width: 100%;
        height: 100%;
        & svg {
          position: absolute;
          top: 50%;
          bottom: 50%;
          transform: translate(-50%, -50%);
          font-size: 12px !important;
        }
      }
    }
  }
}
.confirmBox {
  .confirmBtn {
    display: flex;
    justify-content: flex-end;
  }
  .messageBox {
    display: flex;
    color: @colorA11;
    font-family: medium-font, serif;
    line-height: 1.7 !important;
  }
  .headerIcon {
    margin-top: 2px;
    margin-right: 17px;
  }
  :global {
    .ant-modal {
      width: 500px;
    }
    .ant-modal-body {
      color: @colorA11;
      font-family: normal-font, serif;
    }
    .ant-modal-title {
      font-family: medium-font, serif;
      background: @colorA4;
      color: @colorA12;
    }
    .ant-modal-content {
      width: 500px;
      padding: 30px;
      background: @colorA4;
    }
    .ant-modal-footer {
      margin-top: 20px;
    }
  }
}
