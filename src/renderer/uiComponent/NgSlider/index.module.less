@import '../../static/style/baseColor.module.less';
.ng_slider {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  height: 24px;
  line-height: 24px;
  padding-left: 8px;
  padding-right: 8px;
  border-radius: 4px;
  font-size: 16px;

  &_font12 {
    font-size: 12px;
  }
  &_gray {
    background: @colorA13;
  }

  .start {
    margin-right: 4px;
  }
  .slider_content {
    flex: 1;
    :global {
      .ant-slider {
        cursor: url('@/renderer/static/svg/cursor.cur'), pointer !important;
        margin: 6px 5px;
      }
      .ant-slider .ant-slider-rail {
        background-color: @colorC3;
        height: 2px;
      }
      .ant-slider .ant-slider-track {
        background-color: @colorC4;
        height: 2px;
      }
      .ant-slider .ant-slider-handle {
        border-color: @colorC2;
        width: 8px;
        height: 8px;
      }
      .ant-slider .ant-slider-handle::after {
        cursor: url('@/renderer/static/svg/cursor.cur'), pointer !important;
        background: @colorC4;
        box-shadow: none;
      }
      .ant-slider .ant-slider-handle:hover::after,
      .ant-slider .ant-slider-handle:active::after,
      .ant-slider .ant-slider-handle:focus::after {
        background: @colorC2;
      }

      .ant-tooltip .ant-tooltip-inner {
        min-height: unset;
        background: @colorA4 !important;
        border-radius: 2px;
        padding: 2px 8px;
      }
      .ant-tooltip-inner-content {
        font-family: normal-font;
        color: @colorA11 !important;
      }
      .ant-tooltip-arrow {
        width: 14px !important;
        height: 8px !important;
        left: 50%;
        bottom: 0;
        transform: translateX(-50%) translateY(100%) rotate(180deg);
      }
      .ant-tooltip-arrow:before {
        background-color: transparent;
        clip-path: none;
        width: 12px;
        height: 8px;
        background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiBmaWxsPSJub25lIiB2ZXJzaW9uPSIxLjEiIHdpZHRoPSIxOCIgaGVpZ2h0PSIxMyIgdmlld0JveD0iMCAwIDE4IDEzIj48ZyBzdHlsZT0ibWl4LWJsZW5kLW1vZGU6cGFzc3Rocm91Z2giPjxnIHN0eWxlPSJtaXgtYmxlbmQtbW9kZTpwYXNzdGhyb3VnaCIgdHJhbnNmb3JtPSJtYXRyaXgoMSwwLDAsLTEsMCwyNikiPjxwYXRoIGQ9Ik0wLDI2TDE4LDI2TDkuODIyMTksMTQuMTg3NjFDOS40MjQ1NCwxMy42MTMyMjQsOC41NzU0NiwxMy42MTMyMjQsOC4xNzc4MSwxNC4xODc2MUwwLDI2WiIgZmlsbD0iIzJDMkMzQyIgZmlsbC1vcGFjaXR5PSIxIi8+PC9nPjwvZz48L3N2Zz4=');
        background-size: contain;
        background-repeat: no-repeat;
        transform: rotate(-180deg);
      }
      .ant-tooltip .ant-tooltip-arrow:after {
        background: @colorA4;
      }
    }
  }
  .end {
    margin-left: 4px;
  }

  .start,
  .end {
    color: @colorA11;
    font-family:
      normal-font -apple-system,
      BlinkMacSystemFont,
      'Segoe UI',
      Roboto,
      'Helvetica Neue',
      Arial,
      'Noto Sans',
      sans-serif,
      'Apple Color Emoji',
      'Segoe UI Emoji',
      'Segoe UI Symbol',
      'Noto Color Emoji' !important;
    font-size: 12px;
  }
}

.ng_slider_tooltip {
  font-size: 12px;
}
