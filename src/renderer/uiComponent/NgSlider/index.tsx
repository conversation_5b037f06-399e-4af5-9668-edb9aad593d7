import React from 'react';
import { Slider, SliderSingleProps } from 'antd';
import styles from './index.module.less';
import classnames from 'classnames';
import { SliderRangeProps } from 'antd/lib/slider';

type Props = (SliderSingleProps | SliderRangeProps) & { isGray?: boolean; suffix?: string; overlayClassName?: string };

const renderTooltip = (value?: number, suffix?: string): React.ReactNode | null => {
  if (value === undefined) return null;
  if (typeof value === 'string') return null;

  const val = suffix ? `${value}${suffix}` : value;

  return (
    <div className={styles.ng_slider_tooltip}>
      <div className={styles.ng_slider_tooltip_content}>{val}</div>
    </div>
  );
};
export const NgSlider = (props: Props) => {
  const { isGray, suffix, overlayClassName, ...sliderProps } = props;
  const [min] = React.useState(() => {
    return props.suffix ? `${props.min}${props.suffix}` : props.min;
  });
  const [max] = React.useState(() => {
    return props.suffix ? `${props.max}${props.suffix}` : props.max;
  });

  return (
    <div className={classnames(styles.ng_slider, isGray ? styles.ng_slider_gray : '', overlayClassName)}>
      <span className={styles.start}>{min}</span>
      <div className={styles.slider_content}>
        <Slider
          {...sliderProps}
          tooltip={{
            getPopupContainer: (targetNode: HTMLElement) => {
              return targetNode.parentNode as HTMLElement;
            },
            formatter: (value?: number) => renderTooltip(value, props.suffix),
          }}
        />
      </div>
      <span className={styles.end}>{max}</span>
    </div>
  );
};
