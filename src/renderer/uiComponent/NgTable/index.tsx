import React from 'react';
import { Table, TableProps } from 'antd';
import styles from './index.module.less';
import classnames from 'classnames';

interface CustomTableProps<T> extends TableProps<T> {
  data: T[];
  pagination: TableProps<T>['pagination'];
}

const NgTable = <T extends object>({ data, columns, pagination, ...restProps }: CustomTableProps<T>) => {
  return (
    <div className={classnames(styles.ng_table, 'global-table')}>
      <Table
        className={classnames(restProps.className, {
          [styles.scrollContainer]: Object.keys(restProps.scroll || {}).length > 0,
          [styles.noScroll]: !restProps.scroll,
        })}
        rowClassName={() => {
          return styles.rowBg;
        }}
        // ! 开启bordered 会使用table的分割线样式，目前设计同学不需要分割线
        // bordered={restProps.bordered || true}
        columns={columns}
        dataSource={data}
        pagination={pagination === false? false: { ...pagination, showTitle: false }}
        {...restProps}
      />
    </div>
  );
};

export default NgTable;
