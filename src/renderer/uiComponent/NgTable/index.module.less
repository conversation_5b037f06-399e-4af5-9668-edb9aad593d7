@import '@/renderer/static/style/baseColor.module.less';

.ng_table {
  .rowBg {
    background: @colorA3;
    font-family: normal-font, serif !important;
    color: @colorA10;
    font-size: 16px;

    &:last-child {
      & td {
        //border-color: @colorA3 !important;
      }

      & td:first-child {
        //border-bottom-left-radius: 6px;
      }

      & td:last-child {
        //border-bottom-right-radius: 6px;
      }
    }
  }

  .noScroll {
    :global {
      .ant-select-open {
        .ant-select-arrow {
          background: url('../../static/images/arrow_up.png') no-repeat;
        }
      }

      .ant-table {
        background: transparent !important;
      }
    }
  }

  .scrollContainer {
    :global {
      .ant-table {
        background: @colorA1  !important;
      }
    }
  }

  :global {
    .ant-empty-description {
      color: #fff;
    }

    .ant-pagination-total-text {
      color: @colorA12;
      height: 24px;
      line-height: 24px;
      margin-right: 12px;
    }

    // 升序
    [aria-sort='ascending'] {
      & .ant-table-column-sorter {
        background: url('@/renderer/static/svg/sort_up.svg') no-repeat center center !important;

        &:hover {
          background: @colorA5 url('@/renderer/static/svg/sort_up.svg') no-repeat center center !important;
        }
      }
    }

    // 降序
    [aria-sort='descending'] {
      & .ant-table-column-sorter {
        background: url('@/renderer/static/svg/sort_down.svg') no-repeat center center !important;

        &:hover {
          background: @colorA5 url('@/renderer/static/svg/sort_down.svg') no-repeat center center !important;
        }
      }
    }

    .ant-table-container {
      overflow: hidden;
      border-inline-start: 0px !important;

      & table {
        border-top: 0px !important;
      }
    }

    .ant-table-placeholder>td {

      &,
      &:hover,
      &:focus {
        background: @colorA1  !important;
      }
    }


    .ant-table-wrapper .ant-table {
      font-size: 16px !important;
    }

    .ant-table-filter-column {
      width: fit-content;
    }

    .ant-table-column-title {
      margin-right: 0;
      flex: none !important;
    }

    .ant-table-wrapper .ant-table-column-sorter {
      width: 22px;
      height: 22px;
      color: @colorC3;
      z-index: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 2px;
      background: url('@/renderer/static/svg/sort_default.svg') no-repeat center center;
      margin-left: 0;

      &:hover {
        background: @colorA5 url('@/renderer/static/svg/sort_default.svg') no-repeat center center;
      }

      & span {
        display: none;
      }
    }

    .ant-table-cell-scrollbar {
      //border-start-end-radius: 8px !important;
      box-shadow: @colorA4 0 1px 0 1px !important;
      border-bottom-color: @colorA4  !important;
      background: @colorA4  !important;
      transition: background 0.2s ease;
      margin: 0;
      padding: 0;
    }

    .ant-table-column-sort {
      background: @colorA3  !important;
    }

    .ant-table-wrapper .ant-table-cell-fix-left-last,
    .ant-table-wrapper .ant-table-cell-fix-right-first {
      background: @colorA3;
    }

    .ant-table-thead {
      height: 54px;
    }

    .ant-table-thead>tr>th {
      color: @colorA11;
      font-family: medium-font, serif !important;
      background: @colorA4  !important;
      border-color: @colorA5;
      border-inline-end: 0 !important;
      padding: 0 16px 0 20px;
      border-bottom: 0 !important;

      &::before {
        content: '';
        display: none;
      }
    }

    .ant-table-wrapper .ant-table-thead th.ant-table-column-has-sorters:focus-visible {
      color: @colorA11;
    }

    .ant-pagination-jump-prev,
    .ant-pagination-jump-next {
      min-width: 24px;
      min-height: 24px;
      width: 24px !important;
      height: 24px !important;
      color: @colorA10;
      background: #434351;
      font-family: normal-font, serif;
      border-radius: 2px;
      font-size: 12px;

      & a {
        line-height: 18px;
      }

      .ant-pagination-item-container {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      & .ant-pagination-item-ellipsis {
        color: @colorA10  !important;
        line-height: 24px;
      }
    }

    .ant-pagination-options {
      display: flex;
      margin-left: 8px;
      height: 20px;
      font-size: 12px;

      .ant-select {
        cursor: url('@/renderer/static/svg/cursor.cur'), pointer !important;
      }

      & .ant-select-single {
        height: 24px !important;
      }

      .ant-select-show-arrow {
        display: flex;
        align-items: center;
      }

      .ant-select-arrow {
        width: 20px;
        height: 20px;
        background: url('@/renderer/static/images/arrow_down.png');
        right: 3%;

        & svg {
          display: none;
        }
      }

      & .ant-select-selector {
        background: @colorA5;
        height: 24px;
        border-radius: 2px;
        padding: 0;
        border: 0 !important;
        box-shadow: unset !important;
      }

      & .ant-select-selection-item {
        display: flex;
        align-items: center;
        padding-inline-end: 0 !important;
        font-size: 12px;
        font-family: normal-font, serif;
        padding-left: 4px;
        box-sizing: border-box;
        color: @colorA10  !important;
        margin-right: 25px;
        cursor: url('@/renderer/static/svg/cursor.cur'), pointer !important;
      }

      & .ant-select-dropdown {
        background: @colorA5;
        border-radius: 2px;
        font-family: normal-font, serif;
        cursor: url('@/renderer/static/svg/cursor.cur'), pointer !important;

        & .ant-select-item {
          cursor: url('@/renderer/static/svg/cursor.cur'), pointer !important;
          font-size: 12px !important;
          padding: 1px 0 1px 6px;
          min-height: 20px;
          font-weight: 0;
          font-family: normal-font, serif;

          &:not(.ant-select-item-option-selected):hover {
            background: @colorA4;
          }
        }

        .ant-select-item-option-selected {
          background: @colorA6;
        }
      }

      & .ant-select-selection-search {
        display: none;
      }
    }

    .rc-virtual-list-holder-inner {
      cursor: url('@/renderer/static/svg/cursor.cur'), pointer !important;
    }

    .ant-pagination-prev,
    .ant-pagination-next,
    .ant-pagination-item,
    .ant-pagination-item-link {
      min-width: 24px;
      min-height: 24px;
      width: 24px !important;
      height: 24px !important;
      color: @colorA10;
      font-family: normal-font, serif;
      background: @colorA5;
      border-radius: 2px;
      cursor: url('@/renderer/static/svg/cursor.cur'), pointer !important;

      & svg {
        color: @colorA10;
      }

      & a {
        cursor: url('@/renderer/static/svg/cursor.cur'), pointer !important;
      }
    }

    .ant-pagination .ant-pagination-item:not(.ant-pagination-item-active):hover {
      border: 1px @colorA6 solid;
      box-sizing: border-box;
      background-color: @colorA5  !important;
    }

    .ant-pagination .ant-pagination-item-active:hover {
      & a {
        color: @colorA10;
      }
    }

    .anticon-left {
      width: 100%;
      height: 100%;
      background: url('@/renderer/static/images/left.png') center center;

      & svg {
        display: none;
      }
    }

    .anticon-right {
      width: 100%;
      height: 100%;
      background: url('@/renderer/static/images/right.png') center center;

      & svg {
        display: none;
      }
    }

    .ant-pagination-item {
      display: flex;
      justify-content: center;
      align-items: center;

      & a {
        padding: 0;
        font-size: 12px;
        color: @colorA10;
        font-family: normal-font, serif;
        box-sizing: border-box;
      }
    }

    .ant-pagination .ant-pagination-item-active {
      border-color: @colorA6;
      background-color: @colorA4;
    }

    .ant-table-wrapper .ant-table-tbody>tr>td {
      border-color: @colorA5;
      border-inline-end: 0 !important;
      padding: 0 16px 0 20px;
      height: 54px;
      border-bottom-color: transparent !important;
    }

    .ant-table-wrapper .ant-table-tbody>tr.ant-table-row:hover>td {
      background: @colorA4_1  !important;
    }

    .ant-pagination-options-quick-jumper {
      margin-left: 0;
      color: @colorA12;
      height: 24px;
      font-size: 12px;
      font-family: normal-font, serif;
      display: flex;
      align-items: center;

      & input {
        width: 40px;
        height: 24px;
        margin: 0 4px;
        border-radius: 2px;
        background-color: @colorA5;
        caret-color: @colorA6;
        border: 1px transparent solid;
        color: @colorA10;

        &:focus,
        &:hover {
          border-color: @colorA6;
          background-color: @colorA5;
        }
      }
    }

    .ant-table-empty {
      .ant-table-body {
        &::-webkit-scrollbar {
          background: @colorA1;
        }
      }
    }

    .ant-table-body {
      &::-webkit-scrollbar {
        width: 6px; //y轴滚动条粗细
        height: 0; //x轴滚动条粗细
        background: @colorA3;
        border-radius: 0 0 10px 0;
        padding-bottom: 20px;
      }

      &::-webkit-scrollbar-corner {
        background: @colorA1;
      }

      &::-webkit-scrollbar-thumb {
        background: @colorA6;
        border-radius: 6px;
        height: 6px !important;
        min-height: 54px;
        position: absolute;
        right: 20px;
      }
    }

    .ant-table-column-has-sorters {
      cursor: url('@/renderer/static/svg/cursor.cur'), pointer !important;
    }
  }
}

.scrollContainer {
  :global {
    .ant-table {
      background: @colorA1;
    }
  }
}