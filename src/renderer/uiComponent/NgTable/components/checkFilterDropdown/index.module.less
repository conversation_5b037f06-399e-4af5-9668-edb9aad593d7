@import '@/renderer/static/style/baseColor.module.less';
.container {
  padding: 10px 8px;
  text-align: center;
  font-size: 14px !important;
  color: @colorA12;
  background: @colorA5;
  border-radius: 6px;
  .active {
    color: @colorC1;
  }
  .item {
    height: 32px;
    line-height: 32px;
    &:hover {
      background: @colorA4;
      border-radius: 6px;
      cursor: url('@/renderer/static/svg/cursor.cur'), pointer;
    }
  }
}
:global {
  .ant-table-column-sorters {
    justify-content: flex-start !important;
    margin-left: 0 !important;
  }
  .ant-table-filter-trigger {
    display: block;
    width: 22px;
    margin-inline: 0 !important;
    margin-left: 0 !important;
    height: 22px;
    background: url('@/renderer/static/svg/table_filter.svg') no-repeat center center;
    margin-block: 0 !important;
    border-radius: 2px !important;
    cursor: url('@/renderer/static/svg/cursor.cur'), pointer !important;
    & span {
      display: none;
    }
  }
  .ant-table-wrapper .ant-table-filter-trigger:hover {
    background: @colorA5 url('@/renderer/static/svg/table_filter.svg') no-repeat center center;
  }
  .ant-dropdown .ant-table-filter-dropdown {
    background: transparent;
  }
}
