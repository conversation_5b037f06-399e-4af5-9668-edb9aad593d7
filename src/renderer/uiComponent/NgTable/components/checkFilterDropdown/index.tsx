import React, { useState } from 'react';
import { FilterDropdownProps } from 'antd/lib/table/interface';
import styles from './index.module.less';
import classnames from 'classnames';
import { isEqual } from 'lodash';
import { isDev } from '@/common/lib/env/nodeEnv';
export interface IFilterType {
  type: number;
  value: string;
}
type Props = FilterDropdownProps & {
  onChange: Function;
  selectList: IFilterType[];
  domClassName: string;
  defaultCheck?: string | number;
} & Required<Pick<FilterDropdownProps, 'close'>>;
const CheckFilterDropdown = (props: Props) => {
  const defaultItem = props.selectList.find(item => item.type === props.defaultCheck);
  const [current, setCurrent] = useState<IFilterType | undefined>(+defaultItem?.type! !== 0 ? defaultItem : undefined);
  // confirm ： Table中props中的confirm用于关闭下拉
  const { selectList, onChange, close, domClassName } = props;
  const onClick = async (item: IFilterType) => {
    let fileUrl = [
      `file://${await window.fileAPI.getDirName()}colormap/table_filter.svg`,
      `file://${await window.fileAPI.getDirName()}colormap/table_filter_active.svg`,
    ];
    const devUrl = ['http://localhost:4001/colormap/table_filter.svg', 'http://localhost:4001/colormap/table_filter_active.svg'];
    const domElement = document.querySelector(domClassName) as HTMLElement;
    if (isEqual(item, current)) {
      domElement.style.backgroundImage = `url("${isDev ? devUrl[0] : fileUrl[0]}")`;
      domElement.style.backgroundRepeat = 'no-repeat';
      setCurrent(undefined);
      onChange(undefined, close);

      return;
    }
    domElement.style.backgroundImage = `url("${isDev ? devUrl[1] : fileUrl[1]}")`;
    domElement.style.backgroundRepeat = 'no-repeat';
    setCurrent(item);
    onChange(item, close);
  };

  return (
    <>
      <div
        className={classnames(styles.container, 'check-filter-drop-down', {
          [styles.defaultChecked]: current,
        })}
        data-column={current ? 'active' : ''}
      >
        {selectList.map((item: any, index: number) => (
          <div
            key={index}
            // 这里是对当前选择的item加背景色。
            className={classnames(styles.item, {
              [styles.active]: isEqual(current, item),
            })}
            onClick={async () => onClick(item)}
          >
            {item.value}
          </div>
        ))}
      </div>
    </>
  );
};
export default CheckFilterDropdown;
