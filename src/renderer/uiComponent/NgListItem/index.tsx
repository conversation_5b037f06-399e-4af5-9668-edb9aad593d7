import React, { MouseE<PERSON>Handler, memo, ReactNode, FC, useEffect, useState, useRef } from 'react';
import classnames from 'classnames';
import { Tooltip } from 'antd';
import styles from './index.module.less';

interface IProps {
  children?: ReactNode;
  isActive?: boolean;
  className?: string;
  title?: string;
  fontSize?: number;
  maxWidth?: number;
  onClick?: MouseEventHandler<HTMLDivElement>;
}

const NgListItem: FC<IProps> = ({ maxWidth = Number.MAX_SAFE_INTEGER, fontSize = 14, className, children, onClick, title }: IProps) => {
  const [isOverFlow, setIsOverFlow] = useState(false);
  const spanRef = useRef<HTMLSpanElement | null>(null);

  useEffect(() => {
    if (spanRef.current) {
      const isOverF = spanRef.current.offsetWidth >= maxWidth;
      setIsOverFlow(isOverF);
    }
  }, []);

  return (
    <div className={classnames(styles.ng_list_item, className)} onClick={onClick}>
      {isOverFlow ? (
        <Tooltip title={title} color="#434351">
          <span style={{ fontSize: fontSize }} ref={spanRef}>
            {title}
          </span>
        </Tooltip>
      ) : (
        <span style={{ fontSize: fontSize }} ref={spanRef}>
          {title}
        </span>
      )}
    </div>
  );
};

export default memo(NgListItem);
