@import '@/renderer/static/style/baseColor.module.less';
.openContainer {
  :global {
    .ant-message-notice-content {
      padding: 12px 16px;
      box-sizing: border-box;
    }

    .ant-message-custom-content {
      display: flex;
      align-items: center;
      & svg {
        font-size: 14px !important;
        margin-right: 9px;
      }
      & span {
        color: @colorA1;
        font-family: normal-font;
      }
    }
  }
}
.rotateLoading {
  & svg {
    animation: animationRotate 1s linear infinite !important;
  }
}
@keyframes animationRotate {
  0% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(90deg);
  }
  50% {
    transform: rotate(180deg);
  }
  75% {
    transform: rotate(270deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
