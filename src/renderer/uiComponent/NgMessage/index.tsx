import React from 'react';
import { message } from 'antd';
import { ArgsProps } from 'antd/lib/message';
import { ReactComponent as Success } from '@/renderer/static/svg/successMessage.svg';
import { ReactComponent as Error } from '@/renderer/static/svg/errorMessage.svg';
import { ReactComponent as Warning } from '@/renderer/static/svg/warnMessage.svg';
import { ReactComponent as Loading } from '@/renderer/static/svg/loading.svg';
import classnames from 'classnames';
import styles from './index.module.less';
import { ConfigOptions } from 'antd/lib/message/interface';
import { uniqueId } from 'lodash';

const NgMessage = (props: {}) => {
  return <></>;
};

// eslint-disable-next-line max-lines-per-function
NgMessage.useMessage = (config?: ConfigOptions) => {
  const [messageApi, contextHolder] = message.useMessage({ ...config });

  const open = (props: ArgsProps) => {
    const key = props.key || uniqueId('open');
    let icon = <></>;
    switch (props.type) {
      case 'success': {
        icon = <Success />;
        break;
      }
      case 'error': {
        icon = <Error />;
        break;
      }
      case 'warning': {
        icon = <Warning />;
        break;
      }
      case 'loading': {
        icon = <Loading />;
        break;
      }
    }
    if (props.duration) {
      destroyMessage(key, +props.duration);
    }

    return messageApi.open({
      ...props,
      key,
      type: props.type,
      icon,
      className: classnames(props.className, styles.openContainer, {
        [styles.rotateLoading]: props.type === 'loading',
      }),
    });
  };
  const loading = (props: Omit<ArgsProps, 'type' | 'icon'>) => {
    let icon = <Loading />;
    const key = props.key || uniqueId('loading');
    if (props.duration) {
      destroyMessage(key, +props.duration);
    }

    return messageApi.open({
      ...props,
      key,
      type: 'loading',
      icon,
      className: classnames(props.className, styles.openContainer, styles.rotateLoading),
    });
  };
  const error = (props: Omit<ArgsProps, 'type' | 'icon'>) => {
    let icon = <Error />;
    const key = props.key || uniqueId('error');
    if (props.duration) {
      destroyMessage(key, +props.duration);
    }

    return messageApi.open({
      ...props,
      key,
      type: 'error',
      icon,
      className: classnames(props.className, styles.openContainer),
    });
  };
  const warning = (props: Omit<ArgsProps, 'type' | 'icon'>) => {
    let icon = <Warning />;
    const key = props.key || uniqueId('warning');
    if (props.duration) {
      destroyMessage(key, +props.duration);
    }

    return messageApi.open({
      ...props,
      icon,
      type: 'warning',
      key,
      className: classnames(props.className, styles.openContainer),
    });
  };
  const success = (props: Omit<ArgsProps, 'type' | 'icon'>) => {
    let icon = <Success />;
    const key = props.key || uniqueId('success');
    if (props.duration) {
      destroyMessage(key, +props.duration);
    }

    return messageApi.open({
      ...props,
      icon,
      type: 'success',
      key,
      className: classnames(props.className, styles.openContainer),
    });
  };
  const destroyMessage = (key: React.Key, duration = 3000) => {
    setTimeout(() => {
      messageApi.destroy(key);
    }, duration);
  };

  return {
    success,
    warning,
    error,
    loading,
    open,
    destroy: messageApi.destroy,
    contextHolder,
  };
};

export default NgMessage;
