@import '../../../renderer/static/style/baseColor.module.less';

.verticalLayout {
  :global {
    .ant-form-item-label {
      & label {
        color: @colorA12;
        font-family: normal-font, serif !important;
      }
      &::after {
        color: @colorA12 !important;
        content: ':';
        position: relative;
        margin-block: 0;
        margin-inline-start: 2px;
        margin-inline-end: 8px;
      }
    }
  }
}
.baseLayout {
  :global {
    .ant-form-item-label {
      & label {
        color: @colorA12;
        font-family: normal-font, serif !important;
      }
    }
    .ant-form-item-explain-error {
      color: @colorB3;
      font-family: normal-font, serif !important;
      font-size: 12px;
    }
    .ant-input-status-error:not(.ant-input-disabled):not(.ant-input-borderless).ant-input {
      border-color: @colorB3 !important;
    }
    .ant-form-item .ant-form-item-label > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before {
      color: @colorB3;
    }

    .ant-form-item-extra {
      position: absolute;
      right: 0px;
      top: 32px;
      color: @colorA9;
      font-family: normal-font, serif !important;
    }
  }
}
