import React, { FC, PropsWithChildren } from 'react';
import { Form, FormItemProps, FormProps } from 'antd';
import classnames from 'classnames';
import styles from './index.module.less';

type NgFormProps = FormProps;

export const NgFormItem = (props: FormItemProps & PropsWithChildren) => {
  return <Form.Item {...props}>{props?.children}</Form.Item>;
};
export const NgForm: FC<NgFormProps & PropsWithChildren & { formRef?: any }> = ({
  children,
  initialValues,
  onFinish,
  onFinishFailed,
  formRef,
  ...res
}) => {
  const [form] = Form.useForm();

  const handleSubmit = async (values: any) => {
    onFinish?.(values);
  };

  const handleFinishFailed = (errorInfo: any) => {
    // eslint-disable-next-line @typescript-eslint/no-unused-expressions
    onFinishFailed && onFinishFailed(errorInfo);
  };

  return (
    <Form
      form={res.form ?? form}
      ref={formRef}
      {...res}
      initialValues={initialValues}
      onFinish={handleSubmit}
      onFinishFailed={handleFinishFailed}
      className={classnames(
        res.className,
        {
          [styles.verticalLayout]: res.layout === 'vertical',
        },
        styles.baseLayout
      )}
    >
      {children}
    </Form>
  );
};
