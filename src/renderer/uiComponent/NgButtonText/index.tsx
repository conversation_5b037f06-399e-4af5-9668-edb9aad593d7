import React, { CSSProperties, PropsWithChildren } from 'react';
import styles from './index.module.less';
import classnames from 'classnames';

type Props = {
  style?: CSSProperties;
  buttonTextMode?: 'default' | 'link'; // default：白色文字hover为colorC4 link：类似于a标签的效果
  onClick?: ((e: React.MouseEvent<any, any>) => void) | undefined;
  className?: string;
};
// 用于展示纯hover的文字
const NgButtonText = ({ children, style, buttonTextMode = 'default', onClick, className }: Props & PropsWithChildren) => {
  return (
    <div
      style={style}
      onClick={onClick}
      className={classnames(styles.btnText, className, {
        [styles.link]: buttonTextMode === 'link',
      })}
    >
      {children}
    </div>
  );
};
export default NgButtonText;
