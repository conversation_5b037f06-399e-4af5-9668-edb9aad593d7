import React, { PropsWithChildren } from 'react';
import { Popover, PopoverProps } from 'antd';
import styles from './index.module.less';
import classnames from 'classnames';
import NgButton from '@/renderer/uiComponent/NgButton';
import NgButtonText from '@/renderer/uiComponent/NgButtonText';

type Props = PopoverProps &
PropsWithChildren & {
  showOperation?: {
    // 是否展示 确认 取消 按钮
    onOk(): void;
    onCancel(): void;
  };
};
const NgPopover = ({ children, overlayClassName, content, showOperation, ...rest }: Props) => {
  const { onOk, onCancel } = showOperation || {};

  return (
    <Popover
      content={
        showOperation ? (
          <>
            {content}
            <div className={styles.operationContainer}>
              <NgButtonText onClick={onCancel}>取消</NgButtonText>
              <NgButton buttonMode={'popover'} onClick={onOk}>
                确认
              </NgButton>
            </div>
          </>
        ) : (
          content
        )
      }
      overlayClassName={classnames(overlayClassName, styles.ng_popover)}
      {...rest}
    >
      {children}
    </Popover>
  );
};
export default NgPopover;
