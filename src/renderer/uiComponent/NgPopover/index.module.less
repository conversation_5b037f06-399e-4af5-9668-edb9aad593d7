@import '../../static/style/baseColor.module.less';
.ng_popover {
  .operationContainer {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
    & div:first-child {
      margin-right: 16px;
    }
  }
  :global {
    .ant-popover-inner {
      background: @colorA4;
      border-radius: 6px;
    }
    .ant-popover-inner-content {
      font-family: normal-font;
      color: @colorA11 !important;
    }
    .ant-popover-arrow {
      width: 19px !important;
      height: 19px !important;
    }
    .ant-popover-arrow:before {
      background-color: transparent;
      clip-path: none;
      width: 18px;
      height: 13px;
      background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiBmaWxsPSJub25lIiB2ZXJzaW9uPSIxLjEiIHdpZHRoPSIxOCIgaGVpZ2h0PSIxMyIgdmlld0JveD0iMCAwIDE4IDEzIj48ZyBzdHlsZT0ibWl4LWJsZW5kLW1vZGU6cGFzc3Rocm91Z2giPjxnIHN0eWxlPSJtaXgtYmxlbmQtbW9kZTpwYXNzdGhyb3VnaCIgdHJhbnNmb3JtPSJtYXRyaXgoMSwwLDAsLTEsMCwyNikiPjxwYXRoIGQ9Ik0wLDI2TDE4LDI2TDkuODIyMTksMTQuMTg3NjFDOS40MjQ1NCwxMy42MTMyMjQsOC41NzU0NiwxMy42MTMyMjQsOC4xNzc4MSwxNC4xODc2MUwwLDI2WiIgZmlsbD0iIzJDMkMzQyIgZmlsbC1vcGFjaXR5PSIxIi8+PC9nPjwvZz48L3N2Zz4=');
      background-size: auto auto;
      background-repeat: no-repeat;
      transform: rotate(-180deg);
    }
    .ant-popover-title {
      color: @colorA12;
      font-family: medium-font !important;
      margin-bottom: 6px;
    }
  }
}
