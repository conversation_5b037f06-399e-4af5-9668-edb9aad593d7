@import '../../static/style/baseColor.module.less';
.preview_treat {
  height: 100%;
  position: relative;
  :global {
    .ant-spin-nested-loading,
    .ant-spin-container {
      height: 100%;
      .ng_spin {
        max-height: 100%;
        .ant-spin-dot {
          width: unset;
          height: unset;
          transform: translateY(-50%);
        }
      }
    }
    .ng_breadcrumb {
      position: absolute;
      left: 0px;
      top: 20px;
      z-index: 10;
    }
    .helper_switch{
      position: absolute;
      top: 22px;
      right: 33px;
      width: 118px;
      display: flex;
      z-index: 20;
      .switch{
        display: flex;
        align-items: center;
      }
      .ant-switch.ant-switch-small.ant-switch-checked .ant-switch-handle{
        inset-inline-start: 10px !important;
      }
      .ant-switch{
        min-width: 18px;
        height: 12px;
        &:focus-visible{
          border: none;
          display: unset;
          outline: unset;
        }
        .ant-switch-handle {
          top: 3px;
          width: 6px;
          height: 6px;
          &::before {
            background-color: @colorA3;
          }
        }
        .ant-switch-inner {
          padding-inline-start: initial;
          padding-inline-end: initial;
          &:focus-visible{
            border: none;
            display: unset;
          }
        }
      }
    }
    .photo_and_coil {
      top: 20px;
    }
    .content {
      display: flex;
      padding: 0 12px;
      height: 100%;
      padding-top: 52px;
      // height: calc(100% - 32px);
      justify-content: space-between;
      .surface_spot_view {
        width: calc(100% - 300px);
        display: flex;
        justify-content: space-between;
        padding-right: 21px;
        position: relative;
        .patient_name {
          position: absolute;
          top: 8px;
          left: 6px;
          color: @colorA10;
          z-index: 20;
        }
      }
      .immediate_view_box{
        margin-top: 57px;
      }
      .surface_mask {
        position: absolute;
        width: calc(100% - 450px);
        height: 100%;
        z-index: 1;
        background-color: @colorA1;
      }
      .surface_box {
        width: calc(100% - 410px);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9;
        .volume-container {
          display: none;
        }
      }
      .spot_list_and_control {
        width: 314px;
        height: 100%;
        display: flex;
        justify-content: space-between;
        padding-bottom: 20px;
        .spot_list {
          width: 314px;
          overflow-y: auto;
          &::-webkit-scrollbar {
            width: 4px;
          }
          &::-webkit-scrollbar-thumb {
            background: @colorA6;
            border-radius: 10px;
          }
          &::-webkit-scrollbar-thumb:vertical {
            width: 10px;
            height: 10px;
          }
        }
      }
      .stimulate_template_button {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        padding-bottom: 12px;
        .stimulate_template {
          width: 300px;
          height: 960px;
          padding: 20px;
          border-radius: 6px;
          background-color: @colorA3;
          .relative_strength {
            display: none;
          }
          .import_tem {
            color: @colorA9;
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin: 0 0 12px;
          }
        }
        .ensure_template {
          width: 100%;
          .ant-btn {
            width: 100%;
            height: 40px;
            border-radius: 8px;
            background-color: @colorA4_1;
            color: @colorA12;
          }
          .ant-btn[disabled] {
            color: @colorA6;
          }
        }
      }
      .window_and_treat_info {
        width: 722px;
        height: 100%;
        display: flex;
        justify-content: space-between;
        padding-bottom: 20px;
        .window {
          width: 410px;
          height: 450px;
        }
      }
      .treat_info {
        height: 100%;
        position: relative;
        padding-top: 57px;
        .blank {
          height: 16px;
          background-color: @colorA1;
          z-index: 9;
          position: relative;
        }
        .stop_treat {
          width: 100%;
          margin-top: 16px;
          position: absolute;
          bottom: 12px;
          .ant-btn {
            width: 100%;
            height: 40px;
            border-radius: 8px;
            background-color: @colorA4_1;
            color: @colorA12;
            margin-top: 12px;
          }
        }
      }
    }
    .ant-modal-root {
      .back_modal {
        height: 233px;

        .ant-modal-footer {
          display: none;
        }
        .ant-modal-body {
          display: flex !important;
          flex-direction: column !important;
          justify-content: center;
          align-items: center;
          margin-top: 8px;
          margin-bottom: 10px;
          .action {
            width: 50px;
            height: 50px;
          }
          .treat_complete_text {
            font-size: 16px;
            color: @colorA11;
            margin-top: 13px;
          }
          .auto_hide_text {
            height: 24px;
            line-height: 24px;
            margin-top: 4px;
            font-size: 12px;
            color: @colorA9;
          }
          .back_button {
            margin-top: 20px;
            width: 88px;
            height: 32px;
            .ant-btn {
              width: 88px;
            }
          }
        }
      }
    }
    .helper_modal{
      .ant-modal-body{
        font-size: 16px;
        font-weight: 350;
        .text{
          margin-top: 8px;
          .blank{
            display: inline-block;
            width: 8px;
          }
        }
      }
      .ant-modal-footer{
        .footer{
          display: flex;
          justify-content: flex-end;
          align-items: center;
        }
      }
    }
    .msg{
      position: absolute;
      top: 109px;
      left: 50%;
      transform: translateX(-50%);
      z-index: 9;
      width: 269px;
      height: 48px;
      background-color: @colorA5;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      svg {
        margin-right: 9px;
      }
    }
  }
}