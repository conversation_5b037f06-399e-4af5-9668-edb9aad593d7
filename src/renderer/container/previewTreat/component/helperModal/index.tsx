import React from 'react';
import NgModal from '@/renderer/uiComponent/NgModal';
import NgButton from '@/renderer/uiComponent/NgButton';

type Props = {
  visible: boolean;
  onOk(): void;
  onClose(): void;
};

export const HelperModal: React.FC<Props> = ({
  visible,
  onOk,
  onClose,
}) => {
  return (
    <NgModal
      title='确认关闭目标识别辅助模式？'
      wrapClassName='helper_modal'
      open={visible}
      // closable={false}
      getContainer={() => document.querySelector('.preview_treat') || document.body}
      onCancel={onClose}
      footer={(
        <div className='footer'>
          <NgButton onClick={onOk}>确认关闭</NgButton>
        </div>
      )}
    >
      <div className='tips'>关闭后，以下功能无法使用</div>
      <div className='text'>
        <div>·<span className='blank'/>控制患者、线圈在有效视觉范围内进行刺激治疗</div>
        <div>·<span className='blank'/>追踪靶点实时参数阶梯数值展示</div>
        <div>·<span className='blank'/>更加严格的靶点追踪范围限制</div>
      </div>
    </NgModal>
  );
};
