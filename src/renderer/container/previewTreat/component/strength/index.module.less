@import '../../../../static/style/baseColor.module.less';

.strength_and_rotate {
  font-size: 14px;

  :global {
    .strength {
      .item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 25px;

        .strength_progress {
          position: relative;
          z-index: 2;

          &>div {
            transform: unset;
          }
        }

        .title {
          color: @colorA9;
        }
      }

      .relative_strength_content {
        align-items: unset;
        margin-bottom: 5px;

        .title {
          height: 20px;
          margin-top: 7px;
        }

        .strength_level {
          margin-top: 3px;
          width: 100%;
          display: flex;
          justify-content: space-between;

          .error_info {
            display: inline-block;
            width: auto;
            font-size: 12px;
            color: @colorB3;
          }

          .level {
            color: @colorA9;
          }
        }
      }
    }

    .rotate {
      position: relative;
      z-index: 9;
      margin-top: 42px;

      .ant-tooltip {
        background-color: @colorA5;
        border-radius: 6px;

        .ant-tooltip-arrow {
          left: 86%;
          height: 10px;

          &::before {
            background-color: @colorA5  !important;
          }
        }
      }

      .rotate_header {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .title {
          color: @colorA9;
        }

        .rotate_switch {
          .ant-switch.ant-switch-small {
            min-width: 18px;
            height: 12px;

            &:focus-visible {
              border: none;
              display: unset;
              outline: unset;
            }

            .ant-switch-handle {
              top: 3px;
              width: 6px;
              height: 6px;

              &::before {
                background-color: @colorA3;
              }
            }

            .ant-switch-inner {
              padding-inline-start: initial;
              padding-inline-end: initial;

              &:focus-visible {
                border: none;
                display: unset;
              }
            }
          }

          .ant-switch-checked {
            .ant-switch-handle {
              inset-inline-start: 10px !important;
            }
          }
        }
      }

      .rotate_number {
        margin-top: 10px;

        .error {
          border-color: @colorB3;
        }

        .error_info {
          height: 20px;
          margin-top: 3px;
          font-size: 12px;
          color: @colorB3;
        }
      }

      .rotate_slider {
        display: flex;
        align-items: center;

        .ant-slider {
          width: 202px;
          margin-top: 17px;
        }

        .ant-slider-track {
          height: 2px;
          background-color: @colorC4;
        }

        .ant-slider-rail {
          height: 2px;
          background-color: @colorC3;
        }

        .ant-slider-handle {
          width: 8px;
          height: 8px;

          &:hover {
            &::after {
              box-shadow: 0 0 0 2px @colorC2;
              background-color: @colorC2;
              inset-block-start: 0px !important;
              inset-inline-start: 0px !important;
            }
          }

          &::after {
            width: 8px;
            height: 8px;
            background-color: @colorC4;
            box-shadow: 0 0 0 2px @colorC4;
          }

          &::before {
            width: 8px;
            height: 8px;
            display: none;
          }
        }

        .min,
        .max {
          color: @colorA11;
        }
      }

      .rotate_slider[data-error='error'] {
        .ant-slider-handle {
          display: none;
        }
      }

      .ant-slider-disabled {

        .min,
        .max {
          color: @colorA9;
        }

        .ant-slider-track {
          background-color: @colorA6  !important;
        }

        .ant-slider-rail {
          background-color: @colorA9  !important;
        }

        .ant-slider-handle {
          &:hover {
            &::after {
              box-shadow: 0 0 0 2px @colorA6;
              background-color: @colorA6;
            }
          }

          &::after {
            background-color: @colorA6;
            box-shadow: 0 0 0 2px @colorA6;
          }
        }
      }
    }
  }
}