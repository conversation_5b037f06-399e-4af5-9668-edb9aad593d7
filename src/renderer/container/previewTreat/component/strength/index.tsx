import React, { useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'antd';
import styles from './index.module.less';
import { NgInputNumber } from '../../../../uiComponent/NgInputNumber';
import { PlanStimulusModel } from '../../../../../common/types';
import { NgStrengthProgress } from '../../../../uiComponent/NgProgress';
import NgSwitch from '@/renderer/uiComponent/NgSwitch';
import { calRelativeStrength } from '../../../../component/template/calRules';
import { ParamRulesType, cTBSParamRules } from '../../../../component/template/ctbsParamsRules';
import { getMathRoundMotionThreshold } from '../../../../component/template/calTemplate';

type Props = {
  stimulate?: PlanStimulusModel;
  strengthChange(strength: any): void;
  motion_threshold: number;
  horizontal?: number;
  horizontalChange(value: any): void;
  horizontalDisable: boolean;
  disableHorizontalChange(disable: boolean): void;
};

export const StrengthAndRotate: React.FC<Props> = props => {
  const [horizontalStatus, setHorizontalStatus] = React.useState<'error' | 'warning' | undefined>(undefined);
  const [relativeStatus, setRelativeStatus] = React.useState<'error' | 'warning' | undefined>(undefined);
  const [relativeValue, setRelativeValue] = React.useState<{ min: number; max: number }>({ min: 0, max: 0 });
  const relativeValueRef = React.useRef({ min: 0, max: 0 });
  const [powerOver, setPowerOver] = React.useState<boolean>(false);

  useEffect(() => {
    if (typeof props.horizontal !== 'number') return;
    if (
      props.horizontal > 145 ||
      props.horizontal < -145 ||
      props.horizontal !== Math.round(props.horizontal) ||
      typeof props.horizontal !== 'number' ||
      isNaN(Number(props.horizontal))
    ) {
      setHorizontalStatus('error');
    } else {
      setHorizontalStatus(undefined);
    }
  }, [props.horizontal]);

  const disableHorizontalChange = (disable: boolean) => {
    if (!disable) {
      setHorizontalStatus(undefined);
    }
    props.disableHorizontalChange(disable);
  };

  useEffect(() => {
    const { min, max } = calRelativeStrength(props.motion_threshold);
    setRelativeValue({ min, max });
    relativeValueRef.current = { min, max };
  }, [props.motion_threshold]);

  const handleStrengthChange = (strength: any) => {
    props.strengthChange(strength);
  };

  const getErrorInfo = React.useCallback((status: any, key: any) => {
    if (status !== 'error') return '';
    if (key === null) return '不可为空';

    return '不符合限制';
  }, []);

  const getStrengthErrorInfo = React.useCallback(
    (status: any, key: any) => {
      if (status !== 'error') return '';
      if (key === null) return '不可为空';
      if (typeof key !== 'number' || key > relativeValueRef.current.max || key < relativeValueRef.current.min) return '不符合限制';
      if (powerOver) return '功率超限';

      return '不符合限制';
    },
    [powerOver]
  );

  useEffect(() => {
    const strength = props?.stimulate?.relative_strength;
    if (!props.stimulate) return;
    let status;
    let _powerOver = false;
    if (typeof strength !== 'number' || strength > relativeValueRef.current.max || strength < relativeValueRef.current.min || strength !== Math.round(strength)) {
      status = 'error';
    }
    const notComplete = Object.keys(props.stimulate).find(key => [undefined, null].includes(props?.stimulate?.[key]));
    if (
      !notComplete &&
      cTBSParamRules(props.stimulate, getMathRoundMotionThreshold(props.motion_threshold, props.stimulate)) === ParamRulesType.ParamRulesError
    ) {
      status = 'error';
      _powerOver = true;
    }
    setPowerOver(_powerOver);
    setRelativeStatus(status as any);
  }, [props.stimulate, props.motion_threshold]);

  const rotateBlur = () => {
    if (props.horizontal !== 0 && !props.horizontal) {
      setHorizontalStatus('error');
    }
  };

  const horizontalChange = (value: any) => {
    if ([null, undefined].includes(value)) {
      // NOSONAR
      setHorizontalStatus('error');
    }
    props.horizontalChange(value);
  };

  return (
    <div className={styles.strength_and_rotate}>
      <div className="strength">
        <div className="item relative_strength_content">
          <span className="title">相对强度（%MT）：</span>
          <span style={{ width: 120 }}>
            <NgInputNumber
              status={relativeStatus}
              onChange={props.motion_threshold ? handleStrengthChange : () => null}
              value={props?.stimulate?.relative_strength}
            />
            <div className="strength_level">
              <span className="error_info">{getStrengthErrorInfo(relativeStatus, props?.stimulate?.relative_strength)}</span>
              <span className="level">
                {relativeValue.min}-{relativeValue.max}
              </span>
            </div>
          </span>
        </div>
        <div className="item">
          <span className="title">运动阈值（%MO）：</span>
          <span style={{ width: 120 }}>{props.motion_threshold}</span>
        </div>
        <div className="item">
          <span className="title">实际强度（%MO）：</span>
          <span className="strength_progress" style={{ width: 120 }}>
            <NgStrengthProgress
              percent={Math.min(Math.round((props.motion_threshold * (props?.stimulate?.relative_strength || 0)) / 100), 100)}
              radius={60}
            />
          </span>
        </div>
      </div>
      <div className="rotate">
        <div className="rotate_header">
          <span className="title">线圈旋转角度：</span>
          <div className="rotate_switch">
            <Tooltip
              getPopupContainer={() => document.querySelector('.rotate') || document.body}
              title={props.horizontalDisable ? '显示旋转角度' : '隐藏旋转角度'}
            >
              <NgSwitch size="small" disabled={!!horizontalStatus} checked={!props.horizontalDisable} onChange={disableHorizontalChange} />
            </Tooltip>
          </div>
        </div>
        <div className="rotate_slider" data-error={horizontalStatus} data-disable={props.horizontalDisable}>
          <span className="min">-145</span>
          {/* <NgSlider disabled={props.horizontalDisable} tooltip={{ open: false }} max={180} min={-180} onChange={horizontalChange} value={props.horizontal}/> */}
          <Slider
            disabled={props.horizontalDisable}
            tooltip={{ open: false }}
            max={145}
            min={-145}
            onChange={horizontalChange}
            value={props.horizontal}
          />
          <span className="max">145</span>
        </div>
        <div className="rotate_number">
          <NgInputNumber
            onBlur={rotateBlur}
            disabled={props.horizontalDisable}
            className={horizontalStatus ? 'error_number' : ''}
            status={horizontalStatus}
            onChange={horizontalChange}
            value={props.horizontal}
          />
          <div className="error_info">{getErrorInfo(horizontalStatus, props.horizontal)}</div>
        </div>
      </div>
    </div>
  );
};
export default React.memo(StrengthAndRotate);
