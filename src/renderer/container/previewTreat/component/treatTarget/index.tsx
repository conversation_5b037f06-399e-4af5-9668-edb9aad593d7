import React from 'react';
import styles from './index.module.less';
import { TBSChart } from '../../../../component/tbsChart';
import { EnumPlanStimulusType, PlanStimulusModel } from '../../../../../common/types';

type Props = {
  horizontal: number;
  stimulate: PlanStimulusModel;
  name: string;
  horizontalDisable: boolean;
};

export const TreatTarget: React.FC<Props> = ({ horizontal, stimulate, name, horizontalDisable }) => {
  return (
    <div className={styles.treat_target}>
      <div className="target_name">靶点名称：{name}</div>
      {!horizontalDisable && <div>旋转角度：{horizontal}</div>}
      <div className="tab_chart" data-preview={stimulate.type === EnumPlanStimulusType.RTMS ? 'true' : 'false'}>
        <TBSChart template={stimulate} />
      </div>
    </div>
  );
};
