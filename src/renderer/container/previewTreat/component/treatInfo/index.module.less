@import '../../../../static/style/baseColor.module.less';

.strength {
  width: 300px;
  height: 178px;
  background-color: @colorA3;
  border-radius: 8px;
  margin-top: 16px;
  padding: 0px 20px;
  overflow: hidden;
  :global {
    .strength_treating {
      margin-top: 15px;
      color: @colorA9;
      & > div {
        margin-bottom: 12px;
      }
    }
    .strength_treat_stop {
      .relative_strength {
        display: flex;
        align-items: center;
        margin-top: 15px;
        color: @colorA12;
        .value {
          font-size: 20px;
          color: @colorA12;
        }
        .range {
          font-size: 16px;
          margin-left: 20px;
          color: @colorA9;
        }
      }
      .threshold {
        margin: 7px 0 8px;
        color: @colorA12;
        .value {
          font-size: 20px;
          color: @colorA12;
        }
      }
      .activity_strength {
        height: 79px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
        color: @colorA12;
        z-index: 1;
        .strength_progress {
          & > div {
            transform: unset;
          }
        }
      }
    }
  }
}
.treat_info {
  width: 300px;
  height: 367px;
  padding: 0px 20px 12px;
  background-color: @colorA3;
  border-radius: 8px;
  :global {
    .item {
      width: 100%;
      margin-bottom: 5px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 33px;
      .title {
        display: flex;
        align-items: center;
        .bar {
          width: 6px;
          height: 16px;
          border-radius: 10px;
          margin-right: 10px;
        }
      }

      .value {
        font-size: 26px;
        font-weight: 500;
        text-align: right;
      }
    }
    .process {
      width: 180px;
      margin: auto;
      // margin-top: 50px;
      font-size: 16px;
      .treat_pause {
        height: 47px;
        padding: 12px 0;
        text-align: center;
        font-weight: 350;
        color: @colorB1;
      }
      .pulse {
        margin: 40px 0 10px;
        text-align: center;
      }
      .countdown {
        text-align: center;
      }
    }
    .strength {
      .strength_treating {
        margin-top: 40px;
        color: @colorA9;
        & > div {
          margin-bottom: 12px;
        }
      }
    }
  }
}
