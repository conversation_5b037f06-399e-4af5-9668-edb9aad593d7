import React from 'react';
import classNames from 'classnames';
import { useRecoilValue, useSetRecoilState, useRecoilState } from 'recoil';
import { NgStrengthProgress } from '../../../../uiComponent/NgProgress';
import { secToTime } from '../../../../component/template/calTemplate';
import NgModal from '../../../../uiComponent/NgModal';
import { NgIcon } from '../../../../uiComponent/NgIcon';
import { SuccessMessage } from '../../../../uiComponent/SvgGather';
import NgButton from '../../../../uiComponent/NgButton';
import { ReactComponent as Warning } from '@/renderer/static/svg/warning.svg';
import { connSocket, GetStatusCallbackParam } from '../../../../utils/imgSocket';
import { batInfoByMatrix } from '../../../../utils/treat';
import { StimulateModel } from '../../../noPatientTms';
import { M200Api } from '../../../../../common/api/ng/m200Api';
import { getM200ApiInstance } from '../../../../../common/api/ngApiAgent';
import styles from './index.module.less';
import { cloneDeep, isEqual, isNumber, throttle } from 'lodash';
import { isPowerOver } from '../../../../component/template/ctbsParamsRules';
import { sendRenderLog, sendTreatLog } from '../../../../utils/renderLogger';
import { TmsCoil, tmsCoilAtom } from '../../../../recoil/tmsError';
import { message } from 'antd';
import { TMSScreenState } from '../../../../../common/constant/tms';
import { TreatParam } from '../../../../../common/types/treat';
import { NavigateSupportTypeEnum, PlanTargetModel } from '../../../../../common/types';
import { calRelativeStrengthRangeByActive } from '@/renderer/component/template/calRules';
import { treatResultMap } from '../../../../constant/tmsAndImageSocket';
import { Countdown, Pluse } from '../../../../component/plusecountdown';
import { ReportEndEnnm } from '../..';
import { treatStatusAtom } from '../../../../recoil/treatStatus';
import { VisibleInfoType, initVisibleInfo, initInstructionInfo, InstructionInfo } from '../../../manage/components/manageDevice/config';
import { Fault2RenderMapType, FaultEnum, FaultLevelEnum, FaultStatusEnum } from '../../../../../common/systemFault/type';
import { faultAtom, getFaultWithoutType } from '../../../../recoil/fault';
import TreatCircleProgress from '../../../../component/treatCircleProgress';
import { throttleOption } from '../../../../utils';

type Props = {
  stimulate?: StimulateModel;
  motion_threshold: number;
  pulse_total: number;
  treat_time: number;
  treatCompleteCallback(): void;
  treatParam: TreatParam | null;
  activeSpot: PlanTargetModel;
  horizontalDisable: boolean;
  horizontal: number;
  setTreatLogsError(): void;
  updateStimulate(type: string): void;
  coilInfo: TmsCoil;
  fetchPlanEnd(type: ReportEndEnnm): void;
  tid: string;
  setTreatStatus(status: string): void;
  helper: boolean;
  fault: Fault2RenderMapType;
};
type State = {
  treatStatus: string;
  treatInfo: { count: number; time: number; remain_time?: number };
  backOpen: boolean;
  backTime: number;
  strengthRange: { min: number; max: number };
  identifyError: boolean;
  relativeStrength: number;
  visiblePart: VisibleInfoType;
  instructionPart: InstructionInfo;
};

let isUnmount = false;

export class TreatInfo extends React.PureComponent<Props, State> {
  private treatInterval:  NodeJS.Timeout | null;
  private backInterval:  NodeJS.Timeout | null;
  private listenTreatDataInterval:  NodeJS.Timeout | null;
  private stopTreatTimeout:  NodeJS.Timeout | null;
  private treatStatus: string;
  private m200Api: M200Api;
  private btn_ing: boolean;
  private computed: boolean;
  private thresholdExceeded: boolean;
  private planEndInterval:  NodeJS.Timeout | null;

  constructor(props: Props) {
    super(props);
    this.state = {
      treatStatus: 'treat_stop',
      treatInfo: {
        count: 0,
        time: 0,
      },
      backOpen: false,
      backTime: 3,
      strengthRange: {
        max: 0,
        min: 0,
      },
      identifyError: true,
      relativeStrength: 0,
      visiblePart: initVisibleInfo,
      instructionPart: initInstructionInfo,
    };
    this.treatInterval = null;
    this.backInterval = null;
    this.listenTreatDataInterval = null;
    this.stopTreatTimeout = null;
    this.planEndInterval = null;
    this.treatStatus = 'treat_stop';
    this.m200Api = getM200ApiInstance();
    connSocket.tid = props.tid;
    this.btn_ing = false;
    this.computed = false;
    this.thresholdExceeded = true;
    this.throttleSetTmsPlayData = throttle(this.throttleSetTmsPlayData.bind(this), throttleOption.wait);
  }

  async componentDidMount() {
    isUnmount = false;
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    this.listeningBeat();
    connSocket.listenStatus('TreatInfo_cameraError', this.getCameraErrorsCallback);
    this.getMinAndMaxStrength();
    await this.getTreatConfig();
  }

  componentWillUnmount(): void {
    isUnmount = true;
    clearInterval(this.planEndInterval as any);
    clearInterval(this.treatInterval as any);
    clearInterval(this.listenTreatDataInterval as any);
    window.tmsAPI.remove_beat_btn_by_key('TreatInfo');
    connSocket.clearListenStatusByKey('TreatInfo_cameraError');
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    window.systemAPI.pushSystemFault({ '0A030005': FaultStatusEnum.normal}, 'treatInfo page 卸载');
    this.props.setTreatStatus('treat_stop');
  }

  componentDidUpdate(prevProps: Readonly<Props>, prevState: Readonly<State>, snapshot?: any): void {
    if (prevProps.fault !== this.props.fault) {
      if (this.hasError()) {
        this.listenTmsError();
      }
    }
    if (!isEqual(prevProps.stimulate?.active_strength, this.props.stimulate?.active_strength)) {
      this.setState({ relativeStrength: this.getRelativeStrength() });
    }
    if (!isEqual(prevState.treatStatus, this.state.treatStatus)) {
      this.props.setTreatStatus(this.state.treatStatus);
    }
  }

  private hasError = () => {
    const { helper, fault } = this.props;

    return helper ? !!fault[FaultLevelEnum.error].length : !!getFaultWithoutType(FaultEnum.imageFault).length;
  };

  private listenTreatData = () => {
    const { horizontalDisable, helper } = this.props;
    this.listenTreatDataInterval = setInterval(() => {
      if (!helper) {
        clearInterval(this.listenTreatDataInterval as any);

        return;
      }
      const { visiblePart } = this.state;
      const calInfo = visiblePart.calInfo;
      if (!isNumber(batInfoByMatrix.batAngleCalc.angle) || !isNumber(batInfoByMatrix.batTranslationCalc.distance) || !isNumber(batInfoByMatrix.minDistanceCalc)) return;
      const angle = Math.ceil(batInfoByMatrix.batAngleCalc.angle); // faxian jiajiao
      const translate = Math.ceil(batInfoByMatrix.batTranslationCalc.distance);// pingyi
      const absolute = Math.ceil(batInfoByMatrix.minDistanceCalc); // badian juli
      const conditionals = [calInfo.absolute < absolute, calInfo.translate < translate, calInfo.angle < angle];
      if (!horizontalDisable) {
        if (!isNumber(batInfoByMatrix.horizontalCalc)) {
          conditionals.push(false);
        } else {
          const rotate = Math.ceil(batInfoByMatrix.horizontalCalc); // shuipingxuanzhuanjiao
          conditionals.push(calInfo.rotate < rotate);
        }
      }
      if (conditionals.some(conditional => conditional)) {
        this.thresholdExceeded = true;
        if (!this.stopTreatTimeout && this.treatStatus === 'treat_ing') {
          this.stopTreatTime(visiblePart.timeSlot);
        }
      } else {
        this.thresholdExceeded = false;
        clearTimeout(this.stopTreatTimeout as any);
        this.stopTreatTimeout = null;
      }
    }, 500);
  };

  private stopTreatTime = (time: number) => {
    this.stopTreatTimeout = setTimeout(async () => {
      if (this.treatStatus === 'treat_ing') {
        this.btn_ing = true;
        await this.treatment_plan_pause();
        this.btn_ing = false;
        await this.pauseSendTreatLog();
        clearTimeout(this.stopTreatTimeout as any);
        this.stopTreatTimeout = null;
      } else {
        clearTimeout(this.stopTreatTimeout as any);
        this.stopTreatTimeout = null;
      }
    }, time * 1000);
  };

  private getTreatConfig = async () => {
    const [techSupportInfo,visiblePart, instructionPart] = await this.m200Api.getControlConfig();
    this.setState({
      visiblePart: techSupportInfo.mainControl ? visiblePart : initVisibleInfo,
      instructionPart: techSupportInfo.mainControl ? instructionPart : initInstructionInfo,
    });
    if (techSupportInfo.mainControl) {
      this.listenTreatData();
    } else {
      this.thresholdExceeded = false;
    }
  };

  // 识别不到人脸/拍子 暂停刺激
  private getCameraErrorsCallback = async (status: GetStatusCallbackParam) => {
    const { tid, helper } = this.props;
    if (!status.cameraErrors) return;
    const facepointcloud = status.cameraErrors.facepointcloud; // 点云匹配
    const treatboard = status.cameraErrors.treatboard;
    const facedetection = status.cameraErrors.facedetection;
    if (facepointcloud !== 1 || treatboard !== 1 || facedetection !== 1) {
      if (!helper) return;
      this.setState({ identifyError: true });
      if (['treat_ing'].includes(this.treatStatus)) {
        sendTreatLog.info('刺激中识别不到人脸/拍子',`facepointcloud: ${facepointcloud},treatboard:${treatboard},facedetection:${facedetection}`,`目标识别辅助：${helper}`);
        if (this.btn_ing) return;
        this.btn_ing = true;
        this.treatStatus = 'treat_pause';
        await window.tmsAPI.image_treatment_plan_start('PlanPause', tid).finally(() => {
          if (this.treatStatus !== 'treat_stop') {
            // eslint-disable-next-line @typescript-eslint/no-floating-promises
            this.pauseSendTreatLog();
            this.btn_ing = false;
          }
          this.setState({ treatStatus: 'treat_pause' });
          this.treatStatus = 'treat_pause';
        });
      }
    } else {
      this.setState({ identifyError: false });
    }
  };

  private listenTmsError = () => {
    const { tid, helper } = this.props;
    clearInterval(this.treatInterval as any);
    if (this.treatStatus !== 'treat_stop') {
      // eslint-disable-next-line @typescript-eslint/no-floating-promises
      window.tmsAPI.image_treatment_plan_start('PlanEnd', tid);
      // eslint-disable-next-line @typescript-eslint/no-floating-promises
      // this.m200Api.endTreat({ uuid: this.uuid, type: 1 });
      this.props.fetchPlanEnd(ReportEndEnnm.notComplete);
    }
    this.treatStatus = 'treat_stop';
    this.setState({ treatStatus: 'treat_stop' });
    sendTreatLog.info('影像刺激异常结束', `目标识别辅助：${helper}`);
    this.btn_ing = false;
  };

  private logErrorEndTreat = async (err: any) => {
    const { tid } = this.props;
    sendTreatLog.info('影像刺激错误', `错误信息：${JSON.stringify(err)}`);
    if ([30223, 30224, 30225, 30228, 30229].includes(err.code)) return;
    clearInterval(this.planEndInterval as any);
    this.treatStatus = 'treat_stop';
    this.setState({ treatStatus: 'treat_stop' });
    let count = 0;
    // #M200-1640 这里为了解决start和end间隔时间太短，下位机start还未处理完end就发送过去导致end指令不生效
    this.planEndInterval = setInterval(() => {
      if (count >= 3) {
        clearInterval(this.planEndInterval as any);

        return;
      }
      // eslint-disable-next-line @typescript-eslint/no-floating-promises
      window.tmsAPI.image_treatment_plan_start('PlanEnd', tid);
      count++;
    }, 100);
    this.props.setTreatLogsError();
  };

  private beginSendTreatLog = async (): Promise<void> => {
    const { stimulate, treatParam, motion_threshold, activeSpot, tid, helper } = this.props;
    if (!treatParam || !stimulate?.relative_strength) return;
    const param = {
      navigate_support_type: helper ? NavigateSupportTypeEnum.ENABLE : NavigateSupportTypeEnum.DISABLE,
      uuid: tid,
      plan_id: stimulate?.plan_id,
      subject_id: stimulate?.subject_id,
      plan_stimulus_id: stimulate?.id,
      planned_stimulation_duration: treatParam.time,
      pulse_total: treatParam.sum,
      motion_threshold: motion_threshold,
      plan_target_id: activeSpot.id,
      target_data: {
        target_name: activeSpot.name,
        code: activeSpot.code,
        vol_ras: activeSpot.vol_ras,
      },
      stimulus_data: {
        type: stimulate?.type,
        relative_strength: stimulate?.relative_strength,
        actual_strength: Math.min(Math.round((stimulate?.relative_strength * motion_threshold) / 100), 100),
        strand_pulse_frequency: stimulate?.strand_pulse_frequency,
        inner_strand_pulse_count: stimulate?.inner_strand_pulse_count,
        plexus_inner_frequency: stimulate?.plexus_inner_frequency,
        plexus_inter_frequency: stimulate?.plexus_inter_frequency,
        plexus_inner_pulse_count: stimulate?.plexus_inner_pulse_count,
        plexus_count: stimulate?.plexus_count,
        strand_pulse_count: stimulate?.strand_pulse_count,
        intermission_time: stimulate?.intermission_time,
        pulse_total: treatParam.sum,
      },
    };
    try {
      await this.m200Api.beginTreat(param);
      sendTreatLog.info('影像刺激开始', `刺激参数：${JSON.stringify(param)}`,`目标识别辅助：${helper}`);
    } catch (err) {
      await this.logErrorEndTreat(err);
    }

    return Promise.resolve();
  };

  private pauseSendTreatLog = async (): Promise<void> => {
    const { tid, helper } = this.props;
    try {
      await this.m200Api.pauseTreat({ uuid: tid });
      sendTreatLog.info('影像刺激暂停',`目标识别辅助：${helper}`);
    } catch (err) {
      await this.logErrorEndTreat(err);
    }

    return Promise.resolve();
  };

  private resumeSendTreatLog = async (): Promise<void> => {
    const { stimulate, tid, helper } = this.props;
    try {
      await this.m200Api.continueTreat({ uuid: tid, actual_strength: stimulate?.active_strength });
      sendTreatLog.info('影像刺激继续',`目标识别辅助：${helper}`);
    } catch (err) {
      await this.logErrorEndTreat(err);
    }

    return Promise.resolve();
  };

  private endSendTreatLog = () => {
    const { helper } = this.props;
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    // this.m200Api.endTreat({ uuid: this.uuid, type: 2 });
    this.props.fetchPlanEnd(ReportEndEnnm.complete);
    sendTreatLog.info('影像刺激自动结束',`目标识别辅助：${helper}`);
  };

  private setTmsScreen = (type: number) => {
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    window.tmsAPI.set_beat_screen(type);
  };

  private treatment_plan_resume = async () => {
    const { tid } = this.props;
    const data = await window.tmsAPI.image_treatment_plan_start('PlanResume', tid);
    if (data.code === 0) {
      if (data.data.result === 0) {
        this.setState({ treatStatus: 'treat_ing' });
        this.treatStatus = 'treat_ing';

        return true;
      } else {
        sendTreatLog.info('影像继续刺激出现故障', treatResultMap[data.data.result]);
        await this.messageWarning();

        return false;
      }
    } else {
      await message.error(data.message || 'tms通讯出错');

      return false;
    }
  };

  private messageWarning = async () => {
    return message.warning({
      content: '系统异常，请重试',
      key: 'result',
    });
  };

  private forbidTreat = (data: any) => data.data.key !== 'play';

  private resumeTreat = (data: any) => this.treatStatus === 'treat_pause' && data.data.key === 'play' && this.props.fault[FaultLevelEnum.error].length === 0;

  private pauseTreat = (data: any) => this.treatStatus === 'treat_ing' && data.data.key === 'play';

  private handleTmsPlayData = async (data: any) => {
    const { helper } = this.props;
    const { identifyError } = this.state;
    let forbidBtn = this.computed || this.hasError() || this.btn_ing;
    if (helper) {
      forbidBtn = forbidBtn || identifyError;
    }
    if (forbidBtn) return;
    if (this.treatStatus === 'treat_stop') {
      if (this.forbidTreat(data)) return;
      if (helper && this.thresholdExceeded) return;
      this.btn_ing = true;
      if (await this.treatment_plan_start()) {
        this.setTmsScreen(TMSScreenState.PlanTreat);
        await this.beginSendTreatLog();
      }
      this.btn_ing = false;

      return;
    }
    if (this.pauseTreat(data)) {
      this.btn_ing = true;
      if (await this.treatment_plan_pause()) {
        this.setTmsScreen(TMSScreenState.PlanSuspend);
        await this.pauseSendTreatLog();
      }
      this.btn_ing = false;

      return;
    }
    if (this.resumeTreat(data)) {
      if (helper && this.thresholdExceeded) return;
      const { stimulate } = this.props;
      this.btn_ing = true;
      const coil_max_temperature = this.props.coilInfo.coil_max_temperature;
      if (typeof coil_max_temperature !== 'number' || !stimulate) return;
      if (coil_max_temperature > 36) {
        this.btn_ing = false;

        return message.warning({
          content: '存在超温风险，请降温后再试',
          key: 'over_temp',
        });
      }
      if (await this.treatment_plan_resume()) {
        this.setTmsScreen(TMSScreenState.PlanTreat);
        await this.resumeSendTreatLog();
      }
      this.btn_ing = false;
    }

    return;
  };

  /**
   * 添加拍子节流
   */
  private throttleSetTmsPlayData = (data: any) => {
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    this.handleTmsPlayData(data);
  };

  // eslint-disable-next-line @typescript-eslint/typedef
  private beatBtnCallback = async (event: any, data: any): Promise<any> => { // NOSONAR
    this.throttleSetTmsPlayData(data);
  };

  private listeningBeat = async () => {
    await window.tmsAPI.beat_btn_by_key('TreatInfo', this.beatBtnCallback);
  };

  private startBackInterval = () => {
    this.computed = true;
    let i = 3;
    this.setState({ backOpen: true });
    this.backInterval = setInterval(() => {
      if (i === 0) {
        clearInterval(this.backInterval as any);
        this.props.treatCompleteCallback();
      }
      this.setState({ backTime: i });
      i--;
    }, 1000);
  };

  private treatment_plan_pause = async (): Promise<any> => {
    const { tid } = this.props;
    const data = await window.tmsAPI.image_treatment_plan_start('PlanPause', tid);
    if (data.code === 0) {
      if (data.data.result === 0) {
        this.setState({ treatStatus: 'treat_pause' });
        this.treatStatus = 'treat_pause';

        return true;
      } else {
        sendTreatLog.info('影像暂停出现故障', treatResultMap[data.data.result]);
        await this.messageWarning();

        return false;
      }
    } else {
      await message.error(data.message || 'tms通讯出错');

      return false;
    }
  };

  private treatment_plan_start = async () => { // NOSONAR
    const { pulse_total, tid } = this.props;
    const data = await window.tmsAPI.image_treatment_plan_start('PlanStart', tid);
    if (data.code === 0) {
      if (data?.data.result === 0) {
        this.setState({ treatStatus: 'treat_ing' });
        this.treatStatus = 'treat_ing';
        this.treatInterval = setInterval(async () => {
          if (isUnmount) {
            clearInterval(this.treatInterval as any);

            return;
          }
          const queryData = await window.tmsAPI.query_treatment();
          this.setState({ treatInfo: { time: queryData.data.time, count: queryData.data.count, remain_time: queryData.data.remain_time } });
          if (queryData.data.count < pulse_total && queryData.data.state === 0 && this.treatStatus !== 'treat_stop') {
            this.treatStatus = 'treat_stop';
            clearInterval(this.treatInterval as any);
            // eslint-disable-next-line @typescript-eslint/no-floating-promises
            // this.m200Api.endTreat({ uuid: this.uuid, type: 1 });
            this.props.fetchPlanEnd(ReportEndEnnm.notComplete);
            sendTreatLog.info('影像刺激异常结束-状态不一致');
            // eslint-disable-next-line @typescript-eslint/no-floating-promises
            window.systemAPI.pushSystemFault({ '0A030005': FaultStatusEnum.abnormal}, 'treatInfo page 上下微机状态不一致');
          }
          // 刺激结束
          if (queryData.data.result === 0 && queryData.data.state === 0 && queryData.data.count === pulse_total) {
            // 自动结束
            this.treatStatus = 'treat_stop';
            this.endSendTreatLog();
            this.startBackInterval();
            clearInterval(this.treatInterval as any);
          }
        }, 1000);

        return true;
      } else {
        sendTreatLog.info('影像刺激开始出现故障', treatResultMap[data.data.result]);
        await this.messageWarning();

        return false;
      }
    } else {
      await message.error(data.message || 'tms通讯出错');

      return false;
    }
  };

  private treatComplete = () => {
    clearInterval(this.treatInterval as any);
    clearInterval(this.backInterval as any);
    this.props.treatCompleteCallback();
  };

  private getRelativeStrength = () => {
    const { motion_threshold, stimulate } = this.props;

    return Math.max(Math.min(Math.round((stimulate?.active_strength || 0) / motion_threshold * 100), 150), 0);
  };

  private powerOverStrength = () => {
    const { stimulate, motion_threshold } = this.props;
    const cloneStimulate = cloneDeep(stimulate);
    let level = cloneStimulate?.active_strength;
    if (!cloneStimulate || !level || !motion_threshold) return;
    let run = true;
    let added_level = 0;
    while (run) {
      added_level = Math.ceil((cloneStimulate.relative_strength * motion_threshold) / 100);
      run = isPowerOver(cloneStimulate, added_level);
      cloneStimulate.relative_strength = cloneStimulate.relative_strength + 1;
    }
    sendRenderLog.info(`功率超限值：${added_level - 1}`);

    return added_level - 1;
  };

  private getMinAndMaxStrength = () => {
    const { stimulate, motion_threshold } = this.props;
    if (!stimulate?.active_strength || !motion_threshold) return;
    const powerOverValue = this.powerOverStrength();
    const { min: minRange, max: maxRange } = calRelativeStrengthRangeByActive(stimulate.active_strength, powerOverValue || 150);
    const maxByActivity = Math.ceil(150 * motion_threshold / 100);

    this.setState({
      strengthRange: {
        max: Math.min(maxByActivity,maxRange),
        min: minRange,
      },
    });
  };

  private getRemainTime = (): number => {
    const { treat_time } = this.props;
    const { treatInfo } = this.state;
    const remain_time = typeof treatInfo.remain_time !== 'undefined' ? treatInfo.remain_time : treat_time - treatInfo.time;

    return remain_time || 0;
  };

  render(): React.ReactNode {
    const {
      pulse_total,
      motion_threshold,
      stimulate,
    } = this.props;
    const { treatStatus, treatInfo, backOpen, strengthRange, relativeStrength } = this.state;

    return (
      <>
        <div className={classNames(styles.treat_info)}>
          <div className="process">
            {treatStatus === 'treat_pause' && (
              <div className="treat_pause">
                <NgIcon isPreview style={{ marginRight: '9px' }} fontSize={16} iconSvg={Warning} />
                脉冲暂停
              </div>
            )}
            {treatStatus !== 'treat_pause' && <div className="treat_pause" />}
            <TreatCircleProgress percent={Math.round((treatInfo.count / pulse_total) * 100)} strokeWidth={20} size={180} isTreating={treatStatus === 'treat_ing'} />
            <Countdown status={treatStatus} time={secToTime(this.getRemainTime())} />
            <Pluse status={treatStatus} total={pulse_total} count={treatInfo.count} />
          </div>
        </div>
        <div className={styles.strength}>
          {['treat_stop', 'treat_ing'].includes(treatStatus) && (
            <div data-status={treatStatus} className="strength_treating">
              <div>相对强度（%MT）：{relativeStrength || stimulate?.relative_strength}</div>
              <div>运动阈值（%MO）：{motion_threshold}</div>
              <div>实际强度（%MO）：{stimulate?.active_strength}</div>
            </div>
          )}
          {treatStatus === 'treat_pause' && (
            <div data-status={treatStatus} className="strength_treat_stop">
              <div className="relative_strength">
                相对强度（%MT）：
                <span className="value">{relativeStrength || stimulate?.relative_strength}</span>
              </div>
              <div className="threshold">
                运动阈值（%MO）： <span className="value">{motion_threshold}</span>
              </div>
              <div className="activity_strength">
                <span>实际强度（%MO）：</span>
                <div className="strength_progress">
                  <NgStrengthProgress min_value={strengthRange.min} max_value={strengthRange.max} percent={stimulate?.active_strength || 0} radius={60} />
                </div>
              </div>
            </div>
          )}
        </div>
        <NgModal
          open={backOpen}
          footer={<React.Fragment />}
          closable={false}
          closeIcon={<React.Fragment />}
          className="back_modal"
          width={400}
          centered
          getContainer={() => document.querySelector('.treat_info') || document.body}
        >
          <NgIcon fontSize={34} iconSvg={SuccessMessage} />
          <div className="treat_complete_text">本次治疗完成</div>
          <div className="auto_hide_text">{this.state.backTime}秒后自动消失</div>
          <NgButton onClick={this.treatComplete} className="back_button">
            返回
          </NgButton>
        </NgModal>
      </>
    );
  }
}

const Wrap = React.forwardRef((props: any, ref) => {
  const coilInfo = useRecoilValue(tmsCoilAtom);
  const setTreatStatus = useSetRecoilState(treatStatusAtom);
  const [fault] = useRecoilState(faultAtom);

  return <TreatInfo {...props} fault={fault} ref={ref} coilInfo={coilInfo} setTreatStatus={setTreatStatus} />;
});

export default Wrap;
