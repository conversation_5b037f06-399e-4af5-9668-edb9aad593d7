import React from 'react';
import { useMount, useUpdateEffect } from 'ahooks';
import Form from 'antd/es/form';
import { EditStimulateTemplate } from '../../../../component/template';
import { NgForm } from '../../../../uiComponent/NgForm';
import { StimulateModel } from '../../../noPatientTms';

type Props = {
  stimulate: StimulateModel;
  isNotComplete: boolean;
  motionThreshold?: number;
  onValuesChange(values: any): void;
};
export const StimulateTemplate = React.forwardRef((props: Props, ref) => {
  const [form] = Form.useForm();
  const { motionThreshold = 0 } = props;

  React.useImperativeHandle(ref, () => {
    return {
      form,
    };
  });

  useMount(() => {
    const stimulate = props.stimulate;
    if (stimulate.strand_pulse_count === 1) {
      stimulate.intermission_time = undefined;
    }
    form.setFieldsValue(stimulate);
  });

  useUpdateEffect(() => {
    const stimulate = props.stimulate;
    if (stimulate.strand_pulse_count === 1) {
      stimulate.intermission_time = undefined;
    }
    form.setFieldsValue(stimulate);
  }, [props.stimulate]);

  return (
    <NgForm
      form={form}
      layout="horizontal"
      labelAlign={'left'}
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 16 }}
      onValuesChange={props.onValuesChange}
    >
      {props.stimulate && <EditStimulateTemplate motionThreshold={motionThreshold} stimulate={props.stimulate} formRef={form} />}
    </NgForm>
  );
});
