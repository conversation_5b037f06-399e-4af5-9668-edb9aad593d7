@import '../../../../static/style/baseColor.module.less';

.immediate_view {
  position: relative;
  width: 410px;
  display: flex;
  flex-direction: column;
  border-top-right-radius: 8px;
  border-top-left-radius: 8px;
  overflow: hidden;
  user-select: none;
  :global {
    .person_border {
      width: 120px;
      height: 114px;
      position: absolute;
      top: 101px;
      left: 50%;
      transform: translateX(-50%);
    }
    .view {
      width: 410px;
      height: 306px;
      position: absolute;
      top: 0;
    }
    .camera_error {
      position: absolute;
      top: 0;
      width: 100%;
      height: 230px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: @colorA4_1;
      color: @colorC2;
      .dot_ani {
        display: inline-block;
        height: 12px;
        line-height: 12px;
        overflow: hidden;
      }
      .dot_ani::after {
        display: inline-table;
        white-space: pre;
        content: '\A.\A..\A...';
        animation: spin 2s steps(4) infinite;
      }
    }
    .msg {
      width: 269px;
      height: 48px;
      background-color: @colorA5;
      border-radius: 6px;
      margin: 12px auto;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      svg {
        margin-right: 9px;
      }
    }
    .plc {
      width: 269px;
      height: 48px;
      margin: 12px auto;
    }
    .bottom_bar {
      width: 100%;
      height: 97px;
      padding: 0 15px;
      display: flex;
      justify-content: space-between;
      background-color: @colorA3;
      border-bottom-left-radius: 8px;
      border-bottom-right-radius: 8px;
      overflow: hidden;
      pointer-events: none;
      .anticon {
        cursor: initial !important;
        margin-top: 44px;
      }
      .distance {
        .person,
        .camera {
          & > img {
            margin-left: 16px;
          }
        }
        .person {
          margin-top: 8px;
        }
        .three_distance {
          font-size: 12px;
          color: @colorA9;
          .cm_65 {
            margin-left: 16px;
          }
          .cm_90 {
            margin-left: 139px;
          }
          .cm_110 {
            margin-left: 102px;
          }
        }
      }
    }
    .treat_card{
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      .single_card{
        width: 200px;
        height: 102px;
        padding: 16px;
        margin-top: 12px;
        background-color: @colorA3;
        border-radius: 8px;
        .title {
          display: flex;
          align-items: center;
          .bar {
            width: 6px;
            height: 16px;
            border-radius: 10px;
            margin-right: 10px;
          }
        }
        .value {
          margin-top: 2px;
          font-size: 40px;
          font-weight: 500;
        }
      }
      .single_card[data-visible = false]{
        background-color: @colorA1;
      }
    }
  }
}
