import React, { use<PERSON>allback, useMemo, useRef, useState } from 'react';
import { createPortal } from 'react-dom';
import { isNumber } from 'lodash';
import { useRecoilValue } from 'recoil';
import { useMount, useUnmount } from 'ahooks';
import { GetStatusCallbackParam, connSocket, imgSocket, personBatDistanceSocket } from '../../../../utils/imgSocket';
import { NgIcon } from '../../../../uiComponent/NgIcon';
import { Camera } from '../../../../uiComponent/SvgGather';
import personProgress from '../../../../static/images/personProgress.png';
import personImg from '../../../../static/images/person.png';
import { treatStatusAtom } from '../../../../recoil/treatStatus';
import personBorder from '../../../../static/images/personBorder.png';
import { ReactComponent as Warning } from '@/renderer/static/svg/warning.svg';
import styles from './index.module.less';
import useAnimationFrameWithFps from '../../../../hooks/useAnimationFrameWithFps';
import { batInfoByMatrix } from '../../../../utils/treat';
import { InstructionInfo, VisibleInfoType } from '../../../manage/components/manageDevice/config';
import { isIdentityMatrix } from '../../../../utils/mathUtil';
import './animation.less';

type Props = {
  coilPanelMissingCallback?(): void;
  children?: any;
  horizontalDisable: boolean;
  horizontal: number;
  visiblePart: VisibleInfoType;
  instructionPart?: InstructionInfo;
  helper: boolean;
  isBatVerifiCation?: boolean;
  mainControl: boolean;
  isEmg: boolean;
};

type State = {
  renderImage: boolean;
  personFar: boolean;
  personNear: boolean;
  batFar: boolean;
  batNear: boolean;
  faceError: boolean;
  cameraStatus: boolean;
  identifyError: boolean;
  treatboardError: boolean;
  thresholdExceeded: boolean;
};

type CountValueProps = {
  cameraStatus: boolean;
  identifyError: boolean;
  horizontalDisable: boolean;
  horizontal: number;
  visiblePart: VisibleInfoType;
  instructionPart?: InstructionInfo;
  helper: boolean;
  isBatVerifiCation?: boolean;
  mainControl: boolean;
};
type TreatCardState = {
  angle: undefined | number;
  translate: undefined | number;
  rotate: undefined | number;
  absolute: undefined | number;
};

const TreatCard: React.FC<CountValueProps> = (props: CountValueProps) => {
  const [state, setState] = useState<TreatCardState>({
    angle: undefined,
    translate: undefined,
    rotate: undefined,
    absolute: undefined,
  });
  const isIdentityMatrixWithNull = useRef(true);
  const treatStatus = useRecoilValue(treatStatusAtom);

  useMount(() => {
    personBatDistanceSocket.listenMessage('TreatCard', personBatSocketListener);
  });

  useUnmount(() => {
    personBatDistanceSocket.clearListenMessageByKey('TreatCard');
    batInfoByMatrix.clearData();
  });

  const personBatSocketListener = useCallback((value: any) => {
    if (!isIdentityMatrix(value.coil) && !isIdentityMatrix(value.head)) {
      isIdentityMatrixWithNull.current = false;
    }
  },[]);

  const dataConfig = useMemo(() => {
    const config = props.isBatVerifiCation
      ? [
          {
            title: '靶点距离（mm）：',
            key: 'absolute',
          },
          {
            title: '夹角误差：',
            key: 'angle',
            color: '#459BA8',
          },
        ]
      : [
          {
            title: '靶点距离（mm）：',
            key: 'absolute',
          },
          {
            title: '平移距离（mm）：',
            key: 'translate',
            color: '#7E50CD',
          },
          {
            title: '夹角误差：',
            key: 'angle',
            color: '#459BA8',
          },
          {
            title: `旋转角度${props.horizontalDisable ? '' : '误差'}：`,
            key: 'rotate',
            color: '#3662EC',
          },
        ];

    return config;
  }, [props.horizontalDisable, props.isBatVerifiCation]);

  const getColors = (value: string, key: string): string => {
    const { cameraStatus, identifyError, horizontalDisable, mainControl } = props;
    if (!cameraStatus || identifyError || value === '--') {
      return '#FFFFFF';
    }
    if (!props.helper && treatStatus === 'treat_pause') return '#B8B8BD';
    if (!props.helper) return '#FFFFFF';
    if (key === 'rotate' && horizontalDisable) return '#B8B8BD';
    if (key === 'absolute') {
      if (+value > props.visiblePart.calInfo.absolute) {
        return '#BA6058';
      } else {
        return '#8CAB39';
      }
    }
    if (mainControl) {
      let num = 0;
      if (key === 'translate') {
        num = props.visiblePart.calInfo.translate;
      }
      if (key === 'angle') {
        num = props.visiblePart.calInfo.angle;
      }
      if (key === 'rotate') {
        num = props.visiblePart.calInfo.rotate;
      }
      if (+value < num || +value === num) return '#8CAB39';
      if (+value > num) return '#BA6058';
    } else {
      if (+value < 3 || +value === 3) return '#8CAB39';
      if (+value <= 5 && +value > 3) return '#ECC141';
      if (+value > 5) return '#BA6058';
    }

    return '';
  };

  const getTreatDistance = (item: any) => {
    const { cameraStatus, identifyError } = props;
    if (!cameraStatus || identifyError || isIdentityMatrixWithNull.current) return '--';

    return isNumber(state[item.key]) ? state[item.key] : '--';
  };

  useAnimationFrameWithFps(() => {
    const newState = {
      angle: isNumber(batInfoByMatrix.batAngleCalc.angle) ? Math.ceil(batInfoByMatrix.batAngleCalc.angle) : undefined,
      translate: isNumber(batInfoByMatrix.batTranslationCalc.distance) ? Math.ceil(batInfoByMatrix.batTranslationCalc.distance) : undefined,
      rotate: isNumber(batInfoByMatrix.horizontalCalc) ? Math.ceil(batInfoByMatrix.horizontalCalc) : undefined,
      absolute: isNumber(batInfoByMatrix.minDistanceCalc) ? Math.ceil(batInfoByMatrix.minDistanceCalc) : undefined,
    };
    setState(newState);
  }, 15);

  return (
    <div className="treat_card">
      {dataConfig.map(config => {
        const visible = props.isBatVerifiCation ? true : props.instructionPart?.instruction[config.key];
        const treatValue = getTreatDistance(config);

        return (
          <div className="single_card" key={config.key} data-visible={visible}>
            {visible && (
              <>
                <div className="title">
                  {config.color && <div style={{ background: config.color }} className="bar" />}
                  <span>{config.title}</span>
                </div>
                <div className="value" style={{ color: getColors(treatValue, config.key) }}>
                  {treatValue}
                </div>
              </>
            )}
          </div>
        );
      })}
    </div>
  );
};

export class ImmediateView extends React.PureComponent<Props, State> {
  private imgView: any;
  private personImage: any;
  private batImage: any;
  private facekeypointsError: boolean;
  private facedetectionError: boolean;
  private treatboardError: boolean;
  private personImageEle: any;
  // private batImageEle: any;
  constructor(props: Props) {
    super(props);
    this.state = {
      renderImage: false,
      personFar: false,
      personNear: false,
      batFar: false,
      batNear: false,
      faceError: false,
      cameraStatus: false,
      identifyError: false,
      treatboardError: false,
      thresholdExceeded: false,
    };
    this.imgView = React.createRef();
    this.personImage = React.createRef();
    this.batImage = React.createRef();
    this.facekeypointsError = false;
    this.facedetectionError = false;
    this.treatboardError = false;
    this.personImageEle = new Image();
    // this.batImageEle = new Image();
    this.personImageEle.src = personImg;
    // this.batImageEle.src = batArrowImg;
  }

  public componentDidMount(): void {
    connSocket.getImageCallback = this.imgViewSocket;
    connSocket.coilPanelMissingCallBack = this.props.coilPanelMissingCallback;
    connSocket.setState();
    // this.imgViewSocket();
    this.personBat();
    connSocket.listenStatus('ImmediateView_cameraError', this.getCameraErrorsCallback);
  }

  public componentWillUnmount(): void {
    connSocket.clearStateInterval();
    imgSocket.clearInterval();
    imgSocket.stopReceiveImgMessage();
    personBatDistanceSocket.stopReceivePersonBatMessage();
    personBatDistanceSocket.clearInterval();
    personBatDistanceSocket.clearListenMessageByKey('ImmediateView');
    connSocket.clearListenStatusByKey('ImmediateView_cameraError');
    this.clearCanvas();
  }

  private clearCanvas = () => {
    [this.personImage.current, this.batImage.current].forEach(canvas => {
      if (!canvas) return;
      canvas.width = canvas.height = 0;
      const ctx = canvas.getContext('2d');
      ctx?.fillRect?.(0, 0, 0, 0);
    });
  };

  private getCameraErrorsCallback = (status: GetStatusCallbackParam) => {
    if (!status.cameraErrors) return;
    this.facekeypointsError = status.cameraErrors.facepointcloud !== 1; // 点云匹配
    this.facedetectionError = status.cameraErrors.facedetection !== 1;
    this.treatboardError = status.cameraErrors.treatboard !== 1;
    this.setState({
      faceError: this.facekeypointsError || this.facedetectionError,
      cameraStatus: status.cameraStatus,
      treatboardError: this.treatboardError,
    });
    if (this.facekeypointsError || this.facedetectionError || this.treatboardError) {
      this.setState({ identifyError: true, thresholdExceeded: false });
    } else {
      this.setState({ identifyError: false });
      this.isThresholdExceeded();
    }
  };

  private isThresholdExceeded = () => {
    const { helper, mainControl, isEmg, visiblePart, horizontalDisable } = this.props;
    if (!helper || !mainControl || isEmg) return;
    const calInfo = visiblePart.calInfo;
    if (!isNumber(batInfoByMatrix.batAngleCalc.angle) || !isNumber(batInfoByMatrix.batTranslationCalc.distance) || !isNumber(batInfoByMatrix.minDistanceCalc)) return;
    const angle = Math.ceil(batInfoByMatrix.batAngleCalc.angle); // faxian jiajiao
    const translate = Math.ceil(batInfoByMatrix.batTranslationCalc.distance);// pingyi
    const absolute = Math.ceil(batInfoByMatrix.minDistanceCalc); // badian juli
    const conditionals = [calInfo.absolute < absolute, calInfo.translate < translate, calInfo.angle < angle];
    if (!horizontalDisable) {
      if (!isNumber(batInfoByMatrix.horizontalCalc)) {
        conditionals.push(false);
      } else {
        const rotate = Math.ceil(batInfoByMatrix.horizontalCalc);
        conditionals.push(calInfo.rotate < rotate);
      }
    }
    if (conditionals.some(conditional => conditional)) {
      this.setState({ thresholdExceeded: true });
    } else {
      this.setState({ thresholdExceeded: false });
    }
  };

  private personBat = () => {
    personBatDistanceSocket.startReceive = true;
    personBatDistanceSocket.listenMessage('ImmediateView', this.personBatSocket);
    personBatDistanceSocket.sendPersonBatMessage();
  };

  private renderPersonBatImg = (personX: number, nativeX: number) => {
    if (nativeX === 0 && !this.facekeypointsError && !this.facedetectionError) return;
    const personContext = this.personImage.current?.getContext('2d');
    // const batContext = this.batImage.current?.getContext('2d');
    personContext?.clearRect(0, 0, 356, 37);
    if (personX >= 4 && personX <= 344.2 && !this.facekeypointsError && !this.facedetectionError) {
      personContext?.drawImage(this.personImageEle, personX, 0);
    }
  };

  private imgViewSocket = () => {
    const context: CanvasRenderingContext2D = this.imgView.current?.getContext('2d');
    // M200-739 如果进来canvas没有渲染 重新render一下
    if (!context) {
      this.forceUpdate();

      return;
    }
    imgSocket.startReceiveImg = true;
    imgSocket.sendImageMessage();
    const img = new Image();
    // const context = this.imgView.current?.getContext('bitmaprenderer');
    imgSocket.onmessage = async (data: any) => {
      if (data.b64 && context) {
        if (!this.state.renderImage) {
          this.setState({ renderImage: true });
        }
        img.src = data.b64;
        img.onload = () => {
          context.clearRect(0, 0, 410, 230);
          context.drawImage(img, 0, 0, 410, 230);
        };
      }
    };
  };

  private judgePersonDistance = (distance: number) => {
    if (distance < 65) {
      this.setState({ personFar: false, personNear: true });
    } else if (distance > 90) {
      this.setState({ personFar: true, personNear: false });
    } else {
      this.setState({ personFar: false, personNear: false });
    }
  };

  private personBatSocket = (value: any) => {
    const distanceHead = (value.distancehead_display || 0) / 10;
    // const distanceBoard = (value.distancecaboard || 0) / 10;
    this.judgePersonDistance(distanceHead);
    const headValue = Math.min(358, (distanceHead - 65) * 7.56) + 4;
    // const boardValue = Math.max(20, Math.min(358, Math.round((distanceBoard - 20) * 3.09)) + 4);
    this.renderPersonBatImg(headValue, distanceHead);
  };

  private getMsgText = (text: string) => (
    <div className="msg">
      <Warning />
      {text}
    </div>
  );

  private getMsg = () => {
    const { personFar, faceError, renderImage, personNear, thresholdExceeded } = this.state;
    if (!renderImage) return <div className="plc" />;
    if (faceError || personNear) {
      return this.getMsgText('患者不在相机视野有效范围内');
    }
    if (personFar) {
      return this.getMsgText('患者距离相机较远，请靠近');
    }
    if (thresholdExceeded) {
      return this.getMsgText('精度误差较大，请校准后治疗');
    }

    return <div className="plc" />;
  };

  render(): React.ReactNode {
    const { renderImage, cameraStatus, identifyError } = this.state;
    const { horizontalDisable, horizontal, visiblePart, instructionPart, helper, isBatVerifiCation, mainControl } = this.props;

    return (
      <div className={styles.immediate_view}>
        <canvas width={410} height={230} ref={this.imgView} style={{ zIndex: renderImage ? 5 : 1 }} />
        {instructionPart?.isShowStandardPosition && (
          <img src={personBorder} alt="" className="person_border" style={{ zIndex: renderImage ? 5 : 1 }} />
        )}
        {!renderImage && (
          <div className="camera_error" style={{ zIndex: renderImage ? 1 : 5 }}>
            <div>
              图像加载中
              <span className="dot_ani" />
            </div>
          </div>
        )}
        <div className="bottom_bar">
          <NgIcon iconSvg={Camera} />
          <div className="distance">
            <div className="person">
              <canvas ref={this.personImage} width={356} height={37} />
              <img src={personProgress} alt="" />
            </div>
            <div className="three_distance">
              <span className="cm_65">65cm</span>
              <span className="cm_90">90cm</span>
              <span className="cm_110">110cm</span>
            </div>
          </div>
        </div>
        {instructionPart && (
          <TreatCard
            identifyError={identifyError}
            cameraStatus={cameraStatus}
            horizontalDisable={horizontalDisable}
            horizontal={horizontal}
            visiblePart={visiblePart}
            instructionPart={instructionPart}
            helper={helper}
            isBatVerifiCation={isBatVerifiCation}
            mainControl={mainControl}
          />
        )}
        {helper && createPortal(this.getMsg(), document.querySelector('.preview_treat') || document.body)}
        {/* {helper && this.getMsg()} */}
        {/* {this.props.children} */}
      </div>
    );
  }
}
