import React, { ReactElement } from 'react';
import CameraAndCoil from '@/renderer/component/cameraAndCoil';
import { breadCrumbType, NgBreadcrumb } from '../../uiComponent/NgBreadCrumb';
import { SpotItem } from '../../component/spotItem';
import { EnumPlanStimulusType, PlanImportInfoModel, PlanModel, PlanStimulusModel, PlanTargetModel } from '@/common/types';
import { StrengthAndRotate } from './component/strength';
import NgButton from '../../uiComponent/NgButton';
import NgSwitch from '../../uiComponent/NgSwitch';
import { UserSessionProps } from '../../hocComponent/withUserSession';
import { getM200ApiInstance } from '@/common/api/ngApiAgent';
import TreatInfo from './component/treatInfo';
import { TreatTarget } from './component/treatTarget';
import { RouterProps, withRouter } from '../../hocComponent/withRouter';
import styles from './index.module.less';
import { StimulateTemplate } from './component/stimulateTemplate';
import { calTbsChartData, disableBeatOfTemp } from '../../component/template/calTemplate';
import classNames from 'classnames';
import { IpcRendererEvent } from 'electron';
import { TBSChart } from '../../component/tbsChart';
import { NgIcon } from '../../uiComponent/NgIcon';
import { ImportTemplate } from '../../component/importTemplate';
import { ImportTemplate as ImportTemplateIcon, StimulateImportDisabled } from '../../uiComponent/SvgGather';
import { ErrorModel } from '../../component/systemErrorModel/errorModel';
import { connSocket } from '../../utils/imgSocket';
import { TreatmentSurface } from '../../component/treatmentSurface/treatmentSurface';
import { M200Api } from '@/common/api/ng/m200Api';
import { cloneDeep, isEqual, omit, pick } from 'lodash';
import { message, Spin } from 'antd';
import { TmsWorkStatusProps } from '../../hocComponent/withTmsError';
import { TmsCoil, tmsCoilAtom, tmsWorkStatusAtom } from '../../recoil/tmsError';
import { injectIntl } from 'react-intl';
import { IntlPropType } from '@/common/types/propTypes';
import { treatResultMap } from '../../constant/tmsAndImageSocket';
import { StimulateModel } from '../noPatientTms';
import { LineStatusType, LineType, useLineAtom } from '../../recoil/lineStatus';
import { SetterOrUpdater, useRecoilState, useRecoilValue, useSetRecoilState } from 'recoil';
import { userSessionAtom } from '../../recoil/user';
import { beyondAllEntryPoint } from '../previewPlan/utils';
import { NgLoading } from '../../uiComponent/NgLoading';
import { sendRenderLog, sendTreatLog } from '../../utils/renderLogger';
import { TbsFieldType } from '../../component/template';
import { getRenderFields } from '../stimulateTemplate/component/editTemplateCard';
import { pickParam } from '../../constant/treat';
import { TMSScreenState } from '@/common/constant/tms';
import { TreatParam } from '@/common/types/treat';
import { Coordinate } from '../previewPlan/component/surface/utils';
import { SimpleErrorModel } from '../../component/systemErrorModel/simpleErrorModel';
import { initImageTms } from '../../utils/crashedAction';
import { HelperModal } from './component/helperModal';
import { v4 as uuidv4 } from 'uuid';
import dayjs from 'dayjs';
import { IPC_MODULE } from '../../constant/imgSocket';
import { Sex } from '../home/<USER>';
import { isNotTreatingAtom } from '../../recoil/isNotTreating';
import { FaultTypeList, notNAVTypeList } from '../../../common/systemFault/config';
import { Fault2RenderMapType, FaultEnum, FaultLevelEnum, FaultStatusEnum } from '../../../common/systemFault/type';
import { faultAtom, getFaultWithoutType } from '../../recoil/fault';

export type SpotList = PlanTargetModel & {
  is_active?: boolean;
  color?: string;
  is_error?: boolean;
  is_range_error?: boolean;
  key?: number;
};
type State = {
  pialUrl: string;
  scalpMaskUrl: string;
  volumeFiles: string[];
  surfaceViewer: any;
  spotList: SpotList[];
  targetStimulate?: StimulateModel;
  activeSpot?: PlanTargetModel;
  isTreating: boolean;
  planId: string;
  subjectId: string;
  planInfo: PlanModel | null;
  horizontal: number;
  pulse_total: number;
  treat_time: number;
  activeId: number | null;
  importTemp: boolean;
  treatParam: TreatParam | null;
  prePath: string;
  loadings: {
    apiLoading: boolean;
    surfaceLoading: boolean;
    batLoading: boolean;
    lineLoading: boolean;
  };
  volumeLoading: boolean; // 单拎出来
  horizontalDisable: boolean;
  importDisable: boolean;
  convexUrl: string;
  helper: boolean;
  registCoilVisible: boolean;
  helperVisible: boolean;
};
type Props = UserSessionProps & { fault: Fault2RenderMapType} & TmsWorkStatusProps & {setIsNotTreating: SetterOrUpdater<boolean>} &
TmsCoil &
IntlPropType & { normalLine: LineType[] } & RouterProps<{ planId: string; subjectId: string }>;

export enum ReportEndEnnm {
  notComplete = 1,
  complete = 2,
}

export const breadcrumbList: (
  isTreat: boolean,
  cbList: (() => void)[],
  config: { path: string; breadcrumbName: string; disable?: boolean }[]
) => breadCrumbType[] = (isTreat, cbList, config) => [
  {
    path: '/home',
    breadcrumbName: '首页',
    onClick: cbList[0],
    disable: !!isTreat,
  },
  ...config,
];
export class PreviewTreat extends React.Component<Props, State> {
  private m200Api: M200Api;
  private treatInfoRef: any;
  private spotListRef: any;
  private isDetectLine: boolean;
  private volumeViewer?: any;
  private scalpMaskIndex?: number;
  private spotListScrollTop?: number;
  // private errorCount: number | null;
  private loadingInterval: any;
  private stimulateForm: any;
  private tid: string;
  constructor(props: Props) {
    super(props);
    this.state = {
      pialUrl: '',
      scalpMaskUrl: '',
      convexUrl: '',
      volumeFiles: [],
      surfaceViewer: null,
      spotList: [],
      activeSpot: undefined,
      targetStimulate: undefined,
      isTreating: false,
      planId: props.router.params.planId,
      subjectId: props.router.params.subjectId,
      planInfo: null,
      horizontal: 0,
      pulse_total: 0,
      treat_time: 0,
      activeId: null,
      importTemp: false,
      treatParam: null,
      prePath: '',
      loadings: {
        apiLoading: false,
        surfaceLoading: false,
        batLoading: false,
        lineLoading: false,
      },
      volumeLoading: false,
      horizontalDisable: true,
      importDisable: false,
      helper: true,
      registCoilVisible: false,
      helperVisible: false,
    };
    this.isDetectLine = false;
    this.m200Api = getM200ApiInstance();
    this.treatInfoRef = React.createRef();
    this.spotListRef = React.createRef();
    this.loadingInterval = 0;
    this.stimulateForm = React.createRef();
    this.tid = uuidv4();
  }

  async componentDidMount() {
    this.autoCancelLoading();
    initImageTms();
    connSocket.accuracy.plan_id = this.state.planId;
    connSocket.accuracy.patient_id = this.state.subjectId;
    await Promise.all([this.getPrePath(), this.listeningTms()]);

    document.addEventListener('keydown', event => {
      const code = event.code.toLocaleLowerCase();
      const isAntBtn = document.activeElement?.className?.includes('ant-btn');
      const isAntSwitch = document.activeElement?.className?.includes('ant-switch');
      if (['enter', 'space'].includes(code) && (isAntBtn || isAntSwitch)) {
        event.preventDefault();
      }
    });
  }

  componentWillUnmount() {
    connSocket.clearListenStatusByKey('PreviewTreat');
    window.tmsAPI.remove_beat_btn_by_key('PreviewTreat');
    clearTimeout(this.loadingInterval);
  }

  componentDidUpdate(prevProps: Readonly<Props>, prevState: State): void {
    if (!isEqual(prevProps.normalLine, this.props.normalLine)) {
      this.setTargetNormalLine();
    }
    if (prevState.isTreating !== this.state.isTreating) {
      // eslint-disable-next-line @typescript-eslint/no-floating-promises
      this.reGetPlan(prevState.isTreating);
      this.handleTmsTreatStatusPlanTreat();
      this.props.setIsNotTreating(!this.state.isTreating);
      if (!this.state.isTreating) {
        // eslint-disable-next-line @typescript-eslint/no-floating-promises
        window.systemAPI.pushSystemFault({ '0A030005': FaultStatusEnum.normal, '0A010002': FaultStatusEnum.normal}, 'previewTreat page 结束治疗清除状态');
        this.setState({ helper: true });
      }
    }
    if (prevState.helper !== this.state.helper) {
      connSocket.icp_module = this.state.helper ? IPC_MODULE.NATIVE : IPC_MODULE.NONE;
    }
  }

  private setTmsTreatStatus = (status: number) => {
    window.tmsAPI.set_beat_screen(status);
  };

  // private handleTmsTreatStatusPreparation = (prevTmsError: ISystemErrorInfo[]) => {
  //   if (prevTmsError.length) {
  //     this.setTmsTreatStatus(TMSScreenState.NotStarted);
  //   }
  // };

  private handleTmsTreatStatusPlanTreat = () => {
    if (this.state.isTreating) {
      this.setTmsTreatStatus(TMSScreenState.Preparation);
    } else {
      this.setTmsTreatStatus(TMSScreenState.NotStarted);
    }
  };

  private reGetPlan = async (prevTreat: boolean) => {
    if (!prevTreat) return;
    const { subjectId, planId, spotList, activeId } = this.state;
    const data = await this.m200Api.getPlan(subjectId, planId);
    const treatTarget = data?.plan_target_model_list?.filter(item => !item.has_mep);
    const newSpotList =
      treatTarget?.map((item: any) => {
        const prev = spotList.find(spot => item.id === spot.id);

        return {
          ...item,
          is_active: prev?.is_active,
        };
      }) || [];

    this.setState({
      spotList: newSpotList,
      targetStimulate: newSpotList.find(spot => activeId === spot.id).stimulus,
    });
    setTimeout(() => {
      if (this.spotListScrollTop !== undefined && this.spotListRef.current && this.spotListScrollTop > 710) {
        this.spotListRef.current.scrollTop = this.spotListScrollTop;
      }
    }, 200);
  };

  private detectNormalLine = async (spotArr: PlanTargetModel[]) => {
    const { planId } = this.state;
    const hasLoadLine = this.props.normalLine.find((line: LineType) => `${line.key}` === planId);
    if (hasLoadLine && hasLoadLine.status === LineStatusType.loading) {
      this.startGetNormalLine();
    } else if (this.isDetectLine) {
      return;
    } else if (this.scalpMaskIndex !== undefined && this.volumeViewer) {
      this.computeBeyondAllEntryPoint(this.scalpMaskIndex, spotArr);
    } else if (!this.scalpMaskIndex || !this.volumeViewer) {
      setTimeout(async () => {
        await this.getPlanInfo();
      }, 1500);
    }
  };

  private computeBeyondAllEntryPoint = (scalpIndex: number, spotArr: PlanTargetModel[]) => {
    const { planId, subjectId } = this.state;
    this.isDetectLine = true;
    const setLine = () => {
      // eslint-disable-next-line no-console
      console.log('----');
    };
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    beyondAllEntryPoint(
      this.volumeViewer,
      scalpIndex,
      spotArr,
      {
        plan_id: planId,
        subject_id: subjectId,
      },
      setLine,
      this.state.convexUrl
    ).then(async () => {
      await this.getPlanInfo();
    });
  };

  private startGetNormalLine = () => {
    setTimeout(async () => {
      await this.getPlanInfo();
    }, 1500);
  };

  private setTargetNormalLine = () => {
    const { normalLine } = this.props;
    const { spotList, activeSpot } = this.state;
    if (!activeSpot) {
      // eslint-disable-next-line @typescript-eslint/no-floating-promises
      this.getPlanInfo();

      return;
    }
    const newSpotList = cloneDeep(spotList);
    normalLine.forEach(lines => {
      if (lines.status === LineStatusType.success) {
        lines.value?.forEach(line => {
          const noLineSpot: any = newSpotList.find(spot => spot.id === line?.target_id);
          if (noLineSpot) {
            noLineSpot.normal_line = omit(line, 'target_id');
          }
        });
      }
    });
    this.setState({
      spotList: newSpotList,
    });
  };

  private messageWarning = async (): Promise<any> => {
    return message.warning({
      content: '系统异常，请重试',
      key: 'result',
    });
  };

  private btnCallback = async (_event: IpcRendererEvent, data: any): Promise<any> => { // NOSONAR
    if (!['add', 'sub'].includes(data.data.key) || this.hasError()) return;
    const strengthRange = this.treatInfoRef.current.state.strengthRange;
    const { isTreating, treatParam, targetStimulate, planInfo } = this.state;
    const treatStatus = this.treatInfoRef.current.treatStatus;
    if (!isTreating || !treatParam || !planInfo?.subject_model.motion_threshold || treatStatus !== 'treat_pause') return;
    const newTargetStimulate = cloneDeep(targetStimulate);
    if (!newTargetStimulate?.active_strength) return;
    let level;
    if (data.data.key === 'add') {
      if (newTargetStimulate.active_strength === strengthRange.max) return;
      let added_level = newTargetStimulate.active_strength + ((data.data?.step as number) || 1);
      if (added_level > strengthRange.max) {
        added_level = strengthRange.max;
      }
      newTargetStimulate.active_strength = added_level;
      level = added_level;
      this.setState({ targetStimulate: newTargetStimulate as unknown as PlanStimulusModel });
    }
    if (data.data.key === 'sub') {
      if (newTargetStimulate.active_strength === strengthRange.min) return;
      let sub_level = newTargetStimulate.active_strength - ((data.data?.step as number) || 1);
      if (sub_level < strengthRange.min) {
        sub_level = strengthRange.min;
      }
      newTargetStimulate.active_strength = sub_level;
      level = sub_level;
      this.setState({ targetStimulate: newTargetStimulate as unknown as PlanStimulusModel });
    }
    const newTreatParam = { ...treatParam };
    newTreatParam.level = level;
    this.setState({ treatParam: newTreatParam });
    if (typeof level === 'number') {
      // eslint-disable-next-line @typescript-eslint/no-floating-promises
      window.tmsAPI.set_treatment_level(level,Math.round(level / planInfo?.subject_model.motion_threshold * 100)).then(async levelData => {
        if (levelData.code !== 0 || levelData.data.result !== 0) {
          await this.messageWarning();
          sendTreatLog.info('影像刺激修改强度的返回值', levelData);
        }
      });
    }
  };

  private listeningTms = async () => {
    await window.tmsAPI.beat_btn_by_key('PreviewTreat', this.btnCallback);
  };

  private getPrePath = async () => {
    const prePath = (await this.m200Api.getConfig({ group_name: 'init', name: 'storagePath' }))?.find(item => item.name === 'storagePath')?.value;
    if (!prePath) return;
    this.setState({ prePath }, () => {
      // eslint-disable-next-line @typescript-eslint/no-floating-promises
      this.getPlanInfo();
    });
  };

  private getPlanInfo = async () => {
    const { planId, subjectId, prePath } = this.state;
    this.setState({ loadings: { ...this.state.loadings, apiLoading: true } });
    const size = (await this.m200Api.getStimulusTemplateList({ page_num: 1, page_size: 1 })).records.length;
    const data = await this.m200Api.getPlan(subjectId, planId);
    const treatTarget = data?.plan_target_model_list?.filter(item => !item.has_mep);
    const spotList =
      treatTarget?.map((item: any, _: number) => {
        return { ...item, key: item.id };
      }) || [];
    const hasLineIndex = spotList.findIndex(spot => spot.normal_line);
    let activeSpot;
    if (hasLineIndex > -1) {
      spotList[hasLineIndex].is_active = true;
      activeSpot = spotList[hasLineIndex];
      this.setState({ loadings: { ...this.state.loadings, lineLoading: false } });
    } else {
      await this.detectNormalLine(spotList);
      this.setState({ loadings: { ...this.state.loadings, lineLoading: true } });
    }
    const pialUrlRelativePath = data?.plan_file_model_list?.find(item => item.name === 'pial.gii')?.relative_path;
    const scalpMaskUrlRelativePath = data?.plan_file_model_list?.find(item => item.name === 'scalp_mask.obj')?.relative_path;
    const t1Path = data?.plan_file_model_list?.find(item => item.name === 'T1.mgz')?.relative_path;
    const scalpMaskNii = data?.plan_file_model_list?.find(item => item.name === 'scalp_mask.nii')?.relative_path;
    const plyFile = data?.plan_file_model_list?.find(item => item.name === 'face.ply')?.relative_path;
    const convexUrl = data?.plan_file_model_list?.find(item => item.name === 'head_convex_hull.ply')?.relative_path;
    if (plyFile) {
      const plyMd5 = await window.fileAPI.getFileMd5(`${prePath}/${plyFile}`).catch(err => {
        sendRenderLog.error(err);

        return '';
      });
      connSocket.plyFile = `${prePath}/${plyFile}`;
      connSocket.plyMd5 = plyMd5;
    }
    connSocket.accuracy.cloud_plan_id = data.plan_import_info_model?.import_id;
    connSocket.accuracy.ngfile_sha256 = data.plan_import_info_model?.file_sha256;
    connSocket.accuracy.ngfile_source = data.plan_import_info_model?.file_source;
    connSocket.accuracy.ngfile_version = data.plan_import_info_model?.file_version;
    connSocket.accuracy.gender = Sex[data.subject_model.sex || 0];
    const birthday = dayjs(data.subject_model.birth_date).format('YYYY-MM');
    const now = dayjs(Date.now()).format('YYYY-MM');
    connSocket.accuracy.age = `${dayjs(now).diff(dayjs(birthday),'year')}`;
    connSocket.accuracy.head_mold_name = 'scalp_mask.obj';
    // connSocket.accuracy.head_mold_name = `${prePath}/${scalpMaskUrlRelativePath}`;
    connSocket.accuracy.name = data.subject_model?.name;
    this.setImportInfoModel(data.plan_import_info_model);
    const targetStimulate = activeSpot?.stimulus;
    connSocket.uid = data.subject_model.code;
    this.setState({
      planInfo: data,
      activeSpot,
      spotList,
      activeId: activeSpot?.id || null,
      targetStimulate,
      horizontal: activeSpot?.horizontal || 0,
      pialUrl: `${prePath}/${pialUrlRelativePath}`,
      scalpMaskUrl: `${prePath}/${scalpMaskUrlRelativePath}`,
      convexUrl: convexUrl? `${prePath}/${convexUrl}`: '',
      volumeFiles: [`${prePath}/${t1Path}`, `${prePath}/${scalpMaskNii}`],
      loadings: { ...this.state.loadings, apiLoading: false },
      importDisable: size === 0,
      horizontalDisable: activeSpot?.disable_horizontal,
    });
  };

  private setImportInfoModel = (importModel?: PlanImportInfoModel) => {
    if (!importModel) return;
    connSocket.import_id = importModel.import_id;
    connSocket.file_sha256 = importModel.file_sha256;
    connSocket.file_version = importModel.file_version;
    connSocket.file_source = importModel.file_source;
  };

  private handleSpotClick = (id: number) => {
    const { spotList, planInfo } = this.state;
    let activeSpot: any;
    let targetStimulate: StimulateModel | undefined;
    const newSpotList = spotList.map(spot => {
      if (spot.id === id) {
        activeSpot = { ...spot, is_active: true };
        targetStimulate = spot.stimulus;

        return activeSpot;
      }

      return { ...spot, is_active: false };
    });
    if (!targetStimulate || !planInfo?.subject_model?.motion_threshold || !activeSpot?.normal_line) return;
    const near_index = this.state.spotList.findIndex(v => v.id === id);
    this.spotListScrollTop = 102 * near_index;

    this.setState({
      spotList: newSpotList,
      activeSpot,
      activeId: activeSpot?.id,
      targetStimulate,
      horizontal: activeSpot?.horizontal || 0,
      horizontalDisable: activeSpot.disable_horizontal,
    });
  };

  private stimulateTemplateValueChange = (values: any) => {
    const { targetStimulate, planInfo } = this.state;
    if (!planInfo?.subject_model.motion_threshold) return;
    let newTargetStimulate = {
      ...targetStimulate,
      ...values,
    };
    if (values.type) {
      let renderFields: TbsFieldType[] = getRenderFields(values.type).filter(item => item.key !== 'relative_strength');
      newTargetStimulate = renderFields.reduce((result: any, item: TbsFieldType) => {
        return { ...result, [item.key]: undefined };
      }, newTargetStimulate);
      newTargetStimulate.type = values.type;
    }
    if (values.strand_pulse_count && values.strand_pulse_count === 1) {
      newTargetStimulate.intermission_time = undefined;
    }
    this.setState({ targetStimulate: newTargetStimulate as PlanStimulusModel });
  };

  private horizontalChange = (value: any) => {
    this.setState({ horizontal: value });
  };

  private handleStrengthChange = (strength: number) => {
    const { targetStimulate, planInfo } = this.state;
    if (!planInfo?.subject_model?.motion_threshold) return;
    const newTargetStimulate = { ...targetStimulate, relative_strength: strength };
    this.setState({ targetStimulate: newTargetStimulate as PlanStimulusModel });
  };

  private hasError = () => {
    const { horizontal, helper } = this.state;
    const { fault } = this.props;
    const disable = helper ? !!fault[FaultLevelEnum.error].length : !!getFaultWithoutType(FaultEnum.imageFault).length;

    return !!(disable || typeof horizontal !== 'number');
  };

  public static dealUndefine: (obj: { [name: string]: any }) => { [name: string]: number } = obj => {
    // eslint-disable-next-line radix
    return Object.keys(obj).reduce((pre, cur) => ({ ...pre, [cur]: isNaN(parseInt(obj[cur])) || obj[cur] === null ? '--' : obj[cur] }), {});
  };

  private tbsTreat = async () => {
    const { targetStimulate, planInfo } = this.state;
    if (!targetStimulate?.relative_strength || !planInfo?.subject_model.motion_threshold) return;
    const { pulse_total, treatment_time } = PreviewTreat.dealUndefine(calTbsChartData(targetStimulate));
    if (isNaN(pulse_total)) return;
    this.tid = uuidv4();
    const param = {
      action: 'set_treatment_plan',
      level: Math.min(Math.round((targetStimulate?.relative_strength * planInfo?.subject_model.motion_threshold) / 100), 100),
      frequency: Math.round((targetStimulate.plexus_inner_frequency as number) * 100),
      intensity: Math.round((targetStimulate.plexus_inter_frequency as number) * 100),
      count: targetStimulate.plexus_inner_pulse_count,
      bunch: targetStimulate.plexus_count,
      series: targetStimulate.strand_pulse_count,
      pause: targetStimulate.intermission_time,
      sum: pulse_total,
      time: treatment_time,
      tid: this.tid,
    };
    if (param.series === 1) {
      param.pause = 1;
    }
    sendTreatLog.info('影像设置的tbs参数', param);
    const data = await window.tmsAPI.set_treatment_plan(param);
    if (data.code === 0) {
      sendTreatLog.info('影像设置的tbs参数成功');
      if (data.data?.result === 0) {
        this.setState({ isTreating: true, pulse_total, treat_time: treatment_time, treatParam: param });
      } else {
        sendTreatLog.info('影像设置tbs参数出现故障', treatResultMap[data.data.result]);
        await this.messageWarning();
      }
    } else {
      await message.warning({
        content: data.message || 'tms通讯出错',
        key: 'tms_error',
      });
    }
  };

  private rTmsTreat = async () => {
    const { targetStimulate, planInfo } = this.state;
    if (!targetStimulate?.relative_strength || !planInfo?.subject_model.motion_threshold) return;
    const { pulse_total, treatment_time } = PreviewTreat.dealUndefine(calTbsChartData(targetStimulate));
    if (isNaN(pulse_total)) return;
    this.tid = uuidv4();
    const param = {
      action: 'set_treatment_plan',
      level: Math.min(Math.round((targetStimulate?.relative_strength * planInfo?.subject_model.motion_threshold) / 100), 100),
      frequency: Math.round((targetStimulate.strand_pulse_frequency as number) * 100), // 1
      intensity: Math.round((targetStimulate.strand_pulse_frequency as number) * 100),
      count: 1,
      bunch: targetStimulate.inner_strand_pulse_count,
      series: targetStimulate.strand_pulse_count,
      pause: targetStimulate.intermission_time,
      sum: pulse_total,
      time: treatment_time,
      tid: this.tid,
    };
    if (param.series === 1) {
      param.pause = 1;
    }
    sendTreatLog.info('影像设置的rtms参数', param);
    // this.setState({ isTreating: true, pulse_total, treat_time: +T5, treatParam: param });
    const data = await window.tmsAPI.set_treatment_plan(param);
    if (data.code === 0) {
      sendTreatLog.info('影像设置的rtms参数成功');
      if (data.data.result === 0) {
        this.setState({ isTreating: true, pulse_total, treat_time: treatment_time, treatParam: param });
      } else {
        sendTreatLog.info('影像设置rtms参数出现故障', treatResultMap[data.data.result]);
        await this.messageWarning();
      }
    } else {
      await message.error({
        content: data.message || 'tms通讯出错',
        key: 'tms_error',
      });
    }
  };

  private treatment_plan_end = async () => {
    if (this.treatInfoRef.current.treatStatus === 'treat_stop') {
      this.setState({ isTreating: false, helper: true });

      return;
    }
    sendTreatLog.info('影像刺激手动结束');
    this.treatInfoRef.current.treatStatus = 'treat_stop';
    this.treatInfoRef.current.setState({ treatStatus: 'treat_stop' });
    this.setState({ isTreating: false, helper: true });
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    this.fetchPlanEnd(ReportEndEnnm.notComplete);
    const data = await window.tmsAPI.image_treatment_plan_start('PlanEnd', this.tid);
    if (data.code !== 0) {
      await message.error(data.message || 'tms通讯出错');

      return;
    }
    if (data.data.result !== 0) {
      await this.messageWarning();
      sendTreatLog.info('影像刺激结束出现故障', treatResultMap[data.data.result]);
    }
  };

  private fetchPlanEnd = async (type: ReportEndEnnm) => {
    const { targetStimulate, planInfo } = this.state;
    let { active_strength, relative_strength } = targetStimulate || {};
    if (active_strength && planInfo) {
      relative_strength = Math.round(active_strength / planInfo.subject_model.motion_threshold! * 100);
    }
    await this.m200Api.endTreat({ uuid: this.tid, type, relative_strength });
  };

  private setFormNull = async () => {
    const values = this.stimulateForm.current?.form.getFieldsValue();
    const obj = {};
    for (const key in values) {
      if (Object.prototype.hasOwnProperty.call(values, key)) {
        const item = values[key];
        if ([null, undefined].includes(item) && (key !== 'intermission_time' || (values.strand_pulse_count !== 1 && key === 'intermission_time'))) {
          obj[key] = null;
        } else {
          obj[key] = values[key];
        }
      }
    }
    this.stimulateForm.current?.form.setFieldsValue(obj);
    await new Promise(async res => {
      setTimeout(async () => {
        return res('');
      }, 100);
    });
  };

  private updateStimulate = async (type: string) => {
    const { targetStimulate, planInfo, horizontal, horizontalDisable } = this.state;
    if (!planInfo?.subject_model.motion_threshold) return;
    const plan_stimulus_model_list = { ...pick(targetStimulate, pickParam), horizontal, disable_horizontal: horizontalDisable };
    if (type === 'complete' && targetStimulate?.active_strength) {
      plan_stimulus_model_list.relative_strength = Math.round(targetStimulate?.active_strength / planInfo?.subject_model.motion_threshold * 100);
    }
    const updateParam = {
      subject_id: planInfo?.subject_id,
      plan_id: planInfo?.id,
      plan_stimulus_model_list: [plan_stimulus_model_list],
    };

    await this.m200Api.updateStimulus(updateParam);
  };

  private handleEnsureTreat = async (): Promise<any> => {
    // NOSONAR
    const { targetStimulate, planInfo, horizontal, activeSpot } = this.state;
    if (typeof horizontal !== 'number' || horizontal > 145 || horizontal < -145 || `${horizontal}`.includes('.') || isNaN(horizontal)) return;
    await this.setFormNull();
    await this.stimulateForm.current.form.validateFields();
    const { coilInfo } = this.props;
    if (!targetStimulate?.relative_strength || !planInfo?.subject_model.motion_threshold) return;
    const active_strength = Math.min(Math.round((targetStimulate?.relative_strength * planInfo?.subject_model?.motion_threshold) / 100), 100);
    if (typeof coilInfo?.coil_max_temperature !== 'number') return;
    const disableTreat = disableBeatOfTemp(coilInfo?.coil_max_temperature || 0, active_strength, targetStimulate);
    if (disableTreat) {
      return message.warning({
        content: '存在超温风险，请降温后再试',
        key: 'over_temp',
      });
    }
    connSocket.getImage = false;  // 避免之前退出的时候有请求刚发出去，退回到首页结果返回造成getImage为true
    connSocket.target = [
      [activeSpot?.vol_ras.x, activeSpot?.vol_ras.y, activeSpot?.vol_ras.z, 1],
      [activeSpot?.normal_line?.x, activeSpot?.normal_line?.y, activeSpot?.normal_line?.z, 1],
    ];
    connSocket.navangle = [0, this.state.horizontal, 0];
    connSocket.accuracy.horizontal_rotation_angle = horizontal * (-1);
    connSocket.accuracy.top_corner_no = `${activeSpot?.hemi}${activeSpot?.vertex_index}`;
    connSocket.accuracy.volrsa = `${activeSpot?.vol_ras.x};${activeSpot?.vol_ras.y};${activeSpot?.vol_ras.z}`;
    connSocket.accuracy.stimulate_type = targetStimulate?.type === EnumPlanStimulusType.TBS ? 'TBS' : 'rTMS';
    await this.updateStimulate('ensure');
    this.setState({ targetStimulate: { ...targetStimulate, active_strength } });
    if (targetStimulate?.type === EnumPlanStimulusType.TBS) {
      // eslint-disable-next-line @typescript-eslint/no-floating-promises
      this.tbsTreat();

      return;
    }
    if (targetStimulate?.type === EnumPlanStimulusType.RTMS) {
      // eslint-disable-next-line @typescript-eslint/no-floating-promises
      this.rTmsTreat();
    }
  };

  private treatCompleteCallback = () => {
    this.setState({ isTreating: false, treatParam: null, helper: true });
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    window.tmsAPI.image_treatment_plan_start('PlanEnd',this.tid);
  };

  private importTempOk = (data: any) => {
    const { targetStimulate } = this.state;
    this.setState({
      importTemp: false,
      targetStimulate: {
        ...data,
        target_id: targetStimulate?.target_id,
        plan_id: targetStimulate?.plan_id,
        subject_id: targetStimulate?.subject_id,
      },
    });
  };

  private importTempCancel = () => {
    this.setState({ importTemp: false });
  };

  private getSpotListDom = () => {
    const { activeId } = this.state;

    return (
      <div className="spot_list_and_control">
        <div className="spot_list" ref={this.spotListRef}>
          {this.state.spotList?.map((v: any, index: number) => (
            <SpotItem activeId={activeId} key={v.id} spotInfo={v} onSelect={this.handleSpotClick} index={index} isTreat />
          ))}
        </div>
      </div>
    );
  };

  private handleErrorModalOK = () => {
    const { isTreating } = this.state;
    if (isTreating) {
      this.setState({ isTreating: false });
      // eslint-disable-next-line @typescript-eslint/no-floating-promises
      this.treatment_plan_end();
    } else {
      this.setState({ helper: true});
    }
  };

  private cameraHelperChange = (checked: boolean) => {
    const { helperVisible } = this.state;
    this.setState({ helper: true, helperVisible: checked ? helperVisible : true });
  };

  private setTreatLogsError = () => {
    window.systemAPI.pushSystemFault({ '0A010002': FaultStatusEnum.abnormal }, 'previewTreat page 请求接口错误');
    // this.setState({ isTreating: false});
  };

  private setLoading = (loadings: any) => {
    this.setState({ loadings: { ...this.state.loadings, ...loadings } });
  };

  private getBreadcrumbConfig = () => {
    const { isTreating } = this.state;
    if (!isTreating) {
      return [{ path: '/preview', breadcrumbName: '治疗预览' }];
    } else {
      return [
        { path: '/preview', breadcrumbName: '治疗预览', disable: true },
        { path: '/treating', breadcrumbName: '治疗中', disable: true },
      ];
    }
  };

  private getVolumeScalpIndex = (index: number) => {
    this.scalpMaskIndex = index;
  };

  private setVolumeViewer = (viewer: any) => {
    this.volumeViewer = viewer;
  };

  private goBack = () => {
    const { router } = this.props;
    const { isTreating } = this.state;
    if (isTreating) return;
    router.navigate('/home');
  };

  private computedNormalCallback = (data: any) => {
    this.setState({ loadings: { ...this.state.loadings, lineLoading: false } });
  };

  private setVolumeLoading = (loading: boolean) => {
    this.setState({ volumeLoading: loading });
  };

  private renderLoading = (): ReactElement => {
    return (
      <div>
        <NgLoading loadingText={'加载中...'} />
      </div>
    );
  };

  private disableHorizontalChange = (disable: boolean) => {
    this.setState({ horizontalDisable: !disable });
  };

  private autoCancelLoading = () => {
    this.loadingInterval = setTimeout(
      () => {
        this.setState(
          {
            loadings: {
              apiLoading: false,
              surfaceLoading: false,
              batLoading: false,
              lineLoading: false,
            },
            volumeLoading: false,
          },
          () => {
            clearTimeout(this.loadingInterval);
          }
        );
      },
      5 * 60 * 1000
    );
  };

  private onClickChangePoint = (coords: Coordinate) => {
    const near_spot = this.state.spotList.find(
      v => Math.abs(v.surf_ras.x - coords.x) < 3 && Math.abs(v.surf_ras.y - coords.y) < 3 && Math.abs(v.surf_ras.z - coords.z) < 3
    );
    if (near_spot) {
      // eslint-disable-next-line @typescript-eslint/no-floating-promises
      this.handleSpotClick(near_spot.id!);
      if (this.spotListRef.current) {
        const near_index = this.state.spotList.findIndex(v => v.id === near_spot.id);
        const scrollTop = 102 * near_index;
        this.spotListScrollTop = scrollTop;
        this.spotListRef.current.scrollTop = scrollTop;
      }
    }
  };

  private handleRegistCoilOk = () => {
    this.setState({ registCoilVisible: false });
    const { router } = this.props;
    router.navigate('/registCoil');
  };

  private handleRegistCoilCancle = () => {
    this.setState({ registCoilVisible: false });
    const { router } = this.props;
    router.navigate('/home');
  };

  private handleModalOk = () => {
    this.setState({ helperVisible: false, helper: false });
  };

  render(): React.ReactNode { // NOSONAR
    const {
      targetStimulate,
      importTemp,
      isTreating,
      planInfo,
      activeSpot,
      loadings,
      treatParam,
      volumeLoading,
      importDisable,
      helper,
      registCoilVisible,
      helperVisible,
    } = this.state;
    const spinning = Object.values(loadings).some(loading => !!loading) || volumeLoading;

    return (
      <div className={classNames(styles.preview_treat, 'preview_treat')}>
        <Spin className="ng_spin" spinning={spinning} indicator={this.renderLoading()}>
          <NgBreadcrumb
            contentClassName="ng_breadcrumb"
            items={breadcrumbList(isTreating, [this.goBack], this.getBreadcrumbConfig())}
            isGray={false}
          />
          <CameraAndCoil notShowCameraModal className='photo_and_coil' />
          {!isTreating && (
            <div className='helper_switch'>
              <span>目标识别辅助：</span>
              <NgSwitch className='switch' size="small" checked={helper} onChange={this.cameraHelperChange} />
            </div>
          )}
          <div className="content">
            {planInfo && (
              <TreatmentSurface
                // setSurfaceViewer={(viewer) => this.setState({ surfaceViewer: viewer })}
                pialUrl={this.state.pialUrl}
                scalpMaskUrl={this.state.scalpMaskUrl}
                volumeFiles={this.state.volumeFiles}
                isTreating={this.state.isTreating}
                spotList={this.state.spotList}
                planId={this.state.planId}
                subjectId={this.state.subjectId}
                planInfo={planInfo}
                children={null}
                activeId={this.state.activeId}
                SpotList={this.getSpotListDom()}
                horizontal={this.state.horizontalDisable ? 0 : -(this.state.horizontal || 0)}
                coilPanelMissingCallback={()=>this.setState({registCoilVisible: true})}
                computedNormalCallback={this.computedNormalCallback}
                setLoading={this.setLoading}
                setVolumeLoading={this.setVolumeLoading}
                setVolumeViewer={this.setVolumeViewer}
                getVolumeScalpIndex={this.getVolumeScalpIndex}
                horizontalDisable={this.state.horizontalDisable}
                onClickChangePoint={this.onClickChangePoint}
                helper={helper}
              />
            )}
            {!this.state.isTreating && planInfo && (
              <div className="stimulate_template_button">
                <div className="stimulate_template">
                  <StrengthAndRotate
                    strengthChange={this.handleStrengthChange}
                    stimulate={targetStimulate}
                    motion_threshold={this.state.planInfo?.subject_model.motion_threshold || 0}
                    horizontal={this.state.horizontal}
                    horizontalChange={this.horizontalChange}
                    horizontalDisable={this.state.horizontalDisable}
                    disableHorizontalChange={this.disableHorizontalChange}
                  />
                  <div className="import_tem">
                    <span>参数</span>
                    <NgIcon
                      tooltip={{
                        title: importDisable ? '无可导入脉冲模版' : '导入脉冲模版',
                      }}
                      onClick={() => this.setState({ importTemp: true })}
                      iconSvg={importDisable ? StimulateImportDisabled : ImportTemplateIcon}
                      disabled= {importDisable}
                    />
                  </div>
                  {targetStimulate && (
                    <>
                      <TBSChart template={targetStimulate} motionThreshold={this.state.planInfo?.subject_model.motion_threshold || 0} />
                      <StimulateTemplate
                        ref={this.stimulateForm}
                        stimulate={targetStimulate}
                        motionThreshold={this.state.planInfo?.subject_model.motion_threshold || 0}
                        isNotComplete={false}
                        onValuesChange={this.stimulateTemplateValueChange}
                      />
                    </>
                  )}
                </div>
                <NgButton onClick={this.handleEnsureTreat} disabled={this.hasError()} className="ensure_template">
                  确认
                </NgButton>
              </div>
            )}
            {this.state.isTreating && planInfo && (
              <div className="treat_info">
                <TreatInfo
                  stimulate={targetStimulate}
                  motion_threshold={this.state.planInfo?.subject_model.motion_threshold || 0}
                  pulse_total={this.state.pulse_total}
                  treat_time={this.state.treat_time}
                  tid={this.tid}
                  treatCompleteCallback={this.treatCompleteCallback}
                  ref={this.treatInfoRef}
                  treatParam={treatParam}
                  activeSpot={activeSpot}
                  horizontalDisable={this.state.horizontalDisable}
                  horizontal={this.state.horizontal || 0}
                  setTreatLogsError={this.setTreatLogsError}
                  updateStimulate={this.updateStimulate}
                  fetchPlanEnd={this.fetchPlanEnd}
                  helper={helper}
                />
                <div className="blank" />
                {targetStimulate && (
                  <TreatTarget
                    horizontal={this.state.horizontal || 0}
                    stimulate={targetStimulate}
                    name={activeSpot?.name || ''}
                    horizontalDisable={this.state.horizontalDisable}
                  />
                )}
                <NgButton onClick={this.treatment_plan_end} className="stop_treat">
                  结束
                </NgButton>
              </div>
            )}
          </div>
          <ImportTemplate visible={importTemp} onCancel={this.importTempCancel} onOk={this.importTempOk} />
          {isTreating && <ErrorModel faultTypeList={this.state.helper ? FaultTypeList : notNAVTypeList} isStimulate={isTreating} onOk={this.handleErrorModalOK} onOpen={() => {
            initImageTms(this.tid);
          }}/>}
          {registCoilVisible && (
            <SimpleErrorModel
              visible
              isClearRegistCoilInfo
              title="线圈注册结果异常"
              isStimulate={false}
              errorList={['重新注册线圈后，可继续使用']}
              btnLabel={'线圈注册'}
              cancleLabel="取消"
              onCancle={this.handleRegistCoilCancle}
              onOk={this.handleRegistCoilOk}
            />
          )}
          <HelperModal
            onOk={this.handleModalOk}
            visible={helperVisible}
            onClose={() => this.setState({ helperVisible: false })}
          />
        </Spin>
      </div>
    );
  }
}

const Wrap = (props: any) => {
  const normalLine = useRecoilValue(useLineAtom);
  const tmsWorkStatus = useRecoilValue(tmsWorkStatusAtom);
  const coilInfo = useRecoilValue(tmsCoilAtom);
  const [userSession, setUserSession] = useRecoilState(userSessionAtom);
  const [fault] = useRecoilState(faultAtom);
  const setIsNotTreating = useSetRecoilState(isNotTreatingAtom);

  return (
    <PreviewTreat
      normalLine={normalLine}
      tmsWorkStatus={tmsWorkStatus}
      coilInfo={coilInfo}
      fault={fault}
      setIsNotTreating={setIsNotTreating}
      userSession={userSession}
      setUserSession={setUserSession}
      {...props}
    />
  );
};

export default injectIntl(withRouter(Wrap));
