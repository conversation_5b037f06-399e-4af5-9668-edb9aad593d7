@import '@/renderer/static/style/baseColor.module.less';

.container {
  padding: 20px 20px 0;
  box-sizing: border-box;

  .header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
  }

  .table {
    .tabs {
      display: flex;
      position: relative;

      .createAccount {
        position: absolute;
        right: 0;
      }

      :global {
        .ant-tabs-nav {
          width: fit-content;
        }

        .ant-tabs-tab {
          width: 120px;
          justify-content: center;
        }
      }
    }
  }
}

.accountForm {
  :global {

    .ant-form,
    .ant-form-item-label label {
      color: @colorA9  !important;
    }
  }
}

.accountFooter {
  display: flex;
  justify-content: flex-end;

  & div:first-child {
    margin-right: 24px;
  }
}

.header_side {
  width: 500px;
}

.header_icons {
  display: flex;
  flex-direction: row-reverse;
}
