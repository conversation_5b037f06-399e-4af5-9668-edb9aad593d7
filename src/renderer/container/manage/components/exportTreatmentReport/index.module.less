@import '@/renderer/static/style/baseColor.module.less';

.container {
  display: flex;
  flex-direction: column;
  width: 340px;
  height: 222px;
  padding: 8px 12px;

  .head {
    position: relative;
    width: 100%;
    height: 40px;
    display: flex;
    flex-direction: row;
    align-items: center;

    .label {
      display: inline-flex;
      margin-right: 16px;
    }

    .value {
      display: inline-flex;
    }

    :global {
      .ant-radio-button-wrapper {
        background-color: @colorA5;
        padding: 0 18px;

        &:hover {
          background-color: @colorA5_1;
        }

        &.ant-radio-button-wrapper-checked {
          background-color: @colorA6;
        }
      }
    }
  }

  .center {
    height: 50px;
    padding: 20px 0 34px;
    box-sizing: content-box;
  }

  .footer {
    width: 100%;
    height: 36px;
    display: flex;
    flex-direction: row-reverse;
    margin-top: 40px;
    .export {
      margin-left: 24px;
    }

    .cancel {
      margin-bottom: 4px;
    }
  }

  .type {
    position: absolute;
    width: 48px;
    height: 32px;
    line-height: 32px;
    top: 0;
    left: 0;
  }

  :global {
    .ant-tabs-nav {
      margin-left: 48px;
    }
  }
}

.checkRange {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  .dateRange {
    width: 100%;
    display: flex;
    justify-content: space-between;
  }

  .selectedPatient {
    width: 100%;
    height: 32px;
    margin-top: 24px;

    :global {
      .ant-select-selection-search {
        margin-left: 0px !important;
      }
      .anticon-close {
        color: #ffffff !important;
      }
      .ant-select-selection-item {
        background-color: @colorA6;
      }
      .ant-select-selection-search-input {
        color: @colorA12 !important;
      }
      .anticon-check {
        display: none !important;
      }
      .ant-select-selector {
        padding: 0px 8px !important;
      }
      .ant-select-selection-placeholder {
        color: @colorA12;
      }

      .ant-select-selection-item-remove {
        cursor: url('@/renderer/static/svg/cursor.cur'), pointer;
      }
    }
  }

  .separator {
    color: @colorA7;
    margin-left: 6px;
    margin-right: 6px;
    padding-top: 6px;
  }
}

.exportModal {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: @colorA11;

  .errorTitle {
    font-size: 16px;
    line-height: 28px;
    height: 28px;
    display: inline-flex;
    align-items: center;
    margin-bottom: 16px;
  }

  .content {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;

    .size {
      width: 80%;
      display: flex;
      justify-content: space-between;
      margin-top: 24px;
      margin-bottom: 12px;

      .left {
        width: 50%;
        display: inline-flex;
        justify-content: center;
      }

      .right {
        display: inline-flex;
        width: 50%;
        justify-content: center;
      }

      .label {
        margin-right: 12px;
      }
    }

    .warningLabel {
      width: 100%;
      margin-bottom: 24px;
      display: flex;
      justify-content: center;
    }
  }

  .footer {
    display: flex;

    .cancel {
      margin-right: 24px;
    }
  }

  .ml12 {
    margin-left: 12px;
  }
}
