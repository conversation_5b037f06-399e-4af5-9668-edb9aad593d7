import React, { useEffect, useState } from 'react';
import { useAsyncEffect } from 'ahooks';
import styles from './index.module.less';
import NgModal from '@/renderer/uiComponent/NgModal';
import { NgProgress } from '@/renderer/uiComponent/NgProgress';
import { ReactComponent as Warning } from '@/renderer/static/svg/warning.svg';
import { ReactComponent as Success } from '@/renderer/static/svg/success.svg';
import { message } from 'antd';
import { useIntl } from 'react-intl';
import NgButton from '@/renderer/uiComponent/NgButton';
import NgButtonText from '../../../../uiComponent/NgButtonText';
import { SelectTypeEnmu, SuffixType } from './config';
import dayjs from 'dayjs';
import { getSizeBySuffix, getSizeSuffix } from './utils';

type Props = {
  visible: boolean;
  startTime: dayjs.Dayjs;
  endTime: dayjs.Dayjs;
  path: string;
  type: SelectTypeEnmu;
  onClose(type?: boolean): void;
  allTidString: string;
};

export const ProcessModal = (props: Props) => {
  const intl = useIntl();
  const { visible, startTime, endTime, path, type, onClose, allTidString } = props;
  const [process, setProcess] = useState<number>(0);
  const [uFree, setUFree] = useState<number>(0);
  const [dataTotal, setDataTotal] = useState<number>(0);
  const [sizeSuffix, setSizeSuffix] = useState<SuffixType[]>(['M', 'M']);
  const [isDownOver, setIsDownOver] = useState<boolean>(false);
  const [unmounting, setUnmounting] = useState<boolean>(false);
  const [stoping, setStoping] = useState(false);
  useAsyncEffect(async () => {
    if (visible) {
      setDataTotal(0);
      setProcess(0);
      setSizeSuffix([sizeSuffix[0], 'M']);
      const startStamp = startTime.startOf('days').valueOf() / 1000 | 0;
      const endStamp = endTime.endOf('days').valueOf() / 1000 | 0;
      window.systemAPI.setLokiLog({ startTime, endTime, path, isVideo: type === SelectTypeEnmu.video, type, startStamp, endStamp, allTidString });
      await window.systemAPI.getZipProgress((_event, res: any) => {
        if (res.isErr) {
          closeModal(0, true);

          return;
        }
        console.log('从main中接受到的数据', res); // eslint-disable-line no-console
        if (res.isZip) {
          setIsDownOver(false);
          // 进度处理逻辑
          logicZip(res.proportion, res.uFree, res.total, false);
        } else {
          logicZip(100, res.uFree, res.total, true);
          setIsDownOver(true);
          window.systemAPI.removeZipProgressListener(type);
          // progressDone();
        }
      });
    }
  }, [props.visible, path]);

  useEffect(() => {
    // 页面级的隐藏
    if (!visible) {
      window.systemAPI.removeZipProgressListener(type);
    }

    // 组件级销毁
    return () => {
      window.systemAPI.removeZipProgressListener(type);
    };
  }, [visible]);

  // 100的进度时，暂时调整为99,如果已完成调整为100%
  const logicZip = (proportion: number, uFreeParam: number, totalParam: number, isDown: boolean) => {
    let newProportion = Number.isNaN(proportion) ? 0 : Number.parseInt(proportion.toString(), 10);
    setUFree(uFreeParam);
    setDataTotal(totalParam);
    setSizeSuffix([getSizeSuffix(uFreeParam), getSizeSuffix(totalParam)]);
    if (isDown) {
      setProcess(100);
    } else {
      setProcess(newProportion >= 100 ? 99 : newProportion);
    }
  };
  const getMessageLabel = (isErr: boolean, proportion: number) => {
    if (isErr) {
      return intl.formatMessage({ id: '操作失败' });
    }

    if (proportion === 100) {
      return intl.formatMessage({ id: '操作成功' });
    }

    return intl.formatMessage({ id: '操作失败' });
  };

  const closeModal = (currentProcess: number, isError: boolean) => {
    let typeM = currentProcess === 100 ? 'success' : 'error';
    typeM = isError ? 'error' : typeM;
    if (typeM === 'error') {
      return onClose(typeM === 'error');
    }
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    message[typeM as 'success' | 'error'](getMessageLabel(isError, currentProcess));
    onClose();
  };

  // 先停止进度，再停止
  const onStop = async () => {
    try {
      setStoping(true);
      window.systemAPI.removeZipProgressListener(type);
      await window.systemAPI.stopZip();
    } catch (error) {
      // eslint-disable-next-line @typescript-eslint/no-floating-promises
      message.error('操作失败');
      onClose();
    } finally {
      setIsDownOver(true);
      setStoping(false);
    }
  };

  const handleExportUSB = async () => {
    //
    const usbPath = path.split('/').slice(0, 4).join('/');
    try {
      setUnmounting(true);
      const res = await window.systemAPI.unmoutUSB(usbPath);
      closeModal(100, JSON.parse(res).code !== 1 ? true : false);
    } catch (error) {
      // closeModal(100, true);
    } finally {
      setUnmounting(false);
    }
  };

  return (
    <NgModal title="" closable={false} open={visible} width={460} footer={<> </>}>
      <div className={styles.exportModal}>
        <div className={styles.errorTitle}>
          <>
            {process === 100? <Success /> :<Warning />}
            <span className={styles.ml12}>{intl.formatMessage({ id: process === 100? '治疗记录导出完成': '治疗记录导出中，请不要离开当前页面' })}</span>
          </>
        </div>
        <div className={styles.content}>
          <NgProgress
            percent={process}
            type={'circle'}
            width={180}
            strokeWidth={20}
            status={'normal'}
            trailColor={'#41414b'}
            strokeColor={'#4EB7B9'}
          />

          <div className={styles.size}>
            {dataTotal > 0 && (<>
              <div className={styles.left}>
                <span className={styles.label}>{intl.formatMessage({ id: '导出文件:' })}</span>
                <span className={styles.value}>{getSizeBySuffix(dataTotal, sizeSuffix[1])}{ sizeSuffix[1] }</span>
              </div>
              <div className={styles.right}>
                <span className={styles.label}>{intl.formatMessage({ id: '设备空间:' })}</span>
                <span className={styles.value}>{getSizeBySuffix(uFree, sizeSuffix[0])}{ sizeSuffix[0] }</span>
              </div>
            </>
            )}

          </div>
          {uFree < dataTotal && !isDownOver && (
            <div className={styles.warningLabel}>{intl.formatMessage({ id: '设备空间不足，存在操作失败风险' })}</div>
          )}
          <div className={styles.footer}>
            {!isDownOver && process > 0 && <NgButton disabled={stoping || process > 97} loading={stoping} onClick={onStop}>{intl.formatMessage({ id: '终止' })}</NgButton>}
            {isDownOver && (
              <NgButtonText className={styles.cancel} onClick={() => onClose()}>
                {intl.formatMessage({ id: '取消' })}
              </NgButtonText>
            )}
            {isDownOver && (
              <NgButton loading={unmounting} onClick={handleExportUSB}>
                {intl.formatMessage({ id: '弹出移动设备' })}
              </NgButton>
            )}
          </div>
        </div>
      </div>
    </NgModal>
  );
};
