import React, { useCallback, useEffect, useState } from 'react';
import locale from 'antd/es/date-picker/locale/zh_CN';
// eslint-disable-next-line import/no-unassigned-import,import/no-internal-modules
import 'dayjs/locale/zh-cn';
import styles from './index.module.less';
import NgButton from '@/renderer/uiComponent/NgButton';
import { NGDatePicker } from '@/renderer/uiComponent/NgDatePicker';
import NgSelectFileModal, { UploadType } from '@/renderer/uiComponent/NgSelectFileModal';
import { DeviceType } from '@/renderer/container/previewPlan';
import { useRecoilValue } from 'recoil';
import { osUserInfo } from '@/renderer/recoil/osUserInfo';
import { useIntl } from 'react-intl';
import { ProcessModal } from '@/renderer/container/manage/components/exportTreatmentReport/processModal';
import dayjs from 'dayjs';
import { NgRadio } from '@/renderer/uiComponent/NgRadio';
import NgButtonText from '@/renderer/uiComponent/NgButtonText';
import { product } from '../../../../constant/product';
import { message } from 'antd';
import { SelectTypeEnmu, timeTerm } from './config';
import { NgSelect } from '../../../../uiComponent/NgSelect';
import { useAsyncEffect } from 'ahooks';
import { getM200ApiInstance } from '../../../../../common/api/ngApiAgent';
import { QueryPatientInfo } from '../../../../../common/types';

type Props = {
  setTreatmentPopOverOpen: React.Dispatch<React.SetStateAction<boolean>>;
  modalVisible?: boolean;
  setModalVisible?: React.Dispatch<React.SetStateAction<boolean>>;
  notShowContent?: boolean;
  onClose?(bool: boolean): void;
};

const ExportTreatmentReport = (props: Props) => {
  const deviceType: DeviceType = /macintosh|mac os x/i.test(navigator.userAgent) ? 2 : 1;
  const osInfo = useRecoilValue(osUserInfo);
  const [disabledExport, setDisabledExport] = useState<boolean>(false);
  const intl = useIntl();
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [processModelVisible, setProcessModelVisible] = useState<boolean>(false);
  const [startTimeState, setStartTimeState] = useState<any>(null);
  const [endTimeState, setEndTimeState] = useState<any>(null);
  const [path, setPath] = useState<string>('');
  const [type, setType] = useState<SelectTypeEnmu>(SelectTypeEnmu.text);
  const [selectList, setSelectList] = useState<QueryPatientInfo[]>([]);
  const [selValue, setSelValue] = useState<null | string[]>();

  const m200Api = getM200ApiInstance();
  // 开始日期限制
  // tips: 时间判断一定要精确到开始时间/结束时间
  const disabledStartDate = useCallback(
    (current: dayjs.Dayjs) => {
      const [max, min] = timeTerm(type);
      let startDate = dayjs().subtract(max, 'days').endOf('days');
      let endDate = dayjs().endOf('days');

      if (endTimeState) {
        const mindDate = endTimeState.subtract(min - 1, 'days');
        startDate = mindDate > startDate ? mindDate : startDate;
        endDate = endTimeState.startOf('days');
      }

      return current < startDate || current > endDate;
    },
    [endTimeState, type]
  );
  const disabledEndDate = useCallback(
    (current: dayjs.Dayjs) => {
      const [max, min] = timeTerm(type);
      let startDate = dayjs().subtract(max, 'days').endOf('days');
      let endDate = dayjs().endOf('days');

      if (startTimeState) {
        const minDate = startTimeState.add(min - 1, 'days');
        startDate = startTimeState;
        endDate = minDate > endDate ? endDate : minDate;
      }

      return current < startDate || current > endDate;
    },
    [startTimeState, type]
  );

  const setStart = useCallback(
    (date: any) => {
      setStartTimeState(date);
      if (!endTimeState) {
        setDisabledExport(true);
      } else {
        setDisabledExport(false);
      }
    },
    [startTimeState, endTimeState]
  );
  const setEnd = useCallback(
    (date: any) => {
      setEndTimeState(date);
      if (!startTimeState) {
        setDisabledExport(true);
      } else {
        setDisabledExport(false);
      }
    },
    [startTimeState, endTimeState]
  );

  const renderCenter = (key: SelectTypeEnmu) => {
    if (!(key & SelectTypeEnmu.log)) return <></>;

    return (
      <div className={styles.checkRange}>
        <div className={styles.dateRange}>
          <NGDatePicker
            disabledDate={disabledStartDate}
            value={startTimeState}
            locale={locale}
            showToday={false}
            onChange={date => {
              setStart(date);
            }}
            placeholder={intl.formatMessage({ id: '开始时间' })}
          />
          <span className={styles.separator}>——</span>
          <NGDatePicker
            disabledDate={disabledEndDate}
            value={endTimeState}
            locale={locale}
            showToday={false}
            onChange={date => {
              setEnd(date);
            }}
            placeholder={intl.formatMessage({ id: '结束时间' })}
          />
        </div>
        {key === SelectTypeEnmu.log && (
          <NgSelect
            className={styles.selectedPatient}
            style={{ width: '100%' }}
            disabled={disabledExport}
            mode="multiple"
            options={selectList}
            placeholder="所有患者"
            maxTagCount="responsive"
            onChange={onSelectedChange}
            optionFilterProp="label"
            value={selValue}
          />
        )}
      </div>
    );
  };

  const handleExportLicense = async (v: string) => {
    setPath(v);
    setModalVisible(false);
    // 接下来调接口
    setProcessModelVisible(true);
  };
  const changeType = (e: any) => {
    const key = e.target.value;
    setType(key);
    if (key & SelectTypeEnmu.log) {
      setStartTimeState(null);
      setEndTimeState(null);
      setSelValue(null);
      setDisabledExport(true);
    } else {
      setDisabledExport(false);
    }
  };
  const getModelStartTime = useCallback(() => {
    if (!(type & SelectTypeEnmu.log)) return dayjs();

    return startTimeState;
  }, [startTimeState, type]);
  const getModelEndTime = useCallback(() => {
    if (!(type & SelectTypeEnmu.log)) return dayjs();

    return endTimeState;
  }, [endTimeState, type]);
  const noFileMessage = () => {
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    message.error(intl.formatMessage({ id: '未检测到移动设备' }));
  };
  const initData = () => {
    setStartTimeState(null);
    setEndTimeState(null);
    setType(SelectTypeEnmu.text);
    setDisabledExport(false);
    setSelValue(null);
  };
  const exportOver = (closeType = false) => {
    setProcessModelVisible(false);
    initData();
    props.onClose?.(closeType);
  };

  /**
   * 选择内容发生改变的回调
   */
  const onSelectedChange = (value: any) => {
    setSelValue(value);
  };

  useAsyncEffect(async () => {
    if (type !== SelectTypeEnmu.log) return;
    if (!startTimeState || !endTimeState) return;

    try {
      const data = await m200Api.getSubjectList(startTimeState.startOf('days').valueOf(), endTimeState.endOf('days').valueOf());
      setSelectList(data);

      setDisabledExport(data.length === 0);
      setSelValue(null);
    } catch (error) {
      setDisabledExport(true);
      setSelectList([]);
    }
  }, [startTimeState, endTimeState, type]);

  useEffect(() => {
    setModalVisible(!!props.modalVisible);
  }, [props.modalVisible]);

  return (
    <>
      {!props.notShowContent && (
        <div className={styles.container}>
          <div className={styles.head}>
            <div className={styles.label}>{intl.formatMessage({ id: '类型' })}:</div>
            <div className={styles.value}>
              <NgRadio.Group value={type} onChange={changeType} optionType={'button'}>
                <NgRadio value={SelectTypeEnmu.log}>{intl.formatMessage({ id: '精度记录' })}</NgRadio>
                <NgRadio value={SelectTypeEnmu.text}>{intl.formatMessage({ id: '文本' })}</NgRadio>
                {product.isNav && <NgRadio value={SelectTypeEnmu.video}>{intl.formatMessage({ id: '文本及视频' })}</NgRadio>}
              </NgRadio.Group>
            </div>
          </div>
          <div className={styles.center}>{renderCenter(type)}</div>
          <div className={styles.footer}>
            <NgButton
              className={styles.export}
              onClick={() => {
                setModalVisible(true);
                props.setTreatmentPopOverOpen(false);
              }}
              disabled={disabledExport}
            >
              导出
            </NgButton>
            <NgButtonText
              className={styles.cancel}
              onClick={() => {
                props.setTreatmentPopOverOpen(false);
                initData();
              }}
            >
              取消
            </NgButtonText>
          </div>
        </div>
      )}
      <NgSelectFileModal
        isLicense
        maskClosable={false}
        filepath={deviceType === DeviceType.mac ? '/' : osInfo?.filePath!}
        open={modalVisible}
        onCancel={() => {
          if (props.setModalVisible) props.setModalVisible(false);
          setModalVisible(false);
        }}
        width={770}
        uploadType={UploadType.download}
        handleError={noFileMessage}
        okText={intl.formatMessage({ id: '存储' })}
        onOk={async files => {
          await handleExportLicense(files[0].path);
        }}
      />
      {processModelVisible && (
        <ProcessModal
          visible={processModelVisible}
          type={type}
          endTime={getModelEndTime()}
          allTidString={encodeURIComponent(
            selectList
              .filter(v => (selValue ? selValue.some(val => val === v.value) : true))
              .flatMap(item => item.uuids)
              .join(',')
          )}
          startTime={getModelStartTime()}
          path={path}
          onClose={exportOver}
        />
      )}
    </>
  );
};
export default ExportTreatmentReport;
