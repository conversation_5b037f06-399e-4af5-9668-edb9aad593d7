import { ColumnType } from 'antd/lib/table';
import { getPlanLogEventTypeMap, PlanLogModel } from '@/common/types';
import React from 'react';
import moment from 'moment';
import { IntlShape } from 'react-intl';

export const logColumns: (intl: IntlShape) => ColumnType<PlanLogModel>[] = (intl: IntlShape) => {
  return [
    {
      title: intl.formatMessage({ id: '操作者' }),
      dataIndex: 'created_user_nickname',
      key: 'created_user_nickname',
    },
    {
      title: intl.formatMessage({ id: '患者ID' }),
      dataIndex: 'subject_code',
      key: 'subject_code',
    },
    {
      title: intl.formatMessage({ id: '患者名称' }),
      dataIndex: 'subject_name',
      key: 'subject_name',
    },
    {
      title: intl.formatMessage({ id: '事件描述' }),
      dataIndex: 'created_user_name',
      key: 'created_user_name',
      render: (text, record) => {
        const PlanLogEventTypeMap = getPlanLogEventTypeMap(intl);

        return <>{PlanLogEventTypeMap[record.type!]}</>;
      },
    },
    {
      title: intl.formatMessage({ id: '时间' }),
      dataIndex: 'created_at',
      key: 'created_at',
      render: time => {
        return <>{moment(time).format('YYYY-MM-DD HH:mm:ss')}</>;
      },
    },
  ];
};
