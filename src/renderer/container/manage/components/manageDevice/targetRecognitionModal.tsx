import React from 'react';
import NgModal from '../../../../uiComponent/NgModal';
import { ModalProps } from 'antd';
import styles from './index.module.less';
import NgButton from '../../../../uiComponent/NgButton';

// type Props = IComponentRef

export const TargetRecognitionModal = (props: ModalProps) => {
  return <NgModal
    title="确认关闭目标识别辅助模式？"
    cancelButtonProps={{ style: { display: 'none' } }}
    footer={[
      <NgButton key='ok' className={styles.ok_button} onClick={props.onOk}>确认关闭</NgButton>,
    ]}
    {...props}
    className={styles.target_modal}
  >
    <p className={styles.title}>
      关闭后，以下功能无法使用
    </p>
    <div className={styles.ul}>
      <p className={styles.li}>控制患者、线圈在有效视觉范围内进行刺激治疗</p>
      <p className={styles.li}>追踪靶点实时参数阶梯数值展示</p>
      <p className={styles.li}>更加严格的靶点追踪范围限制</p>
    </div>
  </NgModal>;
};
