export const thresholdList = [
  {
    label: '靶点距离阈值(mm)',
    value: 'absolute',
    blockSuffix: '(0~100)',
    max: 100,
    min: 0,
  },
  {
    label: '平移距离阈值(mm)',
    value: 'translate',
    blockSuffix: '(0~100)',
    max: 100,
    min: 0,
  },
  {
    label: '夹角阈值(°)',
    value: 'angle',
    blockSuffix: '(0~100)',
    max: 100,
    min: 0,
  },
  {
    label: '旋转角阈值(°)',
    value: 'rotate',
    blockSuffix: '(0~100)',
    max: 100,
    min: 0,
  },
];

export const visibleInfo = [
  {
    name: 'accuracy',
    label: '准确率',
    items: [
      {
        label: '靶心瞄准',
        value: 'all',
      },
      {
        label: '遮挡率',
        value: 'mask',
      },
      {
        label: '靶点距离',
        value: 'absolute',
      },
      {
        label: '平移距离',
        value: 'translate',
      },
      {
        label: '夹角',
        value: 'angle',
      },
      // {
      //   label: '旋转角',
      //   value: 'rotate',
      // },
    ],
  },
  {
    name: 'threshold',
    label: '阈值',
    items: [
      {
        label: '靶点距离',
        value: 'absolute',
      },
      {
        label: '平移距离',
        value: 'translate',
      },
      {
        label: '夹角',
        value: 'angle',
      },
      // {
      //   label: '旋转角',
      //   value: 'rotate',
      // },
    ],
  },
  {
    name: 'average',
    label: '平均值',
    items: [
      {
        label: '靶点距离',
        value: 'absolute',
      },
      {
        label: '平移距离',
        value: 'translate',
      },
      {
        label: '夹角',
        value: 'angle',
      },
      // {
      //   label: '旋转角',
      //   value: 'rotate',
      // },
    ],
  },
];

export const instructionInfo = [
  {
    label: '靶点距离数值',
    value: 'absolute',
  },
  {},
  {
    label: '平移距离数值',
    value: 'translate',
  },
  {
    label: '平移指示',
    value: 'translateTip',
  },
  {
    label: '夹角数值',
    value: 'angle',
  },
  {
    label: '夹角指示',
    value: 'angleTip',
  },
  {
    label: '旋转角数值',
    value: 'rotate',
  },
];

export type ViewControlType<T> = {
  /** 靶点距离相关 */
  absolute: T;
  /** 平移距离相关 */
  translate: T;
  /** 夹角相关 */
  angle: T;
  /** 旋转角相关 */
  rotate: T;
};

export type VisibleInfoType = {
  /** 是否显示精度记录 */
  isControl: boolean;
  /** 阈值 */
  calInfo: ViewControlType<number>;
  /** 是否显示准确率 */
  accuracy: {
    /** 靶心瞄准 */
    all: boolean;
    /** 遮挡 */
    mask: boolean;
  } & ViewControlType<boolean>;
  /** 是否显示阈值 */
  threshold: ViewControlType<boolean>;
  /** 是否显示平均值 */
  average: ViewControlType<boolean>;
  /** 误差 */
  timeSlot: number;
};

export const initVisibleInfo: VisibleInfoType = {
  isControl: true,
  calInfo: {
    absolute: 5,
    translate: 3,
    angle: 3,
    rotate: 3,
  },
  accuracy: {
    all: true,
    mask: true,
    absolute: true,
    translate: true,
    angle: true,
    rotate: false,
  },
  threshold: {
    absolute: true,
    translate: true,
    angle: true,
    rotate: false,
  },
  average: {
    absolute: true,
    translate: true,
    angle: true,
    rotate: false,
  },
  timeSlot: 2,
};

export type InstructionInfo = {
  /** 人物标准位置显示 */
  isShowStandardPosition: boolean;
  instruction: {
    /** 平移指示  */
    translateTip: boolean;
    /** 夹角指示 */
    angleTip: boolean;
  } & ViewControlType<boolean>;
};

export const initInstructionInfo: InstructionInfo = {
  isShowStandardPosition: true,
  instruction: {
    absolute: true,
    translate: true,
    angle: true,
    rotate: true,
    translateTip: true,
    angleTip: true,
  },
};

export type TechsupportInfo = {
  mainControl: boolean;
};

export const initTechsupportInfo = {
  mainControl: false,
};

type CommonObj = {
  [props: string]: CommonObj | any;
};

export const enFlatObj = (obj: CommonObj) => {
  let res: CommonObj = {};

  for (let key in obj) {
    if (/\./.test(key)) {
      const splitList = key.split('.');
      res[splitList[0]] = Object.assign(res[splitList[0]] || {}, enFlatObj({ [splitList.slice(1).join('.')]: obj[key] }));
    } else {
      res[key] = obj[key];
    }
  }

  return res;
};

export const flatObj = (obj: CommonObj, pre = '') => {
  let res: CommonObj = {};

  for (let key in obj) {
    if (obj[key]?.toString() === '[object Object]') {
      res = { ...res, ...flatObj(obj[key], pre ? `${pre}.${key}` : key) };
    } else {
      res[pre ? `${pre}.${key}` : key] = obj[key];
    }
  }

  return res;
};
