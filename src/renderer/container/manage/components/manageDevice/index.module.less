@import '@/renderer/static/style/baseColor.module.less';

.container {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  padding-top: 8px;
  position: relative;
  flex-grow: 1;

.error_message_alert {
  position: absolute;
  word-break: keep-all;
  top: -30px;
  transform: translateX(-50%);

  :global {
    .ant-alert {
      padding: 12px !important;
    }
    .ant-alert-icon {
      margin-top: 4px;
      margin-right: 12px;
    }
    .ant-alert-message {
      font-size: 14px;
      line-height: 24px !important;
    }
    .ant-alert-close-icon {
      margin-top: 3px;
      margin-left: 18px;
    }
  }
}

  .deviceSet {
    width: 655px;
    font-size: 16px;
    color: @colorA9;
    margin-bottom: 80px;

    &:nth-child(4) {
      margin-bottom: 0;
    }

    & p {
      font-size: 16px;
      margin-bottom: 10px;
      color: @colorA12;
    }
  }

  .btnContainer {
    margin-top: 105px;
    margin-bottom: 0;
  }

  .flex {
    display: flex;
  }

  .mt30 {
    margin-top: 30px;
  }

  .clearMargin {
    margin: 0;
  }

  .gray {
    color: @colorA9  !important;
    margin: 0;
  }

  .switch {
    display: flex;

    & div label {
      color: @colorA9  !important;
      margin-right: 10px;
    }

    :global {
      .ant-form-item {
        margin-bottom: 0px;

        &:first-child {
          margin-right: 80px;
        }
      }
    }
  }

  .page_container {
    display: flex;
    justify-content: center;

    .view_content {
      margin: 0 10px 10px;
      display: flex;
      flex-direction: column;
    }

    .title {
      margin: 0;
      margin-bottom: 16px;
      font-size: 16px;
      height: 23px;
    }

    .contaienr {
      background-color: @colorA3;
      border-radius: 8px;
      padding: 20px;
    }

    .footer {
      display: flex;
      margin-top: 10px;
      justify-content: end;
    }

    .form_container {
      width: 278px;

      .form_title {
        color: @colorA12;
        margin: 0 0 12px;
      }

      .accuracy_tips {
        padding: 14px 14px 12px;
        background-color: @colorA5;
        border-radius: 6px;
        color: @colorA11;
        margin-bottom: 12px;
        display: flex;
        gap: 20px;

        span {
          line-height: 21px;
          cursor: url('@/renderer/static/svg/defaultMouse.cur'), pointer !important;
        }
      }

      .item_title {
        color: @colorA9;
        margin: 0 0 10px;
        height: 20px;
      }

      :global {
        .ant-form-item {
          margin-bottom: 22px;

          .ant-form-item-label {

            label {
              color: @colorA9;
              line-height: 21px;
            }
          }

          .ant-form-item-row {
            flex-direction: row;
          }

          .ant-form-item-extra {
            color: @colorA9;
            position: absolute;
            top: 32px;
            right: 0;
            font-size: 12px;
          }
        }
      }

      .form_item_switch {
        margin-bottom: 12px;
        margin-top: 20px;

        label {
          color: @colorA12  !important;
          height: 22px;

          &::after {
            content: '';
          }
        }

        :global {
          .ant-row {
            justify-content: space-between;

            .ant-form-item-control-input {
              min-height: auto;
            }

            .ant-form-item-control {
              flex-grow: 0;
              min-width: auto;
            }
          }
        }
      }

      .view_tips_item {
        width: 45%;
        line-height: 20px;
        height: 22px;
        margin-bottom: 10px;

        :global {
          .ant-form-item {
            min-height: auto;
          }

          .ant-form-item-control-input {
            min-height: auto;
          }
        }
      }
    }

    .log_form_container {
      .form_item_switch {
        margin-top: 0px;
      }
    }
  }
}

.target_modal {
  p {
    margin: 0;
    color: @colorA11
  }

  .title {
    margin-bottom: 10px;
  }

  .ul {
    .li {

      &::before {
        content: '·';
        margin-right: 10px;
      }
    }
  }

  .ok_button {
    display: inline-block;
  }
}