// import NgSwitch from '@/renderer/uiComponent/NgSwitch';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import styles from './index.module.less';
import NgDarkButton from '@/renderer/uiComponent/NgDarkButton';
import { getM200ApiInstance } from '@/common/api/ngApiAgent';
import { PlanLogModel, PlanLogQueryModel } from '@/common/types';
import { useAsyncEffect } from 'ahooks';
import { useLicenseAtom } from '@/renderer/recoil/license';
import { useRecoilState, useRecoilValue } from 'recoil';
import moment from 'moment';
import { DeviceType } from '@/renderer/container/previewPlan';
import { UserSessionProps, withUserSession } from '@/renderer/hocComponent/withUserSession';
import classnames from 'classnames';
import NgMessage from '@/renderer/uiComponent/NgMessage';
// import {NgForm, NgFormItem} from '@/renderer/uiComponent/NgForm';
// eslint-disable-next-line import/no-internal-modules,import/no-extraneous-dependencies
// import {FieldData} from 'rc-field-form/lib/interface';
import { osUserInfo } from '@/renderer/recoil/osUserInfo';
import { useIntl } from 'react-intl';
import NgPopover from '@/renderer/uiComponent/NgPopover';
import ExportTreatmentReport from '@/renderer/container/manage/components/exportTreatmentReport';
import LogsModal from '@/renderer/container/manage/components/logsModal';
import { Col, Form, FormInstance, Row } from 'antd';
import { enFlatObj, flatObj, instructionInfo, thresholdList, visibleInfo } from './config';
import { NgInputNumber } from '../../../../uiComponent/NgInputNumber';
import NgSwitch from '../../../../uiComponent/NgSwitch';
import { NgCheckbox } from '../../../../uiComponent/NgCheckbox';
import { NgIcon } from '../../../../uiComponent/NgIcon';
import { InfoMessage } from '../../../../uiComponent/SvgGather';
import NgButton from '../../../../uiComponent/NgButton';
import { useNavigate } from 'react-router-dom';
import NgAlert from '../../../../uiComponent/NgAlert';
import { ReactComponent as MessageError } from '@/renderer/static/svg/errorMessage.svg';

type Props = {
  treatmentPopOverOpen: boolean;
  setTreatmentPopOverOpen: React.Dispatch<React.SetStateAction<boolean>>;
};
export const getLogsData = async (params: PlanLogQueryModel) => {
  const m200Api = getM200ApiInstance();
  let res = await m200Api.getLogs(params);

  return res;
};
const ManageDevice = (props: Props & UserSessionProps) => {
  const { treatmentPopOverOpen, setTreatmentPopOverOpen } = props;
  const { contextHolder, error: messageError, success: MsgSuccess } = NgMessage.useMessage();
  const [isShowControl, setIsShowControl] = useState(false);
  const [showErrorMessage, setShowErrorMessage] = useState(false);
  const deviceType: DeviceType = /macintosh|mac os x/i.test(navigator.userAgent) ? 2 : 1;
  const intl = useIntl();
  // const [form] = Form.useForm();
  const osInfo = useRecoilValue(osUserInfo);
  const [isShowMessage, setIsShowMessage] = useState(false);
  const m200Api = getM200ApiInstance();
  const navigate = useNavigate();

  const visibleRef = useRef<FormInstance>(null);
  const instructionRef = useRef<FormInstance>(null);
  const isUser = useMemo(() => {
    if (props.userSession?.role_id && +props.userSession.role_id === 1003) {
      return true;
    }

    return false;
  }, [props.userSession]);
  const [openModal, setOpenModal] = useState(false);
  const [license] = useRecoilState(useLicenseAtom);
  // useAsyncEffect(async ()=>{
  //   let res = await window.tmsAPI.triggerQuery();
  //   form.setFieldsValue({
  //     input:Boolean(res.data.in),
  //     out:Boolean(res.data.out),
  //   });
  // },[]);
  const paginationParams = useRef<PlanLogQueryModel & { total: number }>({
    page_num: 1,
    page_size: 10,
    total: 0,
  });
  const [data, setData] = useState<PlanLogModel[]>([]);
  useAsyncEffect(async () => {
    let res = await getLogsData(paginationParams.current);
    setData(res.records);
    paginationParams.current = {
      ...paginationParams.current,
      total: res.total || 0,
    };
  }, []);

  const getRules = useCallback((max: number, min: number) => {
    return [
      {
        validator: async (_: any, value: number | null) => {
          if (!value && value !== 0) { // 为空或小数
            return Promise.reject('不可为空');
          }

          if (value > max || value < min || (value | 0) !== value) { // 极致&&小数
            return Promise.reject('不符合限制');
          }

          return Promise.resolve();
        },
      },
    ];
  }, []);

  const onOpenModalReportInfo = async () => {
    try {
      const list = await window.fileAPI.getFolderInfo(deviceType === DeviceType.mac ? '/' : osInfo.filePath!);
      if (list.length === 0) {
        throw Error('');
      }
    } catch (error) {
      if (isShowMessage) return;
      setIsShowMessage(true);
      // eslint-disable-next-line @typescript-eslint/no-floating-promises
      messageError({
        content: intl.formatMessage({ id: '未检测到移动设备' }),
        onClose: () => setIsShowMessage(false),
      });

      return;
    }
    setTreatmentPopOverOpen(true);
  };

  const handleSubmit = async () => {
    await visibleRef.current?.validateFields();
    await instructionRef.current?.validateFields();
    const visibleVal = visibleRef.current?.getFieldsValue();
    const instruction = instructionRef.current?.getFieldsValue();
    try {
      await m200Api.setConfigByLog([{
        group_name: 'control',
        name: 'visibleInfo',
        value: JSON.stringify(enFlatObj(visibleVal)),
      }, {
        group_name: 'control',
        name: 'instructionInfo',
        value: JSON.stringify(enFlatObj(instruction)),
      }]);
      // eslint-disable-next-line @typescript-eslint/no-floating-promises
      MsgSuccess({
        content: '操作成功',
      });
      const timer = setTimeout(() => {
        navigate('/home');
        clearTimeout(timer);
      }, 500);
    } catch (error) {
      // eslint-disable-next-line @typescript-eslint/no-floating-promises
      messageError({
        content: intl.formatMessage({ id: '操作失败' }),
      });
    }
  };

  const initFromInfo = async () => {
    const [
      techsupportInfo,
      visiblePart,
      instructionPart] = await m200Api.getControlConfig();

    setIsShowControl(techsupportInfo.mainControl);
    setTimeout(() => {
      visibleRef.current?.setFieldsValue(flatObj(visiblePart));
      instructionRef.current?.setFieldsValue(flatObj(instructionPart));
    }, 0);
  };

  const handleClose = (isError: boolean) => {
    if (isError) {
      setShowErrorMessage(isError);
    }
  };

  useEffect(() => {
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    initFromInfo();
  }, []);

  return (
    <div className={styles.container}>
      {contextHolder}
      <LogsModal
        openModal={openModal}
        setOpenModal={setOpenModal}
        data={data}
        total={paginationParams.current.total}
        onPaginationChange={async (page_num: number, page_size: number) => {
          let res = await getLogsData({
            page_num,
            page_size,
          });
          paginationParams.current = {
            ...paginationParams.current,
            total: res.total || 0,
          };
          setData(res.records);
        }}
      />
      <div className={styles.page_container}>
        <div className={styles.view_content}>
          <p className={styles.title}>
            {intl.formatMessage({ id: '认证信息' })}
          </p>
          <div className={styles.contaienr}>
            <div className={styles.content}>
              <p className={styles.gray}>
                {intl.formatMessage({ id: '到期时间' })}：{license.simple_end ? moment(license.simple_end).format('YYYY-MM-DD') : '--'}
              </p>
            </div>
            <div className={styles.footer}>
              {!isUser && (
                <NgDarkButton
                  style={{ marginRight: 40 }}
                  onClick={() => {
                    setOpenModal(true);
                    setTreatmentPopOverOpen(false);
                  }}
                >
                  {intl.formatMessage({ id: '日志记录' })}
                </NgDarkButton>
              )}
              <NgPopover
                overlayClassName={styles.treatmentRecord}
                open={treatmentPopOverOpen}
                placement={'bottom'}
                content={<ExportTreatmentReport onClose={handleClose} setTreatmentPopOverOpen={setTreatmentPopOverOpen} />}
              >
                <div>
                  <NgDarkButton onClick={onOpenModalReportInfo}>{intl.formatMessage({ id: '治疗记录' })}</NgDarkButton>
                </div>
              </NgPopover>
            </div>
          </div>
        </div>

        {isShowControl && <div className={styles.view_content}>
          <p className={styles.title}>配置</p>
          <div className={styles.contaienr}>
            <div className={styles.content}>
              <Form labelAlign='left' className={styles.form_container} ref={visibleRef}>
                <p className={styles.form_title}>阈值设置</p>
                {thresholdList.map(v =>
                  <Form.Item
                    name={`calInfo.${v.value}`}
                    key={v.value}
                    label={<>{ v.label}</>}
                    rules={getRules(v.max, v.min)}
                    labelCol={{ span: 13 }}
                    extra={`${v.min}-${v.max}`}
                  >
                    <NgInputNumber />
                  </Form.Item>
                )}
                <p className={styles.form_title}>精度</p>
                <div className={styles.accuracy_tips}>
                  <NgIcon fontSize={14} iconSvg={InfoMessage} />
                  <span>患者和线圈无法识别或不满足阈值设置的情况下，在误差时限2秒后会停止刺激</span>
                </div>
                <Form.Item
                  label={<>误差时限(秒)</>}
                  name='timeSlot'
                  extra={'2-100'}
                  labelCol={{ span: 13 }}
                  rules={getRules(100, 2)}
                >
                  <NgInputNumber />
                </Form.Item>
                <Form.Item
                  label={<>显示精度记录</>}
                  className={styles.form_item_switch} />
                {visibleInfo.map(v => <div key={v.name}>
                  <p className={styles.item_title}>{v.label}</p>
                  <Row justify='space-between' wrap className={styles.check_container}>
                    {v.items.map(val => <Form.Item
                      name={`${v.name}.${val.value}`}
                      key={val.value}
                      className={styles.view_tips_item}
                      labelAlign="right"
                      labelCol={{ span: 8 }}
                      valuePropName='checked'
                    >
                      <NgCheckbox>{ val.label }</NgCheckbox>
                    </Form.Item>)}
                  </Row>
                </div>)}
              </Form>
            </div>
          </div>
        </div>}
        {isShowControl && <div className={styles.view_content}>
          <p className={styles.title} />
          <div style={{flexGrow: 1}}>
            <div className={styles.contaienr}>
              <Form labelAlign='left' className={classnames(styles.form_container, styles.log_form_container)} ref={instructionRef}>
                <Form.Item label={<>人物标准位置显示</>} name='isShowStandardPosition' valuePropName='checked' className={styles.form_item_switch}>
                  <NgSwitch size="small"  />
                </Form.Item>
                <p className={styles.form_title}>文本及指示选项</p>
                <Row wrap justify='space-between' className={styles.check_container}>
                  {instructionInfo.map(v=>
                    v.value? <Form.Item
                      name={`instruction.${v.value}`}
                      key={v.value}
                      className={styles.view_tips_item}
                      labelAlign="right"
                      labelCol={{ span: 8 }}
                      valuePropName='checked'
                    >
                      <NgCheckbox>{ v.label }</NgCheckbox>
                    </Form.Item> : <Col className={styles.view_tips_item} key={1} />)}
                </Row>
              </Form>
            </div>
          </div>
          <div className={styles.footer} style={{textAlign: 'right'}}>
            <NgButton onClick={handleSubmit}>{intl.formatMessage({ id: '确认' })}</NgButton>
          </div>
        </div>}
      </div>
      {showErrorMessage && <NgAlert
        className={styles.error_message_alert}
        icon={<MessageError />}
        showIcon
        message="操作失败"
        type="error"
        closable
      />}
    </div>
  );
};
export default withUserSession(ManageDevice);
