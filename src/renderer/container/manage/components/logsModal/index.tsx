import React from 'react';
import NgModal from '@/renderer/uiComponent/NgModal';
import styles from './index.module.less';
import NgTable from '@/renderer/uiComponent/NgTable';
import { logColumns } from '@/renderer/container/manage/components/manageDevice/tableConfig';
import { useIntl } from 'react-intl';
import { PlanLogModel } from '@/common/types';
import { ConfigProvider } from 'antd';
import NgEmpty from '@/renderer/uiComponent/NgEmpty';

type Props = {
  openModal: boolean;
  setOpenModal: React.Dispatch<React.SetStateAction<boolean>>;
  data: PlanLogModel[];
  total: number;
  onPaginationChange: any;
};
const LogsModal = (props: Props) => {
  const intl = useIntl();
  const { openModal, setOpenModal, data, total, onPaginationChange } = props;

  return (
    <NgModal
      maskClosable={false}
      footer={<></>}
      open={openModal}
      onCancel={() => setOpenModal(false)}
      width={910}
      wrapClassName={styles.logModal}
      title={intl.formatMessage({ id: '日志记录' })}
    >
      <ConfigProvider
        renderEmpty={() => {
          return <NgEmpty customDesc={'暂无数据'} emptyType={'noData'} />;
        }}
      >
        <NgTable
          className={styles.logTable}
          scroll={{ y: 400 }}
          rowKey={record => record.id!}
          columns={logColumns(intl).map(v => ({...v, render: v.render || ((text) => text || '--')}))}
          data={data}
          pagination={{
            total: total,
            onChange: (page_num, page_size) => onPaginationChange(page_num, page_size),
          }}
        />
      </ConfigProvider>
    </NgModal>
  );
};
export default LogsModal;
