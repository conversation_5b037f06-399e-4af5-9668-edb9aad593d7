@import '@/renderer/static/style/baseColor.module.less';
.logModal {
  :global {
    .ant-table-placeholder {
      height: 376px;
      & .ant-table-cell {
        margin-top: 30px;
      }
    }
    // .ant-table-placeholder:hover > td {
    //   background-color: transparent !important;
    // }
    .ant-table-empty {
      .ant-table-body {
        overflow: hidden;
      }
    }
    .ant-table-container {
      user-select: none;
    }
    .ant-modal-content .ant-modal-footer {
      margin-top: 50px !important;
    }
    .ant-table-wrapper .ant-table {
      background: transparent;
    }
    .ant-table-wrapper .ant-table-thead > tr > th,
    .ant-table-thead .ant-table-cell-scrollbar {
      background: @colorA5 !important;
    }
    .ant-table-thead tr .ant-table-cell-scrollbar {
      box-shadow: @colorA5 0 1px 0 1px !important;
      border-bottom-color: @colorA5 !important;
      background: @colorA5 !important;
      transition: background 0.2s ease;
      margin: 0;
      padding: 0;
    }
    .ant-table:not(.ant-table-empty) .ant-table-body {
      &::-webkit-scrollbar {
        background: @colorA4_1 !important;
      }
      &::-webkit-scrollbar-thumb {
        background: @colorA3 !important;
      }
    }
    .ant-table-content .ant-table-thead {
      height: 40px !important;
      font-size: 14px !important;
    }
    .ant-table-cell {
      height: 40px !important;
      font-size: 14px !important;
    }

    .ant-table-row {
      background: @colorA4_1 !important;
    }

    .ant-table-content .ant-table-thead > tr > th {
      background: @colorA5 !important;
    }

    .ant-modal {
      width: 910px !important;
      //height: 577px;
      padding-bottom: 0;
      position: relative;
    }

    .ant-modal-content {
      height: 100%;
    }
    .ant-spin-container {
      height: 430px;
    }
    .ant-modal .ant-modal-header {
      margin-bottom: 20px;
    }
    .ant-pagination {
      position: absolute;
      bottom: -80px;
      right: 0;
      margin: 0 0 20px 0 !important;
    }
    .ant-table-wrapper .ant-table-tbody > tr.ant-table-row:hover > tr,
    .ant-table-wrapper .ant-table-tbody > tr.ant-table-row:hover > td {
      background-color: @colorA4_1 !important;
    }
    .ant-table-wrapper .ant-table-tbody > tr.ant-table-row > tr,
    .ant-table-wrapper .ant-table-tbody > tr.ant-table-row > td {
      background-color: @colorA4_1 !important;
    }
  }
}
.logTable {
  :global {
    .ant-pagination {
      position: absolute;
      bottom: -80px;
      right: 0;
      margin: 0 0 20px 0 !important;
    }
  }
}
