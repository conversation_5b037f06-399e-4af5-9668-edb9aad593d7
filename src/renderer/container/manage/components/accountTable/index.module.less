@import '@/renderer/static/style/baseColor.module.less';

.container {
  width: calc(100vw - 40px);
  height: calc(100vh - 156px);

  .iconContainer {
    &>span:first-child {
      margin-right: 32px;
    }
  }

  :global {
    .ant-table-placeholder {
      height: 60vh;

      & .ant-table-cell {
        margin-top: 30px;
      }
    }

    .ant-table-empty {
      .ant-table-body {
        &::-webkit-scrollbar {
          background: transparent !important;
        }

        &::-webkit-scrollbar-thumb {
          background: transparent !important;
        }
      }
    }
  }
}

.editForm {
  font-size: 14px;
  color: @colorA9;

  .account {
    margin-bottom: 24px;
  }

  .switch {
    :global {
      .ant-form-item-label {
        &:first-child {
          pointer-events: none;
        }

        padding: 0;
      }

      .ant-form-item-row {
        display: flex;
        flex-direction: row !important;
        align-items: center;
      }

      .ant-form-item-control {
        width: fit-content;
      }
    }
  }

  :global {
    & #disable {
      display: flex;
      align-items: center;
      font-size: 14px !important;
      color: @colorA9  !important;
    }

    .ant-form-item-label::after {
      color: @colorA9  !important;
    }

    .ant-form-item-row .ant-form-item-label label {
      font-size: 14px !important;
      color: @colorA9  !important;
    }
  }
}