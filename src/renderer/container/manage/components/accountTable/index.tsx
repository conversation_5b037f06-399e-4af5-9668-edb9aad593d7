import React, { useRef } from 'react';
import NgTable from '@/renderer/uiComponent/NgTable';
import styles from './index.module.less';
import { ColumnType } from 'antd/lib/table';
import moment from 'moment';
import { NgIcon } from '@/renderer/uiComponent/NgIcon';
import { EditDisable, EditUnderline, Eye, EyeSlash, Lock, ResetDisable } from '@/renderer/uiComponent/SvgGather';
import { UserSessionProps, withUserSession } from '@/renderer/hocComponent/withUserSession';
import { AccountStatus, EnumUserPageQueryModelRoleEnumList, UserModel } from '@/common/types';
import NgModal from '@/renderer/uiComponent/NgModal';
import { NgInput, NgInputPassword } from '@/renderer/uiComponent/NgInput';
import { NgForm, NgFormItem } from '@/renderer/uiComponent/NgForm';
import { getM200ApiInstance } from '@/common/api/ngApiAgent';
import { ConfigProvider, Form } from 'antd';
import NgSwitch from '@/renderer/uiComponent/NgSwitch';
import { validatePassword } from '@/renderer/component/toolBoxAndPower';
import { IntlShape, useIntl } from 'react-intl';
import { nameReg } from '../..';
import NgEmpty from '@/renderer/uiComponent/NgEmpty';
import { useDebounceFn } from 'ahooks';
import { debounceOption } from '../../../../utils';

type Props = {
  data: UserModel[];
  setData: React.Dispatch<React.SetStateAction<UserModel[]>>;
};
const getRoleMap = (intl: IntlShape) => {
  return {
    [EnumUserPageQueryModelRoleEnumList.TechSupport]: intl.formatMessage({ id: '技术支持' }),
    [EnumUserPageQueryModelRoleEnumList.Admin]: intl.formatMessage({ id: '管理员' }),
    [EnumUserPageQueryModelRoleEnumList.User]: intl.formatMessage({ id: '普通用户' }),
  };
};
const getColumns: (
  resetPWD: (record: UserModel) => any,
  editPWD: (record: UserModel) => any,
  userSession: UserSessionProps['userSession'],
  intl: IntlShape
) => ColumnType<UserModel>[] = (resetPWD, editPWD, userSession, intl) => {
  return [
    {
      width: 480,
      key: 'username',
      dataIndex: 'username',
      title: intl.formatMessage({ id: '账号' }),
    },
    {
      width: 480,
      key: 'nickname',
      dataIndex: 'nickname',
      title: intl.formatMessage({ id: '姓名' }),
    },
    {
      width: 150,
      key: 'role_id',
      dataIndex: 'role_id',
      title: intl.formatMessage({ id: '角色' }),
      render: text => {
        const roleMap = getRoleMap(intl);

        return <>{roleMap[`${text}`]}</>;
      },
    },
    {
      title: intl.formatMessage({ id: '创建时间' }),
      dataIndex: 'created_at',
      width: 300,
      key: 'created_at',
      render: (record: Date) => {
        return <>{moment(record).format('YYYY-MM-DD HH:mm:ss')}</>;
      },
      sorter: {
        compare: (a, b) => {
          // @ts-ignore
          return new Date(a.created_at) - new Date(b.created_at);
        },
      },
    },
    {
      key: 'action',
      dataIndex: 'action',
      title: intl.formatMessage({ id: '操作' }),
      render: (text, record) => {
        return (
          <div className={styles.iconContainer}>
            {
              // 管理员不能 修改和重置自己的信息
              userSession?.id && +userSession.id === record.id ? (
                <NgIcon
                  style={{
                    width: 32,
                    height: 26,
                  }}
                  disabled
                  fontSize={26}
                  iconSvg={ResetDisable}
                />
              ) : (
                <NgIcon
                  style={{
                    width: 32,
                    height: 26,
                  }}
                  tooltip={{
                    overlayClassName: styles.iconToolTip,
                    title: '重置密码',
                  }}
                  onClick={() => resetPWD(record)}
                  fontSize={26}
                  iconSvg={Lock}
                />
              )
            }
            {userSession?.id && +userSession.id === record.id ? (
              <NgIcon
                style={{
                  width: 32,
                  height: 26,
                }}
                disabled
                fontSize={26}
                iconSvg={EditDisable}
              />
            ) : (
              <NgIcon
                tooltip={{
                  overlayClassName: styles.iconToolTip,
                  title: '编辑',
                }}
                fontSize={22}
                onClick={() => editPWD(record)}
                iconSvg={EditUnderline}
              />
            )}
          </div>
        );
      },
    },
  ];
};
const AccountTable = (props: Props & UserSessionProps) => {
  const m200Api = getM200ApiInstance();
  const [form] = Form.useForm();
  const intl = useIntl();
  const [editForm] = Form.useForm();
  const showModalRef = useRef<boolean>(false);
  const handleValidate = async () => {
    const newPwd = form.getFieldsValue()?.password || '';

    return validatePassword(newPwd, intl);
  };

  /**
   * 添加防抖
   */
  const { run: debouncedResetPWD } = useDebounceFn((record: UserModel) => {
    resetPWD(record);
  }, debounceOption);

  const resetPWD = (record: UserModel) => {
    if (showModalRef.current === true) return;
    NgModal.open({
      maskClosable: false,
      destroyOnClose: true,
      title: intl.formatMessage({ id: '重置密码' }),
      content: (
        <NgForm form={form}>
          <NgFormItem
            validateTrigger={['onBlur']}
            name={'password'}
            rules={[
              {
                validator: handleValidate,
              },
            ]}
          >
            <NgInputPassword
              iconRender={visible => (visible ? <Eye /> : <EyeSlash />)}
              type={'password'}
              placeholder={intl.formatMessage({ id: '8-20位，必须包含大小写字母、数字组合' })}
            />
          </NgFormItem>
        </NgForm>
      ),
      onOk: async () => {
        await form.validateFields();
        await m200Api.resetPWD({
          new_password: form.getFieldsValue().password,
          user_id: record.id,
        });
        form.resetFields();
      },
      onCancel: () => {
        form.resetFields();
      },
    });
    revertShowModalStatus();
  };

  /**
   * 设置showModal的值，防止重复点击
   */
  const revertShowModalStatus = () => {
    showModalRef.current = true;
    setTimeout(() => {
      showModalRef.current = false;
    }, 1000);
  };

  const handleEditValidate = async (fields: any, value: string) => {
    return new Promise((resolve, reject) => {
      if (!value?.length) {
        reject(intl.formatMessage({ id: '姓名不可为空' }));
      } else if (!nameReg.test(value)) {
        reject('仅允许输入字母、数字、中文');
      }
      resolve('');
    });
  };
  const validateEditName = async (fields: any, value: string) => {
    return handleEditValidate(fields, value);
  };

  /**
   * 添加防抖
   */
  const { run: debouncedEditPWD } = useDebounceFn((record: UserModel) => {
    editPWD(record);
  }, debounceOption);

  const editPWD = (record: UserModel) => {
    if (showModalRef.current === true) return;

    editForm.setFieldValue('name', record.nickname);
    editForm.setFieldValue('switch', record.status === 2 ? true : false);
    const roleMap = getRoleMap(intl);
    NgModal.open({
      destroyOnClose: true,
      maskClosable: false,
      title: intl.formatMessage({ id: '编辑' }),
      content: (
        <NgForm className={styles.editForm} form={editForm} layout={'vertical'}>
          <div className={styles.account}>
            {intl.formatMessage({ id: '账号' })}：{record.username}
          </div>
          <NgFormItem
            validateTrigger={['onBlur']}
            rules={[
              {
                validator: validateEditName,
              },
            ]}
            name={'name'}
            label={intl.formatMessage({ id: '姓名' })}
          >
            <NgInput maxLength={30} />
          </NgFormItem>
          <NgFormItem className={styles.switch} name="switch" label={intl.formatMessage({ id: '禁用账号' })} valuePropName="checked">
            <NgSwitch />
          </NgFormItem>
          <div>
            {intl.formatMessage({ id: '角色' })}：{roleMap[`${record.role_id}`]}
          </div>
        </NgForm>
      ),
      onOk: async () => {
        await editForm.validateFields();
        let result = editForm.getFieldsValue();
        await m200Api.editUser({
          id: record.id,
          nickname: result.name,
          status: result.switch ? AccountStatus.The2 : AccountStatus.The1,
        });
        let res = await m200Api.getUserList({
          page_num: 1,
          page_size: 1000,
        });
        props.setData(res.records ?? []);
        editForm.resetFields();
      },
      onCancel: () => {
        editForm.resetFields();
      },
    });
    revertShowModalStatus();
  };

  const columns = getColumns(debouncedResetPWD, debouncedEditPWD, props.userSession, intl);

  return (
    <div className={styles.container}>
      <ConfigProvider
        renderEmpty={() => {
          return <NgEmpty emptyType={'noData'} customDesc={'暂无数据'} />;
        }}
      >
        <NgTable
          showSorterTooltip={{
            open: false,
          }}
          rowKey={record => record.id}
          columns={columns}
          data={props.data}
          pagination={{
            hideOnSinglePage: true,
          }}
        />
      </ConfigProvider>
    </div>
  );
};
export default withUserSession(AccountTable);
