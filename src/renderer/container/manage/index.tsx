import React, { useRef, useState } from 'react';
import { NgBreadcrumb } from '@/renderer/uiComponent/NgBreadCrumb';
import styles from './index.module.less';
import CameraAndCoil from '@/renderer/component/cameraAndCoil';
import ToolBoxAndPower, { validatePassword } from '@/renderer/component/toolBoxAndPower';
import { NgTabs } from '@/renderer/uiComponent/NgTabs';
import NgDarkButton from '@/renderer/uiComponent/NgDarkButton';
import ManageDevice from './components/manageDevice';
import AccountTable from '@/renderer/container/manage/components/accountTable';
import NgModal from '@/renderer/uiComponent/NgModal';
import NgButton from '@/renderer/uiComponent/NgButton';
import NgButtonText from '@/renderer/uiComponent/NgButtonText';
import { NgForm, NgFormItem } from '@/renderer/uiComponent/NgForm';
import { NgInput, NgInputPassword } from '@/renderer/uiComponent/NgInput';
import { Form } from 'antd';
import { getM200ApiInstance } from '@/common/api/ngApiAgent';
import { useAsyncEffect, useDebounceFn } from 'ahooks';
import { EnumUserPageQueryModelRoleEnumList, RoleEnumList, UserModel } from '@/common/types';
import { UserSessionProps, withUserSession } from '@/renderer/hocComponent/withUserSession';
import { Eye, EyeSlash } from '@/renderer/uiComponent/SvgGather';
import { IntlShape, useIntl } from 'react-intl';
import { useRecoilState } from 'recoil';
import { useLicenseAtom } from '@/renderer/recoil/license';
import TechSupportToolBox from '@/renderer/container/techsupport/components/techsupportToolBox';
import classNames from 'classnames';
import { debounceOption } from '../../utils';

type Props = {};

const accountReg = /^[a-zA-Z0-9]+$/;
export const nameReg = /^[\u4e00-\u9fa5a-zA-Z0-9]+$/;
const validateAccount = async (fields: any, value: string, intl: IntlShape) => {
  return new Promise((resolve, reject) => {
    const isAccount = fields.field === 'username';
    if (!value?.length) {
      let fieldName = isAccount ? intl.formatMessage({ id: '账号不可为空' }) : intl.formatMessage({ id: '姓名不可为空' });
      reject(fieldName);
    } else if (!accountReg.test(value) && isAccount) {
      reject(intl.formatMessage({ id: '仅允许输入字母、数字' }));
    } else if (!nameReg.test(value) && fields.field === 'nickname') {
      reject('仅允许输入字母、数字、中文');
    }
    resolve('');
  });
};
const Manage = (props: Props & UserSessionProps) => {
  const [form] = Form.useForm();
  const intl = useIntl();
  const [currentTab, setCurrentTab] = useState('1');
  const [accountData, setAccountData] = useState<UserModel[]>([]);
  const [isTechSupport] = useState(+(props.userSession?.role_id || 0) === EnumUserPageQueryModelRoleEnumList.TechSupport);
  const m200Api = getM200ApiInstance();
  const hideRef = useRef<() => void>();
  const [license] = useRecoilState(useLicenseAtom);
  // @ts-ignore
  const [treatmentPopOverOpen, setTreatmentPopOverOpen] = useState(false);

  const getAccountData = async () => {
    let res = await m200Api.getUserList({
      page_num: 1,
      page_size: 1000,
    });

    return res;
  };
  const handleAdd = async () => {
    try {
      await form.validateFields();
      await m200Api.addUser(form.getFieldsValue());
      hideRef.current?.();
      let res = await getAccountData();
      setAccountData(res.records ?? []);
      form.resetFields();
    } catch (e: any) {
      if (+e?.code === 30230) {
        form.setFields([
          {
            name: 'username',
            errors: [intl.formatMessage({ id: '账号已存在' })],
          },
        ]);
      }
    }
  };
  const tabItems = [
    {
      key: '1',
      label: intl.formatMessage({ id: '设备' }),
      children: <ManageDevice treatmentPopOverOpen={treatmentPopOverOpen} setTreatmentPopOverOpen={setTreatmentPopOverOpen} />,
    },
    {
      key: '2',
      label: intl.formatMessage({ id: '账号' }),
      children: <AccountTable data={accountData} setData={setAccountData} />,
    },
  ];
  const handValidateAccount = async (fields: any, value: string) => {
    return validateAccount(fields, value, intl);
  };
  useAsyncEffect(async () => {
    if (currentTab === '2' || isTechSupport) {
      let res = await getAccountData();
      setAccountData(res.records ?? []);
    }
  }, [currentTab]);
  const handleValidate = async () => {
    const newPwd = form.getFieldsValue()?.password || '';

    return validatePassword(newPwd, intl);
  };

  /**
   * 添加防抖
   */
  const { run: debouncedHandleCreateAccount } = useDebounceFn(() => {
    handleCreateAccount();
  }, debounceOption);

  const handleCreateAccount = () => {
    const { hide } = NgModal.open({
      maskClosable: false,
      onCancel: () => {
        form.resetFields();
      },
      title: intl.formatMessage({ id: '新建账号' }),
      content: (
        <div className={styles.accountForm}>
          <NgForm layout={'vertical'} form={form}>
            <NgFormItem
              validateTrigger={['onBlur']}
              rules={[
                {
                  validator: handValidateAccount,
                },
              ]}
              label={intl.formatMessage({ id: '账号' })}
              name={'username'}
            >
              <NgInput maxLength={30} placeholder={intl.formatMessage({ id: '字母、数字' })} />
            </NgFormItem>
            <NgFormItem
              validateTrigger={['onBlur']}
              rules={[
                {
                  validator: handValidateAccount,
                },
              ]}
              label={intl.formatMessage({ id: '姓名' })}
              name={'nickname'}
            >
              <NgInput maxLength={30} placeholder={intl.formatMessage({ id: '字母、数字、中文' })} />
            </NgFormItem>
            <NgFormItem
              label={intl.formatMessage({ id: '密码' })}
              name={'password'}
              validateTrigger={['onBlur']}
              rules={[
                {
                  validator: handleValidate,
                },
              ]}
            >
              <NgInputPassword
                iconRender={visible => (visible ? <Eye /> : <EyeSlash />)}
                placeholder={intl.formatMessage({ id: '8-20位，必须包含大小写字母、数字组合' })}
                type={'password'}
              />
            </NgFormItem>
            <div>
              {intl.formatMessage({ id: '角色' })}：
              {props.userSession?.role_id && +props.userSession?.role_id === EnumUserPageQueryModelRoleEnumList.TechSupport ? '管理员' : '普通用户'}
            </div>
          </NgForm>
        </div>
      ),
      footer: (
        <div className={styles.accountFooter}>
          <NgButtonText
            onClick={() => {
              hide();
              form.resetFields();
            }}
          >
            {intl.formatMessage({ id: '取消' })}
          </NgButtonText>
          <NgButton buttonMode={'popover'} onClick={handleAdd}>
            {intl.formatMessage({ id: '确定' })}
          </NgButton>
        </div>
      ),
    });
    hideRef.current = hide;
  };

  const renderContent = () => {
    if (props.userSession?.role_id && +props.userSession.role_id === EnumUserPageQueryModelRoleEnumList.User) {
      return <ManageDevice treatmentPopOverOpen={treatmentPopOverOpen} setTreatmentPopOverOpen={setTreatmentPopOverOpen} />;
    }
    if (isTechSupport) {
      return (
        <div style={{ marginTop: '48px' }}>
          <AccountTable data={accountData} setData={setAccountData} />
        </div>
      );
    }

    return (
      <NgTabs
        onChange={tab => {
          setTreatmentPopOverOpen(v => {
            setCurrentTab(tab);

            return false;
          });
        }}
        items={tabItems}
        type="card"
      />
    );
  };

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <CameraAndCoil />
        <div className={styles.header_side}>
          <NgBreadcrumb
            isGray={false}
            items={
              isTechSupport
                ? [
                    {
                      path: '/home',
                      breadcrumbName: '首页',
                    },
                    {
                      path: '',
                      breadcrumbName: '账号管理',
                    },
                  ]
                : [
                    {
                      path: '/home',
                      breadcrumbName: '首页',
                    },
                    {
                      path: '',
                      breadcrumbName: '管理',
                    },
                  ]
            }
          />
        </div>
        <div className={classNames(styles.header_side, styles.header_icons)}>
          {`${props.userSession?.role_id}` === RoleEnumList.TechSupport ? (
            <TechSupportToolBox />
          ) : (
            <ToolBoxAndPower
              callBackMap={{
                hideTreatmentRecord: setTreatmentPopOverOpen,
              }}
            />
          )}
        </div>
      </div>

      <div className={styles.table}>
        <div className={styles.tabs}>
          {renderContent()}
          {(+currentTab === 2 || isTechSupport) && (
            <div className={styles.createAccount}>
              <NgDarkButton disabled={license.hasLicenseError} onClick={debouncedHandleCreateAccount} style={{ width: 144 }}>
                {intl.formatMessage({ id: '新建账号' })}
              </NgDarkButton>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
export default withUserSession(Manage);
