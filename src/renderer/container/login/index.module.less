@import '../../static/style/base.module.less';
@import '../../static/style/baseColor.module.less';

.login {
  width: 100vw;
  height: 100vh;
  display: flex;
  background: url('../../static/images/loginBackground.png') no-repeat;
  background-size: cover;

  .left {
    width: 60vw;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    .pointImage {
      width: 910px;
      height: 584px;
      // background: url("../../static/images/loginLeftImage.png") no-repeat;
      background-size: contain;
    }
  }

  .right {
    width: 40vw;
    height: 100%;
    align-items: center;
    justify-content: space-between;
    display: flex;
    flex-direction: column;

    .header {
      width: 40vw;
      margin-right: 50px;
      margin-top: 30px;
      color: @colorA12;
      display: inline-flex;
      flex-direction: row-reverse;
      align-items: center;

      .language {
        margin-right: 32px;
        width: auto;
        display: inline-flex;
        font-size: 16px;
        flex-direction: row-reverse;

        .isActive {
          color: @colorC4;
        }

        .isNormal {
          color: @colorA12;
        }
      }
    }

    .rightContainer {
      width: 400px;
      flex: 1;
      display: inline-flex;
      align-items: center;

      .formContainer {
        display: flex;
        align-items: flex-start;
        justify-content: center;
        flex-direction: column;
        color: #ffffff;

        .loginTitle {
          width: 400px;
          font-weight: 400;
          display: inline-block;
          text-align: justify;
          text-justify: unset;
          text-align-last: justify;
          color: #ffffff;
          line-height: 1;
          margin-bottom: 48px;
        }

        .loginForm {
          width: 400px;
          color: #ffffff;

          .errorAlert {
            :global {
              .ant-alert {
                padding: 5px 14px;
              }
            }
          }

          .loginButton {
            width: 100%;
            button {
              height: 40px;
            }
          }
        }
      }
    }

    .version {
      width: 40vw;
      margin-right: 50px;
      margin-bottom: 30px;
      display: inline-flex;
      flex-direction: row-reverse;
      color: @colorA12;
      font-size: @font-size-lg;
      font-weight: 500;
    }
  }
}
