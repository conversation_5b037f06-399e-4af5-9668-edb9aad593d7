import React, { ReactElement, useCallback, useEffect, useRef, useState } from 'react';
import { FormattedMessage, injectIntl } from 'react-intl';
import { useRecoilState, useSetRecoilState } from 'recoil';
import { Spin, Typography, Input } from 'antd';
import Form, { Rule } from 'antd/es/form';
import { UserSessionProps, withUserSession } from '../../hocComponent/withUserSession';
import { IntlPropType } from '../../../common/types/propTypes';
import { intlState } from '../../recoil/intl';
import styles from './index.module.less';
import ngInputStyles from '@/renderer/uiComponent/NgInput/index.module.less';
import { ApiStatus, EnumUserPageQueryModelRoleEnumList } from '../../../common/types';
import { NgForm, NgFormItem } from '@/renderer/uiComponent/NgForm';
import NgButton from '@/renderer/uiComponent/NgButton';
import { NgInput } from '@/renderer/uiComponent/NgInput';
import { withRouter, RouterProps } from '../../hocComponent/withRouter';
import { AllowClear, Eye, EyeSlash } from '@/renderer/uiComponent/SvgGather';
import { NgLoading } from '@/renderer/uiComponent/NgLoading';
import { getAuthApiEndpoint, setM200ApiInstance } from '@/common/api/ngApiAgent';
import { axiosHttpClient } from '@/common/api/httpClient/axiosHttpClientImplMain';
import { ShutDown } from '@/renderer/component/shutdown';
import NgAlert from '@/renderer/uiComponent/NgAlert';
import { ReactComponent as Error } from '@/renderer/static/svg/errorMessage.svg';
import Icon from '@ant-design/icons';
import { useStoragePath } from '@/renderer/recoil/storagePath';
import { useMount } from 'ahooks';
import { initObserver } from '@/renderer/utils/elementClickEventLog';
import { sendRenderLog, sendHistoryLog } from '../../utils/renderLogger';
import { ErrorModel } from '../../component/systemErrorModel/errorModel';
import { initImageTms } from '../../utils/crashedAction';
import { defaultHomeFilter, useFilterParams } from '../../recoil/tableFilterParams';
import { FaultEnum, FaultStatusEnum } from '../../../common/systemFault/type';

let currentLanguage = 'zh-CN';
// FDA 放开
// const LanguageChange = () => {
//   const setIntlState = useSetRecoilState(modifyIntlState);
//   const [intlLocale] = useRecoilState(intlState);
//   const changeLanguage = async (locale: string) => {
//     setIntlState({ locale });
//     currentLanguage = locale;
//   };
//
//   return (
//     <div className={styles.language}>
//       <NgButtonText onClick={async () => changeLanguage('en-US')}
//         style={{color: intlLocale.locale === 'en-US' ? '#4EB7B9' : '#FFFFFF'}}>ENG</NgButtonText>
//         &nbsp;/&nbsp;
//       <NgButtonText onClick={async () => changeLanguage('zh-CN')}
//         style={{color: intlLocale.locale === 'zh-CN' ? '#4EB7B9' : '#FFFFFF'}}>中</NgButtonText>
//     </div>
//   );
// };

type Props = IntlPropType & UserSessionProps & RouterProps;
const { Title } = Typography;

// eslint-disable-next-line max-lines-per-function
const InnerLogin = (props: Props) => {
  const { intl } = props;
  const [form] = Form.useForm();
  const [loading, setLoading] = useState<boolean>(true);
  const [apiStatus, setApiStatus] = useState<ApiStatus | undefined>();
  const [intlLocale] = useRecoilState(intlState);
  const setStoragePath = useSetRecoilState(useStoragePath);
  const [loginVersion, setLoginVersion] = useState<string>('');
  const [errorList, setErrorList] = useState<string[]>([]);
  const [disabledLoginBtn, setDisabledLoginBtn] = useState<boolean>(false);
  const [, setTableFilter] = useRecoilState(useFilterParams);
  const errorListRef = useRef(errorList);
  const diskSmartRef = useRef<any>();
  const SubmitingRef = useRef<boolean>(false);

  useMount(async () => {
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    window.systemAPI.checkDisk80Percent();
    initObserver('/login');
    initImageTms();
    const version = await window.systemAPI.getLoginVersion();
    setLoginVersion(version);
    currentLanguage = intlLocale.locale || 'zh-CN';

    await window.tmsAPI.set_render();
  });

  const checkDiskSmart = async () => {
    let count = 0;

    if ((await window.systemAPI.checkDiskSmart()) === 'success') {
      return;
    }

    diskSmartRef.current = setInterval(async () => {
      let status = await window.systemAPI.checkDiskSmart();
      // eslint-disable-next-line no-console
      console.log('磁盘检查', status);
      if (status === 'success') {
        clearInterval(diskSmartRef.current);

        return;
      }
      if (count === 4) {
        clearInterval(diskSmartRef.current);
        if (['service-error'].includes(status)) {
          // eslint-disable-next-line @typescript-eslint/no-floating-promises
          window.systemAPI.pushSystemFault({ '0A010004': FaultStatusEnum.abnormal }, 'login page 磁盘检查超时');
        }

        return;
      }
      const error = [];
      if (['error'].includes(status)) {
        error.push('0A010005: 磁盘故障');
        const _errorList = Array.from(new Set(errorList.concat(error)));
        errorListRef.current = _errorList;
        setErrorList(_errorList);
        clearInterval(diskSmartRef.current);
      }

      count++;
    }, 3000);
  };

  useEffect(() => {
    // NOSONAR
    let intervalCount = 0;
    const getLastLoginInfo = async () => {
      try {
        const getLastName = getAuthApiEndpoint().getConfig({ group_name: 'init', name: 'lastAccount' });
        let [lastLoginUser] = await Promise.all([getLastName]);
        if (lastLoginUser && lastLoginUser[0]) {
          form.setFieldsValue({ username: lastLoginUser[0].value });
        }
        if (statusInterval) clearInterval(statusInterval);
        setLoading(false);
        window.systemAPI.pushSystemFault({ '0A010001': FaultStatusEnum.normal }, '登录页请求到数据');
        // eslint-disable-next-line @typescript-eslint/no-floating-promises
        checkDiskSmart();
      } catch (e) {
        sendRenderLog.error(`服务器链接异常${e}`);
      }
    };

    const statusInterval = setInterval(async () => {
      await getLastLoginInfo();
      intervalCount++;
      if (intervalCount === 100) {
        window.systemAPI.pushSystemFault({ '0A010001': FaultStatusEnum.abnormal }, '登录页请求数据超时');
      }
    }, 2000);

    return () => {
      if (statusInterval) clearInterval(statusInterval);
    };
  }, [form]);
  const setLoginDisabledStatus = (apiStatusState: ApiStatus) => {
    if (!apiStatusState.error) return;
    const { message } = apiStatusState.error;
    if (message === '产品不可用，请 5 分钟后再试' || message === 'Product not available, please try again after 5 minutes.') {
      setDisabledLoginBtn(true);
      setTimeout(() => {
        setDisabledLoginBtn(false);
        setApiStatus(undefined);
      }, 300000);
    } else {
      setDisabledLoginBtn(false);
    }
  };
  const getSubmitBtnItem = () => {
    return (
      <NgFormItem>
        <NgButton block htmlType="submit" id="loginSubmit" className={styles.loginButton} disabled={disabledLoginBtn}>
          <FormattedMessage id="登录" />
        </NgButton>
      </NgFormItem>
    );
  };
  const handleSubmit = async (value: { username: string; password: string }): Promise<any> => {
    if (SubmitingRef.current) return;
    SubmitingRef.current = true;
    const { setUserSession, router } = props;
    const loginDto = {
      username: value.username.trim(),
      password: value.password,
      language: currentLanguage,
    };
    try {
      const { userSession, apiStatus: resApiStatus } = await window.authAPI.login(loginDto);
      if (resApiStatus) {
        // @ts-ignore
        setApiStatus(resApiStatus);
        setLoginDisabledStatus(resApiStatus);

        return;
      }
      if (userSession) {
        setUserSession?.(userSession);
        axiosHttpClient.setLanguage(loginDto.language);
        axiosHttpClient.setAuthToken(userSession.jwtToken);
        setM200ApiInstance();
        let storagePathStr = await window.systemAPI.getStoragePath();
        setStoragePath(storagePathStr);
        sendHistoryLog.info('登录成功');
        if (+userSession.role_id === EnumUserPageQueryModelRoleEnumList.TechSupport) {
          router?.navigate?.('/techsupport');

          return;
        }
        setTableFilter(defaultHomeFilter);
        router?.navigate?.('/home');
      }
    } catch (error) {
      //
    } finally {
      SubmitingRef.current = false;
    }
  };

  const handleUserNameChange = () => {
    form.setFieldsValue({
      password: '',
    });
  };

  const getUsernameItem = () => {
    const rules: Rule[] = [
      {
        required: true,
        message: intl.formatMessage({ id: '账号不可为空' }),
      },
    ];

    return (
      <Form.Item label={intl.formatMessage({ id: '账号' })} validateTrigger="onBlur" rules={rules} name="username">
        <NgInput
          allowClear={{ clearIcon: <Icon component={AllowClear} style={{ fontSize: 16 }} rev="" /> }}
          placeholder={intl.formatMessage({ id: '请输入账号' })}
          onChange={handleUserNameChange}
        />
      </Form.Item>
    );
  };

  const getPasswordItem = () => {
    const rules: Rule[] = [{ required: true, message: intl.formatMessage({ id: '密码不可为空' }) }];

    return (
      <NgFormItem label={<FormattedMessage id="密码" />} validateTrigger="onBlur" name="password" rules={rules} className={ngInputStyles.ng_input}>
        <Input.Password
          allowClear={{ clearIcon: <Icon component={AllowClear} style={{ fontSize: 16 }} rev="" /> }}
          iconRender={(visible: boolean) => (visible ? <Eye /> : <EyeSlash />)}
          placeholder={intl.formatMessage({ id: '请输入密码' })}
        />
      </NgFormItem>
    );
  };
  const authErrorMessage = useCallback(() => {
    return (
      <Form.Item name={'error'}>
        {apiStatus ? (
          <NgAlert icon={<Error />} className={styles.errorAlert} message={apiStatus.error?.message} type="error" closeIcon={false} showIcon />
        ) : (
          <div />
        )}
      </Form.Item>
    );
  }, [apiStatus]);

  const renderLoading = (): ReactElement => {
    return (
      <div>
        <NgLoading loadingText={'加载中...'} />
      </div>
    );
  };

  // const loginCheckShouldShutdown = (list: string[]) => {
  //   const errorListKeys = list.map(errors => errors.split(':')?.[0]);

  //   return shutdownList.some(error => errorListKeys.includes(error));
  // };

  return (
    <Spin spinning={loading} delay={500} indicator={renderLoading()} wrapperClassName={styles.ng_spin}>
      <section className={styles.login}>
        <div className={styles.left}>
          <div className={styles.pointImage} />
        </div>
        <div className={styles.right}>
          <div className={styles.header}>
            <ShutDown />
          </div>
          <div className={styles.rightContainer}>
            <div className={styles.formContainer}>
              <Title level={3} className={styles.loginTitle}>
                {intl.formatMessage({ id: '磁刺激仪' })}
              </Title>
              <NgForm form={form} size={'large'} layout="vertical" onFinish={handleSubmit} requiredMark={false} className={styles.loginForm}>
                {getUsernameItem()}
                {getPasswordItem()}
                {authErrorMessage()}
                {getSubmitBtnItem()}
              </NgForm>
            </div>
          </div>
          <div className={styles.version}>
            {intl.formatMessage({ id: '发布版本' })}: {loginVersion}
          </div>
        </div>
      </section>
      <ErrorModel
        isStimulate={false}
        faultTypeList={[FaultEnum.systemFault]}
        onOk={() => {
          //
        }}
      />
    </Spin>
  );
};
export const Login = withUserSession(withRouter(injectIntl(InnerLogin)));
export default Login;
