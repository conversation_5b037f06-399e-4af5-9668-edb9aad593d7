import { breadCrumbType } from '../../uiComponent/NgBreadCrumb';

export const breadcrumbList: (cbList: (() => void)[]) => breadCrumbType[] = cbList => [
  {
    path: '/home',
    breadcrumbName: '首页',
    onClick: cbList[0],
  },
  {
    path: '',
    breadcrumbName: '方案信息',
  },
];

export const colors = [
  '#0074D9',
  '#FF851B',
  '#999E10',
  '#A11665',
  '#86B67C',
  '#FF4136',
  '#3D9970',
  '#F2BEEF',
  '#F7D777',
  '#00009C',
  '#A9A9F0',
  '#B10DC9',
  '#8D001E',
  '#39CCCC',
  '#C79B8A',
  '#CA8911',
  '#48878A',
  '#460078',
  '#29CAA8',
  '#C4E1E5',
  '#714A67',
  '#E7C077',
  '#FCB543',
  '#738223',
];
