@import '../../static/style/baseColor.module.less';

.constainer {
  background-color: @colorA1;
  height: 100%;

  .loading {
    max-height: 100% !important;
  }

  .crumb {
    margin-bottom: 10px;
  }

  :global {
    .ant-spin-nested-loading {
      height: 100%;
    }

    .ant-spin-container {
      height: 100%;
      display: flex;
      flex-direction: column;
      padding: 20px 12px 4px;
    }
  }

  .photo_content {
    position: absolute;
    justify-content: center;
    left: 50%;
    right: 50%;
  }

  .content {
    box-sizing: border-box;
    color: @colorA12;
    display: flex;
    flex-grow: 1;
    overflow: hidden;

    .patient_info {
      box-sizing: border-box;
      width: 280px;
      height: fit-content;
      background-color: @colorA3;
      flex-shrink: 0;
      padding: 20px 20px 0;
      border-radius: 6px;
    }

    .viewer_container {
      flex: 1;
      display: flex;
      height: 100%;
      padding: 121px 12px 0;
      position: relative;

      .control_container {
        top: 0;
      }

      :global {
        .volume-container {
          canvas {
            background-color: #141426 !important;
          }
        }
      }
    }

    .spot_container {
      display: flex;
      flex: 0;
      .spot_list {
        width: 314px;
        margin-right: 8px;
        padding-right: 4px;
        height: 100%;
        overflow-y: auto;

        &::-webkit-scrollbar {
          width: 4px;
        }
        &::-webkit-scrollbar-thumb {
          background: @colorA6;
          border-radius: 10px;
        }
        &::-webkit-scrollbar-thumb:vertical {
          width: 10px;
          height: 10px;
        }
      }

      .spot_edit {
        box-sizing: border-box;
        height: fit-content;
        // display: flex;
        // flex-direction: column;
        // justify-content: space-between;
        width: 300px;
        padding: 20px;
        background-color: @colorA3;
        border-radius: 6px;

        .spot_edit_footer {
          display: flex;
          justify-content: flex-end;

          .spot_cancel,
          .spot_submit {
            width: 120px;
            height: 32px;

            button {
              width: 100%;
              border: 0;
              border-radius: 6px;
            }
          }

          .spot_cancel {
            margin-right: 20px;
          }
        }
      }
    }
  }

  .footer_container {
    display: flex;
    justify-content: flex-end;
    padding: 0 12px 20px;
  }
}

.modal_confirm {
  :global {
    .ant-modal-content {
      background-color: @colorA4;

      .ant-modal-confirm-content,
      .ant-modal-confirm-title {
        color: @colorA11;
      }

      .ant-btn {
        cursor: url('@/renderer/static/svg/cursor.cur'), pointer;

        &.ant-btn-default {
          color: @colorA10;
          align-items: center;
          cursor: url('@/renderer/static/svg/cursor.cur'), pointer;
          background-color: rgba(0, 0, 0, 0);
          border-color: rgba(0, 0, 0, 0);
          &:hover {
            color: @colorC4;
          }
        }

        .ant-btn:not(:disabled):focus-visible {
          outline: 0px;
        }

        &.ant-btn-primary {
          color: @colorA1;
          background-color: @colorC4;
          margin-left: 16px;

          &:hover {
            background-color: @colorC1;
          }
        }
      }
    }
  }
}
