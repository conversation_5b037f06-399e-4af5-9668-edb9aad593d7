/* eslint-disable import/no-internal-modules */
import React, { useCallback, useMemo } from 'react';
import styles from './index.module.less';
import { Form, FormInstance } from 'antd';
import { NgInput } from '../../../../uiComponent/NgInput';
import { NgRadio } from '../../../../uiComponent/NgRadio';
import classnames from 'classnames';
import { NgTextArea } from '../../../../uiComponent/NgTextarea';
import { NGDatePicker } from '../../../../uiComponent/NgDatePicker';
// eslint-disable-next-line import/no-unassigned-import
import 'dayjs/locale/zh-cn'; // 需要导入！！
import locale from 'antd/es/date-picker/locale/zh_CN';
import moment, { Moment } from 'moment';
import { Rule } from 'antd/es/form';
import { getM200ApiInstance } from '../../../../../common/api/ngApiAgent';
import { AuthEnum } from '../../../../utils/authConfig';
import { useIntl } from 'react-intl';

const { Item } = Form;

type Props = {
  formRef: React.RefObject<FormInstance<null>>;
  isPreview: boolean;
  itemStatus?: AuthEnum;
};

const CodeItem = (props: any) => {
  return <p className={styles.preview_code}>{props.value}</p>;
};

export const PatientForm = (props: Props) => {
  const { itemStatus } = props;
  const m400Api = getM200ApiInstance();
  const intl = useIntl();

  const intlMessage = useCallback((value: string, options?: { [prop: string]: any }) => {
    return intl.formatMessage({ id: value, ...options });
  }, []);

  const codeRules: Rule[] = useMemo(() => {
    return [
      { required: true, message: intlMessage('患者ID不可为空') },
      {
        validator: async (_, val) => {
          return new Promise(async (res, rej) => {
            if (!val) {
              res('');

              return;
            }

            if (!/^[a-zA-Z0-9\_-]{6,30}$/.test(val)) {
              rej(intlMessage('仅允许输入6-30位字母、数字、_、-'));

              return;
            }
            if (!/^(?![-_])[a-zA-Z0-9\_-]{6,30}$/.test(val)) {
              rej(intlMessage('仅允许以字母、数字开头'));

              return;
            }
            const isExit = await m400Api.isSubjectExit(val);
            if (isExit) {
              rej(intlMessage('患者ID已存在'));

              return;
            }
            res('');
          });
        },
      },
    ];
  }, []);

  const datePickerRule = useMemo(() => {
    return [
      {
        validator: async (_: any, val: Moment) => {
          return new Promise(async (res, rej) => {
            if (!val) {
              res('');

              return;
            }
            if (val > moment().endOf('day') || val < moment().subtract(100, 'years').startOf('day')) {
              rej(intlMessage('不符合限制'));
            }
            res('');
          });
        },
      },
    ];
  }, []);

  return (
    <Form ref={props.formRef} layout="vertical" className={classnames(styles.verticalLayout, styles.container)}>
      <p className={styles.title}>{intlMessage('患者信息')}</p>
      <Item
        name="name"
        label={<>{intlMessage('姓名：')}</>}
        rules={[
          { required: true, message: intlMessage('姓名不可为空') },
          { pattern: /\S/, message: intlMessage('姓名不可为空') },
        ]}
        validateTrigger="onBlur"
        required
      >
        <NgInput showCount maxLength={30} disabled={itemStatus === AuthEnum.disable} placeholder={intlMessage('1-30位字符')} />
      </Item>
      <Item name="code" label={<>ID：</>} rules={!props.isPreview ? codeRules : []} validateTrigger="onBlur">
        {props.isPreview ? (
          <CodeItem disabled={itemStatus === AuthEnum.disable} />
        ) : (
          <NgInput maxLength={30} showCount placeholder={intlMessage('1-30位字符')} />
        )}
      </Item>
      <Item name="sex" className={styles.sex_item} label={<>{intlMessage('性别：')}</>}>
        <NgRadio.Group disabled={itemStatus === AuthEnum.disable}>
          <NgRadio value={1}>{intlMessage('男')}</NgRadio>
          <NgRadio value={2}>{intlMessage('女')}</NgRadio>
          <NgRadio value={3}>{intlMessage('其他')}</NgRadio>
        </NgRadio.Group>
      </Item>
      <Item name="birth_date" rules={datePickerRule} label={<>{intlMessage('出生日期：')}</>}>
        <NGDatePicker
          locale={locale}
          disabled={itemStatus === AuthEnum.disable}
          disabledDate={current => current && (current > moment().endOf('day') || current < moment().subtract(100, 'years').startOf('day'))}
        />
      </Item>
      <Item name="condition_desc" label={<>{intlMessage('病情描述：')}</>}>
        <NgTextArea
          disabled={itemStatus === AuthEnum.disable}
          className={styles.desc_textarea}
          style={{ resize: 'none' }}
          autoSize={false}
          maxLength={50}
          showCount
          placeholder={intlMessage('1-50位字符')}
        />
      </Item>
    </Form>
  );
};
