@import '../../../../static/style/baseColor.module.less';

.container {
  .title {
    color: @colorA12;
    margin: 0 0 24px;
    line-height: 21px;
  }

  .sex_item {
    // background-color: red;
    margin-bottom: 22px;

    :global {
      .ant-form-item-control-input {
        min-height: auto;

        .ant-radio-wrapper {
          padding: 0;
        }
      }
    }
  }

  :global {
    .ant-form-item-label {
      line-height: 21px;
      height: 29px;

      label {
        color: @colorA9;
      }
    }

    .ant-input-affix-wrapper-status-error {
      color: @colorB3;
      border-color: @colorB3 !important;
    }

    .ant-form-item-explain {
      max-height: 24px;
    }

    .ant-form-item-explain-error {
      color: @colorB3;
      font-size: 12px;
      line-height: 18px;
      height: 18px;
    }

    .ant-radio-group {
      display: block;

      .ant-radio-wrapper {
        padding: 6px 0;
        width: 30%;
      }
    }
  }

  .preview_code {
    color: #fff;
    margin: 0;
    word-wrap: break-word;
  }

  .desc_textarea {
    height: 115px;

    :global {
      .ant-input {
        height: 100% !important;

        &::-webkit-scrollbar {
          width: 4px;
        }
        &::-webkit-scrollbar-thumb {
          background: @colorA5;
          border-radius: 10px;
        }
        &::-webkit-scrollbar-thumb:vertical {
          width: 10px;
          height: 10px;
        }
      }

      .ant-input-data-count {
        color: @colorA9;
      }
    }
  }
}
