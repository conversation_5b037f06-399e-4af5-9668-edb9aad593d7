import React from 'react';
import { BrainBrowser } from '../../../../component/brainbrowser/imports';
import { clearViewerCache, getColorMapUrl, loadVolumesColormap, makeVolumeM400, preLoadVolumesColorMap } from './utils';
import { debounce } from 'lodash';

export type worldCoordsType = {
  x: number;
  y: number;
  z: number;
};

export type voxelCoordsType = {
  i: number;
  j: number;
  k: number;
};

export enum VolumeViewerOverlayType {
  overlay = 'overlay',
  overlayAligned = 'overlayaligned',
}

type Props = {
  vizArray: string[];
  getVolumeScalpIndex(index: number): void;
  updateViewer(viewer: any): void;
  onClickChangePoint?(voxelCoords?: voxelCoordsType, worldCoords?: worldCoordsType): void;
  style?: any;
  setLoading?(loading: boolean): void;
  refreshKey?: number;
};

type State = any;

export class PreviewVolume extends React.Component<Props, State> {
  private brainBrowserRef: React.RefObject<HTMLDivElement>;
  private views: any[];
  private overlay: any;
  private debounceSyncVoxel: any;

  constructor(props: Props) {
    super(props);
    this.state = {
      viewer: null,
      volumes: [],
    };
    this.brainBrowserRef = React.createRef<HTMLDivElement>();
    this.views = ['xspace', 'yspace', 'zspace'];
    this.overlay = {
      type: VolumeViewerOverlayType.overlayAligned,
      views: ['xspace', 'yspace', 'zspace'],
      backgroundColor: '#141426',
      activeBorder: '#141426',
      defaultBorder: '#141426',
    };
    this.debounceSyncVoxel = debounce((volume: any) => {
      this.syncVoxel(volume);
    }, 120);
  }

  public async componentDidUpdate(prevProps: Props, prevState: State) {
    const { refreshKey } = this.props;

    if (refreshKey !== prevProps.refreshKey) {
      await this.calculateVolumesFromState();
      this.loadBaseVolumes(this.state.viewer, true, false);
    }
  }

  private start = async () => {
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    await this.calculateVolumesFromState();
    this.startBrainBrowser();
  };

  private startBrainBrowser = () => {
    // eslint-disable-next-line @typescript-eslint/no-shadow
    BrainBrowser.VolumeViewer.start(this.brainBrowserRef.current, async (viewer: any) => {
      viewer.isLinkZoom = true;
      // eslint-disable-next-line @typescript-eslint/no-floating-promises
      preLoadVolumesColorMap(this.state.volumes).then();
      // setViewer(viewer);
      this.setState({
        viewer,
      });
      viewer.addEventListener('volumesloaded', (event: any) => {
        this.props.updateViewer(viewer);
      });
      viewer.addEventListener('cursorupdate', (event: any) => {
        this.debounceSyncVoxel(event.volume);
      });

      await this.loadDefaultColorMap(viewer);
      viewer.render();
      this.loadBaseVolumes(viewer, false, false);
    });
  };

  // eslint-disable-next-line @typescript-eslint/no-shadow
  private loadBaseVolumes = (viewer: any, reload?: boolean, hideCursor?: boolean) => {
    const panelWidth = 240;
    if (reload || (viewer && viewer.clearVolumes)) {
      viewer.volumes.forEach((volume: any) => {
        volume.data = [];
      });
      viewer.clearVolumes(true);
    }

    if (!reload && panelWidth) {
      viewer.setDefaultPanelSize(panelWidth, panelWidth);
    }

    let vols = this.state.volumes;
    viewer.loadVolumes({
      hideCursor: hideCursor || !reload,
      alwaysHideCursor: false,
      volumes: vols,
      overlay: this.overlay,
      complete: async () => {
        if (!reload) {
          loadVolumesColormap(vols, viewer)
            .then(() => undefined)
            .catch((e: any) => e);
        }
        this.updateCanvasWidth(!reload);

        updateMarks();
        if (this.props.setLoading) {
          this.props.setLoading(false);
        }
      },
    });

    const updateMarks = () => {
      if (!viewer) return;
      viewer.updateMarks([]);
    };
  };

  private updateCanvasWidth = (init?: boolean) => {
    const panelWidth = 240;
    const panelHeight = 240;
    const { viewer } = this.state;

    if (!viewer || !viewer.volumes) return;

    const height = panelHeight || panelWidth;
    const size = panelWidth < height ? panelWidth : height;
    viewer.setPanelSize(size, size, { scale_image: true, init });

    const { current } = this.brainBrowserRef;
    if (!current || !current.childNodes.length) return;
    const canvasList = current.childNodes[0].childNodes;
    if (!canvasList || !canvasList.length) return;
    [].forEach.call(canvasList, (canvas: any, index: number) => {
      canvas.style.width = `${size - 2}px`;
      canvas.style.height = `${size - 2}px`;
    });
  };

  // eslint-disable-next-line @typescript-eslint/no-shadow
  private loadDefaultColorMap = async (viewer: any) => {
    return new Promise((resolve: any) => {
      viewer.loadDefaultColorMapFromURL(getColorMapUrl('gray-scale.txt'), '#FFFFFF', () => {
        resolve();
      });
    });
  };

  private syncVoxel = (volume: any) => {
    const { onClickChangePoint } = this.props;
    const { viewer } = this.state;

    if (!viewer) return;

    const baseVolume = viewer.volumes[0];

    if (!baseVolume) return;

    if (BrainBrowser.utils.isFunction(volume.getVoxelCoords)) {
      let { x, y, z } = volume.getWorldCoords();
      // overlay voxel coords order is error
      let { i, j, k } = volume.getVoxelCoords();
      i = i >= 1 ? i - 1 : 0;
      j = j >= 1 ? j - 1 : 0;
      k = k >= 1 ? k - 1 : 0;

      if (onClickChangePoint) {
        onClickChangePoint(
          { i, j, k },
          {
            x: Number(x.toPrecision(4)),
            y: Number(y.toPrecision(4)),
            z: Number(z.toPrecision(4)),
          }
        );
      }
    }
  };

  private async calculateVolumesFromState() {
    const {
      // t1Opacity,
      // showScalp,
      // updateScalpMaskIndex,
      getVolumeScalpIndex,
      vizArray,
    } = this.props;
    const views = this.views;
    const t1Opacity = 100;
    const showScalp = true;
    if (this.props.setLoading) {
      this.props.setLoading(true);
    }

    let { t1Index, scalpMaskIndex, overlayIndex } = this.state;
    const vizPath = vizArray.find(item => item?.includes('T1.mgz'));

    if (!vizPath)
      return new Promise((resolve: any) => {
        this.setState(
          {
            volumes: [],
          },
          resolve
        );
      });

    const volumes: any[] = [];
    let volumeIndex = 0;

    // const baseVolume = await makeVolume200(views, viz, false, volumeIndex, t1Opacity);
    const baseVolume = await makeVolumeM400(views, vizPath, false, volumeIndex, t1Opacity);
    t1Index = volumeIndex;
    volumeIndex++;

    volumes.push(baseVolume);

    const scalpVizPath = vizArray.find(item => item?.includes('scalp_mask.nii.gz'));

    if (scalpVizPath && showScalp) {
      const scalpMaskArtiVolume = await makeVolumeM400(views, scalpVizPath, false, volumeIndex, 1);
      scalpMaskIndex = volumeIndex;
      volumeIndex++;
      volumes.push(scalpMaskArtiVolume);
    }

    if (volumes.length === 1) {
      volumes[0].show_volume = true;
    }

    // volumes.concat(this.props.volumes);
    if (getVolumeScalpIndex) {
      getVolumeScalpIndex(scalpMaskIndex);
    }

    return new Promise((resolve: any) => {
      this.setState(
        {
          volumes,
          t1Index,
          scalpMaskIndex,
          overlayIndex,
        },
        () => {
          resolve();
        }
      );
    });
  }

  public componentDidMount(): void {
    BrainBrowser.VolumeViewer.cachedLoader = undefined;
    BrainBrowser.VolumeViewer.canCached = true;

    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    this.start();
  }

  public componentWillUnmount() {
    this.setState = () => undefined;
    BrainBrowser.VolumeViewer.cachedLoader = undefined;
    clearViewerCache(this.state.viewer, true);
  }

  public render() {
    return (
      <div
        ref={this.brainBrowserRef}
        style={
          this.props.style || {
            width: '286px',
            height: '286px',
          }
        }
      />
    );
  }
}
