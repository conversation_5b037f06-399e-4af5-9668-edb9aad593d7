import axios from 'axios';
import { <PERSON><PERSON>rowser } from '../../../../component/brainbrowser/imports';

export enum SurfaceViewerModelFormat {
  gifti = 'gifti',
  mgh = 'mgh',
  freesurferbin = 'freesurferbin',
  freesurferasc = 'freesurferasc',
  txt = 'text',
}

export enum Colormap {
  StandardParcSurf = '18networks-surface.txt',
  StandardParcSurf92 = '92networks-surface.txt',
  StandardParcSurf152 = '152networks-surface.txt',
  StandardParcSurf213 = '213networks-surface.txt',
  StandardParcVol = '18networks-volume.txt',
  StandardParcVol92 = '92networks-volume.txt',
  StandardParcVol152 = '152networks-volume.txt',
  StandardParcVol213 = '213networks-volume.txt',
  HighResParc = 'color-table-108.txt',
  GrayScale = 'gray-scale.txt',
  BlackWhite = 'black-white.txt',
  Spectral = 'spectral-brainview.txt',
  Thermal = 'thermal.txt',
  TaskfMRI = 'contrast-2.96-1.96-volume.txt',
  surfAnatAparcAsegLH = 'surfAnatAparcAsegLH.txt',
  surfAnatAparcAsegRH = 'surfAnatAparcAsegRH.txt',
  SurfAnatAparcA2009sAsegLH = 'SurfAnatAparcA2009sAsegLH.txt',
  SurfAnatAparcA2009sAsegRH = 'SurfAnatAparcA2009sAsegRH.txt',
  FreeSurferAseg = 'free-surfer-aseg.txt',
  FreeSurferA2009sAseg = 'free-surfer-a2009s-aseg.txt',
  AllVolumeColor = 'FreeSurferColorLUT.txt',
  // connectivity-0.2-0.6-surface.txt
  ConnectivityColorSurface = 'connectivity-0.2-0.6-1-surface.txt',
  ConnectivityColorVolume = 'connectivity-0.2-0.6-volume.txt',
  TaskfSurface = 'contrast-5-1.96-surface.txt',
  TaskfVolume = 'contrast-5-1.96-volume.txt',
  GroupSurface = 'contrast-4-0-surface.txt',
  GroupVolume = 'contrast-4-0-volume.txt',
  FunctionalColor = 'connectivity-0.25-1-volume.txt',
  BlackRed = 'black-red.txt',
  RiskRedYellowVolume = 'contrast-0-1-volume.txt',
  Test = 'volume-test.txt',
  SurfaceRiskMap = 'surface-risk-map.txt',
  RiskGreenYellowVolume = 'contrast-0-1-volume-green-yellow.txt',
  RiskGreenYellowSurface = 'contrast-0-1-surface-green-yellow.txt',
  White = 'white.txt',
  Black = 'black.txt',
}

export enum VolumeViewerVolumeFormat {
  mgh = 'mgh',
  nifti1 = 'nifti1',
}

export const makeVolumeM400 = async (
  views: any[],
  path: string,
  showVolume: boolean,
  displayZIndex: number,
  opacity: number = 100,
  flyPoints?: any
  // colormap: string = 'gray-scale.txt'
): Promise<any> => {
  opacity = opacity / 100;
  let type = VolumeViewerVolumeFormat.mgh;

  if (path.includes('nii')) {
    type = VolumeViewerVolumeFormat.nifti1;
  }

  const buffer = await window.fileAPI.getFile(path);

  return {
    show_volume: showVolume,
    colormap: 'gray-scale.txt',
    display_zindex: displayZIndex,
    intensity: { min: 0, max: 255 },
    type,
    views,
    opacity,
    buffer: buffer,
    // url: '',
    hideBorder: false,
    flyPoints,
    backgroundColor: '#141426',
    activeBorder: '#141426',
    defaultBorder: '#141426',
  };
};

export const getColorMapUrl = (colormap: string) => {
  return `colormap/${colormap}`;
};

const CachedColorMaps = {};

export const preLoadVolumesColorMap = async (volumes: any[]) => {
  await Promise.all(
    volumes.map(async volume => {
      const colormapUrl = getColorMapUrl(volume.colormap);
      if (!CachedColorMaps[colormapUrl]) {
        CachedColorMaps[colormapUrl] = await axios.get(colormapUrl);
      }
    })
  );
};

export const checkColormapIsFunc = (colormap: Colormap) => {
  return (
    colormap === Colormap.StandardParcSurf ||
    colormap === Colormap.StandardParcSurf92 ||
    colormap === Colormap.StandardParcSurf152 ||
    colormap === Colormap.StandardParcSurf213 ||
    colormap === Colormap.StandardParcVol ||
    colormap === Colormap.StandardParcVol92 ||
    colormap === Colormap.StandardParcVol152 ||
    colormap === Colormap.StandardParcVol213 ||
    colormap === Colormap.Test
  );
};

export const formatFreeSurferColorMap = (colormap: string = '') => {
  const lines = colormap.trim().split(/\n/);
  const colorMapColors: number[] = [];
  let ic = 0;
  const lineCount = lines.length;
  let lineLength;

  for (let i = 0; i < lineCount; i++) {
    let color = lines[i].trim().split(/\s+/).slice(0, 6);
    lineLength = color.length;

    if (lineLength === 6) {
      ic = parseInt(color[0], 10);
      ic *= 4;
      color = color.slice(2, 6);
    }

    for (let j = 0; j < 4; j++) {
      const val = parseFloat(color[j]);
      // correct opacity 0 to 1
      const colorVal = j === 3 && val === 0 ? 1 : val;
      colorMapColors[ic + j] = colorVal;
    }

    ic += 4;
  }
  const retArr = [];
  for (let i = 0; i < colorMapColors.length; i++) {
    const isOpacity = (i + 1) % 4 === 0;
    let colorVal: number | '1.0' = '1.0';
    if (colorMapColors[i] === undefined) {
      colorVal = 0;
    } else if (!isOpacity) {
      colorVal = colorMapColors[i] / 255;
    }
    retArr.push(colorVal);
    retArr.push(isOpacity ? '\n' : ' ');
  }

  return retArr.join('');
};

export const loadVolumeColorMapFromUrl = async (
  viewer: any,
  volId: number,
  colormapUrl: string,
  isFreeSurfer: boolean,
  patchIds?: number[],
  isResection?: boolean
) => {
  let response;
  if (CachedColorMaps[colormapUrl]) {
    response = CachedColorMaps[colormapUrl];
  } else {
    response = await axios.get(colormapUrl);
    CachedColorMaps[colormapUrl] = response;
  }
  const resactionGap = 96;
  let colormap = response.data;
  if (isFreeSurfer) {
    colormap = formatFreeSurferColorMap(colormap);
  }
  let nextPatchIds: number[] | undefined;
  if (isResection && patchIds) {
    nextPatchIds = [];
    for (let patchId of patchIds) {
      if (patchId === 1) {
        nextPatchIds = [...nextPatchIds, ...Array.apply(undefined, Array(96)).map((item: any, index: number) => index + 1)];
      } else {
        nextPatchIds.push(patchId * resactionGap);
      }
    }
  } else {
    nextPatchIds = patchIds;
  }
  colormap = getColorsByPatchIds(colormap, nextPatchIds, true);
  loadVolumeColorMapFromString(viewer, volId, colormap);
};

export const loadVolumeColorMapFromString = (viewer: any, volId: number, colormapData: string, spectrumRange?: any) => {
  const colormap = BrainBrowser.createColorMap(colormapData, { scale: 255 });
  viewer.setVolumeColorMap(volId, colormap);
  if (spectrumRange) {
    viewer.volumes[volId].intensity_min = -spectrumRange.max_value;
    viewer.volumes[volId].intensity_max = spectrumRange.max_value;
  }
};

export const getColorsByPatchIds = (colormap: string, patchIds?: number[], isVolume?: boolean) => {
  let str = '';
  const colors = colormap.split(/\n/);
  if (!isVolume && patchIds) {
    patchIds = patchIds.filter((id: number) => id >= 1000).map((id: number) => id % 100);
  }
  for (let i = 0; i < colors.length; i++) {
    const matchPatchId = patchIds ? patchIds.includes(i) : true;
    if (matchPatchId) {
      str += `${colors[i]}\n`;
    } else if (colors[i].trim() !== '') {
      str += isVolume ? '0 0 0 0\n' : '1 1 1 1\n';
    }
  }

  return str;
};

export const loadVolumesColormap = async (
  volumes: any[],
  viewer: any,
  anatPatchIds?: number[],
  funcPatchIds?: number[],
  isResection: boolean = false
) => {
  return Promise.all(
    volumes.map(async (propVolume: any, volId: number) => {
      if (propVolume.colormap) {
        const isFreeSurfer =
          propVolume.colormap === Colormap.FreeSurferAseg ||
          propVolume.colormap === Colormap.FreeSurferA2009sAseg ||
          propVolume.colormap === Colormap.AllVolumeColor;
        const isFunc = checkColormapIsFunc(propVolume.colormap);
        let nextPatchIds: number[] | undefined;
        if (isFreeSurfer || isFunc) {
          nextPatchIds = isFreeSurfer ? anatPatchIds : funcPatchIds;
        }
        const colormapUrl = getColorMapUrl(propVolume.colormap);
        await loadVolumeColorMapFromUrl(viewer, volId, colormapUrl, isFreeSurfer, nextPatchIds, isResection);
      }
      const selfVolume: any = viewer.volumes[volId];
      const intensity = propVolume.intensity;
      if (selfVolume && intensity) {
        selfVolume.intensity_min = intensity.min;
        selfVolume.intensity_max = intensity.max;
      }
      if (selfVolume) {
        selfVolume.isRiskMask = propVolume.isRiskMask;
        selfVolume.isRiskHeatMap = propVolume.isRiskHeatMap;
      }
      const clamp = propVolume.clamp;
      if (selfVolume && clamp) {
        selfVolume.display.setClamp(clamp);
      }
    })
  );
};

export const clearPanelCachedData = (panel: any) => {
  if (!panel) return;

  if (panel.slice && panel.slice.slices) {
    panel.slice.slices.forEach((s: any) => {
      s.data = [];
    });
  }
  if (panel.slice_image) {
    panel.slice_image = null;
  }
};

export const clearVolume = (volume: any, completeClear: boolean = false) => {
  if (!volume) return;

  volume.data = [];
  if (volume.clearCachedSlices) {
    volume.clearCachedSlices();
  }
  if (!volume.display) return;

  volume.display.forEach((panel: any) => {
    if (!panel) return;

    clearPanelCachedData(panel);

    let { canvas } = panel;
    if (canvas) {
      // canvas.clearAllListeners();
      // In chrome, set canvas width and height 0 for force free memory
      canvas.width = 0;
      canvas.height = 0;
      if (completeClear) {
        canvas = null;
      }
    }

    if (completeClear) {
      panel.clearAllListeners();
      panel = null;
    }
  });
};

export const clearViewerCache = (viewer: any, completeClear: boolean = false, onlyClearXHR: boolean = false) => {
  let { cacheVolumeXHRs } = BrainBrowser.loader;
  if (cacheVolumeXHRs && cacheVolumeXHRs.length) {
    cacheVolumeXHRs.forEach((e: any, index: number) => (cacheVolumeXHRs[index] = undefined));
    cacheVolumeXHRs = [];
  }

  if (!viewer || !viewer.volumes || onlyClearXHR) return;

  /* if (viewer.requestAnimationFrame) {
  window.cancelAnimationFrame(viewer.requestAnimationFrame);
  } */

  viewer.volumes.forEach((volume: any) => {
    clearVolume(volume, completeClear);
  });

  if (completeClear) {
    viewer.clearVolumes();
    viewer.clearAllListeners();
  }
};
