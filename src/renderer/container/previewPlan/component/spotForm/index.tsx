import React, { use<PERSON>allback, useEffect, useMemo, useState } from 'react';
import styles from './index.module.less';
import { Form, FormInstance } from 'antd';
import { NgRadio } from '../../../../uiComponent/NgRadio';
import classnames from 'classnames';
import { NgInput } from '../../../../uiComponent/NgInput';
import { CoorInput } from '../../../../component/coorInput';
import { EditStimulateTemplate } from '../../../../component/template';
import { cloneDeep } from 'lodash';
import { enFlatObj, flatObj } from '../../utils';
import { TBSChart } from '../../../../component/tbsChart';
import { ImportTemplate as ImportIcon, StimulateImportDisabled } from '../../../../uiComponent/SvgGather';
import { NgIcon } from '../../../../uiComponent/NgIcon';
import { ImportTemplate } from '../../../../component/importTemplate';
import { AuthEnum } from '../../../../utils/authConfig';
import { useIntl } from 'react-intl';
import { Rule } from 'antd/es/form';

export interface SpotErrorType {
  isMepError: boolean;
  isTreatError: boolean;
}

const { Item } = Form;
const headerList = new Set<string>(['has_mep', 'name', 'vol_ras']);

type Props = {
  formRef: React.RefObject<FormInstance<null>>;
  spotInfo: any;
  iconDisabled: boolean;
  onChangeCoord(val: any): void;
  isChangedSpotForm: boolean;
  onChangeTreatSpot(): void;
  onChangeSpotForm(isEditing: true): void;
  onChangeMepSpot(): void;
  refreshKey: number;
  hasError: SpotErrorType;
  itemStatus?: AuthEnum;
};

export const SpotForm = (props: Props) => {
  const { iconDisabled = true, onChangeMepSpot, onChangeTreatSpot, onChangeSpotForm } = props;
  const [isMep, setIsMep] = useState<boolean>(false);
  const [preName] = useState<'stimulus'>('stimulus');
  const [stimulus, setStimulus] = useState<any>({});
  const [importVisible, setImportVisible] = useState<boolean>(false);
  const [selectedMepTab, setSelectedMepTab] = useState<boolean>(false);
  const [isChangedSpot, setIsChangedSpot] = useState<boolean>(false);
  const [hasError, setHasError] = useState<SpotErrorType>({ isMepError: false, isTreatError: false });
  const { itemStatus } = props;
  const intl = useIntl();

  const intlMessage = useCallback((value: string, options?: { [prop: string]: any }) => {
    return intl.formatMessage({ id: value, ...options });
  }, []);

  const changeTreatSpot = () => {
    setSelectedMepTab(false);
    onChangeTreatSpot();
  };
  const changeMepSpot = () => {
    setSelectedMepTab(true);
    onChangeMepSpot();
  };

  useEffect(() => {
    setIsMep(selectedMepTab);
    if (selectedMepTab) {
      props.formRef.current?.resetFields();
      props.formRef.current?.setFieldsValue({ has_mep: true } as any);
    } else {
      props.formRef.current?.resetFields();
      props.formRef.current?.setFieldsValue({ has_mep: false, type: 5 } as any);
      setStimulus({ type: 5 });
    }
  }, [props.refreshKey]);

  useEffect(() => {
    const { isChangedSpotForm } = props;
    setIsChangedSpot(isChangedSpotForm);
  }, [props.isChangedSpotForm]);

  useEffect(() => {
    if (!props.spotInfo) {
      setIsMep(selectedMepTab);

      return;
    }
    setIsMep(props.spotInfo?.has_mep);
    setStimulus(props.spotInfo.stimulus || {});
  }, [JSON.stringify(props.spotInfo)]);

  useEffect(() => {
    setHasError(props.hasError);
  }, [props.hasError]);

  const coorRules: Rule[] = useMemo(() => {
    return [
      {
        validator: async (_, val) => {
          if (!val || !val.x || !val.y || !val.z) return Promise.reject('');
          const reg = /^-?\d+(\.\d+)?$/;
          if (!reg.test(val.x) || !reg.test(val.y) || !reg.test(val.z)) return Promise.reject('');

          return Promise.resolve();
        },
      },
    ];
  }, []);

  const handleValueChange = (changeValues: any, values: any) => {
    onChangeSpotForm(true);
    const key = Object.keys(changeValues)[0];
    if (headerList.has(key)) return;

    let res = cloneDeep(values);
    if (key === 'type') {
      const reg = new RegExp(`^${preName}\\.`);
      Object.keys(res).forEach(v => {
        if (reg.test(v)) {
          res[v] = undefined;
        }
      });
    }
    const stimulate_static = { ...enFlatObj(res)[preName], type: res.type };
    setStimulus(stimulate_static);
  };

  const handleChangeMep = (value: boolean) => {
    setIsMep(value);
    props.formRef.current?.setFieldsValue({ type: 5 } as any);
  };

  const handleCancelImport = () => {
    setImportVisible(false);
  };

  const handleImport = (stimulate_static: any) => {
    props.formRef.current?.setFieldsValue({ ...flatObj({ [preName]: stimulate_static }), type: stimulate_static.type } as any);
    setStimulus(stimulate_static);
    setImportVisible(false);
    onChangeSpotForm(true);
  };

  return (
    <>
      <Form ref={props.formRef} layout="vertical" onValuesChange={handleValueChange} className={classnames(styles.verticalLayout, styles.container)}>
        <div className={styles.spot_info}>
          <Item name="has_mep" className={styles.mep_select}>
            <NgRadio.Group
              disabled={itemStatus === AuthEnum.disable}
              onChange={e => {
                handleChangeMep(e.target.value);
              }}
              size="middle"
              optionType={'button'}
              buttonStyle={'solid'}
              id="preview-plan-spot-type"
            >
              <NgRadio
                className={isMep ? styles.radio_normal : styles.radio_selected}
                disabled={selectedMepTab && isChangedSpot}
                onClick={changeTreatSpot}
              >
                {intlMessage('治疗靶点')}
              </NgRadio>
              {hasError.isTreatError && <div className={styles.treat_red_dot}> </div>}
              <NgRadio
                className={isMep ? styles.radio_selected : styles.radio_normal}
                disabled={!selectedMepTab && isChangedSpot}
                onClick={changeMepSpot}
                value
              >
                {intlMessage('MEP参考点')}
              </NgRadio>
              {hasError.isMepError && <div className={styles.mep_red_dot}> </div>}
            </NgRadio.Group>
          </Item>
          <Item name="name" label={intlMessage('靶点名称：')} className={styles.name_input}>
            <NgInput disabled={itemStatus === AuthEnum.disable} showCount maxLength={30} />
          </Item>
          <Item name="vol_ras" label={intlMessage('靶点坐标（volRAS）：')} className={styles.vol_input} rules={coorRules}>
            <CoorInput disabled={itemStatus === AuthEnum.disable} onChange={props.onChangeCoord} />
          </Item>
        </div>
        {!isMep && (
          <>
            <p className={styles.chart_title}>
              <span>{intlMessage('参数')}</span>
              <NgIcon
                tooltip={{
                  title: !iconDisabled ? intlMessage('导入脉冲模板') : intlMessage('无可导入脉冲模板'),
                  color: '#434351',
                  placement: 'left',
                }}
                authName="previewPlan.importStimulate"
                disabled={iconDisabled}
                iconSvg={iconDisabled ? StimulateImportDisabled : ImportIcon}
                onClick={() => {
                  if (!iconDisabled) setImportVisible(true);
                }}
              />
            </p>
            <TBSChart className={styles.chart_container} template={stimulus} />
          </>
        )}
        {!isMep && (
          <div className={styles.stimulate_container}>
            <EditStimulateTemplate
              itemCol={{ wrapperCol: { span: 11 }, labelCol: { span: 13 } }}
              preName={preName}
              formRef={props.formRef.current}
              stimulate={stimulus}
              motionThreshold={0}
              inputClass="spot-input-number-class"
              itemDisabled={itemStatus === AuthEnum.disable}
            />
          </div>
        )}
      </Form>
      <ImportTemplate visible={importVisible} onCancel={handleCancelImport} onOk={handleImport} />
    </>
  );
};
