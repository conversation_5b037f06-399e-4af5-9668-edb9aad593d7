@import '../../../../static/style/baseColor.module.less';

.container {
  :global {
    .ant-form-item-label {
      padding-bottom: 4px;

      label {
        color: @colorA9;
        line-height: 21px;
      }
    }

    .ant-form-item-has-error {
      .spot-input-number-class {
        border: 1px solid #ba6058;
      }
    }

    .ant-input-affix-wrapper-status-error {
      color: @colorB3;
    }

    .ant-form-item-explain {
      max-height: 24px;
    }

    .ant-form-item-explain-error {
      color: @colorB3;
      font-size: 12px;
      line-height: 18px;
      height: 18px;
    }

    .ant-radio-group {
      display: block;

      .ant-radio-button-wrapper {
        padding: 6px 0;
        width: 50%;
        height: 32px;
      }
    }
  }

  .chart_container {
    height: 152px;
    display: flex;
    justify-content: flex-end;
  }

  .mep_select {
    margin-bottom: 20px;
    position: 'relative';

    .radio_selected {
      background-color: @colorA5;
    }
    .radio_normal {
      color: @colorA9;
    }
    .treat_red_dot {
      position: absolute;
      top: 0;
      right: 49%;
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background-color: @colorB5;
      transform: translate(10%, -10%);
      z-index: 9;
    }

    .mep_red_dot {
      position: absolute;
      top: 0;
      right: 0;
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background-color: @colorB5;
      transform: translate(10%, -10%);
      z-index: 9;
    }
  }

  .vol_input,
  .name_input {
    margin-bottom: 16px;
  }

  .vol_input {
    :global {
      .ant-form-item-label label {
        color: @colorA9;
      }

      .ant-form-item:nth-child(4) {
        display: none;
      }

      .ant-form-item-explain {
        position: absolute;
      }

      .ant-form-item-explain-error {
        color: #ba6058;
        font-size: 12px;
        line-height: 18px;
        height: 18px;
        margin: 2px 0 4px;
      }
    }
  }

  .stimulate_container {
    min-height: 463px;

    :global {
      .ant-form-item {
        .ant-form-item-label {
          margin-top: 5px;
          padding: 0 0 5px;

          &::after {
            content: ':';
            color: @colorA9;
          }
        }

        .ant-form-item-row {
          flex-direction: row;
        }

        .ant-form-item-extra {
          color: @colorA9;
          position: absolute;
          top: 32px;
          right: 0;
          font-size: 12px;
        }
      }
    }
  }

  .chart_title {
    display: flex;
    justify-content: space-between;
    color: @colorA9;
    margin: 0;
  }

  .spot_info {
    margin-bottom: 32px;
  }
}
