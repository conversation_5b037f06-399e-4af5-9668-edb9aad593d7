export type Coordinate = {
  x: number;
  y: number;
  z: number;
};

export const viewerLoadColorMapFromURL = async (viewer: any, url: string) => {
  return new Promise((resolve: any) => {
    viewer.loadColorMapFromURL(url, () => {
      resolve();
    });
  });
};

type LoadOptions = {
  format: string;
  modelName: string;
  resultType?: string;
  contentType?: string;
  opacity?: number;
  scalp_mask_options?: {
    color?: number;
    emissive?: number;
    emissiveIntensity?: number;
  };
};
export const viewerLoadModelFromLocalPath = async (viewer: any, url: string, options?: LoadOptions) => {
  const buffer = await window.fileAPI.getFile(url);

  return new Promise<void>(resolve => {
    viewer.loadModelFromLocalFilePath((buffer as Buffer).buffer, {
      ...options,
      model_name: options?.modelName,
      complete: () => {
        resolve();
      },
    });
  });
};

export const updateSurfaceCursorByWord = (
  surfaceViewer: any,
  coord: Coordinate,
  color: number,
  suffix: string,
  size: number = 2.8,
  isBaseMeterial: boolean = false
) => {
  color = color || 0xffffff;
  const { x, y, z } = coord;
  const dotName = `Dot${suffix}`;
  if (surfaceViewer) {
    let dotIndex = surfaceViewer.model.children.findIndex((v: any) => v.name === dotName);
    if (dotIndex > -1) {
      surfaceViewer.model.remove(surfaceViewer.model.children[dotIndex]);
    }
    surfaceViewer.drawDot(x, y, z, size, color, 0, { name: dotName, isBaseMeterial });
    surfaceViewer.updated = true;
  }
};

export const removeDot = (surfaceViewer: any, suffix: string) => {
  const dotName = `Dot${suffix}`;
  if (surfaceViewer) {
    let dotIndex = surfaceViewer.model.children.findIndex((v: any) => v.name === dotName);
    if (dotIndex > -1) {
      surfaceViewer.model.remove(surfaceViewer.model.children[dotIndex]);
    }
    surfaceViewer.updated = true;
  }
};

export const updateVolumeCursorByWorld = (volumeViewer: any, coord: Coordinate) => {
  const { x, y, z } = coord;
  if (volumeViewer && volumeViewer.volumes) {
    volumeViewer.volumes.forEach((volume: any) => {
      // eslint-disable-next-line @typescript-eslint/no-unused-expressions
      volume.setWorldCoords && volume.setWorldCoords(x, y, z);
      volume.display.forEach((panel: any) => {
        panel.hideCursor = false;
        panel.updateCursor(0xffffff);
        panel.updated = true;
        panel.updateSlice();
      });
    });
  }
};
