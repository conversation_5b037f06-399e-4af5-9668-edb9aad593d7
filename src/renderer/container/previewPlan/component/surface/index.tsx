import React from 'react';
import { Coordinate, viewerLoadColorMapFromURL, viewerLoadModelFromLocalPath } from './utils';
import { BrainBrowser } from '../../../../component/brainbrowser/imports';

type Props = {
  setSurfaceViewer(viewer: any): void;
  colorMap?: string;
  pialUrl: string;
  scalpMaskUrl: string;
  volumeFiles: string[];
  onClickChangePoint(coords: Coordinate, index: number): void;
  setLoading?(loading: boolean): void;
  refreshKey?: number;
};
type State = {
  viewer: any;
};

export class PreviewSurface extends React.Component<Props, State> {
  private brainBrowserRef: React.RefObject<HTMLDivElement>;
  constructor(props: Props) {
    super(props);
    this.state = {
      viewer: null,
    };
    this.brainBrowserRef = React.createRef<HTMLDivElement>();
  }

  public componentDidMount() {
    this.initBrain();
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    this.startSurface();
  }

  private initBrain = () => {
    BrainBrowser.SurfaceViewer.cachedLoader = undefined;
    BrainBrowser.SurfaceViewer.canCached = true;
    BrainBrowser.config.set('worker_dir', 'brainbrowser-2.5.5/workers');
  };

  public componentDidUpdate(prevProps: Readonly<Props>, prevState: Readonly<State>, snapshot?: any): void {
    if (prevProps.refreshKey !== this.props.refreshKey) {
      this.loadModel();
    }
  }

  private startSurface = async () => {
    BrainBrowser.SurfaceViewer.start(
      this.brainBrowserRef.current,
      async (viewer: any) => {
        this.setState({ viewer });
        viewer.updateViewport(700, 700);
        this.props.setSurfaceViewer(viewer);
        viewer.maxZoom = 2.5;
        viewer.minZoom = 0.5;
        viewer.render();
        this.loadModel(viewer);
      },
      undefined,
      false,
      0x141426
    );
  };

  private handleBrainbrowserClick = (event: React.MouseEvent<HTMLDivElement>) => {
    const { viewer } = this.state;
    if (viewer.moveFlag) return;
    if (viewer.model.children.length === 0) {
      return;
    }
    const pickInfo = viewer.pick(viewer.mouse.x, viewer.mouse.y, 100);
    if (pickInfo) {
      let x = pickInfo.point.x.toFixed(2);
      let y = pickInfo.point.y.toFixed(2);
      let z = pickInfo.point.z.toFixed(2);

      this.props.onClickChangePoint({ x: Number(x), y: Number(y), z: Number(z) }, pickInfo.index);
    }
  };

  private loadModel = (viewer: any = this.state.viewer) => {
    const { colorMap, pialUrl, scalpMaskUrl } = this.props;
    if (BrainBrowser.loader && BrainBrowser.loader.abortCacheSurfaceXHRs) {
      BrainBrowser.loader.abortCacheSurfaceXHRs();
    }
    try {
      viewer.clearScreen();
      if (viewer.annotations && viewer.annotations.setMarkerRadius) {
        viewer.annotations.setMarkerRadius(1);
      }
    } catch (e: any) {
      // catch viewer error
    }
    if (this.props.setLoading) {
      this.props.setLoading(true);
    }
    const promiseList = [];
    if (colorMap) {
      promiseList.push(viewerLoadColorMapFromURL(viewer, `colormap/${colorMap}`));
    }
    if (pialUrl) {
      promiseList.push(
        viewerLoadModelFromLocalPath(viewer, pialUrl, {
          format: 'gifti',
          modelName: 'pial_gii',
          contentType: 'text',
          opacity: 100,
        })
      );
    }
    if (scalpMaskUrl) {
      promiseList.push(
        viewerLoadModelFromLocalPath(viewer, scalpMaskUrl, {
          format: 'mniobj',
          modelName: 'scalp_mask',
          contentType: 'text',
          opacity: 20,
          scalp_mask_options: {
            color: 0xaeaeae,
            emissive: 0xa3a3a3,
          },
        })
      );
    }
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    Promise.all(promiseList).then(res => {
      viewer.zoom = 0.8;
      if (this.props.setLoading) {
        this.props.setLoading(false);
      }
    });
  };

  public componentWillUnmount() {
    if (!this.state) return;
    // fix Warning: Can't perform a React state update on an unmounted component
    this.setState = () => undefined;

    const { viewer } = this.state;
    if (viewer) {
      viewer.dom_element = undefined;
      viewer.drawTrajectory = undefined;
      viewer.clearLines = undefined;
      viewer.updateLineText = undefined;
      (window as any).cancelAnimationFrame(viewer.requestAnimationFrame);

      this.clearThreeData();

      this.clearMemory();

      viewer.clearScreen();
    }

    BrainBrowser.events.clearAllListeners();
  }

  private clearMemory = () => {
    const { viewer } = this.state;

    let { cacheSurfaceXHRs } = BrainBrowser.loader;
    if (cacheSurfaceXHRs && cacheSurfaceXHRs.length) {
      cacheSurfaceXHRs.forEach((e: any, index: number) => (cacheSurfaceXHRs[index] = undefined));
      cacheSurfaceXHRs = [];
    }

    if (!viewer) return;

    if (viewer.clearCachedWebgl) {
      viewer.clearCachedWebgl();
    }

    if (this.brainBrowserRef.current) {
      const canvas = this.brainBrowserRef.current.childNodes[0] as HTMLCanvasElement;
      if (!canvas) return;
      const context = (canvas.getContext('webgl') || canvas.getContext('experimental-webgl')) as any;
      if (context) {
        context.clearColor(1.0, 1.0, 0.0, 1.0);
        context.clear(context.COLOR_BUFFER_BIT);
        context.clear(context.DEPTH_BUFFER_BIT);
        context.clear(context.STENCIL_BUFFER_BIT);
        const webglContext = context.getExtension('WEBGL_lose_context');
        if (webglContext) webglContext.loseContext();
      }
    }
  };

  private clearThreeData = () => {
    const { viewer } = this.state;
    if (!(viewer && viewer.model)) return;
    const masks = this.state.viewer.model.children || [];

    // clear threejs memory
    masks.forEach((mask: any) => {
      if (!mask) return;
      if (mask.geometry) {
        mask.geometry.dispose();
        mask.geometry = undefined;
        delete mask.geometry;
      }
      if (mask.material) {
        mask.material.dispose();
        mask.material = undefined;
        delete mask.material;
      }
      if (mask.userData) {
        mask.userData = undefined;
      }
    });
  };

  render(): React.ReactNode {
    return <div ref={this.brainBrowserRef} style={{ width: '660px', height: '850px' }} onMouseUp={this.handleBrainbrowserClick} />;
  }
}
