import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import styles from './index.module.less';
import { NgIcon } from '../../../../uiComponent/NgIcon';
import { NgSlider } from '../../../../uiComponent/NgSlider';
import {
  BrainBottomIcon,
  BrainLocationBack,
  BrainLocationBottom,
  BrainLocationFront,
  BrainLocationLeft,
  BrainLocationRight,
  BrainLocationTop,
  BrainOpacity,
  BrainVolumeGray,
  CircleDoubt,
} from '../../../../uiComponent/SvgGather';
import { Tooltip } from 'antd';
// import Draggable, { DraggableData, DraggableEvent } from 'react-draggable';
import classNames from 'classnames';
import { useIntl } from 'react-intl';
import Icon from '@ant-design/icons';

type SurfaceViewerView = 'superior' | 'inferior' | 'medial' | 'lateral' | 'anterior' | 'posterior';

type Props = {
  surfaceViewer: any;
  volumeViewer: any;
  className?: string;
  refreshKey?: number;
};

type iconInfo = {
  key: SurfaceViewerView;
  label: string;
  icon: any;
};

const iconList: iconInfo[] = [
  { key: 'superior', label: '上', icon: BrainLocationTop },
  { key: 'inferior', label: '下', icon: BrainLocationBottom },
  { key: 'lateral', label: '左', icon: BrainLocationRight },
  { key: 'medial', label: '右', icon: BrainLocationLeft },
  { key: 'anterior', label: '前', icon: BrainLocationFront },
  { key: 'posterior', label: '后', icon: BrainLocationBack },
];

export const ViewControl = (props: Props) => {
  const { surfaceViewer } = props;
  const [towardInfo, setTowardInfo] = useState<iconInfo>(iconList[0]);
  const [opacity, setOpacity] = useState<number>(38);
  const [grayValue, setGrayValue] = useState<[number, number]>([0, 255]);
  const draggleRef = useRef<HTMLDivElement>(null);
  const intl = useIntl();

  const intlMessage = useCallback((value: string, options?: { [prop: string]: any }) => {
    return intl.formatMessage({ id: value, ...options });
  }, []);

  const handleChangeToward = (key: SurfaceViewerView) => {
    surfaceViewer.setView(key);
    surfaceViewer.zoom = 0.8;
    setTowardInfo(iconList.find(v => v.key === key)!);
  };

  const handleChangeOpcity = (key: number) => {
    const view = surfaceViewer.model.children.find((obj: any) => obj.name === 'scalp_mask_1');
    setOpacity(key);
    view.material.opacity = key / 100;
    view.material.transparent = true;
    surfaceViewer.updated = true;
  };

  const handleContrastChange = ([val1, val2]: number[]) => {
    const { volumes } = props.volumeViewer;
    if (volumes.length) {
      volumes[0].intensity_min = Math.min(val1, val2);
      volumes[0].intensity_max = Math.max(val1, val2);
      props.volumeViewer.redrawVolumes();
    }
    setGrayValue([val1, val2]);
  };

  const brainIconList = useMemo(() => {
    return iconList.map(v => (
      <div className={styles.content} key={v.key} onClick={() => handleChangeToward(v.key)}>
        <NgIcon iconClass={styles.icon} iconSvg={v.icon} />
        <span className={styles.label}>{v.label}</span>
      </div>
    ));
  }, []);

  useEffect(() => {
    setTowardInfo(iconList[0]);
    setOpacity(38);
    setGrayValue([0, 255]);
  }, [props.refreshKey]);

  return (
    <div className={classNames(styles.view_control_container, props.className)} id="view-control">
      <strong className={styles.header} ref={draggleRef} />
      <Tooltip
        trigger="click"
        overlayClassName={styles.direction_container}
        title={brainIconList}
        placement="rightBottom"
        getPopupContainer={(triggerNode: any) => triggerNode.parentNode}
      >
        {/* <NgIcon /> */}
        <Icon component={BrainBottomIcon} className={styles.brain_bottom} size={4} rev="" />
        <NgIcon
          iconClass={styles.iconClass}
          fontSize={20}
          iconSvg={towardInfo.icon}
          tooltip={{
            placement: 'left',
            color: '#434351',
            overlayClassName: styles.view_control_tips,
            title: `${intlMessage('大脑分区：')}${intlMessage(towardInfo.label)}`,
          }}
        />
      </Tooltip>
      <Tooltip
        overlayClassName={styles.tooltip_contaienr}
        getPopupContainer={(triggerNode: any) => triggerNode.parentNode}
        trigger="click"
        title={() => (
          <div className={styles.slider_content}>
            <NgSlider value={opacity} suffix="%" min={10} max={90} onChange={handleChangeOpcity} />
          </div>
        )}
        placement="right"
      >
        <NgIcon
          iconClass={styles.iconClass}
          fontSize={20}
          iconSvg={BrainOpacity}
          tooltip={{ title: intlMessage('调节图像不透明度'), overlayClassName: styles.view_control_tips, color: '#434351', placement: 'left' }}
        />
      </Tooltip>
      <Tooltip
        overlayClassName={styles.tooltip_contaienr}
        getPopupContainer={(triggerNode: any) => triggerNode.parentNode}
        trigger="click"
        title={() => (
          <div className={classNames(styles.slider_content, styles.gray_slider)}>
            <NgSlider value={grayValue} min={0} max={255} onChange={handleContrastChange} range />
          </div>
        )}
        placement="right"
      >
        <NgIcon
          iconClass={styles.iconClass}
          fontSize={20}
          iconSvg={BrainVolumeGray}
          tooltip={{ title: intlMessage('调节灰度值'), color: '#434351', overlayClassName: styles.view_control_tips, placement: 'left' }}
        />
      </Tooltip>
      <div className={styles.split_icon} />
      <NgIcon
        iconClass={styles.iconClass}
        fontSize={20}
        iconSvg={CircleDoubt}
        tooltip={{
          title: intlMessage('1. 按住 Ctrl + 鼠标滚轮 可缩放视图\n2.长按鼠标左键，旋转脑图\n3.长按鼠标右键，平移脑图'),
          placement: 'left',
          color: '#434351',
          overlayClassName: `${styles.view_control_tips} ${styles.tooltip_tips}`,
        }}
      />
    </div>
  );
};
