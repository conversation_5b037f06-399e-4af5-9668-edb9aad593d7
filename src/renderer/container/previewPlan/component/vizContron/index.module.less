@import '../../../../static/style/baseColor.module.less';

.view_control_container {
  position: absolute;
  width: 40px;
  height: 146px;
  background-color: @colorA4;
  border-radius: 4px;
  text-align: center;
  padding-bottom: 5px;

  .header {
    display: block;
    height: 8px;
    line-height: 10px;
    margin-bottom: 6px;
    background: @colorA3;
    border-radius: 4px 4px 0 0;
  }

  .iconClass {
    margin-bottom: 3px;

    &:hover {
      background-color: @colorA3;
      border-radius: 4px;
    }
  }

  .split_icon {
    background-image: url('@/renderer/static/images/split_icon.png');
    height: 3px;
    margin: 8px 10px;
  }
}

.tooltip_contaienr {
  :global {
    .ant-tooltip-inner {
      background-color: @colorA4;
      box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.3);
      height: 24px;
      padding: 4px 8px;
    }
    .ant-tooltip-content .ant-tooltip-arrow::before {
      background-color: transparent !important;
      // background-image: none !important;
    }

    .ant-tooltip-arrow::after {
      background-color: transparent !important;
    }
  }
}

.slider_content {
  width: 155px;
  // height: 16px;
  z-index: 1;
  border-radius: 2px;
}

.gray_slider {
  width: 150px;
}

.tooltip_tips {
  white-space: pre;
}

.brain_bottom {
  position: absolute;
  display: block;
  transform: scale(0.4);
  right: 5px;
  top: 26px;
}

.direction_container {
  position: relative;

  :global {
    .ant-tooltip-inner {
      background-color: @colorA4 !important;
    }

    .ant-tooltip-arrow {
      &::before {
        display: none;
      }
    }
  }

  .content {
    cursor: url('@/renderer/static/svg/cursor.cur'), pointer !important;
    &:hover {
      background-color: @colorA2;
      border-radius: 3px;
    }

    width: 50px;

    .icon {
      text-align: center;
    }

    :global {
      .anticon {
        margin-right: 6px;
        vertical-align: middle;

        &:hover {
          background-color: transparent;
        }
      }
    }
  }
}

.view_control_tips {
  :global {
    .ant-tooltip-arrow {
      &::before {
        display: none;
      }
    }
  }
}
