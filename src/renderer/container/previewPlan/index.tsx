/* eslint-disable @typescript-eslint/no-shadow */
import React, { useRef, useEffect, useState, useCallback, ReactElement } from 'react';
import styles from './index.module.less';
import { NgBreadcrumb } from '../../uiComponent/NgBreadCrumb';
import { breadcrumbList, colors } from './config';
import { PatientForm } from './component/patientForm';
import { FormInstance, Modal, Spin, message } from 'antd';
// eslint-disable-next-line import/no-extraneous-dependencies
import { pinyin } from 'pinyin-pro';
import { SpotItem } from '../../component/spotItem';
import { SpotErrorType, SpotForm } from './component/spotForm';
import { useRecoilState, useRecoilValue } from 'recoil';
import { planInfoAtom } from '../../recoil/planInfo';
import { useNavigate, useParams } from 'react-router';
import dayjs from 'dayjs';
import { PreviewSurface } from './component/surface';
import { Coordinate, removeDot, updateSurfaceCursorByWord, updateVolumeCursorByWorld } from './component/surface/utils';
import { PreviewVolume } from './component/volume';
import {
  beyondAllEntryPoint,
  brainbrowserLoaderText,
  enFlatObj,
  flatObj,
  getSpotStatus,
  getVertexByPosition,
  getflatInfo,
  saveSpotList,
  saveStimulateList,
  surfCoord2VolCoord,
  volumeXYZtoSurface,
} from './utils';
import { getM200ApiInstance } from '@/common/api/ngApiAgent';
import { calTbsChartData } from '../../component/template/calTemplate';
import NgSelectFileModal from '../../uiComponent/NgSelectFileModal';
import { ReactComponent as Warning } from '@/renderer/static/svg/warning.svg';
import NgModal from '../../uiComponent/NgModal';
import { cloneDeep, throttle } from 'lodash';
import { ViewControl } from './component/vizContron';
import CameraAndCoil from '../../component/cameraAndCoil';
import { NgIcon } from '../../uiComponent/NgIcon';
import { WarnMessage } from '../../uiComponent/SvgGather';
import { authTypeState } from '../../recoil/license';
import { AuthEnum } from '../../utils/authConfig';
import { osUserInfo } from '@/renderer/recoil/osUserInfo';
import NgDarkButton from '../../uiComponent/NgDarkButton';
import { useIntl } from 'react-intl';
import { LineStatusType, useLineAtom } from '../../recoil/lineStatus';
import { NgLoading } from '../../uiComponent/NgLoading';

export enum DeviceType {
  linux = 1,
  mac = 2,
}

enum TriggerType {
  surf = 1,
  vol = 2,
  form = 3,
}

const RangeErrorLimit = 20;

let spotListStatic: any[] = [];
let triggerType: TriggerType = TriggerType.surf;
let staticColorList: string[] = []; // 静态避免重复，遵循queue

export const PreviewPlan = () => {
  const [ loading, setLoading ] = useState<boolean>(true);
  const [ surfaceLoading, setSurfaceLoading ] = useState<boolean>(true);
  const [ volumeLoading, setVolumeLoading ] = useState<boolean>(true);
  const [ planInfo, setPlanInfo ] = useState<any>();
  const [ treatSpotList, setTreatSpotList ] = useState<any[]>([]);
  const [ mepSpotList, setMepSpotList ] = useState<any[]>([]);
  const [ showSpotList, setShowSpotList ] = useState<any[]>([]);
  const [ surfaceViewer, setSurfaceViewer ] = useState<any>();
  const [ volumeViewer, setVolumeViewer ] = useState<any>();
  const [ fileInfo, setFileInfo ] = useState<{ [prop: string]: string }>();
  const [ volumeScalpMaskIndex, setVolumeScalpIndex ] = useState<number>(-1);
  const [ tkras2ras, setTkras2ras ] = useState<any>();
  const [ curSpotKey, setCurSpotKey ] = useState<number | null>(null);
  const [ modalVisiable, setModalVisiable ] = useState<boolean>(false);
  const [ uploading, setUploading ] = useState<boolean>(false);
  const [ importDisabled, setImportDisabled ] = useState<boolean>(false);
  const [ isChangedSpotForm, setIsChangedSpotForm ] = useState<boolean>(false);
  const [ refreshSpotFormKey, setRefreshSpotFormKey ] = useState<number>(0);
  const [ refreshViewer, setRereshViewer ] = useState<number>(0);
  const [ refreshScroll, setRefreshScroll ] = useState<number>(0);
  const [leftVertexNumber18, setleftVertexNumber18] = useState<number>(0);
  const [spotError, setSpotError] = useState<SpotErrorType>({isTreatError: false, isMepError: false});
  const [ extList, setExtList ] = useState<string[]>([]);
  const osInfo = useRecoilValue(osUserInfo);
  const isTreatSpotTabRef = useRef<boolean>(true);
  const totalSpotListRef = useRef<any[]>([]);

  const patientRef = useRef<FormInstance>(null);
  const spotRef = useRef<FormInstance>(null);
  const spotListRef = useRef<HTMLDivElement>(null);
  const volumeDomRef = useRef<HTMLDivElement>(null);

  const [ localPlanInfo ] = useRecoilState(planInfoAtom);
  const [ authState ] = useRecoilState(authTypeState);
  const [ , setLine ] = useRecoilState(useLineAtom);
  const intl = useIntl();
  const m200Api = getM200ApiInstance();
  const { subjectId, planId } = useParams();
  const navigate = useNavigate();
  const [ messageApi, contextHolder ] = message.useMessage();
  const deviceType: DeviceType = /macintosh|mac os x/i.test(navigator.userAgent) ? 2 : 1;

  const intlMessage = useCallback((value: string, options?: { [prop: string]: any }) => {
    return intl.formatMessage({ id: value, ...options });
  }, []);

  const fixNumber = (value: { x: number; y: number; z: number }) => {
    return {
      x: Number(value.x).toFixed(2),
      y: Number(value.y).toFixed(2),
      z: Number(value.z).toFixed(2),
    };
  };

  const initInfo = async (plan_info: any, prePath: string) => {
    setCurSpotKey(null);
    setRefreshSpotFormKey(Date.now());
    const subject_model = plan_info.plan.subject_model || {};
    patientRef.current?.setFieldsValue({
      ...subject_model,
      birth_date: dayjs(subject_model.birth_date),
      condition_desc: subject_model.condition_desc?.slice(0, 50),
    });
    staticColorList = [ ...colors ];
    const spot_list: any = plan_info.plan.plan_target_model_list.map((v: any, i: number) => {
      const color = v.color || colors[i % colors.length];
      staticColorList = staticColorList.filter(v => color !== v);

      return {
        ...v,
        key: v.id || i + 1,
        color,
        vol_ras: fixNumber(v.vol_ras),
        surf_ras: fixNumber(v.surf_ras),
        is_error: getSpotStatus(v, i),
        is_range_error: i >= RangeErrorLimit,
      };
    });
    const file_info = plan_info.plan.plan_file_model_list.reduce(
      (pre: any, cur: any) => ({ ...pre, [cur.name]: `${prePath}/${cur.relative_path}` }),
      {}
    );
    const res = await brainbrowserLoaderText(file_info['lh_anat_parc_aparc.txt']);
    setleftVertexNumber18(res.split('\n').length);
    setTkras2ras(JSON.parse(await window.fileAPI.getFileJson(file_info['tkras2ras_matrices.json'])));
    setFileInfo(file_info);
    const mepSpotList = spot_list.filter((item: any) => item.has_mep);
    const treatSpotList = spot_list.filter((item: any) => !item.has_mep);
    setMepSpotList(mepSpotList);
    setTreatSpotList(treatSpotList);
    setShowSpotList(treatSpotList);
    spotListStatic = cloneDeep(spot_list);
    setTimeout(() => {
      patientRef.current?.validateFields().catch(_ => {
        //
      });
    }, 500);
  };

  const handleChangeActive = (cur_spot: any) => {
    triggerType = TriggerType.form;
    updateVolumeCursorByWorld(volumeViewer, cur_spot.vol_ras);
    surfaceViewer?.focusMark('pial_gii', cur_spot.surf_ras);
    spotRef.current?.resetFields();
    spotRef.current?.setFieldsValue({ ...flatObj(cur_spot), type: cur_spot.stimulus?.type, vol_ras: cur_spot.vol_ras });
    setTimeout(() => {
      spotRef.current?.validateFields().catch(_ => {
        //
      });
    }, 500);
  };

  const handleSpotClickValidate = async (isChanged: boolean) => {
    return new Promise(async (res, rej) => {
      if (authState['previewPlan.pageItem'] !== AuthEnum.normal) {
        res('');

        return;
      }
      try {
        await setFormNull();
        await spotRef.current?.validateFields();
        if (!isChanged) {
          res('');

          return;
        }

        Modal.confirm({
          content: intlMessage('是否更新修改后的靶点及参数信息？'),
          okText: intlMessage('更新'),
          icon: <NgIcon isPreview iconSvg={WarnMessage}/>,
          wrapClassName: styles.modal_confirm,
          onOk: async () => {
            await updateSpotList(true);
            res('');
          },
          cancelText: intlMessage('否'),
          onCancel: () => res(''),
        });
      } catch (err) {
        Modal.error({
          content: intlMessage('存在错误信息，请更正后操作'),
          icon: <NgIcon isPreview iconSvg={WarnMessage}/>,
          wrapClassName: styles.modal_confirm,
          okText: intlMessage('我知道了'),
          onOk: () => rej(),
        });
      }
    });
  };

  const handleSpotClick = async (id: number) => {
    try {
      if (curSpotKey !== null) {
        await handleSpotClickValidate(isChangedSpotForm);
      }

      setCurSpotKey(id);
      setIsChangedSpotForm(false);
      const cur_spot = showSpotList.find(v => v.key === id);
      handleChangeActive({ ...cur_spot });
      const pre_spot = showSpotList.find(v => v.key === curSpotKey);
      removeDot(surfaceViewer, '');
      surfaceViewer.zoom = 0.8;
      if (pre_spot) {
        updateSurfaceCursorByWord(surfaceViewer, pre_spot.surf_ras, parseInt(pre_spot.color.slice(1), 16), curSpotKey?.toString() || '', 2);
      }
      updateSurfaceCursorByWord(surfaceViewer, cur_spot.surf_ras, parseInt(cur_spot.color.slice(1), 16), id?.toString() || '');
    } catch (err) {
      //
    }
  };

  const handleDelete = (id: number) => {
    if (id === curSpotKey) {
      setCurSpotKey(null);
      setRefreshSpotFormKey(Date.now());
    }
    const cur_spot = showSpotList.find(v => v.key === id);
    // 如果不在颜色池子里
    if (!staticColorList.some(v => v === cur_spot.color)) {
      staticColorList.push(cur_spot.color);
    }
    removeDot(surfaceViewer, id.toString());
    resetSpotList(showSpotList.filter((v: any) => v.key !== id), true);
  };

  const ignoreLineList = () => {
    const ignoreSet = new Set<number>();

    spotListStatic.forEach((v: any) => {
      if (v.id) {
        const cur_vol = showSpotList.find(val => val.id === v.id)?.vol_ras;
        if (!cur_vol) return;
        if (v.normal_line && cur_vol.x === v.vol_ras.x && cur_vol.y === v.vol_ras.y && cur_vol.z === v.vol_ras.z) {
          // return;
          ignoreSet.add(v.id);
        }
      }
    });

    return ignoreSet;
  };

  const handleSubmit = async (): Promise<void> => { // NOSONAR
    await handleValidate();
    setLoading(true);
    try {
      let subject_model = patientRef.current?.getFieldsValue();
      let pinyinValue = pinyin(subject_model.name, { toneType: 'none', type: 'array' });
      const ignoreSet = ignoreLineList();

      const pinyin_username =
        subject_model.name === planInfo.plan.subject_model?.name ? planInfo.plan.subject_model.pinyin_username : pinyinValue.flat().join('');

      subject_model = {
        ...subject_model,
        birth_date: subject_model.birth_date && dayjs(subject_model.birth_date).startOf('days').valueOf(),
        id: subjectId,
        pinyin_username,
      };
      const save_spot_info = totalSpotListRef.current.map(v => {
        const res = getflatInfo(saveStimulateList, v);
        if (v.has_mep) {
          res.stimulus = undefined;
        }
        if (!ignoreSet.has(v.id)) {
          res.clear_algorithm_data = true;
        }

        return res;
      });
      let res_plan: { [props: string]: any } = {};
      if (planId && subjectId) {
        const save_plan_info = getflatInfo(saveSpotList, planInfo.plan);
        res_plan = await m200Api.editPlan({
          ...save_plan_info,
          file_name: planInfo.file_name,
          temp_directory: planInfo.temp_directory,
          plan_target_model_list: save_spot_info,
          subject_model: {
            ...subject_model,
            code: undefined,
          },
          subject_id: subjectId,
          id: planId,
        });
      } else {
        const params = {
          file_name: planInfo.file_name,
          temp_directory: planInfo.temp_directory,
          plan: {
            ...planInfo.plan,
            subject_model: {
              ...subject_model,
              id: planInfo.plan.subject_model?.id,
            },
            plan_target_model_list: totalSpotListRef.current.map(v => {
              if (v.has_mep)
                return {
                  ...v,
                  disable_horizontal: false,
                };

              return v;
            }),
            plan_file_model_list: undefined,
            type: undefined,
          },
        };
        res_plan = await m200Api.importPlan(params);
        res_plan.plan_target_model_list = res_plan.plan_target_model_list.filter((v: any) => !v.normal_line);
      }
      const cal_list = res_plan.plan_target_model_list.filter((v: any) => !ignoreSet.has(v.id));
      if (!isNaN(volumeScalpMaskIndex) && cal_list.length) {
        setLine([ { key: res_plan.id, status: LineStatusType.loading } ]);
        let convex_file_name: string | undefined;
        if (planId && subjectId) {
          convex_file_name = (fileInfo || {})['head_convex_hull.ply'];
        } else {
          const prePath = await window.systemAPI.getStoragePath();
          convex_file_name = res_plan.plan_file_model_list.reduce(
            (pre: any, cur: any) => ({ ...pre, [cur.name]: `${prePath}/${cur.relative_path}` }),
            {}
          )['head_convex_hull.ply'];
        }
        const spot_info = {
          plan_id: res_plan.id,
          subject_id: res_plan.subject_id,
        };
        // eslint-disable-next-line @typescript-eslint/no-floating-promises
        beyondAllEntryPoint(volumeViewer, volumeScalpMaskIndex, cal_list, spot_info, setLine, convex_file_name);
      }
      Modal.destroyAll();
      navigate('/home');
      setLoading(false);
    } catch (e: any) {
      setLoading(false);
    }
  };

  const bindUpdateVolumeViewer = (viewer: any) => {
    viewer.showMark = true;

    setVolumeViewer(viewer);
    // setVolumeLoading(false);
  };

  const bindUpdateSurfaceViewer = (viewer: any) => {
    setSurfaceViewer(viewer);
    // setSurfaceLoading(false);
  };

  const handleSurfClick = (coords: Coordinate) => {
    // console.log(volumeViewer)
    triggerType = TriggerType.surf;
    let vol_ras = surfCoord2VolCoord(coords, tkras2ras)!;

    if (curSpotKey === null) {
      const near_spot = showSpotList.find(
        v => Math.abs(v.surf_ras.x - coords.x) < 3 && Math.abs(v.surf_ras.y - coords.y) < 3 && Math.abs(v.surf_ras.z - coords.z) < 3
      );
      if (near_spot) {
        // eslint-disable-next-line @typescript-eslint/no-floating-promises
        handleSpotClick(near_spot.key);
        if (spotListRef.current) {
          const near_index = showSpotList.findIndex(v => v.key === near_spot.key);
          spotListRef.current.scrollTop = 102 * near_index;
        }

        return;
      }
    }

    // console.log(res, 'res');
    updateVolumeCursorByWorld(volumeViewer, vol_ras);
    changeCoord(vol_ras, coords);
  };

  const handleVolumeClick = (voxelCoords: any, coords: Coordinate) => {
    if (triggerType === TriggerType.surf || triggerType === TriggerType.form) {
      return;
    }
    const surf_ras = volumeXYZtoSurface(coords, tkras2ras);
    changeCoord(coords, surf_ras!);
  };

  const handleChangeCoord = (coords: Coordinate) => {
    triggerType = TriggerType.form;
    const reg = /^-?\d+(\.\d+)?$/;
    if ([ 'x', 'y', 'z' ].some(v => !coords[v] || !reg.test(coords[v]))) return;
    updateVolumeCursorByWorld(volumeViewer, coords);
    const surf_ras = volumeXYZtoSurface(coords, tkras2ras);
    changeCoord(coords, surf_ras!, true);
  };

  const changeCoord = (vol_ras: any, surf_ras: Coordinate, without_vol?: boolean) => {
    const cur_spot = showSpotList.find(v => v.key === curSpotKey);
    let color = staticColorList[0];
    if (cur_spot) {
      color = cur_spot.color;
    }
    updateSurfaceCursorByWord(surfaceViewer, surf_ras, parseInt(color.slice(1), 16), curSpotKey?.toString() || '');
    if (triggerType !== TriggerType.surf) {
      surfaceViewer?.focusMark('pial_gii', surf_ras);
    }
    if (authState['previewPlan.pageItem'] === AuthEnum.normal) {
      changeSpotName(vol_ras, surf_ras, without_vol);
    }
    surfaceViewer.zoom = 0.8;
    setIsChangedSpotForm(curSpotKey === null? false: true);
  };

  const changeSpotName = (vol_ras: Coordinate, surf_ras: Coordinate, without_vol?: boolean) => {
    const spot_name = spotRef.current?.getFieldValue('name');
    const params: any = {};
    if (!without_vol) {
      params.vol_ras = vol_ras;
    }
    if (!spot_name || /^(lh|rh)/.test(spot_name)) {
      const { hemi, vertex_index } = calVertexIndex(surf_ras);
      params.name = `${hemi}${vertex_index}`;
    }
    spotRef.current?.setFieldsValue(params);
  };
  const calVertexIndex = (surf_ras: any) => {
    // const leftVertexNumber18 = 147477;
    let vertex_index = getVertexByPosition(surfaceViewer, surf_ras);
    const hemi = leftVertexNumber18 && vertex_index >= leftVertexNumber18 - 1 ? 'rh' : 'lh';
    vertex_index = leftVertexNumber18 && vertex_index >= leftVertexNumber18 - 1 ? vertex_index - (leftVertexNumber18 - 1) : vertex_index;

    return {
      hemi,
      vertex_index,
    };
  };

  const setFormNull = async () => {
    const values = spotRef.current?.getFieldsValue();
    const obj = {};
    for (const key in values) {
      if (Object.prototype.hasOwnProperty.call(values, key)) {
        const item = values[key];
        if (
          item === undefined &&
          (key !== 'stimulus.intermission_time' || (values['stimulus.strand_pulse_count'] !== 1 && key === 'stimulus.intermission_time'))
        ) {
          obj[key] = null;
        } else {
          obj[key] = values[key];
        }
      }
    }

    spotRef.current?.setFieldsValue(obj);
    await new Promise(async res => {
      setTimeout(async () => {
        return res('');
      }, 100);
    });
  };

  const updateSpotList = async (needNotRefresh: boolean = false) => {
    if ((mepSpotList.length + treatSpotList.length) >= RangeErrorLimit && !curSpotKey) return;
    try {
      await setFormNull();
      await spotRef.current?.validateFields();
      let form_value = enFlatObj(spotRef.current?.getFieldsValue());
      const surf_ras = volumeXYZtoSurface(form_value.vol_ras, tkras2ras);
      const vertex_info = calVertexIndex(surf_ras);
      if (!form_value.has_mep) {
        const { treatment_time = 0, pulse_total = 0 } = calTbsChartData(form_value.stimulus);
        form_value.stimulus.treatment_time = treatment_time;
        form_value.stimulus.pulse_total = pulse_total;
        form_value.stimulus.type = form_value.type;
      } else {
        form_value.stimulus = undefined;
      }
      form_value.type = undefined;

      form_value = {
        ...form_value,
        surf_ras,
        ...vertex_info,
      };
      if (curSpotKey) {
        const cur_spot = showSpotList.find(v => v.key === curSpotKey);
        form_value = {
          ...cur_spot,
          ...form_value,
          is_error: false,
        };
        resetSpotList(showSpotList.map(v => {
          if (v.key === curSpotKey) {
            return form_value;
          } else {
            return v;
          }
        }));
        updateSurfaceCursorByWord(surfaceViewer, surf_ras!, parseInt(cur_spot.color.slice(1), 16), cur_spot.key?.toString() || '', 2);
      } else {
        const add_spot = { ...form_value, surf_ras, key: Date.now(), color: staticColorList[0], is_range_error: (treatSpotList.length + mepSpotList.length) >= RangeErrorLimit };
        staticColorList.shift();
        removeDot(surfaceViewer, '');
        updateSurfaceCursorByWord(surfaceViewer, surf_ras!, parseInt(add_spot.color.slice(1), 16), add_spot.key?.toString() || '', 2);
        resetSpotList([...showSpotList, add_spot]);
      }
      if (!needNotRefresh) {
        setCurSpotKey(null);
        setRefreshSpotFormKey(Date.now());
      }
      setIsChangedSpotForm(false);
    } catch (error) {
      //
    }
  };
  const resetSpotList = (spots: any[], isDelete?: boolean) => {
    // 删除靶点后，靶点数小于最大限制数,把超限的错误给去掉
    if (isDelete && (mepSpotList.length + treatSpotList.length) === RangeErrorLimit + 1) {
      spots.forEach(v => { v.is_range_error = false; });
      setShowSpotList(spots);
      if (isTreatSpotTabRef.current) {
        setTreatSpotList(spots);
        setMepSpotList(mepSpotList.map((v) => ({ ...v, is_range_error: false })));
      } else {
        setMepSpotList(spots);
        setTreatSpotList(treatSpotList.map((v) => ({ ...v, is_range_error: false })));
      }
    } else {
      setShowSpotList(spots);
      if (isTreatSpotTabRef.current) {
        setTreatSpotList(spots);
      } else {
        setMepSpotList(spots);
      }
    }
  };

  const handleCancelSpot = () => {
    const cur_spot = showSpotList.find(v => v.key === curSpotKey)!;
    setIsChangedSpotForm(false);
    setCurSpotKey(null);
    setRefreshSpotFormKey(Date.now());
    updateSurfaceCursorByWord(surfaceViewer, cur_spot.surf_ras, parseInt(cur_spot.color.slice(1), 16), cur_spot.key?.toString() || '', 2);
  };

  const init = async () => {
    if (!(subjectId && planId) && !localPlanInfo) {
      navigate('/home');
      Modal.destroyAll();
    }
    const size = (await m200Api.getStimulusTemplateList({ page_num: 1, page_size: 1 })).records.length;
    setImportDisabled(size === 0);
    try {
      const ext_list = await window.systemAPI.getEnv('extList');
      setExtList(ext_list);
    } catch (error) {
      //
    }

    let planInfo: any;
    let prePath: string;
    if (subjectId && planId) {
      const res = await m200Api.getPlanById(parseInt(planId, 10), parseInt(subjectId, 10));

      planInfo = { plan: res };
      prePath = await window.systemAPI.getStoragePath();
    } else {
      planInfo = localPlanInfo;
      prePath = planInfo.temp_directory;
    }
    setPlanInfo(planInfo);
    await initInfo(planInfo, prePath);
  };

  const handleReChooseFile = async () => {
    try {
      const list = await window.fileAPI.getFolderInfo(deviceType === DeviceType.mac ? '/' : osInfo.filePath!);
      if (list.length === 0) throw Error('未检测到移动设备');
      NgModal.confirm({
        content: <>{intlMessage('患者信息及方案数据将被覆盖，是否进行该操作？')}</>,
        icon: <NgIcon iconSvg={WarnMessage}/>,
        headerIcon: <Warning/>,
        onOk: () => {
          setModalVisiable(true);
        },
        closable: false,
        okText: intlMessage('是'),
        cancelText: intlMessage('否'),
      });
    } catch (error) {
      noFileMessage();
    }
    //
  };

  const noFileMessage = () => {
    // eslint-disable-next-line no-void, @typescript-eslint/no-floating-promises
    messageApi.open({
      type: 'error',
      content: intlMessage('未检测到移动设备'),
    });
  };

  const handleSumbitFileModal = async (path: string) => {
    setUploading(true);
    try {
      const res = await window.fileAPI.uploadFile(path);
      triggerType = TriggerType.surf;
      removeAllDot(showSpotList);
      const prePath = res.temp_directory;
      await initInfo(res, prePath);
      setRereshViewer(Date.now());
      setPlanInfo(res);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log(error, '新建患者的error');
      NgModal.confirm({
        content: <>{intlMessage('文件信息不符合规范，请重新选择。')}</>,
        icon: <NgIcon iconSvg={WarnMessage}/>,
        headerIcon: <Warning/>,
        okText: intlMessage('确定'),
        closable: false,
      });
    } finally {
      setUploading(false);
      setModalVisiable(false);
      await window.systemAPI.checkDisk80Percent();
    }
  };

  const renderAllDot = (renderSpotList: any[]) => {
    renderSpotList.forEach(v => {
      const color = v.color;
      const surf_ras = v.surf_ras;
      if (surf_ras) {
        updateSurfaceCursorByWord(surfaceViewer, surf_ras, parseInt(color.slice(1), 16), v.key, 2);
      }
    });
    setLoading(false);
  };

  const removeAllDot = (removeSpotList: any[]) => {
    removeDot(surfaceViewer, '');
    removeSpotList.forEach(v => {
      removeDot(surfaceViewer, v.key);
    });
  };

  const handleValidate = async () => {
    return new Promise(async (res, rej) => {
      try {
        await patientRef.current?.validateFields();
        if (totalSpotListRef.current.some(v => v.is_error || v.is_range_error)) {
          throw JSON.parse(JSON.stringify({ type: 'spot_list_error', message: '靶点信息存在错误' }));
        }

        res('');
      } catch (err: any) {
        Modal.confirm({
          content: intlMessage('存在错误信息，请更正后操作'),
          className: styles.modal_confirm,
          icon: <NgIcon isPreview iconSvg={WarnMessage}/>,
          okText: intlMessage('更正'),
          cancelText: intlMessage('退出'),
          onCancel: () => {
            Modal.destroyAll();
            navigate('/home');
          },
          // eslint-disable-next-line @typescript-eslint/no-floating-promises
          onOk: () => rej(),
        });
      }
    });
  };

  const exitConfirm = () => {
    Modal.confirm({
      content: intlMessage('是否保存已填写的信息？'),
      className: styles.modal_confirm,
      icon: <NgIcon isPreview iconSvg={WarnMessage}/>,
      okText: intlMessage('保存'),
      cancelText: intlMessage('不保存'),
      onCancel: () => {
        Modal.destroyAll();
        navigate('/home');
      },
      onOk: () => {
        Modal.destroyAll();
        // eslint-disable-next-line @typescript-eslint/no-floating-promises
        handleSubmit();
      },
    });
  };

  /**
   * 1. 校验form错误&sportList有无错误
   * 2. 如果是新增|编辑有改动，弹窗
   */
  const handleClickBread = async () => {
    if (authState['previewPlan.pageItem'] !== AuthEnum.normal) {
      Modal.destroyAll();
      navigate('/home');
      Modal.destroyAll();

      return;
    }
    try {
      await handleValidate();

      if (!planId && !subjectId) {
        exitConfirm();

        return;
      }

      const patient_form_info = cloneDeep(patientRef.current?.getFieldsValue());
      if (patient_form_info.birth_date) {
        patient_form_info.birth_date = dayjs(patient_form_info.birth_date).valueOf();
      }
      // 判断subject有无改动
      let is_changed = Object.keys(patient_form_info).some(v => planInfo.plan.subject_model[v] !== patient_form_info[v]);

      const totalSpotList = [...mepSpotList, ...treatSpotList];
      // 判断spot是否有改动
      if (totalSpotList.length !== spotListStatic.length) {
        is_changed = true;
      }
      const calList = saveStimulateList.filter(v => !/^surf_ras/.test(v));
      is_changed =
        is_changed ||
        spotListStatic.some(v => {
          const id = v.id;
          const res_spot = totalSpotList.find(v => v.id === id);
          if (!res_spot) {
            return true;
          }

          return JSON.stringify(getflatInfo(calList, v)) !== JSON.stringify(getflatInfo(calList, res_spot));
        });

      if (is_changed) {
        exitConfirm();
      } else {
        Modal.destroyAll();
        navigate('/home');
      }
    } catch (err) {
      //
    }
  };

  const renderLoading = (): ReactElement => {
    return (
      <div>
        <NgLoading loadingText={'加载中...'}/>
      </div>
    );
  };

  const handleScroll = throttle(() => { setRefreshScroll(Date.now()); },500,{trailing: false});

  const changeTreatSpot = () => {
    if (isTreatSpotTabRef.current) return;
    isTreatSpotTabRef.current = true;

    setCurSpotKey(null);
    setRefreshSpotFormKey(Date.now());
    removeAllDot(mepSpotList);
    setShowSpotList(treatSpotList);
    renderAllDot(treatSpotList);
    setIsChangedSpotForm(false);
  };

  const changeMepSpot = () => {
    if (!isTreatSpotTabRef.current) return;
    isTreatSpotTabRef.current = false;

    setCurSpotKey(null);
    setRefreshSpotFormKey(Date.now());
    removeAllDot(treatSpotList);
    setShowSpotList(mepSpotList);
    renderAllDot(mepSpotList);
    setIsChangedSpotForm(false);
  };

  const changeSpotForm = (isEditing: boolean) => {
    if (curSpotKey !== null) {
      setIsChangedSpotForm(isEditing);
    }
  };

  useEffect(() => {
    if (!surfaceLoading && !volumeLoading) {
      renderAllDot(showSpotList);
      volumeDomRef.current?.addEventListener('mousedown', () => {
        triggerType = TriggerType.vol;
      });
    }
  }, [ surfaceLoading, volumeLoading ]);

  useEffect(() => {
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    init();
  }, []);

  /**
   * 监听mep和治疗靶点的变化
   * 当发生变化的时候，同步更新保存的总靶点数组以及判断mep和治疗靶点是否报错
   */
  useEffect(() => {
    const mepIsError = mepSpotList.some(v => v.is_error || v.is_range_error);
    const treatIsError = treatSpotList.some(v => v.is_error || v.is_range_error);
    totalSpotListRef.current = [...treatSpotList, ...mepSpotList];
    setSpotError({
      isTreatError: treatIsError,
      isMepError: mepIsError,
    });
  }, [mepSpotList, treatSpotList]);

  /**
   * 监听当前靶点的选中态
   * 处理当前选中态由选中变为取消选中的时候，同步更新当前页面是否更新的状态（isChangedSpotForm）
   */
  useEffect(() => {
    if (curSpotKey === null) {
      setIsChangedSpotForm(false);
    }
  }, [curSpotKey]);

  /**
   * 当spotlist发生改变的时候，创建scroll事件的监听
   * 在监听函数内更新state，隐藏删除悬框
   */
  useEffect(() => {
    spotListRef.current?.addEventListener('scroll', handleScroll);

    return () => {
      spotListRef.current?.removeEventListener('scroll', handleScroll);
    };
  }, [showSpotList]);

  useEffect(() => {
    return () => {
      Modal.destroyAll();
    };
  }, []);

  return (
    <>
      <div className={styles.constainer}>
        {contextHolder}
        <Spin spinning={loading} className={styles.loading} indicator={renderLoading()}>
          <NgBreadcrumb contentClassName={styles.crumb} items={breadcrumbList([ handleClickBread ])} isGray={false}/>
          <CameraAndCoil/>
          <div className={styles.content}>
            <div className={styles.patient_info} id='preview-plan-patient'>
              <PatientForm formRef={patientRef} isPreview={planId !== undefined} itemStatus={authState['previewPlan.pageItem']}/>
            </div>
            <div className={styles.viewer_container} id={'view-container'}>
              <>
                {surfaceViewer && volumeViewer && (
                  <ViewControl
                    surfaceViewer={surfaceViewer}
                    volumeViewer={volumeViewer}
                    refreshKey={refreshViewer}
                    className={styles.control_container}
                  />
                )}
              </>
              {fileInfo && (
                <div ref={volumeDomRef}>
                  <PreviewVolume
                    setLoading={setVolumeLoading}
                    getVolumeScalpIndex={setVolumeScalpIndex}
                    vizArray={[ 'T1.mgz', 'scalp_mask.nii.gz', 'lhrh_anat_parc_aparc.txt', 'lhrh_anat_parc_a2009s.txt' ].map(v => fileInfo[v])}
                    updateViewer={bindUpdateVolumeViewer}
                    onClickChangePoint={handleVolumeClick}
                    refreshKey={refreshViewer}
                  />
                </div>
              )}
              {fileInfo && (
                <PreviewSurface
                  setSurfaceViewer={bindUpdateSurfaceViewer}
                  setLoading={setSurfaceLoading}
                  onClickChangePoint={handleSurfClick}
                  colorMap={'white.txt'}
                  pialUrl={fileInfo['pial.gii']}
                  scalpMaskUrl={fileInfo['scalp_mask.obj']}
                  volumeFiles={[ fileInfo['T1.mgz'], fileInfo['scalp_mask.nii'] ]}
                  refreshKey={refreshViewer}
                />
              )}
            </div>

            <div className={styles.spot_container} id='spot-container'>
              <div className={styles.spot_list} ref={spotListRef} id='spot-item-container'>
                {showSpotList.map((v: any, index: number) => (
                  <SpotItem
                    key={v.key}
                    spotInfo={v}
                    activeId={curSpotKey}
                    onSelect={handleSpotClick}
                    onDelete={handleDelete}
                    index={index}
                    isDelete
                    itemStatus={authState['previewPlan.pageItem']}
                    refreshOpen={refreshScroll}
                  />
                ))}
              </div>
              <div className={styles.spot_edit} id='spot-form-container'>
                <SpotForm
                  formRef={spotRef}
                  spotInfo={curSpotKey && showSpotList.find(v => curSpotKey === v.key)}
                  iconDisabled={importDisabled}
                  onChangeCoord={handleChangeCoord}
                  isChangedSpotForm={isChangedSpotForm}
                  onChangeSpotForm={changeSpotForm}
                  onChangeMepSpot={changeMepSpot}
                  onChangeTreatSpot={changeTreatSpot}
                  refreshKey={refreshSpotFormKey}
                  itemStatus={authState['previewPlan.pageItem']}
                  hasError={spotError}
                />
                <div className={styles.spot_edit_footer}>
                  {curSpotKey !== null ? (
                    <>
                      <NgDarkButton
                        style={{ marginRight: 10 }}
                        className={styles.spot_cancel}
                        authName="previewPlan.spotCancel"
                        onClick={handleCancelSpot}
                      >
                        {intlMessage('取消')}
                      </NgDarkButton>
                      <NgDarkButton
                        className={styles.spot_submit}
                        disabled={!isChangedSpotForm}
                        onClick={async () => updateSpotList()}
                        authName="previewPlan.spotUpdate"
                      >
                        {intlMessage('更新')}
                      </NgDarkButton>
                    </>
                  ) : (
                    <NgDarkButton
                      disabled={(mepSpotList.length + treatSpotList.length) >= RangeErrorLimit}
                      style={{ marginRight: 10 }}
                      className={styles.spot_submit}
                      onClick={async () => updateSpotList()}
                      authName="previewPlan.spotAdd"
                    >
                      {intlMessage('添加')}
                    </NgDarkButton>
                  )}
                </div>
              </div>
            </div>
          </div>
          <div className={styles.footer_container}>
            {!(planId && subjectId) && (
              <NgDarkButton style={{ marginRight: 10 }} onClick={handleReChooseFile} authName="previewPlan.reUpload">
                {intlMessage('重选文件')}
              </NgDarkButton>
            )}
            <NgDarkButton onClick={handleSubmit} authName="previewPlan.submit">
              {intlMessage('保存方案')}
            </NgDarkButton>
          </div>
        </Spin>

        <NgSelectFileModal
          maskClosable={false}
          filepath={deviceType === DeviceType.mac ? '/' : osInfo.filePath!}
          open={modalVisiable}
          onCancel={() => setModalVisiable(false)}
          width={762}
          controlLoading={uploading}
          handleError={noFileMessage}
          extList={extList}
          onOk={files => {
            // eslint-disable-next-line @typescript-eslint/no-floating-promises
            handleSumbitFileModal(files[0].path);
          }}
        />
      </div>
    </>
  );
};

export default PreviewPlan;
