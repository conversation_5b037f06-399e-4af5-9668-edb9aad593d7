// import { getM200ApiInstance } from '../../../common/api/ngApiAgent';
import { Coordinate } from './component/surface/utils';
import * as mathjs from 'mathjs';
import WorkerList from '@/renderer/utils/worker/getEntryMinPointList.worker?worker';
import { getM200ApiInstance } from '../../../common/api/ngApiAgent';
import { calRules, getFields } from '../../component/template/calRules';
import { LineStatusType, LineType } from '../../recoil/lineStatus';
import { cloneDeep } from 'lodash';
import { BrainBrowser } from '../../component/brainbrowser/imports';
// eslint-disable-next-line import/no-internal-modules
import { PLYLoader } from 'three/examples/jsm/loaders/PLYLoader';
import { BufferGeometry } from 'three';
import { LOCALSURFENTRYPOINT_VERSION } from '../../utils/demoData';
// @ts-ignore
export type Tkras2rasType = {
  vox2ras: any;
  // eslint-disable-next-line camelcase
  vox2ras_tkr_inv: any;
};

/** 计算错误法线默认值 */
export const ERROR_MIN_POINT = {
  x: 0,
  y: 0,
  z: 500,
};

type CommonObj = { [props: string]: any };
const PLY = new PLYLoader();

export const surfCoord2VolCoord = (surfRASleWorldCoords?: Coordinate, vox?: Tkras2rasType): Coordinate | undefined => {
  if (!vox || !surfRASleWorldCoords) {
    return undefined;
  }
  const transformMatrix = mathjs.multiply(vox.vox2ras, vox.vox2ras_tkr_inv);
  const t1Tkras = [surfRASleWorldCoords.x, surfRASleWorldCoords.y, surfRASleWorldCoords.z, 1];
  const t1RasResult = mathjs.multiply(transformMatrix, t1Tkras) as any;

  return { x: t1RasResult[0].toFixed(2), y: t1RasResult[1].toFixed(2), z: t1RasResult[2].toFixed(2) };
};

export const volumeXYZtoSurface = (worldCoords?: Coordinate, tkras2ras?: any): Coordinate | undefined => {
  if (tkras2ras && worldCoords) {
    const transformMatrix = mathjs.multiply(tkras2ras.vox2ras_tkr, tkras2ras.vox2ras_inv);
    const t1Tkras = [worldCoords.x, worldCoords.y, worldCoords.z, 1];
    const t1tkRas = mathjs.multiply(transformMatrix, t1Tkras) as unknown as number[];

    return {
      x: Number(t1tkRas[0].toPrecision(4)),
      y: Number(t1tkRas[1].toPrecision(4)),
      z: Number(t1tkRas[2].toPrecision(4)),
    };
  }

  return undefined;
};

export const flatObj = (obj: CommonObj, pre = '') => {
  let res: CommonObj = {};

  for (let key in obj) {
    if (obj[key]?.toString() === '[object Object]') {
      res = { ...res, ...flatObj(obj[key], pre ? `${pre}.${key}` : key) };
    } else {
      res[pre ? `${pre}.${key}` : key] = obj[key];
    }
  }

  return res;
};

export const enFlatObj = (obj: CommonObj) => {
  let res: CommonObj = {};

  for (let key in obj) {
    if (/\./.test(key)) {
      const splitList = key.split('.');
      res[splitList[0]] = Object.assign(res[splitList[0]] || {}, enFlatObj({ [splitList.slice(1).join('.')]: obj[key] }));
    } else {
      res[key] = obj[key];
    }
  }

  return res;
};

export const getflatInfo = (str: string[], info: { [prop: string]: any }) => {
  const res: { [prop: string]: any } = {};
  for (let item of str) {
    if (/\./.test(item)) {
      const splitList = item.split('.');
      res[splitList[0]] = Object.assign(res[splitList[0]] || {}, getflatInfo([splitList.slice(1).join('.')], info[splitList[0]] || {}));
    } else {
      res[item] = info[item];
    }
  }

  return res;
};

export const saveSpotList = ['id', 'subject_id', 'name'];

export const saveStimulateList = [
  'id',
  // 'remark',
  'name',
  // 'horizontal',
  'has_mep',
  'hemi',
  'color',
  'vertex_index',
  'vol_ras.y',
  'vol_ras.x',
  'vol_ras.z',
  'surf_ras.y',
  'surf_ras.x',
  'surf_ras.z',
  // 'stimulus.id',
  'stimulus.remark',
  'stimulus.type',
  'stimulus.relative_strength',
  'stimulus.strand_pulse_frequency',
  'stimulus.inner_strand_pulse_count',
  'stimulus.plexus_inner_frequency',
  'stimulus.plexus_inter_frequency',
  'stimulus.plexus_inner_pulse_count',
  'stimulus.plexus_count',
  'stimulus.strand_pulse_count',
  'stimulus.intermission_time',
  'stimulus.pulse_total',
  'stimulus.treatment_time',
];

export const getVertexByPosition = (surfaceViewer: any, surfSeed: Coordinate, isShowAll: boolean = false) => {
  let { x, y, z } = surfSeed;
  let temp = surfaceViewer.reverseByVertexCoordstoPoint(x, y, z);
  let data = surfaceViewer.pick(temp.x, temp.y, 50);
  if (isShowAll) return isShowAll ? data : 0;
  const pialGii = surfaceViewer.model.children.find((obj: any) => obj.name === 'pial_gii_1');
  const vertices = pialGii.userData.original_data.vertices;
  // const start = (get3multiple(data.index * 3) - 9999) > 0 ? get3multiple(data.index * 3) - 9999  : 0;
  // const end = get3multiple(data.index * 3) + 9999;
  const arr = [];
  for (let i = 0; i < vertices.length; i += 3) {
    const element0 = vertices[i];
    const element1 = vertices[i + 1];
    const element2 = vertices[i + 2];
    if (
      element0 < parseFloat(`${surfSeed.x}`) + 2 &&
      element0 > parseFloat(`${surfSeed.x}`) - 2 &&
      element1 < parseFloat(`${surfSeed.y}`) + 2 &&
      element1 > parseFloat(`${surfSeed.y}`) - 2 &&
      element2 < parseFloat(`${surfSeed.z}`) + 2 &&
      element2 > parseFloat(`${surfSeed.z}`) - 2
    ) {
      const difference =
        (surfSeed.x - element0) * (surfSeed.x - element0) +
        (surfSeed.y - element1) * (surfSeed.y - element1) +
        (surfSeed.z - element2) * (surfSeed.z - element2);
      arr.push({ index: i, difference, point: { x: element0, y: element1, z: element2 } });
    }
  }
  const arrs = [...arr].sort((obj1, obj2) => obj1.difference - obj2.difference);
  if (arrs.length > 0) {
    if (data) data.index = arrs[0].index / 3;

    return isShowAll ? data : arrs[0].index / 3;
  }

  if (isShowAll && data) {
    return data;
  } else {
    return 0;
  }
};

const initPly = async (parseFile: BufferGeometry): Promise<[number[][], number[][][]]> => {
  const arr = parseFile.attributes.position.array;
  const face = parseFile.index!.array;
  const res: number[][] = [];
  const pointsList: number[][][] = [];
  for (let i = 0; i < face.length; i += 3) {
    const point_a = [arr[face[i] * 3], arr[face[i] * 3 + 1], arr[face[i] * 3 + 2]];
    const point_b = [arr[face[i + 1] * 3], arr[face[i + 1] * 3 + 1], arr[face[i + 1] * 3 + 2]];
    const point_c = [arr[face[i + 2] * 3], arr[face[i + 2] * 3 + 1], arr[face[i + 2] * 3 + 2]];
    const cur_face = mathjs.cross(
      [point_a[0] - point_b[0], point_a[1] - point_b[1], point_a[2] - point_b[2]],
      [point_c[0] - point_b[0], point_c[1] - point_b[1], point_c[2] - point_b[2]]
    ) as number[];
    const norm_face = mathjs.norm(cur_face) as number;
    const norm_list = [cur_face[0] / norm_face, cur_face[1] / norm_face, cur_face[2] / norm_face];

    res.push([...norm_list, -(point_a[0] * norm_list[0] + norm_list[1] * point_a[1] + norm_list[2] * point_a[2])]);
    pointsList.push([point_a, point_b,point_c]);
  }

  return [res, pointsList];
};

const calNormalLine = async (type: string, vol_ras: Coordinate, id: number, res: number[][], pointsList: number[][][]) => {
  const vol_matrix = [[vol_ras.x], [vol_ras.y], [vol_ras.z], [1]];
  const distance_list = mathjs.multiply(res, vol_matrix).map((v, i) => Math.abs(v[0] / (mathjs.norm([res[i][0], res[i][1], res[i][2]]) as number)));
  let distance = Infinity;
  let scalp_vol_ras: Coordinate;
  for (let i = 0; i < distance_list.length; i++) {
    if (distance_list[i] <= distance) {
      const min_distance = res[i];
      const temp = mathjs.dot(min_distance, vol_matrix) / (mathjs.norm(min_distance.slice(0, 3)) as number) ** 2;
      let temp_ras = { x: vol_ras.x - min_distance[0] * temp, y: vol_ras.y - min_distance[1] * temp, z: vol_ras.z - min_distance[2] * temp };
      if (isPointInTriangle(pointsList[i][0],pointsList[i][1],pointsList[i][2] , [temp_ras.x, temp_ras.y, temp_ras.z])) {
        distance = distance_list[i];
        scalp_vol_ras = temp_ras;
      }
    }
  }
  scalp_vol_ras = scalp_vol_ras! || ERROR_MIN_POINT;

  return { ...scalp_vol_ras, target_id: id, ver: LOCALSURFENTRYPOINT_VERSION };
};

const isPointInTriangle = (a: number[], b: number[], c: number[], p: number[]) => {
  const v0 = [c[0] - b[0], c[1] - b[1], c[2] - b[2]];
  const v1 = [a[0] - b[0], a[1] - b[1], a[2] - b[2]];
  const v2 = [p[0] - b[0], p[1] - b[1], p[2] - b[2]];
  const cross_product = mathjs.cross(v0, v1);
  const area = mathjs.norm(cross_product) as number;

  const u = mathjs.norm(mathjs.cross(v2, v1)) as number / area;
  const v = mathjs.norm(mathjs.cross(v0, v2)) as number / area;
  const w = 1 - u - v;
  if (u < 0 || v < 0 || w < 0 || u > 1 || v > 1 || w > 1) {
    return false;
  }

  return true;
};

export const beyondAllEntryPoint = async (
  volumeViewer: any,
  volumeScalpMaskIndex: number,
  spotList: any[],
  spotInfo: any,
  cb: (value: LineType[]) => void,
  convexFile?: string
) => { // NOSONAR
  return new Promise(async res => { // NOSONAR
    if (!isNaN(volumeScalpMaskIndex)) {
      const m400Api = getM200ApiInstance();
      if (convexFile) {
        const buffer = (await window.fileAPI.getFile(convexFile)) as Buffer;
        const parseFile = PLY.parse(buffer.buffer);
        const [initList, pointsList] = await initPly(parseFile);
        try {
          // eslint-disable-next-line @typescript-eslint/promise-function-async
          const line_list = (await Promise.all(spotList.map((v: any) => calNormalLine(v.type, v.vol_ras, v.id, initList, pointsList)))).filter(v => v);
          await m400Api.saveTargetLine({
            ...spotInfo,
            normal_line_list: line_list,
          });
          res(line_list);
          if (cb) {
            cb([
              {
                value: line_list,
                key: spotInfo.plan_id,
                status: LineStatusType.success,
              },
            ]);
          }
        } catch (error) {
          if (cb) {
            cb([
              {
                value: undefined,
                status: LineStatusType.error,
                key: spotInfo.plan_id,
              },
            ]);
          }
        }

        return;
      }
      let scalpVolume = {
        data: volumeViewer.volumes[volumeScalpMaskIndex].data,
        header: volumeViewer.volumes[volumeScalpMaskIndex].header,
      };
      // console.log(volSeedList)
      // @ts-ignore
      const myCalMinPointWorker = new WorkerList();
      myCalMinPointWorker.postMessage({ volume: scalpVolume, volSeedList: spotList, planId: spotInfo.plan_id });

      myCalMinPointWorker.addEventListener('message', async (e: any) => {
        try {
          await m400Api.saveTargetLine({
            ...spotInfo,
            normal_line_list: e.data.map((v: any) => ({ ...v.value, target_id: v.id })),
          });
          if (cb) {
            cb([
              {
                value: e.data.map((v: any) => ({ ...v.value, target_id: v.id })),
                status: LineStatusType.success,
                key: spotInfo.plan_id,
              },
            ]);
          }
        } catch (error) {
          if (cb) {
            cb([
              {
                value: undefined,
                status: LineStatusType.error,
                key: spotInfo.plan_id,
              },
            ]);
          }
        }
        res(e.data);
      });
    } else {
      res(undefined);
    }
  });
};

export const getSpotStatus = (spotInfo: any, index: number) => {
  const { stimulus } = spotInfo;
  const is_error = ['has_mep', 'vol_ras'].some(v => spotInfo[v] === undefined);

  if (spotInfo.has_mep) {
    return is_error;
  } else {
    const cur_key_list = getFields(stimulus, 0);
    const cur_stimulate = cloneDeep(stimulus);
    cur_key_list.forEach(v => {
      if (cur_stimulate[v.key] === undefined) cur_stimulate[v.key] = null;
    });

    const rules = calRules(cur_stimulate, 0, '');

    return is_error || rules.some(v => v.status === 'error');
  }
};

export const brainbrowserLoaderText = async (url: string) => {
  const buffer = (await window.fileAPI.getFile(url)) as unknown as any;

  const res = await new Promise((resolve: (res: any) => void) => {
    BrainBrowser.loader.loadFromFilePath(
      buffer.buffer,
      (data: string, filename: any, options: any) => {
        resolve({ data, filename });
      },
      {
        content_type: 'text',
        result_type: 'arraybuffer',
        format: 'text',
      }
    );
  });
  const dv = new DataView(res.data);
  const decoder = new TextDecoder();

  return decoder.decode(dv);
};
