import { breadCrumbType } from '../../uiComponent/NgBreadCrumb';

// export const breadcrumbList: (isEdit: boolean, cb: (() => void)) => Route[] = (isEdit, cb) => [{
//   path: '/',
//   breadcrumbName: '首页',
//   onClick: cb,
// }, {
//   path: '',
//   breadcrumbName: isEdit ? '新建无影像方案' : '治疗预览',
// }];

// 无影像刺激面包屑
export const breadcrumbList: (breadLabel: string, isTreat: boolean, cbList: (() => void)[]) => breadCrumbType[] = (breadLabel, isTreat, cbList) => {
  const routerConfig = [
    {
      path: '/home',
      breadcrumbName: '首页',
      onClick: cbList[0],
      disable: isTreat,
    },
    {
      path: '',
      breadcrumbName: breadLabel,
      disable: isTreat,
    },
  ];
  if (isTreat) {
    routerConfig.push({ path: '', breadcrumbName: isTreat ? '治疗中' : '', disable: false });
  }

  return routerConfig;
};

// 脉冲刺激面包屑
export const noPatientBreadcrumbList: (isEdit: boolean, cbList: (() => void)[]) => breadCrumbType[] = (isEdit, cbList) => {
  const routerConfig = [
    {
      path: '/home',
      breadcrumbName: '首页',
      onClick: isEdit
        ? cbList[0]
        : () => {
            // 刺激流程不能离开页面
          },
      disable: !isEdit,
    },
    {
      path: '',
      breadcrumbName: '重复刺激',
      disable: !isEdit,
    },
  ];
  if (!isEdit) {
    routerConfig.push({
      path: '/treating',
      breadcrumbName: '治疗中',
      disable: false,
      onClick: () => {
        // null
      },
    });
  }

  return routerConfig;
};
