import React from 'react';
import { useNavigate, useParams, useLocation, NavigateFunction, Location } from 'react-router';
import { v4 as uuidv4 } from 'uuid';
import styles from './index.module.less';
import { getM200ApiInstance } from '@/common/api/ngApiAgent';
import { PatientForm } from '../previewPlan/component/patientForm';
import { Spin, message } from 'antd';
import { StimulateForm } from './component/stimulateForm';
import { ReactComponent as Warning } from '@/renderer/static/svg/warning.svg';
import { breadcrumbList } from './config';
import { NgBreadcrumb } from '../../uiComponent/NgBreadCrumb';
import { calTbsChartData, disableBeatOfTemp, secToTime } from '../../component/template/calTemplate';
// eslint-disable-next-line import/no-extraneous-dependencies
import { pinyin } from 'pinyin-pro';
import dayjs from 'dayjs';
import CameraAndCoil from '../../component/cameraAndCoil';
import { Strength } from './component/strength';
import { NgIcon } from '../../uiComponent/NgIcon';
import { NgStrengthProgress } from '../../uiComponent/NgProgress';
import { TBSChart } from '../../component/tbsChart';
import { EnumPlanStimulusType, NavigateSupportTypeEnum, PlanModel } from '@/common/types';
import { PreviewTreat } from '../previewTreat';
import classNames from 'classnames';
import { authTypeState } from '../../recoil/license';
import { SetterOrUpdater, useRecoilState, useRecoilValue, useSetRecoilState } from 'recoil';
import NgDarkButton from '../../uiComponent/NgDarkButton';
import { cloneDeep, isNumber, pick, throttle } from 'lodash';
import { sendRenderLog, sendTreatLog } from '../../utils/renderLogger';
import { isPowerOver } from '../../component/template/ctbsParamsRules';
import { ErrorModel } from '../../component/systemErrorModel/errorModel';
import { treatResultMap } from '../../constant/tmsAndImageSocket';
import { TmsCoil, tmsCoilAtom } from '../../recoil/tmsError';
import NgModal from '../../uiComponent/NgModal';
import { SuccessMessage } from '../../uiComponent/SvgGather';
import NgButton from '../../uiComponent/NgButton';
import { pickParam } from '../../constant/treat';
import { TMSScreenState } from '@/common/constant/tms';
import { TreatParam } from '@/common/types/treat';
import { calRelativeStrengthRangeByActive } from '@/renderer/component/template/calRules';
import { Countdown, Pluse } from '../../component/plusecountdown';
import { initNoImageTms } from '../../utils/crashedAction';
import { isNotTreatingAtom } from '../../recoil/isNotTreating';
import { notNAVTypeList } from '../../../common/systemFault/config';
import { FaultEnum, FaultMapItemType, FaultStatusEnum } from '../../../common/systemFault/type';
import { faultAtom, getFaultWithoutType } from '../../recoil/fault';
import TreatCircleProgress from '../../component/treatCircleProgress';
import { throttleOption } from '../../utils';

type State = {
  loading: boolean;
  isTreating: boolean;
  planInfo: PlanModel | any;
  relativeStrength: number;
  importDisabled: boolean;
  backInfo: { backTime: number; backOpen: boolean };
  treatStatus: string;
  treatParam: TreatParam | null;
  treatProgress: { time: number; count: number; remain_time?: number };
  isPreview: boolean;
  strengthRange: { min: number; max: number };
  stimulus: any;
  patientName: string;
};
type Props = {
  subjectId?: string;
  planId?: string;
  navigate: NavigateFunction;
  authState: any;
  location: Location;
  tmsError: FaultMapItemType[];
  coilInfo: TmsCoil;
  setIsNotTreating: SetterOrUpdater<boolean>;
};

class PreviewNoImagePlan extends React.PureComponent<Props, State> {
  private stimulateRef: any;
  private patientRef: any;
  private m200Api: any;
  private uuid: string;
  private backIntervalRef: any;
  private treatStatusRef: any;
  private treatInterval: any;
  private btn_ing: boolean;
  private computing: boolean;

  constructor(props: Props) {
    super(props);
    this.state = {
      loading:false,
      isTreating:false,
      planInfo:{},
      relativeStrength:0,
      importDisabled:false,
      backInfo:{ backOpen:false, backTime:3 },
      treatStatus:'treat_stop',
      treatParam:null,
      treatProgress:{ time:0, count:0 },
      isPreview:/^\/previewNoImagePlan/.test(props.location.pathname),
      strengthRange:{ min:0, max:0 },
      stimulus:{ type:5 },
      patientName:'',
    };
    this.stimulateRef = React.createRef();
    this.patientRef = React.createRef();
    this.m200Api = getM200ApiInstance();
    this.uuid = '';
    this.backIntervalRef = React.createRef();
    this.treatStatusRef = React.createRef();
    this.treatStatusRef.current = 'treat_stop';
    this.treatInterval = React.createRef();
    this.btn_ing = false;
    this.computing = false;

    this.throttleSetTmsPlayData = throttle(this.throttleSetTmsPlayData.bind(this), throttleOption.wait);
  }

  async componentDidMount() {
    initNoImageTms();
    await this.init();
    await this.initSize();
    await this.handleListenerTmsPlay();
    await this.handleListenerTmsStrengthChange();
  }

  componentWillUnmount(): void {
    window.tmsAPI.remove_beat_btn_by_key('PreviewNoImagePlan');
    window.tmsAPI.remove_beat_btn_by_key('PreviewNoImagePlanStrength');
  }

  componentDidUpdate(prevProps: Readonly<Props>, prevState: Readonly<State>): void {
    if (prevState.isTreating !== this.state.isTreating) {
      this.props.setIsNotTreating(!this.state.isTreating);
      if (this.props.subjectId && this.props.planId) {
        if (!this.state.isTreating) {
          // eslint-disable-next-line @typescript-eslint/no-floating-promises
          this.init();
        }
        this.handleTmsTreatStatusPlanTreat();
      }
      if (!this.state.isTreating) {
        window.systemAPI.pushSystemFault({ '0A030005': FaultStatusEnum.normal}, 'previewNoImagePlan page 非治疗态清除tms故障');
        window.systemAPI.pushSystemFault({ '0A010002': FaultStatusEnum.normal }, 'previewNoImagePlan page 关闭只显示一次的弹窗');
        this.setState({treatProgress: { time: 0, count: 0 }});
      }
    } else {
      this.setState({ loading:false });
    }
  }

  private stopTreat = () => {
    clearInterval(this.treatInterval.current);
    this.treatStatusRef.current = 'treat_stop';
    this.setState({ treatStatus:'treat_stop' });
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    window.tmsAPI.noImage_treatment_plan_start('PlanEnd', this.uuid);
    this.setTmsTreatStatus(TMSScreenState.NotStarted);
    sendTreatLog.info('影像刺激异常结束');
  };

  private handleTmsTreatStatusPlanTreat = () => {
    if (this.state.isTreating) {
      this.setTmsTreatStatus(TMSScreenState.Preparation);
    } else {
      this.setTmsTreatStatus(TMSScreenState.NotStarted);
    }
  };

  private setTmsTreatStatus = (status: number) => {
    window.tmsAPI.set_beat_screen(status);
  };

  private checkIsTreatStart = (data: any) => this.treatStatusRef.current === 'treat_stop' && data.data.key === 'play';
  private checkIsTreatPause = (data: any) => this.treatStatusRef.current === 'treat_ing' && data.data.key === 'play';
  private checkIsTreatResume = (data: any) => this.treatStatusRef.current === 'treat_pause' && data.data.key === 'play';
  private checkIsAddStrength = (data: any) => this.treatStatusRef.current === 'treat_pause' && data.data.key === 'add';
  private checkIsSubStrength = (data: any) => this.treatStatusRef.current === 'treat_pause' && data.data.key === 'sub';

  private logErrorEndTreat = async (err: any) => {
    sendTreatLog.info('无影像刺激错误', `错误信息：${JSON.stringify(err)}`);
    if ([ 30223, 30224, 30225, 30228, 30229 ].includes(err.code)) return;
    this.treatStatusRef.current = 'treat_stop';
    window.systemAPI.pushSystemFault({ '0A010002': FaultStatusEnum.abnormal }, 'previewNoImagePlan page 请求接口错误');
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    await window.tmsAPI.noImage_treatment_plan_start('PlanEnd', this.uuid);
  };

  private beginSendTreatLog = async () => {
    const stimulate_info = this.stimulateRef.current.form.getFieldsValue();
    const { planInfo, treatParam } = this.state;
    if (!planInfo || !treatParam) return;
    const param = {
      navigate_support_type:NavigateSupportTypeEnum.NOT_APPLICABLE,
      uuid:this.uuid,
      plan_id:planInfo.stimulus.plan_id,
      subject_id:planInfo.subject_id,
      plan_stimulus_id:planInfo.stimulus.id,
      planned_stimulation_duration:treatParam.time,
      pulse_total:treatParam.sum,
      motion_threshold:planInfo.subject_model.motion_threshold,
      stimulus_data:{
        type:stimulate_info?.type,
        relative_strength:stimulate_info?.relative_strength,
        actual_strength:Math.min(100, Math.round((stimulate_info?.relative_strength * planInfo.subject_model.motion_threshold) / 100)),
        strand_pulse_frequency:stimulate_info?.strand_pulse_frequency,
        inner_strand_pulse_count:stimulate_info?.inner_strand_pulse_count,
        plexus_inner_frequency:stimulate_info?.plexus_inner_frequency,
        plexus_inter_frequency:stimulate_info?.plexus_inter_frequency,
        plexus_inner_pulse_count:stimulate_info?.plexus_inner_pulse_count,
        plexus_count:stimulate_info?.plexus_count,
        strand_pulse_count:stimulate_info?.strand_pulse_count,
        intermission_time:stimulate_info?.intermission_time,
        pulse_total:treatParam.sum,
      },
    };
    try {
      sendTreatLog.info('无影像刺激开始', `刺激参数：${JSON.stringify(param)}`);
      await this.m200Api.beginTreat(param);
    } catch (err) {
      await this.logErrorEndTreat(err);
    }

    return Promise.resolve();
  };

  private pauseSendTreatLog = async () => {
    try {
      await this.m200Api.pauseTreat({ uuid:this.uuid });
      sendTreatLog.info('无影像刺激暂停');
    } catch (err) {
      await this.logErrorEndTreat(err);
    }

    return Promise.resolve();
  };

  private resumeSendTreatLog = async () => {
    if (!this.state.treatParam) return;
    try {
      await this.m200Api.continueTreat({ uuid:this.uuid, actual_strength:this.state.treatParam.level });
      sendTreatLog.info('无影像刺激继续');
    } catch (err) {
      await this.logErrorEndTreat(err);
    }

    return Promise.resolve();
  };

  private messageWarning = async () => {
    return message.warning({
      content:'系统异常，请重试',
      key:'result',
    });
  };

  private treatment_plan_resume = async () => {
    const data = await window.tmsAPI.noImage_treatment_plan_start('PlanResume', this.uuid);
    if (data.code === 0) {
      if (data.data.result === 0) {
        this.setState({ treatStatus:'treat_ing' });
        this.treatStatusRef.current = 'treat_ing';

        return true;
      } else {
        sendTreatLog.info('无影像继续刺激出现故障', treatResultMap[data.data.result]);
        await this.messageWarning();

        return false;
      }
    } else {
      await message.error(data.message || 'tms通讯出错');

      return false;
    }
  };

  private handleTmsPlayData = async (data: any) => {
    if (getFaultWithoutType(FaultEnum.imageFault).length) return;
    const { isTreating, planInfo } = this.state;
    const { coilInfo } = this.props;
    if (!isTreating || this.btn_ing || this.computing || !planInfo?.subject_model?.motion_threshold) return;
    if (this.checkIsTreatStart(data)) {
      this.btn_ing = true;
      if (await this.treatment_plan_start()) {
        this.setTmsTreatStatus(TMSScreenState.PlanTreat);
        await this.beginSendTreatLog();
      }
      this.btn_ing = false;

      return;
    }
    if (this.checkIsTreatPause(data)) {
      this.btn_ing = true;
      if (await this.treatment_plan_pause()) {
        this.setTmsTreatStatus(TMSScreenState.PlanSuspend);
        await this.pauseSendTreatLog();
      }
      this.btn_ing = false;

      return;
    }
    if (this.checkIsTreatResume(data)) {
      this.btn_ing = true;
      const coil_max_temperature = coilInfo?.coil_max_temperature;
      if (typeof coil_max_temperature !== 'number') return;
      if (coil_max_temperature > 36) {
        this.btn_ing = false;

        return message.warning({
          content:'存在超温风险，请降温后再试',
        });
      }
      if (await this.treatment_plan_resume()) {
        this.setTmsTreatStatus(TMSScreenState.PlanTreat);
        await this.resumeSendTreatLog();
      }
      this.btn_ing = false;
    }

    return;
  };

  /**
   * 添加拍子节流
   */
  private throttleSetTmsPlayData = (data: any) => {
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    this.handleTmsPlayData(data);
  };

  private handleListenerTmsPlay = async () => {  // NOSONAR
    await window.tmsAPI.beat_btn_by_key('PreviewNoImagePlan', async (_: any, data: any): Promise<any> => {  // NOSONAR
      this.throttleSetTmsPlayData(data);
    });
  };

  private handleListenerTmsStrengthChange = async () => {
    await window.tmsAPI.beat_btn_by_key('PreviewNoImagePlanStrength', async (_: any, data: any): Promise<any> => { // NOSONAR
      if (getFaultWithoutType(FaultEnum.imageFault).length) return;
      const { isTreating, treatParam, planInfo, strengthRange, stimulus } = this.state;
      if (!isTreating || !planInfo || !treatParam || !planInfo?.subject_model?.motion_threshold) return;
      const newParam = cloneDeep(treatParam);
      const newPlanInfo = cloneDeep(planInfo);
      if (!newParam.level) return;
      if (this.checkIsAddStrength(data)) {
        if (newParam.level === strengthRange.max) return;
        let add_level = newParam.level + ((data.data.step as number) || 1);
        if (add_level > strengthRange.max) {
          add_level = strengthRange.max;
        }
        const relative_strength = Math.min(Math.round(add_level / planInfo?.subject_model?.motion_threshold * 100), 150);
        newPlanInfo.stimulus.relative_strength = relative_strength;
        newParam.level = add_level;
        // eslint-disable-next-line @typescript-eslint/no-floating-promises
        window.tmsAPI.set_treatment_level(newParam.level, relative_strength).then(async levelData => {
          if (levelData.code !== 0 || levelData.data.result !== 0) {
            sendTreatLog.info('无影像刺激增加强度的返回值', levelData);
            await this.messageWarning();
          }
        });
        this.setState({ treatParam:newParam, planInfo:newPlanInfo, stimulus:{ ...stimulus, relative_strength } });
      }
      if (this.checkIsSubStrength(data)) {
        if (newParam.level === strengthRange.min) return;
        let sub_level = newParam.level - ((data.data.step as number) || 1);
        if (sub_level < strengthRange.min) {
          sub_level = strengthRange.min;
        }
        const relative_strength = Math.max(Math.round(sub_level / planInfo?.subject_model?.motion_threshold * 100), 0);
        newPlanInfo.stimulus.relative_strength = relative_strength;
        newParam.level = sub_level;
        // eslint-disable-next-line @typescript-eslint/no-floating-promises
        window.tmsAPI.set_treatment_level(newParam.level, relative_strength).then(async levelData => {
          if (levelData.code !== 0 || levelData.data.result !== 0) {
            sendTreatLog.info('无影像刺激降低强度的返回值', levelData);
            await this.messageWarning();
          }
        });
        this.setState({ treatParam:newParam, planInfo:newPlanInfo, stimulus:{ ...stimulus, relative_strength } });
      }
    });
  };

  private initSize = async () => {
    const size = (await this.m200Api.getStimulusTemplateList({ page_num:1, page_size:1 })).records.length;
    this.setState({ importDisabled:size === 0 });
  };

  private init = async () => {
    const { subjectId, planId } = this.props;
    if (!(subjectId && planId)) return;
    const res = await this.m200Api.getPlanById(parseInt(planId, 10), parseInt(subjectId, 10));
    res.subject_model.birth_date = res.subject_model.birth_date && dayjs(res.subject_model.birth_date);
    // res.subject_model.motion_threshold = 5;
    this.patientRef.current?.setFieldsValue(res.subject_model);
    this.setState({
      planInfo:res,
      relativeStrength:res.stimulus?.relative_strength,
      loading:false,
      stimulus:res.stimulus || { type:5 },
      patientName:res.subject_model.name,
    });
  };

  private handleClickBread = () => {
    if (this.state.isTreating) return;
    this.props.navigate('/home');
  };

  private handleRelativeStrengthChange = (value: any) => {
    const { stimulus } = this.state;
    const clonestimulus = { ...stimulus };
    clonestimulus.relative_strength = value;
    this.setState({ relativeStrength:value, stimulus:clonestimulus });
  };

  // 结束刺激log
  private endSendTreatLog = (type: number) => {
    const { stimulus } = this.state;
    const { relative_strength } = stimulus;
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    this.m200Api.endTreat({ uuid:this.uuid, type, relative_strength });
  };

  private treatment_plan_start = async () => { // NOSONAR
    const { treatParam } = this.state;
    if (!treatParam) return;
    const data = await window.tmsAPI.noImage_treatment_plan_start('PlanStart', this.uuid);
    if (data.code === 0) {
      if (data?.data.result === 0) {
        this.treatStatusRef.current = 'treat_ing';
        this.setState({ treatStatus:'treat_ing' });
        this.treatInterval.current = setInterval(async () => {
          if (!this.state.isTreating) {
            clearInterval(this.treatInterval.current);

            return;
          }
          const queryData: any = await window.tmsAPI.query_treatment();
          this.setState({ treatProgress:{ time:queryData.data.time, count:queryData.data.count, remain_time:queryData.data.remain_time } });
          // 判断上位机与下位机状态是否一致
          if (treatParam.sum && queryData.data.count < treatParam.sum && queryData.data.state === 0 && this.treatStatusRef.current !== 'treat_stop') {
            this.treatStatusRef.current = 'treat_stop';
            clearInterval(this.treatInterval.current);
            const { stimulus } = this.state;
            const { relative_strength } = stimulus;
            // eslint-disable-next-line @typescript-eslint/no-floating-promises
            this.m200Api.endTreat({ uuid:this.uuid, type:1, relative_strength });
            sendTreatLog.info('无影像刺激异常结束');
            // eslint-disable-next-line @typescript-eslint/no-floating-promises
            window.systemAPI.pushSystemFault({ '0A030005': FaultStatusEnum.abnormal}, 'previewNoImagePlan page 判断上下危机状态不一致');
          }
          // 刺激结束
          if (queryData.data.result === 0 && queryData.data.state === 0 && queryData.data.count === treatParam.sum) {
            // 自动结束
            this.endSendTreatLog(2);
            this.startBackInterval();
            clearInterval(this.treatInterval.current);
          }
        }, 1000);

        return true;
      } else {
        sendTreatLog.info('无影像刺激开始出现故障', treatResultMap[data.data.result]);
        await this.messageWarning();

        return false;
      }
    } else {
      await message.warning(data.message || 'tms通讯出错');

      return false;
    }
  };

  private treatment_plan_pause = async () => {
    const data = await window.tmsAPI.noImage_treatment_plan_start('PlanPause', this.uuid);
    if (data.code === 0) {
      if (data.data.result === 0) {
        this.setState({ treatStatus:'treat_pause' });
        this.treatStatusRef.current = 'treat_pause';

        return true;
      } else {
        sendTreatLog.info('无影像暂停刺激出现故障', treatResultMap[data.data.result]);
        await this.messageWarning();

        return false;
      }
    } else {
      await message.error(data.message || 'tms通讯出错');

      return false;
    }
  };

  private handleTreatEnd = async (endType?: number) => { // NOSONAR
    const { treatStatus } = this.state;
    if (treatStatus !== 'treat_stop') {
      clearInterval(this.treatInterval.current);
      this.treatStatusRef.current = 'treat_stop';
      this.setState({
        treatStatus:'treat_stop',
        isTreating:endType ? this.state.isTreating : false,
        treatParam:null,
        treatProgress:{ time:0, count:0 },
      });
      this.endSendTreatLog(endType || 1);
      const data = await window.tmsAPI.noImage_treatment_plan_start('PlanEnd', this.uuid);
      if (data.code === 0) {
        if (data.data.result !== 0) {
          sendTreatLog.info('影像继续刺激出现故障', treatResultMap[data.data.result]);
          await this.messageWarning();
        }
      } else {
        await message.error(data.message || 'tms通讯出错');
      }
    } else {
      clearInterval(this.treatInterval.current);
      this.setState({
        isTreating:endType ? this.state.isTreating : false,
        treatParam:null,
        treatProgress:{ time:0, count:0 },
      });
    }
    await this.init();
  };

  private treatComplete = () => {
    this.setState({
      backInfo:{ backOpen:false, backTime:3 },
      isTreating:false,
      treatStatus:'treat_stop',
      treatParam:null,
      treatProgress:{ time:0, count:0 },
    }, () => {
      this.computing = false;
    });
    // eslint-disable-next-line
    window.tmsAPI.noImage_treatment_plan_start('PlanEnd', this.uuid);
    clearInterval(this.backIntervalRef.current);
    this.treatStatusRef.current = 'treat_stop';
  };

  private startBackInterval = () => {
    this.computing = true;
    let i = 3;
    this.setState({ backInfo:{ ...this.state.backInfo, backOpen:true } });
    this.backIntervalRef.current = setInterval(() => {
      i--;
      this.setState({ backInfo:{ ...this.state.backInfo, backTime:i } });
      if (i === 0) {
        clearInterval(this.backIntervalRef.current);
        this.treatComplete();
      }
    }, 1000);
  };

  private setFormNull = async () => {
    const values = this.stimulateRef.current.form.getFieldsValue();
    const obj = {};
    for (const key in values) {
      if (Object.prototype.hasOwnProperty.call(values, key)) {
        const item = values[key];
        if ([ undefined, null ].includes(item) && (key !== 'intermission_time' || (values.strand_pulse_count !== 1 && key === 'intermission_time'))) {
          obj[key] = null;
        } else {
          obj[key] = values[key];
        }
      }
    }
    this.stimulateRef.current.form.setFieldsValue(obj);
    this.setState({ stimulus:obj });
    await new Promise(async res => {
      setTimeout(async () => {
        return res('');
      }, 100);
    });
  };

  private handleSubmit = async (goHome?: boolean) => {
    try {
      const { planInfo, stimulus } = this.state;
      if (!planInfo) return;
      const { subjectId, planId, navigate } = this.props;
      await this.patientRef.current?.validateFields();
      const values = { ...stimulus };
      // const values = this.stimulateRef.current.form.getFieldsValue();
      const value_keys = Object.keys(values);
      let is_empty: boolean = false;
      if (value_keys.filter(v => !isNumber(values[v])).length === value_keys.length - 1) {
        is_empty = true;
      } else {
        await this.setFormNull();
        await this.stimulateRef.current.form.validateFields();
      }
      this.setState({ loading:true });
      const patient_info = this.patientRef.current?.getFieldsValue();
      // const stimulate_info = this.stimulateRef.current.form.getFieldsValue();
      const { treatment_time = 0, pulse_total = 0 } = calTbsChartData(values);
      values.treatment_time = treatment_time;
      values.pulse_total = pulse_total;
      let pinyin_value = pinyin(patient_info.name, { toneType:'none', type:'array' });
      if (planInfo && planInfo.subject_model?.pinyin_username !== patient_info.name) {
        patient_info.pinyin_username = pinyin_value.flat().join('');
      } else {
        patient_info.pinyin_username = planInfo?.subject_model?.pinyin_username;
      }
      patient_info.birth_date = patient_info.birth_date && dayjs(patient_info.birth_date).startOf('days').valueOf();
      const params: any = {
        stimulus:is_empty ? undefined : pick(values, pickParam),
        clear_stimulus:is_empty,
        subject_model:patient_info,
        name:planInfo?.name || '',
        // id: planId? parseInt(planId, 10): undefined,
        subject_id:subjectId ? parseInt(subjectId, 10) : undefined,
      };
      if (planId) {
        params.id = planId;
        params.subject_model.code = undefined;
        params.subject_model.id = subjectId;
        const editInfo = await this.m200Api.editPlan(params);
        this.setState({
          patientName:editInfo.subject_model.name,
        });
      } else {
        await this.m200Api.importNoImagePlan(params);
      }
      if (goHome) {
        navigate('/home');
      }
    } catch (err) {
      //
    } finally {
      this.setState({
        loading:false,
      });
    }
  };

  private getEnsureDisable = (): boolean => {
    const { tmsError } = this.props;

    return !!tmsError.length;
  };

  private getMinAndMaxStrength = () => {
    const { planInfo } = this.state;
    const stimulate_info = this.stimulateRef.current.form.getFieldsValue();
    if (!stimulate_info?.relative_strength || !planInfo?.subject_model?.motion_threshold) return;
    const cloneStimulate = cloneDeep(stimulate_info);
    let level = cloneStimulate?.relative_strength;
    if (!cloneStimulate || !level || !planInfo?.subject_model?.motion_threshold) return;
    const active_strength = Math.round((stimulate_info?.relative_strength * planInfo?.subject_model?.motion_threshold) / 100);
    let run = true;
    let added_level = 0;
    while (run) {
      added_level = Math.ceil((cloneStimulate.relative_strength * planInfo?.subject_model?.motion_threshold) / 100);
      run = isPowerOver(cloneStimulate, added_level);
      cloneStimulate.relative_strength = (cloneStimulate.relative_strength as number) + 1;
    }
    sendRenderLog.info(`功率超限值：${added_level - 1}`);
    const { min:minRange, max:maxRange } = calRelativeStrengthRangeByActive(
      active_strength,
      added_level - 1
    );
    const maxByActivity = Math.ceil(150 * planInfo?.subject_model?.motion_threshold / 100);
    this.setState({
      strengthRange:{
        max:Math.min(maxRange, maxByActivity),
        min:minRange,
      },
    });
  };

  private rTbsTreat = async (stimulate_info: any) => {
    const { planInfo } = this.state;
    const { pulse_total, treatment_time } = PreviewTreat.dealUndefine(calTbsChartData(stimulate_info));
    if (isNaN(pulse_total) || !planInfo?.subject_model?.motion_threshold) return;
    const param = {
      action:'set_treatment_plan',
      level:Math.min(Math.round((stimulate_info?.relative_strength * planInfo?.subject_model?.motion_threshold) / 100), 100),
      frequency:(stimulate_info.strand_pulse_frequency as number) * 100, // 1
      intensity:(stimulate_info.strand_pulse_frequency as number) * 100,
      count:1,
      bunch:stimulate_info.inner_strand_pulse_count,
      series:stimulate_info.strand_pulse_count,
      pause:stimulate_info.intermission_time,
      sum:pulse_total,
      time:treatment_time,
      tid:this.uuid,
    };
    if (param.series === 1) {
      param.pause = 1;
    }
    sendTreatLog.info('无影像设置的tbs参数', param);
    const data = await window.tmsAPI.set_treatment_plan(param, true);
    if (data.code === 0) {
      sendTreatLog.info('影像设置的tbs参数成功');
      if (data.data?.result === 0) {
        this.setState({ treatParam:param, isTreating:true });
      } else {
        sendTreatLog.info('无影像设置tbs参数出现故障', treatResultMap[data.data.result]);
        await this.messageWarning();
      }
    } else {
      await message.warning({
        content:data.message || 'tms通讯出错',
        key:'tms_error',
      });
    }
  };

  private setTBSParam = async (stimulate_info: any) => {
    const { planInfo } = this.state;
    const { pulse_total, treatment_time } = PreviewTreat.dealUndefine(calTbsChartData(stimulate_info));
    if (isNaN(pulse_total) || !planInfo?.subject_model?.motion_threshold) return;
    const param = {
      action:'set_treatment_plan',
      level:Math.min(Math.round((stimulate_info?.relative_strength * planInfo?.subject_model?.motion_threshold) / 100), 100),
      frequency:Math.round((stimulate_info.plexus_inner_frequency as number) * 100),
      intensity:Math.round((stimulate_info.plexus_inter_frequency as number) * 100),
      count:stimulate_info.plexus_inner_pulse_count,
      bunch:stimulate_info.plexus_count,
      series:stimulate_info.strand_pulse_count,
      pause:stimulate_info.intermission_time,
      sum:pulse_total,
      time:treatment_time,
      tid:this.uuid,
    };
    if (param.series === 1) {
      param.pause = 1;
    }
    sendTreatLog.info('无影像设置的rtms参数', param);
    const data = await window.tmsAPI.set_treatment_plan(param, true);
    if (data.code === 0) {
      if (data.data.result === 0) {
        sendTreatLog.info('影像设置的rtms参数成功');
        this.setState({ treatParam:param, isTreating:true });
      } else {
        sendTreatLog.info('无影像设置rtms参数出现故障', treatResultMap[data.data.result]);
        await this.messageWarning();
      }
    } else {
      await message.error({
        content:data.message || 'tms通讯出错',
        key:'tms_error',
      });
    }
  };

  private handleSumbitAndTreat = async (): Promise<any> => {
    const { coilInfo } = this.props;
    const { stimulus, planInfo } = this.state;
    await this.setFormNull();
    await this.stimulateRef.current.form.validateFields();
    await this.patientRef.current.validateFields();
    const stimulate_info = stimulus;
    if (typeof coilInfo?.coil_max_temperature !== 'number' || !planInfo?.subject_model?.motion_threshold) return;
    if (coilInfo?.coil_max_temperature > 20) {
      const disableTreat = disableBeatOfTemp(
        coilInfo?.coil_max_temperature || 0,
        Math.min(Math.round((stimulate_info?.relative_strength * planInfo?.subject_model?.motion_threshold) / 100), 100),
        stimulate_info
      );
      if (disableTreat) {
        return message.warning({
          content:'存在超温风险，请降温后再试',
        });
      }
    }
    this.uuid = uuidv4();
    await this.handleSubmit();
    if (stimulate_info.type === EnumPlanStimulusType.RTMS) {
      await this.rTbsTreat(stimulate_info);
    }
    if (stimulate_info.type === EnumPlanStimulusType.TBS) {
      await this.setTBSParam(stimulate_info);
    }
    this.getMinAndMaxStrength();
  };

  private handleErrorModalOK = () => {
    this.setState({ isTreating:false });
  };

  private updateStimulate = (stimulate: any) => {
    this.setState({
      relativeStrength:stimulate.relative_strength,
      stimulus:stimulate,
    });
  };

  private getRemainTime = (): number => {
    const { treatProgress, treatParam } = this.state;
    const remain_time = typeof treatProgress.remain_time !== 'undefined' ? treatProgress.remain_time : (treatParam?.time || 0) - treatProgress.time;

    return remain_time || 0;
  };

  private getBreadLabel = () => {
    const { planId } = this.props;
    const { isPreview } = this.state;
    if (!planId) {
      return '新建无影像方案';
    } else {
      if (isPreview) {
        return '方案信息';
      } else {
        return '治疗预览';
      }
    }
  };

  render(): React.ReactNode { // NOSONAR
    const {
      loading,
      isTreating,
      treatStatus,
      isPreview,
      importDisabled,
      planInfo,
      relativeStrength,
      treatProgress,
      treatParam,
      strengthRange,
      backInfo,
      stimulus,
    } = this.state;
    const { planId, authState } = this.props;
    const breadLabel = this.getBreadLabel();

    return (
      <Spin spinning={loading} wrapperClassName={styles.loading}>
        <div className={classNames(styles.container, 'no_img_plan')}>
          <header className={styles.header}>
            <NgBreadcrumb items={breadcrumbList(breadLabel, isTreating, [ this.handleClickBread ])} isGray={false}/>
            <CameraAndCoil/>
          </header>
          {isTreating && <div className={styles.patient_name}>患者姓名：{this.state.patientName}</div>}
          <div className={styles.info_content}>
            <div>
              <div className={styles.content}>
                <div className="stimulate" style={isTreating ? { position:'absolute', zIndex:-1 } : {}}>
                  <div className={styles.patient_container}>
                    <PatientForm formRef={this.patientRef} isPreview={planId !== undefined} itemStatus={authState['previewPlan.pageItem']}/>
                  </div>
                  <div className={styles.stimulate_container} data-preview={isPreview}>
                    <StimulateForm
                      itemStatus={authState['previewPlan.pageItem']}
                      iconDisabled={importDisabled}
                      ref={this.stimulateRef}
                      formRef={this.stimulateRef}
                      stimulate={this.state.stimulus}
                      motion_threshold={planInfo?.subject_model?.motion_threshold || 0}
                      handleRelativeStrengthChange={this.handleRelativeStrengthChange}
                      updateStimulate={this.updateStimulate}
                      isPreview={isPreview}
                    />
                  </div>
                  {!isPreview && (
                    <div className={styles.strength_container}>
                      <Strength
                        relativeStrength={relativeStrength}
                        motion_threshold={planInfo?.subject_model?.motion_threshold || 0}
                        stimulate={this.state.stimulus}
                        onChange={this.handleRelativeStrengthChange}
                      />
                    </div>
                  )}
                </div>
                {isTreating && (
                  <div className="treat_info" data-treat={isTreating}>
                    <div className={styles.pregress_container}>
                      {treatStatus === 'treat_pause' && (
                        <div className="treat_pause">
                          <NgIcon isPreview style={{ marginRight:'9px' }} fontSize={16} iconSvg={Warning}/>
                          脉冲暂停
                        </div>
                      )}
                      {treatStatus !== 'treat_pause' && <div className="treat_pause"/>}
                      <TreatCircleProgress percent={Math.round((treatProgress.count / (treatParam?.sum || 0)) * 100)} strokeWidth={20} size={180} isTreating={treatStatus === 'treat_ing'} />
                      <Countdown status={treatStatus} time={secToTime(this.getRemainTime())}/>
                      <Pluse status={treatStatus} total={treatParam?.sum || 0} count={treatProgress?.count}/>
                    </div>
                    <div className={styles.treatment_contianer}>
                      <p className={styles.title}>
                        <span className={styles.label}>相对强度（%MT）：</span>
                        <span className={classNames(styles.value, `strength_${treatStatus}`)}>
                          {stimulus?.relative_strength}
                        </span>
                      </p>
                      <p className={classNames(styles.title, `threshold_${treatStatus}`)}>
                        <span className={styles.label}>运动阈值（%MO）：</span>
                        <span className={styles.value}>{planInfo?.subject_model?.motion_threshold}</span>
                      </p>
                      <p className={classNames(styles.title, `truth_${treatStatus}`)}>
                        <span className={styles.label}>实际强度（%MO）：</span>
                        {treatStatus !== 'treat_pause' && (
                          <span className={styles.value}>
                            {treatParam?.level}
                          </span>
                        )}
                        {treatStatus === 'treat_pause' && (
                          <div className={styles.strength_progress}>
                            <NgStrengthProgress
                              percent={treatParam?.level || 0}
                              min_value={strengthRange.min}
                              max_value={strengthRange.max}
                              radius={60}
                            />
                          </div>
                        )}
                        {/* <span className={styles.value}>{planInfo.stimulus?.relative_strength * planInfo?.subject_model?.motion_threshold / 100}</span> */}
                      </p>
                      {stimulus && (
                        <TBSChart
                          className={styles.chart}
                          template={stimulus}
                          motionThreshold={planInfo?.subject_model?.motion_threshold || 0}
                        />
                      )}
                    </div>
                  </div>
                )}
              </div>
              <div className={styles.botton_footer}>
                {isPreview ? (
                  <NgDarkButton className={styles.spot_cancel} authName="noImagePlan.submit" onClick={async () => this.handleSubmit(true)}>
                    保存
                  </NgDarkButton>
                ) : (
                  <>
                    {isTreating ? (
                      <NgDarkButton className={styles.spot_cancel} onClick={async () => this.handleTreatEnd()}>
                        结束
                      </NgDarkButton>
                    ) : (
                      <NgDarkButton className={styles.spot_cancel} disabled={this.getEnsureDisable()} onClick={this.handleSumbitAndTreat}>
                        确认
                      </NgDarkButton>
                    )}
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
        {isTreating && <ErrorModel onOpen={this.stopTreat} faultTypeList={notNAVTypeList} isStimulate onOk={this.handleErrorModalOK}/>}
        <NgModal
          open={backInfo.backOpen}
          footer={<React.Fragment/>}
          closeIcon={<React.Fragment/>}
          closable={false}
          className="back_modal"
          width={400}
          centered
          getContainer={() => document.querySelector('.no_img_plan') || document.body}
        >
          <NgIcon fontSize={34} iconSvg={SuccessMessage}/>
          <div className="treat_complete_text">本次治疗完成</div>
          <div className="auto_hide_text">{backInfo.backTime}秒后自动消失</div>
          <NgButton onClick={this.treatComplete} className="back_button">
            返回
          </NgButton>
        </NgModal>
      </Spin>
    );
  }
}

const Wrap = () => {
  const { subjectId, planId } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const [ authState ] = useRecoilState(authTypeState);
  const coilInfo = useRecoilValue(tmsCoilAtom);
  const [fault] = useRecoilState(faultAtom);
  const setIsNotTreating = useSetRecoilState(isNotTreatingAtom);

  return (
    <PreviewNoImagePlan
      subjectId={subjectId}
      planId={planId}
      navigate={navigate}
      location={location}
      tmsError={getFaultWithoutType(FaultEnum.imageFault, fault)}
      authState={authState}
      setIsNotTreating={setIsNotTreating}
      coilInfo={coilInfo}
    />
  );
};

export default Wrap;
