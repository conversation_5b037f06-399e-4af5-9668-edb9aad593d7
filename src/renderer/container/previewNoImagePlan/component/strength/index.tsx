import React, { useEffect } from 'react';
import { NgInputNumber } from '../../../../uiComponent/NgInputNumber';
import styles from './index.module.less';
import { NgStrengthProgress } from '../../../../uiComponent/NgProgress';
import { calRelativeStrength } from '../../../../component/template/calRules';
import { ParamRulesType, cTBSParamRules } from '../../../../component/template/ctbsParamsRules';
import { getMathRoundMotionThreshold } from '../../../../component/template/calTemplate';

type Props = {
  motion_threshold: number;
  relativeStrength: number;
  onChange(value: any): void;
  stimulate: any;
};
export const Strength: React.FC<Props> = (props: Props) => {
  const [relativeStatus, setRelativeStatus] = React.useState<'error' | 'warning' | undefined>(undefined);
  const [relativeValue, setRelativeValue] = React.useState<{ min: number; max: number }>({ min: 0, max: 0 });
  const relativeValueRef = React.useRef({ min: 0, max: 0 });
  const [powerOver, setPowerOver] = React.useState(false);

  useEffect(() => {
    if (!props.motion_threshold) return;
    const { min, max } = calRelativeStrength(props.motion_threshold);
    setRelativeValue({ min, max });
    relativeValueRef.current = { min, max };
  }, [props.motion_threshold]);

  useEffect(() => {
    if (relativeValue.max === 0) return;
    let _powerOver = false;
    let status;
    const notComplete = Object.keys(props.stimulate).find(key => [undefined, null].includes(props?.stimulate?.[key]));
    if (typeof props.relativeStrength !== 'number' ||
      props.relativeStrength > relativeValue.max ||
      props.relativeStrength < relativeValue.min ||
      props.relativeStrength !== Math.round(props.relativeStrength)
    ) {
      status = 'error';
    }
    if (
      !notComplete &&
      cTBSParamRules(props.stimulate, getMathRoundMotionThreshold(props.motion_threshold, props.stimulate)) === ParamRulesType.ParamRulesError
    ) {
      status = 'error';
      _powerOver = true;
    }
    setPowerOver(_powerOver);
    setRelativeStatus(status as any);
  }, [props.relativeStrength, relativeValue, props.stimulate]);

  const getErrorInfo = () => {
    const strength = props?.relativeStrength as any;
    if (relativeStatus !== 'error') return '';
    if ([null, undefined].includes(props?.relativeStrength as any)) return '不可为空';
    if (typeof strength !== 'number' || strength > relativeValueRef.current.max || strength < relativeValueRef.current.min) return '不符合限制';
    if (powerOver) return '功率超限';

    return '不符合限制';
  };

  return (
    <div className={styles.strength_box}>
      <div>强度</div>
      <div className="strength">
        <div className="item relative_strength_content">
          <span className='relative_strength'>相对强度（%MT）：</span>
          <div style={{ width: 130 }}>
            <NgInputNumber status={relativeStatus} onChange={props.onChange} value={props.relativeStrength} />
            <div className="strength_level">
              <span className="error_info">{getErrorInfo()}</span>
              <span className="level">
                {relativeValue.min}-{relativeValue.max}
              </span>
            </div>
          </div>
        </div>
        <div className="item">
          <span className='motion_threshold'>运动阈值（%MO）：</span>
          <span style={{ width: 130 }}>{props.motion_threshold}</span>
        </div>
        <div className="item">
          <span className='active_strength'>实际强度（%MO）：</span>
          <span className="strength_progress" style={{ width: 130 }}>
            <NgStrengthProgress percent={Math.min(Math.round((props.motion_threshold * (props.relativeStrength || 0)) / 100), 100)} radius={60} />
          </span>
        </div>
      </div>
    </div>
  );
};
