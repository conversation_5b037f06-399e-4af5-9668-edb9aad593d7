@import '../../../../static/style/baseColor.module.less';
.strength_box {
  :global {
    width: 100%;
    height: 100%;
    .strength {
      margin-top: 25px;
      .item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 25px;
        .strength_progress {
          position: relative;
          z-index: 2;
          & > div {
            transform: unset;
          }
        }
        .relative_strength,.motion_threshold,.active_strength{
          color: @colorA9;
        }
      }
      .relative_strength_content {
        align-items: unset;
        margin-bottom: 5px;
        .relative_strength{
          padding-top: 6px;
        }
        .title {
          height: 20px;
          margin-top: 7px;
        }
        .strength_level {
          margin-top: 3px;
          width: 100%;
          display: flex;
          justify-content: space-between;
          .error_info {
            display: inline-block;
            width: auto;
            font-size: 12px;
            color: @colorB3;
          }
          .level {
            color: @colorA9;
          }
        }
      }
    }
  }
}
