import React, { useState } from 'react';
import styles from './index.module.less';
import { EditStimulateTemplate, TbsFieldType } from '../../../../component/template';
import { TBSChart } from '../../../../component/tbsChart';
import { ImportTemplate as ImportIcon, StimulateImportDisabled } from '../../../../uiComponent/SvgGather';
import { NgIcon } from '../../../../uiComponent/NgIcon';
import { ImportTemplate } from '../../../../component/importTemplate';
import { AuthEnum } from '../../../../utils/authConfig';
import { NgForm } from '@/renderer/uiComponent/NgForm';
import { Form } from 'antd';
import { getRenderFields } from '../../../stimulateTemplate/component/editTemplateCard';

type Props = {
  formRef: any;
  stimulate: any;
  iconDisabled: boolean;
  itemStatus?: AuthEnum;
  motion_threshold: number;
  handleRelativeStrengthChange(v: number): void;
  updateStimulate(stimulate: any): void;
  isPreview: boolean;
};

export const StimulateForm = React.forwardRef((props: Props, ref) => {
  const [form] = Form.useForm();
  const { iconDisabled } = props;
  const [importVisible, setImportVisible] = useState<boolean>(false);

  React.useImperativeHandle(ref, () => {
    return {
      form,
    };
  });

  const handleValueChange = (changeValues: any, values: any) => {
    let key = Object.getOwnPropertyNames(changeValues)[0];
    let value = changeValues[key];
    let template = values;
    if (changeValues.type) {
      // 修改了刺激类型, 重置表单
      let renderFields: TbsFieldType[] = getRenderFields(value).filter(item => item.key !== 'relative_strength');
      template = renderFields.reduce((result: any, item: TbsFieldType) => {
        return { ...result, [item.key]: undefined };
      }, {});
      template.type = value;
      if (!props.isPreview) {
        template.relative_strength = props.stimulate.relative_strength;
      } else {
        template.relative_strength = undefined;
      }
    }
    if (key === 'strand_pulse_count' && changeValues[key] === 1) {
      template.intermission_time = undefined;
    }
    props.updateStimulate(template);
  };

  const handleCancelImport = () => {
    setImportVisible(false);
  };

  const handleImport = (stimulate_static: any) => {
    props.updateStimulate(stimulate_static);
    setImportVisible(false);
  };

  // console.log(props.stimulate,'stimulate');

  return (
    <>
      <NgForm
        // formRef={props.formRef}
        form={form}
        layout="horizontal"
        labelAlign={'left'}
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 16 }}
        onValuesChange={handleValueChange}
        className={styles.container}
      >
        <p className={styles.chart_title}>
          <span>参数</span>
          <NgIcon
            tooltip={{
              title: !iconDisabled ? '导入脉冲模板' : '无可导入脉冲模板',
              color: '#434351',
              arrow: { pointAtCenter: true },
              autoAdjustOverflow: true,
            }}
            authName="previewPlan.importStimulate"
            disabled={iconDisabled}
            iconSvg={iconDisabled ? StimulateImportDisabled : ImportIcon}
            onClick={() => {
              if (!iconDisabled) setImportVisible(true);
            }}
          />
          {/* <NgIcon authName='noImagePlan.importStimulate' disabled={props.iconDisabled} iconSvg={props.iconDisabled ? StimulateImportDisabled : ImportIcon} onClick={() => { if (!props.iconDisabled) setImportVisible(true); }} /> */}
        </p>
        <TBSChart className={styles.chart_container} template={props.stimulate} motionThreshold={props.motion_threshold} />
        <EditStimulateTemplate
          formRef={form}
          stimulate={props.stimulate}
          motionThreshold={props.motion_threshold}
          itemDisabled={props.itemStatus === AuthEnum.disable}
          AllNullSueecss={props.isPreview}
        />
      </NgForm>
      <ImportTemplate visible={importVisible} onCancel={handleCancelImport} onOk={handleImport} />
    </>
  );
});
