@import '../../static/style/baseColor.module.less';
.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: @colorA1;
  padding: 8px 12px;
  position: relative;

  .header {
    position: relative;
    display: inline-flex;
    width: 100%;
    color: #b8b8bd;
    padding: 12px 20px 0px 4px;
  }

  .patient_name {
    position: absolute;
    top: 54px;
    left: 28px;
  }

  .info_content {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .content {
    display: flex;
    justify-content: center;
    position: relative;

    :global {
      .stimulate {
        // position: absolute;
        // top: 50%;
        // transform: translateY(-50%);
        display: flex;
        justify-content: center;

        & > div:last-child {
          margin-right: 0 !important;
        }
      }
      .treat_info {
        // position: absolute;
        // top: 0;
        display: flex;
        justify-content: center;
      }
      .stimulate[data-treat='true'] {
        z-index: 10;
      }
      .stimulate[data-treat='false'] {
        z-index: 1;
      }
      .treat_info[data-treat='true'] {
        z-index: 10;
      }
      .treat_info[data-treat='false'] {
        z-index: 1;
      }
    }

    .patient_container,
    .stimulate_container,
    .strength_container {
      background-color: @colorA3;
      border-radius: 6px;
      height: 716px;
      padding: 20px;
    }

    .patient_container,
    .stimulate_container {
      width: 280px;
      margin-right: 40px;
    }

    .stimulate_container,
    .strength_container {
      width: 304px;
    }
    .stimulate_container[data-preview='false'] {
      :global {
        .relative_strength {
          display: none;
        }
      }
    }
  }

  .botton_footer {
    margin: 30px 0 0 0;
    display: flex;
    justify-content: flex-end;
  }

  .photo_content {
    justify-content: center;
    position: absolute;
    left: 50%;
    right: 50%;
  }

  .pregress_container {
    padding: 51px 77px;
    height: 463px;
    :global {
      .treat_pause {
        height: 23px;
        margin: 12px 0 17px 0;
        text-align: center;
        font-weight: 350;
        color: @colorB1;
      }
    }
    .title_container {
      margin-top: 40px;

      p {
        margin-bottom: 9px;
        color: #fff;
        text-align: center;
      }
    }
  }

  .treatment_contianer {
    width: 312px;
    height: 463px;
    padding: 25px 20px;
    padding-right: 0px;
    box-sizing: border-box;
    background-color: @colorA3;
    .strength_progress {
      margin-right: 19px;
      & > div {
        transform: unset;
      }
    }
    :global {
      .strength_treat_pause {
        color: @colorA12 !important;
        font-size: 20px !important;
        font-weight: 350 !important;
        .range {
          font-size: 16px !important;
          color: @colorA9 !important;
        }
      }
      .truth_treat_pause {
        height: 79px;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }

    .title {
      height: 44px;
      margin-bottom: 43px;
      display: flex;
      align-items: center;

      .label {
        color: @colorA9;
        font-size: 16px;
      }

      .value {
        color: @colorA9;
        font-size: 20px;
      }
    }

    .chart {
      & > div:last-child {
        display: none;
      }
    }
  }
  :global {
    .ant-modal-root {
      .back_modal {
        height: 233px;

        .ant-modal-footer {
          display: none;
        }
        .ant-modal-body {
          display: flex !important;
          flex-direction: column !important;
          justify-content: center;
          align-items: center;
          margin-top: 8px;
          margin-bottom: 10px;
          .action {
            width: 50px;
            height: 50px;
          }
          .treat_complete_text {
            font-size: 16px;
            color: @colorA11;
            margin-top: 13px;
          }
          .auto_hide_text {
            height: 24px;
            line-height: 24px;
            margin-top: 4px;
            font-size: 12px;
            color: @colorA9;
          }
          .back_button {
            margin-top: 20px;
            width: 88px;
            height: 32px;
            .ant-btn {
              width: 88px;
            }
          }
        }
      }
    }
  }
}

.loading {
  :global {
    &,
    .ant-spin-container {
      height: 100%;
    }
  }
}

.modal_confirm {
  :global {
    .ant-modal-content {
      background-color: @colorA4;

      .ant-modal-confirm-content,
      .ant-modal-confirm-title {
        color: @colorA11;
      }

      .ant-btn {
        &.ant-btn-default {
          color: @colorA10;
          align-items: center;
          cursor: url('@/renderer/static/svg/cursor.cur'), pointer;
          background-color: rgba(0, 0, 0, 0);
          border-color: rgba(0, 0, 0, 0);
          &:hover {
            color: @colorC4;
          }
        }

        .ant-btn:not(:disabled):focus-visible {
          outline: 0;
        }

        &.ant-btn-primary {
          color: @colorA1;
          background-color: @colorC4;
          margin-left: 16px;

          &:hover {
            background-color: @colorC1;
          }
        }
      }
    }
  }
}
