import React, { useState } from 'react';
import NgButton from '@/renderer/uiComponent/NgButton';
import NgButtonText from '@/renderer/uiComponent/NgButtonText';
import NgPopover from '@/renderer/uiComponent/NgPopover';
import ButtonCode from '@/renderer/container/uiComponentDemo/codeDemo/buttonCode';
import NgDarkButton from '@/renderer/uiComponent/NgDarkButton';

type Props = {};
export const Button: React.FC<Props> = (props: Props) => {
  const [loading, setLoading] = useState(false);

  return (
    <div>
      <div style={{ display: 'flex' }}>
        <NgDarkButton style={{ marginRight: 10 }}>dark button</NgDarkButton>
        <NgButton style={{ marginRight: 10 }}>default</NgButton>
        <NgButton style={{ marginRight: 10 }} disabled>
          disable
        </NgButton>
        <NgButton style={{ marginRight: 10 }} ghost>
          ghost
        </NgButton>
        <NgPopover
          placement={'top'}
          style={{ marginRight: 10 }}
          content={
            <>
              <div>This is a description.</div>
              <div style={{ display: 'flex', alignItems: 'center', marginTop: 10 }}>
                <NgButtonText style={{ marginRight: 10 }}>取消</NgButtonText>
                <NgButton buttonMode={'popover'} style={{ marginRight: 10 }}>
                  确定
                </NgButton>
                <NgButton buttonMode={'popover'} disabled>
                  disabled
                </NgButton>
              </div>
            </>
          }
        >
          <NgButton buttonMode={'default'} style={{ width: 115, padding: '4px 12px' }}>
            show popover
          </NgButton>
        </NgPopover>
        <NgButton
          style={{ marginLeft: 10 }}
          loading={loading}
          onClick={() => {
            setLoading(v => !v);
            setTimeout(() => {
              setLoading(false);
            }, 2000);
          }}
        >
          loading
        </NgButton>
        {/* <NgButton style={{marginLeft:10}} loading={loading} onClick={()=>setLoading((v)=>!v)}>*/}
        {/*  {loading ? '正在加载' : '加载完成'}*/}
        {/* </NgButton>*/}
        <NgButtonText style={{ marginTop: 4, marginLeft: 10, marginRight: 10, display: 'inline-block' }}>取消</NgButtonText>
        <NgButtonText buttonTextMode={'link'} style={{ marginTop: 4, marginLeft: 10, marginRight: 10, display: 'inline-block' }}>
          忘记密码
        </NgButtonText>
      </div>
      <ButtonCode />
    </div>
  );
};
export default Button;
