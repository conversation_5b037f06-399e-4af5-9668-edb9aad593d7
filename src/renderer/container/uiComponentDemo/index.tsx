import React, { useEffect } from 'react';
import { Menu, MenuProps } from 'antd';
import { Outlet, useNavigate } from 'react-router-dom';
import('./index.module.less');

type MenuItem = Required<MenuProps>['items'][number];
type Props = {};

const UiComponentDemo = (props: Props) => {
  const navigate = useNavigate();
  useEffect(() => {
    // NgModal.open({
    //   title:'modal.open',
    //   content:<>12312312</>,
    //   onOk:async () => {
    //     let res = await  fetch('');
    //     /* eslint-disable no-console */
    //     console.log('ok',res);
    //   },
    // });
  }, []);
  const getItem: (label: React.ReactNode, key?: React.Key | null, icon?: React.ReactNode, children?: MenuItem[]) => MenuItem = (
    label,
    key,
    icon,
    children
  ) => {
    return {
      key,
      icon,
      children,
      label,
    } as MenuItem;
  };

  const items: MenuItem[] = [
    getItem('Button', 'button'),
    getItem('Modal', 'modal'),
    getItem('Form', 'form'),
    getItem('Input', 'input'),
    getItem('Radio&Checkbox', 'radio'),
    getItem('Steps', 'steps'),
    getItem('Popover', 'popover'),
    getItem('Select', 'select'),
    getItem('Icons', 'icons'),
    getItem('BrainBar', 'brainBar'),
    getItem('Message', 'message'),
    getItem('breadCrumb', 'breadCrumb'),
    getItem('Progress', 'progress'),
    getItem('Table', 'table'),
    getItem('Empty', 'empty'),
    getItem('Loading', 'loading'),
    getItem('Pagination', 'pagination'),
  ];
  useEffect(() => {
    navigate('button');
  }, []);

  return (
    <div style={{ display: 'flex' }}>
      <Menu
        style={{ width: 256, height: '100vh', overflow: 'auto' }}
        defaultSelectedKeys={['button']}
        defaultOpenKeys={['button']}
        items={items}
        onClick={menuInfo => {
          navigate(`${menuInfo.key}`);
        }}
      />
      <div id={'rightBox'} style={{ padding: 48, width: '100%', overflow: 'auto' }}>
        <Outlet />
      </div>
    </div>
  );
};
export default UiComponentDemo;
