import React, { memo, useState } from 'react';
import NgEmpty from '@/renderer/uiComponent/NgEmpty';
import NgModal from '../../uiComponent/NgModal';
import EmptyCode from '@/renderer/container/uiComponentDemo/codeDemo/emptyCode';

type EmptyType = 'noTask' | 'noPatient' | 'noData' | 'pageFail' | 'taskFail' | 'loadingFail' | 'addTask';
const statusList: EmptyType[] = ['noTask', 'noPatient', 'noData', 'pageFail', 'taskFail', 'loadingFail', 'addTask'];
const Empty = () => {
  const [openModal, setOpenModal] = useState(false);
  const addTaskHandle = () => {
    setOpenModal(true);
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'row', flexWrap: 'wrap' }}>
      {statusList.map(item => {
        return (
          <div key={item} style={{ width: '200px', height: '200px' }}>
            <NgEmpty key={item} emptyType={item} onAddTask={item === 'addTask' ? addTaskHandle : undefined} />
            <NgModal
              title={'创建治疗任务'}
              open={openModal}
              onOk={() => {
                setOpenModal(false);
              }}
              onCancel={e => {
                setOpenModal(false);
              }}
            >
              是否创建治疗任务？？？
            </NgModal>
          </div>
        );
      })}
      <EmptyCode />
    </div>
  );
};

export default memo(Empty);
