import React from 'react';
import { NgBreadcrumb } from '@/renderer/uiComponent/NgBreadCrumb';
import { formatBreadcrumb, routers } from '@/renderer/router/routers';
import { ItemType } from 'antd/es/breadcrumb/Breadcrumb';
import styles from './index.module.less';
// eslint-disable-next-line import/no-internal-modules
import { Divider } from 'antd/lib';
import BreadCrumbCode from '@/renderer/container/uiComponentDemo/codeDemo/breadCrumbCode';

type Props = {};
const home = {
  path: '/home',
  breadcrumbName: '首页',
};
const demo = {
  path: '/demo/button',
  breadcrumbName: 'demo',
};
export const BreadCrumb: React.FC<Props> = (props: Props) => {
  const [currentRoutes] = React.useState(formatBreadcrumb(routers));
  const [renderRoutes] = React.useState<ItemType[]>(() => {
    return [home, demo, currentRoutes[10]];
  });

  return (
    <div className={styles.ng_demo} style={{ padding: 0 }}>
      <NgBreadcrumb isGray={false} items={renderRoutes} />
      第一种写法，手动传入routes;这种方式不会被覆盖，
      <Divider />
      <NgBreadcrumb
        isGray={false}
        supplement={{
          path: '/',
          breadcrumbName: '治疗中，张三',
        }}
      />
      第二种写法，手动传入supplement; 用于补充最后一项
      <Divider />
      面包屑采用 外部容器宽度,请根据实际情况调整容器宽度
      <div style={{ width: '30%' }}>
        <NgBreadcrumb isGray items={renderRoutes} />
      </div>
      <BreadCrumbCode />
    </div>
  );
};
export default BreadCrumb;
