import React, { useState } from 'react';
import NgModal from '@/renderer/uiComponent/NgModal';
// import NgButtonText from '@/renderer/uiComponent/NgButtonText/ngButtonText';
import NgButton from '@/renderer/uiComponent/NgButton';
import { ReactComponent as Warning } from '@/renderer/static/svg/warning.svg';
import NgSelectFileModal from '@/renderer/uiComponent/NgSelectFileModal';
import ModalCode from '@/renderer/container/uiComponentDemo/codeDemo/modalCode';
type Props = {};
const Modal = (props: Props) => {
  const [openModal, setOpenModal] = useState(false);
  const [openFileModal, setOpenFileModal] = useState(false);
  const handleOpenModal = () => {
    NgModal.info({
      title: 'title g',
      content: (
        <>
          <p>模版名称：iTBS刺激模版</p>
          <p>模版名称：iTBS刺激模版</p>
          <p>模版名称：iTBS刺激模版</p>
          <p>模版名称：iTBS刺激模版</p>
          <p>模版名称：iTBS刺激模版</p>
          <p>模版名称：iTBS刺激模版</p>
          <p>模版名称：iTBS刺激模版</p>
          <p>模版名称：iTBS刺激模版</p>
          <p>模版名称：iTBS刺激模版</p>
          <p>模版名称：iTBS刺激模版</p>
          <p>模版名称：iTBS刺激模版g</p>
        </>
      ),
    });
  };

  const handleOpenConfirm = () => {
    NgModal.confirm({
      content: <>这里是对于问题的详细描述和说明，一些文字的说明，大江东去，浪淘尽，千古风流人物。g</>,
      headerIcon: <Warning />,
    });
  };

  return (
    <div>
      <div style={{ display: 'flex' }}>
        <NgSelectFileModal
          maskClosable={false}
          filepath={'/'}
          open={openFileModal}
          onCancel={() => setOpenFileModal(false)}
          onOk={file => {
            // eslint-disable-next-line no-console
            console.log(file); // 获取的文件结果
            setOpenFileModal(false);
          }}
        />
        <NgButton style={{ marginRight: 10 }} onClick={() => setOpenFileModal(true)}>
          open file modal
        </NgButton>
        <NgButton style={{ marginRight: 10 }} onClick={() => setOpenModal(true)}>
          Popover 1
        </NgButton>
        <NgButton style={{ marginRight: 10 }} onClick={handleOpenConfirm}>
          Popover 2
        </NgButton>
        <NgButton onClick={handleOpenModal} style={{ marginRight: 10 }}>
          popover3
        </NgButton>
        <NgModal
          title={'demo modal g'}
          open={openModal}
          // ! 当存在footer时，组件的open、close在footer中维护
          onOk={() => {
            setOpenModal(false);
          }}
          onCancel={e => {
            setOpenModal(false);
          }}
          // footer={<>
          //   <NgButtonText onClick={(e)=>{
          //     setOpenModal(false);
          //   }
          //   }>取消</NgButtonText>
          //   <NgButton onClick={(e)=>{
          //     setOpenModal(false);
          //   }
          //   }>确认</NgButton>
          // </>}
        >
          modal content
          <br />
          bla bla bla g
        </NgModal>
      </div>
      <ModalCode />
    </div>
  );
};
export default Modal;
