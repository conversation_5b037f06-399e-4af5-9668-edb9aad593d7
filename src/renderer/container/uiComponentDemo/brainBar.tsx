import React, { useCallback } from 'react';
import NgBrain from '@/renderer/uiComponent/NgBrain';
import { Ng<PERSON>lider } from '@/renderer/uiComponent/NgSlider';
import styles from './index.module.less';
import BrainBarCode from '@/renderer/container/uiComponentDemo/codeDemo/brainBarCode';

type Props = {};
export const BrainBar: React.FC<Props> = (props: Props) => {
  const handleIconClick = useCallback((field: string) => {
    console.log(field); // eslint-disable-line no-console
  }, []);

  const onChangeScalpOpacity = useCallback((value: number) => {
    console.log('onChangeScalpOpacity', value); // eslint-disable-line no-console
  }, []);

  return (
    <div className={styles.ng_demo} style={{ padding: 0 }}>
      <div>
        <NgBrain
          iconClick={handleIconClick}
          location={'BrainLocationFront'}
          onChangeScalpOpacity={onChangeScalpOpacity}
          onChangeScalpZoom={() => {
            //
          }}
        />
        <div className={styles.mt16} style={{ width: '300px' }}>
          <NgSlider min={0} max={100} defaultValue={50} />
        </div>
        <div className={styles.mt16} style={{ width: '300px' }}>
          <NgSlider min={0} max={100} defaultValue={80} isGray />
        </div>
        <div className={styles.mt16} style={{ width: '300px', marginTop: 100 }}>
          <NgSlider min={0} max={100} defaultValue={20} suffix={'%'} />
        </div>
      </div>
      <BrainBarCode />
    </div>
  );
};
export default BrainBar;
