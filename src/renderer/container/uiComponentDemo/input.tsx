// import { Form } from 'antd';
import { NgInputNumber } from '@/renderer/uiComponent/NgInputNumber';
import React, { useCallback } from 'react';
import { withUserSession, UserSessionProps } from '../../hocComponent/withUserSession';
import { NgForm, NgFormItem } from '../../uiComponent/NgForm';
import { NgInput } from '../../uiComponent/NgInput';
import InputCode from '@/renderer/container/uiComponentDemo/codeDemo/inputCode';

type Props = UserSessionProps;
const defaultValue = 1;
export const Input: React.FC<Props> = props => {
  const [valNumber, setValNumber] = React.useState<number>(1);
  const logout = async () => {
    await window.authAPI.logout();
    props.setUserSession?.(undefined);
  };

  const handleChangeValue = useCallback((value: number | string | null) => {
    if (value === null) {
      setValNumber(defaultValue);

      return;
    }
    if (typeof value === 'string') {
      setValNumber(Number(value));

      return;
    }
    setValNumber(value);
  }, []);

  return (
    <div style={{ width: '800px', padding: '20px' }}>
      <div style={{ color: 'red' }} onClick={logout}>
        退出登录
      </div>
      <NgInput />
      <NgInput value={444} disabled style={{ marginTop: '20px' }} />
      <NgInput style={{ marginTop: '20px' }} placeholder="请输入相关内容" />
      <NgForm name="basic">
        <NgFormItem name="username" rules={[{ required: true, message: 'Please input your username!' }]}>
          <NgInput status="error" style={{ marginTop: '20px' }} />
        </NgFormItem>
      </NgForm>
      <div style={{ width: '200px', marginTop: '20px' }}>
        <NgInputNumber defaultValue={1} value={valNumber} step={0.5} min={1} max={10} onChange={handleChangeValue} />
      </div>
      <InputCode />
    </div>
  );
};

export default withUserSession(Input);
