import React from 'react';
import { Divider } from 'antd';
import { CheckboxValueType } from 'antd/es/checkbox/Group';
import { CheckboxChangeEvent } from 'antd/es/checkbox';
import { NgCheckbox } from '../../uiComponent/NgCheckbox';
import { NgRadio } from '../../uiComponent/NgRadio';
import RadioCode from '@/renderer/container/uiComponentDemo/codeDemo/radioCode';

export const Radio: React.FC<void> = props => {
  const [checkList, setCheckList] = React.useState<CheckboxValueType[]>([]);
  const [indeterminate, setIndeterminate] = React.useState(true);
  const [checkAll, setCheckAll] = React.useState(false);

  const handleCheckAll = (e: CheckboxChangeEvent) => {
    setCheckList(e.target.checked ? ['5', '6'] : []);
    setIndeterminate(false);
    setCheckAll(e.target.checked);
  };

  const handleCheck = (list: CheckboxValueType[]) => {
    setCheckList(list);
    setIndeterminate(!!list.length && list.length < 2);
    setCheckAll(list.length === 2);
  };

  return (
    <div style={{ display: 'flex' }}>
      <div style={{ width: '600px', padding: '20px' }}>
        <div>
          <NgRadio>单元1</NgRadio>
        </div>
        <div>
          <NgRadio checked>单元2</NgRadio>
        </div>
        <div>
          <NgRadio.Group defaultValue={22}>
            <NgRadio value={22}>单元22</NgRadio>
            <NgRadio value={33}>单元33</NgRadio>
          </NgRadio.Group>
        </div>
        <div>
          <NgRadio.Group defaultValue="a" size="large" optionType={'button'} buttonStyle={'solid'}>
            <NgRadio value="a">全部</NgRadio>
            <NgRadio value="b">rTMS</NgRadio>
            <NgRadio value="c">TMS</NgRadio>
          </NgRadio.Group>
        </div>
        <div style={{ marginTop: '20px' }}>
          <NgCheckbox disabled checked>单元3</NgCheckbox>
        </div>
        <div style={{ marginTop: '20px' }}>
          <NgCheckbox indeterminate={indeterminate} onChange={handleCheckAll} checked={checkAll}>
            全选
          </NgCheckbox>
          <Divider />
          <NgCheckbox.Group onChange={handleCheck} value={checkList}>
            <NgCheckbox value={'5'}>单元5</NgCheckbox>
            <NgCheckbox value={'6'}>单元6</NgCheckbox>
          </NgCheckbox.Group>
        </div>
      </div>
      <RadioCode />
    </div>
  );
};

export default Radio;
