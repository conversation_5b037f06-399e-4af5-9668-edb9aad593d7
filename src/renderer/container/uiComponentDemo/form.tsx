import React from 'react';
import { Ng<PERSON><PERSON>, NgFormItem } from '@/renderer/uiComponent/NgForm';
import { Col, Form, Row } from 'antd';
import { NgInput } from '@/renderer/uiComponent/NgInput';
import NgButton from '@/renderer/uiComponent/NgButton';
import NgButtonText from '@/renderer/uiComponent/NgButtonText';
import NgMessage from '@/renderer/uiComponent/NgMessage';
import FormCode from '@/renderer/container/uiComponentDemo/codeDemo/formCode';

type Props = {};

const FormDemo = (props: Props) => {
  const [form] = Form.useForm();
  const { open, contextHolder } = NgMessage.useMessage();

  return (
    <div>
      {contextHolder}
      <div className="form" style={{ display: 'flex', width: '100%' }}>
        <div className="left" style={{ width: '50%', paddingTop: '28px' }}>
          <NgForm
            form={form}
            onFinish={val => {
              /* eslint-disable no-console */
              console.log(val);
            }}
            labelCol={{ span: 4 }}
            wrapperCol={{ span: 10 }}
          >
            <Row gutter={24}>
              <Col span={24}>
                <NgFormItem
                  validateStatus={'error'}
                  help={'first name is required'}
                  rules={[{ required: true, message: 'first name is required' }]}
                  name={'firstName'}
                  label={'first name'}
                >
                  <NgInput placeholder={'请输入'} />
                </NgFormItem>
              </Col>
              <Col span={24}>
                <NgFormItem name={'lastName'} label={'last name'}>
                  <NgInput placeholder={'请输入'} />
                </NgFormItem>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col offset={6} span={2} style={{ display: 'flex', alignItems: 'center' }}>
                <NgButtonText
                  onClick={() => {
                    return open({
                      content: 'operation is canceled',
                    });
                  }}
                >
                  cancel
                </NgButtonText>
              </Col>
              <Col span={2}>
                <NgButton htmlType={'submit'}>submit</NgButton>
              </Col>
            </Row>
          </NgForm>
        </div>
        <div className="right" style={{ width: '50%' }}>
          <NgForm
            layout={'vertical'}
            form={form}
            onFinish={val => {
              /* eslint-disable no-console */
              console.log(val);
            }}
            labelCol={{ span: 4 }}
            wrapperCol={{ span: 10 }}
          >
            <Row gutter={24}>
              <Col span={24}>
                <NgFormItem rules={[{ required: true, message: 'first name is required' }]} name={'firstName'} label={'first name'}>
                  <NgInput placeholder={'请输入'} />
                </NgFormItem>
              </Col>
              <Col span={24}>
                <NgFormItem name={'lastName'} label={'last name'}>
                  <NgInput placeholder={'请输入'} />
                </NgFormItem>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col offset={3} span={2} style={{ display: 'flex', alignItems: 'center' }}>
                <NgButtonText
                  onClick={() => {
                    return open({
                      content: 'operation is canceled',
                    });
                  }}
                >
                  cancel
                </NgButtonText>
              </Col>
              <Col span={2}>
                <NgButton htmlType={'submit'}>submit</NgButton>
              </Col>
            </Row>
          </NgForm>
        </div>
      </div>
      <FormCode />
    </div>
  );
};
export default FormDemo;
