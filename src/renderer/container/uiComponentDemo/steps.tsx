import React, { useState } from 'react';
import { NgSteps } from '@/renderer/uiComponent/NgStep';
import StepsCode from '@/renderer/container/uiComponentDemo/codeDemo/stepsCode';

type Props = {};
export const Steps: React.FC<Props> = (props: Props) => {
  const [current, setCurrent] = useState(0);

  return (
    <div style={{ padding: 100, display: 'flex', color: '#B8B8BD' }}>
      <span>请点一下圆圈区域</span>
      <NgSteps
        current={current}
        onChange={setCurrent}
        items={[
          {
            title: 'Step 1',
          },
          {
            title: 'Step 2',
          },
          {
            title: 'Step 3',
          },
          {
            title: 'Step 4',
          },
        ]}
      />
      <StepsCode />
    </div>
  );
};
export default Steps;
