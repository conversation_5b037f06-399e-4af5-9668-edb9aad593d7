import React from 'react';
import NgHighlightCode from '@/renderer/uiComponent/NgHighlightCode';

type Props = {};
const MessageCode = (props: Props) => {
  return (
    <div style={{ overflowY: 'scroll', height: '80vh', marginTop: 20 }}>
      <NgHighlightCode
        title={'Message'}
        children={`import React from 'react';
import NgMessage from '@/renderer/uiComponent/NgMessage';
import NgButton from '@/renderer/uiComponent/NgButton';
import {uniqueId} from 'lodash';

type Props = {

};
const Message = (props: Props) => {
  const {
    open,
    error,
    contextHolder,
    loading,
    warning,
    success,
    destroy,
  } = NgMessage.useMessage();

  const handleShowMessage = (type?: string): any => {
    // ! 使用return可以避免报Promises must be awaited, end with a call to .catch 错误
    switch (type) {
      case 'success':{
        return success({
          content:'this is a success info',
        });
      }
      case 'error':{
        return error({
          content:'this is a error info',
        });
      }
      case 'warning':{
        return warning({
          content:'this is a warning info',
        });
      }
      default:{
        return open({
          content:'this is a default info',
        });
      }
    }
  };

  const handleLoading = () => {
    // 可以维护message的key，可在destroy中传入key手动控制message的destroy
    const customKey = uniqueId('loading'); // 避免message的key冲突，建议使用uniqueId

    setTimeout(()=>{
      destroy(customKey);
    }, 3000);

    return loading({
      key:customKey,
      content:<>123123</>,
    });
  };

  return (
    <div style={{display:'flex'}}>
      <NgButton style={{marginRight:15}} onClick={()=>handleShowMessage()}> default </NgButton>
      <NgButton style={{marginRight:15}} onClick={()=>handleShowMessage('success')}> success </NgButton>
      <NgButton style={{marginRight:15}} onClick={()=>handleShowMessage('error')}> error </NgButton>
      <NgButton style={{marginRight:15}} onClick={()=>handleShowMessage('warning')}> warning </NgButton>
      {/* ! loading 必须手动挂载contextHolder，否则会报React 18 concurrent 模式下的error */}
      {contextHolder}
      <NgButton style={{marginRight:15}} onClick={handleLoading}> loading </NgButton>
    </div>
  );
};
export default Message;
`}
      />
    </div>
  );
};
export default MessageCode;
