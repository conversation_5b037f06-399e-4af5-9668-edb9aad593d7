import React from 'react';
import NgHighlightCode from '@/renderer/uiComponent/NgHighlightCode';
import { Divider } from 'antd';

type Props = {};
const PopoverCode = (props: Props) => {
  return (
    <div style={{ overflowY: 'scroll', height: '80vh', marginTop: 20 }}>
      <NgHighlightCode
        title={'popover'}
        children={`import NgButton from '@/renderer/uiComponent/NgButton';
import NgPopover from '@/renderer/uiComponent/NgPopover';
import React,{useState} from 'react';

type Props = {

};
const Index = (props: Props) => {
  const [popover,setPopover]=useState(false);

  return (
    <NgPopover open={popover} placement="top" title={<span>prompt text</span>}>
      <NgButton  style={{width:80,marginRight:15}} onClick={() => setPopover(true)}>TL</NgButton>
    </NgPopover>
  );
};
export default Index;
`}
      />
      <Divider style={{ background: '#fff' }} />

      <NgHighlightCode
        title={'popover button'}
        children={`import NgButtonText from '@/renderer/uiComponent/NgButtonText';
import NgPopover from '@/renderer/uiComponent/NgPopover';
import React,{useState} from 'react';

type Props = {

};
const Index = (props: Props) => {
  const [popOpen,setPopOpen]=useState(false);

  return (
    <NgPopover
      open={popOpen}
      showOperation={{
        onOk:()=>{
          setPopOpen(false);
        },
        onCancel:()=>setPopOpen(false),
      }}
      title={'popover title g'}
      placement={'top'}
      // ! 设置了点击事件 trigger=hover将失效
      trigger={'hover'}
      content={<>content</>
      }>
      <div>
        <NgButtonText onClick={()=>setPopOpen(true)}
        >popover 5</NgButtonText>
      </div>
    </NgPopover>
  );
};
export default Index;`}
      />
      <Divider style={{ background: '#fff' }} />

      <NgHighlightCode
        title={'Alert'}
        children={`import React from 'react';
import {ReactComponent as Info} from '@/renderer/static/svg/infoMessage.svg'; // ! 必须使用这种方式导入SVG，否则vite会编译错误
import NgAlert from '@/renderer/uiComponent/NgAlert';

type Props = {

};
const Index = (props: Props) => {
  return (

    <NgAlert
      icon={<Info />}
      message="Informational Notes g Informational Notes g Informational Notes g Informational Notes g Informational Notes g"
      type="info" // type = 'success' | 'info' | 'warning' | 'error'
      showIcon
    />
  );
};
export default Index;`}
      />
    </div>
  );
};
export default PopoverCode;
