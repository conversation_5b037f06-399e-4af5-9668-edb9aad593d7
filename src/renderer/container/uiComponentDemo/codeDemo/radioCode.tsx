import React from 'react';
import NgHighlightCode from '@/renderer/uiComponent/NgHighlightCode';
import { Divider } from 'antd';

type Props = {};
const RadioCode = (props: Props) => {
  return (
    <div style={{ marginTop: 20 }}>
      <NgHighlightCode
        title={'Radio.Group'}
        children={`import { NgRadio } from '@/renderer/uiComponent/NgRadio';
import React from 'react';

type Props = {

};
const Index = (props: Props) => {
  return (
    <NgRadio.Group defaultValue={22}>
      <NgRadio value={22}>单元22</NgRadio>
      <NgRadio value={33}>单元33</NgRadio>
    </NgRadio.Group>
  );
};
export default Index;`}
      />
      <Divider style={{ background: '#fff' }} />

      <NgHighlightCode
        title={'Checkbox'}
        children={`import { NgCheckbox } from '@/renderer/uiComponent/NgCheckbox';
import React from 'react';

type Props = {

};
const Index = (props: Props) => {
  return (
    <NgCheckbox>单元3</NgCheckbox>
  );
};
export default Index;
`}
      />
      <Divider style={{ background: '#fff' }} />

      <NgHighlightCode
        title={'Select All'}
        children={`import { NgCheckbox } from '@/renderer/uiComponent/NgCheckbox';
import { CheckboxChangeEvent } from 'antd/es/checkbox/Checkbox';
import { CheckboxValueType } from 'antd/es/checkbox/Group';
import React from 'react';

type Props = {

};
const Index = (props: Props) => {
  const [checkList, setCheckList] = React.useState<CheckboxValueType[]>([]);
  const [indeterminate, setIndeterminate] = React.useState(true);
  const [checkAll, setCheckAll] = React.useState(false);

  const handleCheckAll = (e: CheckboxChangeEvent) => {
    setCheckList(e.target.checked ? ['5','6'] : []);
    setIndeterminate(false);
    setCheckAll(e.target.checked);
  };
  const handleCheck = (list: CheckboxValueType[]) => {
    setCheckList(list);
    setIndeterminate(!!list.length && list.length < 2);
    setCheckAll(list.length === 2);
  };

  return (
    <>
      <NgCheckbox indeterminate={indeterminate} onChange={handleCheckAll}
        checked={checkAll}>全选</NgCheckbox>
      <NgCheckbox.Group onChange={handleCheck} value={checkList}>
        <NgCheckbox value={'5'}>单元5</NgCheckbox>
        <NgCheckbox value={'6'}>单元6</NgCheckbox>
      </NgCheckbox.Group></>
  );
};
export default Index;`}
      />
    </div>
  );
};
export default RadioCode;
