import React from 'react';
import NgHighlightCode from '@/renderer/uiComponent/NgHighlightCode';

type Props = {};
const LoadingCode = (props: Props) => {
  return (
    <div style={{ overflowY: 'scroll', height: '80vh', marginTop: 20 }}>
      <NgHighlightCode
        title={'global loading'}
        children={`import React, {ReactElement} from 'react';
import {NgLoading} from '@/renderer/uiComponent/NgLoading';
import { Spin } from 'antd';

type Props = {

};
const Index = (props: Props) => {
  const [loading, setLoading] = React.useState(true);
  const renderLoading = (): ReactElement => {
    return <div>
      <NgLoading loadingText={'页面加载中,5秒后关闭'}/>
    </div>;
  };
  React.useEffect(()=>{
    setTimeout(()=>{
      setLoading(false);
    },5000);
  },[]);

  return (
    //  全局loading
    <Spin spinning={loading} indicator={renderLoading()} />
  );
};
export default Index;`}
      />
    </div>
  );
};
export default LoadingCode;
