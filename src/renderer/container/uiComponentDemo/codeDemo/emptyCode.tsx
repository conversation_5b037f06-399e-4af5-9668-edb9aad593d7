import React from 'react';
import NgHighlightCode from '@/renderer/uiComponent/NgHighlightCode';

type Props = {};
const EmptyCode = (props: Props) => {
  return (
    <div style={{ overflowY: 'scroll', height: '80vh', marginTop: 20 }}>
      <NgHighlightCode
        title={'Empty'}
        children={`import React, { memo, useState } from 'react';
import NgEmpty from '@/renderer/uiComponent/NgEmpty';
import NgModal  from '../../uiComponent/NgModal';

type EmptyType = 'noTask' | 'noPatient' | 'noData' | 'pageFail' | 'taskFail' | 'loadingFail' | 'addTask';
// 无治疗任务、无患者、无数据、页面失败、加载任务失败、加载失败、添加任务失败
const statusList: EmptyType[] = ['noTask', 'noPatient', 'noData', 'pageFail', 'taskFail', 'loadingFail', 'addTask'];

const Index = () => {
  const [openModal, setOpenModal] = useState(false);
  const addTaskHandle = () => {
    setOpenModal(true);
  };

  return (
    <div style={{display: 'flex' ,flexDirection: 'row', flexWrap: 'wrap'}}>
      {
        statusList.map((item) => {
          return <div key={item} style={{ width: '200px', height: '200px' }}>
            <NgEmpty
              key={item}
              emptyType={item}
              onAddTask={item === 'addTask'? addTaskHandle:undefined}
            />
            <NgModal
              title={'创建治疗任务'}
              open={openModal}
              onOk={()=>{
                setOpenModal(false);
              }}
              onCancel={(e)=>{
                setOpenModal(false);
              }}
            >
              是否创建治疗任务？？？
            </NgModal>
          </div>;
        })
      }
    </div>
  );
};

export default Index;`}
      />
    </div>
  );
};
export default EmptyCode;
