import React from 'react';
import NgHighlightCode from '@/renderer/uiComponent/NgHighlightCode';
import { Divider } from 'antd';

type Props = {};
// eslint-disable-next-line max-lines-per-function
const ModalCode = (props: Props) => {
  return (
    <div style={{ overflowY: 'scroll', height: '80vh', marginTop: 20 }}>
      <NgHighlightCode
        title={'open file modal'}
        children={`import React,{useState} from 'react';
import NgSelectFileModal from '@/renderer/uiComponent/NgSelectFileModal';
import NgButton from '@/renderer/uiComponent/NgButton';

const Index: React.FC = () => {
  const [openFileModal,setOpenFileModal]=useState(false);

  return <>
    <NgSelectFileModal
      maskClosable={false}
      filepath={'/'}
      open={openFileModal}
      onCancel={() => setOpenFileModal(false)}
      onOk={(file)=>{
        // eslint-disable-next-line no-console
        console.log(file); // 获取的文件结果
        setOpenFileModal(false);
      }}/>
    <NgButton style={{marginRight:10}} onClick={()=>setOpenFileModal(true)}>open file modal</NgButton>
  </>;
};

export default Index;`}
      />
      <Divider style={{ background: '#fff' }} />

      <NgHighlightCode
        title={'Popover 1'}
        children={`import React,{useState} from 'react';
import NgButton from '@/renderer/uiComponent/NgButton';
import { NgModal } from '@/renderer/uiComponent/NgModal';

const Index: React.FC = () => {
  const [openModal,setOpenModal]=useState(false);

  return <>
    <NgButton style={{marginRight:10}} onClick={()=>setOpenModal(true)}>Popover 1</NgButton>
    <NgModal
      title={'demo modal g'}
      open={openModal}
      // ! 当存在footer时，组件的open、close在footer中维护
      onOk={()=>{
        setOpenModal(false);
      }}
      onCancel={(e)=>{
        setOpenModal(false);
      }}
      // footer={<>
      //   <NgButtonText onClick={(e)=>{
      //     setOpenModal(false);
      //   }
      //   }>取消</NgButtonText>
      //   <NgButton onClick={(e)=>{
      //     setOpenModal(false);
      //   }
      //   }>确认</NgButton>
      // </>}
    >
      modal content<br/>
      bla bla bla g
    </NgModal>
  </>;
};

export default Index;`}
      />
      <Divider style={{ background: '#fff' }} />

      <NgHighlightCode
        title={'Popover 2'}
        children={`import React from 'react';
import NgButton from '@/renderer/uiComponent/NgButton';
import { NgModal } from '@/renderer/uiComponent/NgModal';
import {ReactComponent as Warning} from '@/renderer/static/svg/warning.svg';

const Index: React.FC = () => {
  const handleOpenConfirm = ()=>{
    NgModal.confirm({
      content:<>这里是对于问题的详细描述和说明，一些文字的说明，大江东去，浪淘尽，千古风流人物。g</>,
      headerIcon:<Warning />,
    });
  };

  return <>
    <NgButton style={{marginRight:10}} onClick={handleOpenConfirm}>Popover 2</NgButton>
  </>;
};

export default Index;`}
      />
      <Divider style={{ background: '#fff' }} />

      <NgHighlightCode
        title={'popover3'}
        children={`import React from 'react';
import NgButton from '@/renderer/uiComponent/NgButton';
import { NgModal } from '@/renderer/uiComponent/NgModal';

const Index: React.FC = () => {
  const handleOpenModal= ()=>{
    NgModal.info({
      title:'title g',
      content:<>
        <p>模版名称：iTBS刺激模版</p>
        <p>模版名称：iTBS刺激模版</p>
        <p>模版名称：iTBS刺激模版</p>
        <p>模版名称：iTBS刺激模版</p>
        <p>模版名称：iTBS刺激模版</p>
        <p>模版名称：iTBS刺激模版</p>
        <p>模版名称：iTBS刺激模版</p>
        <p>模版名称：iTBS刺激模版</p>
        <p>模版名称：iTBS刺激模版</p>
        <p>模版名称：iTBS刺激模版</p>
        <p>模版名称：iTBS刺激模版g</p>
      </>,
    });
  };

  return <>
    <NgButton onClick={handleOpenModal} style={{marginRight:10}}>popover3</NgButton>
  </>;
};

export default Index;`}
      />
    </div>
  );
};
export default ModalCode;
