import React from 'react';
import NgHighlightCode from '@/renderer/uiComponent/NgHighlightCode';

type Props = {};
const FormCode = (props: Props) => {
  return (
    <div style={{ overflowY: 'scroll', height: '80vh', marginTop: 20 }}>
      <NgHighlightCode
        title={'form'}
        children={`import { NgForm, NgFormItem } from '@/renderer/uiComponent/NgForm';
import React, {useRef} from 'react';
import {Row,Col} from 'antd';
import { NgInput } from '@/renderer/uiComponent/NgInput';
import NgButtonText from '@/renderer/uiComponent/NgButtonText';
import NgButton from '@/renderer/uiComponent/NgButton';
import NgMessage from '@/renderer/uiComponent/NgMessage';

type Props = {

};
const Index = (props: Props) => {
  const formRef = useRef();
  const {open} = NgMessage.useMessage();

  return (
    <NgForm
      formRef={formRef}
      onFinish={(val)=>{
        /* eslint-disable no-console */
        console.log(val);
      }}
      labelCol={{ span: 4 }}
      wrapperCol={{ span: 10 }}
    >
      <Row gutter={24}>
        <Col span={24}>
          <NgFormItem
            validateStatus={'error'}
            help={'first name is required'}
            rules={[{ required: true,message:'first name is required' }]}
            name={'firstName'}
            label={'first name'}>
            <NgInput placeholder={'请输入'} />
          </NgFormItem>
        </Col>
        <Col span={24}>
          <NgFormItem name={'lastName'} label={'last name'}>
            <NgInput placeholder={'请输入'} />
          </NgFormItem>
        </Col>
      </Row>
      <Row gutter={24}>
        <Col offset={6} span={2} style={{display:'flex',alignItems:'center'}}>
          <NgButtonText onClick={()=>{
            return open({
              content:'operation is canceled',
            });
          }}>cancel</NgButtonText>
        </Col>
        <Col span={2}>
          <NgButton htmlType={'submit'}>submit</NgButton>
        </Col>
      </Row>
    </NgForm>
  );
};
export default Index;`}
      />
    </div>
  );
};
export default FormCode;
