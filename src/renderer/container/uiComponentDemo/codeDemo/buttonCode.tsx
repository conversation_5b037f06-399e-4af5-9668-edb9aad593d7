import React from 'react';
import NgHighlightCode from '@/renderer/uiComponent/NgHighlightCode';
import { Divider } from 'antd';

type Props = {};
// eslint-disable-next-line max-lines-per-function
const ButtonCode = (props: Props) => {
  return (
    <div style={{ overflowY: 'scroll', height: '80vh', marginTop: 20 }}>
      <NgHighlightCode
        title={'button default'}
        children={`import React from 'react';
import NgButton from '@/renderer/uiComponent/NgButton';

const Index: React.FC = () => (
  <>
    <NgButton type=default'>default</NgButton>
  </>
);

export default Index;`}
      />
      <Divider style={{ background: '#fff' }} />

      <NgHighlightCode
        title={'button disabled'}
        children={`import React from 'react';
import NgButton from '@/renderer/uiComponent/NgButton';

const Index: React.FC = () => (
  <>
    <NgButton type='disabled'>disabled</NgButton>
  </>
);

export default Index;`}
      />
      <Divider style={{ background: '#fff' }} />

      <NgHighlightCode
        title={'ghost'}
        children={`import React from 'react';
import NgButton from '@/renderer/uiComponent/NgButton';

const Index: React.FC = () => (
  <>
    <NgButton type='ghost'>ghost</NgButton>
  </>
);

export default Index;`}
      />
      <Divider style={{ background: '#fff' }} />

      <NgHighlightCode
        title={'popover button'}
        children={`import React from 'react';
import NgButton from '@/renderer/uiComponent/NgButton';
import NgPopover from '@/renderer/uiComponent/NgPopover';
import NgButtonText from '@/renderer/uiComponent/NgButtonText';

const Index: React.FC = () => (
  <>
    <NgPopover
      placement={'top'}
      style={{marginRight:10,width:100}}
      open
      content={<>
        <div>This is a description.</div>
        <div style={{display:'flex',alignItems:'center',marginTop:10}}>
          <NgButtonText style={{marginRight:10}}>取消</NgButtonText>
          <NgButton buttonMode={'popover'} style={{marginRight:10}}>确定</NgButton>
          <NgButton buttonMode={'popover'} disabled>disabled</NgButton>
        </div>
      </>}
    >
      <NgButton buttonMode={'default'} style={{width:115,padding:'4px 12px'}}>
          show popover
      </NgButton>
    </NgPopover>
  </>
);

export default Index;`}
      />
      <Divider style={{ background: '#fff' }} />

      <NgHighlightCode
        title={'loading button'}
        children={`import React,{useState} from 'react';
import NgButton from '@/renderer/uiComponent/NgButton';

const Index: React.FC = () => {
  const [loading,setLoading] = useState(false);

  return <>
    <NgButton style={{marginLeft:10}} loading={loading} onClick={()=>{
      setLoading((v)=>!v);
      setTimeout(()=>{
        setLoading(false);
      },2000);
    }}>loading</NgButton>
  </>;
};

export default Index;`}
      />
      <Divider style={{ background: '#fff' }} />

      <NgHighlightCode
        title={'default button text'}
        children={`import React,{useState} from 'react';
import NgButtonText from '@/renderer/uiComponent/NgButtonText';

const Index: React.FC = () => {
  const [loading,setLoading] = useState(false);

  return <>
   <NgButtonText style={{marginTop:4,marginLeft:10,marginRight:10,display:\\'inline-block\\'}}>取消</NgButtonText>
  </>;
};

export default Index;`}
      />
      <Divider style={{ background: '#fff' }} />

      <NgHighlightCode
        title={'link button text'}
        children={`import React,{useState} from 'react';
import NgButtonText from '@/renderer/uiComponent/NgButtonText';

const Index: React.FC = () => {
  const [loading,setLoading] = useState(false);

  return <>
   <NgButtonText buttonTextMode={'link'} style={{marginTop:4,marginLeft:10,marginRight:10,display:\\'inline-block\\'}}>确定</NgButtonText>
  </>;
};

export default Index;`}
      />
    </div>
  );
};
export default ButtonCode;
