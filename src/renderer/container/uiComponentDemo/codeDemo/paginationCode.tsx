import React from 'react';
import NgHighlightCode from '@/renderer/uiComponent/NgHighlightCode';

type Props = {};
const PaginationCode = (props: Props) => {
  return (
    <div style={{ overflowY: 'scroll', height: '80vh', marginTop: 20 }}>
      <NgHighlightCode
        title={'Pagination'}
        children={`import React, { useCallback } from 'react';
import {NgPagination} from '@/renderer/uiComponent/NgPagination';

type Props = {

};
const Index = (props: Props) => {
  const [current, setCurrent] = React.useState(3);
  const [pageSize, setPageSize] = React.useState(10);

  const handleChangeCurrent= useCallback((currentPage: number, currentPageSize: number) => {
    setCurrent(currentPage);
    setPageSize(currentPageSize);
  },[]);

  const handleChangePageSize = useCallback((currentPage: number, currentPageSize: number) => {
    setCurrent(currentPage);
    setPageSize(currentPageSize);
  },[]);

  return (
    <NgPagination total={67} pageSize={pageSize} current={current} onChange={handleChangeCurrent} onShowSizeChange={handleChangePageSize}/>

  );
};
export default Index;`}
      />
    </div>
  );
};
export default PaginationCode;
