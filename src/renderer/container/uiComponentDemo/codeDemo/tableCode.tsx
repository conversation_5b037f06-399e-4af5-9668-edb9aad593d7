import React from 'react';
import NgHighlightCode from '@/renderer/uiComponent/NgHighlightCode';

type Props = {};
// eslint-disable-next-line max-lines-per-function
const TableCode = (props: Props) => {
  return (
    <div style={{ overflowY: 'scroll', height: '80vh', marginTop: 20 }}>
      <NgHighlightCode
        title={'table'}
        children={`import React, {useState} from 'react';
import {ColumnsType} from 'antd/lib/table';
import moment from 'moment';
import {NgIcon} from '@/renderer/uiComponent/NgIcon';
import {Eye, TriangleWarn} from '@/renderer/uiComponent/SvgGather';
import NgTable from '@/renderer/uiComponent/NgTable';
import {TablePaginationConfig} from 'antd';
const dataSource: DataType[] =  [];
// mock
for (let i = 0; i < 100; i++) {
  dataSource.push({
    key: i,
    name: 'user_'+i+ 1, 
    'create time': new Date(2023,3,22,i+1,14,20),
    'column 2': 32,
    'column 3': 32,
    'column 4': 32,
    'column 5': 32,
  });
}
interface DataType {
  key: React.Key;
  name: string;
  'create time': Date;
  'column 2': number;
  'column 3': number;
  'column 4': number;
  'column 5': number;
}
const columns:  ColumnsType<DataType> = [
  {
    title: 'fixed left',
    dataIndex: 'name',
    key: 'name',
    width:160,
    fixed:'left',
  },
  {
    title: 'create time g',
    dataIndex: 'create time',
    width:300,
    key: 'create time',
    render:(record: Date)=>{
      return <>{moment(record).format('YYYY-MM-DD HH:mm:ss')}</>;
    },
    sorter: {
      compare: (a, b) => {
        // @ts-ignore
        return new Date(a['create time']) - new Date(b['create time']);
      },
    },
  },
  {
    title: 'column 2',
    dataIndex: 'column 2',
    width:300,
    key: 'column 2',
  },
  {
    title: 'column 3',
    dataIndex: 'column 3',
    key: 'column 3',
    width:300,
  },
  {
    title: 'column 4',
    dataIndex: 'column 4',
    key: 'column 4',
    width:300,
  },
  {
    title: 'column 5',
    dataIndex: 'column 5',
    key: 'column 5',
    width:300,

  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    width:150,
    fixed:'right',
    render:()=>{
      return <div style={{display:'flex'}}>
        <NgIcon iconSvg={TriangleWarn}/>
        <NgIcon iconSvg={Eye}/>
      </div>;
    },
  },

];
type Props = {

};
export const Index: React.FC<Props> = (props: Props) => {
  const [pagination,setPagination] = useState<TablePaginationConfig>({
    total:100,
    current:1,
    pageSize:10,
  });
  const handlePaginationChange = (page: TablePaginationConfig)=>{
    setPagination({...page});
  };

  return (
    <>
      <NgTable
        data={dataSource}
        columns={columns}
        onChange={handlePaginationChange}
        // ! 如果传了 rowClassName，所有 行样式 都需要从调用NgTable处控制
        // ! 注意：传入rowClassName的同时，如果columns中的某一列包含fixed属性需要额外在外部控制fixed样式
        // rowClassName={()=>'row-item'}
        scroll={{x:1500,y:300}}
        pagination={{
          ...pagination,
          showQuickJumper:true,
          pageSizeOptions: ['10','20','50','80'],
          showSizeChanger:true, // 隐藏每页页数
        }}
      />
    </>
  );
};
export default Index;
`}
      />
    </div>
  );
};
export default TableCode;
