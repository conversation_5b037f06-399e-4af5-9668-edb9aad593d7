import React from 'react';
import NgHighlightCode from '@/renderer/uiComponent/NgHighlightCode';
import { Divider } from 'antd';

type Props = {};
const InputCode = (props: Props) => {
  return (
    <div style={{ overflowY: 'scroll', height: '80vh', marginTop: 20 }}>
      <NgHighlightCode
        title={'input'}
        children={`import { NgInput } from '@/renderer/uiComponent/NgInput';
import React from 'react';

type Props = {

};
const Index = (props: Props) => {
  return (
    <NgInput />
  );
};
export default Index;`}
      />
      <Divider style={{ background: '#fff' }} />

      <NgHighlightCode
        title={'disabled'}
        children={`import { NgInput } from '@/renderer/uiComponent/NgInput';
import React from 'react';

type Props = {

};
const Index = (props: Props) => {
  return (
    <NgInput value={444} disabled />
  );
};
export default Index;
`}
      />
      <Divider style={{ background: '#fff' }} />

      <NgHighlightCode
        title={'placeholder'}
        children={`import { NgInput } from '@/renderer/uiComponent/NgInput';
import React from 'react';

type Props = {

};
const Index = (props: Props) => {
  return (
    <NgInput placeholder='请输入相关内容' />
  );
};
export default Index;
`}
      />
      <Divider style={{ background: '#fff' }} />

      <NgHighlightCode
        title={'form + input'}
        children={`import { NgForm, NgFormItem } from '@/renderer/uiComponent/NgForm';
import { NgInput } from '@/renderer/uiComponent/NgInput';
import React from 'react';

type Props = {

};
const Index = (props: Props) => {
  return (
    <NgForm
      name="basic">
      <NgFormItem
        name="username"
        rules={[{ required: true, message: 'Please input your username!' }]}>
        <NgInput status="error" style={{ marginTop: '20px' }} />
      </NgFormItem>
    </NgForm>
  );
};
export default Index;
`}
      />
      <Divider style={{ background: '#fff' }} />

      <NgHighlightCode
        title={'input number'}
        children={`import { NgInputNumber } from '@/renderer/uiComponent/NgInputNumber';
import React, { useState, useCallback } from 'react';

type Props = {
  defaultValue: number;
};
const Index = (props: Props) => {
  const [valueNumber,setValNumber] = useState(1);
  const handleChangeValue = useCallback((value: number | string | null) => {
    if (value === null) {
      setValNumber(props.defaultValue);

      return;
    }
    if (typeof value === 'string') {
      setValNumber(Number(value));

      return;
    }
    setValNumber(value);
  }, []);

  return (
    <NgInputNumber defaultValue={1} value={valueNumber} step={0.5} min={1} max={10} onChange={handleChangeValue}/>

  );
};
export default Index;`}
      />
    </div>
  );
};
export default InputCode;
