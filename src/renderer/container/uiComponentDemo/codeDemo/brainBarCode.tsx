import React from 'react';
import NgHighlightCode from '@/renderer/uiComponent/NgHighlightCode';
import { Divider } from 'antd';

type Props = {};
const BrainBarCode = (props: Props) => {
  return (
    <div style={{ overflowY: 'scroll', height: '80vh', marginTop: 20 }}>
      <NgHighlightCode
        title={'BrainBar'}
        children={`import React, { useCallback } from 'react';
import NgBrain from '@/renderer/uiComponent/NgBrain';
import styles from './index.module.less';

type Props = {

};
export const Index: React.FC<Props> = (props: Props) => {
  const handleIconClick = useCallback((field: string) => {
    console.log(field); // eslint-disable-line no-console
  },[]);

  const onChangeScalpOpacity = useCallback((value: number) => {
    console.log('onChangeScalpOpacity', value);  // eslint-disable-line no-console
  },[]);

  return (
    <div className={styles.ng_demo}>
      <NgBrain iconClick={handleIconClick} location={'BrainLocationFront'} onChangeScalpOpacity={onChangeScalpOpacity}/>
    </div>
  );
};
export default Index;`}
      />
      <Divider style={{ background: '#fff' }} />
      <NgHighlightCode
        title={'Slider'}
        children={`import React  from 'react';
import { NgSlider } from '@/renderer/uiComponent/NgSlider';
import styles from './index.module.less';

type Props = {

};
export const Index: React.FC<Props> = (props: Props) => {
  return (
    <div className={styles.ng_demo}>
      <div className={styles.mt16} style={{width: '300px'}}>
        <NgSlider min={0} max={100} defaultValue={50} />
      </div>
      <div className={styles.mt16} style={{width: '300px'}}>
        <NgSlider min={0} max={100} defaultValue={80} isGray/>
      </div>
      <div className={styles.mt16} style={{width: '300px', marginTop: 100}}>
        <NgSlider min={0} max={100} defaultValue={20} suffix={'%'}/>
      </div>
    </div>
  );
};
export default Index;
`}
      />
    </div>
  );
};
export default BrainBarCode;
