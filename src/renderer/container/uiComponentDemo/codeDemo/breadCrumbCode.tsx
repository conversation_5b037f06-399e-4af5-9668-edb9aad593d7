import React from 'react';
import NgHighlightCode from '@/renderer/uiComponent/NgHighlightCode';

type Props = {};
const BreadCrumbCode = (props: Props) => {
  return (
    <div style={{ marginTop: 20 }}>
      <NgHighlightCode
        title={'Bread Crumb'}
        children={`import React  from 'react';
import {NgBreadcrumb} from '@/renderer/uiComponent/NgBreadCrumb';
import {Route} from 'antd/es/breadcrumb/Breadcrumb';
import {formatBreadcrumb, routers} from '@/renderer/router/routers';

type Props = {

};
const home = {
  path: '/',
  breadcrumbName:'首页',
};
const demo= {
  path: '/demo/button',
  breadcrumbName:'demo',
};
export const Index: React.FC<Props> = (props: Props) => {
  const [currentRoutes] = React.useState(formatBreadcrumb(routers));
  const [renderRoutes] = React.useState<Route[]>(() =>{
    return [home, demo].concat([currentRoutes[10]]);
  });

  return (
    <>
       面包屑采用 外部容器宽度,请根据实际情况调整容器宽度
      <div style={{width: '30%'}}>
        <NgBreadcrumb
          isGray
          routes={renderRoutes}
        />
      </div>
    </>
  );
};
export default Index;
`}
      />
    </div>
  );
};
export default BreadCrumbCode;
