import React from 'react';
import NgHighlightCode from '@/renderer/uiComponent/NgHighlightCode';

type Props = {};
const IconCode = (props: Props) => {
  return (
    <div style={{ overflowY: 'scroll', height: '80vh', marginTop: 20 }}>
      <NgHighlightCode
        title={'Icons'}
        children={`import React from 'react';
import {Setting} from '@/renderer/uiComponent/SvgGather';
import {NgIcon} from '@/renderer/uiComponent/NgIcon';

type Props = {

};
const Index = (props: Props) => {
  // 具体ICON参考：src/renderer/uiComponent/SvgGather/index.ts
  return (
    <NgIcon iconSvg={Setting} fontSize={20}/>
  );
};
export default Index;`}
      />
    </div>
  );
};
export default IconCode;
