import React from 'react';
import NgHighlightCode from '@/renderer/uiComponent/NgHighlightCode';

type Props = {};
const StepsCode = (props: Props) => {
  return (
    <div style={{ marginTop: 20 }}>
      <NgHighlightCode
        title={'Steps'}
        children={`import { NgSteps } from '@/renderer/uiComponent/NgStep';
import React,{useState} from 'react';

type Props = {

};
const Index = (props: Props) => {
  const [current, setCurrent] = useState(0);

  return (
    <NgSteps
      current={current}
      onChange={setCurrent}
      items={
        [
          {
            title: 'Step 1',
          },
          {
            title: 'Step 2',
          },
          {
            title: 'Step 3',
          },
          {
            title: 'Step 4',
          },
        ]
      }/>
  );
};
export default Index;
`}
      />
    </div>
  );
};
export default StepsCode;
