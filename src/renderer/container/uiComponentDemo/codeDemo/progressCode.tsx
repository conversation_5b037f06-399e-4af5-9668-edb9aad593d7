import React from 'react';
import NgHighlightCode from '@/renderer/uiComponent/NgHighlightCode';
import { Divider } from 'antd';

type Props = {};
const ProgressCode = (props: Props) => {
  return (
    <div style={{ overflowY: 'scroll', height: '80vh', marginTop: 20 }}>
      <NgHighlightCode
        title={'progress line'}
        children={`import React  from 'react';
import {NgProgress} from '@/renderer/uiComponent/NgProgress';

type Props = {

};
export const Index: React.FC<Props> = (props: Props) => {
  const [percent] = React.useState(50);

  return (
    <>
      <NgProgress
        percent={percent}
        type={'line'}
        width={180}
      />
    </>
  );
};
export default Index;
`}
      />
      <Divider style={{ background: '#fff' }} />

      <NgHighlightCode
        title={'progress circle'}
        children={`import React  from 'react';
import {NgProgress} from '@/renderer/uiComponent/NgProgress';

type Props = {

};
export const Index: React.FC<Props> = (props: Props) => {
  const [percent] = React.useState(50);

  return (
    <>
      <NgProgress
        percent={percent}
        type={'circle'}
        width={180}
        strokeWidth={7}
      />
    </>
  );
};
export default Index;`}
      />
      <Divider style={{ background: '#fff' }} />

      <NgHighlightCode
        title={'upload progress'}
        children={`import React  from 'react';
import { NgProgressUpload } from '@/renderer/uiComponent/NgProgress';

type Props = {

};
export const Index: React.FC<Props> = (props: Props) => {
  const [percent] = React.useState(50);

  return (
    <>
      <NgProgressUpload
        percent={percent}
        type={'line'}
        uploadFileName={'这里是上传选择的文件名'}
        width={480}
      />
    </>
  );
};
export default Index;;`}
      />
    </div>
  );
};
export default ProgressCode;
