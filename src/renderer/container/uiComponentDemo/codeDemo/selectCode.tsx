import React from 'react';
import NgHighlightCode from '@/renderer/uiComponent/NgHighlightCode';
import { Divider } from 'antd';

type Props = {};
const SelectCode = (props: Props) => {
  return (
    <div>
      <NgHighlightCode
        title={'NgSelect'}
        children={`import React from 'react';
import { NgSelect } from '@/renderer/uiComponent/NgSelect';

type Props = {

};
const Index = (props: Props) => {
  const options = [{ value: 'gold', label: 'gold' }, { value: 'lime', label: 'lime' }];

  return (

    <NgSelect
      // open={true} // 默认打开
      options={options}
    />
  );
};
export default Index;`}
      />
      <Divider style={{ background: '#fff' }} />

      <NgHighlightCode
        title={'NgTabs'}
        children={`import React from 'react';
import {NgTabs} from '@/renderer/uiComponent/NgTabs';

type Props = {

};
const Index = (props: Props) => {
  const tabItems = [
    {
      key: '1',
      label: 'Tab 1',
      children: \`Content of Tab Pane 1Content of Tab Pane 1Content of Tab Pane 1Content of Tab Pane 
            1Content of Tab Pane 1Content of Tab Pane 1Content of Tab Pane 1Content of Tab Pane 1Content of Tab Pane 1\`,
    },
    {
      key: '2',
      label: 'Tab 2',
      children: 'Content of Tab Pane 2',
    },
    {
      key: '3',
      label: 'Tab 3',
      children: 'Content of Tab Pane 3',
    },
  ];

  return (
    <NgTabs items={tabItems} type='line'/> // type = 'line' | 'card' | 'editable-card'
  );
};
export default Index;`}
      />
    </div>
  );
};
export default SelectCode;
