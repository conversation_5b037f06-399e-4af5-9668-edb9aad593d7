import React, { useState } from 'react';
import { NgIcon } from '@/renderer/uiComponent/NgIcon';
import {
  Setting,
  BrainLocationBack,
  BrainLocationFront,
  BrainLocationLeft,
  BrainLocationRight,
  TriangleWarn,
  Delete,
  BrainBarClose,
  BrainBarOpen,
  BrainDirectionGather,
  BrainLayout,
  BrainLocationTop,
  BrainLocationBottom,
  BrainVolumeGray,
  BrainOpacity,
  CalendarBlank,
  CalendarCheck,
  CalendarPlus,
  CircleDoubt,
  CircleDotsThree,
  CircleDotsThreeVertical,
  CircleGlassPlus,
  CircleGlassMinus,
  CircleMinus,
  CirclePlus,
  ContainerMax,
  Control,
  Copy,
  CloseLight,
  Cursor,
  Edit,
  EditUnderline,
  Eye,
  EyeSlash,
  Filter,
  Folder,
  FolderClose,
  FolderOpen,
  ImportTemplate,
  loading,
  Lock,
  Patient,
  Pulse,
  Rect,
  Shutdown,
  ToggleLeft,
  ToggleRight,
  Upload,
  WifiHigh,
  WifiSlash,
  Setting24,
  InfoMessage,
  ErrorMessage,
  SuccessMessage,
  WarnMessage,
  Bat,
  Camera,
  SortAsc,
  SortDefault,
  SortDesc,
  Search,
} from '@/renderer/uiComponent/SvgGather';
import { PopconfirmProps, TooltipProps } from 'antd';
import styles from './index.module.less';
import { TooltipPlacement } from 'antd/es/tooltip';
import { ReactComponent } from '@/custom';
import IconCode from '@/renderer/container/uiComponentDemo/codeDemo/iconCode';
import { withUserSession } from '@/renderer/hocComponent/withUserSession';
import { RouterProps, withRouter } from '@/renderer/hocComponent/withRouter';
type Props = RouterProps;
// eslint-disable-next-line max-lines-per-function
export const Icons: React.FC<Props> = (props: RouterProps) => {
  const [useLocation, setUseLocation] = useState('');
  const { router } = props;
  const [svgList] = useState([
    {
      field: 'BrainLocationRight',
      svg: BrainLocationRight,
      tooltip: {
        title: 'right',
        placement: 'topRight',
      } as TooltipProps,
    },
    {
      field: 'CloseLight',
      svg: CloseLight,
      popConfirm: {
        title: '提示',
        description: '确定要删除吗？',
        icon: TriangleWarn,
        onConfirm: () => {
          // console.log('confirm');
        },
        onCancel: () => {
          // console.log('cancel');
        },
      },
    },
    {
      field: 'TriangleWarn',
      svg: Delete,
      tooltip: {
        title: 'right',
        placement: 'topRight',
      } as TooltipProps,
      popConfirm: {
        title: '提示',
        placement: 'bottomLeft' as TooltipPlacement,
        description: '确定要删除吗？',
        icon: TriangleWarn,
        onConfirm: () => {
          // console.log('confirm');
        },
        onCancel: () => {
          // console.log('cancel');
        },
      },
    },
    { field: 'BrainBarClose', svg: BrainBarClose },
    { field: 'BrainBarOpen', svg: BrainBarOpen },
    { field: 'BrainDirectionGather', svg: BrainDirectionGather },
    { field: 'BrainLayout', svg: BrainLayout },
    { field: 'BrainLocationTop', svg: BrainLocationTop },
    { field: 'BrainLocationBottom', svg: BrainLocationBottom },
    { field: 'BrainVolumeGray', svg: BrainVolumeGray },
    { field: 'BrainOpacity', svg: BrainOpacity },
    { field: 'Bat', svg: Bat },
    { field: 'Camera', svg: Camera },
    { field: 'CalendarBlank', svg: CalendarBlank },
    { field: 'CalendarCheck', svg: CalendarCheck },
    { field: 'CalendarPlus', svg: CalendarPlus },
    { field: 'CircleDoubt', svg: CircleDoubt },
    { field: 'CircleDotsThree', svg: CircleDotsThree },
    { field: 'CircleDotsThreeVertical', svg: CircleDotsThreeVertical },
    { field: 'CircleMinus', svg: CircleMinus },
    { field: 'CirclePlus', svg: CirclePlus },
    { field: 'ContainerMax', svg: ContainerMax },
    { field: 'Control', svg: Control },
    { field: 'Copy', svg: Copy },
    { field: 'Cursor', svg: Cursor },
    { field: 'Edit', svg: Edit },
    { field: 'EditUnderline', svg: EditUnderline },
    { field: 'Eye', svg: Eye },
    { field: 'EyeSlash', svg: EyeSlash },
    { field: 'Filter', svg: Filter },
    { field: 'Folder', svg: Folder },
    { field: 'FolderClose', svg: FolderClose },
    { field: 'ImportTemplate', svg: ImportTemplate },
    { field: 'FolderOpen', svg: FolderOpen },
    { field: 'loading', svg: loading },
    { field: 'Lock', svg: Lock },
    { field: 'CircleGlassMinus', svg: CircleGlassMinus },
    { field: 'CircleGlassPlus', svg: CircleGlassPlus },
    { field: 'Patient', svg: Patient },
    { field: 'Pulse', svg: Pulse },
    { field: 'Rect', svg: Rect },
    {
      field: 'Shutdown',
      svg: Shutdown,
      tooltip: {
        title: 'right',
        placement: 'topRight',
      } as TooltipProps,
      popConfirm: {
        title: '提示',
        placement: 'bottomLeft' as TooltipPlacement,
        description: '确定要登出吗？',
        icon: TriangleWarn,
        onConfirm: () => {
          router?.navigate?.('/login');
        },
        onCancel: () => {
          // console.log('cancel');
        },
      },
    },
    { field: 'Search', svg: Search },
    { field: 'SortAsc', svg: SortAsc },
    { field: 'SortDefault', svg: SortDefault },
    { field: 'SortDesc', svg: SortDesc },
    { field: 'ToggleLeft', svg: ToggleLeft },
    { field: 'ToggleRight', svg: ToggleRight },
    { field: 'Upload', svg: Upload },
    { field: 'WifiHigh', svg: WifiHigh },
    { field: 'WifiSlash', svg: WifiSlash },
    { field: 'InfoMessage', svg: InfoMessage },
    { field: 'ErrorMessage', svg: ErrorMessage },
    { field: 'SuccessMessage', svg: SuccessMessage },
    { field: 'WarnMessage', svg: WarnMessage },
    {
      field: 'Setting',
      svg: Setting,
    },
    {
      field: 'BrainLocationFront',
      svg: BrainLocationFront,
    },
    {
      field: 'BrainLocationBack',
      svg: BrainLocationBack,
      tooltip: {
        title: 'back',
        placement: 'topRight',
      } as TooltipProps,
    },
    {
      field: 'BrainLocationLeft',
      svg: BrainLocationLeft,
      tooltip: {
        title: 'left',
        placement: 'topRight',
      } as TooltipProps,
    },
  ]);

  return (
    <div className={styles.ng_demo} style={{ padding: 0 }}>
      <div className={styles.header}>
        <div>前三个组合了 tooltip 和 气泡确认框</div>
        <span>您点击了：{useLocation}</span>
      </div>
      <div className={styles.content}>
        {svgList.map(item => {
          return (
            <NgIcon
              key={item.field}
              tooltip={item.tooltip}
              popConfirm={item.popConfirm as unknown as PopconfirmProps}
              iconSvg={item.svg as ReactComponent}
              fontSize={20}
              onClick={() => setUseLocation(item.field)}
            />
          );
        })}
        <NgIcon iconSvg={Setting} fontSize={20} />
        <NgIcon iconSvg={Setting24} fontSize={24} />
      </div>
      <IconCode />
    </div>
  );
};
export default withUserSession(withRouter(Icons));
