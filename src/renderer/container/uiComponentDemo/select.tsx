import React from 'react';
import { NgSelect } from '../../uiComponent/NgSelect';
import { NgTabs } from '../../uiComponent/NgTabs';
import SelectCode from '@/renderer/container/uiComponentDemo/codeDemo/selectCode';

export const Select = () => {
  const options = [
    { value: 'gold', label: 'gold' },
    { value: 'lime', label: 'lime' },
    { value: 'green', label: 'green' },
    { value: 'cyan', label: 'cyan' },
  ];
  const tabItems = [
    {
      key: '1',
      label: 'Tab 1',
      children:
        'Content of Tab Pane 1Content of Tab Pane 1Content of Tab Pane 1Content of Tab Pane 1Content of Tab Pane 1Content of Tab Pane 1Content of Tab Pane 1Content of Tab Pane 1Content of Tab Pane 1',
    },
    {
      key: '2',
      label: 'Tab 2',
      children: 'Content of Tab Pane 2',
    },
    {
      key: '3',
      label: 'Tab 3',
      children: 'Content of Tab Pane 3',
    },
  ];

  return (
    <div style={{ display: 'flex' }}>
      <div style={{ width: '300px', color: '#fff', marginRight: 50 }}>
        <NgSelect
          // open={true}
          options={options}
        />
        <div style={{ marginTop: '20px' }} />
        <NgSelect open options={options} />
        <div style={{ marginTop: '280px' }} />
        <NgTabs items={tabItems} type="line" />
        <div style={{ marginTop: '20px' }} />
        <NgTabs items={tabItems} type="card" />
        <div style={{ marginTop: '20px' }} />
        <NgTabs items={tabItems} />
      </div>
      <SelectCode />
    </div>
  );
};

export default Select;
