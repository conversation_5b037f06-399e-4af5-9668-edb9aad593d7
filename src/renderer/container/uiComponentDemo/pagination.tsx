import React, { useCallback } from 'react';
import { NgPagination } from '@/renderer/uiComponent/NgPagination';
import styles from './index.module.less';
import { Divider } from 'antd';
import PaginationCode from '@/renderer/container/uiComponentDemo/codeDemo/paginationCode';

type Props = {};
export const Pagination: React.FC<Props> = (props: Props) => {
  const [current1, setCurrent1] = React.useState(1);
  const [current2, setCurrent2] = React.useState(3);
  const [pageSize1, setPageSize1] = React.useState(10);
  const [pageSize2, setPageSize2] = React.useState(10);

  const handleChangeCurrent1 = useCallback((current: number, pageSize: number) => {
    setCurrent1(current1);
    setPageSize1(pageSize);
  }, []);
  const handleChangeCurrent2 = useCallback((current: number, pageSize: number) => {
    setCurrent2(current);
    setPageSize2(pageSize);
  }, []);
  const handleChangePageSize1 = useCallback((current: number, pageSize: number) => {
    setCurrent1(current);
    setPageSize1(pageSize);
  }, []);
  const handleChangePageSize2 = useCallback((current: number, pageSize: number) => {
    setCurrent2(current);
    setPageSize2(pageSize);
  }, []);

  return (
    <div className={styles.ng_demo} style={{ padding: 0 }}>
      <NgPagination total={47} pageSize={pageSize1} current={current1} onChange={handleChangeCurrent1} onShowSizeChange={handleChangePageSize1} />
      <Divider />
      <NgPagination
        total={800}
        isShowTotal
        pageSize={pageSize2}
        current={current2}
        onChange={handleChangeCurrent2}
        onShowSizeChange={handleChangePageSize2}
      />
      <PaginationCode />
    </div>
  );
};
export default Pagination;
