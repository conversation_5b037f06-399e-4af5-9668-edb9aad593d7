import React from 'react';
import { NgProgress, NgProgressUpload, NgStrengthProgress } from '@/renderer/uiComponent/NgProgress';
import styles from './index.module.less';
// eslint-disable-next-line import/no-internal-modules
import { Divider } from 'antd/lib';
import ProgressCode from '@/renderer/container/uiComponentDemo/codeDemo/progressCode';
import { InputNumber } from 'antd';

type Props = {};

export const Progress: React.FC<Props> = (props: Props) => {
  const [percent, setPercent] = React.useState(60);

  return (
    <div className={styles.ng_demo} style={{ padding: 0 }}>
      <div>
        <InputNumber value={percent} min={0} max={100} onChange={v => setPercent(v ? v : 0)} />
      </div>
      <div style={{ width: 200, height: 60 }}>
        <NgProgress percent={percent} type={'line'} width={180} />
      </div>

      <Divider />
      <div style={{ width: 800, height: 200, display: 'inline-flex', flexDirection: 'row' }}>
        <div>
          <span>李怡说： 到时候按照实际情况，传入strokeWidth 的值，来调整圆环宽度</span>
          <NgProgress
            percent={percent}
            type={'circle'}
            width={180}
            strokeWidth={16}
            status={'normal'}
            strokeColor={{ '0%': '#1A6533', '100%': '#49EB74' }}
          />
        </div>
        <div style={{ width: 400, height: 400 }}>
          <NgStrengthProgress percent={percent} radius={60} />
        </div>
      </div>
      <Divider />
      <div style={{ width: 500, height: 200 }}>
        <NgProgressUpload percent={percent} type={'line'} uploadFileName={'这里是上传选择的文件名'} width={480} />
      </div>
      <Divider />

      <ProgressCode />
    </div>
  );
};
export default Progress;
