import React, { useState } from 'react';
import NgPopover from '@/renderer/uiComponent/NgPopover';
import NgButton from '@/renderer/uiComponent/NgButton';
import NgButtonText from '@/renderer/uiComponent/NgButtonText';
import NgAlert from '@/renderer/uiComponent/NgAlert';
import { ReactComponent as Warning } from '@/renderer/static/svg/warnMessage.svg';
import { ReactComponent as Info } from '@/renderer/static/svg/infoMessage.svg';
import { ReactComponent as Error } from '@/renderer/static/svg/errorMessage.svg';
import { ReactComponent as Success } from '@/renderer/static/svg/successMessage.svg';
import PopoverCode from '@/renderer/container/uiComponentDemo/codeDemo/popoverCode';

type Props = {};
// eslint-disable-next-line max-lines-per-function
const PopoverContainer = (props: Props) => {
  const [popOpen, setPopOpen] = useState(false);
  const [popOver4, setPopOver4] = useState(false);
  const buttonWidth = 70;
  const text = <span>prompt text</span>;
  const operations = {
    onOk: () => {
      setPopOver4(false);
    },
    onCancel: () => {
      setPopOver4(false);
    },
  };

  return (
    <div style={{ display: 'flex', flexWrap: 'wrap' }}>
      <div style={{ marginRight: 10 }}>
        <div>
          <div style={{ marginLeft: buttonWidth, display: 'flex', whiteSpace: 'nowrap' }}>
            <NgPopover placement="topLeft" title={text}>
              <NgButton style={{ width: 80, marginRight: 15 }} onClick={() => setPopOver4(true)}>
                TL
              </NgButton>
            </NgPopover>
            <NgPopover open={popOver4} trigger={'click'} showOperation={operations} placement="top" title={text}>
              <NgButton style={{ width: 80, marginRight: 15 }} onClick={() => setPopOver4(true)}>
                Top
              </NgButton>
            </NgPopover>
            <NgPopover placement="topRight" title={text}>
              <NgButton style={{ width: 80, marginRight: 15 }} onClick={() => setPopOver4(true)}>
                TR
              </NgButton>
            </NgPopover>
          </div>
          <div style={{ float: 'left' }}>
            <NgPopover placement="leftTop" title={text}>
              <NgButton style={{ width: 80, marginTop: 15 }}>LT</NgButton>
            </NgPopover>
            <NgPopover placement="left" title={text}>
              <NgButton style={{ width: 80, marginTop: 15 }}>Left</NgButton>
            </NgPopover>
            <NgPopover placement="leftBottom" title={text}>
              <NgButton style={{ width: 80, marginTop: 15 }}>LB</NgButton>
            </NgPopover>
          </div>
          <div style={{ marginLeft: buttonWidth * 4 + 24 }}>
            <NgPopover placement="rightTop" title={text}>
              <NgButton style={{ width: 80, marginLeft: 15, marginTop: 15 }}>RT</NgButton>
            </NgPopover>
            <NgPopover placement="right" title={text}>
              <NgButton style={{ width: 80, marginLeft: 15, marginTop: 15 }}>Right</NgButton>
            </NgPopover>
            <NgPopover placement="rightBottom" title={text}>
              <NgButton style={{ width: 80, marginLeft: 15, marginTop: 15 }}>RB</NgButton>
            </NgPopover>
          </div>
          <div style={{ marginLeft: buttonWidth, clear: 'both', display: 'flex', whiteSpace: 'nowrap' }}>
            <NgPopover placement="bottomLeft" title={text}>
              <NgButton style={{ width: 80, marginTop: 15 }}>BL</NgButton>
            </NgPopover>
            <NgPopover placement="bottom" title={text}>
              <NgButton style={{ width: 80, marginTop: 15, marginLeft: 15 }}> Bottom</NgButton>
            </NgPopover>
            <NgPopover placement="bottomRight" title={text}>
              <NgButton style={{ width: 80, marginTop: 15, marginLeft: 15 }}>BR</NgButton>
            </NgPopover>
          </div>
        </div>
      </div>

      <div style={{ margin: '0 50px' }}>
        <NgPopover
          open={popOpen}
          showOperation={{
            onOk: () => {
              setPopOpen(false);
            },
            onCancel: () => setPopOpen(false),
          }}
          title={'popover title g'}
          placement={'top'}
          // ! 设置了点击事件 trigger=hover将失效
          trigger={'hover'}
          content={<>content</>}
        >
          <div>
            <NgButtonText onClick={() => setPopOpen(true)}>popover 5</NgButtonText>
          </div>
        </NgPopover>
      </div>
      <div style={{ marginRight: 10, marginTop: 20 }}>
        <NgAlert
          icon={<Info />}
          message="Informational Notes g Informational Notes g Informational Notes g Informational Notes g Informational Notes g"
          type="info"
          showIcon
        />
        <NgAlert
          style={{ marginTop: 20 }}
          icon={<Warning />}
          showIcon
          message="Warning Text Warning Text Warning TextW arning Text Warning Text Warning Text Warning Text"
          type="warning"
          closable
        />
        <NgAlert
          style={{ marginTop: 20 }}
          icon={<Error />}
          showIcon
          message="Error Text Error Text Error TextW arning Text Error Text Error Text Error Text"
          type="error"
          closable
        />
        <NgAlert
          style={{ marginTop: 20 }}
          icon={<Success />}
          showIcon
          message="success Text success Text success TextW arning Text success Text success Text success Text"
          type="success"
          closable
        />
      </div>
      <PopoverCode />
    </div>
  );
};
export default PopoverContainer;
