import React, { ReactElement } from 'react';
import { NgLoading } from '@/renderer/uiComponent/NgLoading';
import { Spin } from 'antd';
import styles from './index.module.less';
// eslint-disable-next-line import/no-internal-modules
import { Divider } from 'antd/lib';
import LoadingCode from '@/renderer/container/uiComponentDemo/codeDemo/loadingCode';

type Props = {};
export const Loading: React.FC<Props> = (props: Props) => {
  const [loading, setLoading] = React.useState(true);

  React.useEffect(() => {
    setTimeout(() => {
      setLoading(false);
    }, 6000);
  }, []);
  const renderLoading = (): ReactElement => {
    return (
      <div>
        <NgLoading loadingText={'页面加载中,6秒'} />
      </div>
    );
  };

  return (
    <Spin spinning={loading} indicator={renderLoading()}>
      <div className={styles.ng_demo}>
        <NgLoading />
        <Divider />
        <NgLoading loadingText={'脑图加载中,请稍等'} />
        <LoadingCode />
      </div>
    </Spin>
  );
};
export default Loading;
