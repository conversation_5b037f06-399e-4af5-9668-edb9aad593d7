import React, { useState } from 'react';
import NgTable from '@/renderer/uiComponent/NgTable';
import { TablePaginationConfig } from 'antd';
import TableCode from '@/renderer/container/uiComponentDemo/codeDemo/tableCode';
import { getColumns, PlanStatus } from '../home/<USER>';
import { PlanModel } from '@/common/types';
import { useNavigate } from 'react-router-dom';
import { useIntl } from 'react-intl';
import { useAsyncEffect } from 'ahooks';

type Props = {};
export const mockData = [
  {
    id: 92,
    name: '龙青处算包',
    subject_id: 36,
    updated_at: 1612226360999,
    stimulus: {
      pulse_total: 15,
      relative_strength: 41,
      strand_pulse_count: 10,
      target_id: 66,
      treatment_time: 875485229369,
      type: '3',
      remark: 'ipsum dolor incididunt',
      strand_pulse_frequency: 33,
      id: 70,
      created_id: 42,
      plan_id: 51,
      source: 9,
      plexus_count: 84,
      intermission_time: 1387159447522,
      inner_strand_pulse_count: 64,
      plexus_inter_frequency: 34,
      plexus_inner_frequency: 78,
      trace_id: '44',
      updated_id: 38,
      plexus_inner_pulse_count: 43,
      updated_at: 307987305383,
      created_at: 1631415413608,
      subject_id: 38,
    },
    updated_treatment_at: 92,
    created_treatment_at: 27,
    updated_id: 66,
    created_at: 385711802889,
    remark: 'ut',
    plan_delete_target_id_list: [84, 49, 100, 3],
    plan_file_model_list: [
      {
        trace_id: '8',
        updated_at: 1062927823518,
        name: '子月风',
        id: 42,
        remark: 'in dolor mollit',
        created_at: 94886122357,
        plan_id: 24,
        updated_id: 90,
        created_id: 85,
        subject_id: 2,
        relative_path: 'eiusmod dolor esse mollit',
      },
    ],
    status: PlanStatus.Pending,
    subject_model: {
      code: '34',
      id: 16,
      name: '几常米报整她',
      pinyin_username: '金平',
      phone: '18111617513',
      treatment_count: 0,
      condition_desc: 'ipsum deserunt occaecat velit',
      has_demo: true,
      sex: 90,
      remark: 'mollit voluptate',
      created_at: 310140321199,
      plan_count: 50,
      birth_date: 19710802,
      updated_at: 655336248509,
      created_user_name: '黄洋',
      motion_threshold: 0,
      updated_id: 84,
      created_id: 68,
    },
    created_user_name: '程敏',
    plan_import_model: {
      file_name: '例来山收',
      plan_name: '采华划南了',
      subject_id: 64,
      temp_directory: 'ex nulla adipisicing ipsum sed',
    },
    type: 1,
    created_id: 91,
    treatment_count: 0,
    target_count: 71,
    has_demo: true,
    plan_target_model_list: [
      {
        hemi: 'lh',
        name: '报飞增头林',
        surf_ras: {
          x: 87,
          y: 6,
          z: 86,
        },
        vertex_index: 19,
        vol_ras: {
          x: 74,
          y: 24,
          z: 47,
        },
        updated_id: 62,
        id: 35,
        code: '96',
        created_id: 5,
        clear_normal_line: false,
        remark: 'occaecat magna ad Lorem sint',
        normal_line: {
          ver: 13,
          x: 82,
          y: 96,
          z: 84,
        },
        score: 54,
        horizontal: 84,
        trace_id: '22',
        stimulus: {
          pulse_total: 59,
          relative_strength: 25,
          strand_pulse_count: 53,
          target_id: 4,
          treatment_time: 1361856251790,
          type: '5',
          remark: 'ea proident incididunt Excepteur',
          strand_pulse_frequency: 34,
          plexus_inner_frequency: 25,
          updated_id: 76,
          updated_at: 106981660208,
          plexus_inner_pulse_count: 83,
          plan_id: 28,
          inner_strand_pulse_count: 50,
          intermission_time: 994989975133,
          source: 60,
          trace_id: '16',
          id: 32,
          subject_id: 5,
          plexus_inter_frequency: 32,
          plexus_count: 7,
          created_id: 52,
          created_at: 576715388679,
        },
        subject_id: 46,
        source: '0',
        type: 'sunt magna',
        has_mep: false,
        created_at: 1444120583768,
        plan_id: 75,
        score_index: 38,
        updated_at: 778638110317,
      },
      {
        hemi: 'rh',
        name: '为局表',
        surf_ras: {
          x: 22,
          y: 55,
          z: 53,
        },
        vertex_index: 86,
        vol_ras: {
          x: 28,
          y: 29,
          z: 80,
        },
        created_id: 84,
        horizontal: 41,
        normal_line: {
          ver: 73,
          x: 23,
          y: 72,
          z: 91,
        },
        type: 'Lorem dolore velit nostrud',
        source: '0',
        code: '49',
        has_mep: false,
        created_at: 1670627925756,
        updated_at: 650198780369,
        score_index: 57,
        id: 57,
        updated_id: 32,
        score: 34,
        trace_id: '99',
        stimulus: {
          pulse_total: 94,
          relative_strength: 33,
          strand_pulse_count: 26,
          target_id: 6,
          treatment_time: 654863133868,
          type: '4',
          subject_id: 2,
          source: 83,
          updated_at: 28054101528,
          intermission_time: 1383547847054,
          plexus_inner_pulse_count: 81,
          id: 4,
          updated_id: 57,
          remark: 'consectetur proident aute',
          created_id: 54,
          inner_strand_pulse_count: 91,
          trace_id: '90',
          plan_id: 55,
          strand_pulse_frequency: 82,
          plexus_count: 14,
          plexus_inter_frequency: 82,
          plexus_inner_frequency: 25,
          created_at: 1434941479849,
        },
        subject_id: 79,
        remark: 'sunt eiusmod ea et',
        plan_id: 3,
        clear_normal_line: false,
      },
      {
        hemi: 'rh',
        name: '传究究非',
        surf_ras: {
          x: 16,
          y: 7,
          z: 42,
        },
        vertex_index: 58,
        vol_ras: {
          x: 11,
          y: 27,
          z: 99,
        },
        stimulus: {
          pulse_total: 82,
          relative_strength: 62,
          strand_pulse_count: 63,
          target_id: 96,
          treatment_time: 1143237454858,
          type: '4',
          plexus_inter_frequency: 74,
          inner_strand_pulse_count: 5,
          strand_pulse_frequency: 84,
          updated_at: 343477642225,
          plan_id: 33,
          plexus_inner_pulse_count: 45,
          remark: 'dolor deserunt reprehenderit ut',
          plexus_inner_frequency: 56,
          trace_id: '65',
          updated_id: 76,
          intermission_time: 1022959151632,
          created_id: 3,
          subject_id: 69,
          created_at: 606903978694,
          id: 35,
          source: 73,
          plexus_count: 86,
        },
        updated_at: 892439014946,
        clear_normal_line: true,
        id: 18,
        remark: 'sunt do in minim',
        trace_id: '57',
        plan_id: 99,
        code: '85',
        created_at: 882139458956,
        subject_id: 84,
        has_mep: false,
        score: 12,
        score_index: 1,
        horizontal: 152,
        updated_id: 10,
        source: '0',
        type: 'Excepteur enim fugiat nostrud amet',
        normal_line: {
          ver: 44,
          x: 92,
          y: 46,
          z: 100,
        },
        created_id: 7,
      },
      {
        hemi: 'rh',
        name: '长面听',
        surf_ras: {
          x: 58,
          y: 34,
          z: 2,
        },
        vertex_index: 16,
        vol_ras: {
          x: 23,
          y: 62,
          z: 31,
        },
        trace_id: '35',
        code: '61',
        subject_id: 41,
        score: 3,
        type: 'sit nulla sint culpa voluptate',
        plan_id: 95,
        source: '0',
        created_id: 32,
        remark: 'dolore Excepteur est',
        has_mep: false,
        updated_at: 1260671975498,
        score_index: 8,
        horizontal: 30,
        stimulus: {
          pulse_total: 21,
          relative_strength: 79,
          strand_pulse_count: 24,
          target_id: 41,
          treatment_time: 429734930804,
          type: '4',
          id: 89,
          inner_strand_pulse_count: 55,
          subject_id: 86,
          plexus_inter_frequency: 73,
          updated_id: 54,
          intermission_time: 484477272785,
          plexus_inner_pulse_count: 4,
          created_at: 780932665764,
          plexus_inner_frequency: 90,
          remark: 'culpa in mollit',
          trace_id: '47',
          plan_id: 26,
          strand_pulse_frequency: 74,
          updated_at: 501711565605,
          created_id: 76,
          plexus_count: 12,
          source: 56,
        },
        created_at: 166122768570,
        id: 69,
        clear_normal_line: false,
        updated_id: 92,
        normal_line: {
          ver: 22,
          x: 6,
          y: 9,
          z: 32,
        },
      },
      {
        hemi: 'lh',
        name: '常看按打并',
        surf_ras: {
          x: 3,
          y: 82,
          z: 80,
        },
        vertex_index: 51,
        vol_ras: {
          x: 57,
          y: 71,
          z: 10,
        },
        code: '41',
        created_id: 90,
        updated_at: 480744744157,
        id: 19,
        remark: 'sint est qui',
        plan_id: 98,
        source: '0',
        trace_id: '62',
        type: 'aliquip',
        stimulus: {
          pulse_total: 64,
          relative_strength: 31,
          strand_pulse_count: 7,
          target_id: 81,
          treatment_time: 234280125349,
          type: '2',
          plexus_inner_frequency: 81,
          created_at: 852225141180,
          plexus_inter_frequency: 38,
          updated_id: 70,
          strand_pulse_frequency: 76,
          created_id: 99,
          plexus_count: 43,
          remark: 'fugiat aliquip',
          plexus_inner_pulse_count: 4,
          updated_at: 1661403769188,
          subject_id: 61,
          id: 6,
          intermission_time: 956202243944,
          inner_strand_pulse_count: 45,
          trace_id: '28',
          plan_id: 42,
          source: 94,
        },
        subject_id: 66,
        updated_id: 78,
        has_mep: false,
        score: 85,
        created_at: 125992473640,
        normal_line: {
          ver: 76,
          x: 25,
          y: 53,
          z: 24,
        },
        clear_normal_line: true,
        horizontal: 92,
        score_index: 69,
      },
    ],
    trace_id: '82',
  },
  {
    id: 30,
    name: '革例上由',
    subject_id: 74,
    treatment_count: 0,
    created_user_name: '范平',
    plan_file_model_list: [
      {
        created_at: 1124424453015,
        remark: 'dolore eu amet Duis dolor',
        updated_id: 35,
        id: 69,
        created_id: 14,
        name: '号史心战',
        plan_id: 53,
        relative_path: 'tempor incididunt ut dolor',
        trace_id: '15',
        updated_at: 1136055156552,
        subject_id: 54,
      },
      {
        subject_id: 58,
        name: '矿然无习打间',
        relative_path: 'elit dolor nostrud enim dolor',
        plan_id: 37,
        created_at: 1084919600994,
        remark: 'Excepteur aute',
        updated_id: 78,
        trace_id: '60',
        updated_at: 548180293044,
        id: 66,
        created_id: 28,
      },
      {
        plan_id: 41,
        id: 17,
        subject_id: 45,
        name: '期标设',
        relative_path: 'dolore dolor',
        remark: 'sint id qui ullamco labore',
        updated_at: 61820151086,
        created_at: 790504386959,
        updated_id: 15,
        trace_id: '32',
        created_id: 71,
      },
      {
        trace_id: '81',
        updated_id: 49,
        created_at: 1343451419091,
        updated_at: 199348687002,
        remark: 'officia aliquip culpa',
        plan_id: 65,
        name: '关确件品战构',
        subject_id: 87,
        id: 13,
        relative_path: 'do officia reprehenderit',
        created_id: 58,
      },
    ],
    created_at: 1445071241589,
    stimulus: {
      pulse_total: 11,
      relative_strength: 54,
      strand_pulse_count: 77,
      target_id: 40,
      treatment_time: 1502730948310,
      type: '1',
      updated_id: 27,
      created_at: 527330480350,
      plexus_count: 16,
      plexus_inner_pulse_count: 97,
      plexus_inner_frequency: 56,
      strand_pulse_frequency: 81,
      trace_id: '81',
      remark: 'dolor esse',
      created_id: 92,
      intermission_time: 830567068713,
      plexus_inter_frequency: 87,
      id: 3,
      subject_id: 34,
      plan_id: 57,
      updated_at: 1642143557119,
      source: 27,
      inner_strand_pulse_count: 67,
    },
    status: PlanStatus.Normal,
    plan_target_model_list: [
      {
        hemi: 'lh',
        name: '精林话千动',
        surf_ras: {
          x: 17,
          y: 13,
          z: 81,
        },
        vertex_index: 21,
        vol_ras: {
          x: 39,
          y: 32,
          z: 84,
        },
        plan_id: 52,
        clear_normal_line: true,
        created_id: 27,
        has_mep: true,
        source: '2',
        id: 50,
        updated_id: 35,
        normal_line: {
          ver: 5,
          x: 33,
          y: 82,
          z: 63,
        },
        code: '97',
        created_at: 1604618110853,
        remark: 'irure',
        score: 98,
        stimulus: {
          pulse_total: 73,
          relative_strength: 77,
          strand_pulse_count: 12,
          target_id: 83,
          treatment_time: 1634337872460,
          type: '2',
          plexus_count: 60,
          updated_at: 1514527712989,
          source: 95,
          remark: 'ad in reprehenderit',
          intermission_time: 1517670985038,
          trace_id: '98',
          plan_id: 21,
          inner_strand_pulse_count: 64,
          updated_id: 10,
          plexus_inner_pulse_count: 41,
          id: 70,
          created_at: 1555583599740,
          strand_pulse_frequency: 90,
          plexus_inter_frequency: 17,
          created_id: 87,
          subject_id: 65,
          plexus_inner_frequency: 14,
        },
        subject_id: 40,
        updated_at: 1611266452764,
        trace_id: '10',
        horizontal: 155,
        type: 'exercitation culpa non consectetur eiusmod',
        score_index: 76,
      },
      {
        hemi: 'lh',
        name: '压门且史今京',
        surf_ras: {
          x: 62,
          y: 93,
          z: 87,
        },
        vertex_index: 94,
        vol_ras: {
          x: 91,
          y: 62,
          z: 59,
        },
        id: 91,
        has_mep: true,
        normal_line: {
          ver: 1,
          x: 50,
          y: 86,
          z: 28,
        },
        updated_at: 830597781887,
        created_id: 64,
        clear_normal_line: true,
        stimulus: {
          pulse_total: 47,
          relative_strength: 52,
          strand_pulse_count: 25,
          target_id: 34,
          treatment_time: 296334293150,
          type: '1',
          plexus_count: 61,
          plexus_inter_frequency: 75,
          plan_id: 48,
          updated_id: 51,
          trace_id: '9',
          intermission_time: 1261316943354,
          id: 60,
          inner_strand_pulse_count: 44,
          updated_at: 1200526491398,
          created_at: 1225851256098,
          subject_id: 81,
          plexus_inner_pulse_count: 50,
          created_id: 19,
          strand_pulse_frequency: 88,
          remark: 'nostrud',
          plexus_inner_frequency: 44,
          source: 9,
        },
        created_at: 739230111120,
        score: 38,
        score_index: 76,
        trace_id: '6',
        horizontal: 96,
        plan_id: 9,
        code: '91',
        source: '0',
        remark: 'non tempor',
        type: 'elit',
        subject_id: 95,
        updated_id: 85,
      },
      {
        hemi: 'lh',
        name: '总取发',
        surf_ras: {
          x: 98,
          y: 72,
          z: 27,
        },
        vertex_index: 88,
        vol_ras: {
          x: 72,
          y: 21,
          z: 18,
        },
        remark: 'deserunt elit nisi sed',
        score_index: 36,
        source: '1',
        clear_normal_line: false,
        stimulus: {
          pulse_total: 37,
          relative_strength: 59,
          strand_pulse_count: 22,
          target_id: 54,
          treatment_time: 949253866083,
          type: '1',
          plan_id: 71,
          intermission_time: 299073020993,
          strand_pulse_frequency: 64,
          remark: 'ullamco',
          inner_strand_pulse_count: 20,
          source: 41,
          updated_at: 1435106962212,
          subject_id: 50,
          plexus_inter_frequency: 83,
          plexus_inner_pulse_count: 12,
          id: 36,
          plexus_count: 66,
          created_at: 508548224604,
          created_id: 86,
          trace_id: '73',
          updated_id: 72,
          plexus_inner_frequency: 100,
        },
        subject_id: 60,
        id: 64,
        plan_id: 16,
        updated_id: 74,
        has_mep: false,
        trace_id: '47',
        normal_line: {
          ver: 56,
          x: 61,
          y: 83,
          z: 81,
        },
        created_at: 455105753381,
        type: 'irure ullamco amet dolore',
        created_id: 52,
        score: 64,
        horizontal: -144,
        code: '67',
        updated_at: 1557943985327,
      },
      {
        hemi: 'rh',
        name: '火局离命处半传',
        surf_ras: {
          x: 67,
          y: 86,
          z: 97,
        },
        vertex_index: 94,
        vol_ras: {
          x: 77,
          y: 53,
          z: 93,
        },
        plan_id: 29,
        score_index: 88,
        updated_at: 599394168068,
        source: '2',
        clear_normal_line: true,
        created_at: 1017860146688,
        normal_line: {
          ver: 71,
          x: 90,
          y: 40,
          z: 31,
        },
        id: 50,
        created_id: 12,
        trace_id: '90',
        type: 'cupidatat irure ut elit',
        horizontal: -82,
        score: 94,
        has_mep: false,
        remark: 'incididunt sed ullamco',
        code: '24',
        stimulus: {
          pulse_total: 51,
          relative_strength: 52,
          strand_pulse_count: 46,
          target_id: 26,
          treatment_time: 1478882271019,
          type: '2',
          updated_id: 34,
          plan_id: 84,
          trace_id: '70',
          strand_pulse_frequency: 76,
          plexus_inter_frequency: 54,
          inner_strand_pulse_count: 87,
          plexus_count: 99,
          plexus_inner_pulse_count: 57,
          created_at: 777553810711,
          updated_at: 940304936094,
          source: 3,
          intermission_time: 1521133242648,
          id: 86,
          created_id: 51,
          remark: 'dolore cillum officia',
          plexus_inner_frequency: 37,
          subject_id: 24,
        },
        updated_id: 1,
        subject_id: 89,
      },
    ],
    trace_id: '58',
    updated_at: 917643575021,
    remark: 'labore',
    plan_import_model: {
      file_name: '相美层',
      plan_name: '备都没',
      subject_id: 25,
      temp_directory: 'pariatur sunt laborum consequat aliqua',
    },
    updated_id: 4,
    type: 2,
    created_treatment_at: 30,
    updated_treatment_at: 56,
    subject_model: {
      code: '12',
      id: 7,
      name: '其么金边就',
      pinyin_username: '郑杰',
      condition_desc: 'sint anim incididunt dolor',
      plan_count: 47,
      phone: '18123263324',
      created_id: 97,
      sex: 11,
      updated_id: 63,
      created_user_name: '乔娟',
      birth_date: 20220420,
      created_at: 908944012144,
      treatment_count: 0,
      motion_threshold: 98,
      updated_at: 494951400788,
      remark: 'officia minim in ea',
      has_demo: true,
    },
    has_demo: false,
    created_id: 84,
    plan_delete_target_id_list: [76, 67, 8, 72, 36],
    target_count: 60,
  },
  {
    id: 11,
    name: '接山结将置',
    subject_id: 38,
    updated_treatment_at: 468990985787,
    plan_file_model_list: [
      {
        updated_id: 96,
        id: 35,
        created_at: 684271708195,
        plan_id: 11,
        remark: 'pariatur est ut',
        subject_id: 45,
        relative_path: 'non Lorem cupidatat nostrud',
        name: '流器火头件技',
        created_id: 68,
        updated_at: 1621439290224,
        trace_id: '3',
      },
      {
        subject_id: 43,
        created_id: 18,
        remark: 'in elit ea',
        relative_path: 'occaecat anim proident non',
        updated_id: 67,
        id: 75,
        trace_id: '100',
        created_at: 623234001700,
        plan_id: 72,
        name: '因机斯流走',
        updated_at: 1415466244749,
      },
      {
        subject_id: 11,
        created_id: 99,
        relative_path: 'eu ea amet dolore',
        name: '结红难支',
        plan_id: 2,
        id: 67,
        updated_id: 83,
        updated_at: 383572425049,
        created_at: 259878213573,
        remark: 'dolor',
        trace_id: '67',
      },
      {
        updated_at: 1612595824484,
        trace_id: '48',
        plan_id: 64,
        created_id: 53,
        relative_path: 'Excepteur id consequat',
        updated_id: 2,
        subject_id: 59,
        created_at: 1232252679242,
        name: '果位离',
        remark: 'culpa voluptate quis',
        id: 89,
      },
    ],
    trace_id: '4',
    remark: 'id voluptate in qui deserunt',
    updated_at: 855363224218,
    created_treatment_at: 74,
    target_count: 70,
    created_user_name: '曹秀英',
    plan_target_model_list: [
      {
        hemi: 'lh',
        name: '响导许温',
        surf_ras: {
          x: 86,
          y: 65,
          z: 43,
        },
        vertex_index: 31,
        vol_ras: {
          x: 100,
          y: 58,
          z: 98,
        },
        code: '91',
        updated_id: 68,
        remark: 'minim nisi Lorem fugiat',
        updated_at: 1332597402551,
        id: 40,
        type: 'elit ipsum',
        normal_line: {
          ver: 15,
          x: 86,
          y: 94,
          z: 65,
        },
        created_id: 22,
        score_index: 1,
        score: 94,
        source: '2',
        horizontal: -120,
        created_at: 1002936290716,
        stimulus: {
          pulse_total: 81,
          relative_strength: 48,
          strand_pulse_count: 56,
          target_id: 48,
          treatment_time: 79152454674,
          type: '5',
          source: 51,
          created_id: 91,
          subject_id: 9,
          plan_id: 67,
          plexus_count: 20,
          created_at: 248003452837,
          plexus_inter_frequency: 20,
          remark: 'laboris',
          inner_strand_pulse_count: 31,
          id: 81,
          plexus_inner_pulse_count: 61,
          updated_at: 74514626121,
          plexus_inner_frequency: 43,
          updated_id: 36,
          intermission_time: 224965568356,
          strand_pulse_frequency: 86,
          trace_id: '48',
        },
        plan_id: 73,
        trace_id: '5',
        clear_normal_line: false,
        subject_id: 80,
        has_mep: true,
      },
      {
        hemi: 'lh',
        name: '数酸验及划',
        surf_ras: {
          x: 77,
          y: 4,
          z: 52,
        },
        vertex_index: 33,
        vol_ras: {
          x: 31,
          y: 92,
          z: 69,
        },
        created_at: 1130786025728,
        score_index: 80,
        created_id: 85,
        updated_at: 849893632900,
        score: 85,
        has_mep: true,
        id: 64,
        remark: 'cupidatat sed officia',
        type: 'laboris sed in fugiat',
        horizontal: -77,
        code: '55',
        trace_id: '61',
        normal_line: {
          ver: 42,
          x: 59,
          y: 66,
          z: 57,
        },
        source: '1',
        updated_id: 3,
        clear_normal_line: false,
        stimulus: {
          pulse_total: 91,
          relative_strength: 25,
          strand_pulse_count: 53,
          target_id: 43,
          treatment_time: 265398302090,
          type: '5',
          updated_id: 7,
          intermission_time: 870770773438,
          plexus_count: 75,
          created_at: 1422730993316,
          source: 43,
          trace_id: '77',
          remark: 'officia laboris nostrud non',
          plexus_inter_frequency: 29,
          id: 25,
          plan_id: 76,
          strand_pulse_frequency: 43,
          updated_at: 1126313566704,
          plexus_inner_frequency: 83,
          created_id: 50,
          subject_id: 98,
          inner_strand_pulse_count: 39,
          plexus_inner_pulse_count: 51,
        },
        plan_id: 31,
        subject_id: 12,
      },
    ],
    created_id: 22,
    plan_import_model: {
      file_name: '始今极需',
      plan_name: '委比很比构时',
      subject_id: 10,
      temp_directory: 'consectetur officia dolore',
    },
    updated_id: 27,
    plan_delete_target_id_list: [54],
    treatment_count: 99,
    type: 1,
    created_at: 870975809850,
    has_demo: true,
    status: PlanStatus.Pending,
    subject_model: {
      code: '27',
      id: 58,
      name: '山连解其',
      pinyin_username: '熊伟',
      birth_date: 20010922,
      sex: 56,
      created_at: 723416428038,
      created_user_name: '姜超',
      updated_id: 7,
      phone: '19886835135',
      remark: 'culpa laborum ex pariatur anim',
      updated_at: 250021510234,
      condition_desc: 'dolore ad magna',
      has_demo: true,
      created_id: 66,
      treatment_count: 75,
      motion_threshold: 0,
      plan_count: 60,
    },
    stimulus: {
      pulse_total: 39,
      relative_strength: 48,
      strand_pulse_count: 42,
      target_id: 76,
      treatment_time: 968675612353,
      type: '2',
      updated_at: 1229711812894,
      remark: 'incididunt qui mollit laboris',
      plexus_count: 73,
      inner_strand_pulse_count: 48,
      created_id: 17,
      created_at: 98082221108,
      intermission_time: 1001365546305,
      subject_id: 56,
      plexus_inner_frequency: 62,
      source: 32,
      plexus_inter_frequency: 95,
      plan_id: 46,
      plexus_inner_pulse_count: 94,
      strand_pulse_frequency: 13,
      id: 78,
      trace_id: '11',
      updated_id: 30,
    },
  },
  {
    id: 97,
    name: '象制斗边观几',
    subject_id: 47,
    type: 1,
    remark: 'quis',
    subject_model: {
      code: '82',
      id: 5,
      name: '治厂式',
      pinyin_username: '熊娟',
      sex: 75,
      created_user_name: '朱洋',
      created_id: 58,
      updated_at: 205992469939,
      has_demo: true,
      phone: '18125141179',
      treatment_count: 35,
      condition_desc: 'aliqua Duis consectetur irure officia',
      motion_threshold: 92,
      created_at: 1282915681587,
      updated_id: 86,
      remark: 'ipsum irure amet adipisicing Lorem',
      plan_count: 61,
      birth_date: 19890204,
    },
    treatment_count: 36,
    created_id: 22,
    updated_treatment_at: 525014914419,
    updated_at: 1228246374212,
    plan_delete_target_id_list: [10, 66, 42, 25],
    target_count: 94,
    plan_file_model_list: [
      {
        name: '如关也其南',
        created_id: 21,
        id: 6,
        updated_id: 1,
        updated_at: 1258238218668,
        trace_id: '12',
        plan_id: 47,
        subject_id: 70,
        created_at: 1402875528858,
        relative_path: 'deserunt ut sit',
        remark: 'laborum proident in voluptate',
      },
      {
        remark: 'amet Excepteur id in nulla',
        plan_id: 52,
        updated_at: 354235425419,
        name: '近间期',
        subject_id: 27,
        relative_path: 'do exercitation sit eiusmod',
        id: 55,
        created_id: 74,
        updated_id: 67,
        created_at: 1357033146796,
        trace_id: '7',
      },
    ],
    stimulus: {
      pulse_total: 31,
      relative_strength: 66,
      strand_pulse_count: 59,
      target_id: 22,
      treatment_time: 1476632954296,
      type: '1',
      trace_id: '70',
      plan_id: 72,
      created_id: 41,
      plexus_count: 12,
      plexus_inter_frequency: 21,
      subject_id: 8,
      id: 94,
      created_at: 26601116214,
      strand_pulse_frequency: 36,
      remark: 'cillum qui',
      plexus_inner_pulse_count: 14,
      plexus_inner_frequency: 55,
      intermission_time: 285090737496,
      inner_strand_pulse_count: 6,
      source: 26,
      updated_id: 55,
      updated_at: 1465530414870,
    },
    created_user_name: '侯艳',
    created_treatment_at: 29,
    created_at: 872392639291,
    trace_id: '89',
    plan_import_model: {
      file_name: '六区导么构',
      plan_name: '持发领的',
      subject_id: 50,
      temp_directory: 'minim consequat ullamco',
    },
    has_demo: false,
    plan_target_model_list: [
      {
        hemi: 'rh',
        name: '万建其变人',
        surf_ras: {
          x: 98,
          y: 47,
          z: 7,
        },
        vertex_index: 6,
        vol_ras: {
          x: 3,
          y: 73,
          z: 95,
        },
        code: '79',
        trace_id: '48',
        plan_id: 61,
        score_index: 13,
        score: 64,
        has_mep: false,
        id: 48,
        type: 'aliqua officia',
        created_id: 73,
        updated_at: 1672599666207,
        subject_id: 5,
        updated_id: 9,
        created_at: 1427751980812,
        remark: 'eu elit aliquip do amet',
        source: '1',
        normal_line: {
          ver: 65,
          x: 64,
          y: 51,
          z: 71,
        },
        clear_normal_line: false,
        stimulus: {
          pulse_total: 45,
          relative_strength: 78,
          strand_pulse_count: 91,
          target_id: 58,
          treatment_time: 1398027504497,
          type: '4',
          created_at: 236787895070,
          plexus_count: 63,
          plexus_inner_frequency: 56,
          updated_id: 82,
          intermission_time: 82277285544,
          plan_id: 71,
          inner_strand_pulse_count: 33,
          source: 65,
          created_id: 81,
          plexus_inter_frequency: 3,
          remark: 'aute',
          updated_at: 491429831820,
          strand_pulse_frequency: 96,
          plexus_inner_pulse_count: 52,
          trace_id: '30',
          subject_id: 26,
          id: 77,
        },
        horizontal: 130,
      },
      {
        hemi: 'rh',
        name: '到上合收选切成',
        surf_ras: {
          x: 96,
          y: 86,
          z: 21,
        },
        vertex_index: 5,
        vol_ras: {
          x: 10,
          y: 46,
          z: 36,
        },
        type: 'exercitation',
        stimulus: {
          pulse_total: 78,
          relative_strength: 20,
          strand_pulse_count: 89,
          target_id: 24,
          treatment_time: 765623904278,
          type: '2',
          plexus_inner_frequency: 11,
          intermission_time: 497244777569,
          source: 48,
          created_id: 88,
          trace_id: '42',
          inner_strand_pulse_count: 64,
          updated_id: 10,
          plan_id: 55,
          updated_at: 480231200949,
          subject_id: 52,
          created_at: 861484708499,
          plexus_inner_pulse_count: 51,
          plexus_inter_frequency: 5,
          id: 62,
          remark: 'mollit officia nostrud',
          plexus_count: 70,
          strand_pulse_frequency: 95,
        },
        horizontal: 60,
        has_mep: true,
        remark: 'aute qui',
        plan_id: 19,
        updated_at: 581844165079,
        created_id: 18,
        score_index: 87,
        clear_normal_line: true,
        source: '2',
        trace_id: '15',
        subject_id: 37,
        normal_line: {
          ver: 36,
          x: 77,
          y: 16,
          z: 80,
        },
        created_at: 239039214451,
        score: 69,
        updated_id: 80,
        id: 38,
        code: '60',
      },
      {
        hemi: 'rh',
        name: '情南四置六',
        surf_ras: {
          x: 32,
          y: 82,
          z: 80,
        },
        vertex_index: 38,
        vol_ras: {
          x: 19,
          y: 21,
          z: 13,
        },
        score: 8,
        has_mep: false,
        horizontal: -48,
        id: 77,
        type: 'deserunt ea ut proident pariatur',
        score_index: 20,
        code: '80',
        updated_at: 1491079680228,
        stimulus: {
          pulse_total: 26,
          relative_strength: 34,
          strand_pulse_count: 88,
          target_id: 90,
          treatment_time: 315589784402,
          type: '5',
          updated_at: 832987818608,
          created_at: 594285928532,
          plexus_inner_pulse_count: 14,
          plexus_count: 10,
          remark: 'voluptate',
          source: 52,
          intermission_time: 1056182742904,
          inner_strand_pulse_count: 33,
          plan_id: 34,
          plexus_inter_frequency: 42,
          updated_id: 38,
          subject_id: 86,
          created_id: 22,
          id: 81,
          strand_pulse_frequency: 97,
          trace_id: '10',
          plexus_inner_frequency: 75,
        },
        updated_id: 95,
        created_id: 98,
        clear_normal_line: false,
        trace_id: '50',
        remark: 'cillum consequat irure fugiat minim',
        normal_line: {
          ver: 4,
          x: 78,
          y: 2,
          z: 46,
        },
        created_at: 91790103591,
        plan_id: 2,
        source: '2',
        subject_id: 12,
      },
      {
        hemi: 'lh',
        name: '三高很决器阶',
        surf_ras: {
          x: 50,
          y: 20,
          z: 55,
        },
        vertex_index: 40,
        vol_ras: {
          x: 84,
          y: 33,
          z: 17,
        },
        subject_id: 78,
        updated_at: 52951936062,
        plan_id: 48,
        created_id: 10,
        has_mep: false,
        type: 'sint reprehenderit deserunt',
        source: '2',
        created_at: 605040978393,
        updated_id: 81,
        code: '9',
        remark: 'qui elit mollit',
        stimulus: {
          pulse_total: 24,
          relative_strength: 61,
          strand_pulse_count: 82,
          target_id: 88,
          treatment_time: 652955029964,
          type: '2',
          plan_id: 76,
          updated_id: 89,
          trace_id: '90',
          strand_pulse_frequency: 76,
          updated_at: 1299667614243,
          source: 16,
          intermission_time: 1332471868348,
          inner_strand_pulse_count: 95,
          created_id: 18,
          created_at: 420375267575,
          plexus_count: 42,
          plexus_inner_pulse_count: 64,
          id: 63,
          plexus_inter_frequency: 8,
          subject_id: 4,
          plexus_inner_frequency: 45,
          remark: 'nulla sint veniam nostrud',
        },
        id: 71,
        clear_normal_line: false,
        score: 73,
        normal_line: {
          ver: 75,
          x: 86,
          y: 75,
          z: 25,
        },
        trace_id: '69',
        score_index: 18,
        horizontal: -116,
      },
      {
        hemi: 'rh',
        name: '八式办因',
        surf_ras: {
          x: 80,
          y: 22,
          z: 56,
        },
        vertex_index: 96,
        vol_ras: {
          x: 77,
          y: 57,
          z: 55,
        },
        subject_id: 60,
        normal_line: {
          ver: 61,
          x: 26,
          y: 2,
          z: 18,
        },
        has_mep: true,
        type: 'pariatur id laborum nostrud minim',
        source: '0',
        created_id: 46,
        score: 30,
        plan_id: 37,
        horizontal: -132,
        remark: 'magna laboris cillum occaecat consectetur',
        updated_id: 48,
        clear_normal_line: false,
        code: '71',
        created_at: 185573091180,
        updated_at: 787607844012,
        id: 4,
        score_index: 82,
        trace_id: '85',
        stimulus: {
          pulse_total: 60,
          relative_strength: 32,
          strand_pulse_count: 32,
          target_id: 99,
          treatment_time: 203339568538,
          type: '2',
          id: 98,
          subject_id: 69,
          plan_id: 13,
          plexus_inter_frequency: 91,
          source: 80,
          plexus_count: 54,
          updated_id: 7,
          inner_strand_pulse_count: 43,
          remark: 'incididunt',
          updated_at: 1178449181593,
          created_id: 98,
          plexus_inner_frequency: 40,
          strand_pulse_frequency: 89,
          created_at: 1545624633163,
          intermission_time: 220897923983,
          plexus_inner_pulse_count: 18,
          trace_id: '2',
        },
      },
    ],
    status: PlanStatus.Normal,
    updated_id: 50,
  },
  {
    id: 77,
    name: '先须路白看导',
    subject_id: 9,
    created_treatment_at: 21,
    treatment_count: 17,
    type: 2,
    plan_delete_target_id_list: [53, 14, 17, 17],
    updated_treatment_at: 329598465486,
    updated_at: 1120907537890,
    has_demo: true,
    plan_file_model_list: [
      {
        trace_id: '21',
        relative_path: 'in eu',
        plan_id: 74,
        id: 87,
        created_at: 1163990999335,
        updated_id: 57,
        remark: 'ut incididunt elit sint',
        updated_at: 391806255574,
        created_id: 2,
        name: '再与点计前见',
        subject_id: 72,
      },
      {
        created_id: 73,
        updated_id: 4,
        trace_id: '62',
        updated_at: 1538228398277,
        name: '然人儿东真路',
        plan_id: 49,
        remark: 'sunt laboris sint aliqua',
        subject_id: 81,
        id: 26,
        relative_path: 'incididunt Excepteur dolor cupidatat dolore',
        created_at: 1389576949886,
      },
      {
        remark: 'commodo est ex nostrud',
        id: 27,
        trace_id: '3',
        plan_id: 25,
        created_at: 716937930800,
        updated_at: 901737117689,
        subject_id: 2,
        created_id: 34,
        name: '想后人型次',
        relative_path: 'eiusmod deserunt magna',
        updated_id: 93,
      },
      {
        remark: 'id',
        trace_id: '87',
        subject_id: 27,
        plan_id: 51,
        created_at: 1248375047856,
        relative_path: 'voluptate',
        created_id: 70,
        name: '热那数代劳',
        id: 60,
        updated_at: 799774114813,
        updated_id: 3,
      },
      {
        plan_id: 7,
        subject_id: 92,
        created_at: 286709662170,
        remark: 'do occaecat',
        updated_id: 76,
        name: '被里线因始',
        relative_path: 'tempor irure velit anim',
        updated_at: 735535712455,
        trace_id: '47',
        created_id: 35,
        id: 5,
      },
    ],
    remark: 'eiusmod',
    updated_id: 15,
    target_count: 56,
    stimulus: {
      pulse_total: 30,
      relative_strength: 31,
      strand_pulse_count: 21,
      target_id: 80,
      treatment_time: 1425204768647,
      type: '1',
      subject_id: 70,
      intermission_time: 1313292568419,
      id: 67,
      plexus_inter_frequency: 97,
      updated_id: 97,
      plan_id: 48,
      updated_at: 889671053306,
      plexus_inner_frequency: 2,
      plexus_count: 32,
      plexus_inner_pulse_count: 54,
      source: 66,
      created_id: 3,
      inner_strand_pulse_count: 99,
      strand_pulse_frequency: 76,
      trace_id: '22',
      remark: 'incididunt dolore ullamco',
      created_at: 1231707297407,
    },
    trace_id: '66',
    created_at: 1237344941022,
    created_user_name: '黎洋',
    created_id: 13,
    plan_import_model: {
      file_name: '界才证入省',
      plan_name: '院研气',
      subject_id: 15,
      temp_directory: 'ex nisi velit cillum',
    },
    subject_model: {
      code: '69',
      id: 62,
      name: '证石分思',
      pinyin_username: '史平',
      has_demo: false,
      created_user_name: '姚霞',
      plan_count: 9,
      remark: 'dolore exercitation do',
      sex: 2,
      created_at: 569625501264,
      updated_id: 49,
      updated_at: 1431504305421,
      treatment_count: 69,
      created_id: 97,
      phone: '13479084056',
      condition_desc: 'enim',
      motion_threshold: 0,
      birth_date: 20061103,
    },
    status: PlanStatus.Normal,
    plan_target_model_list: [
      {
        hemi: 'lh',
        name: '标听局组团',
        surf_ras: {
          x: 64,
          y: 10,
          z: 5,
        },
        vertex_index: 13,
        vol_ras: {
          x: 22,
          y: 87,
          z: 100,
        },
        normal_line: {
          ver: 11,
          x: 73,
          y: 29,
          z: 58,
        },
        code: '71',
        updated_at: 1047365064803,
        plan_id: 78,
        score: 14,
        clear_normal_line: false,
        created_id: 88,
        horizontal: -82,
        remark: 'Ut Duis ullamco quis',
        created_at: 1095764474241,
        stimulus: {
          pulse_total: 74,
          relative_strength: 87,
          strand_pulse_count: 8,
          target_id: 77,
          treatment_time: 1247882983291,
          type: '2',
          updated_id: 61,
          intermission_time: 1253269545643,
          subject_id: 94,
          created_id: 18,
          remark: 'aliquip consectetur elit in labore',
          inner_strand_pulse_count: 97,
          updated_at: 1278324192121,
          strand_pulse_frequency: 5,
          id: 35,
          source: 40,
          created_at: 1400480023074,
          plexus_count: 70,
          plexus_inner_frequency: 46,
          plexus_inner_pulse_count: 15,
          plexus_inter_frequency: 78,
          trace_id: '21',
          plan_id: 55,
        },
        id: 72,
        updated_id: 16,
        source: '0',
        has_mep: true,
        trace_id: '80',
        score_index: 50,
        type: 'dolore nulla',
        subject_id: 45,
      },
      {
        hemi: 'rh',
        name: '资天府',
        surf_ras: {
          x: 67,
          y: 44,
          z: 74,
        },
        vertex_index: 39,
        vol_ras: {
          x: 63,
          y: 44,
          z: 56,
        },
        updated_id: 80,
        type: 'anim fugiat elit laborum',
        has_mep: true,
        remark: 'nisi veniam',
        source: '2',
        code: '84',
        updated_at: 1242204589195,
        score_index: 58,
        created_at: 545302447181,
        trace_id: '26',
        id: 79,
        created_id: 59,
        clear_normal_line: true,
        subject_id: 85,
        horizontal: 140,
        score: 17,
        stimulus: {
          pulse_total: 80,
          relative_strength: 29,
          strand_pulse_count: 36,
          target_id: 43,
          treatment_time: 367461124249,
          type: '1',
          plexus_count: 28,
          trace_id: '25',
          remark: 'ipsum',
          id: 52,
          plexus_inter_frequency: 89,
          plexus_inner_pulse_count: 54,
          source: 50,
          plexus_inner_frequency: 68,
          updated_at: 931617254273,
          subject_id: 54,
          created_id: 83,
          plan_id: 16,
          created_at: 1552261226509,
          intermission_time: 1450234223967,
          inner_strand_pulse_count: 60,
          updated_id: 43,
          strand_pulse_frequency: 83,
        },
        plan_id: 76,
        normal_line: {
          ver: 37,
          x: 23,
          y: 37,
          z: 27,
        },
      },
    ],
  },
];
const Table = (props: Props) => {
  //  原有的数据源，用于筛选使用
  const [filterData, setFilterData] = useState(mockData);
  useAsyncEffect(async () => {
    // mock request
    setTimeout(() => {
      setFilterData(mockData);
    }, 2000);
  }, []);
  // const [pagination,setPagination] = useRecoilState(usePageParams);
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    total: 0,
    current: 1,
    pageSize: 15,
  });
  const navigate = useNavigate();
  const intl = useIntl();
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  const columns = getColumns(
    false,
    false,
    navigate,
    true,
    false,
    false,
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    () => {},
    intl
  );
  const handlePaginationChange = (page: TablePaginationConfig) => {
    setPagination({
      ...page,
    });
  };

  return (
    <div>
      <NgTable<PlanModel>
        data={filterData as unknown as PlanModel[]}
        columns={columns}
        // ! 如果传了 rowClassName，所有 行样式 都需要从调用NgTable处控制
        // ! 注意：传入rowClassName的同时，如果columns中的某一列包含fixed属性需要额外在外部控制fixed样式
        // rowClassName={()=>'row-item'}
        onChange={handlePaginationChange}
        pagination={{
          ...pagination,
          showQuickJumper: true,
          pageSizeOptions: ['5', '10', '20', '50', '80'],
          showSizeChanger: true, // 隐藏每页页数
        }}
      />
      <TableCode />
    </div>
  );
};
export default Table;
