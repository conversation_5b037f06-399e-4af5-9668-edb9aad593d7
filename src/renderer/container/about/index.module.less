@import '@/renderer/static/style/baseColor.module.less';
.container {
  padding: 20px;
  .header {
    display: flex;
    justify-content: space-between;
  }
  .outside {
    height: calc(90vh - 86px);
    display: flex;
    align-items: center;
  }
  .infoBox {
    font-size: 16px;
    color: @colorA12;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    & > p {
      margin: 0 0 20px;
    }
    & > p:first-child {
      width: 260px;
      text-align: left;
    }
    .product {
      width: 260px;
      text-align: left;
      margin-top: 50px;
    }
  }
}

.desc {
  display: flex;
  flex-direction: column;
  font-size: 16px;
  width: 260px;
  & p {
    margin: 0;
  }
  & > p:first-child {
    color: @colorA9;
  }
  & > p:last-child {
    color: @colorA12;
    margin-bottom: 20px;
  }
}

.header_side {
  width: 500px;
}

.header_icons {
  display: flex;
  flex-direction: row-reverse;
}
