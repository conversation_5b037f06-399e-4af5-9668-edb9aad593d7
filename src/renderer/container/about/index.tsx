import React, { useEffect, useState } from 'react';
import styles from './index.module.less';
import { NgBreadcrumb } from '@/renderer/uiComponent/NgBreadCrumb';
import CameraAndCoil from '@/renderer/component/cameraAndCoil';
import ToolBoxAndPower from '@/renderer/component/toolBoxAndPower';
import { useIntl } from 'react-intl';
import classNames from 'classnames';
type Props = {};
const Description = (props: { title: string; msg: string }) => {
  return (
    <div className={styles.desc}>
      <p>{props.title}：</p>
      <p>{props.msg}</p>
    </div>
  );
};

const About = (props: Props) => {
  const intl = useIntl();
  const [productInfo, setProductInfo] = useState<any>({});

  const getProductInfo = async () => {
    const info = await window.systemAPI.getProductInfo();
    setProductInfo(info);
  };

  useEffect(() => {
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    getProductInfo();
  }, []);

  const productMap = {
    [intl.formatMessage({ id: '产品名称' })]: intl.formatMessage({ id: '磁刺激仪' }),
    [intl.formatMessage({ id: '产品型号' })]: `M${productInfo.name || ''}`,
    [intl.formatMessage({ id: '软件名称' })]: intl.formatMessage({ id: '磁刺激仪管控软件' }),
    [intl.formatMessage({ id: '软件型号' })]: 'M200-SW',
    [intl.formatMessage({ id: '完整版本' })]: productInfo.version || '--',
  };

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.header_side}>
          <NgBreadcrumb isGray={false} />
        </div>
        <CameraAndCoil />
        <div className={classNames(styles.header_side, styles.header_icons)}>
          <ToolBoxAndPower />
        </div>
      </div>
      <div className={styles.outside}>
        <div className={styles.infoBox}>
          {Object.keys(productMap).map(key => {
            return <Description key={key} title={key} msg={productMap[key]} />;
          })}
        </div>
      </div>
    </div>
  );
};
export default About;
