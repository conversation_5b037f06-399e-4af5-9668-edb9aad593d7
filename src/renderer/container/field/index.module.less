@import '@/renderer/static/style/baseColor.module.less';

.container {
  padding: 20px;
  box-sizing: border-box;
  height: 100vh;
  .clearPadding {
    padding: 0 !important;
  }

  .tableEmpty {
    height: calc(100vh - 160px);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: @colorA11;

    & p {
      line-height: 0;
      margin-top: 18px;
    }
  }

  :global {
    .ant-table-column-title {
      margin-right: 14px;
      width: fit-content;
    }
    .ant-table-filter-column:not(:has(.ant-table-column-sorter)) {
      .ant-table-column-title {
        margin-right: 14px !important;
      }
    }

    .ant-table-filter-column:has(.ant-table-column-sorter) {
      .ant-table-column-title {
        margin-right: 0;
      }

      & .ant-table-column-sorter {
        margin: 0 12px 0 18px;
      }
    }
    .ant-table-cell-ellipsis > div:first-child {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .ant-input-prefix {
      margin-inline-end: 12px;
    }

    & .ant-input-affix-wrapper {
      height: 40px;
      margin: 20px 0;
    }

    .ant-input-suffix {
      & span {
        width: 16px;
        height: 16px;
        background: url('@/renderer/static/svg/allowClear.svg') no-repeat;

        & svg {
          display: none;
        }
      }
    }

    .ant-table-pagination {
      position: fixed;
      bottom: 5px;
      right: 20px;
      z-index: 99;
    }

    .ant-pagination-options-quick-jumper {
      margin-left: 12px !important;
    }
  }
}
