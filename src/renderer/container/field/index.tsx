import React, { useRef, useState } from 'react';
import { NgInput } from '@/renderer/uiComponent/NgInput';
import { NgBreadcrumb } from '@/renderer/uiComponent/NgBreadCrumb';
import styles from './index.module.less';
// @ts-ignore
import homeStyles from '../home/<USER>';
import { NgIcon } from '@/renderer/uiComponent/NgIcon';
import { Search } from '@/renderer/uiComponent/SvgGather';
import { useIntl } from 'react-intl';
import { getColumns } from '@/renderer/container/home/<USER>';
import { useNavigate } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import { IPlanListParams, PlanModel } from '@/common/types';
import { useRecoilState, useRecoilValue } from 'recoil';
import { useLicenseAtom } from '@/renderer/recoil/license';
import NgTable from '@/renderer/uiComponent/NgTable';
import { getM200ApiInstance } from '@/common/api/ngApiAgent';
import { useAsyncEffect } from 'ahooks';
import { debounce, omit } from 'lodash';
import classNames from 'classnames';
import { useFilterParams } from '@/renderer/recoil/tableFilterParams';
import { formatFilter, setTableSort } from '@/renderer/container/home';
import { usePageParams } from '@/renderer/recoil/pageParams';
import { sendRenderLog } from '../../utils/renderLogger';

type Props = {};
const Field = (props: Props) => {
  const intl = useIntl();
  const navigate = useNavigate();
  const [_forceUpdate, setForceUpdate] = useState(false);
  const [tableFilter, setTableFilter] = useRecoilState(useFilterParams);
  const [pageParams, setPageParams] = useRecoilState(usePageParams);
  const { fieldPage } = pageParams;
  const license = useRecoilValue(useLicenseAtom);
  const m200Api = getM200ApiInstance();
  const timer: any = useRef();
  const [filterData, setFilterData] = useState<PlanModel[]>([]);
  const [searchValue, setSearchValue] = useState('');
  const originTotal = useRef({
    total: 0,
    current: 1,
    pageSize: 15,
  });
  // const [pagination,setPagination] = useRecoilState(usePageParams);
  const [pagination, setPagination] = useState({
    total: 0,
    current: 1,
    pageSize: 15,
  });
  React.useEffect(() => {
    // data修改手动刷新组件，确保在getColumns中可以正确的获取dom，超长展示tooltips
    setForceUpdate(v => !v);
  }, [filterData]);
  const handleRestoreField = async (plan_id: number) => {
    try {
      await m200Api.archiveFiled({
        plan_id,
        has_archive: false,
      });
      await fetchList({
        ...formatFilter(tableFilter.fieldFilter),
        keyword: searchValue,
        page_num: pagination.current,
        page_size: fieldPage?.pageSize,
      });
    } catch (e) {
      sendRenderLog.error(`归档数据报错： ${e}`);
    }
  };
  const columns = getColumns(
    true,
    true,
    navigate,
    false,
    false,
    license.hasLicenseError as boolean,
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    () => {},
    intl,
    true,
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    async () => {},
    handleRestoreField,
    {
      setTableFilter,
      tableFilter,
      type: 'fieldFilter',
    }
  );
  useAsyncEffect(async () => {
    clearTimeout(timer.current);
    timer.current = setTimeout(async () => {
      let result = formatFilter(tableFilter.fieldFilter);
      await fetchList({
        ...result,
        keyword: searchValue,
        page_num: 1,
        page_size: fieldPage?.pageSize,
      });
    }, 100);
  }, [tableFilter.fieldFilter]);

  const fetchList = async (params?: IPlanListParams) => {
    let res = await m200Api.getPlanList({
      ...params,
      has_archive: true,
    });
    const pageInfo = {
      current: res.current,
      pageSize: res.size,
      total: res.total,
    };
    if (res.records.length === 0 && pagination?.current > 1) {
      pageInfo.current -= 1;
      res = await m200Api.getPlanList({
        ...params,
        page_num: pageInfo.current,
        has_archive: true,
      });
    }
    setFilterData(res?.records);
    originTotal.current = { ...pageInfo };
    setPagination({
      ...pageInfo,
    });
  };

  const handlePaginationChange = async (current: number, pageSize: number) => {
    await fetchList({
      ...omit(pagination, 'pageSize', 'current'),
      ...formatFilter(tableFilter.fieldFilter),
      keyword: searchValue,
      page_num: current,
      page_size: pageSize,
    });
    setPageParams({
      fieldPage: {
        ...fieldPage,
        pageSize,
      },
      homePage: { ...pageParams.homePage },
    });
  };
  const handleSearch = debounce(async (value: string) => {
    setSearchValue(value);
    await fetchList({
      ...formatFilter(tableFilter.fieldFilter),
      keyword: value,
      page_num: 1,
      page_size: fieldPage?.pageSize,
    });
  }, 500);

  return (
    <div className={classNames(styles.container, homeStyles.homeContainer)}>
      <NgBreadcrumb isGray={false} />
      <NgInput
        placeholder={intl.formatMessage({ id: '请输入搜索内容' })}
        prefix={<NgIcon iconSvg={Search} fontSize={18} />}
        onChange={async e => handleSearch(e.target.value)}
        allowClear
      />

      <div
        className={classNames(styles.clearPadding, homeStyles.tableContainer, {
          [homeStyles.emptyTable]: filterData.length <= 0,
        })}
      >
        <ConfigProvider
          renderEmpty={() => {
            return <></>;
          }}
        >
          <NgTable<PlanModel>
            onChange={(_, _filters, sorter: any, extra) => {
              // 屏蔽翻页，避免错误调用全量排序
              if (pagination.current !== _.current || pagination.pageSize !== _.pageSize) {
                return;
              }

              setTableSort(sorter, tableFilter, setTableFilter, 'fieldFilter');
            }}
            showSorterTooltip={{
              open: false,
            }}
            rowKey={record => record.id}
            data={filterData}
            columns={columns}
            scroll={{ x: 'max-content', y: 830 }}
            pagination={{
              ...pagination,
              pageSize: fieldPage?.pageSize,
              showTotal: (total, range) => {
                return <>共{total}条</>;
              },
              defaultPageSize: 15,
              onChange: handlePaginationChange,
              showQuickJumper: true,
              pageSizeOptions: ['15', '20', '50', '100'],
              showSizeChanger: true, // 隐藏每页页数
            }}
          />
        </ConfigProvider>
      </div>
    </div>
  );
};
export default Field;
