import { StimulateModel } from '@/renderer/container/noPatientTms';
import { message } from 'antd';

export const sendParamsRTMSToTms = async (params: StimulateModel, tid: string) => {
  const param = {
    action: 'set_treatment_plan',
    level: params.active_strength, // 相对强度
    frequency: Math.round((params.strand_pulse_frequency as number) * 100), // 串脉冲频率
    intensity: Math.round((params.strand_pulse_frequency as number) * 100), // 串脉冲频率
    count: 1,
    bunch: params.inner_strand_pulse_count, // 串内脉冲数
    series: params.strand_pulse_count, // 串数
    pause: params.strand_pulse_count === 1 ? 1 : params.intermission_time, // 刺激间隔  间歇时间
    sum: params.pulse_total, // 总脉冲数
    time: params.treatment_time, // 总刺激时间
    tid: tid, // uuid
  };
  // this.setState({ isTreating: true, pulse_total, treat_time: +T5, treatParam: param });
  const data = await window.tmsAPI.set_treatment_plan(param);
  if (data.code === 0) {
    if (data.data.result === 0) {
      return 'success';
    }
    await message.warning('系统错误，请重试');

    return 'error';
  } else {
    await message.warning(data.message || 'tms通讯出错');

    return 'error';
  }
};

export const sendParamsTBSToTms = async (params: StimulateModel, tid: string) => {
  const param = {
    action: 'set_treatment_plan',
    level: params.active_strength, // 相对强度
    frequency: Math.round((params.plexus_inner_frequency as number) * 100), // 丛内频率
    intensity: Math.round((params.plexus_inter_frequency as number) * 100), // 丛间频率
    count: params.plexus_inner_pulse_count, // 丛内脉冲数
    bunch: params.plexus_count, // 刺激丛数
    series: params.strand_pulse_count, // 刺激串数
    pause: params.strand_pulse_count === 1 ? 1 : params.intermission_time, // 刺激间隔  间歇时间
    sum: params.pulse_total, // 总脉冲数
    time: params.treatment_time, // 总刺激时间
    tid: tid, // uuid
  };
  const data = await window.tmsAPI.set_treatment_plan(param);
  if (data.code === 0) {
    if (data.data.result === 0) {
      return 'success';
    } else {
      await message.warning('系统异常，请重试');

      return 'error';
    }
  } else {
    await message.warning(data.message || 'tms通讯出错');

    return 'error';
  }
};

export const sendActiveStrengthToTms = async (activeStrength: number, relativeStrength: number) => {
  await window.tmsAPI.set_treatment_level(activeStrength, relativeStrength);
};
