@import '../../../static/style/baseColor.module.less';
@import '../../../static/style/base.module.less';
.stimulatePage {
  display: flex;
  width: 640px;
  height: 100%;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .topLayout {
    width: 100%;
    height: 420px;
    display: flex;
    justify-content: space-between;
    .leftCard {
      width: 300px;
      height: 420px;
      background: inherit;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      :global {
        .ant-progress-text {
          font-size: 30px;
        }
      }

      .pauseTitle {
        display: block;
        width: 100%;
        height: 23px;
        margin: 12px 0 17px 0;
        text-align: center;
        font-weight: 350;
        color: @colorB1;
        .innerTitle {
          display: flex;
          justify-content: center;
          align-items: center;
          height: 23px;
          color: @colorB1;
          .icon_layout {
            width: 24px;
            height: 40px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
          }

          .ml12 {
            margin-left: 12px;
          }
        }
      }
      .pulseTotal {
        margin-top: 30px;
        height: 32px;
        line-height: 32px;
      }

      .time {
        height: 32px;
        line-height: 32px;
      }
    }
    .rightCard {
      width: 300px;
      height: 420px;
      background: @colorA3;
      padding: 14px 16px;
      border-radius: 6px;
      color: @colorA9;
      .strength {
        font-size: @font-size-lg;
        margin-bottom: 30px;
        height: 44px;
        display: flex;
        align-items: flex-end;
        .value {
          font-size: 20px;
          display: inline-flex;
          align-items: flex-end;
          .num {
            margin-bottom: -2px;
          }
        }
      }

      .strength_pause {
        font-size: @font-size-lg;
        margin-bottom: 30px;
        .label {
          color: @colorA12;
        }
        .value {
          font-size: 30px;
          color: @colorC4;
          .num {
            margin-bottom: -5px;
          }
        }
        .range {
          font-size: @font-size-lg;
          color: @colorA9;
        }
      }
    }
  }

  .bottomLayout {
    margin-top: 30px;
    width: 640px;
    height: 60px;
    display: flex;
    flex-direction: row-reverse;
  }
}
.countdownContent {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  .anticon {
    width: 50px;
    height: 50px;
    display: inline-flex;
    justify-content: center;
  }
  .treat_complete_text {
    font-size: 16px;
    color: @colorA11;
  }
  .auto_hide_text {
    height: 24px;
    line-height: 24px;
    margin-top: 4px;
    font-size: 12px;
    color: @colorA9;
  }
  .back_button {
    margin-top: 20px;
    width: 88px;
    height: 32px;
    .ant-btn {
      width: 88px;
    }
  }
}
