import React, { useEffect, useState } from 'react';
import styles from './editPage.module.less';
import { NgForm } from '@/renderer/uiComponent/NgForm';
import { NgIcon } from '@/renderer/uiComponent/NgIcon';
import { ImportTemplate as ImportIcon, StimulateImportDisabled } from '@/renderer/uiComponent/SvgGather';
import { TBSChart } from '@/renderer/component/tbsChart';
import { EditStimulateTemplate, TbsFieldType } from '@/renderer/component/template';
import { ImportTemplate } from '@/renderer/component/importTemplate';
import { Form } from 'antd';
import { PlanStimulusModel } from '@/common/types';
import { NgInputNumber } from '@/renderer/uiComponent/NgInputNumber';
import { valueType } from 'antd/lib/statistic/utils';
import { calRules, getFields } from '@/renderer/component/template/calRules';
import { changeISComplete } from '@/renderer/container/stimulateTemplate/component/editTemplateCard';
import { StimulateModel } from '@/renderer/container/noPatientTms';
import NgDarkButton from '@/renderer/uiComponent/NgDarkButton';
import { useRecoilState } from 'recoil';
import { Rule } from 'antd/es/form';
import { validatorCommonRules, validatorCTBSRules } from '@/renderer/component/template/validatorFun';
import { useIntl } from 'react-intl';
import { calTbsChartData } from '@/renderer/component/template/calTemplate';
import { faultAtom, getFaultWithoutType } from '../../../recoil/fault';
import { FaultEnum } from '../../../../common/systemFault/type';

interface Props {
  stimulate: StimulateModel;
  iconDisabled: boolean;
  onOk(stimulate: StimulateModel): Promise<void>;
  isTechSupport?: boolean;
}

export const EditParamsPage = (props: Props) => {
  const { iconDisabled, stimulate } = props;
  const intl = useIntl();
  const [form] = Form.useForm();
  const [formStrength] = Form.useForm();
  const [importVisible, setImportVisible] = useState(false);
  const [formValues, setFormValues] = useState<any>(stimulate);
  const [errorStrengthFields, setErrorStrengthFields] = useState<string[]>([]);
  const [fault] = useRecoilState(faultAtom);
  const [activeStrength, setActiveStrength] = useState(0);
  const hasError = !!getFaultWithoutType(FaultEnum.imageFault, fault).length;

  useEffect(() => {
    changeStateValues(stimulate);
    setActiveStrength(stimulate.active_strength || 0);
    formStrength.setFieldsValue({ active_strength: stimulate.active_strength });
  }, [JSON.stringify(stimulate)]);
  const handleChangeStrength = (value: valueType | null) => {
    setActiveStrength(Number(value));
  };
  const handleCancelImport = () => {
    setImportVisible(false);
  };
  const handleImport = (params: PlanStimulusModel) => {
    params.relative_strength = activeStrength;
    setFormValues(params);
    setImportVisible(false);
    form.setFieldsValue(params);
    const active_strength = formStrength.getFieldValue('active_strength');
    if (active_strength) {
      formStrength
        .validateFields()
        .then(res => {
          // eslint-disable-next-line no-void
        })
        .catch(error => {
          // console.log(error); // eslint-disable-line no-console
        });
    }
  };

  const changeStateValues = (newParams: any): any => {
    form.setFieldsValue({
      ...newParams,
      intermission_time: newParams.strand_pulse_count === 1 ? undefined : newParams.intermission_time,
    });
    setFormValues({
      ...newParams,
      intermission_time: newParams.strand_pulse_count === 1 ? undefined : newParams.intermission_time,
    });
    const isComplete = changeISComplete(newParams, newParams.type);
    if (isComplete) {
      formStrength
        .validateFields()
        .then(res => {
          // eslint-disable-next-line no-void
        })
        .catch(error => {
          // console.log(error); // eslint-disable-line no-console
        });
    }
  };
  const handleChangeValues = (changeValues: any, values: any): any => {
    let key = Object.getOwnPropertyNames(changeValues)[0];
    let value = changeValues[key];
    if (changeValues.type) {
      // 修改了刺激类型, 重置表单
      let renderFields: TbsFieldType[] = calRules(values, 0, '', props.isTechSupport);
      let template = renderFields.reduce((result: any, item: TbsFieldType) => {
        return { ...result, [item.key]: undefined };
      }, {});
      form.setFieldsValue({ type: value, ...template, relative_strength: activeStrength });
      setFormValues({
        type: value,
        ...template,
        relative_strength: activeStrength,
      });

      return;
    }

    changeStateValues(values);
  };

  const handleChangeStrengthForm = (changeValues: any, values: any): any => {
    handleChangeStrength(changeValues.active_strength);
    formValues.relative_strength = changeValues.active_strength;
    changeStateValues(formValues);
  };
  const setNullOfUndefined = () => {
    const fields = getFields(formValues, 0);
    let newValues = fields.reduce(
      (res, cur) => {
        let temp = {};
        temp[cur.key] = formValues[cur.key] === undefined ? null : formValues[cur.key];

        return Object.assign({}, res, temp);
      },
      { type: formValues.type }
    );
    setFormValues(newValues);
  };
  const validateTemplateForm = async (active_strength: number) => {
    return form
      .validateFields()
      .then(async res => {
        await props.onOk(Object.assign({}, res, { active_strength }));
        // eslint-disable-next-line no-void
        setErrorStrengthFields([]);
      })
      .catch(error => {
        throw error;
      });
  };
  const handleOk = async () => {
    setNullOfUndefined();
    try {
      let strengthFormValues = await formStrength.validateFields();
      await validateTemplateForm(strengthFormValues.active_strength ? strengthFormValues.active_strength : 0);
    } catch (e) {
      // 数据错误
      setErrorStrengthFields(['active_strength']);
      await validateTemplateForm(0);
    }
  };

  const getActiveStrengthRule = () => {
    const rules: Rule[] = [
      {
        validator: validatorNumber(1, 100, 'active_strength'),
      },
    ];

    return rules;
  };
  const checkInvalidate = (pulseTotal: number, treatmentTime: number) => {
    if (pulseTotal === 0 || pulseTotal > 65535) {
      return true;
    }

    return treatmentTime < 3 || treatmentTime > 65535;
  };
  const validatorNumber =
    (minValue: number, maxValue: number, keyParam: string) =>
      async (_: any, value: number): Promise<string | void> => {
        try {
        // 验证普通空 和范围错误
          await validatorCommonRules(value, minValue, maxValue, 0, intl);
          // 如果左侧有超限报错，这里不校验功率报错
          const { pulse_total, treatment_time } = calTbsChartData(formValues);
          if (checkInvalidate(pulse_total, treatment_time)) {
            setErrorStrengthFields([]);

            return Promise.resolve('');
          }
          if (pulse_total && treatment_time && !props.isTechSupport) {
            await validatorCTBSRules(100, formValues, 'relative_strength', intl);
          }
          setErrorStrengthFields([]);
        } catch (e) {
          if (value !== undefined) {
            setErrorStrengthFields([keyParam]);
          }

          return Promise.reject(e);
        }
      };

  return (
    <div className={styles.editPage}>
      <div className={styles.topLayout}>
        <div className={styles.leftCard}>
          <NgForm
            form={form}
            layout="horizontal"
            labelAlign={'left'}
            labelCol={{ span: 8 }}
            wrapperCol={{ span: 16 }}
            className={styles.templateForm}
            onValuesChange={handleChangeValues}
          >
            <div className={styles.row_1}>
              <span>{intl.formatMessage({ id: '参数' })}</span>
              <NgIcon
                disabled={iconDisabled}
                tooltip={{ placement: 'top', title: intl.formatMessage({ id: iconDisabled ? '无可导入脉冲模板' : '导入脉冲模板' }) }}
                iconSvg={iconDisabled ? StimulateImportDisabled : ImportIcon}
                onClick={() => {
                  if (!iconDisabled) setImportVisible(true);
                }}
              />
            </div>
            <div className={styles.row_2}>{<TBSChart template={formValues} motionThreshold={100} />}</div>
            <EditStimulateTemplate motionThreshold={100} formRef={form} stimulate={formValues} />
          </NgForm>
        </div>
        <div className={styles.rightCard}>
          <div className={styles.row_1}>
            <span>{intl.formatMessage({ id: '强度' })}</span>
          </div>
          <div className={styles.row_2}>
            <NgForm
              form={formStrength}
              layout="horizontal"
              labelAlign={'left'}
              labelCol={{ span: 8 }}
              wrapperCol={{ span: 16 }}
              colon={false}
              className={styles.templateForm}
              onValuesChange={handleChangeStrengthForm}
            >
              <Form.Item
                className={'active_strength'}
                name={'active_strength'}
                key={'active_strength'}
                extra={'1-100'}
                label={<span>{intl.formatMessage({ id: '实际强度(%MO) :' })}</span>}
                wrapperCol={{ span: 12 }}
                labelCol={{ span: 12 }}
                rules={getActiveStrengthRule()}
              >
                <NgInputNumber status={errorStrengthFields.includes('active_strength') ? 'error' : ''} step={1} />
              </Form.Item>
            </NgForm>
          </div>
        </div>
      </div>
      <div className={styles.bottomLayout}>
        <NgDarkButton type={'default'} disabled={hasError} style={{ width: 120 }} onClick={handleOk}>
          {intl.formatMessage({ id: '确认' })}
        </NgDarkButton>
      </div>

      <ImportTemplate visible={importVisible} onCancel={handleCancelImport} onOk={handleImport} />
    </div>
  );
};
