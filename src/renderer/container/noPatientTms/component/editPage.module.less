@import '../../../static/style/baseColor.module.less';
@import '../../../static/style/base.module.less';
.editPage {
  display: flex;
  width: 640px;
  height: 100%;
  flex-direction: column;
  justify-content: center;

  .topLayout {
    width: 100%;
    height: 650px;
    display: flex;
    justify-content: space-between;
    .leftCard {
      width: 300px;
      height: 650px;
      background: @colorA3;
      padding: 14px 16px;
      border-radius: 6px;

      .row_1 {
        width: 100%;
        display: flex;
        justify-content: space-between;
        color: @colorA12;
      }

      .templateForm {
        :global {
          .ant-form-item-label label {
            color: @colorA9;
          }

          .ant-form-item:nth-child(4) {
            display: none;
          }

          .ant-form-item-explain-error {
            font-size: 12px;
          }
        }
      }
    }
    .rightCard {
      width: 300px;
      height: 650px;
      background: @colorA3;
      padding: 14px 16px;
      border-radius: 6px;

      .row_1 {
        width: 100%;
        display: flex;
        font-size: @font-size-base;
        justify-content: space-between;
        color: @colorA12;
        margin-bottom: 20px;
      }

      .row_2 {
        width: 100%;
        display: flex;
        font-size: @font-size-base;
        justify-content: space-between;
        color: @colorA9;
        align-items: center;
        .templateForm {
          :global {
            .ant-form-item-label label {
              color: @colorA9;
            }

            .ant-form-item:nth-child(4) {
              display: none;
            }

            .ant-form-item-explain-error {
              font-size: 12px;
            }
          }
        }
        .label {
          width: auto;
        }
        .value {
          width: 120px;
        }
      }
    }
  }

  .bottomLayout {
    width: 640px;
    display: flex;
    flex-direction: row-reverse;
    align-items: center;
    margin-top: 30px;
  }
}
