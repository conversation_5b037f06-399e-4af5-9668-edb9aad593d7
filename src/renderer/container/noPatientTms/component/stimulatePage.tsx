import React, { useEffect, useRef, useState } from 'react';
import { StimulateModel } from '@/renderer/container/noPatientTms';
import styles from './stimulatePage.module.less';
import { TBSChart } from '@/renderer/component/tbsChart';
import NgDarkButton from '@/renderer/uiComponent/NgDarkButton';
import { useAsyncEffect, useMount, useThrottleFn, useUnmount } from 'ahooks';
import { secToTime } from '@/renderer/component/template/calTemplate';
import { SuccessMessage, WarnMessage } from '@/renderer/uiComponent/SvgGather';
import { isPowerOver } from '@/renderer/component/template/ctbsParamsRules';
import { sendActiveStrengthToTms } from '@/renderer/container/noPatientTms/utils/sendParamsToTms';
import { useRecoilState, useSetRecoilState } from 'recoil';
import { tmsCoilSelector } from '@/renderer/recoil/tmsError';
import { message } from 'antd';
import { useIntl } from 'react-intl';
import classnames from 'classnames';
import NgModal from '@/renderer/uiComponent/NgModal';
import Icon from '@ant-design/icons';
import NgButton from '@/renderer/uiComponent/NgButton';
import { ErrorModel } from '@/renderer/component/systemErrorModel/errorModel';
import { TMSScreenState } from '@/common/constant/tms';
import { Countdown, Pluse } from '../../../component/plusecountdown';
import { treatResultMap } from '../../../constant/tmsAndImageSocket';
import { sendTreatLog } from '../../../utils/renderLogger';
import { initNoImageTms } from '../../../utils/crashedAction';
import { isNotTreatingAtom } from '../../../recoil/isNotTreating';
import { notNAVTypeList } from '../../../../common/systemFault/config';
import { FaultStatusEnum } from '../../../../common/systemFault/type';
import TreatCircleProgress from '../../../component/treatCircleProgress';
import { throttleOption } from '../../../utils';

interface Props {
  stimulate: StimulateModel;
  onOver(): void;
  onSetActiveStrength(activeStrength: number): void;
  isTechSupport: boolean;
  tid?: string;
}
let isUnmount = false;
export const StimulatePage = (props: Props) => {
  const intl = useIntl();
  let errorVisible = useRef(false);
  const { tid, stimulate, onOver, onSetActiveStrength } = props;
  const [isPause, setIsPause] = useState(false);
  const [hadPulseTotal, setHadPulseTotal] = useState(0);
  const [residualTime, setResidualTime] = useState(stimulate.treatment_time);
  const hadPulseTotalRef = useRef(0);
  const residualTimeRef = useRef(stimulate.treatment_time);
  const treatStatus = useRef('treat_init');
  const [coilSelector] = useRecoilState(tmsCoilSelector);
  const interval = React.useRef<any>(0);
  const [activeStrength, setActiveStrength] = useState<number>(stimulate.active_strength!);
  const refActiveStrength = useRef<number>(stimulate.active_strength!);
  const lastActiveStrength = useRef<number>(stimulate.active_strength!);
  const setIsNotTreating = useSetRecoilState(isNotTreatingAtom);
  const [isStart, setIsStart] = useState<boolean>(false);
  const [backInfo, setBackInfo] = useState({
    backTime: 3,
    backOpen: false,
  });
  const currentTemperature = useRef<number>(coilSelector.coil_max_temperature ? coilSelector.coil_max_temperature : 0);

  useMount(async () => {
    isUnmount = false;
    errorVisible.current = false;
    setTmsTreatStatus(TMSScreenState.Preparation);
    await listeningTms();
  });

  useUnmount(async () => {
    isUnmount = true;
    if (interval) {
      clearInterval(interval.current);
    }
    window.systemAPI.pushSystemFault({ '0A030005': FaultStatusEnum.normal }, 'stimulate page 卸载清除');
    window.tmsAPI.remove_beat_btn_by_key('StimulatePage');
    initNoImageTms(tid);
  });

  useEffect(() => {
    setIsNotTreating(treatStatus.current === 'treat_init' || treatStatus.current === 'treat_stop');
  }, [treatStatus.current]);

  useAsyncEffect(async () => {
    currentTemperature.current = coilSelector.coil_max_temperature ? coilSelector.coil_max_temperature : 0;
  }, [coilSelector.coil_max_temperature]);

  const checkNeedError = async () => {
    clearInterval(interval.current);
    errorVisible.current = true;
    if (treatStatus.current === 'treat_ing') {
      await treatmentPlanStop();
    }
  };

  /**
   * @description 按了拍子中间 按钮
   * 从暂停到开始，需要验证温升比
   */
  const deviceClickPlay = async (): Promise<any> => {
    switch (treatStatus.current) {
      case 'treat_ing':
        await treatmentPlanPause();
        break;
      case 'treat_pause':
        // 检查温升比,如果温升比超过限制,则提示用户
        if (currentTemperature.current > 36) {
          return message.warning({
            content: '存在超温风险，请降温后再试',
            key: 'over_temp',
          });
        }
        if (errorVisible.current) return;
        lastActiveStrength.current = refActiveStrength.current;
        treatStatus.current = 'treat_ing';
        setTmsTreatStatus(TMSScreenState.PlanTreat);
        setIsPause(false);
        const data = await window.tmsAPI.noImage_treatment_plan_start('PlanResume', tid);
        if (data.code === 0) {
          if (data.data.result !== 0) {
            sendTreatLog.info('noPatient继续刺激出现故障', treatResultMap[data.data.result]);
            await messageWarning();
          }
        } else {
          await message.error(data.message || 'tms通讯出错');
        }
        break;
      case 'treat_init':
        await treatmentPlanStart();
        break;
      default:
        break;
    }
  };

  // 暂停情况下，拍子 上下 按钮切换实际强度
  const deviceClickControl = async (key: string, step: number) => {
    if (treatStatus.current !== 'treat_pause') return;
    switch (key) {
      case 'add':
        let valAdd = refActiveStrength.current + step > getMax() ? getMax() : refActiveStrength.current + step;
        refActiveStrength.current = valAdd;
        // 脉冲刺激没有相对强度的概念，默认填100
        await sendActiveStrengthToTms(valAdd, 100);
        setActiveStrength(valAdd);
        break;
      case 'sub':
        let valSub = refActiveStrength.current - step < getMin() ? getMin() : refActiveStrength.current - step;
        refActiveStrength.current = valSub;
        // 脉冲刺激没有相对强度的概念，默认填100
        await sendActiveStrengthToTms(valSub, 100);
        setActiveStrength(valSub);
        break;
      default:
        break;
    }
  };

  /**
   * 添加拍子节流
   */
  const { run: debounceBeatBtnData } = useThrottleFn((_data: any) => {
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    deviceClickPlay();
  }, throttleOption);

  const listeningTms = async () => {
    await window.tmsAPI.beat_btn_by_key('StimulatePage', async (event: any, data: any) => {
      // 错误弹窗情况下，对这个按钮事件不响应
      if ((backInfo.backOpen || errorVisible.current) && ['play', 'add', 'sub'].includes(data.data.key)) return;
      if (data.data.key === 'play' && !backInfo.backOpen && !errorVisible.current) {
        debounceBeatBtnData(data);
      } else {
        await deviceClickControl(data.data.key, data.data.step);
      }
    });
  };

  const checkIsOver = (tmsData: any, stimulateParam: StimulateModel) => {
    return (
      tmsData.result === 0 && tmsData.state === 0 && (tmsData.time === stimulateParam.treatment_time || tmsData.count === stimulateParam.pulse_total)
    );
  };

  const checkIsStatusError = (tmsData: any, stimulateParam: StimulateModel) => {
    return tmsData.result === 0 && tmsData.count < stimulateParam.pulse_total && tmsData.state === 0;
  };

  const treatmentPlanStart = async () => {
    // NOSONAR
    if (errorVisible.current) return;
    const data = await window.tmsAPI.noImage_treatment_plan_start('PlanStart', tid);
    treatStatus.current = 'treat_ing';
    setTmsTreatStatus(TMSScreenState.PlanTreat);
    if (data.code === 0) {
      if (data?.data.result === 0) {
        setIsStart(true);
        interval.current = setInterval(async () => {
          if (isUnmount) {
            clearInterval(interval.current);

            return;
          }
          const queryData = await window.tmsAPI.query_treatment();
          handleSetHandlePulseTotal(queryData);
          handleSetResidualTime(queryData);
          if (checkIsStatusError(queryData.data, props.stimulate)) {
            treatStatus.current = 'treat_stop';
            clearInterval(interval.current);
            sendTreatLog.info('noPatent刺激出现故障');
            window.tmsAPI.remove_beat_btn_by_key('StimulatePage');
            // eslint-disable-next-line @typescript-eslint/no-floating-promises
            window.systemAPI.pushSystemFault({ '0A030005': FaultStatusEnum.abnormal }, 'stimulate page 上下微机状态不一致');
          }
          // 刺激结束
          if (checkIsOver(queryData.data, props.stimulate)) {
            // 自动结束
            treatStatus.current = 'treat_stop';
            setIsStart(false);
            onSetActiveStrength(refActiveStrength.current);
            await window.tmsAPI.noImage_treatment_plan_start('PlanEnd', tid);
            window.tmsAPI.remove_beat_btn_by_key('StimulatePage');
            clearInterval(interval.current);
            startBackInterval();
          }
        }, 1000);
      } else {
        sendTreatLog.info('noPatent开始刺激出现故障', treatResultMap[data.data.result]);
        await messageWarning();
      }
    } else {
      await message.error(data.message || 'tms通讯出错');
    }
  };
  // 设置刺激脉冲数
  const handleSetHandlePulseTotal = (queryData: any) => {
    if (typeof queryData.data.count !== 'number') return;
    setHadPulseTotal(queryData.data.count);
    hadPulseTotalRef.current = queryData.data.count;
  };
  // 设置刺激剩余时间
  const handleSetResidualTime = (queryData: any) => {
    if (queryData.data.remain_time === undefined) return;
    if (isPause && queryData.data.remain_time !== 0) {
      setResidualTime(queryData.data.remain_time);
      residualTimeRef.current = queryData.data.remain_time;

      return;
    }
    setResidualTime(queryData.data.remain_time !== undefined ? queryData.data.remain_time : props.stimulate.treatment_time - queryData.data.time);
    residualTimeRef.current =
      queryData.data.remain_time !== undefined ? queryData.data.remain_time : props.stimulate.treatment_time - queryData.data.time;
  };

  const messageWarning = async () => {
    return message.warning({
      content: '系统异常，请重试',
      key: 'result',
    });
  };

  const treatmentPlanPause = async () => {
    const data = await window.tmsAPI.noImage_treatment_plan_start('PlanPause', tid);
    if (data.code === 0) {
      if (data.data.result === 0) {
        treatStatus.current = 'treat_pause';
        setIsPause(true);
        setTmsTreatStatus(TMSScreenState.PlanSuspend);
      } else {
        sendTreatLog.info('noPatient暂停刺激出现故障', treatResultMap[data.data.result]);
        await messageWarning();
      }
    } else {
      await message.error(data.message || 'tms通讯出错');
    }
  };

  const treatmentPlanStop = async () => {
    initNoImageTms(tid);
    treatStatus.current = 'treat_stop';
  };
  const setTmsTreatStatus = (status: number) => {
    window.tmsAPI.set_beat_screen(status);
  };

  const handleOver = async () => {
    treatStatus.current = 'treat_stop';
    onSetActiveStrength(refActiveStrength.current);
    await window.tmsAPI.noImage_treatment_plan_start('PlanEnd', tid);
    clearInterval(interval.current);
    setIsStart(false);
    onOver();
  };

  const handleErrorModelClose = () => {
    errorVisible.current = false;
    onOver();
  };
  const getMax = () => {
    let value = stimulate.active_strength! + stimulate.active_strength! * 0.15;
    for (let i = stimulate.active_strength!; i < value; i++) {
      const isCorrect = isPowerOver(props.stimulate, i);
      if (!isCorrect) {
        value = i - 1;
        break;
      }
    }

    return value > 100 ? 100 : Math.floor(value);
  };
  const getMin = () => {
    const value = stimulate.active_strength! - stimulate.active_strength! * 0.15;

    return value < 1 ? 1 : Math.floor(value);
  };
  const backIntervalRef = React.useRef<any>(0);
  const startBackInterval = () => {
    let i = 3;
    setBackInfo(prev => ({ ...prev, backOpen: true }));
    backIntervalRef.current = setInterval(() => {
      i--;
      setBackInfo(prev => ({ ...prev, backTime: i }));
      if (i === 0) {
        clearInterval(backIntervalRef.current);
        treatComplete();
      }
    }, 1000);
  };
  const treatComplete = () => {
    setBackInfo({ backOpen: false, backTime: 3 });
    onOver();
    clearInterval(backIntervalRef.current);
  };

  return (
    <div className={styles.stimulatePage}>
      <div className={styles.topLayout}>
        <div className={styles.leftCard}>
          <div className={styles.pauseTitle}>
            {isPause && (
              <div className={styles.innerTitle}>
                <span className={styles.icon_layout}>
                  <WarnMessage />
                </span>
                <span className={styles.ml12}>{intl.formatMessage({ id: '脉冲暂停' })}</span>
              </div>
            )}
          </div>
          <TreatCircleProgress
            percent={Math.floor((hadPulseTotal / stimulate.pulse_total) * 100)}
            strokeWidth={20}
            size={180}
            isTreating={!isPause}
          />
          <Countdown status={isPause ? 'treat_pause' : ''} time={secToTime(residualTime)} />
          <Pluse status={treatStatus.current} total={stimulate.pulse_total} count={hadPulseTotal} />
        </div>
        <div className={styles.rightCard}>
          <div className={classnames(styles.strength, isPause ? styles.strength_pause : '')}>
            <span className={styles.label}>{intl.formatMessage({ id: '实际强度(%MO) :' })}&nbsp; &nbsp;</span>
            <span className={styles.value}>
              <span className={styles.num}>{activeStrength}</span>
              {isPause && (
                <span className={styles.range}>
                  ({getMin()} - {getMax()})
                </span>
              )}
            </span>
          </div>
          <TBSChart template={stimulate} isNotLastRow motionThreshold={100} />
        </div>
      </div>
      <div className={styles.bottomLayout}>
        <NgDarkButton type={'default'} style={{ width: 120 }} onClick={handleOver}>
          {intl.formatMessage({ id: '结束' })}
        </NgDarkButton>
      </div>

      <NgModal
        open={backInfo.backOpen}
        footer={<React.Fragment />}
        closable={false}
        closeIcon={<React.Fragment />}
        width={400}
        className={'back_modal'}
        centered
      >
        <div className={styles.countdownContent}>
          <div className={styles.anticon}>
            <Icon component={SuccessMessage} style={{ width: 34, height: 34, fontSize: 34 }} rev="" />
          </div>
          <div className={styles.treat_complete_text}>本次治疗完成</div>
          <div className={styles.auto_hide_text}>{backInfo.backTime}秒后自动消失</div>
          <NgButton onClick={treatComplete} className={styles.back_button}>
            返回
          </NgButton>
        </div>
      </NgModal>

      <ErrorModel onOpen={checkNeedError} isStimulate={isStart} faultTypeList={notNAVTypeList} onOk={handleErrorModelClose} />
    </div>
  );
};
