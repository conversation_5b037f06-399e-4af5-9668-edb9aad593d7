import React, { useEffect, useRef, useState } from 'react';
import styles from './index.module.less';
import { getM200ApiInstance } from '@/common/api/ngApiAgent';
import { EditParamsPage } from '@/renderer/container/noPatientTms/component/editParamsPage';
import { EnumPlanStimulusType, EnumUserPageQueryModelRoleEnumList, PlanStimulusModel } from '@/common/types';
import { StimulatePage } from '@/renderer/container/noPatientTms/component/stimulatePage';
import { useIntl } from 'react-intl';
import { NgBreadcrumb } from '@/renderer/uiComponent/NgBreadCrumb';
import CameraAndCoil from '@/renderer/component/cameraAndCoil';
import { sendParamsRTMSToTms, sendParamsTBSToTms } from '@/renderer/container/noPatientTms/utils/sendParamsToTms';
import { calTbsChartData, disableBeatOfTemp } from '@/renderer/component/template/calTemplate';
import { message } from 'antd';
import { useRecoilState, useSetRecoilState } from 'recoil';
import { tmsCoilSelector } from '@/renderer/recoil/tmsError';
import { useNavigate } from 'react-router-dom';
import classNames from 'classnames';
import { UserSessionProps, withUserSession } from '../../hocComponent/withUserSession';
import { noPatientBreadcrumbList } from '../previewNoImagePlan/config';
import { v4 as uuidv4 } from 'uuid';
import { isNotTreatingAtom } from '../../recoil/isNotTreating';
import { FaultStatusEnum } from '../../../common/systemFault/type';
import { TMSScreenState } from '../../../common/constant/tms';

export type StimulateModel = PlanStimulusModel & { active_strength?: number };

const NoPatientTms = (props: UserSessionProps) => {
  const tidRef = useRef('');
  const [isEdit, setIsEdit] = useState(true);
  const [importDisabled, setImportDisabled] = useState(false);
  const intl = useIntl();
  const [isTechSupport] = useState(+(props.userSession?.role_id || 0) === EnumUserPageQueryModelRoleEnumList.TechSupport);
  const [stimulate, setStimulate] = useState<StimulateModel>({
    active_strength: undefined,
    type: EnumPlanStimulusType.TBS,
    relative_strength: 0,
    pulse_total: 0,
    treatment_time: 0,
  }); // 刺激参数
  const m200Api = getM200ApiInstance();
  const [coilSelector] = useRecoilState(tmsCoilSelector);
  const setIsNotTreating = useSetRecoilState(isNotTreatingAtom);
  const navigate = useNavigate();
  useEffect(() => {
    fetchData();
    // 初次进入初始化，如页面刷新
    window.tmsAPI.set_beat_screen(TMSScreenState.NotStarted);
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    window.tmsAPI.noImage_treatment_plan_start('SingleEnd');
  }, []);

  useEffect(() => {
    window.systemAPI.pushSystemFault({ '0A030005': FaultStatusEnum.normal }, 'noPatientTms page 删除tms故障');
    setIsNotTreating(isEdit);
  }, [isEdit]);

  // @ts-ignore
  const fetchData = async (): never => {
    const size = (await m200Api.getStimulusTemplateList({ page_num: 1, page_size: 10 })).records.length;
    setImportDisabled(size === 0);
  };
  const handleOk = async (params: StimulateModel): Promise<any> => {
    tidRef.current = uuidv4();
    const disableTreat = disableBeatOfTemp(coilSelector.temperature || 0, params.active_strength!, params, isTechSupport);
    if (disableTreat) {
      await message.warning({ content: intl.formatMessage({ id: '存在超温风险，请降温后再试' }) });

      return;
    }
    if (coilSelector.temperature > 36) {
      await message.warning({
        content: '存在超温风险，请降温后再试',
      });

      return;
    }
    const { pulse_total, treatment_time } = calTbsChartData(params);
    params.pulse_total = pulse_total;
    params.treatment_time = treatment_time;
    setStimulate(params);
    let status: string;
    if (params.type === EnumPlanStimulusType.TBS) {
      status = await sendParamsTBSToTms(params, tidRef.current);
    } else {
      status = await sendParamsRTMSToTms(params, tidRef.current);
    }
    if (status === 'error') return;

    setIsEdit(false);
  };
  const handleOver = () => {
    setIsEdit(true);
  };
  const setActiveStrength = (newActiveStrength: number) => {
    setStimulate({ ...stimulate, active_strength: newActiveStrength });
  };

  return (
    <div className={classNames(styles.noPatientTms)}>
      <div className={styles.header}>
        <CameraAndCoil />
        <div className={styles.left}>
          <NgBreadcrumb isGray={false} items={noPatientBreadcrumbList(isEdit, [() => navigate('/home')])} />
        </div>
        <div className={styles.center} />
        <div className={styles.right} />
      </div>
      <div className={styles.content}>
        {isEdit && <EditParamsPage isTechSupport={isTechSupport} iconDisabled={importDisabled} onOk={handleOk} stimulate={stimulate} />}
        {!isEdit && (
          <StimulatePage
            isTechSupport={isTechSupport}
            stimulate={stimulate}
            onOver={handleOver}
            tid={tidRef.current}
            onSetActiveStrength={setActiveStrength}
          />
        )}
      </div>
    </div>
  );
};

export default withUserSession(NoPatientTms);
