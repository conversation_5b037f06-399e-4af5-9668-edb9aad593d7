import React, { useRef, useState } from 'react';
import { useAsyncEffect } from 'ahooks';
import { getM200ApiInstance } from '../../../common/api/ngApiAgent';
import { UserSessionProps, withUserSession } from '../../hocComponent/withUserSession';
import { PlanImportInfoModel, PlanModel, PlanTargetModel } from '../../../common/types';
import { useNavigate, useParams } from 'react-router';
import { SpotItem } from '../../component/spotItem';
import classNames from 'classnames';
import styles from './index.module.less';
import { RegistCoilHeader } from '../registCoil/component/registCoilHeader';
import { TreatmentSurface } from '../../component/treatmentSurface/treatmentSurface';
import { ErrorModel } from '../../component/systemErrorModel/errorModel';
import { ITmsError } from '../../recoil/tmsError';
import { useRecoilState } from 'recoil';
import { useIntl } from 'react-intl';
import { useStoragePath } from '../../recoil/storagePath';
import { connSocket } from '../../utils/imgSocket';
import { Spin } from 'antd';
import { useLineAtom } from '../../recoil/lineStatus';
import { beyondAllEntryPoint } from '../previewPlan/utils';
import { SpotList } from '../previewTreat';
import { NgLoading } from '../../uiComponent/NgLoading';
import { SimpleErrorModel } from '../../component/systemErrorModel/simpleErrorModel';
import { FaultEnum } from '../../../common/systemFault/type';
export type EmgContainerPropsType = { tmsError: ITmsError } & UserSessionProps & { subjectId?: string; planId?: string };

export const BatVerification = (props: EmgContainerPropsType) => {
  const m200Api = getM200ApiInstance();
  const intl = useIntl();
  const { subjectId, planId } = useParams();
  const volumeRef = useRef<{
    volumeViewer: any;
    volumeScalpIndex: number | undefined;
    isDetectLine: boolean;
  }>({ isDetectLine: false, volumeViewer: undefined, volumeScalpIndex: undefined });
  const navigate = useNavigate();
  const [planInfo, setPlanInfo] = useState<PlanModel>();
  const [activeSpot, setActiveSpot] = useState<(PlanTargetModel & { is_active?: boolean }) | undefined>();
  const [fileInfo, setFileInfo] = useState<{ [prop: string]: string }>();
  const [spotList, setSpotList] = useState<PlanTargetModel[] | undefined>();
  const [registCoilVisible, setRegistCoilVisible] = useState(false);
  const [storagePathPrePath] = useRecoilState(useStoragePath);
  const [isTreating, setIsTreating] = useState<boolean>(false);
  const [spinning, setSpinning] = useState<boolean>(true);
  const [_, setLine] = useRecoilState(useLineAtom);

  useAsyncEffect(async () => {
    await getPlanInfo(storagePathPrePath);
  }, []);

  const handleErrorModalOK = () => {
    navigate('/techsupport');
  };

  const getPlanInfo = async (prePath: string) => {
    const subjectIdVar = subjectId ? subjectId : '';
    const planIdVar = planId ? planId : '';
    const plan = await m200Api.getPlanById(parseInt(planIdVar, 10), parseInt(subjectIdVar, 10));
    const mepTargetList = plan?.plan_target_model_list?.filter(item => item.has_mep) || [];
    const spotArr = mepTargetList?.length > 0 ? mepTargetList : plan?.plan_target_model_list || [];
    if (spotArr.length === 0) return;
    let activeSpotVar = spotArr.find((spot: PlanTargetModel) => !!spot.normal_line);
    activeSpotVar = activeSpotVar ? activeSpotVar : spotArr[0];
    const file_info = plan.plan_file_model_list?.reduce((pre: any, cur: any) => ({ ...pre, [cur.name]: `${prePath}/${cur.relative_path}` }), {});
    const plyFile = plan?.plan_file_model_list?.find(item => item.name === 'face.ply')?.relative_path;
    if (plyFile) await setPlyMd5(prePath, plyFile);
    setImportInfoModel(plan.plan_import_info_model);
    setFileInfo(file_info);
    setPlanInfo(plan);
    setActiveSpot(activeSpotVar);
    setSpotList(spotArr.map(v => ({ ...v, disable_horizontal: false })));

    if (activeSpotVar && activeSpotVar?.normal_line) {
      hasMepAndStartTreating(subjectIdVar, planIdVar, spotArr, file_info);
    } else {
      await detectNormalLine(spotArr);
    }
  };

  const setImportInfoModel = (importModel?: PlanImportInfoModel) => {
    if (!importModel) return;
    connSocket.import_id = importModel.import_id;
    connSocket.file_sha256 = importModel.file_sha256;
    connSocket.file_version = importModel.file_version;
    connSocket.file_source = importModel.file_source;
  };

  const setPlyMd5 = async (prePath: string, plyFile: string) => {
    const plyMd5 = await window.fileAPI.getFileMd5(`${prePath}/${plyFile}`);
    connSocket.plyFile = `${prePath}/${plyFile}`;
    connSocket.plyMd5 = plyMd5;
  };

  const hasMepAndStartTreating = (subjectIdVar: string, planIdVar: string, spotArr: PlanTargetModel[], file_info: any) => {
    startTreating();
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    findNotNormalLine(subjectIdVar, planIdVar, spotArr, file_info);
  };

  const findNotNormalLine = async (subjectIdVar: string, planIdVar: string, spotArr: PlanTargetModel[], file_info: any) => {
    const notNormalLine = spotArr.find((obj: PlanTargetModel) => !obj.normal_line && obj.has_mep);
    const callBack = async () =>
      beyondAllEntryPoint(
        volumeRef.current.volumeViewer,
        volumeRef.current.volumeScalpIndex!,
        spotArr,
        {
          plan_id: planId,
          subject_id: subjectId,
        },
        setLine,
        (file_info || {})['head_convex_hull.ply']
      ).then(async () => {
        const plan = await m200Api.getPlanById(parseInt(planIdVar, 10), parseInt(subjectIdVar, 10));
        const mepTargetList = plan?.plan_target_model_list?.filter(item => item.has_mep) || [];
        const newSpotArr = mepTargetList?.length > 0 ? mepTargetList : [];
        setSpotList(newSpotArr.map(v => ({ ...v, disable_horizontal: false })));
      });
    if (notNormalLine && volumeRef.current.volumeScalpIndex && volumeRef.current.volumeViewer) {
      // eslint-disable-next-line @typescript-eslint/no-floating-promises
      callBack();
    } else if (notNormalLine) {
      setTimeout(() => {
        // eslint-disable-next-line @typescript-eslint/no-floating-promises
        findNotNormalLine(subjectIdVar, planIdVar, spotArr, file_info);
      }, 1000);
    }
  };

  const detectNormalLine = async (spotArr: PlanTargetModel[]) => {
    if (volumeRef.current.volumeScalpIndex !== undefined && volumeRef.current.volumeViewer) {
      computeBeyondAllEntryPoint(volumeRef.current.volumeScalpIndex, spotArr);
    } else if (!volumeRef.current.volumeScalpIndex || !volumeRef.current.volumeViewer) {
      setTimeout(async () => {
        await getPlanInfo(storagePathPrePath);
      }, 1500);
    }
  };

  const computeBeyondAllEntryPoint = (scalpIndex: number, spotArr: PlanTargetModel[]) => {
    volumeRef.current.isDetectLine = true;
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    beyondAllEntryPoint(
      volumeRef.current.volumeViewer,
      scalpIndex,
      spotArr,
      {
        plan_id: planId,
        subject_id: subjectId,
      },
      setLine,
      (fileInfo || {})['head_convex_hull.ply']
    ).then(async () => {
      await getPlanInfo(storagePathPrePath);
    });
  };

  const handleRegistCoilOk = () => {
    setRegistCoilVisible(false);
    navigate('/registCoil');
  };

  const handleRegistCoilCancle = () => {
    setRegistCoilVisible(false);
    navigate('/techsupport');
  };

  const startTreating = () => {
    setTimeout(() => {
      setIsTreating(true);
    }, 2000);
    setSpinning(false);
  };

  const handleSpotClick = (id: number) => {
    setIsTreating(false);
    let newActiveSpot: (PlanTargetModel & { is_active: boolean }) | undefined;
    const spotListVal = spotList ? spotList : [];
    const newSpotList = spotListVal.map((spot: PlanTargetModel) => {
      if (spot.id === id) newActiveSpot = { ...spot, is_active: true };
      if (spot.id === id) {
        return { ...spot, is_active: true };
      } else {
        return { ...spot, is_active: false };
      }
    });
    if (!newActiveSpot?.normal_line) return;
    setSpotList(newSpotList);
    setActiveSpot(newActiveSpot);
    setTimeout(() => {
      setIsTreating(true);
    }, 300);
  };

  const renderLoading = () => {
    return (
      <div>
        <NgLoading loadingText={'加载中...'} />
      </div>
    );
  };

  const renderSpotList = () => {
    return (
      <div className="spot_list_and_control">
        <div className="spot_list">
          {spotList?.map((v: SpotList, index: number) => (
            <SpotItem
              key={v.id}
              spotInfo={v}
              onSelect={handleSpotClick}
              index={index}
              isBatVerification
              isTreat
              activeId={activeSpot?.id !== undefined ? activeSpot.id : null}
            />
          ))}
        </div>
      </div>
    );
  };

  if (!fileInfo) return <React.Fragment />;
  const pialUrl = fileInfo['pial.gii'];
  const volumeFiles = fileInfo['T1.mgz'];
  const scalpMaskUrl = fileInfo['scalp_mask.obj'];
  if (!planInfo || !pialUrl || !scalpMaskUrl || !volumeFiles || !spotList || !activeSpot) return <React.Fragment />;

  return (
    <div className={classNames(styles.preview_treat, 'preview_treat')}>
      <Spin spinning={spinning} indicator={renderLoading()}>
        <RegistCoilHeader isDisable={false} title={intl.formatMessage({ id: '验证' })} />
        <div className={styles.flex}>
          <TreatmentSurface
            pialUrl={pialUrl}
            scalpMaskUrl={scalpMaskUrl}
            volumeFiles={[fileInfo['T1.mgz'], fileInfo['scalp_mask.nii']]}
            spotList={spotList}
            targetStimulate={activeSpot.stimulus}
            activeSpot={activeSpot}
            isTreating={isTreating}
            isBatVerifiCation
            planId={planId ? planId : ''}
            subjectId={subjectId ? subjectId : ''}
            planInfo={planInfo}
            isEmg
            children={null}
            immediateViewClass={'immediateViewClass'}
            SpotList={renderSpotList()}
            activeId={activeSpot?.id !== undefined ? activeSpot.id : null}
            getVolumeScalpIndex={(index: number) => (volumeRef.current.volumeScalpIndex = index)}
            setVolumeViewer={(viewer: any) => (volumeRef.current.volumeViewer = viewer)}
            horizontalDisable
            horizontal={0}
            coilPanelMissingCallback={() => setRegistCoilVisible(true)}
            helper
          />
        </div>
        {registCoilVisible && (
          <SimpleErrorModel
            visible
            isClearRegistCoilInfo
            title="线圈注册结果异常"
            isStimulate={false}
            errorList={['重新注册线圈后，可继续使用']}
            btnLabel={'线圈注册'}
            cancleLabel="取消"
            onCancle={handleRegistCoilCancle}
            onOk={handleRegistCoilOk}
          />
        )}
        <ErrorModel faultTypeList={[FaultEnum.imageFault]} isStimulate={false} onOk={handleErrorModalOK} />
      </Spin>
    </div>
  );
};

export default withUserSession(BatVerification);
