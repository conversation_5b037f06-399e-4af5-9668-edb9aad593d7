@import '../../static/style/baseColor.module.less';
.preview_treat {
  height: 100%;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  .flex {
    display: flex;
    overflow: hidden;
    margin-top: -46px;
  }
  :global {
    .ant-spin-nested-loading > div > .ant-spin {
      max-height: 1200px;
    }
    .ant-spin-nested-loading {
      height: 100%;
      overflow: hidden;
    }
    .ant-spin-container {
      overflow: hidden;
      display: flex;
      flex-direction: column;
      height: 100%;
    }
    .immediateViewClass {
      display: flex;
      flex-direction: column;
    }
    .photo_and_coil {
      position: absolute;
      top: 20px;
      left: 50%;
      transform: translateX(-50%);
    }

    .strength {
      margin-top: 15px;
      margin-bottom: 12px;
      width: 410px;
      height: 108px;
      border-radius: 8px;
      background-color: @colorA3;
      opacity: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;

      .item {
        display: flex;
        align-items: center;
        padding: 0 24px 0 36px;
        justify-content: space-between;
        font-size: 16px;
        font-weight: 350;
        letter-spacing: 0em;
        color: @colorA12;
        .title {
          display: flex;
          align-items: center;
          .bar {
            width: 6px;
            height: 16px;
            border-radius: 10px;
            margin-right: 10px;
          }
        }
        .value {
          font-size: 26px;
          font-weight: 500;
          letter-spacing: 0em;
          color: @colorB3;
        }
        .strength_progress {
          position: relative;
          z-index: 2;

          & > div {
            transform: unset;
          }
        }
      }
    }

    .surface_spot_view {
      margin-top: -12px;
      display: flex;
      padding: 0 12px;
      overflow: hidden;
      height: 100%;
      width: 1920px;
      justify-content: space-between;
      margin-bottom: 130px;
      position: relative;
      .patient_name {
        position: absolute;
        top: 28px;
        left: 15px;
        color: @colorA10;
      }
      .immediate_view_box {
        margin-top: 67px;
      }
      .surface_box {
        width: 1186px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .spot_list_and_control {
        width: 410px;
        height: 100%;
        display: flex;
        justify-content: space-between;
        flex: 1;
        overflow-y: auto;
        margin-top: 21px;
        overflow: hidden;
        .spot_list {
          width: 410px;
          overflow-y: auto;
          &::-webkit-scrollbar {
            width: 6px;
          }
          &::-webkit-scrollbar-thumb {
            background: @colorA6;
            border-radius: 10px;
          }
          &::-webkit-scrollbar-thumb:vertical {
            width: 10px;
            height: 10px;
          }
        }

        .ensure_template {
          width: 100%;
          .ant-btn {
            width: 100%;
            height: 48px;
            border-radius: 8px;
            background-color: @colorA4_1;
            color: @colorA12;
          }
        }
        .ensure_template[disabled] {
          color: @colorA6;
        }
      }
      .window_and_treat_info {
        width: 722px;
        height: 100%;
        display: flex;
        justify-content: space-between;
        padding-bottom: 20px;
        .window {
          width: 410px;
          height: 450px;
        }
        .treat_info {
          height: 100%;
          .blank {
            height: 12px;
            background-color: @colorA1;
            z-index: 9;
            position: relative;
          }
          .stop_treat {
            width: 100%;
            .ant-btn {
              width: 100%;
              height: 48px;
              border-radius: 8px;
              background-color: @colorA4_1;
              color: @colorA12;
              margin-top: 12px;
            }
          }
        }
      }
    }
    .ant-modal-root {
      .back_modal {
        .ant-modal-footer {
          display: none;
        }
        .ant-modal-body {
          display: flex !important;
          flex-direction: column !important;
          justify-content: center;
          align-items: center;
          .action {
            width: 50px;
            height: 50px;
          }
        }
      }
    }
    .msg {
      position: absolute;
      top: 109px;
      left: 50%;
      transform: translateX(-50%);
      z-index: 9;
      width: 269px;
      height: 48px;
      background-color: @colorA5;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      svg {
        margin-right: 9px;
      }
    }
  }
}
