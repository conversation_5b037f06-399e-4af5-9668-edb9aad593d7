import React, { ReactElement, useEffect, useState } from 'react';
import styles from './index.module.less';
import { NgLogoIcon } from '@/renderer/uiComponent/SvgGather';
import Description from '@/renderer/container/techsupport/components/description';
import Account from '@/renderer/container/techsupport/components/account';
import Vision from '@/renderer/container/techsupport/components/version';
import Tms from '@/renderer/container/techsupport/components/tms';
import License from '@/renderer/container/techsupport/components/license';
import Coil from '@/renderer/container/techsupport/components/coil';
import Cooling from '@/renderer/container/techsupport/components/cooling';
import Disk from '@/renderer/container/techsupport/components/disk';
import Power from '@/renderer/container/techsupport/components/power';
import Fault from '@/renderer/container/techsupport/components/fault';
import TechSupportToolBox from '@/renderer/container/techsupport/components/techsupportToolBox';
import { ProductType } from '../../constant/product';
import BindBox from '@/renderer/container/techsupport/components/bindBox';
import CameraAndCoil from '../../component/cameraAndCoil';
import { CameraInfoType, TmsInfoType } from '../../../common/types';
import { connSocket } from '../../utils/imgSocket';
import { faultAtom, getFaultByKey } from '@/renderer/recoil/fault';
import { useRecoilState, useSetRecoilState } from 'recoil';
import { FaultKeyEnum } from '../../../common/systemFault/type';
import { NgLoading } from '../../uiComponent/NgLoading';
import { queryCoilInfo } from '../../router/utils';
import { Spin } from 'antd';
import { useAsyncEffect } from 'ahooks';
import { tmsCoilSelector } from '../../recoil/tmsError';
import { useInterval } from 'react-timing-hooks';

type Props = {};

const TechSupport = (props: Props) => {
  const [fault] = useRecoilState(faultAtom);
  const [cameraInfo, setCameraInfo] = useState<CameraInfoType | undefined>();
  const [tmsInfo, setTmsInfo] = useState<TmsInfoType | undefined>();
  const [productInfo, setProductInfo] = useState<ProductType>();
  const [loading, setLoading] = useState<boolean>(false);
  const setTmsCoil = useSetRecoilState(tmsCoilSelector);

  /**
   * loading加载提示文案
   */
  const renderLoading = (): ReactElement => {
    return (
      <div>
        <NgLoading loadingText={'恢复出厂设置中，请稍后'} />
      </div>
    );
  };

  /**
   * 恢复系统配置的laoding显示
   */
  const resetFactoryLoading = (isLoading: boolean) => {
    setLoading(isLoading);
  };

  /** 更新product info*/
  const updateProductInfo = async () => setProductInfo(await window.systemAPI.getProductInfo());
  /**
   * 更新相机信息
   */
  const updateCameraInfo = async () => {
    if (!!getFaultByKey(FaultKeyEnum.A080001) || !!getFaultByKey(FaultKeyEnum.A080002)) {
      setCameraInfo(undefined);

      return;
    }

    try {
      const res: any = await connSocket.getCameraInfo();
      const serialNum = res.camera_serial_number;

      const cameraData: CameraInfoType = {
        cameraSerialNum: serialNum,
        version: res.version,
        commit: res.commit,
      };

      setCameraInfo(cameraData);
    } catch (error) {
      setTimeout(async () => {
        await updateCameraInfo();
      }, 500);
    }
  };

  useEffect(() => {
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    updateCameraInfo();
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    updateTmsVersion();
  }, [fault]);

  useAsyncEffect(async () => {
    await queryCoilInfo({ setCoilSelector: setTmsCoil });
  }, []);

  /**
   * 更新tms版本信息
   */
  const updateTmsVersion = async () => {
    if (!!getFaultByKey(FaultKeyEnum.A030001)) {
      setTmsInfo(undefined);

      return;
    }

    const data = await window.tmsAPI.get_tms_version();
    const tmsData = data
      ? {
          firmware_version: data.data.firmware_version || '',
          commit: data.data.commit || '',
          voltageB: data.data.voltageB,
          voltageK: data.data.voltageK,
          voltage_calibration: data.data.voltage_calibration,
          treat_count: data.data.treat_count,
          sn: data.data.sn,
        }
      : {
          firmware_version: '',
          commit: '',
        };

    setTmsInfo(tmsData);
  };

  useEffect(() => {
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    updateTmsVersion();
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    updateProductInfo();
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    updateCameraInfo();
  }, []);

  useInterval(updateTmsVersion, 3500, { startOnMount: true });

  return (
    <Spin spinning={loading} delay={500} indicator={renderLoading()} wrapperClassName={styles.loading}>
      <div className={styles.container}>
        <div className={styles.header}>
          <NgLogoIcon className={styles.logoIcon} />
          <CameraAndCoil />
          <TechSupportToolBox />
        </div>
        <div className={styles.view}>
          <div className={styles.first_column}>
            <Description productInfo={productInfo} resetLoading={resetFactoryLoading} cameraInfo={cameraInfo} tmsInfo={tmsInfo} />
            <Account />
            <BindBox />
            <License />
          </div>
          <div className={styles.second_column}>
            <Fault />
          </div>
          <div className={styles.third_column}>
            <Vision cameraInfo={cameraInfo} />
            <Coil />
            <Disk />
          </div>
          <div className={styles.fourth_column}>
            <Tms versionInfo={tmsInfo} />
            <Cooling />
            <Power />
          </div>
        </div>
      </div>
    </Spin>
  );
};

export default TechSupport;
