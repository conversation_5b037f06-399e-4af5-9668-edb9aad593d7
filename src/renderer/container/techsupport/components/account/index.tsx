import React from 'react';
import styles from './index.module.less';
import NgDarkButton from '@/renderer/uiComponent/NgDarkButton';
import { useNavigate } from 'react-router';
import { product } from '@/renderer/constant/product';
import classnames from 'classnames';

const Account = () => {
  const navigate = useNavigate();
  const handleClick = () => {
    navigate('/manage');
  };

  return (
    <div
      className={classnames(styles.container, styles.container, {
        [styles.hasNavContainer]: product.isNav,
      })}
    >
      <span className={styles.title}>账号管理</span>
      <NgDarkButton onClick={handleClick}>账号管理</NgDarkButton>
    </div>
  );
};

export default Account;
