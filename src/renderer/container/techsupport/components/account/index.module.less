@import '@/renderer/static/style/baseColor.module.less';

.hasNavContainer {
  height: 68px !important;
  display: flex;
  flex-direction: row !important;
}

.container {
  display: flex;
  position: relative;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding: 24px;
  border-radius: 8px;
  background-color: @colorA3;
  margin-top: 12px;
  font-size: 14px !important;

  :global {
    button.ant-btn.ant-btn-default.NgButton__defaultBtn___ypVbh {
      font-size: 14px !important;
    }
  }

  .title {
    font-weight: 600 !important;
  }

  & > h3 + div {
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
}
