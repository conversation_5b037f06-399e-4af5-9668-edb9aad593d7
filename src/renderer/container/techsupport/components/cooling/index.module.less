@import '@/renderer/static/style/baseColor.module.less';

.container {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  font-size: 14px;
  background-color: @colorA3;
  border-radius: 8px;
  padding: 24px 24px 14px 24px;
  margin-top: 12px;
  flex-grow: 1;

  .title {
    font-weight: 600 !important;
    margin-bottom: 12px;
  }

  .content {
    font-weight: 350;
    flex-grow: 1;
    color: @colorA10;

    .item {
      margin-bottom: 10px;
      .fan_val {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
      }

      .fan {
        margin: 0;
        margin-bottom: 10px;
      }
    }
  }
}
