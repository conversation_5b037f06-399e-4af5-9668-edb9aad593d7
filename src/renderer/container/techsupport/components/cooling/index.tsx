import React, { useEffect, useState } from 'react';
import styles from './index.module.less';
import { useMount } from 'ahooks';
import { renderCoolList } from '@/renderer/container/techsupport/components/config';
import { faultAtom, getFaultByKey } from '@/renderer/recoil/fault';
import { FaultKeyEnum } from '@/common/systemFault/type';
import { useRecoilState } from 'recoil';

type Props = {};

const Cooling = (props: Props) => {
  const [info, setInfo] = useState<any>({});
  const [fault] = useRecoilState(faultAtom);

  useMount(async () => {
    await window.tmsAPI.water_cooling_query_by_key('water_query', async (_, data) => {
      const waterInfo = data.data;
      const isNotOpen = waterInfo.content_status === 0;
      setInfo(isNotOpen ? {} : data.data);
    });
  });

  useEffect(() => {
    const isNotConnectCoil = getFaultByKey(FaultKeyEnum.A030001) || getFaultByKey(FaultKeyEnum.A040001) || getFaultByKey(FaultKeyEnum.A060001);
    if (isNotConnectCoil) {
      setInfo({});
    }
  }, [fault]);

  return (
    <div className={styles.container}>
      <span className={styles.title}>液冷</span>
      <div className={styles.content}>
        {renderCoolList.map(v => {
          return (
            <div key={v.value} className={styles.item}>
              <span className={styles.label}>{v.label}</span>
              {v.render ? (
                v.render(info[v.value], info)
              ) : (
                <span className={styles.value}>{info[v.value] ? `${info[v.value]} ${v?.suffix || ''}` : '--'}</span>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};
export default Cooling;
