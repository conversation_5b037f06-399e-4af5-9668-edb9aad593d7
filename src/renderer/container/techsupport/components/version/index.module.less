@import '@/renderer/static/style/baseColor.module.less';

.container {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  font-size: 14px;
  height: 270px;
  border-radius: 8px;
  padding: 24px;
  background-color: @colorA3;
  font-weight: 350;

  .title {
    display: flex;
    justify-content: space-between;
    font-weight: 600 !important;

    .title_right_icon {
      margin: 0;
      font-size: 14px;
      font-weight: 350;

      .label {
        vertical-align: middle;
      }

      & > .switch {
        display: inline;
        margin-left: 11px;
      }
    }
  }

  .content {
    flex-grow: 1;
    margin-top: 12px;
    color: @colorA10;

    .item {
      margin-bottom: 10px;
    }
  }

  .footer {
    text-align: right;

    .coilButtonError {
      position: absolute;
      top: 3px;
      right: 13px;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: @colorD3_start;
    }

    & > div {
      margin-left: 20px;
      display: inline-block;
    }
  }
}

.createPatientDrop {
  :global {
    .ant-dropdown-menu {
      background: @colorA4 !important;
      padding: 10px 17px !important;
    }

    .ant-dropdown-menu-item {
      cursor: url('@/renderer/static/svg/cursor.cur'), pointer !important;
      font-family: normal-font -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif,
        'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
      text-align: center;
      border-radius: 6px !important;
      padding: 0 !important;
      margin-bottom: 10px !important;

      &:hover {
        background: @colorA6 !important;
      }

      & a {
        cursor: url('@/renderer/static/svg/cursor.cur'), pointer !important;
      }
    }

    .ant-dropdown-menu-title-content {
      width: 100%;
      line-height: 32px;
      height: 32px;
      font-family: sans-serif !important;

      a {
        color: @colorA12 !important;
        position: relative;
        width: 100%;
        display: inline-block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: 16px;
        line-height: 30px;
        padding: 0 6px;
      }
    }

    .ant-dropdown-menu-item-disabled .ant-dropdown-menu-title-content > a {
      color: @colorA9 !important;
    }

    .ant-dropdown-menu-item-disabled {
      cursor: url('@/renderer/static/svg/disableMouse.cur'), not-allowed !important;
    }
  }
}
