import React, { useEffect, useRef, useState } from 'react';
import styles from './index.module.less';
import NgDarkButton from '@/renderer/uiComponent/NgDarkButton';
import { useNavigate } from 'react-router';
import { useRecoilState, useRecoilValue } from 'recoil';
import { useLicenseAtom } from '../../../../recoil/license';
import { tmsCoilSelector } from '../../../../recoil/tmsError';
import classnames from 'classnames';
import { useMount, useUnmount } from 'ahooks';
import { connSocket } from '../../../../utils/imgSocket';
import { Dropdown, MenuProps, message } from 'antd';
import { getDropdownStaticList, importPatientInfo } from '../config';
import { getM200ApiInstance } from '../../../../../common/api/ngApiAgent';
import { CameraInfoType, PlanModel } from '../../../../../common/types';
import NgSelectFileModal from '../../../../uiComponent/NgSelectFileModal';
import { DeviceType } from '../../../previewPlan';
import { osUserInfo } from '../../../../recoil/osUserInfo';
import NgModal from '../../../../uiComponent/NgModal';
import { ReactComponent as Warning } from '@/renderer/static/svg/warning.svg';
import { product } from '../../../../constant/product';
import NgSwitch from '../../../../uiComponent/NgSwitch';
import { faultAtom, getFaultByType } from '../../../../recoil/fault';
import { FaultEnum, FaultLevelEnum } from '../../../../../common/systemFault/type';

type Props = {
  cameraInfo?: CameraInfoType;
};

const Vision = (props: Props) => {
  const { cameraInfo } = props;
  const navigate = useNavigate();
  const [dropdownList, setDropdownList] = useState<MenuProps['items']>([]);
  const [modalVisiable, setModalVisable] = useState(false);
  const [uploading, setUploading] = useState<boolean>(false);
  const [extList, setExtList] = useState<string[]>([]);
  const [isImage, setIsImage] = useState<boolean>(false);
  const [license] = useRecoilState(useLicenseAtom);
  const [coilSelector] = useRecoilState(tmsCoilSelector);
  const [fault] = useRecoilState(faultAtom);
  const [mainControl, setMainControl] = useState(false);
  const osInfo = useRecoilValue(osUserInfo);
  const m200Api = getM200ApiInstance();
  const [messageApi, contextHolder] = message.useMessage();
  const deviceType: DeviceType = /macintosh|mac os x/i.test(navigator.userAgent) ? 2 : 1;
  const isNotConnectTms = fault[FaultLevelEnum.error].some(item => item.key === '0A040001' || item.key === '0A030001');
  const originCameraStatus = useRef<any>();

  useUnmount(() => {
    originCameraStatus.current = {};
  });
  const handleCoilRegisterClick = () => {
    navigate('/registCoil/step0');
  };

  const verifyAccuracy = (data: PlanModel) => {
    navigate(`/batVerification/${data.id}/${data.subject_id}`);
  };

  const parsePlanList = (data: PlanModel[]) => {
    return data.map(v => ({
      key: v.id,
      label: (
        <a
          onClick={() => {
            verifyAccuracy(v);
          }}
        >
          {v.subject_model.code}
        </a>
      ),
    }));
  };

  const handleUpdate = async () => {
    try {
      const list = await window.fileAPI.getFolderInfo(deviceType === DeviceType.mac ? '/' : osInfo.filePath!);
      if (list.length === 0) throw Error('未检测到移动设备');
      setModalVisable(true);
    } catch (error) {
      noFileMessage();
    }
  };

  const handleModalVisible = (type: boolean) => {
    setModalVisable(type);
  };
  const noFileMessage = () => {
    // eslint-disable-next-line no-void, @typescript-eslint/no-floating-promises
    messageApi.open({
      type: 'error',
      content: '未检测到移动设备',
    });
  };

  const getPlanList = async () => {
    const res = await m200Api.getPlanList({
      page_size: 999,
      page_num: 1,
    });
    setDropdownList([...getDropdownStaticList(handleUpdate), ...parsePlanList(res.records)]);
  };

  const getExtList = async () => {
    try {
      const ext_list = await window.systemAPI.getEnv('extList');
      const image_type = await window.systemAPI.getEnv('appImage');
      setExtList(ext_list || []);
      setIsImage(image_type === 'nav-appimage');
    } catch (error) {
      //
    }
  };

  const handleSubmitFileModal = async (path: string) => {
    setUploading(true);
    try {
      const res = await window.fileAPI.uploadFile(path);

      const isExit = await m200Api.isSubjectExit(res.plan.subject_model.code);
      if (isExit) {
        // eslint-disable-next-line no-void, @typescript-eslint/no-floating-promises
        messageApi.open({
          type: 'error',
          content: '数据已存在，上传失败',
        });

        return;
      }
      const params = {
        file_name: res.file_name,
        temp_directory: res.temp_directory,
        plan: {
          ...res.plan,
          subject_model: importPatientInfo.reduce(
            (pre, cur) => ({
              ...pre,
              [cur]: res.plan.subject_model[cur],
            }),
            {}
          ),
          plan_file_model_list: undefined,
          type: undefined,
        },
      };
      try {
        await m200Api.importPlan(params);
        // eslint-disable-next-line @typescript-eslint/no-floating-promises
        getPlanList();
      } catch (error) {
        // eslint-disable-next-line no-void, @typescript-eslint/no-floating-promises
        messageApi.open({
          type: 'error',
          content: '导入失败',
        });
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log(error, '新建患者的error');
      NgModal.confirm({
        content: <>{'文件信息不符合规范，请重新选择。'}</>,
        headerIcon: <Warning />,
        closable: false,
        okText: '确定',
      });
    } finally {
      setUploading(false);
      setModalVisable(false);
    }
  };

  const handleChangeSwitch = async (e: boolean) => {
    await m200Api.setConfig([
      {
        group_name: 'control',
        name: 'techsupportInfo',
        value: JSON.stringify({ mainControl: e }),
      },
    ]);
    setMainControl(e);
  };

  const initConfig = async () => {
    const [techsupportInfo] = await m200Api.getControlConfig();
    setMainControl(techsupportInfo.mainControl);
  };

  useMount(async () => {
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    initConfig();
  });

  useUnmount(async () => {
    connSocket.clearListenStatusByKey('versionCameraAndCoil');
  });

  useEffect(() => {
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    getPlanList();
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    getExtList();
  }, []);

  return (
    <div className={styles.container}>
      {contextHolder}
      <span className={styles.title}>
        视觉管理
        {product.isNav && (
          <div className={styles.title_right_icon}>
            <span className={styles.label}>视觉精度配置</span>
            <NgSwitch className={styles.switch} value={mainControl} size="small" onChange={handleChangeSwitch} />
          </div>
        )}
      </span>
      <div className={styles.content}>
        <div className={styles.item}>
          <span className={styles.label}>SLBS标识号：</span>
          <span className={styles.value}>{cameraInfo?.version ? `${cameraInfo.version}(${cameraInfo.commit})` : '--'}</span>
        </div>
        <div className={styles.item}>
          <span className={styles.label}>序列号：</span>
          <span className={styles.value}>{cameraInfo?.cameraSerialNum ? cameraInfo?.cameraSerialNum : '--'}</span>
        </div>
      </div>
      <div className={styles.footer}>
        {isImage && (
          <Dropdown
            menu={{ items: dropdownList }}
            overlayClassName={styles.createPatientDrop}
            disabled={(!license.hasLicenseError && !coilSelector?.isRegisterCoil) || getFaultByType(FaultEnum.imageFault, fault).length > 0}
          >
            <NgDarkButton>验证</NgDarkButton>
          </Dropdown>
        )}
        <NgDarkButton disabled={getFaultByType(FaultEnum.imageFault, fault).length > 0 || isNotConnectTms} onClick={handleCoilRegisterClick}>
          线圈注册
          <span
            className={classnames({
              // 已连接线圈，同时线圈未注册
              [styles.coilButtonError]: !license.hasLicenseError && !coilSelector?.isRegisterCoil, // 线圈连接不展示红点
            })}
          />
        </NgDarkButton>
      </div>
      <NgSelectFileModal
        maskClosable={false}
        filepath={deviceType === DeviceType.mac ? '/' : osInfo.filePath!}
        open={modalVisiable}
        onCancel={() => handleModalVisible(false)}
        width={770}
        controlLoading={uploading}
        handleError={noFileMessage}
        extList={extList}
        onOk={(files: any) => {
          // eslint-disable-next-line @typescript-eslint/no-floating-promises
          handleSubmitFileModal(files[0].path);
        }}
      />
    </div>
  );
};
export default Vision;
