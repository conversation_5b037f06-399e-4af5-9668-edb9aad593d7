import React, { useEffect, useMemo, useState } from 'react';
import styles from './index.module.less';
import NgModal from '@/renderer/uiComponent/NgModal';
import { NgProgress } from '@/renderer/uiComponent/NgProgress';
import { useIntl } from 'react-intl';
import NgButton from '@/renderer/uiComponent/NgButton';
import NgButtonText from '../../../../../uiComponent/NgButtonText';
import { ErrorIcon, SuccessIcon, WarningIcon } from '../../../../../uiComponent/SvgGather';

type Props = {
  visible: boolean;
  onClose(): void;
};

/**
 * processing: 同步中; failure: 失败； success:成功
 */
enum ProcessStatusEnum {
  processing = 0,
  failure,
  success,
}

export const BindBoxProcessModal = (props: Props) => {
  const intl = useIntl();
  const { visible, onClose } = props;
  const [process, setProcess] = useState<number>(0);
  const [finishNum, setFinishNum] = useState<number>(0);
  const [totalNum, setTotalNum] = useState<number>(0);
  const [processStatus, setProcessStatus] = useState<ProcessStatusEnum>(ProcessStatusEnum.processing);

  useEffect(() => {
    if (visible) {
      setProcess(100);
      setFinishNum(100);
      setTotalNum(1000);
      setProcessStatus(ProcessStatusEnum.success);
    }
  }, [visible]);

  /**
   * 提示的icon获取
   */
  const tipIcon = useMemo(() => {
    switch (processStatus) {
      case ProcessStatusEnum.processing:
        return <WarningIcon />;

      case ProcessStatusEnum.failure:
        return <ErrorIcon />;

      case ProcessStatusEnum.success:
        return <SuccessIcon />;

      default:
        return <WarningIcon />;
    }
  }, [processStatus]);

  /**
   * 点击事件处理
   */
  const clickAction = () => {
    onClose();
  };

  return (
    <NgModal title="" closable={false} open={visible} width={460} footer={<> </>}>
      <div className={styles.container}>
        <div className={styles.header}>
          {tipIcon}
          <div className={styles.tipText}>同步中，请不要离开当前页面</div>
        </div>
        <NgProgress percent={process} type={'circle'} size={180} strokeWidth={20} status={'normal'} trailColor={'#41414b'} strokeColor={'#4EB7B9'} />
        <div className={styles.processNum}>
          {finishNum}/ {totalNum}
        </div>
        <div className={styles.footer}>
          {processStatus === ProcessStatusEnum.processing && <NgButton onClick={clickAction}>{intl.formatMessage({ id: '终止' })}</NgButton>}
          {processStatus === ProcessStatusEnum.failure && (
            <div className={styles.failureContent}>
              <NgButtonText
                className={styles.cancel}
                onClick={() => {
                  // console.log('cancel ##');
                }}
              >
                {intl.formatMessage({ id: '取消' })}
              </NgButtonText>
              <NgButton onClick={clickAction}>{intl.formatMessage({ id: '重试' })}</NgButton>
            </div>
          )}
          {processStatus === ProcessStatusEnum.success && <NgButton onClick={clickAction}>{intl.formatMessage({ id: '关闭' })}</NgButton>}
        </div>
      </div>
    </NgModal>
  );
};
