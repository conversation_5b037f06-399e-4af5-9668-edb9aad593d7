@import '@/renderer/static/style/baseColor.module.less';

.bindBoxContainer {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  justify-content: space-between;
  border-radius: 8px;
  padding: 24px;
  background: @colorA3;
  font-weight: 350;

  &:hover {
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.5);
  }
  & > span {
    font-size: 14px;
    margin-bottom: 16px;
    display: flex;
    justify-content: space-between;
    font-weight: 600 !important;
    & > span {
      font-size: 14px;
      font-weight: 0;
      color: @colorB3;
    }
  }

  .boxForm_content {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    color: @colorA10;
  }

  .boxForm {
    flex-grow: 1;

    :global {
      label.ant-form-item-required {
        color: @colorA10 !important;
      }

      .ant-col > div:first-child:hover {
        box-shadow: unset;
      }

      .ant-form-item {
        &:nth-child(3) {
          & .ant-input-password {
            height: 32px;
          }
        }
        &:not(:first-child) {
          .ant-form-item-label {
            & label {
              margin-inline-end: 13px;
            }
          }
        }

        margin-bottom: 19px;
      }
      .ant-form-item .ant-form-item-control-input {
        height: auto !important;
        padding: 0 !important;
      }
      .ant-col {
        height: auto !important;
        padding: 0 !important;
      }
    }
  }
}
.unbinding {
  display: flex;
  justify-content: space-between;
  height: calc(100% - 45px);
  & > p {
    margin: 0;
    font-size: 14px;
    font-weight: 600 !important;
  }
  & .unbindBtn {
    align-self: flex-end;
    margin-top: 12px;
  }
}
.saveBox {
  margin-left: auto;
}

.optContainer {
  display: flex;
  align-items: center;
  & > svg:first-child {
    margin: 0 5px 0 0;
  }
}
.confirmContainer {
  :global {
    .ant-popover-inner {
      padding: 15px;
    }
  }
}
.syncTask {
  display: flex;
  flex-direction: row;
  font-size: 14px;
  margin-left: 66px;

  .selected {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: @colorC4;
    border-radius: 4px;
    width: 16px;
    height: 16px;
    margin-right: 12px;
    margin-top: 3px;
  }

  .no_selected {
    background-color: @colorA4_1;
    border-radius: 4px;
    width: 16px;
    height: 16px;
    margin-right: 12px;
  }
}
