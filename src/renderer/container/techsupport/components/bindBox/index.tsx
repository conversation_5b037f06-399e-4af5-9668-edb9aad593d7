import React, { useState } from 'react';
import styles from './index.module.less';
import { <PERSON><PERSON><PERSON>, NgFormItem } from '@/renderer/uiComponent/NgForm';
import { NgInput, NgInputPassword } from '@/renderer/uiComponent/NgInput';
import NgDarkButton from '@/renderer/uiComponent/NgDarkButton';
import { Form } from 'antd';
import { Rule } from 'antd/es/form';
import { useAsyncEffect } from 'ahooks';
import { getM200ApiInstance } from '@/common/api/ngApiAgent';
import NgMessage from '@/renderer/uiComponent/NgMessage';
import { IBoxInfo } from '@/common/types';
import NgPopover from '@/renderer/uiComponent/NgPopover';
// import { SelectedSuccess, WarnMiddle } from '@/renderer/uiComponent/SvgGather';
import { WarnMiddle } from '@/renderer/uiComponent/SvgGather';
import { BindBoxProcessModal } from './bindBoxProcess';

type Props = {};
const notEmptyRules: Rule[] = [
  {
    required: true,
  },
];
const BindBox = (props: Props) => {
  const { error: messageError, success: messageSuccess, contextHolder } = NgMessage.useMessage();
  const m200Api = getM200ApiInstance();
  const [isBind, setIsBind] = useState(false);
  const [boxInfo, setBoxInfo] = useState<IBoxInfo>();
  const [openConfirm, setOpenConfirm] = useState(false);
  const [form] = Form.useForm();
  const [showConnectMsg, setShowConnectMsg] = useState(false);
  // const [syncTask, setSyncTask] = useState<boolean>(true);
  const [processVisible, setProcessVisible] = useState<boolean>(false);

  const handleSaveBox = async (): Promise<any> => {
    try {
      await form.validateFields();
      // 临时，等待接口和方案明确之后再调整
      // setProcessVisible(syncTask);
      let formValue = await form.getFieldsValue();
      let bindResult = await m200Api.bindingBox(formValue);
      if (bindResult?.box_host) {
        setIsBind(true);
        setBoxInfo(bindResult);
        setShowConnectMsg(false);
      }
    } catch (e: any) {
      setShowConnectMsg(false);
      if (e?.errorFields?.length > 0) {
        return;
      }
      if (e?.code === '20408') {
        form.setFields([
          {
            name: 'box_host',
            errors: ['IP地址错误'],
          },
        ]);

        return;
      } else if (e?.code === '20403') {
        form.setFields([
          {
            name: 'password',
            errors: ['账号或密码错误'],
          },
          {
            name: 'username',
            errors: ['账号或密码错误'],
          },
        ]);

        return;
      } else {
        setShowConnectMsg(true);
      }
    }
  };
  useAsyncEffect(async () => {
    let res = await m200Api.getBindStateInfo();
    if (Object.keys(res).length <= 0) {
      setIsBind(false);
    } else {
      setIsBind(true);
      setBoxInfo(res);
    }
  }, []);

  return (
    <div className={styles.bindBoxContainer}>
      {contextHolder}
      <span>工作站服务 {showConnectMsg && <span>连接失败</span>}</span>
      {isBind ? (
        <div className={styles.unbinding}>
          <p>IP地址：{boxInfo?.box_host}</p>
          <NgPopover
            rootClassName={styles.confirmContainer}
            open={openConfirm}
            showOperation={{
              onOk: async (): Promise<any> => {
                let res = await m200Api.unbindingBox();
                if (Object.keys(res).length <= 0) {
                  form.resetFields();
                  setIsBind(false);
                  setBoxInfo({});
                  setOpenConfirm(false);

                  return messageSuccess({
                    content: '解绑成功',
                  });
                }

                return messageError({
                  content: '解绑失败',
                });
              },
              onCancel: () => {
                setOpenConfirm(false);
              },
            }}
            content={
              <div className={styles.optContainer}>
                <WarnMiddle />
                <span>是否解绑</span>
              </div>
            }
            trigger={'click'}
          >
            <NgDarkButton className={styles.unbindBtn} onClick={() => setOpenConfirm(true)}>
              解绑
            </NgDarkButton>
          </NgPopover>
        </div>
      ) : (
        <div className={styles.boxForm_content}>
          <NgForm form={form} className={styles.boxForm}>
            <NgFormItem rules={notEmptyRules} label={'IP地址'} name={'box_host'}>
              <NgInput />
            </NgFormItem>
            <NgFormItem rules={notEmptyRules} label={'账号'} name={'username'}>
              <NgInput />
            </NgFormItem>
            <NgFormItem rules={notEmptyRules} label={'密码'} name={'password'}>
              <NgInputPassword />
            </NgFormItem>
          </NgForm>
          {/* <div className={styles.syncTask}>
            {syncTask ? (
              <div
                className={styles.selected}
                onClick={() => {
                  setSyncTask(false);
                }}
              >
                <SelectedSuccess />
              </div>
            ) : (
              <div
                className={styles.no_selected}
                onClick={() => {
                  setSyncTask(true);
                }}
              />
            )}
            <div>同步所有治疗任务</div>
          </div> */}
          <NgDarkButton className={styles.saveBox} onClick={handleSaveBox}>
            保存
          </NgDarkButton>
        </div>
      )}
      <BindBoxProcessModal
        visible={processVisible}
        onClose={() => {
          setProcessVisible(false);
        }}
      />
    </div>
  );
};
export default BindBox;
