import React from 'react';
import styles from '../components/cooling/index.module.less';
import moment from 'moment';
import { isNumber } from 'lodash';

type ListType = {
  label: string;
  value: string;
  defaultValue?: string | any;
  render?(value: any, record?: any): any;
  suffix?: string | any;
  prefix?: string | any;
};

const coolingRange = {
  min: 3,
  max: 50,
};

const renderLiquidLevel = (text: number) => {
  if (text === 1) {
    return '液位缺油';
  } else if (text === 2) {
    return '中等液位';
  } else if (text === 3) {
    return '液位充足';
  } else {
    return '液位未知';
  }
};

const renderWaterRate = (text: number) => {
  let status = '异常';

  if (text >= coolingRange.min && text <= coolingRange.max) {
    status = '正常';
  }

  return (
    <div style={{ display: 'inline-block' }}>
      {status} <span style={{ display: 'inline-block', width: '60px', margin: '0px' }}>{text}ml/秒</span>（合理范围：
      {coolingRange.min}~{coolingRange.max} ml/秒）
    </div>
  );
};

const renderCoilLife = (text: number) => {
  const totalNum = 30000000;

  return !isNaN(text) ? <>{`${text.toLocaleString('en-US')}/${totalNum.toLocaleString('en-US')}`}</> : '--';
};

const renderVersion = (text: string, record?: any) => {
  return `${text}(${record.commit})`;
};

const renderWaterFan = (text: number) => {
  return (
    <div className={styles.fan_val}>
      {new Array(6).fill(true).map((v, i) => {
        return (
          <p key={i} className={styles.fan}>
            风扇{i + 1}：{((1 << (8 + i)) & (text || 0xffff)) === 0 ? '正常' : `${!text ? '--' : '故障'}`}
          </p>
        );
      })}
    </div>
  );
};

const renderEndDate = (text: string) => {
  return moment(text).add(5, 'years').format('YYYY-MM-DD');
};

const wrapRender = (val: any, cb: (value: any, record?: any) => any, record?: any) => {
  if (val === undefined) return '--';

  return cb(val, record);
};

export const renderCoilList: ListType[] = [
  {
    label: '线圈类型：',
    value: 'type',
  },
  {
    label: '固件标识号：',
    value: 'software_version',
    render: (text: string, record?: any) => wrapRender(text, renderVersion, record),
  },
  {
    label: '序列号：',
    value: 'sn',
  },
  {
    label: '到期时间：',
    value: 'production_date',
    render: (text: string) => wrapRender(text, renderEndDate),
  },
  {
    label: '线圈寿命：',
    value: 'life',
    render: (text: number) => wrapRender(text, renderCoilLife),
  },
  {
    label: '温度传感器A1：',
    value: 'temp_0',
    render: (text: number) => (isNumber(text) ? `${text}°C` : '--'),
    suffix: '°',
  },
  {
    label: '温度传感器A2：',
    value: 'temp_1',
    render: (text: number) => (isNumber(text) ? `${text}°C` : '--'),
    suffix: '°',
  },
  {
    label: '温度传感器B1：',
    value: 'temp_2',
    render: (text: number) => (isNumber(text) ? `${text}°C` : '--'),
    suffix: '°',
  },
  {
    label: '温度传感器B2：',
    value: 'temp_3',
    render: (text: number) => (isNumber(text) ? `${text}°C` : '--'),
    suffix: '°',
  },
];

export const renderCoolList: ListType[] = [
  {
    label: '固件标识号：',
    value: 'version',
  },
  {
    label: '液位：',
    value: 'liquid_level',
    render: (text: any) => wrapRender(text, renderLiquidLevel),
  },
  {
    label: '流速：',
    value: 'water_pump_flow_rate',
    render: (text: any) => wrapRender(text, renderWaterRate),
  },
  {
    label: '温度传感器A（入水口）：',
    value: 'inlet_temperature',
    suffix: '°',
    render: (text: number) => (isNumber(text) ? `${text.toFixed(1)}°C` : '--'),
  },
  {
    label: '温度传感器B（出水口）：',
    value: 'outlet_temperature',
    suffix: '°',
    render: (text: number) => (isNumber(text) ? `${text.toFixed(1)}°C` : '--'),
  },
  {
    label: '温度传感器C（散热片）：',
    value: 'radiator_temperature',
    suffix: '°',
    render: (text: number) => (isNumber(text) ? `${text.toFixed(1)}°C` : '--'),
  },
  {
    label: '温度传感器D（环境）：',
    value: 'ambient_temperature',
    suffix: '°',
    render: (text: number) => (isNumber(text) ? `${text.toFixed(1)}°C` : '--'),
  },
  {
    label: '湿度：',
    value: 'environmental_humidity',
    suffix: '%Rh',
    render: (text: number) => (isNumber(text) ? `${text.toFixed(1)}%Rh` : '--'),
  },
  {
    label: '',
    value: 'psd',
    render: (text: number) => renderWaterFan(text),
  },
];

export const getDropdownStaticList = (cb: any) => [
  {
    key: -1,
    label: <a onClick={cb}>上传数据</a>,
  },
];

export const importPatientInfo = ['name', 'code', 'sex', 'birth_date', 'condition_desc', 'pinyin_username', 'id'];
