import React, { useEffect } from 'react';
import styles from './index.module.less';
import { useRecoilState } from 'recoil';
import { faultAtom, getFaultByKey } from '@/renderer/recoil/fault';
import { FaultKeyEnum } from '@/common/systemFault/type';

const Power = () => {
  const [fault] = useRecoilState(faultAtom);
  const [info, setInfo] = React.useState({ hardware_version: '--', software_version: '--', sn: '--' });

  /**
   * 获取pdu信息
   */
  const getPduDetail = async () => {
    const isNotConnectPower = !!getFaultByKey(FaultKeyEnum.A020001);
    if (isNotConnectPower) {
      setInfo({ hardware_version: '--', software_version: '--', sn: '--' });

      return;
    }
    let res = await window.systemAPI.getPduInfo();
    if (res) {
      setInfo(res);
    }
  };

  useEffect(() => {
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    getPduDetail();
  }, [fault]);

  return (
    <div className={styles.container}>
      <span className={styles.title}>电源管理</span>
      <div className={styles.content}>
        <div className={styles.item}>
          <span className={styles.label}>固件标识号</span>:<span className={styles.value}>{info.software_version}</span>
        </div>
      </div>
    </div>
  );
};
export default Power;
