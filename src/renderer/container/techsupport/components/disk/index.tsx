import React from 'react';
import { useMount } from 'ahooks';
import styles from './index.module.less';
import { NgProgress } from '@/renderer/uiComponent/NgProgress';
import NgDarkButton from '@/renderer/uiComponent/NgDarkButton';
import NgModal from '@/renderer/uiComponent/NgModal';
import axios from 'axios';
import { CHECK_SMART_BASEURL } from '../../../../utils/renderSetting';
import { getMetricsValue, initMetricsInfo } from '../../../../../main/smart/utils';

type Props = {};
type capacityType = {
  name: string;
  fileSystemAvail: number;
  fileSystemSize: number;
  fileSystemUsed: number;
  mountpoint: string;
};
type statusType = {
  name: string;
  type: string;
  status: string;
};
type smartType = {
  attribute_name: string;
  attribute_value_type: string;
  device: string;
  value: number;
};
const Disk = (props: Props) => {
  const [status, setStatus] = React.useState<any[]>([]);
  const [capacities, setCapacities] = React.useState<any[]>([]);
  const [visibleSmart, setVisibleSmart] = React.useState(false);
  const [smartList, setSmartList] = React.useState<smartType[]>([]);
  useMount(async () => {
    let res = await window.systemAPI.getDiskDetailSmart();
    if (res && Array.isArray(res)) {
      setStatus(res.filter((v: statusType) => v.type === 'sat'));
    }
    const capacity = await window.systemAPI.checkDiskCapacity();
    if (capacity && Array.isArray(capacity)) {
      setCapacities(capacity);
    }
    await window.systemAPI.checkDisk80Percent();
  });

  // eslint-disable-next-line @typescript-eslint/no-shadow
  const calPercent = (capacities: capacityType[], mountpoint: string) => {
    const target = capacities.find(v => v.mountpoint === mountpoint);
    if (!target) return 0;

    return Math.round((target.fileSystemUsed / target.fileSystemSize) * 100);
  };

  const getStatus = (diskStatus: any) => {
    if (diskStatus === 'success') {
      return '正常';
    } else {
      return '异常';
    }
  };
  const onCancel = () => {
    setVisibleSmart(false);
  };
  const handleShowSmartInfo = async () => {
    try {
      const smartURL = CHECK_SMART_BASEURL();
      const smartInfo = initMetricsInfo(await axios.get(`${smartURL}/metrics`));
      const tempList = getMetricsValue('smartctl_device_attribute', smartInfo);
      let list = tempList.map(v => {
        return {
          attribute_name: v.conditions.attribute_name,
          attribute_value_type: v.conditions.attribute_value_type,
          device: v.conditions.device,
          value: v.value,
        };
      });
      setSmartList(list);
      setVisibleSmart(true);
    } catch (e) {
      //
    }
  };

  return (
    <div className={styles.container}>
      <span className={styles.title}>磁盘信息</span>
      <div className={styles.content}>
        <div className={styles.disk_card}>
          <span className={styles.status_area}>
            {status?.length ? (
              status.map((item: statusType, index) => {
                return (
                  <span className={styles.status} key={item.name}>
                    <span className={styles.label}>磁盘{index + 1}&nbsp;:</span>
                    <span className={styles.value}>{getStatus(item.status)}</span>
                  </span>
                );
              })
            ) : (
              <span className={styles.status_error_tip}>服务异常，获取不到磁盘信息</span>
            )}
          </span>
          <span className={styles.capacity_area}>
            <span className={styles.capacity}>
              <div className={styles.label}>数据磁盘总容量</div>
              <div className={styles.value}>
                <NgProgress percent={calPercent(capacities, '/data')} type={'line'} />
              </div>
            </span>
            <span className={styles.capacity}>
              <div className={styles.label}>系统磁盘总容量</div>
              <div className={styles.value}>
                <NgProgress percent={calPercent(capacities, '/')} type={'line'} />
              </div>
            </span>
          </span>
        </div>
      </div>
      <NgDarkButton className={styles.info_btn} onClick={handleShowSmartInfo}>
        S.M.A.R.T信息
      </NgDarkButton>
      <NgModal title={'S.M.A.R.T.信息'} open={visibleSmart} width={916} closable footer={<> </>} onCancel={onCancel}>
        <div className={styles.smart_content}>
          {smartList.map((item: smartType) => {
            return (
              // eslint-disable-next-line react/jsx-key
              <p>{`attribute_name:${item.attribute_name},attribute_value_type:${item.attribute_value_type}, device:${item.device},value: ${item.value}`}</p>
            );
          })}
        </div>
      </NgModal>
    </div>
  );
};
export default Disk;
