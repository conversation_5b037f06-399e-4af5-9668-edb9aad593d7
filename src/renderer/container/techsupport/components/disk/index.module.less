@import '@/renderer/static/style/baseColor.module.less';

.container {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  font-size: 14px;
  background-color: @colorA3;
  margin-top: 12px;
  padding: 24px;
  border-radius: 8px;
  height: 300px;

  .title {
    display: flex;
    justify-content: space-between;
    font-weight: 600 !important;
    margin-bottom: 12px;
  }

  .info_btn {
    margin-left: auto;
    margin-bottom: 0px;
  }

  .content {
    flex-grow: 1;
    font-weight: 350;
    color: @colorA10;

    .disk_card {
      flex-grow: 1;
      display: flex;
      flex-direction: column;

      .status_area {
        display: flex;
        flex-direction: column;

        .status_error_tip {
          margin-top: 12px;
          margin-bottom: 10px;
        }

        .status {
          width: 100%;
          margin-bottom: 10px;
          .value {
            margin-left: 8px;
          }
        }
      }
      .capacity_area {
        width: 100%;
        display: inline-flex;
        flex-direction: column;
        .capacity {
          width: 100%;
          display: inline-flex;
          flex-direction: column;
          margin-bottom: 10px;
          .label {
            width: 100%;
          }

          .value {
            width: 100%;
            height: 36px;
          }
        }
      }
    }
  }
}

.smart_content {
  height: 500px;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 6px;
  }
  &::-webkit-scrollbar-thumb {
    background: @colorA6;
    border-radius: 10px;
  }
  &::-webkit-scrollbar-thumb:vertical {
    width: 10px;
    height: 10px;
  }
}
