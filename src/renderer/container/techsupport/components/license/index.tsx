import React, { useMemo, useState } from 'react';
import styles from './index.module.less';
import NgDarkButton from '@/renderer/uiComponent/NgDarkButton';
import NgModal from '@/renderer/uiComponent/NgModal';
import { ReactComponent as Warning } from '@/renderer/static/svg/warning.svg';
import { useRecoilState, useRecoilValue } from 'recoil';
import { osUserInfo } from '@/renderer/recoil/osUserInfo';
import NgSelectFileModal, { UploadType } from '@/renderer/uiComponent/NgSelectFileModal';
import { useIntl } from 'react-intl';
import axios, { AxiosRequestConfig } from 'axios';
import NgMessage from '@/renderer/uiComponent/NgMessage';
import { useLicenseAtom } from '@/renderer/recoil/license';
import { UserSessionProps, withUserSession } from '@/renderer/hocComponent/withUserSession';
import moment from 'moment';
import { REACT_APP_NG_API_BASEURL } from '../../../../utils/renderSetting';

type Props = UserSessionProps;

export enum DeviceType {
  linux = 1,
  mac = 2,
}
const License = (props: Props) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [isShowMessage, setIsShowMessage] = useState<boolean>(false);
  const [isImport, setIsImport] = useState<boolean>();
  const [license, setLicense] = useRecoilState(useLicenseAtom);
  const { contextHolder, error: messageError, success } = NgMessage.useMessage();
  const intl = useIntl();
  const osInfo = useRecoilValue(osUserInfo);
  const deviceType: DeviceType = /macintosh|mac os x/i.test(navigator.userAgent) ? 2 : 1;

  const handleClickButton = async (type: UploadType) => {
    await checkFile();
    setIsImport(type === UploadType.uplaod);
    setModalVisible(true);
  };

  // @ts-ignore 导出license
  const handleExportLicense = async (path: string) => {
    try {
      let config: AxiosRequestConfig = {
        method: 'get',
        baseURL: REACT_APP_NG_API_BASEURL(),
        responseType: 'arraybuffer',
        headers: {
          Authorization: `Bearer ${props.userSession?.jwtToken}`,
          'Content-Type': 'application/json; charset=UTF-8',
          Accept: 'application/json; charset=UTF-8',
        },
      };

      let res = await axios.get('/pro/license/export', config);
      let fileName = res.headers['content-disposition'].match(/(?<=filename=).*/g)[0] || 'license.lic';
      let isWriteSuccess = await window.fileAPI.writeFileToDisk(`${path}/${fileName}`, res.data);
      if (isWriteSuccess) {
        if (isShowMessage) return;
        setIsShowMessage(true);

        return success({
          content: intl.formatMessage({ id: '操作成功' }),
          onClose: () => setIsShowMessage(false),
        });
      }
    } catch (error) {
      if (isShowMessage) return;
      setIsShowMessage(true);

      return messageError({
        content: intl.formatMessage({ id: '操作失败' }),
        onClose: () => setIsShowMessage(false),
      });
    } finally {
      setModalVisible(false);
    }
  };

  const handleImportLicense = async (path: string) => {
    // NOSONAR
    setModalVisible(false);
    const { hide } = NgModal.confirm({
      content: <>{intl.formatMessage({ id: '是否确认导入许可认证文件？' })}</>,
      headerIcon: <Warning />,
      closable: false,
      okText: intl.formatMessage({ id: '确定' }),
      cancelText: intl.formatMessage({ id: '否' }),
      onOk: async () => {
        // setUploading(true);
        try {
          await checkFile();
          let formData = new FormData();
          let result = await fetch(`file://${path}`)
            .then(async r => r.arrayBuffer())
            .then(r => {
              return r;
            });
          formData.append('file', new Blob([result]));

          let config: AxiosRequestConfig = {
            method: 'post',
            baseURL: REACT_APP_NG_API_BASEURL(),
            headers: {
              Authorization: `Bearer ${props.userSession?.jwtToken}`,
              Accept: '*/*',
              'Content-Type': 'multipart/form-data;',
            },
          };

          let res = await axios.post('/pro/license/import', formData, config);
          setLicense(res.data);
          if (isShowMessage) return;
          setIsShowMessage(true);

          return success({
            content: intl.formatMessage({ id: '操作成功' }),
            onClose: () => setIsShowMessage(false),
          });
        } catch (err: any) {
          // @ts-ignore
          if (err?.message !== '未检测到移动设备') {
            if (isShowMessage) return;
            setIsShowMessage(true);

            return messageError({
              content: intl.formatMessage({ id: '操作失败' }),
              onClose: () => setIsShowMessage(false),
            });
          }

          return;
        } finally {
          hide();
          // setUploading(false);
        }
      },
    });
  };

  const noFileMessage = () => {
    if (!isShowMessage) {
      setIsShowMessage(true);
      // eslint-disable-next-line no-void, @typescript-eslint/no-floating-promises
      messageError({
        content: intl.formatMessage({ id: '未检测到移动设备' }),
        onClose: () => setIsShowMessage(false),
      });
    }
  };

  const checkFile = async () => {
    try {
      const list = await window.fileAPI.getFolderInfo(deviceType === DeviceType.mac ? '/' : osInfo.filePath!);
      if (list.length === 0) throw Error('未检测到移动设备');
    } catch (error) {
      if (!isShowMessage) {
        setIsShowMessage(true);
        // eslint-disable-next-line no-void, @typescript-eslint/no-floating-promises
        messageError({
          content: intl.formatMessage({ id: '未检测到移动设备' }),
          onClose: () => setIsShowMessage(false),
        });
      }

      throw Error('未检测到移动设备');
    }
  };

  const authInfoTile = useMemo(() => {
    if (license.simple_status) {
      const status = +license.simple_status;
      if (status === 99) {
        return intl.formatMessage({ id: '许可异常' });
      } else if (status === 100) {
        return intl.formatMessage({ id: '试用许可正常' });
      } else {
        return intl.formatMessage({ id: '许可正常' });
      }
    }

    return '';
  }, [license]);

  return (
    <div className={styles.container}>
      {contextHolder}
      <span className={styles.title}>许可证</span>
      <div className={styles.content}>
        <span className={styles.item}>
          <span className={styles.label}>认证信息：</span>
          <span className={styles.value}>{authInfoTile || '--'}</span>
        </span>
        <span className={styles.item}>
          <span className={styles.label}>到期时间：</span>
          <span className={styles.value}>{license.simple_end ? moment(license.simple_end).format('YYYY-MM-DD') : '--'}</span>
        </span>
      </div>
      <div className={styles.footer}>
        <NgDarkButton
          onClick={async () => {
            await handleClickButton(UploadType.uplaod);
          }}
        >
          导入许可
        </NgDarkButton>
        <NgDarkButton
          disabled={license.status !== 200}
          onClick={async () => {
            await handleClickButton(UploadType.download);
          }}
        >
          导出许可
        </NgDarkButton>
      </div>
      <NgSelectFileModal
        isLicense
        maskClosable={false}
        filepath={deviceType === DeviceType.mac ? '/' : osInfo?.filePath!}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        width={770}
        extList={['.lic']}
        uploadType={isImport ? UploadType.uplaod : UploadType.download}
        okText={!isImport && '存储'}
        handleError={noFileMessage}
        onOk={async files => {
          if (isImport) {
            await handleImportLicense(files[0].path);
          } else {
            await handleExportLicense(files[0].path);
          }
        }}
      />
    </div>
  );
};
export default withUserSession(License);
