@import '@/renderer/static/style/baseColor.module.less';

.container {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background-color: @colorA3;
  padding: 24px;
  margin-top: 12px;
  border-radius: 8px;
  height: 300px;
  font-size: 14px;

  .title {
    font-weight: 600;
    margin-bottom: 12px;
  }

  .content {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    color: @colorA10;

    .item {
      font-weight: 350;
      margin-bottom: 10px;
    }
  }
  .footer {
    text-align: right;
    .coilButtonError {
      position: absolute;
      top: 3px;
      right: 33px;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: @colorD3_start;
    }

    & > div {
      margin-left: 20px;
      display: inline-block;
    }
  }
}
