import React, { CSSProperties, useEffect, useRef, useState } from 'react';
import { useRecoilState, useRecoilValue, useSetRecoilState } from 'recoil';
import { useLicenseAtom } from '@/renderer/recoil/license';
import { useAsyncEffect } from 'ahooks';
import { CameraInfoType, PlanLogModel, PlanLogQueryModel, SimpleStatus, TmsInfoType } from '@/common/types';
import styles from './index.module.less';
import NgPopover from '@/renderer/uiComponent/NgPopover';
import ExportTreatmentReport from '@/renderer/container/manage/components/exportTreatmentReport';
import { useIntl } from 'react-intl';
import LogsModal from '@/renderer/container/manage/components/logsModal';
import NgDarkButton from '@/renderer/uiComponent/NgDarkButton';
import { DeviceType } from '@/renderer/container/previewPlan';
import { osUserInfo } from '@/renderer/recoil/osUserInfo';
import NgMessage from '@/renderer/uiComponent/NgMessage';
import { GIT_COMMIT_SHORT_SHA } from '@/renderer/utils/renderSetting';
import NgAlert from '../../../../uiComponent/NgAlert';
import { ReactComponent as MessageError } from '@/renderer/static/svg/errorMessage.svg';
import { NgForm, NgFormItem } from '../../../../uiComponent/NgForm';
import { NgInput, NgInputPassword } from '../../../../uiComponent/NgInput';
import { Dropdown, Form, MenuProps, message } from 'antd';
import NgModal from '../../../../uiComponent/NgModal';
import { tmsCoilAtom } from '../../../../recoil/tmsError';
import { faultAtom, getFaultLevel } from '../../../../recoil/fault';
import NgListItem from '../../../../uiComponent/NgListItem';
import { getLogsData } from '../../../manage/components/manageDevice';
import { getM200ApiInstance } from '../../../../../common/api/ngApiAgent';
import { ProductType, product } from '../../../../constant/product';
import { FaultTypeList, notNAVTypeList } from '../../../../../common/systemFault/config';
import { getFaultLevelLabel } from '../../../../../common/systemFault/utils';
import { EllipsisIcon, Eye, EyeSlash, YellowWarning } from '../../../../uiComponent/SvgGather';
import classNames from 'classnames';
import { Rule } from 'antd/es/form';
import { safetyBtnStatusEnum, useSafetyBtnStatus } from '../../../../recoil/safetyBtn';
import { getSystemFaultLogPath, sendRenderLog } from '../../../../utils/renderLogger';

type Props = {
  productInfo: ProductType | undefined;
  resetLoading(loading: boolean): void;
  cameraInfo?: CameraInfoType;
  tmsInfo?: TmsInfoType;
  classname?: CSSProperties;
};

const Description = (props: Props) => {
  const { productInfo, cameraInfo, tmsInfo } = props;
  const [openModal, setOpenModal] = useState(false);
  const [showQrCode, setShowQrCode] = useState<boolean>(false);
  const [isShowMessage, setIsShowMessage] = useState<boolean>(false);
  const [data, setData] = useState<PlanLogModel[]>([]);
  const [modalVisible, setModalVisible] = useState(false);
  const intl = useIntl();
  const deviceType: DeviceType = /macintosh|mac os x/i.test(navigator.userAgent) ? 2 : 1;
  const osInfo = useRecoilValue(osUserInfo);
  const [treatmentRecordOpen, setTreatmentRecordOpen] = useState(false);
  const [fault] = useRecoilState(faultAtom);
  const [license, setLicense] = useRecoilState(useLicenseAtom);
  const [showErrorMessage, setShowErrorMessage] = useState(false);
  const [serialNumber, setSerialNumber] = useState<string>('');
  const [isSerialModify, setIsSerialModify] = useState<boolean>(false);
  const qrCodeImgStreamRef = useRef<any>();
  const coilInfo = useRecoilValue(tmsCoilAtom);
  const setSafetyBtnStatus = useSetRecoilState(useSafetyBtnStatus);
  const coolingInfoRef = useRef<any>();
  const [showResetModal, setShowResetModal] = useState<boolean>(false);
  const paginationParams = useRef<PlanLogQueryModel & { total: number }>({
    page_num: 1,
    page_size: 10,
    total: 0,
  });
  const { contextHolder, error: messageError } = NgMessage.useMessage();
  const [form] = Form.useForm();
  const m200Api = getM200ApiInstance();
  const timerRef = React.useRef<NodeJS.Timeout | undefined>();
  const [boxErrorTip, setBoxErrorTip] = useState<string>('');

  /**
   * 关机
   */
  const sendShutdown = () => {
    timerRef.current = setTimeout(() => {
      setSafetyBtnStatus(safetyBtnStatusEnum.error);
    }, 60000);
    window.systemAPI.shutdown();
  };

  /**
   * 序列号输入验证
   */
  const validateInputSerial = async (_: any, value: string) => {
    return new Promise((resolve, reject) => {
      if (!value?.length) {
        reject('序列号不可为空');
      }
      resolve('');
    });
  };

  const handleClose = (isError: boolean) => {
    if (isError) {
      setShowErrorMessage(isError);
    }
  };

  /**
   * 获取二维码图片流
   */
  const getQrImageStream = async (coolingData: any): Promise<string> => {
    const tunnel_info = await window.tmsAPI.get_tunnel_info();
    const params = {
      product_name: '磁刺激仪',
      version_code: productInfo?.version,
      model_code: productInfo?.name,
      serial_number: serialNumber,
      desktop: {
        git_id: GIT_COMMIT_SHORT_SHA(),
      },
      gateway: {
        version: tunnel_info.data.version,
        git_id: tunnel_info.data.git_log,
      },
      slbs: {
        version: cameraInfo?.version || '',
        git_id: cameraInfo?.commit || '',
        serial_number: cameraInfo?.cameraSerialNum || '',
      },
      tms: {
        firmware_version: tmsInfo?.firmware_version || '', // 固件版本
        git_id: tmsInfo?.commit || '', // 固件gitid
        voltage_calibration: tmsInfo?.voltage_calibration, // 是否校准：0未校准，1校准
        voltageB: tmsInfo?.voltageB, // 斜率
        voltageK: tmsInfo?.voltageK, // 截距
      },
      coil: {
        firmware_version: coilInfo.software_version || '', // 固件版本
        git_id: coilInfo.commit, // 固件gitid
        serial_number: coilInfo.sn, // 生产序列号
        type: coilInfo.type, // 线圈类型
        production_date: coilInfo.production_date, // 线圈生产时间(时间戳)
        life: coilInfo.life, // 线圈寿命
      },
      cooling: {
        firmware_version: coolingData.data.version || '', // 固件版本
        liquid_level: coolingData.data.liquid_level || '', // 液位
        water_pump_flow_rate: coolingData.data.water_pump_flow_rate || '', // 流速
      },
    };

    return await m200Api.makeQrCode(params);
  };

  // 证书是否过期或异常
  const isLicenseExpire = (status: SimpleStatus) => {
    const trialList = [SimpleStatus.The99];
    // 如果状态是99代表证书异常
    if (trialList.includes(status)) {
      return true;
    }

    return false;
  };

  const handleSaveSerialNum = async () => {
    const formValue = await form.getFieldsValue();
    if (!formValue.serialNumber.length) return;
    try {
      const isSuccess = await m200Api.saveSerialNumber(formValue.serialNumber);
      if (isSuccess) {
        const serialNum = await m200Api.getSerialNumber();
        setIsSerialModify(false);
        setSerialNumber(serialNum);
      }
    } catch (error) {
      await message.error('操作失败');
    }
  };

  const showQrCodeImage = async () => {
    const imgBase64 = await getQrImageStream(coolingInfoRef.current);
    if (!imgBase64.length) return;
    qrCodeImgStreamRef.current = imgBase64;
    setShowQrCode(true);
  };

  /**
   * 修改序列号
   */
  const modifyBtnSerialNum = () => {
    setIsSerialModify(true);
    form.setFieldValue('serialNumber', serialNumber);
  };

  useAsyncEffect(async () => {
    let res = await getLogsData(paginationParams.current);
    setData(res.records);
    paginationParams.current = {
      ...paginationParams.current,
      total: res.total || 0,
    };
    await window.tmsAPI.water_cooling_query_by_key('water_query', async (_, coolData) => {
      coolingInfoRef.current = coolData;
    });
    if (Object.keys(license).length === 0) {
      let licenseData = await m200Api.getLicense();
      setLicense({
        ...licenseData,
        hasLicenseError: isLicenseExpire(licenseData.simple_status!),
      });
    }
    const serialNum = await m200Api.getSerialNumber();
    setSerialNumber(serialNum);
  }, []);

  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, []);

  /**
   * 密码的校验规则
   */
  const rules: Rule[] = [{ required: true, message: intl.formatMessage({ id: '密码不可为空' }) }];

  /**
   * 恢复出厂设置
   */
  const resetFactory = async (password: string) => {
    setShowResetModal(false);
    props.resetLoading(true);
    setBoxErrorTip('');
    try {
      await m200Api.resetFactory({ validate_only: false, password: password });
      window.fileAPI.setBatData('coil_id_empty');
      await deleteSystemFaultLog();
      props.resetLoading(false);
      sendShutdown();
    } catch (error: any) {
      props.resetLoading(false);
      // 进行关机失败弹窗
      setSafetyBtnStatus(safetyBtnStatusEnum.error);
    }
  };

  /**
   * 删除故障log文件
   */
  const deleteSystemFaultLog = async () => {
    await window.fileAPI.removeFile(await getSystemFaultLogPath());
  };

  /**
   * 验证是否可进行恢复操作
   */
  const validateFactory = async () => {
    const formValue = await form.getFieldsValue();
    if (!formValue.password || !formValue.password.length) {
      form.setFields([
        {
          name: 'password',
          errors: [intl.formatMessage({ id: '密码不可为空' })],
        },
      ]);

      return;
    }

    try {
      await m200Api.resetFactory({ validate_only: true, password: formValue.password });
      await resetFactory(formValue.password);
    } catch (error: any) {
      setBoxErrorTip('');
      if (error.code === 'SU10107') {
        form.setFields([
          {
            name: 'password',
            errors: [intl.formatMessage({ id: error.message })],
          },
        ]);
      } else {
        setBoxErrorTip(error.message);
      }
      sendRenderLog.info(`恢复出厂设备失败：${error}`);
    }
  };

  /**
   * 获取更多操作的menu
   */
  const getMoreHandleMenu = () => {
    const moreHandleMenuItems: MenuProps['items'] = [
      {
        key: '1',
        label: <a onClick={modifyBtnSerialNum}>{intl.formatMessage({ id: '修改序列号' })}</a>,
      },
      {
        key: '2',
        label: (
          <a
            onClick={() => {
              setShowResetModal(true);
            }}
          >
            {intl.formatMessage({ id: '恢复出厂设置' })}
          </a>
        ),
      },
    ];

    return serialNumber.length > 0 && !isSerialModify ? moreHandleMenuItems : moreHandleMenuItems.filter(item => item?.key !== '1');
  };

  return (
    <div className={styles.container}>
      {contextHolder}
      <p>产品名称：磁刺激仪</p>
      <p> 系统状态：{getFaultLevelLabel(getFaultLevel(product.isNav ? FaultTypeList : notNAVTypeList, fault))}</p>
      <p>{`产品型号：${props.productInfo?.name ? `M${props.productInfo?.name}` : '--'}`}</p>
      <p>{`标识号：${props.productInfo?.version || '--'}(${GIT_COMMIT_SHORT_SHA()})`}</p>

      {!isSerialModify && serialNumber.length > 0 ? (
        <div className={styles.showSeriralContent}>
          <span style={{ width: '56px' }}>序列号：</span>
          <NgListItem key={'serialNumber'} title={`${serialNumber}`} maxWidth={234} fontSize={14} className={styles.ngListItem} />
        </div>
      ) : (
        <div className={styles.editSeriralContent}>
          <NgForm form={form} className={styles.serialForm}>
            <NgFormItem validateTrigger={['onBlur']} rules={[{ validator: validateInputSerial }]} label={'序列号'} name="serialNumber">
              <NgInput maxLength={50} />
            </NgFormItem>
          </NgForm>
          <NgDarkButton className={styles.serialSave} onClick={handleSaveSerialNum}>
            保存
          </NgDarkButton>
          <NgDarkButton onClick={() => setIsSerialModify(false)}>取消</NgDarkButton>
        </div>
      )}

      <div className={styles.btnContainer}>
        <NgDarkButton
          onClick={() => {
            setTreatmentRecordOpen(false);
            setOpenModal(true);
          }}
        >
          日志记录
        </NgDarkButton>
        <NgPopover
          overlayClassName={styles.treatmentRecord}
          open={treatmentRecordOpen}
          placement={'bottom'}
          content={<ExportTreatmentReport onClose={handleClose} modalVisible={modalVisible} setTreatmentPopOverOpen={setTreatmentRecordOpen} />}
        >
          <div>
            <NgDarkButton
              onClick={async () => {
                try {
                  const list = await window.fileAPI.getFolderInfo(deviceType === DeviceType.mac ? '/' : osInfo.filePath!);
                  if (list.length === 0) {
                    throw new Error('');
                  }
                } catch (e) {
                  if (isShowMessage) return;
                  setIsShowMessage(true);
                  // eslint-disable-next-line @typescript-eslint/no-floating-promises
                  messageError({
                    content: intl.formatMessage({ id: '未检测到移动设备' }),
                    onClose: () => setIsShowMessage(false),
                  });

                  return;
                }
                if (props.productInfo?.appImage === 'nonav-appimage') {
                  setModalVisible(true);
                } else {
                  setTreatmentRecordOpen(true);
                }
              }}
            >
              {intl.formatMessage({ id: '治疗记录' })}
            </NgDarkButton>
          </div>
        </NgPopover>
        {props.productInfo?.appImage === 'nonav-appimage' && (
          <ExportTreatmentReport
            notShowContent
            modalVisible={modalVisible}
            setModalVisible={setModalVisible}
            setTreatmentPopOverOpen={setTreatmentRecordOpen}
          />
        )}
        {serialNumber.length > 0 && !isSerialModify && (
          <NgDarkButton className={styles.qrCodeBtn} onClick={showQrCodeImage}>
            认证信息
          </NgDarkButton>
        )}
        <Dropdown menu={{ items: getMoreHandleMenu() }} overlayClassName={classNames(styles.moreHandleDrop)}>
          <div className={styles.moreHandleIcon}>
            <EllipsisIcon />
          </div>
        </Dropdown>
      </div>
      <LogsModal
        openModal={openModal}
        setOpenModal={setOpenModal}
        data={data}
        total={paginationParams.current.total}
        onPaginationChange={async (page_num: number, page_size: number) => {
          let res = await getLogsData({
            page_num,
            page_size,
          });
          paginationParams.current = {
            ...paginationParams.current,
            total: res.total || 0,
          };
          setData(res.records);
        }}
      />
      {showQrCode && (
        <NgModal
          title="认证信息"
          maskClosable={false}
          footer={<></>}
          open={showQrCode}
          onCancel={() => setShowQrCode(false)}
          width={860}
          className={styles.qrCodeModal}
        >
          <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', background: '#FFF', padding: '20px' }}>
            <img src={qrCodeImgStreamRef.current} alt="" style={{ width: '760px', height: '760px' }} />
          </div>
        </NgModal>
      )}
      {showErrorMessage && (
        <NgAlert
          className={styles.error_message_alert}
          icon={<MessageError />}
          showIcon
          message="操作失败"
          type="error"
          closable
          onClose={() => setShowErrorMessage(false)}
        />
      )}
      {showResetModal && (
        <NgModal
          okText={'关机'}
          maskClosable={false}
          open={showResetModal}
          closable={false}
          onCancel={() => {
            form.resetFields();
            setShowResetModal(false);
            setBoxErrorTip('');
          }}
          onOk={validateFactory}
        >
          <div className={styles.modalContent}>
            <div className={styles.contentLeft}>
              <YellowWarning />
            </div>

            <div className={styles.contentRight}>
              <div className={styles.textLab}>输入密码后，会清空设备所有数据，需要关机重启设备后生效</div>
              <div className={styles.pwTitle}>密码：</div>
              <NgForm form={form}>
                <NgFormItem key={'password'} validateTrigger={['onBlur']} name={'password'} rules={rules}>
                  <NgInputPassword
                    iconRender={visible => (visible ? <Eye /> : <EyeSlash />)}
                    type={'password'}
                    placeholder={intl.formatMessage({ id: '请输入密码' })}
                  />
                </NgFormItem>
              </NgForm>
              {boxErrorTip.length > 0 && <div className={styles.boxErrorTip}>{boxErrorTip}</div>}
            </div>
          </div>
        </NgModal>
      )}
    </div>
  );
};

export default Description;
