@import '@/renderer/static/style/baseColor.module.less';

.container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  position: relative;
  font-size: 14px;
  background-color: @colorA3;
  border-radius: 8px;
  padding: 24px;
  height: 270px;

  :global {
    .ant-dropdown-trigger {
      display: flex;
      align-items: center;
      margin: 4px 12px 0 0;
    }
  }

  > p {
    margin-top: 0px;
    margin-bottom: 10px;
    font-size: 14px;
  }

  > p:last-child {
    margin-bottom: 0px;
  }

  label {
    font-size: 14px !important;
    color: #ffffff !important;
  }

  .showSeriralContent {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    height: 32px;
    white-space: nowrap;
    font-size: 14px;

    span {
      flex-shrink: 0;
    }
  }

  .showSeriralContent .ngListItem {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-right: 15px;
    max-width: 234px;
  }

  .editSeriralContent {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    height: 32px;
    width: 100%;

    .serialForm {
      flex-grow: 1;
      margin-right: 18px;
    }
    .serialSave {
      margin-right: 6px;
    }

    :global {
      .ant-btn {
        width: 80px;
      }
    }
  }

  .btnContainer {
    position: absolute;
    bottom: 24px;
    right: 12px;
    display: flex;
    flex-direction: row-reverse;

    & > div:first-child {
      margin-left: 15px;
    }

    .qrCodeBtn {
      margin-right: 8px;
    }
  }
}

.hardwareIDRoot {
  :global {
    .ant-modal-mask {
      background-color: rgba(0, 0, 0, 0.98) !important;
    }
  }
}

.openHardwareID {
  :global {
    .ant-modal-content {
      display: flex;
    }

    .ant-modal-body {
      width: 100%;
      height: 100%;

      & > div:first-child {
        width: 100%;
        height: 100%;
        font-size: 30px;
        display: flex;
        justify-content: center;
        align-content: center;
      }
    }
  }
}

.error_message_alert {
  position: fixed;
  word-break: keep-all;
  top: 78px;
  left: 50%;
  z-index: 1;
  transform: translateX(-50%);
  :global {
    .ant-alert {
      padding: 12px !important;
    }
    .ant-alert-icon {
      margin-top: 4px;
      margin-right: 12px;
    }
    .ant-alert-message {
      font-size: 14px;
      line-height: 24px !important;
    }
    .ant-alert-close-icon {
      margin-top: 3px;
      margin-left: 18px;
    }
  }

  .qrCodeModal {
    display: flex;
  }
}

.moreHandleDrop {
  :global {
    .ant-dropdown-menu {
      background: @colorA4 !important;
      padding: 10px !important;
    }

    .ant-dropdown-menu-item {
      cursor: url('@/renderer/static/svg/cursor.cur'), pointer !important;
      font-family: normal-font -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif,
        'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
      text-align: center;
      border-radius: 6px !important;
      padding: 0 !important;

      &:hover {
        background: @colorC4 !important;
      }
      & a {
        cursor: url('@/renderer/static/svg/cursor.cur'), pointer !important;
        padding: 5px 12px;
      }
    }
    .ant-dropdown-menu-title-content > a {
      color: @colorA12 !important;
      position: relative;
      width: 100%;
      display: inline-block;
    }

    .ant-dropdown-menu-item-disabled .ant-dropdown-menu-title-content > a {
      color: @colorA9 !important;
    }
    .ant-dropdown-menu-item-disabled {
      cursor: url('@/renderer/static/svg/disableMouse.cur'), not-allowed !important;
    }
  }
}

.modalContent {
  display: flex;
  flex-direction: row;

  .contentLeft {
    padding-top: 4px;
    margin-right: 17px;
  }

  .contentRight {
    display: flex;
    flex-direction: column;

    .textLab {
      font-size: 16px;
      // width: 394px;
    }

    .boxErrorTip {
      color: @colorB3;
    }

    .pwTitle {
      margin-top: 16px;
      margin-bottom: 8px;
      font-size: 14px;
      color: @colorA9;
    }
  }
}

.moreHandleIcon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 24px;
  height: 24px;
  border-radius: 2px;

  &:hover {
    background-color: @colorA5;
    cursor: url('@/renderer/static/svg/cursor.cur'), pointer !important;
  }
}
