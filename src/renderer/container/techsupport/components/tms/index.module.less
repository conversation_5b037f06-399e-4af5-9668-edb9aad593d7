@import '@/renderer/static/style/baseColor.module.less';

.container {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  font-size: 14px;
  background-color: @colorA3;
  border-radius: 8px;
  padding: 24px;
  height: 270px;
  font-weight: 350;

  .title {
    display: flex;
    justify-content: space-between;
    font-weight: 600 !important;
    margin-bottom: 12px;
  }

  .content {
    flex-grow: 1;
    color: @colorA10;

    .item {
      display: flex;
      flex-direction: row;
      margin-bottom: 10px;
    }

    .item_list {
      margin: 0 30px 0 0;
      display: inline-block;
      width: 160px;
      white-space: nowrap;
    }
  }

  .footer {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;

    & > div {
      margin-left: 20px;
    }
  }
}

.moreHandleDrop {
  :global {
    .ant-dropdown-menu {
      background: @colorA4 !important;
      padding: 10px !important;
    }

    .ant-dropdown-menu-item {
      cursor: url('@/renderer/static/svg/cursor.cur'), pointer !important;
      font-family: normal-font -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON>o, 'Helvetica Neue', <PERSON><PERSON>, 'Noto Sans', sans-serif,
        'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
      text-align: center;
      border-radius: 6px !important;
      padding: 0 !important;

      &:hover {
        background: @colorC4 !important;
      }
      & a {
        cursor: url('@/renderer/static/svg/cursor.cur'), pointer !important;
        padding: 5px 12px;
      }
    }
    .ant-dropdown-menu-title-content > a {
      color: @colorA12 !important;
      position: relative;
      width: 100%;
      display: inline-block;
    }

    .ant-dropdown-menu-item-disabled .ant-dropdown-menu-title-content > a {
      color: @colorA9 !important;
    }
    .ant-dropdown-menu-item-disabled {
      cursor: url('@/renderer/static/svg/disableMouse.cur'), not-allowed !important;
    }
  }
}

.moreHandleIcon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 24px;
  height: 24px;
  border-radius: 2px;
  padding: 4px !important;

  &:hover {
    background-color: @colorA5;
    cursor: url('@/renderer/static/svg/cursor.cur'), pointer !important;
  }
}
