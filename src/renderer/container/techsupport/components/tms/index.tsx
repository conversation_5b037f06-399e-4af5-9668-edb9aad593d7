import React, { useState } from 'react';
import styles from './index.module.less';
import NgDarkButton from '@/renderer/uiComponent/NgDarkButton';
import { useNavigate } from 'react-router';
import { MeasurementStrength } from '@/renderer/container/home/<USER>/measurement';
import { useIntl } from 'react-intl';
import { useRecoilState, useSetRecoilState } from 'recoil';
import { useAsyncEffect } from 'ahooks';
import { ErrorModel } from '../../../../component/systemErrorModel/errorModel';
import { TMSScreenState } from '../../../../../common/constant/tms';
import { faultAtom, getFaultWithoutType } from '../../../../recoil/fault';
import { FaultEnum } from '../../../../../common/systemFault/type';
import { notNAVTypeList } from '../../../../../common/systemFault/config';
import { isNotTreatingAtom } from '../../../../recoil/isNotTreating';
import { TmsInfoType } from '../../../../../common/types';
import classNames from 'classnames';
import { Dropdown, MenuProps } from 'antd';
import { EllipsisIcon } from '../../../../uiComponent/SvgGather';
import { isNumber } from 'lodash';

type Props = {
  versionInfo?: TmsInfoType;
};

const Tms = (props: Props) => {
  const { versionInfo } = props;
  const intl = useIntl();
  const [fault] = useRecoilState(faultAtom);
  const [visible, setVisible] = useState(false);
  const navigate = useNavigate();
  const setIsNotTreating = useSetRecoilState(isNotTreatingAtom);

  const handleErrorModalOK = () => {
    setVisible(false);
  };

  const handleTemplateClick = () => {
    navigate('/stimulate');
  };

  const handleStimulateOnce = () => {
    setVisible(true);
  };

  const handleStimulateNormal = () => {
    navigate('/noPatientTms');
  };

  const initThreshold = async () => {
    window.tmsAPI.set_beat_screen(TMSScreenState.NotStarted);
    await window.tmsAPI.noImage_treatment_plan_start('SingleEnd');
  };

  const onCancelClick = async () => {
    setVisible(false);

    await initThreshold();
  };

  /**
   * MoreMenuEnum： menu选项
   * chargeDetail 充电箱详情;
   * repeatTreat: 连续刺激；
   */
  enum MoreMenuTypeEnum {
    chargeDetail = 0,
    repeatTreat = 1,
  }

  const getMoreHandleMenu = (cb: (menuType: MoreMenuTypeEnum) => void): MenuProps['items'] => {
    return [
      {
        key: '1',
        label: (
          <a
            onClick={() => {
              cb(MoreMenuTypeEnum.repeatTreat);
            }}
          >
            连续刺激
          </a>
        ),
      },
    ];
  };

  useAsyncEffect(async () => {
    setIsNotTreating(!visible);
  }, [visible]);

  useAsyncEffect(async () => {
    await initThreshold(); // 应对刷新, 取消只能放到外部
  }, []);

  return (
    <div className={styles.container}>
      <span className={styles.title}>TMS</span>
      <div className={styles.content}>
        <div className={styles.item}>
          <span className={styles.label}>固件标识号：</span>
          <span className={styles.value}>
            {versionInfo?.firmware_version.length ? `${versionInfo.firmware_version}（${versionInfo.commit}）` : '--'}
          </span>
        </div>
        <div className={styles.item}>
          <span className={styles.label}>电压校准：</span>
          <span className={styles.value}>
            {versionInfo?.voltageK
              ? `${versionInfo.voltage_calibration ? '已校准' : '未校准'}（斜率：${versionInfo.voltageK.toLocaleString(
                'en-US'
              )} 截距：${versionInfo.voltageB!.toLocaleString('en-US')}）`
              : '--'}
          </span>
        </div>
        <div className={styles.item}>
          <p className={styles.item_list}>
            <span className={styles.label}>电容放电次数 ：</span>
            <span className={styles.value}>{!isNumber(versionInfo?.treat_count) ? '--' : versionInfo?.treat_count.toLocaleString()}</span>
          </p>
          <p className={styles.item_list}>
            {<span className={styles.label}>序列号：</span>}
            <span className={styles.value}>{!versionInfo?.sn ? '--' : versionInfo?.sn}</span>
          </p>
        </div>
      </div>
      <div className={styles.footer}>
        <Dropdown
          menu={{
            items: getMoreHandleMenu(() => {
              navigate('/repeatTreat');
            }),
          }}
          overlayClassName={classNames(styles.moreHandleDrop)}
        >
          <div className={styles.moreHandleIcon}>
            <EllipsisIcon />
          </div>
        </Dropdown>
        <NgDarkButton onClick={handleTemplateClick}>脉冲模板</NgDarkButton>
        <NgDarkButton onClick={handleStimulateOnce} disabled={getFaultWithoutType(FaultEnum.imageFault, fault).length > 0}>
          单脉冲
        </NgDarkButton>
        <NgDarkButton onClick={handleStimulateNormal} disabled={getFaultWithoutType(FaultEnum.imageFault, fault).length > 0}>
          重复刺激
        </NgDarkButton>
      </div>
      {visible && (
        <MeasurementStrength
          onCancelClick={onCancelClick}
          onOkClick={onCancelClick}
          hasFault={!!getFaultWithoutType(FaultEnum.imageFault, fault).length}
          title={intl.formatMessage({ id: '单脉冲' })}
          contentLabel="实际强度(%MO)："
          wrapClassName={styles.tech_support_strength_modal}
        />
      )}
      {visible && <ErrorModel isStimulate faultTypeList={notNAVTypeList} onOk={handleErrorModalOK} onOpen={initThreshold} />}
    </div>
  );
};

export default Tms;
