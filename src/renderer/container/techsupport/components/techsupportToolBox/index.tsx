import React from 'react';
import styles from './index.module.less';
import { NgIcon } from '@/renderer/uiComponent/NgIcon';
import { LogoutOutlined } from '@ant-design/icons';
import { ShutDown } from '@/renderer/component/shutdown';
import { useNavigate } from 'react-router-dom';
type Props = {};
const TechSupportToolBox = (props: Props) => {
  const navigate = useNavigate();

  return (
    <div className={styles.toolbox}>
      <NgIcon
        // @ts-ignore
        iconSvg={LogoutOutlined}
        tooltip={{
          overlayClassName: styles.logout,
          title: '登出',
        }}
        onClick={() => navigate('/logout')}
        fontSize={25}
      />
      <ShutDown />
    </div>
  );
};
export default TechSupportToolBox;
