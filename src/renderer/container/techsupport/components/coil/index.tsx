import React, { useEffect, useState } from 'react';
import styles from './index.module.less';
import { useRecoilState, useRecoilValue } from 'recoil';
import { TmsCoil, tmsCoilAtom } from '@/renderer/recoil/tmsError';
import { renderCoilList } from '@/renderer/container/techsupport/components/config';
import { uniqueId } from 'lodash';
import { faultAtom, getFaultByKey } from '@/renderer/recoil/fault';
import { FaultKeyEnum } from '@/common/systemFault/type';

type Props = {};

const Coil = (props: Props) => {
  const coilInfo = useRecoilValue(tmsCoilAtom);
  const [fault] = useRecoilState(faultAtom);
  const [coilData, setCoilData] = useState<TmsCoil>(coilInfo);

  useEffect(() => {
    const isNotConnectCoil = !!getFaultByKey(FaultKeyEnum.A030001) || !!getFaultByKey(FaultKeyEnum.A040001);
    const data = isNotConnectCoil ? {} : coilInfo;

    setCoilData(data);
  }, [coilInfo, fault]);

  return (
    <div className={styles.container}>
      <span className={styles.title}>线圈</span>
      <div className={styles.content}>
        {renderCoilList.map(v => {
          return (
            <span key={uniqueId(v.value)} className={styles.item}>
              <span className={styles.label}>{v.label}</span>
              {v.render ? (
                v.render(coilData[v.value], coilData)
              ) : (
                <span className={styles.value}>{coilData[v.value] ? `${coilData[v.value]} ${v?.suffix || ''}` : '--'}</span>
              )}
            </span>
          );
        })}
      </div>
    </div>
  );
};
export default Coil;
