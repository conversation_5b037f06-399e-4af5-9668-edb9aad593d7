import { ColumnType } from 'antd/lib/table';
import moment from 'moment';
import React from 'react';
import { FaultMapItemType } from '../../../../../common/systemFault/type';
import { getFaultLevelLabel } from '../../../../../common/systemFault/utils';
export const columns: (isHistory?: boolean) => ColumnType<FaultMapItemType>[] = (isHistory = false) => [
  {
    key: 'errorLevel',
    dataIndex: 'errorLevel',
    title: '故障等级',
    render: value => getFaultLevelLabel(value),
  },
  {
    key: 'key',
    dataIndex: 'key',
    title: '代码',
  },
  {
    key: 'description',
    dataIndex: 'description',
    title: '描述文字',
    render: (value, record, index) => {
      return (
        <>
          {value}
          {(isHistory && record.suffix) || ''}
        </>
      );
    },
  },
  {
    key: 'createAt',
    dataIndex: 'createAt',
    title: '时间',
    render: (value, record, index) => {
      return <>{moment(record.createAt).format('YYYY-MM-DD HH:mm:ss')}</>;
    },
  },
];

