@import '@/renderer/static/style/baseColor.module.less';

.container {
  display: flex;
  flex-direction: column;
  height: 100%;

  .tableContainer {
    flex: 1;

    :global {
      .ant-table-container {
        user-select: none;
      }
      & .ant-table-tbody > tr > td {
        background-color: @colorA4 !important;
      }

      .ant-table-body {
        max-height: 820px !important;
        background: @colorA4;

        &::-webkit-scrollbar {
          background: @colorA4;
        }
      }

      .ant-table-placeholder {
        height: 820px;
        background: @colorA4;
      }
    }
  }

  .history {
    display: flex;
    justify-content: flex-end;
    // margin-bottom: 100px;
  }
}

.historyTable {
  :global {
    .ant-table-placeholder {
      height: 376px;
    }
    .ant-table-container {
      user-select: none;
    }
    // .ant-table-wrapper .ant-table-tbody > tr.ant-table-placeholder:hover > td {
    //   background-color: @colorA4_1 !important;
    // }
    .ant-table-wrapper .ant-table-tbody > tr.ant-table-row:hover > tr,
    .ant-table-wrapper .ant-table-tbody > tr.ant-table-row:hover > td {
      background-color: @colorA4_1 !important;
    }
    .ant-table-wrapper .ant-table-tbody > tr.ant-table-row > tr,
    .ant-table-wrapper .ant-table-tbody > tr.ant-table-row > td {
      background-color: @colorA4_1 !important;
    }
    .ant-table-pagination {
      position: absolute;
      bottom: -45px;
      right: 0;
    }

    & .ant-table-thead > tr > th {
      background-color: @colorA5 !important;
    }

    & .ant-table-row {
      background-color: @colorA4_1 !important;
    }

    & .ant-table-wrapper .ant-table-tbody > tr > td {
      height: 40px;
    }

    & .ant-table-cell-scrollbar {
      background-color: @colorA5 !important;
      border: 0 !important;
      box-shadow: @colorA5 0 1px 0 1px !important;
    }

    .ant-table-body {
      background-color: @colorA4_1 !important;

      &::-webkit-scrollbar {
        background: @colorA4_1;
      }
    }

    .ant-spin-container {
      height: 460px;
    }
  }
}
