import React, { useMemo, useState } from 'react';
import { useRecoilState } from 'recoil';
import NgTable from '@/renderer/uiComponent/NgTable';
import NgDarkButton from '@/renderer/uiComponent/NgDarkButton';
import { columns } from './config';
import NgModal from '@/renderer/uiComponent/NgModal';
import { uniqueId } from 'lodash';
import styles from './index.module.less';
import { ConfigProvider } from 'antd';
import NgEmpty from '@/renderer/uiComponent/NgEmpty';
import { product } from '../../../../constant/product';
import { FaultEnum, FaultLevelEnum, FaultMapItemType } from '../../../../../common/systemFault/type';
import { faultAtom } from '../../../../recoil/fault';

const Fault = () => {
  const [fault] = useRecoilState(faultAtom);
  const [openModal, setOpenModal] = useState(false);
  const [historyError, setHistoryError] = useState<FaultMapItemType[]>([]);
  const allInfo = useMemo(() => {
    let list = [...fault[FaultLevelEnum.error], ...fault[FaultLevelEnum.warning]];
    if (!product.isNav) {
      list = list.filter(v => v.type !== FaultEnum.imageFault);
    }
    // @ts-ignore
    list = list.sort((a, b) => new Date(b.createAt) - new Date(a.createAt));

    return list;
  }, [fault]);
  const handleReviewLogs = async () => {
    setOpenModal(true);
    const logPath = await window.fileAPI.getLogPath();
    const logInfo = await window.fileAPI.getLogsInfo(`${logPath}/logs/systemFault/systemFault.log`);
    // @ts-ignore
    let descLog = logInfo.sort((a, b) => new Date(b.createAt) - new Date(a.createAt));
    if (!product.isNav) {
      descLog = descLog?.filter((log: any) => !['视觉系统故障', '视觉系统相机故障'].includes(log?.content));
    }
    setHistoryError(descLog);
  };

  return (
    <div className={styles.container}>
      <div className={styles.tableContainer}>
        <ConfigProvider
          renderEmpty={() => {
            return <NgEmpty emptyType={'noNotice'} />;
          }}
        >
          <NgTable<FaultMapItemType>
            columns={columns()}
            data={allInfo}
            scroll={{ y: 500 }}
            pagination={{
              total: allInfo.length,
              pageSize: 1000,
              disabled: true,
              showPrevNextJumpers: false,
              showQuickJumper: false,
              showSizeChanger: false,
              hideOnSinglePage: true,
            }}
          />
        </ConfigProvider>
      </div>
      <div className={styles.history}>
        <NgDarkButton onClick={handleReviewLogs}>查看历史</NgDarkButton>
      </div>
      <NgModal open={openModal} title={'历史故障'} width={910} onCancel={() => setOpenModal(false)} className={styles.historyTable} footer={<></>}>
        <NgTable<FaultMapItemType>
          columns={columns(true)}
          data={historyError}
          rowKey={record => uniqueId(record.key)}
          scroll={{ y: 400 }}
          pagination={{
            total: historyError.length,
          }}
        />
      </NgModal>
    </div>
  );
};
export default Fault;
