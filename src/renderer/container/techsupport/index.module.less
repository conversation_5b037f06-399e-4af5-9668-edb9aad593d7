@import '@/renderer/static/style/baseColor.module.less';

.container {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 20px;
  box-sizing: border-box;
  height: 100%;
  overflow: hidden;
  position: relative;

  :global {
    [class^='NgDarkButton__ng_dark_button'] div:first-child button {
      padding: 3px 24px !important;
    }
    [class^='NgDarkButton__ng_dark_button'] .ant-btn-default:not(:disabled):hover {
      padding: 3px 24px !important;
    }
    [class^='NgDarkButton__ng_dark_button'] .ant-btn-default:disabled {
      padding: 3px 24px !important;
    }
  }

  .camera_and_coil {
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .logoIcon {
      visibility: hidden;
    }
  }

  .toolbox {
    display: flex;
    align-items: center;
    background: @colorA4;
    padding: 5px 16px;
    border-radius: 6px;
    height: 46px;

    & > span:first-child {
      margin-right: 20px;
    }
  }

  .view {
    display: flex;
    flex-direction: row;
    flex-grow: 1;

    > div {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      width: 23%;
      margin-right: 12px;
    }

    > div:last-child {
      margin-right: 0px;
    }

    .second_column {
      padding: 24px;
      flex-grow: 1;
      background-color: @colorA3;
      border-radius: 8px;
    }
  }
}

.loading {
  max-height: 100% !important;
}

:global {
  .ant-spin-nested-loading {
    height: 100%;
  }
  .ant-spin-container {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .ant-spin-nested-loading > div > .ant-spin {
    max-height: 100% !important;
  }
}
