@import '../../static/style/baseColor.module.less';

.constainer {
  background-color: @colorA1;
  height: 100%;
  color: @colorA12;
  overflow: hidden;
  position: relative;
  .content {
    display: flex;
    // align-items: center;
    justify-content: center;
    .ng_steps {
      width: 500px;
      margin-top: 32px;
    }
  }
  .pre_button {
    position: absolute;
    bottom: 130px;
    left: 309px;
  }
  .next_button {
    position: absolute;
    bottom: 64px;
    right: 22%;
  }
  .next_button_2 {
    position: absolute;
    bottom: 130px;
    right: 23%;
  }
  .save {
    position: absolute;
    bottom: 130px;
    right: 557px;
  }

  .sure {
    position: absolute;
    bottom: 130px;
    right: 23%;
  }
}
