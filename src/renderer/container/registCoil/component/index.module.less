@import '../../../static/style/baseColor.module.less';

.header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px 20px 24px 20px;
  height: 90px;

  .ng_breadcrumb {
    z-index: 1000;
    position: absolute;
    padding-left: 20px;
    left: 0px;
    top: 18px;
  }
  .photoandcoil {
    z-index: 100;
  }
}

.bat_front_content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .ng_alert {
    margin-top: 20px;
    width: 404px;
    max-width: 500px;
    :global {
      .ant-alert-icon {
        margin-top: 2px;
      }
      .ant-alert {
        max-width: 500px;
      }
      .ant-alert-message {
        font-size: 14px;
      }
    }
  }
  .center {
    margin-top: 50px;
    display: flex;
    margin-left: -118px;
    .status {
      :last-child {
        margin-top: 18px;
      }
      display: flex;
      flex-direction: column;
      width: 218px;
      .panle_red {
        background-color: @colorD3_end;
        display: inline-block;
        width: 14px;
        height: 14px;
        border-radius: 7px;
      }
      .panle_green {
        background-color: @colorD2_end;
        display: inline-block;
        width: 14px;
        height: 14px;
        border-radius: 7px;
      }
      .ball_red {
        width: 50px;
        height: 24px;
        border-radius: 20px;
        margin-top: 20px;
        background: linear-gradient(180deg, #ff6f6f 3%, #d93c3c 100%);
      }
      .ball_green {
        width: 50px;
        height: 24px;
        border-radius: 20px;
        margin-top: 20px;
        background: linear-gradient(4deg, #125c2f -17%, #177d3f 6%, #29f479 94%);
      }
      .status_ball {
        width: 85px;
        display: inline-block;
      }
      .discicca_content {
        margin-top: 20px;
        margin-bottom: 10px;
      }
      .discicca_green {
        width: 49px;
        height: 43px;
        opacity: 1;
        font-size: 30px;
        font-weight: 350;
        letter-spacing: 0em;
        color: @colorB2;
      }
      .discicca_red {
        width: 49px;
        height: 43px;
        opacity: 1;
        font-size: 30px;
        font-weight: 350;
        letter-spacing: 0em;
        color: @colorD3_end;
      }

      .discicca_white {
        width: 49px;
        height: 43px;
        opacity: 1;
        font-size: 30px;
        font-weight: 350;
        letter-spacing: 0em;
        color: @colorA10;
      }
      .norm_content {
        margin-top: 0px;
        margin-bottom: 10px;
        .norm {
          width: 41px;
          height: 43px;
          opacity: 1;
          font-size: 30px;
          font-weight: 350;
          letter-spacing: 0em;
          color: @colorA10;
        }
        .white {
          color: @colorA10;
        }
        .green {
          color: @colorB2;
        }
        .yellow {
          color: @colorB1;
        }
        .red {
          color: @colorB3;
        }
      }
      .registNorm {
        margin-bottom: 35px;
        .norm {
          width: 41px;
          height: 43px;
          opacity: 1;
          font-size: 30px;
          font-weight: 350;
          letter-spacing: 0em;
          color: @colorB2;
        }
        .white {
          color: @colorA10;
        }
        .green {
          color: @colorB2;
        }
        .yellow {
          color: @colorB1;
        }
        .red {
          color: @colorB3;
        }
      }
      .des {
        width: 168px;
        height: 20px;
        opacity: 1;
        font-size: 14px;
        font-weight: 350;
        letter-spacing: 0em;
        color: @colorA9;
        margin-top: 10px;
      }
    }
    .canvas_content {
      position: relative;
      width: 520px;
      height: 500px;
      margin-right: 60px;
      :global {
        .camera_error {
          position: absolute;
          border-radius: 6px;
          top: 0;
          width: 520px;
          height: 500px;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: @colorA4_1;
          color: @colorC2;
          .dot_ani {
            display: inline-block;
            height: 22px;
            line-height: 22px;
            overflow: hidden;
          }
          .dot_ani::after {
            display: inline-table;
            white-space: pre;
            content: '\A.\A..\A...';
            animation: spin1 2s steps(4) infinite;
          }
        }
      }
      .photo {
        position: absolute;
        top: 0;
        width: 520px;
        height: 500px;
        border-radius: 6px;
      }
      .white,
      .black {
        position: absolute;
        z-index: 100;
      }
      .white[data-type = 'CB'],.black[data-type = 'CB']{
        width: 165px;
        height: 188px;
      }
      .white[data-type = 'CBF'],.black[data-type = 'CBF']{
        width: 180px;
        height: 191px;
      }
      .white[data-type = 'CB']{
        top: 227px;
        left: 95px;
      }
      .black[data-type = 'CB'] {
        top: 35px;
        left: 95px;
      }
      .white[data-type = 'CBF']{
        top: 271px;
        left: 50px;
      }
      .black[data-type = 'CBF'] {
        top: 29px;
        left: 50px;
      }
      .bord {
        position: absolute;
        z-index: 100;
        width: 176px;
        height: 291px;
      }
      .bord[data-type = 'CB']{
        top: 72px;
        left: 266px;
      }
      .bord[data-type = 'CBF']{
        top: 84px;
        left: 299px;
      }
    }
    .imgs {
      display: flex;
      flex-direction: column;
      margin-right: 30px;
      img {
        width: 360px;
        height: 500px;
        margin-top: 50px;
      }
    }
    .imgs :first-child {
      margin-top: 0px;
    }
  }
}

.propmt {
  .alerts :first-child {
    margin-right: 30px;
  }
  .img_content {
    display: flex;
    justify-content: center;
    // margin-top: 30px;
    .imgs_left {
      margin-top: 40px;
      display: flex;
      flex-direction: column;
      .ng_alert {
        width: 376px;
        :global {
          .ant-alert {
            max-width: 500px;
          }
          .ant-alert-icon {
            margin-top: 2px;
            margin-right: 8px;
          }
          .ant-alert-message {
            font-size: 14px;
            width: 322px;
          }
        }
      }
      img {
        width: 376px;
        height: 400px;
      }
      .imgs_left_first {
        height: 370px;
      }
      .imgs_left_last {
        margin-top: 20px;
      }
    }
    .imgs_right {
      margin-left: 57px;
      margin-top: 40px;
      .ng_alert {
        width: 347px;
        :global {
          .ant-alert {
            max-width: 500px;
          }
          .ant-alert-icon {
            margin-top: 2px;
            margin-right: 8px;
          }
          .ant-alert-message {
            font-size: 14px;
            width: 322px;
          }
        }
      }
      img {
        width: 347px;
        height: 788px;
      }
    }
  }
}

.next_button {
  position: absolute;
  bottom: 30px;
  right: 24%;
}
