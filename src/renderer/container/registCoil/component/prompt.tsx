import React from 'react';
import { bat } from '../../../utils/treat';
import styles from './index.module.less';
// eslint-disable-next-line import/no-internal-modules
import CB03BgPNG1 from '../../../static/images/regist_coil1_1.png';
import CB03BgPNG2 from '../../../static/images/regist_coil1_2.png';
import CB03BgPNG3 from '../../../static/images/regist_coil1_3.png';
import CBF03BgPNG1 from '../../../static/images/regist_coil_cbf03_1.png';
import CBF03BgPNG2 from '../../../static/images/regist_coil_cbf03_2.png';
import CBF03BgPNG3 from '../../../static/images/regist_coil_cbf03_3.png';

export const Propmt = () => {
  const type = bat.batType || 'CB-03';
  let bgPNG1 = '';
  let bgPNG2 = '';
  let bgPNG3 = '';
  if (type === 'CB-03') {
    bgPNG1 = CB03BgPNG1;
    bgPNG2 = CB03BgPNG2;
    bgPNG3 = CB03BgPNG3;
  } else {
    bgPNG1 = CBF03BgPNG1;
    bgPNG2 = CBF03BgPNG2;
    bgPNG3 = CBF03BgPNG3;
  }

  return (
    <div className={styles.propmt}>
      <div className={styles.img_content}>
        <div className={styles.imgs_left}>
          <img className={styles.imgs_left_first} alt="" src={bgPNG2} />
          <img className={styles.imgs_left_last} alt="" src={bgPNG3} />
        </div>
        <div className={styles.imgs_right}>
          <img alt="" src={bgPNG1} />
        </div>
      </div>
    </div>
  );
};

export default Propmt;
