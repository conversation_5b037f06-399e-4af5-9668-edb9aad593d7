import React from 'react';
import { NgBreadcrumb } from '../../../uiComponent/NgBreadCrumb';
import CameraAndCoil from '../../../component/cameraAndCoil';
import styles from './index.module.less';
import { UserSessionProps, withUserSession } from '../../../hocComponent/withUserSession';
import classNames from 'classnames';

type RegistCoilHeaderPropsType = {
  title: string;
  isDisable?: boolean;
  propsClassName?: string;
};

export const RegistCoilHeader = withUserSession((props: RegistCoilHeaderPropsType & UserSessionProps) => {
  return (
    <div className={classNames(props.propsClassName, styles.header)}>
      <NgBreadcrumb
        disable={props.isDisable}
        isGray={false}
        contentClassName={styles.ng_breadcrumb}
        items={[
          { disable: props.isDisable, path: '/home', breadcrumbName: '首页' } as any,
          { disable: props.isDisable, path: '', breadcrumbName: props.title },
        ]}
      />
      <CameraAndCoil container={styles.photoandcoil} />
    </div>
  );
});
