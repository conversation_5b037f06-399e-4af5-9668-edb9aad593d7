import { imgSocket } from '../../../utils/imgSocket';
import React, { useEffect } from 'react';
import styles from './index.module.less';
import bgPNG2 from '../../../static/images/regist_coil2_1.png';
import bgPNG3 from '../../../static/images/regist_coil3_1.png';
import bordPNG2 from '../../../static/images/registcoil_bord_tip.png';
import whitePNG2 from '../../../static/images/registcoil_white_tip.png';
import blackPNG from '../../../static/images/registcoil_black_tip.png';
import { useIntl } from 'react-intl';
import classNames from 'classnames';
import './animation.less';
import { useUnmount } from 'ahooks';
import { bat } from '../../../utils/treat';
export type BatFrontPropsType = {
  registNorm?: number;
  isRegist: boolean;
  loading?: boolean;
  setBallPanleState(ball: boolean, panle: boolean, norm1m: number, discicca?: number): void;
  setRegistNorm(num?: number): void;
  setLoading(loading: boolean): void;
  step: number;
};
export const BatFront = (props: BatFrontPropsType) => {
  const imgRef = React.createRef<HTMLCanvasElement>();
  const [ball, setBall] = React.useState(0);
  const [panle, setPanle] = React.useState(0);
  const intl = useIntl();
  const [norm, setNorm] = React.useState<number | undefined>(undefined);
  const [discicca, setDiscicca] = React.useState<number | undefined>(undefined);
  const [batType] = React.useState<string>(() => {
    if (['CB-03', 'CB-04'].includes(bat.batType)) return 'CB';
    if (['CBF-03', 'CBF-04'].includes(bat.batType)) return 'CBF';

    return 'CB';
  });
  useEffect(() => {
    const img = new Image();
    const context = imgRef.current?.getContext('2d');
    imgSocket.onmessage = async (data: any) => {
      setBall(data.ciccaready);
      setPanle(data.cicready);
      props.setBallPanleState(!!data.ciccaready, !!data.cicready, data.norm1m, data.discicca);
      props.setLoading(false);
      if (data.b64 && context) {
        img.src = data.b64;
        img.onload = () => {
          context.clearRect(0, 0, 520, 500);
          context.drawImage(img, 0, 0, 520, 500);
        };
      }
      if (data.norm1m) {
        setNorm(data.norm1m);
      }
      if (data.discicca !== undefined) {
        setDiscicca(data.discicca);
      }
    };
  }, [panle]);

  useEffect(() => {
    setNorm(undefined);
  }, [props.step]);

  useUnmount(() => {
    imgSocket.onmessage = undefined;
    props.setLoading(true);
  });

  const getNormColor = () => {
    if (props.registNorm !== undefined && props.isRegist) {
      return styles.white;
    } else if (norm && norm <= 1) {
      return styles.green;
    } else if (norm && norm > 1 && norm <= 1.5) {
      return styles.yellow;
    } else if (norm && norm > 1.5) {
      return styles.red;
    } else {
      return '';
    }
  };

  const getregistNormColor = () => {
    if (props.registNorm && props.registNorm <= 1) {
      return styles.green;
    } else if (props.registNorm && props.registNorm > 1 && props.registNorm <= 1.5) {
      return styles.yellow;
    } else if (props.registNorm && props.registNorm > 1.5) {
      return styles.red;
    } else {
      return '';
    }
  };

  const getDisciccaClassName = () => {
    if (props.isRegist) return styles.discicca_white;

    return discicca && discicca < 350 ? styles.discicca_green : styles.discicca_red;
  };

  return (
    <div className={styles.bat_front_content}>
      <div className={styles.center}>
        <div className={styles.imgs}>
          <img alt="" src={props.step === 1 ? bgPNG2 : bgPNG3} />
        </div>
        <div className={styles.canvas_content}>
          <div className="camera_error" style={{ zIndex: props.loading ? 5 : 1 }} />
          <canvas width={520} height={500} style={{ zIndex: props.loading ? 1 : 5 }} className={styles.photo} ref={imgRef} />
          <div>
            {props.step === 1 ? <img src={blackPNG} alt="" data-type={batType} className={styles.white} /> : <img src={whitePNG2} alt="" data-type={batType} className={styles.black} />}
            <img src={bordPNG2} alt="" data-type={batType} className={styles.bord} />
          </div>
        </div>
        <div className={styles.status}>
          <div>
            <div className={styles.status_ball}>{intl.formatMessage({ id: '状态:' })}</div>
            <div className={ball && panle ? styles.ball_green : styles.ball_red} />
          </div>
          {
            <div className={styles.discicca_content}>
              {intl.formatMessage({ id: '距离（mm）：' })}
              <span className={getDisciccaClassName()}>{discicca?.toFixed(2)}</span>
            </div>
          }
          {
            <div className={styles.norm_content}>
              {intl.formatMessage({ id: '误差（mm）：' })}
              <span className={classNames(styles.norm, getNormColor())}>{norm?.toFixed(2)}</span>
            </div>
          }
          {
            <div className={styles.registNorm}>
              {intl.formatMessage({ id: '验证（mm）：' })}
              <span className={classNames(styles.norm, getregistNormColor())}>{props.registNorm?.toFixed(2)}</span>
            </div>
          }
          {
            <>
              <div className={styles.des}>{intl.formatMessage({ id: '为保证视觉追踪治疗有效性' })}</div>
              <div className={styles.des}>{intl.formatMessage({ id: '1mm 内为最佳距离' })}</div>
              <div className={styles.des}>{intl.formatMessage({ id: '1.5mm 外需要重新注册' })}</div>
            </>
          }
        </div>
      </div>
    </div>
  );
};
