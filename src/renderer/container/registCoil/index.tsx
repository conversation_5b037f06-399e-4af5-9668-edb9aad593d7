import React from 'react';
import styles from './index.module.less';
import { useMount, useUnmount } from 'ahooks';
import { RegistCoilHeader } from './component/registCoilHeader';
import { NgSteps } from '../../uiComponent/NgStep';
import { Route, Routes, useNavigate } from 'react-router';
import NgButton from '../../uiComponent/NgButton';
import { BatFront } from './component/batFront';
import Propmt from './component/prompt';
import { Panel, connSocket, imgSocket } from '../../utils/imgSocket';
import NgMessage from '../../uiComponent/NgMessage';
import { useRecoilValue } from 'recoil';
import { tmsCoilAtom } from '../../recoil/tmsError';
import { useIntl } from 'react-intl';
import { sendRenderLog } from '../../utils/renderLogger';
import { isDev } from '@/common/lib/env/nodeEnv';
import { UserSessionProps, withUserSession } from '@/renderer/hocComponent/withUserSession';
import { EnumUserPageQueryModelRoleEnumList } from '@/common/types';
import { ErrorModel } from '../../component/systemErrorModel/errorModel';
import { FaultEnum } from '../../../common/systemFault/type';

export const RegistCoilCiontainer = (props: UserSessionProps) => {
  const [current, setCurrent] = React.useState(0);
  const [ball, setBall] = React.useState(false);
  const [panle, setPanle] = React.useState(false);
  const [msgLoading, setMsgLoading] = React.useState(false);
  const [discicca, setDiscicca] = React.useState<number | undefined>();
  const norm1m = React.useRef(0);
  const [regist1Norm, setRegist1Norm] = React.useState<number | undefined>(undefined);
  const [regist2Norm, setRegist2Norm] = React.useState<number | undefined>(undefined);
  const [isRegist2, setIsRegist2] = React.useState(false);
  const [isRegist3, setIsRegist3] = React.useState(false);
  const [registNum, setRegistNum] = React.useState(0);
  const [loading, setLoading] = React.useState(true);
  const coilInfo = useRecoilValue(tmsCoilAtom);
  const [isTechSupport] = React.useState(+(props.userSession?.role_id || 0) === EnumUserPageQueryModelRoleEnumList.TechSupport);
  const navigate = useNavigate();
  const intl = useIntl();
  const { error, contextHolder } = NgMessage.useMessage();
  const errmes = intl.formatMessage({ id: '操作失败, 请重试' });

  const handleFaultNormal = () => {
    setCurrent(0);
    navigate(`step${0}`);
    setMsgLoading(false);
  };

  useMount(() => {
    setCurrent(0);
    navigate(`step${0}`);
  });

  useUnmount(() => {
    imgSocket.stopRegistGetImg();
    connSocket.endRegistCoil();
    connSocket.closeGetImage();
  });

  const jumpRouter = () => {
    return isTechSupport ? '/techsupport' : '/home';
  };

  const registSucceed = async (value: string) => {
    window.fileAPI.setBatData(value);
    navigate(jumpRouter());
  };

  const setCurrentReduce = async () => {
    if (current - 1 === 0) {
      gotoStep1();
    } else if (current - 1 === 1) {
      await regist0GotoStep2();
    }
  };

  const regist0GotoStep2 = async () => {
    try {
      setMsgLoading(true);
      await connSocket.registCoil(Panel.panelBlack);
      setCurrent(1);
      navigate(`step${1}`);
      setIsRegist3(false);
    } catch (errors: any) {
      if (errors.timeout) sendRenderLog.error('超时了');
      // eslint-disable-next-line no-console
      console.log(errors);
    } finally {
      setMsgLoading(false);
    }
  };

  const gotoStep1 = () => {
    setCurrent(0);
    navigate(`step${0}`);
  };

  const setCurrentpuls = async () => {
    if (isDev) {
      if (current + 1 === 2) {
        setCurrent(2);
        navigate(`step${2}`);
        setRegistNum(0);
      } else if (current + 1 === 1) {
        setCurrent(1);
        navigate(`step${1}`);
      }

      return;
    }

    if (current + 1 === 2 && registNum !== 0) {
      // 第二步，且已经点过注册registNum ！=0
      await saveRegistAndGotoStep3();
    } else if (current + 1 === 2 && isRegist2 && registNum === 0) {
      // 第二步，已注册过，从其他步骤返回，可以点击下一步
      await regist1GotoStep3();
    } else if (current + 1 === 1) {
      await startRegistGetImg();
    }
  };

  const regist1GotoStep3 = async () => {
    try {
      setMsgLoading(true);
      await registCoil(Panel.panelWhite);
      setCurrent(2);
      navigate(`step${2}`);
      setRegistNum(0);
    } catch (errors) {
      // eslint-disable-next-line no-console
      console.log(errors);
    } finally {
      setMsgLoading(false);
    }
  };

  const saveRegistAndGotoStep3 = async () => {
    try {
      setMsgLoading(true);
      await connSocket.saveRegist();
      window.fileAPI.setBatData('coil_id_empty');
      sendRenderLog.info('抹掉注册信息');
      await registCoil(Panel.panelWhite);
      setCurrent(2);
      navigate(`step${2}`);
      setRegistNum(0);
    } catch (errormsg: any) {
      await error({ content: errmes });
      if (errormsg.timeout) sendRenderLog.error('超时了');
    } finally {
      setMsgLoading(false);
    }
  };

  const startRegistGetImg = async () => {
    try {
      setMsgLoading(true);
      await registCoil(1);
      setCurrent(1);
      navigate(`step${1}`);
      imgSocket.startReceiveImg = true;
      imgSocket.startRegistGetImg();
    } catch (errorMsg: any) {
      await error({ content: errmes });
    } finally {
      setMsgLoading(false);
    }
  };

  const renderNgSteps = () => {
    return (
      <NgSteps
        current={current}
        overlayClassName={styles.ng_steps}
        items={[
          {
            title: intl.formatMessage({ id: '固定注册板' }),
          },
          {
            title: intl.formatMessage({ id: '注册黑色面板' }),
          },
          {
            title: intl.formatMessage({ id: '注册白色面板' }),
          },
        ]}
      />
    );
  };

  const sendRegist = async () => {
    try {
      setMsgLoading(true);
      await connSocket.sendRegist(current + 1);
      if (current === 1) {
        setIsRegist2(true);
        setRegistNum(registNum + 1);
      } else if (current === 2) {
        setIsRegist3(true);
      }
      setTimeout(() => {
        if (current === 1) {
          setRegist1Norm(norm1m.current);
        } else {
          setRegist2Norm(norm1m.current);
        }
      }, 300);
    } catch (errorMsg: any) {
      if (errorMsg.timeout) sendRenderLog.error('超时了');
      await error({ content: errmes });
    } finally {
      setMsgLoading(false);
    }
  };

  const getNextButtonIsDisabled = () => {
    if (isDev) {
      return false;
    }

    if (current === 0) return false;
    if (current === 1) return !isRegist2;

    return false;
  };

  const getRegitstButtonIsDisabled = () => {
    if (discicca && discicca > 350) {
      return false;
    } else {
      return ball && panle;
    }
  };

  const onSure = async () => {
    try {
      setMsgLoading(true);
      await connSocket.saveRegist();
      await registSucceed(coilInfo.sn!);
      sendRenderLog.info('注册成功拍子id是:', coilInfo.sn);
    } catch (errorMsg: any) {
      if (errorMsg.timeout) sendRenderLog.error('超时了');
      await error({ content: errmes });
    } finally {
      setMsgLoading(false);
    }
  };

  const registCoil = async (num: Panel) => {
    try {
      await connSocket.registCoil(num);
    } catch (errormsg: any) {
      if (errormsg.timeout) sendRenderLog.error('超时了');
      await error({ content: errmes });
    }
  };

  /*
  ballP: 注册版就绪
  panleP：定位板就绪
  norm：误差中位数
  dis：标定工具距离
  */
  const setBallPanleState = (ballP: boolean, panleP: boolean, norm: number, dis: number) => {
    setBall(ballP);
    setPanle(panleP);
    setDiscicca(dis);
    norm1m.current = norm;
  };

  const isShowNextButton = () => {
    if (isDev) {
      return true;
    }

    if (current === 0) {
      return true;
    } else if (current === 1 && isRegist2) {
      return true;
    } else {
      return false;
    }
  };

  const handleErrorModalOK = () => {
    navigate(jumpRouter());
  };

  return (
    <div className={styles.constainer}>
      {contextHolder}
      <RegistCoilHeader title={intl.formatMessage({ id: '线圈注册' })} />
      <div className={styles.content}>{renderNgSteps()}</div>
      <Routes>
        <Route path="step0" element={<Propmt />} />
        <Route
          path="step1"
          element={
            <BatFront
              setBallPanleState={setBallPanleState}
              step={current}
              setLoading={setLoading}
              registNorm={regist1Norm}
              setRegistNorm={setRegist1Norm}
              loading={loading}
              key={'step1'}
              isRegist={isRegist2}
            />
          }
        />
        <Route
          path="step2"
          element={
            <BatFront
              isRegist={isRegist3}
              key={'step2'}
              loading={loading}
              setLoading={setLoading}
              registNorm={regist2Norm}
              setRegistNorm={setRegist2Norm}
              setBallPanleState={setBallPanleState}
              step={current}
            />
          }
        />
      </Routes>
      {(current === 1 || current === 2) && (
        <NgButton ghost disabled={msgLoading} loading={msgLoading} isNothrottle className={styles.pre_button} onClick={setCurrentReduce}>
          {intl.formatMessage({ id: '上一步' })}
        </NgButton>
      )}
      {isShowNextButton() && (
        <NgButton
          ghost
          loading={msgLoading}
          isNothrottle
          disabled={getNextButtonIsDisabled() || msgLoading}
          className={current === 0 ? styles.next_button : styles.next_button_2}
          onClick={setCurrentpuls}
        >
          {intl.formatMessage({ id: '下一步' })}
        </NgButton>
      )}
      {(current === 1 || current === 2) && (
        <NgButton
          loading={msgLoading}
          isNothrottle
          disabled={!getRegitstButtonIsDisabled() || msgLoading}
          className={styles.save}
          onClick={sendRegist}
        >
          {intl.formatMessage({ id: '注册' })}
        </NgButton>
      )}
      {current === 2 && isRegist3 && (
        <NgButton ghost loading={msgLoading} isNothrottle disabled={!isRegist3 || msgLoading} className={styles.sure} onClick={onSure}>
          {intl.formatMessage({ id: '确定' })}
        </NgButton>
      )}
      <ErrorModel onNormal={handleFaultNormal} faultTypeList={[FaultEnum.imageFault]} isStimulate={false} onOk={handleErrorModalOK} />
    </div>
  );
};

export default withUserSession(RegistCoilCiontainer);
