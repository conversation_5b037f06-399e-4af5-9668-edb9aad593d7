@import '../../static/style/baseColor.module.less';
.preview_treat {
  height: 100%;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  .flex {
    display: flex;
    overflow: hidden;
    flex: 1;
    padding-top: 52px;
  }
  :global {
    .ant-spin-nested-loading > div > .ant-spin {
      max-height: 1200px;
    }
    .ant-spin-nested-loading {
      height: 100%;
      overflow: hidden;
    }
    .ant-spin-container {
      overflow: hidden;
      display: flex;
      flex-direction: column;
      height: 100%;
    }
    .emg_header{
      position: absolute;
      width: 100%;
    }
    .immediateViewClass {
      display: flex;
      flex-direction: column;
    }
    .photo_and_coil {
      position: absolute;
      top: 20px;
      left: 50%;
      transform: translateX(-50%);
    }
    .stimulate_template_button {
      width: 300px;
      margin-top: 43px;
      margin-right: 12px;
    }
    .stimulate_template {
      border-radius: 6px;
      background-color: @colorA3;
      .relative_strength {
        display: none;
      }
    }
    .spot_list_and_control{
      margin-top: 12px;
    }
    .spot_list{
      height: 575px;
      overflow-y: auto;
      &::-webkit-scrollbar {
        width: 0;
      }
      &::-webkit-scrollbar-thumb {
        background: @colorA6;
        border-radius: 10px;
      }
      &::-webkit-scrollbar-thumb:vertical {
        width: 10px;
        height: 10px;
      }
    }
    .buttons {
      display: flex;
      position: absolute;
      bottom: 20px;
      .cancel{
        margin-right: 7px;
      }
      button {
        width: 144px;
        line-height: 40px;
        height: 40px;
        // margin-left: 12px;
      }
    }
    .surface_spot_view {
      // margin-top: -12px;
      display: flex;
      padding: 0 12px;
      overflow: hidden;
      height: 100%;
      width: 1596px;
      justify-content: space-between;
      margin-bottom: 130px;
      position: relative;
      .patient_name {
        position: absolute;
        top: 8px;
        left: 15px;
        color: @colorA10;
        z-index: 20;
      }
      .immediate_view_box{
        margin-top: 57px;
      }
      .surface_box {
        width: 1186px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .spot_list_and_control {
        width: 410px;
        height: 100%;
        display: flex;
        justify-content: space-between;
        flex: 1;
        overflow-y: auto;
        margin-top: 21px;
        overflow: hidden;
        .spot_list {
          width: 410px;
          overflow-y: auto;
          &::-webkit-scrollbar {
            width: 6px;
          }
          &::-webkit-scrollbar-thumb {
            background: @colorA6;
            border-radius: 10px;
          }
          &::-webkit-scrollbar-thumb:vertical {
            width: 10px;
            height: 10px;
          }
        }

        .ensure_template {
          width: 100%;
          .ant-btn {
            width: 100%;
            height: 48px;
            border-radius: 8px;
            background-color: @colorA4_1;
            color: @colorA12;
          }
        }
        .ensure_template[disabled] {
          color: @colorA6;
        }
      }
      .window_and_treat_info {
        width: 722px;
        height: 100%;
        display: flex;
        justify-content: space-between;
        padding-bottom: 20px;
        .window {
          width: 410px;
          height: 450px;
        }
        .treat_info {
          height: 100%;
          .blank {
            height: 12px;
            background-color: @colorA1;
            z-index: 9;
            position: relative;
          }
          .stop_treat {
            width: 100%;
            .ant-btn {
              width: 100%;
              height: 48px;
              border-radius: 8px;
              background-color: @colorA4_1;
              color: @colorA12;
              margin-top: 12px;
            }
          }
        }
      }
    }
    .ant-modal-root {
      .back_modal {
        .ant-modal-footer {
          display: none;
        }
        .ant-modal-body {
          display: flex !important;
          flex-direction: column !important;
          justify-content: center;
          align-items: center;
          .action {
            width: 50px;
            height: 50px;
          }
          .treat_complete_text {
            font-size: 16px;
            color: @colorA11;
          }
          .auto_hide_text {
            height: 24px;
            line-height: 24px;
            margin-top: 4px;
            font-size: 12px;
            color: @colorA9;
          }
          .back_button {
            margin-top: 20px;
            width: 88px;
            height: 32px;
            .ant-btn {
              width: 88px;
            }
          }
        }
      }
    }
    .msg{
      position: absolute;
      top: 109px;
      left: 50%;
      transform: translateX(-50%);
      z-index: 9;
      width: 269px;
      height: 48px;
      background-color: @colorA5;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      svg {
        margin-right: 9px;
      }
    }
  }
}
