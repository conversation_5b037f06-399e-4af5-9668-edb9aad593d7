import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'antd';
import { useMount, useUnmount } from 'ahooks';
import { NgInputNumber } from '../../../uiComponent/NgInputNumber';
import styles from './index.module.less';
import { useIntl } from 'react-intl';
import { SelectedMeasurement, ThresholdEnum } from '../../home/<USER>/subjectMeasurement';
import NgSwitch from '../../../uiComponent/NgSwitch';
import { useSetRecoilState } from 'recoil';
import { treatStatusAtom } from '../../../recoil/treatStatus';

export type EmgStrengthPropsType = {
  isStartInterval: boolean;
  hasFault: boolean;
  strength: number;
  rotate?: number;
  horizontalDisable: boolean;
  allDisabled: boolean;
  onsave(): void;
  setIsAllDisabledTrue(disable: boolean): void;
  setIsAllDisabledFalse(disable: boolean): void;
  disableHorizon<PERSON><PERSON>hange(disable: boolean): void;
  onChangeRotate(rotate: number): void;
  setThresholdEnum(e: ThresholdEnum): void;
  setInputThreshold(e: number): void;
  setOkbuttonisDisabled(disabled: boolean): void;
};

export const EmgStrength = (props: EmgStrengthPropsType) => {
  const [, setIsDisabled] = useState<undefined | boolean>();
  const [inputThreshold, setInputThreshold] = useState<number | undefined>(props.strength);
  const [thresholdType, setThresholdType] = useState(ThresholdEnum.Measurement);
  const setTreatStatus = useSetRecoilState(treatStatusAtom);

  const setOkbuttonisDisabled = (disabled: boolean) => {
    setIsDisabled(disabled);
    props.setOkbuttonisDisabled(disabled);
  };

  const setInputThresholdStrength = (threshold: number) => {
    setInputThreshold(threshold);
    props.setInputThreshold(threshold);
  };

  const setThresholdEnum = (e: ThresholdEnum) => {
    setThresholdType(e);
    props.setThresholdEnum(e);
    if (e === ThresholdEnum.Measurement) {
      props.setOkbuttonisDisabled(false);
    } else {
      props.setOkbuttonisDisabled(!inputThreshold);
    }
  };

  const setIsAllDisabledTrue = () => {
    props.setIsAllDisabledTrue(true);
  };

  const setIsAllDisabledFalse = () => {
    props.setIsAllDisabledFalse(false);
  };
  const onChangeRotate = (rotate?: number | string | null) => {
    props.onChangeRotate(rotate as number);
  };
  const intl = useIntl();

  // const getPersonDistance = (value: any) => {
  //   const distanceHead = (value.distancehead || 0) / 10;
  //   if (distanceHead < 65 || distanceHead > 110) {
  //     setHeadDistanceError(true);
  //   } else {
  //     setHeadDistanceError(false);
  //   }
  // };

  const getErrorInfo = (key: any) => {
    if (key === null) return ['error', '不可为空'];
    if (key > 145 || key < -145) return ['error', '不符合限制'];
    if (key !== Math.round(key)) return ['error', '不符合限制'];

    return ['success', ''];
  };

  const onOkClick = () => {
    props.onsave();
  };

  useMount(() => {
    setTreatStatus('treat_ing');
  });

  useUnmount(() => {
    setTreatStatus('treat_stop');
  });

  return (
    <div className={styles.strength_and_rotate}>
      <div className={styles.strength_content}>
        <div className="strength">
          <SelectedMeasurement
            isEmg
            allDisabled={props.allDisabled}
            hasFault={props.hasFault}
            threshold={props.strength}
            onOkClick={onOkClick}
            isStartInterval={props.isStartInterval}
            thresholdType={thresholdType}
            setThreshold={setInputThresholdStrength}
            setOkbuttonisDisabled={setOkbuttonisDisabled}
            setIsAllDisabledTrue={setIsAllDisabledTrue}
            setIsAllDisabledFalse={setIsAllDisabledFalse}
            onChangeRadio={setThresholdEnum}
          />
        </div>
      </div>
      <div className="rotate">
        <div className="rotate_switch_content">
          <div>{intl.formatMessage({ id: '线圈旋转角度：' })}</div>
          <div className="rotate_switch">
            <Tooltip
              getPopupContainer={() => document.querySelector('.rotate') || document.body}
              title={!props.horizontalDisable ? '隐藏旋转角度' : '显示旋转角度'}
            >
              <NgSwitch size="small" disabled={!(getErrorInfo(props.rotate)[0] === 'success')} checked={!props.horizontalDisable} onChange={props.disableHorizontalChange} />
            </Tooltip>
          </div>
        </div>
        <div className="rotate_slider" data-disable={props.horizontalDisable}>
          <span className="min">-145</span>
          <Slider
            disabled={props.horizontalDisable}
            tooltip={{ open: false }}
            max={145}
            min={-145}
            onChange={onChangeRotate}
            value={getErrorInfo(props.rotate)[0] === 'success' ? props.rotate : (null as unknown as undefined)}
          />
          <span className="max">145</span>
        </div>
        <div className="rotate_number">
          <NgInputNumber
            disabled={props.horizontalDisable}
            className={getErrorInfo(props.rotate)[0]}
            onChange={onChangeRotate}
            value={props.rotate}
          />
          <div className="error_info">{getErrorInfo(props.rotate)[1]}</div>
        </div>
      </div>
    </div>
  );
};
