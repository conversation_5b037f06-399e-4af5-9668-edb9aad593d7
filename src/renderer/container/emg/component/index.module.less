@import '../../../static/style/baseColor.module.less';
.strength_and_rotate {
  font-size: 14px;
  background-color: @colorA1;
  .strength_content {
    margin-bottom: 12px;
    height: 171px;
    border-radius: 6px;
    opacity: 1;
    background: @colorA3;
    padding: 20px 0px 0px 20px;
    overflow: hidden;
  }
  :global {
    .ant-slider-disabled {
      .min,
      .max {
        color: @colorA9;
      }
      .ant-slider-track {
        background-color: @colorA6 !important;
      }
      .ant-slider-rail {
        background-color: @colorA9 !important;
      }
      .ant-slider-handle {
        &:hover {
          &::after {
            box-shadow: 0 0 0 2px @colorA6;
            background-color: @colorA6;
          }
        }
        &::after {
          background-color: @colorA6 !important;
          box-shadow: 0 0 0 2px @colorA6 !important;
        }
      }
    }
    .strength {
      .strength_progress_content {
        display: flex;
        justify-content: space-between;
        margin-top: 25px;
      }
      .item {
        display: flex;
        // justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        justify-content: space-between;
        font-size: 16px;
        font-weight: 350;
        letter-spacing: 0em;
        color: @colorA12;
        .title {
          display: flex;
          align-items: center;
          .bar {
            width: 6px;
            height: 16px;
            border-radius: 10px;
            margin-right: 10px;
          }
        }
        .value {
          font-size: 26px;
          font-weight: 500;
          letter-spacing: 0em;
          color: @colorB3;
        }
        .strength_progress {
          position: relative;
          z-index: 2;

          & > div {
            transform: unset;
          }
        }
      }
    }
    .rotate {
      position: relative;
      z-index: 9;
      height: 145px;
      border-radius: 6px;
      background: @colorA3;
      padding: 20px;
      .rotate_number {
        margin-top: 10px;

        .error {
          border-color: @colorB3;
        }

        .error_info {
          height: 20px;
          margin-top: 3px;
          font-size: 12px;
          color: @colorB3;
        }
      }

      .rotate_slider[data-disable='true'] {
        .min,
        .max {
          color: @colorA9;
        }
      }

      .rotate_slider {
        display: flex;
        align-items: center;
        .ant-slider {
          width: 207px;
          margin-top: 17px;
        }
        .ant-slider-track {
          height: 2px;
          background-color: @colorC4;
        }
        .ant-slider-rail {
          height: 2px;
          background-color: @colorC3;
        }
        .ant-slider-handle {
          width: 8px;
          height: 8px;
          &:hover {
            &::after {
              box-shadow: 0 0 0 2px @colorC2;
              background-color: @colorC2;
              inset-block-start: 0px !important;
              inset-inline-start: 0px !important;
            }
          }
          &::after {
            width: 8px;
            height: 8px;
            background-color: @colorC4;
            box-shadow: 0 0 0 2px @colorC4;
          }
          &::before {
            width: 8px;
            height: 8px;
            display: none;
          }
        }
      }
    }
    .rotate_switch_content {
      display: flex;
      justify-content: space-between;
    }
    .rotate_switch {
      .ant-switch.ant-switch-small {
        min-width: 18px;
        height: 12px;
        .ant-switch-handle {
          top: 3px;
          width: 6px;
          height: 6px;
          &::before {
            background-color: @colorA3;
          }
        }
        .ant-switch-inner {
          padding-inline-start: initial;
          padding-inline-end: initial;
        }
      }
      .ant-switch-checked {
        .ant-switch-handle {
          inset-inline-start: 10px !important;
        }
      }
    }
    .import_tem {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }
}
