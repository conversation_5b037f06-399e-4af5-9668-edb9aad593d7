import React, { useEffect, useRef, useState } from 'react';
import { useAsyncEffect, useThrottleFn, useUnmount } from 'ahooks';
import { getM200ApiInstance } from '../../../common/api/ngApiAgent';
import { UserSessionProps, withUserSession } from '../../hocComponent/withUserSession';
import { EnumUserPageQueryModelRoleEnumList, MotionThresholdModel, PlanImportInfoModel, PlanModel, PlanTargetModel } from '../../../common/types';
import { useNavigate, useParams } from 'react-router';
import { SpotItem } from '../../component/spotItem';
import classNames from 'classnames';
import styles from './index.module.less';
import { RegistCoilHeader } from '../registCoil/component/registCoilHeader';
import { TreatmentSurface } from '../../component/treatmentSurface/treatmentSurface';
import { EmgStrength } from './component/emgStrength';
import { IpcRendererEvent } from 'electron';
import NgDarkButton from '../../uiComponent/NgDarkButton';
import { ErrorModel } from '../../component/systemErrorModel/errorModel';
import { ITmsError } from '../../recoil/tmsError';
import { useRecoilState, useSetRecoilState } from 'recoil';
import { useIntl } from 'react-intl';
import { useStoragePath } from '../../recoil/storagePath';
import { connSocket } from '../../utils/imgSocket';
import { Spin } from 'antd';
import { LineStatusType, LineType, useLineAtom } from '../../recoil/lineStatus';
import { beyondAllEntryPoint } from '../previewPlan/utils';
import { ThresholdEnum, getTh } from '../home/<USER>/subjectMeasurement';
import { SpotList } from '../previewTreat';
import { NgLoading } from '../../uiComponent/NgLoading';
import { TMSScreenState } from '../../../common/constant/tms';
import { sendRenderLog } from '../../utils/renderLogger';
import { SimpleErrorModel } from '../../component/systemErrorModel/simpleErrorModel';
import { isNotTreatingAtom } from '../../recoil/isNotTreating';
import { FaultTypeList } from '../../../common/systemFault/config';
import { faultAtom } from '../../recoil/fault';
import { FaultLevelEnum } from '../../../common/systemFault/type';
import { throttleOption } from '../../utils';
export type EmgContainerPropsType = { tmsError: ITmsError } & UserSessionProps & {
  subjectId?: string;
  planId?: string;
};

const horizontalMap = {
  'lh': -45,
  'rh': 45,
};

export const EmgContainer = (props: EmgContainerPropsType) => {
  const m200Api = getM200ApiInstance();
  const intl = useIntl();
  const { subjectId, planId } = useParams();
  const tmsInfo = useRef({
    isOpen: false,
    threshold: 60,
    thresholdType: ThresholdEnum.Measurement,
    inputThreshold: 60,
  });
  const volumeRef = useRef<{
    volumeViewer: any;
    volumeScalpIndex: number | undefined;
    isDetectLine: boolean;
  }>({ isDetectLine: false, volumeViewer: undefined, volumeScalpIndex: undefined });
  const activeSpotRef = useRef<PlanTargetModel>();
  const navigate = useNavigate();
  const [planInfo, setPlanInfo] = useState<PlanModel>();
  const [activeSpot, setActiveSpot] = useState<(PlanTargetModel & { is_active?: boolean }) | undefined>();
  const [fileInfo, setFileInfo] = useState<{ [prop: string]: string }>();
  const [threshold, setThreshold] = useState<number>(1);
  const [spotList, setSpotList] = useState<PlanTargetModel[]>([]);
  const [storagePathPrePath] = useRecoilState(useStoragePath);
  const [isTreating, setIsTreating] = useState<boolean>(false);
  const [spinning, setSpinning] = useState<boolean>(true);
  const [lines, setLine] = useRecoilState(useLineAtom);
  const [okDisable, setOkDisable] = useState(false);
  const [horizontalDisable, setHorizontalDisable] = useState(false);
  const [isStartInterval, setIsStartInterval] = useState<boolean>(false);
  const [fault] = useRecoilState(faultAtom);
  const [allDisabled, setAllDisabled] = useState(false);
  const [registCoilVisible, setRegistCoilVisible] = useState(false);
  const [isTechSupport] = useState(+(props.userSession?.role_id || 0) === EnumUserPageQueryModelRoleEnumList.TechSupport);
  const setIsNotTreating = useSetRecoilState(isNotTreatingAtom);
  const [surfaceHorizontal, setSurfaceHorizontal] = useState(0);
  const [pialUrl, setPialUrl] = useState<string>('');
  const [scalpMaskUrl, setScalpMaskUrl] = useState<string>('');

  useEffect(() => {
    if (!fileInfo) return;
    setPialUrl(fileInfo['pial.gii']);
    setScalpMaskUrl(fileInfo['scalp_mask.obj']);
  }, [fileInfo]);

  useAsyncEffect(async () => {
    window.tmsAPI.set_beat_screen(TMSScreenState.SingleTreat);
    await getPlanInfo(storagePathPrePath);
    setIsNotTreating(false);
  }, []);

  useAsyncEffect(async () => {
    window.tmsAPI.remove_beat_btn_by_key('EmgContainer');
    window.tmsAPI.remove_sock_info_by_key('EmgContainer');
    await window.tmsAPI.beat_btn_by_key('EmgContainer', async (event: IpcRendererEvent, data: any) => {
      if (tmsInfo.current.thresholdType === ThresholdEnum.Input) return;
      if (!!fault[FaultLevelEnum.error].length) return;
      if (data.data.key === 'play') {
        if (allDisabled || isStartInterval) return;
        debounceBeatBtnData(data);

        return;
      }
      // eslint-disable-next-line @typescript-eslint/restrict-plus-operands
      let th = getTh(data, tmsInfo.current.threshold);
      tmsInfo.current.threshold = th;
      setThreshold(tmsInfo.current.threshold);
      window.tmsAPI.set_treatment_threshold({ level: tmsInfo.current.threshold });
    });
    window.tmsAPI.get_sock_info_by_key('EmgContainer', async (value, data) => {
      if (data.open && tmsInfo.current.isOpen !== data.open) {
        window.tmsAPI.set_treatment_threshold({ level: tmsInfo.current.threshold });
      }
      tmsInfo.current.isOpen = data.open;
    });
  }, [fault, allDisabled, isStartInterval]);

  const endTmsConnect = async () => {
    window.tmsAPI.remove_beat_btn_by_key('EmgContainer');
    window.tmsAPI.remove_sock_info_by_key('EmgContainer');
    await window.tmsAPI.image_treatment_plan_start('SingleEnd');
    window.tmsAPI.set_beat_screen(TMSScreenState.NotStarted);
  };

  useUnmount(async () => {
    setIsNotTreating(true);
    await endTmsConnect();
  });

  /**
   * 添加拍子节流
   */
  const { run: debounceBeatBtnData } = useThrottleFn((_data: any) => {
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    window.tmsAPI.image_treatment_plan_start('SingleStart');
  }, throttleOption);

  const setIsAllDisabledTrue = () => {
    setAllDisabled(true);
  };

  const setIsAllDisabledFalse = () => {
    setAllDisabled(false);
  };

  const jumpRouter = () => {
    return isTechSupport ? '/techsupport' : '/home';
  };

  const handleErrorModalOK = () => {
    navigate(jumpRouter());
  };

  const onOkClick = () => {
    setIsStartInterval(true);
    sendRenderLog.info('测阈值点保存按钮');
  };

  const onsave = async () => {
    const { thresholdType, inputThreshold } = tmsInfo.current;
    if (
      isNaN(activeSpot?.horizontal!) ||
      (activeSpot?.horizontal as unknown as null) === null ||
      activeSpot?.horizontal! > 145 ||
      activeSpot?.horizontal! < -145
    ) {
      setIsStartInterval(false);

      return;
    }
    const params: MotionThresholdModel = {
      subject_id: parseInt(subjectId ? subjectId : '', 10),
      plan_id: parseInt(planId ? planId : '', 10),
      horizontal_list: [],
      motion_threshold: thresholdType === ThresholdEnum.Measurement ? threshold : inputThreshold,
    };
    let horizontalList = [
      {
        target_id: activeSpot?.id!,
        horizontal: activeSpot?.horizontal!,
        disable_horizontal: horizontalDisable,
      },
    ];
    params.horizontal_list = horizontalList;
    try {
      await m200Api.saveThreshold(params);
    } catch (error) {
      sendRenderLog.error(JSON.stringify(error));
    }
    navigate(jumpRouter());
  };

  const onChangeRotate = (horizontalVar: number) => { // NOSONAR
    if (activeSpot) {
      setActiveSpot({ ...activeSpot, horizontal: horizontalVar });
      activeSpotRef.current = { ...activeSpot, horizontal: horizontalVar };
      setSendSurfaceHorizontal(activeSpotRef.current);
    }
  };

  const getPlanInfo = async (prePath: string) => {
    // NOSONAR
    const subjectIdVar = subjectId ? subjectId : '';
    const planIdVar = planId ? planId : '';
    const plan = await m200Api.getPlanById(parseInt(planIdVar, 10), parseInt(subjectIdVar, 10));
    const treatTargetList = plan?.plan_target_model_list?.filter(item => item.has_mep) || [];
    const spotArr = treatTargetList?.length > 0 ? treatTargetList : [];
    let activeSpotVar = spotArr.find((spot: PlanTargetModel) => !!spot.normal_line);
    activeSpotVar = activeSpotVar ? activeSpotVar : spotArr[0];
    const file_info = plan.plan_file_model_list?.reduce((pre: any, cur: any) => ({ ...pre, [cur.name]: `${prePath}/${cur.relative_path}` }), {});
    const plyFile = plan?.plan_file_model_list?.find(item => item.name === 'face.ply')?.relative_path;
    if (plyFile) await setPlyMd5(prePath, plyFile);
    setImportInfoModel(plan.plan_import_info_model);
    const horizon = getRealHorizontal(activeSpotVar);
    setFileInfo(file_info);
    setPlanInfo(plan);
    activeSpotVar.horizontal = horizon;
    setActiveSpot(activeSpotVar);
    activeSpotRef.current = {...activeSpotVar};
    setSpotList(spotArr.map(v => ({ ...v, disable_horizontal: false, horizontal: v.horizontal === undefined ?  getRealHorizontal(v) : v.horizontal })));
    setThreshold(plan.subject_model.motion_threshold || 60);
    setHorizontalDisable(false);
    setSendSurfaceHorizontal(activeSpotVar);
    tmsInfo.current.threshold = plan.subject_model.motion_threshold || 60;
    if (activeSpotVar && activeSpotVar?.normal_line) {
      hasMepAndStartTreating(subjectIdVar, planIdVar, spotArr, file_info);
    } else {
      await detectNormalLine(spotArr);
    }
    await window.tmsAPI.set_treatment_threshold({ level: plan.subject_model.motion_threshold || 60 });
  };

  const setImportInfoModel = (importModel?: PlanImportInfoModel) => {
    if (!importModel) return;
    connSocket.import_id = importModel.import_id;
    connSocket.file_sha256 = importModel.file_sha256;
    connSocket.file_version = importModel.file_version;
    connSocket.file_source = importModel.file_source;
  };

  const setPlyMd5 = async (prePath: string, plyFile: string) => {
    const plyMd5 = await window.fileAPI.getFileMd5(`${prePath}/${plyFile}`);
    connSocket.plyFile = `${prePath}/${plyFile}`;
    connSocket.plyMd5 = plyMd5;
  };

  const hasMepAndStartTreating = (subjectIdVar: string, planIdVar: string, spotArr: PlanTargetModel[], file_info: any) => {
    startTreating();
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    findNotNormalLine(subjectIdVar, planIdVar, spotArr, file_info);
  };

  const findNotNormalLine = async (subjectIdVar: string, planIdVar: string, spotArr: PlanTargetModel[], file_info: any) => {
    const notNormalLine = spotArr.find((obj: PlanTargetModel) => !obj.normal_line && obj.has_mep);
    const callBack = async () =>
      beyondAllEntryPoint(
        volumeRef.current.volumeViewer,
        volumeRef.current.volumeScalpIndex!,
        spotArr,
        {
          plan_id: planId,
          subject_id: subjectId,
        },
        setLine,
        (file_info || {})['head_convex_hull.ply']
      ).then(async () => {
        const plan = await m200Api.getPlanById(parseInt(planIdVar, 10), parseInt(subjectIdVar, 10));
        const treatTargetList = plan?.plan_target_model_list?.filter(item => item.has_mep) || [];
        const newSpotArr = treatTargetList?.length > 0 ? treatTargetList : [];
        setSpotList(newSpotArr.map(v => (activeSpotRef.current!.id === v.id!? activeSpotRef.current!: { ...v, disable_horizontal: false, horizontal:  getRealHorizontal(v)})));
      });
    if (notNormalLine && volumeRef.current.volumeScalpIndex && volumeRef.current.volumeViewer) {
      // eslint-disable-next-line @typescript-eslint/no-floating-promises
      callBack();
    } else if (notNormalLine) {
      setTimeout(() => {
        // eslint-disable-next-line @typescript-eslint/no-floating-promises
        findNotNormalLine(subjectIdVar, planIdVar, spotArr, file_info);
      }, 1000);
    }
  };

  const detectNormalLine = async (spotArr: PlanTargetModel[]) => {
    const hasLoadLine = lines.find((line: LineType) => `${line.key}` === planId);
    if ((hasLoadLine && hasLoadLine.status === LineStatusType.loading) || volumeRef.current.isDetectLine) {
      startGetNormalLine();
    } else if (volumeRef.current.volumeScalpIndex !== undefined && volumeRef.current.volumeViewer) {
      computeBeyondAllEntryPoint(volumeRef.current.volumeScalpIndex, spotArr);
    } else if (!volumeRef.current.volumeScalpIndex || !volumeRef.current.volumeViewer) {
      setTimeout(async () => {
        await getPlanInfo(storagePathPrePath);
      }, 1500);
    }
  };

  const computeBeyondAllEntryPoint = (scalpIndex: number, spotArr: PlanTargetModel[]) => {
    volumeRef.current.isDetectLine = true;
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    beyondAllEntryPoint(
      volumeRef.current.volumeViewer,
      scalpIndex,
      spotArr,
      {
        plan_id: planId,
        subject_id: subjectId,
      },
      setLine,
      (fileInfo || {})['head_convex_hull.ply']
    ).then(async () => {
      await getPlanInfo(storagePathPrePath);
    });
  };

  const handleRegistCoilOk = () => {
    setRegistCoilVisible(false);
    navigate('/registCoil');
  };

  const handleRegistCoilCancle = () => {
    setRegistCoilVisible(false);
    navigate(jumpRouter());
  };

  const startTreating = () => {
    setTimeout(() => {
      setIsTreating(true);
    }, 2000);
  };

  const startGetNormalLine = () => {
    setTimeout(async () => {
      await getPlanInfo(storagePathPrePath);
    }, 1500);
  };

  const goback = () => {
    navigate(jumpRouter());
  };

  const getRealHorizontal = (info: PlanTargetModel) => {
    if (typeof info.horizontal === 'number') return info.horizontal;

    return horizontalMap[info.hemi] || 0;
  };

  const handleSpotClick = (id: number) => {
    setIsTreating(false);
    let newActiveSpot: (PlanTargetModel & { is_active: boolean }) | undefined;
    const spotListVal = spotList ? spotList : [];
    const newSpotList = spotListVal.map((spot: PlanTargetModel) => {
      if (spot.id === id) newActiveSpot = { ...spot, is_active: true };
      if (activeSpotRef.current?.id! === spot.id) {
        return { ...spot,  horizontal: activeSpotRef.current?.horizontal, is_active: false};
      }

      if (spot.id === id) {
        return { ...spot, is_active: true };
      } else {
        return { ...spot, is_active: false };
      }
    });
    if (!newActiveSpot?.normal_line) return;
    setSpotList(newSpotList);
    setActiveSpot(newActiveSpot);
    activeSpotRef.current = newActiveSpot;
    setHorizontalDisable(!!newActiveSpot?.disable_horizontal);
    setTimeout(() => {
      setIsTreating(true);
    }, 300);
  };

  const setThresholdEnum = (e: ThresholdEnum) => {
    tmsInfo.current.thresholdType = e;
    if (e === ThresholdEnum.Measurement) {
      window.tmsAPI.set_treatment_threshold({ level: tmsInfo.current.threshold });
    }
  };

  const setInputThreshold = (num: number) => {
    tmsInfo.current.inputThreshold = num;
    tmsInfo.current.threshold = num;
    setThreshold(num);
    window.tmsAPI.set_treatment_threshold({ level: tmsInfo.current.threshold });
  };

  const setOkbuttonisDisabled = (disable: boolean) => {
    setOkDisable(disable);
  };

  const disableHorizontalChange = (disable: boolean) => {
    setHorizontalDisable(!disable);
    const spotListVar = spotList ? spotList : [];
    const newSpot = spotListVar.find((spotVar: PlanTargetModel) => spotVar.id === activeSpot?.id)!;
    newSpot.disable_horizontal = !disable;
  };

  /**
   * 脑图渲染完毕，loading加载结束
   */
  const surfaceLoadComplete = () => {
    setSpinning(false);
  };

  const renderLoading = () => {
    return (
      <div>
        <NgLoading loadingText={'加载中...'} />
      </div>
    );
  };

  const setSendSurfaceHorizontal = (activeSpotVar: any) => {
    if (horizontalDisable) {
      setSurfaceHorizontal(0);

      return;
    }
    if (activeSpotVar) {
      const val = -getRealHorizontal(activeSpotVar);
      if (val >= -145 && val <= 145) {
        setSurfaceHorizontal(val);
      }
    }
  };

  const renderSpotList = () => {
    return (
      <div className="spot_list_and_control">
        <div className="spot_list">
          {spotList?.map((v: SpotList, index: number) => (
            <SpotItem
              key={v.id}
              spotInfo={v}
              onSelect={handleSpotClick}
              index={index}
              isTreat
              activeId={activeSpot?.id !== undefined ? activeSpot.id : null}
            />
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className={classNames(styles.preview_treat,'preview_treat')}>
      <Spin spinning={spinning} indicator={renderLoading()}>
        <RegistCoilHeader propsClassName='emg_header' isDisable={isStartInterval} title='测量阈值' />
        {planInfo && (
          <div className={styles.flex}>
            <TreatmentSurface
              pialUrl={pialUrl}
              scalpMaskUrl={scalpMaskUrl}
              volumeFiles={[fileInfo?.['T1.mgz'] || '', fileInfo?.['scalp_mask.nii'] || '']}
              spotList={spotList}
              targetStimulate={activeSpot?.stimulus}
              activeSpot={activeSpot}
              isTreating={isTreating}
              planId={planId ? planId : ''}
              subjectId={subjectId ? subjectId : ''}
              planInfo={planInfo}
              isEmg
              children={null}
              immediateViewClass={'immediateViewClass'}
              activeId={activeSpot?.id !== undefined ? activeSpot.id : null}
              getVolumeScalpIndex={(index: number) => (volumeRef.current.volumeScalpIndex = index)}
              setVolumeViewer={(viewer: any) => (volumeRef.current.volumeViewer = viewer)}
              horizontalDisable={horizontalDisable}
              coilPanelMissingCallback={()=>setRegistCoilVisible(true)}
              horizontal={surfaceHorizontal}
              surfaceLoadComplete={surfaceLoadComplete}
              // horizontal={horizontalDisable ? 0 : -getRealHorizontal(activeSpot)}
              helper
            />
            <div className="stimulate_template_button">
              <div className="stimulate_template">
                <EmgStrength
                  isStartInterval={isStartInterval}
                  hasFault={!!fault[FaultLevelEnum.error].length}
                  strength={threshold}
                  rotate={activeSpot?.horizontal}
                  onsave={onsave}
                  allDisabled={allDisabled}
                  onChangeRotate={onChangeRotate}
                  setThresholdEnum={setThresholdEnum}
                  setInputThreshold={setInputThreshold}
                  setOkbuttonisDisabled={setOkbuttonisDisabled}
                  setIsAllDisabledFalse={setIsAllDisabledFalse}
                  setIsAllDisabledTrue={setIsAllDisabledTrue}
                  horizontalDisable={horizontalDisable}
                  disableHorizontalChange={disableHorizontalChange}
                />
              </div>
              <div className='spot_list'>
                {renderSpotList()}
              </div>
              <div className={'buttons'}>
                <NgDarkButton className='cancel' disabled={isStartInterval} onClick={goback}>
                  {intl.formatMessage({ id: '取消' })}
                </NgDarkButton>
                <NgDarkButton disabled={okDisable || isStartInterval || allDisabled} onClick={onOkClick}>
                  {intl.formatMessage({ id: '保存' })}
                </NgDarkButton>
              </div>
            </div>
          </div>
        )}
        {registCoilVisible && (
          <SimpleErrorModel
            visible
            isClearRegistCoilInfo
            title="线圈注册结果异常"
            isStimulate={false}
            errorList={['重新注册线圈后，可继续使用']}
            btnLabel={'线圈注册'}
            cancleLabel="取消"
            onCancle={handleRegistCoilCancle}
            onOk={handleRegistCoilOk}
          />
        )}
        <ErrorModel isStimulate faultTypeList={FaultTypeList} onOpen={endTmsConnect} onOk={handleErrorModalOK} />
      </Spin>
    </div>
  );
};

export default withUserSession(EmgContainer);
