export interface IReportDetail {
  motion_threshold: number;
  plan_id: number;
  plan_stimulus_id: number;
  plan_target_id: number;
  planned_stimulation_duration: number;
  stimulus_data: StimulusData;
  subject_id: number;
  target_data: TargetData;
  uuid: string;
  remark: string;
  updated_id: number;
  created_id: number;
  id: number;
  updated_at: number;
  created_at: number;
  type: number;
  trace_id: string;
  stimulus_end_time: number;
  stimulus_start_time: number;
  strength_data_list: StrengthDataList[];
  actual_stimulation_duration: number;
}
export enum stimulusTypeEnum {
  'SINGLE' = 1,
  'rTMS' = 2,
  'iTBS' = 3,
  'cTBS' = 4,
  'TBS' = 5,
}
export interface StimulusData {
  actual_strength: number;
  relative_strength: number;
  type: number;
  plexus_count: number;
  inner_strand_pulse_count: number;
  plexus_inner_frequency: number;
  strand_pulse_frequency: number;
  plexus_inter_frequency: number;
  pulse_total: number;
  intermission_time: number;
  plexus_inner_pulse_count: number;
  strand_pulse_count: number;
}

export interface TargetData {
  code: string;
  target_name: string;
  vol_ras: VolRas;
}

export interface VolRas {
  x: number;
  y: number;
  z: number;
}

export interface StrengthDataList {
  stimulus_time: number;
  actual_strength: number;
}
