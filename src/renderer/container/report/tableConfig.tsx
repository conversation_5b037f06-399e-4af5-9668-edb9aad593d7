// eslint-disable-next-line import/no-internal-modules
import moment from 'moment/moment';
import React, { Fragment } from 'react';
import { stimulusTypeEnum } from '@/renderer/container/report/types';
import { ColumnType } from 'antd/lib/table';
import { EnumPlanStimulusType, IReportModel, ISubjectResponse } from '@/common/types';
import { IntlShape } from 'react-intl';
import { calRules } from '../../component/template/calRules';
import styles from './index.module.less';
import classnames from 'classnames';
import { MenuTypeEnum } from './component/reportMenu';
import { calTbsChartData } from '../../component/template/calTemplate';
// 用于展示患者详情Table，渲染病情描述的mock数据，实际渲染的是患者的病情描述
export const seizeASeatData: any[] = [
  {
    key: 'name',
    name: '病情描述',
  },
];
// 为了阻止其他列渲染
export const generateRender = (text: any, record: any, index: any) => {
  const isFirstRow = index === 0;
  if (isFirstRow) return null;

  return <></>;
};
// 将其他列的colSpan设为0
export const sharedOnCell: any = (_: any, index: number) => {
  if (index === 0) {
    return { colSpan: 0 };
  }

  return {};
};

export const dealUndefine: (obj: { [name: string]: any }) => { [name: string]: number } = obj => {
  // eslint-disable-next-line radix
  return Object.keys(obj).reduce((pre, cur) => ({ ...pre, [cur]: isNaN(parseInt(obj[cur])) || obj[cur] === null ? '--' : obj[cur] }), {});
};

export const handleAccuracy = (value: number) => {
  return value >= 9999 ? '无效' : value;
};

export const formatSeconds = (seconds: number) => {
  if (isNaN(seconds)) {
    return '--';
  }

  seconds = seconds | 0;
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;

  // 使用padStart来确保每个部分至少有两位数字
  const formattedHours = hours.toString().padStart(2, '0');
  const formattedMinutes = minutes.toString().padStart(2, '0');
  const formattedSeconds = secs.toString().padStart(2, '0');

  return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`;
};

export const getSubjectTableColumns = (state: ISubjectResponse, intl: IntlShape) => {
  return [
    {
      title: () => {
        return (
          <div className={styles.wrapContainer}>
            <div className={styles.textTitle}>{intl.formatMessage({ id: '姓名' })}：</div>
            <div className={styles.message}>{state?.name}</div>
          </div>
        );
      },
      width: 180,
      key: 'name',
      dataIndex: 'name',
      onCell: (_: any, index: number) => ({
        colSpan: index === 0 ? 5 : 1,
      }),
      render: () => {
        return (
          <>
            {intl.formatMessage({ id: '病情描述' })}：{state.condition_desc}
          </>
        );
      },
    },
    {
      title: () => {
        return (
          <div className={styles.wrapContainer}>
            <div className={styles.textTitle}>ID：</div>
            <div className={classnames(styles.message, styles.code)}>{state?.code}</div>
          </div>
        );
      },
      width: 180,
      key: 'id',
      onCell: sharedOnCell,
      render: generateRender,
    },
    {
      title: () => {
        return (
          <>
            {intl.formatMessage({ id: '运动阈值（%MO）' })}：{state?.motion_threshold}
          </>
        );
      },
      key: 'motion',
      onCell: sharedOnCell,
      render: generateRender,
    },
    {
      title: () => {
        const sexMap = {
          1: intl.formatMessage({ id: '男' }),
          2: intl.formatMessage({ id: '女' }),
          3: intl.formatMessage({ id: '其他' }),
        };

        return (
          <>
            {intl.formatMessage({ id: '性别' })}：{sexMap[state?.sex!]}
          </>
        );
      },
      key: 'gender',
      onCell: sharedOnCell,
      render: generateRender,
    },
    {
      title: () => {
        return (
          <>
            {intl.formatMessage({ id: '出生日期' })}：{state?.birth_date ? moment(state?.birth_date).format('YYYY-MM-DD') : ''}
          </>
        );
      },
      key: 'birthday',
      onCell: sharedOnCell,
      render: generateRender,
    },
  ];
};

export const renderLabel = (key: string, stimulate: any, intl: IntlShape, isReturnStr?: boolean) => {
  let label = '';
  if (stimulate.type === EnumPlanStimulusType.RTMS) {
    label = intl.formatMessage({ id: `rtms_${key}` });
  } else {
    label = intl.formatMessage({ id: `tbs_${key}` });
  }
  if (isReturnStr) {
    return label;
  }

  return <span>{label}</span>;
};

export const getTreatmentReportColumns: (intl: IntlShape, isNoImage: boolean) => ColumnType<IReportModel>[] = (intl, isNoImage) => {
  const baseColumns: ColumnType<IReportModel>[] = [
    {
      width: 145,
      title: intl.formatMessage({ id: '运动阈值（%MO）' }),
      dataIndex: 'motion_threshold',
      align: 'center',
      key: 'motion_threshold',
    },
    {
      width: 88,
      title: intl.formatMessage({ id: '类型' }),
      dataIndex: 'type',
      key: 'type',
      align: 'center',
      render: (text, record) => {
        return <>{stimulusTypeEnum[record.stimulus_data?.type]}</>;
      },
    },
    {
      width: 192,
      title: intl.formatMessage({ id: '详情' }),
      dataIndex: 'treatment_detail',
      align: 'center',
      className: styles.detail,
      key: 'treatment_detail',
      render: (text, record: any) => {
        const { stimulus_data } = record;
        const { pulse_total } = dealUndefine(calTbsChartData(stimulus_data));

        return (
          <div className={styles.detailBox}>
            <div>总脉冲数：{pulse_total}</div>
            {calRules(stimulus_data, 0).map(v => {
              if (v.key === 'relative_strength') {
                return <Fragment key={v.key} />;
              }

              return (
                <div key={v.key}>
                  {renderLabel(v.key, stimulus_data, intl)}：{stimulus_data[v.key] || '--'}
                </div>
              );
            })}
          </div>
        );
      },
    },
    {
      title: '方案时长',
      dataIndex: 'planned_stimulation_duration',
      width: 120,
      align: 'center',
      key: 'planned_stimulation_duration',
      render: (text: number) => formatSeconds(text),
    },
    {
      title: '治疗时长',
      dataIndex: 'stimulus_start_time',
      width: 120,
      align: 'center',
      key: 'stimulus_start_time',
      render: (text: number, record: any) => formatSeconds((record.stimulus_end_time - text) / 1000),
    },
    {
      title: '刺激时长',
      dataIndex: 'actual_stimulation_duration',
      width: 120,
      align: 'center',
      key: 'actual_stimulation_duration',
      render: (text: number) => formatSeconds(text),
    },
    {
      title: intl.formatMessage({ id: '实际强度（%MO' + '）' }),
      width: 194,
      dataIndex: 'relative_strength',
      align: 'center',
      key: 'relative_strength',
      render: (text, record) => {
        return (
          <>
            {record.strength_data_list?.map(item => {
              return (
                <div key={item.stimulus_time}>
                  <span style={{ marginRight: 20 }}>{item?.actual_strength}</span>
                  <span>{item.stimulus_time ? moment(item?.stimulus_time).format('YYYY-MM-DD HH:mm:ss') : ''}</span>
                </div>
              );
            })}
          </>
        );
      },
    },
    {
      title: '暂停次数',
      dataIndex: 'stimulus_pause_count',
      width: 142,
      align: 'center',
      key: 'stimulus_pause_count',
    },
    {
      title: '治疗开始时间',
      dataIndex: 'stimulus_start_time',
      width: 142,
      align: 'center',
      key: 'stimulus_start_time',
      render: (text: number) => (text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : ''),
    },
  ];
  // 无影像不展示靶点名称
  if (!isNoImage) {
    baseColumns.splice(1, 0, {
      title: intl.formatMessage({ id: '靶点名称' }),
      dataIndex: 'target_name',
      width: 108,
      key: 'target_name',
      align: 'center',
      render: (text, record) => {
        return <>{record.target_data?.target_name}</>;
      },
    }); // 在第三项位置插入数据
  }

  return baseColumns;
};

export const initMenuList = [
  {
    label: '常规治疗',
    value: [2, 3],
    type: MenuTypeEnum.normal,
  },
  {
    label: '全部治疗',
    value: [1, 2, 3],
    type: MenuTypeEnum.all,
  },
  {
    label: '精度记录',
    value: [],
    type: MenuTypeEnum.log,
  },
];

export const accuracyColumns = [
  {
    title: '类型',
    dataIndex: 'pulseStimulateType',
    key: 'pulseStimulateType',
    width: 70,
  },
  {
    title: '治疗开始时间',
    dataIndex: 'pulseStartTime',
    key: 'pulseStartTime',
    width: 100,
  },
  {
    title: '治疗结束时间',
    dataIndex: 'pulseOverTime',
    key: 'pulseOverTime',
    width: 100,
  },
  {
    title: '方案时长',
    dataIndex: 'pulseTreatmentTime',
    key: 'pulseTreatmentTime', // s
    render: (text: number) => formatSeconds(text),
  },
  {
    title: '治疗时长',
    dataIndex: 'planDuration',
    key: 'planDuration',
    render: (text: string, record: any) => formatSeconds((moment(record.pulseOverTime).valueOf() - moment(record.pulseStartTime).valueOf()) / 1000),
  },
  {
    title: '刺激时长',
    dataIndex: 'pulseStartTime',
    key: 'pulseTime',
    render: (text: string, record: any) =>
      formatSeconds((moment(record.pulseOverTime).valueOf() - moment(text).valueOf() - record.pulsePauseDuration) / 1000), // 脉冲开始-脉冲结束-暂停
  },
  {
    title: '靶心瞄准准确率(%)',
    dataIndex: 'bullseyeAccuracy',
    key: 'bullseyeAccuracy',
    hiddenValue: 'accuracy.all',
  },
  {
    title: '靶点距离准确率(%)',
    dataIndex: 'absoluteDistanceAccuracy',
    key: 'absoluteDistanceAccuracy',
    hiddenValue: 'accuracy.absolute',
  },
  {
    title: '靶点距离阈值(mm)',
    dataIndex: 'absoluteDistanceThreshold',
    key: 'absoluteDistanceThreshold',
    hiddenValue: 'threshold.absolute',
  },
  {
    title: '靶点距离平均值(mm)',
    dataIndex: 'absoluteDistanceAverage',
    key: 'absoluteDistanceAverage',
    hiddenValue: 'average.absolute',
    render: (text: number) => handleAccuracy(text),
  },
  {
    title: '平移距离准确率(%)',
    dataIndex: 'translationDistanceAccuracy',
    key: 'translationDistanceAccuracy',
    hiddenValue: 'accuracy.translate',
  },
  {
    title: '平移距离阈值(mm)',
    dataIndex: 'translationDistanceThreshold',
    key: 'translationDistanceThreshold',
    hiddenValue: 'threshold.translate',
  },
  {
    title: '平移距离平均值(mm)',
    dataIndex: 'translationDistanceAverage',
    key: 'translationDistanceAverage',
    hiddenValue: 'average.translate',
    render: (text: number) => handleAccuracy(text),
  },
  {
    title: '夹角准确率(%)',
    dataIndex: 'normalAngleAccuracy',
    key: 'normalAngleAccuracy',
    hiddenValue: 'accuracy.angle',
  },
  {
    title: '夹角阈值(mm)',
    dataIndex: 'normalAngleThreshold',
    key: 'normalAngleThreshold',
    hiddenValue: 'threshold.angle',
  },
  {
    title: '夹角平均值(mm)',
    dataIndex: 'normalAngleAverage',
    key: 'normalAngleAverage',
    hiddenValue: 'average.angle',
    render: (text: number) => handleAccuracy(text),
  },
  {
    title: '旋转角准确率(%)',
    dataIndex: 'horizontalRotationAngleAccuracy',
    key: 'horizontalRotationAngleAccuracy',
    render: (text: string, record: any) => (record.horizontalRotationAngleSwitch ? text : '--'),
    hiddenValue: 'accuracy.rotate',
  },
  {
    title: '旋转角阈值(mm)',
    dataIndex: 'horizontalRotationAngleThreshold',
    key: 'horizontalRotationAngleThreshold',
    hiddenValue: 'threshold.rotate',
  },
  {
    title: '旋转角平均值(mm)',
    dataIndex: 'horizontalRotationAngleAverage',
    key: 'horizontalRotationAngleAverage',
    render: (text: number, record: any) => (record.horizontalRotationAngleSwitch ? handleAccuracy(text) : '--'),
    hiddenValue: 'average.rotate',
  },
  {
    title: '遮挡率(%)',
    dataIndex: 'occlusionAccuracy',
    key: 'occlusionAccuracy',
    hiddenValue: 'accuracy.mask',
  },
];
