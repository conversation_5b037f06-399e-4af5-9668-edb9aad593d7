import React, { useState } from 'react';
import { Ng<PERSON>readcrumb } from '@/renderer/uiComponent/NgBreadCrumb';
import CameraAndCoil from '@/renderer/component/cameraAndCoil';
import styles from './index.module.less';
import NgTable from '@/renderer/uiComponent/NgTable';
import { useLocation } from 'react-router-dom';
import { ColumnType } from 'antd/es/table';
import {
  accuracyColumns,
  dealUndefine,
  formatSeconds,
  getSubjectTableColumns,
  getTreatmentReportColumns,
  initMenuList,
  renderLabel,
  seizeASeatData,
} from './tableConfig';
import classnames from 'classnames';
import { onDownloadReport } from '@/renderer/utils/reportDownload';
import { isDev } from '@/common/lib/env/nodeEnv';
import { injectIntl, useIntl } from 'react-intl';
import { IntlPropType } from '@/common/types/propTypes';
import { stimulusTypeEnum } from '@/renderer/container/report/types';
import { uniqueId } from 'lodash';
import { getM200ApiInstance } from '@/common/api/ngApiAgent';
import { useAsyncEffect } from 'ahooks';
import { IReportModel, ISubjectResponse } from '@/common/types';
import NgSelectFileModal, { UploadType } from '@/renderer/uiComponent/NgSelectFileModal';
import { DeviceType } from '@/renderer/container/previewPlan';
// eslint-disable-next-line import/no-internal-modules
import moment from 'moment/moment';
import { useRecoilState, useRecoilValue } from 'recoil';
import { useLicenseAtom } from '@/renderer/recoil/license';
import NgDarkButton from '@/renderer/uiComponent/NgDarkButton';
import { osUserInfo } from '@/renderer/recoil/osUserInfo';
import NgMessage from '@/renderer/uiComponent/NgMessage';
import { calRules } from '@/renderer/component/template/calRules';
import { LOG_API_BASEURL, WEB_ORIGIN_URL } from '../../utils/renderSetting';
import { Spin, message } from 'antd';
import { MenuInfo, MenuTypeEnum, ReportMenu } from './component/reportMenu';
import { flatObj } from '../manage/components/manageDevice/config';
import { calTbsChartData } from '../../component/template/calTemplate';
import { NgLoading } from '../../uiComponent/NgLoading';
type Props = {};
const Report = (props: Props & IntlPropType) => {
  const location = useLocation();
  const { contextHolder, error } = NgMessage.useMessage();
  const intl = useIntl();
  const [license] = useRecoilState(useLicenseAtom);
  const [columns, setColumns] = useState<ColumnType<ISubjectResponse>[]>([]);
  const [logs, setLogs] = useState<any[]>([]);
  const [reportData, setReportData] = useState<IReportModel[]>([]);
  const [allreportData, setAllReportData] = useState<IReportModel[]>([]);
  const [accuracyLogs, setAccuracyLogs] = useState<any>([]);
  const [tableColumns, setTableColumns] = useState<any>(getTreatmentReportColumns(intl, location.state?.type === 1));
  const [subjectInfo, setSubjectInfo] = useState<ISubjectResponse>();
  const osInfo = useRecoilValue(osUserInfo);
  const deviceType: DeviceType = /macintosh|mac os x/i.test(navigator.userAgent) ? 2 : 1;
  const [modalVisible, setModalVisible] = useState(false);
  const [uploading, _setUploading] = useState<boolean>(false);
  const [isShowMessage, setIsShowMessage] = useState(false);
  const [pageType, setPageType] = useState(MenuTypeEnum.normal);
  const [menuList, setMenuList] = useState<MenuInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [visibleInfo, setVisibleInfo] = useState<{ [key: string]: boolean }>({});
  const m200Api = getM200ApiInstance();
  const fetchSubject = async () => {
    let res = await m200Api.getSubjectById(location.state.subject_model.id);
    setSubjectInfo(res);
    setColumns(getSubjectTableColumns(res, intl) as ColumnType<ISubjectResponse>[]);
  };

  const parseReport = (res: IReportModel[]) => {
    setReportData([...res]);
    let reportLogs = res.map((item, index) => {
      let detail = calRules(item.stimulus_data as any, 0).reduce((value, v) => {
        if (v.key === 'relative_strength') {
          return value;
        }

        return `${value}${renderLabel(v.key, item.stimulus_data, intl, true)}：${item.stimulus_data[v.key] || '--'}` + '\n';
      }, '');
      const { pulse_total } = dealUndefine(calTbsChartData(item.stimulus_data as any));
      detail = `总脉冲数：${ pulse_total }\n${detail}`;

      return {
        // eslint-disable-next-line @typescript-eslint/restrict-plus-operands
        motion_threshold: item.motion_threshold,
        ...{
          ...(location.state?.type === 1 ? {} : { target_name: item.target_data.target_name }),
        },
        type: stimulusTypeEnum[item.stimulus_data.type],
        treatment_detail: detail,
        planned_stimulation_duration: formatSeconds(item.planned_stimulation_duration),
        stimulus_treat_time: formatSeconds((item.stimulus_end_time! - item.stimulus_start_time) / 1000),
        actual_stimuluation_duration: formatSeconds(item.actual_stimulation_duration),
        relative_strength: item?.strength_data_list
          ?.map(dataItem => {
            return `${dataItem?.actual_strength}  ${moment(dataItem?.stimulus_time).format('YYYY-MM-DD HH:mm:ss')}`;
          })
          .reduce((value, dataItem, idx) => {
            if (idx !== 0) {
              value += `\n${dataItem}`;
            } else {
              value += dataItem;
            }

            return value;
          }, ''),
        stimulus_pause_count: item.stimulus_pause_count,
        stimulus_start_time: item.stimulus_start_time ? moment(item.stimulus_start_time).format('YYYY-MM-DD HH:mm:ss') : '',
      };
    });
    setLogs([...reportLogs]);
    setLoading(false);
  };

  const splitFetchAccuracy = async (index = 0): Promise<any[]> => {
    const len = allreportData.length;
    if (len <= index) return [];

    try {
      const res = await fetch(
        `${LOG_API_BASEURL()}/api/v1/report/json/summary/${allreportData
          .slice(index, index + 100)
          .map(v => v.uuid)
          .filter(v => v)
          .join(',')}`
      ).then((response: any) => response.json());

      return [...res.data, ...(await splitFetchAccuracy(index + 100))];
    } catch (err: any) {
      return [];
    }
  };

  const parseAccuracyLogs = async () => {
    if (accuracyLogs.length) {
      setReportData(accuracyLogs);

      return;
    }
    setLoading(true);
    try {
      const list = await splitFetchAccuracy();
      setAccuracyLogs(list.sort((a, b) => new Date(b.pulseOverTime).valueOf() - new Date(a.pulseOverTime).valueOf()));
      setReportData(list);
    } catch (e) {
      //
    } finally {
      setLoading(false);
    }
  };

  const fetchReport = async (type_list: number[]) => {
    let res = await m200Api.getReport(location.state.id, type_list);
    const curMenu = initMenuList.find(v => v.type === pageType)!.value;
    setAllReportData([...res]);
    parseReport(res.filter(v => curMenu.some(val => val === v.type)));
  };

  const handleClickMenu = (type: MenuTypeEnum, activeKey: number[]) => {
    setPageType(type);
    if (type === MenuTypeEnum.log) {
      setTableColumns(accuracyColumns.filter(v => !v.hiddenValue || visibleInfo[v.hiddenValue]));
      // eslint-disable-next-line @typescript-eslint/no-floating-promises
      parseAccuracyLogs();
    } else {
      setTableColumns(getTreatmentReportColumns(intl, location.state?.type === 1));
      parseReport(allreportData.filter(v => activeKey.some(val => val === v.type)));
    }
  };

  const isNoImage = () => {
    return location.state?.type === 1;
  };

  const initConfig = async () => {
    const [techsupportInfo, visiblePart] = await m200Api.getControlConfig();
    const list = techsupportInfo.mainControl && !isNoImage() ? initMenuList : initMenuList.filter(v => v.type !== MenuTypeEnum.log);
    setMenuList(list);
    setVisibleInfo(flatObj(visiblePart));
  };

  useAsyncEffect(async () => {
    await initConfig();
    await fetchSubject();
    await fetchReport([1, 2, 3]);
  }, []);
  //  下载
  const handleDownload = async () => {
    try {
      const list = await window.fileAPI.getFolderInfo(deviceType === DeviceType.mac ? '/' : osInfo.filePath!);
      if (list.length === 0) {
        throw new Error('');
      }
      setModalVisible(true);
    } catch (e) {
      if (isShowMessage) return;
      setIsShowMessage(true);
      // eslint-disable-next-line @typescript-eslint/no-floating-promises
      error({
        content: intl.formatMessage({ id: '未检测到移动设备' }),
        onClose: () => setIsShowMessage(false),
      });
      setModalVisible(false);
    }
  };

  return (
    <Spin spinning={loading} indicator={<div><NgLoading /></div>}  wrapperClassName={styles.container_spinning}>
      <div className={styles.container}>
        {contextHolder}
        <NgSelectFileModal
          okText={'存储'}
          maskClosable={false}
          uploadType={UploadType.download}
          filepath={deviceType === DeviceType.mac ? '/Volumes' : osInfo?.filePath!}
          open={modalVisible}
          onCancel={() => setModalVisible(false)}
          width={770}
          controlLoading={uploading}
          onOk={async files => {
            try {
              if (!subjectInfo) return;
              let date = moment().format('YYYYMMDD');
              let time = moment().format(
                intl.formatMessage(
                  { id: '{h}时{m}分{s}秒' },
                  {
                    h: new Date().getHours(),
                    m: new Date().getMinutes(),
                    s: new Date().getSeconds(),
                  }
                )
              );
              const optFileName = `${subjectInfo.name}-${date}-${time}.docx`.replace(/[^a-zA-Z0-9_\.\-\u4e00-\u9fa5]/g, '_');
              const fileName = `${files[0].path}/${optFileName}`;
              let fileUrl = `file://${await window.fileAPI.getDirName()}colormap/${
                location.state.type !== 1 ? 'cureTemplate.docx' : 'cureTemplateNoImage.docx'
              }`;
              let devUrl = `${WEB_ORIGIN_URL()}/colormap/${location.state.type !== 1 ? 'cureTemplate.docx' : 'cureTemplateNoImage.docx'}`;
              let isWriteSuccess = await onDownloadReport(
                {
                  plan: subjectInfo,
                  logs,
                  fileName,
                },
                props.intl,
                isDev ? devUrl : fileUrl
              );
              if (isWriteSuccess) {
                setModalVisible(false);
                // eslint-disable-next-line @typescript-eslint/no-floating-promises
                message.success('操作成功');
              }
            } catch (err) {
              setModalVisible(false);
              // eslint-disable-next-line no-void, @typescript-eslint/no-floating-promises
              error({
                content: intl.formatMessage({ id: '未检测到移动设备' }),
              });
            }
          }}
        />
        <div className={styles.headerContainer}>
          <div className={styles.bread}>
            <NgBreadcrumb isGray={false} />
          </div>
          <CameraAndCoil />
        </div>

        <div className={styles.reportContainer}>
          <div className={styles.title}>
            <div />
            <p style={{ position: 'absolute', left: '50%', transform: 'translateX(-50%)' }}>
              {pageType === MenuTypeEnum.log ? intl.formatMessage({ id: '精度记录' }) : intl.formatMessage({ id: '脉冲治疗记录' })}
            </p>
            {pageType !== MenuTypeEnum.log && (
              <NgDarkButton disabled={license?.hasLicenseError} onClick={handleDownload}>
                {intl.formatMessage({ id: '下载' })}
              </NgDarkButton>
            )}
          </div>

          <ReportMenu list={menuList} activeId={pageType} onClick={handleClickMenu} />

          <div className={styles.tableContainer}>
            <div className={styles.userTable}>
              <NgTable
                columns={columns}
                data={seizeASeatData}
                pagination={{
                  hideOnSinglePage: true,
                  showSizeChanger: false,
                  showQuickJumper: false,
                }}
                bordered
              />
            </div>
            <div className={classnames(styles.treatmentTable, styles.userTable)}>
              <NgTable<IReportModel>
                rowKey={record => uniqueId(`${record.id}-`)}
                columns={tableColumns}
                data={reportData}
                scroll={{
                  y: 650,
                }}
                pagination={false}
                bordered
              />
            </div>
          </div>
        </div>
      </div>
    </Spin>
  );
};
export default injectIntl(Report);
