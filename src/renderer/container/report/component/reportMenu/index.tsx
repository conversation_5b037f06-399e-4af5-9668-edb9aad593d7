import React from 'react';
import styles from './index.module.less';
import classNames from 'classnames';

export enum MenuTypeEnum {
  normal = 'normal',
  all = 'all',
  log = 'log',
}

export type MenuInfo = {
  label: string;
  value: number[];
  type: MenuTypeEnum;
};

type Props = {
  list: MenuInfo[];
  activeId: MenuTypeEnum;
  onClick(type: MenuTypeEnum, value: number[]): void;
};

export const ReportMenu = (props: Props) => {
  const { list, activeId, onClick } = props;

  return <div className={styles.container}>
    {list.map(v => <p key={v.label} onClick={() => onClick(v.type, v.value)} className={classNames(styles.menu_item, activeId === v.type? styles.menu_item_actived: '')}>
      {v.label}
    </p>)}
  </div>;
};
