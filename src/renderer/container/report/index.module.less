@import '@/renderer/static/style/baseColor.module.less';

.container_spinning {
  height: 100%;
  position: relative;

  :global {
    .ant-spin-container {
      height: 100%;
    }

    .ant-spin {
      max-height: 100% !important;
    }
  }
}

.container {
  .headerContainer {
    height: 90px;
    display: flex;
    align-items: center;
    padding: 20px 20px 24px 20px;
  }

  .bread {
    position: fixed;
    top: 20px;
    left: 20px;
  }

  .photo {
    margin: 0 auto;
  }

  .reportContainer {
    padding: 20px 76px 0 229px;
    position: relative;

    .title {
      display: flex;
      justify-content: space-between;
      margin: 0 0 40px;
      position: relative;
      line-height: 32px;
      height: 32px;

      & div:first-child+p {
        font-size: 16px;
        text-align: center;
        font-weight: 500;
        margin: 0;
      }

      :global {
        & button:disabled {
          color: @colorA9  !important;
          background-color: @colorA6  !important;
        }
      }
    }
  }

  .treatmentTable {
    margin-top: 30px;

    :global {
      .ant-table-body {
        &::-webkit-scrollbar {
          border-radius: 0;
          width: 0;
        }
      }

      .ant-table-wrapper .ant-table .ant-table-header {
        border-radius: 0;
      }

      .ant-table-measure-row {
        &>td {
          border-bottom: 0 !important;
        }
      }

      .ant-table-container table {
        border-bottom: 0 !important;
      }

      .ant-table-wrapper .ant-table-thead>tr>th {
        font-weight: 350 !important;
        color: @colorA12  !important;
      }

      .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-content>table>tbody>tr>td {
        border-inline-end: 1px solid @colorA5  !important;
      }

      .ant-table-wrapper .ant-table-tbody>tr>td {
        border-inline-end: 1px solid @colorA5  !important;
        border-bottom-color: @colorA5  !important;
      }

      .ant-table-wrapper .ant-table-tbody .ant-table-row:last-child {
        & td {
          border-bottom: 0 !important;
        }
      }

      .ant-table-row:last-child {
        border-bottom: 0 !important;
      }

      .ant-table-content .ant-table-thead>tr>th {
        background: @colorA3  !important;
      }

      .ant-table-wrapper .ant-table-container table>thead>tr:first-child>*:first-child {
        border-start-start-radius: 0 !important;
      }
    }
  }

  .userTable {
    :global {
      .ant-table-wrapper .ant-table-thead>tr>th {
        color: @colorA10;
        font-weight: normal;
        font-family: normal-font, serif !important;
      }

      .ant-table-wrapper .ant-table-tbody>tr>td {
        padding: 0 8px 0 8px !important;
      }

      .ant-table-cell {
        background: @colorA1;
        padding: 0 7px 0 8px;
      }

      .ant-table-wrapper .ant-table-thead>tr>td {
        border-color: @colorA5;
      }

      .ant-table-container table {
        border-radius: 0 !important;
      }

      .ant-table-container {
        border: 1px solid @colorA5  !important;
      }

      .ant-table-wrapper .ant-table-container,
      .ant-table-wrapper .ant-table-container table>thead>tr:first-child>*:last-child {
        border-radius: 0 !important;
      }

      .ant-table-thead>tr>th {
        background: transparent !important;
        color: #e8e8ea;
        border-bottom: 1px solid @colorA5  !important;
        border-inline-end: 1px solid @colorA5  !important;

        &:last-child {
          border-inline-end: 0 !important;
        }
      }

      .ant-table-wrapper .ant-table-tbody>tr.ant-table-row {
        background: transparent !important;
      }

      .ant-table-wrapper .ant-table-tbody>tr.ant-table-row:hover>td {
        background: transparent !important;
      }
    }
  }
}

.detail {
  & div {
    text-align: left !important;
  }
}

.detailBox {
  padding: 18px 0 20px 0;
}

.wrapContainer {
  position: relative;

  .textTitle {
    position: absolute;
  }

  .message {
    width: 180px;
    text-indent: 43px;
  }

  .code {
    text-indent: 28px;
  }
}