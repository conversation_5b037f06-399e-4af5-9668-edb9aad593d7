import { FormInstance } from 'antd';
import { IntlShape } from 'react-intl';

export interface CreatPlanModalHookType {
  intl: IntlShape;
  paramasForm: FormInstance;
  setFormValues: React.Dispatch<any>;
  strengthForm: FormInstance;
  setActiveStrength: React.Dispatch<React.SetStateAction<number>>;
  setErrorStrengthFields: React.Dispatch<React.SetStateAction<string[]>>;
  nameForm: FormInstance;
}
