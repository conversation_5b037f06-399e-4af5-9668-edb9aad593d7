import { IntlShape } from 'react-intl';
import { CreatPlanModalHookType } from './type';
import { TreatStimulateModel } from '../type';
import { calRules, getFields, normalFields, tbsFields } from '../../../../../component/template/calRules';
import { sendRenderLog } from '../../../../../utils/renderLogger';
import { EnumPlanStimulusType } from '../../../../../../common/types';
import { TbsFieldType } from '../../../../../component/template';
import { calTbsChartData } from '../../../../../component/template/calTemplate';
import { validatorCTBSRules } from '../../../../../component/template/validatorFun';

class CreatPlanModal {
  private modelHook?: CreatPlanModalHookType;

  initWithHook = (modelHook: CreatPlanModalHookType) => {
    this.modelHook = modelHook;
  };

  /**
   * stimulate默认值
   */
  stimulateDefault = (): TreatStimulateModel => {
    return {
      name: '',
      actual_strength: undefined,
      type: EnumPlanStimulusType.TBS,
      relative_strength: 0,
      pulse_total: 0,
      treatment_time: 0,
    };
  };

  /**
   * 获取handleOK的函数
   */
  handleOkAction = async (formValues: any, onOk: (stimulate: TreatStimulateModel) => Promise<void>) => {
    if (!this.modelHook) return;
    const { strengthForm, setFormValues, nameForm } = this.modelHook;

    setFormValues(this.replaceUndefinedWithNull(formValues));
    try {
      const nameFormValues = await nameForm.validateFields();
      const name = nameFormValues.plan_name ? nameFormValues.plan_name : '';
      const strengthFormValues = await strengthForm.validateFields();
      const actual_strength = strengthFormValues.active_strength ? strengthFormValues.active_strength : 0;
      await this.validateTemplateForm(actual_strength, name, onOk);
    } catch (e) {
      sendRenderLog.info(`连续刺激设置治疗参数有误：${JSON.stringify(e)}`);
    }
  };

  /**
   * 获取Rule函数
   */
  validatorStrengthForm =
  (minValue: number, maxValue: number, keyParam: string, formValues: any, isTechSupport?: boolean) =>
    async (_: any, value: number): Promise<string | void> => {
      if (!this.modelHook) return;
      const { intl, setErrorStrengthFields } = this.modelHook;

      try {
        // 验证普通空 和范围错误
        await this.validateCommonRules(value, minValue, maxValue, 0, intl);

        // 如果左侧有超限报错，这里不校验功率报错
        const { pulse_total, treatment_time } = calTbsChartData(formValues);
        if (this.checkInvalidate(pulse_total, treatment_time)) {
          setErrorStrengthFields([]);

          return Promise.resolve('');
        }
        if (pulse_total && treatment_time && !isTechSupport) {
          await validatorCTBSRules(100, formValues, 'relative_strength', intl);
        }
        setErrorStrengthFields([]);
      } catch (error) {
        if (value !== undefined) {
          setErrorStrengthFields([keyParam]);
        }

        return Promise.reject(error);
      }
    };

  /**
   * 实际强度表单
   */
  onChangeStrengthForm = (changeValues: any, formValues: any) => {
    if (!this.modelHook) return;
    const { setActiveStrength } = this.modelHook;

    setActiveStrength(Number(changeValues.active_strength));
    formValues.relative_strength = changeValues.active_strength;
    this.setStateValues(formValues);
  };

  /**
   * 修改了刺激类型, 重置表单
   */
  onChangeFormValues = (changeValues: any, values: any, relative_strength: number, isTechSupport: boolean | undefined) => {
    if (!this.modelHook) return;
    const { paramasForm, setFormValues } = this.modelHook;

    if (changeValues.type) {
      let key = Object.getOwnPropertyNames(changeValues)[0];
      let value = changeValues[key];
      let renderFields: TbsFieldType[] = calRules(values, 0, '', isTechSupport);
      let template = renderFields.reduce((result: any, item: TbsFieldType) => {
        return { ...result, [item.key]: undefined };
      }, {});
      const fieldsValue = { type: value, ...template, relative_strength };
      paramasForm.setFieldsValue(fieldsValue);
      setFormValues(fieldsValue);
    } else {
      this.setStateValues(values);
    }
  };

  /**
   * 设置参数配置
   */
  private setStateValues = (newParams: any) => {
    if (!this.modelHook) return;
    const { paramasForm, strengthForm, setFormValues } = this.modelHook;

    paramasForm.setFieldsValue({
      ...newParams,
      intermission_time: this.getMissionTime(newParams),
    });
    setFormValues({
      ...newParams,
      intermission_time: this.getMissionTime(newParams),
    });
    if (this.changeIsComplete(newParams, newParams.type)) {
      strengthForm
        .validateFields()
        .then(() => {
          /** 结果处理*/
        })
        .catch(e => {
          sendRenderLog.info(`repeatTreatPage:表单验证发生参数输入内容错误:${JSON.stringify(e)}`);
        });
    }
  };

  private getMissionTime = (newParams: any) => {
    return newParams.strand_pulse_count === 1 ? undefined : newParams.intermission_time;
  };

  private changeIsComplete = (values: any, type: EnumPlanStimulusType) => {
    let renderFields: TbsFieldType[] = this.getRenderFields(type);
    const fieldObj = renderFields
      .map(v => v.key)
      .filter((v: any) => {
        if (values.strand_pulse_count === 1) {
          return v !== 'intermission_time';
        } else {
          return true;
        }
      });

    return fieldObj.every(v => {
      return values[v] && values[v] > 0;
    });
  };

  private getRenderFields = (type: EnumPlanStimulusType) => {
    return type === EnumPlanStimulusType.TBS ? tbsFields : normalFields;
  };

  private validateCommonRules = async (
    value: any,
    minValue: number,
    maxValue: number,
    precision: number,
    intl: IntlShape
  ): Promise<string | void> => {
    if (value === '' || value === null || value === undefined) {
      return Promise.reject(intl.formatMessage({ id: '不可为空' }));
    }
    const numberValue = Number(value);

    return new Promise((resolve, reject) => {
      if (this.checkNumberTail(numberValue) > precision) {
        return reject(intl.formatMessage({ id: '不符合限制' }));
      }
      if (numberValue < minValue) {
        return reject(intl.formatMessage({ id: '不符合限制' }));
      }
      if (numberValue > maxValue) {
        return reject(intl.formatMessage({ id: '不符合限制' }));
      }

      resolve();
    });
  };

  /**
   * 判断小数位数
   */
  private checkNumberTail = (value: number) => {
    const parts = value.toString().split('.');

    return parts.length > 1 ? parts[1].length : 0;
  };

  private checkInvalidate = (pulseTotal: number, treatmentTime: number) => {
    if (pulseTotal === 0 || pulseTotal > 65535) {
      return true;
    }

    return treatmentTime < 3 || treatmentTime > 65535;
  };

  /**
   * 替换undefined
   */
  private replaceUndefinedWithNull = (formValues: any): any => {
    const fields = getFields(formValues, 0);
    let newValues = fields.reduce(
      (res: any, cur: any) => {
        let temp: any = {};
        temp[cur.key] = formValues[cur.key] === undefined ? null : formValues[cur.key];

        return Object.assign({}, res, temp);
      },
      { type: formValues.type }
    );

    return newValues;
  };

  /**
   * 验证模版
   */
  private validateTemplateForm = async (actual_strength: number, name: string, onOk: (stimulate: TreatStimulateModel) => Promise<void>) => {
    if (!this.modelHook) return;
    const { paramasForm, setErrorStrengthFields } = this.modelHook;

    return paramasForm
      .validateFields()
      .then(async res => {
        await onOk(Object.assign({}, res, { actual_strength, name }));
        setErrorStrengthFields([]);
      })
      .catch(error => {
        throw error;
      });
  };
}

export const creatPlanModal = new CreatPlanModal();
