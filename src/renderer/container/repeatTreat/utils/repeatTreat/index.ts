import { RepeatTreatHookType, StimulusItemModel, TreatStimulateModel, UpdateStimulateParams } from './type';
import { sendRenderLog } from '../../../../utils/renderLogger';
import { EnumPlanStimulusType } from '../../../../../common/types';
import { calTbsChartData, disableBeatOfTemp } from '../../../../component/template/calTemplate';
import { TmsResponseEnum, TMSScreenState } from '../../../../../common/constant/tms';
import { FaultStatusEnum } from '../../../../../common/systemFault/type';

import { M200Api } from '../../../../../common/api/ng/m200Api';
import { getM200ApiInstance } from '../../../../../common/api/ngApiAgent';

class RepeatTreatModel {
  private modelHook?: RepeatTreatHookType;
  private m200Api: M200Api;

  constructor() {
    this.m200Api = getM200ApiInstance();
  }

  initWithHook = (modelHook: RepeatTreatHookType) => {
    this.modelHook = modelHook;
  };

  /**
   * 温度检查，看是否可进行下一个刺激
   */
  checkisDisableTreat = (temperature: number, stimulate: TreatStimulateModel) => {
    const disable = disableBeatOfTemp(temperature, stimulate.actual_strength || 0, stimulate, true);

    if (disable || temperature > 41) {
      sendRenderLog.info(`连续刺激治疗温度不达标：${temperature}`);

      return true;
    } else {
      sendRenderLog.info(`连续刺激治疗温度达标：${temperature}`);

      return false;
    }
  };

  /**
   * 给TMS发送参数
   */
  sendParamsToTms = async (params: StimulusItemModel, tid: string) => {
    let status: TmsResponseEnum;
    if (params.type === EnumPlanStimulusType.TBS) {
      status = await this.sendParamsTBSToTms(params, tid);
    } else {
      status = await this.sendParamsRTMSToTms(params, tid);
    }

    return status === TmsResponseEnum.success;
  };

  /**
   * 给TMS发送TBS参数
   */
  private sendParamsTBSToTms = async (params: StimulusItemModel, tid: string) => {
    const param = {
      action: 'set_treatment_plan',
      level: params.actual_strength, // 实际强度
      frequency: Math.round(params.plexus_inner_frequency * 100), // 丛内频率
      intensity: Math.round(params.plexus_inter_frequency * 100), // 丛间频率
      count: params.plexus_inner_pulse_count, // 丛内脉冲数
      bunch: params.plexus_count, // 刺激丛数
      series: params.strand_pulse_count, // 刺激串数
      pause: params.strand_pulse_count === 1 ? 1 : params.intermission_time, // 刺激间隔  间歇时间
      sum: params.pulse_total, // 总脉冲数
      time: params.treatment_time, // 总刺激时间
      tid: tid, // uuid
    };
    try {
      const data = await window.tmsAPI.set_treatment_plan(param);
      if (data.data.result === TmsResponseEnum.failure) {
        sendRenderLog.info(`连续刺激治疗设置TBS治疗参数报错：${JSON.stringify(params)}`);
      }

      return data.data.result;
    } catch (error) {
      sendRenderLog.info(`连续刺激治疗设置治疗tms通讯出错：${JSON.stringify(error)}`);

      return TmsResponseEnum.failure;
    }
  };

  /**
   * 给TMS发送RTMS参数
   */
  private sendParamsRTMSToTms = async (params: StimulusItemModel, tid: string) => {
    const param = {
      action: 'set_treatment_plan',
      level: params.actual_strength, // 实际强度
      frequency: Math.round(params.strand_pulse_frequency! * 100), // 串脉冲频率
      intensity: Math.round(params.strand_pulse_frequency! * 100), // 串脉冲频率
      count: 1,
      bunch: params.inner_strand_pulse_count, // 串内脉冲数
      series: params.strand_pulse_count, // 串数
      pause: params.strand_pulse_count === 1 ? 1 : params.intermission_time, // 刺激间隔  间歇时间
      sum: params.pulse_total, // 总脉冲数
      time: params.treatment_time, // 总刺激时间
      tid: tid, // uuid
    };
    try {
      const data = await window.tmsAPI.set_treatment_plan(param);
      if (data.data.result === TmsResponseEnum.failure) {
        sendRenderLog.info(`连续刺激治疗设置RTMS治疗参数报错：${JSON.stringify(data)}`);
      }

      return data.data.result;
    } catch (error: any) {
      sendRenderLog.info(`连续刺激治疗设置治疗tms通讯出错：${JSON.stringify(error)}`);

      return TmsResponseEnum.failure;
    }
  };

  /**
   * 开始刺激治疗
   */
  treatPlanStart = async (tid?: string): Promise<boolean> => {
    try {
      const data = await window.tmsAPI.noImage_treatment_plan_start('PlanStart', tid);
      if (data?.data.result === TmsResponseEnum.success) {
        sendRenderLog.info(`连续刺激开始成功： ${JSON.stringify(data)}`);
        await this.resetTmsTreatStatus(TMSScreenState.PlanTreat);

        return true;
      } else {
        sendRenderLog.info(`连续刺激开始报错： ${JSON.stringify(data)}`);

        return false;
      }
    } catch (error: any) {
      sendRenderLog.info(`连续刺激TMS通讯故障： ${JSON.stringify(error)}`);

      return false;
    }
  };

  /**
   * 设置拍子状态
   */
  resetTmsTreatStatus = async (status: number) => {
    await window.tmsAPI.set_beat_screen(status);
  };

  /**
   * 刺激中轮询治疗
   */
  queryTreatmentData = async (stimulate: TreatStimulateModel, onOver: () => void, tid?: string) => {
    if (!this.modelHook) return;
    const { checkTreatStatusRef } = this.modelHook;

    if (checkTreatStatusRef.current) {
      clearInterval(checkTreatStatusRef.current);
    }

    checkTreatStatusRef.current = setInterval(async () => {
      const queryData = await window.tmsAPI.query_treatment();
      this.handleSetResidualTime(stimulate.treatment_time, queryData);
      if (this.checkIsStatusError(queryData.data, stimulate)) {
        clearInterval(checkTreatStatusRef.current);

        sendRenderLog.info(`连续刺激治疗出现故障:${JSON.stringify(stimulate)}`);
        window.systemAPI.pushSystemFault({ '0A030005': FaultStatusEnum.abnormal }, '连续刺激上下微机状态不一致');
      }
      if (this.checkIsOver(queryData.data, stimulate)) {
        clearInterval(checkTreatStatusRef.current);
        await this.treatmentPlanEnd(tid);
        onOver();
        sendRenderLog.info(`连续刺激治疗结束:${JSON.stringify(stimulate)}`);
      }
    }, 1000);
  };

  /**
   * 刺激结束并清空定时器
   */
  treatmentPlanEnd = async (tid?: string) => {
    if (!this.modelHook) return;
    const { checkTreatStatusRef } = this.modelHook;

    try {
      await window.tmsAPI.set_beat_screen(TMSScreenState.NotStarted);
      await window.tmsAPI.noImage_treatment_plan_start('PlanEnd', tid);
      clearInterval(checkTreatStatusRef.current);
    } catch (error: any) {
      sendRenderLog.info(`连续刺激治疗结束失败:${JSON.stringify(error)}`);
    }
  };

  /**
   * 状态是否异常
   */
  private checkIsStatusError = (tmsData: any, stimulateParam: TreatStimulateModel) => {
    const isError = tmsData.result === TmsResponseEnum.success && tmsData.count < stimulateParam.pulse_total && tmsData.state === 0;

    return isError;
  };

  /**
   * 检查是否结束
   */
  private checkIsOver = (tmsData: any, stimulateParam: TreatStimulateModel) => {
    return (
      tmsData.result === TmsResponseEnum.success &&
      tmsData.state === 0 &&
      (tmsData.time === stimulateParam.treatment_time || tmsData.count === stimulateParam.pulse_total)
    );
  };

  /**
   * 设置刺激剩余时间:props.stimulate.
   */
  private handleSetResidualTime = (treatment_time: number, queryData: any) => {
    if (!this.modelHook) return;
    const { setResidualTime, residualTimeRef } = this.modelHook;

    if (queryData.data.remain_time === undefined) return;

    const time = queryData.data.remain_time !== undefined ? queryData.data.remain_time : treatment_time - queryData.data.time;
    setResidualTime(time);
    residualTimeRef.current = time;
  };

  /**
   * stimulate默认值
   */
  stimulateDefault = (): TreatStimulateModel => {
    return {
      name: '',
      actual_strength: undefined,
      type: EnumPlanStimulusType.TBS,
      relative_strength: 0,
      pulse_total: 0,
      treatment_time: 0,
    };
  };

  /**
   * 添加一个刺激
   */
  addStimulus = async (stimulate: TreatStimulateModel) => {
    const { pulse_total, treatment_time } = calTbsChartData(stimulate);
    const requestParams = {
      ...stimulate,
      actual_strength: stimulate.actual_strength || 0,
      treatment_time: treatment_time,
      pulse_total: pulse_total,
    };

    return this.m200Api.addStimulus(requestParams);
  };

  /**
   * 查询刺激列表
   */
  getStimulusList = async (): Promise<StimulusItemModel[]> => {
    const res = await this.m200Api.stimulusList();

    return res;
  };

  /**
   * 更新刺激列表
   */
  updateStimulusList = async (params: UpdateStimulateParams) => {
    return this.m200Api.setOrderStimulus(params);
  };

  /**
   * 删除刺激
   */
  deleteStimulusItem = async (id: Number) => {
    return this.m200Api.deleteStimulusItem(id);
  };
}

export const repeatTreatModel = new RepeatTreatModel();
