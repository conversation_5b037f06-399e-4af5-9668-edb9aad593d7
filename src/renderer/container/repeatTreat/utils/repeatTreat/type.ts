import { EnumPlanStimulusType, PlanStimulusModel } from '../../../../../common/types';

export interface RepeatTreatHookType {
  checkTreatStatusRef: React.MutableRefObject<any>;
  residualTimeRef: React.MutableRefObject<number>;
  setResidualTime: React.Dispatch<React.SetStateAction<number>>;
}

/**
 * 新建刺激方案的传参
 */
export interface AddStimulusParams {
  /** 刺激类型 1.SINGLE(单刺激) 2.rTMS(重复刺激) 3.iTBS(刺激间歇性的TBS) 4.cTBS(刺激连续的丛状刺激称) 5.TBS(爆发式刺激) */
  type: number;
  /** 实际强度 */
  actual_strength: number;
  /** 名称*/
  name?: string;
  /** 串脉冲频率 */
  strand_pulse_frequency?: number;
  /** 串内脉冲数 */
  inner_strand_pulse_count?: number;
  /** 丛内频率 */
  plexus_inner_frequency?: number;
  /** 丛间频率 */
  plexus_inter_frequency?: number;
  /** 丛内脉冲数 */
  plexus_inner_pulse_count?: number;
  /** 刺激丛数 */
  plexus_count?: number;
  /** 脉冲串数 */
  strand_pulse_count?: number;
  /** 刺激间隔 */
  intermission_time?: number;
  /** 脉冲数(前端计算出的值) */
  pulse_total?: number;
  /** 刺激总时长 */
  treatment_time?: number;
}

/**
 * 刺激方案列表的对象返参
 */
export interface StimulusItemModel {
  /** 名称*/
  name: string;
  /** 刺激类型*/
  type: EnumPlanStimulusType;
  /** 刺激的id */
  id: number;
  /** 实际强度 */
  actual_strength: number;
  /** 串内脉冲数 */
  inner_strand_pulse_count?: number;
  /** 刺激丛数 */
  plexus_count: number;
  /** 刺激间隔 */
  intermission_time: number;
  /** 丛内频率 */
  plexus_inner_frequency: number;
  /** 丛内脉冲数 */
  plexus_inner_pulse_count: number;
  /** 丛间频率 */
  plexus_inter_frequency: number;
  /** 脉冲串数 */
  strand_pulse_count: number;
  /** 刺激总时长 */
  treatment_time: number;
  /** 脉冲数 */
  pulse_total?: number;
  /** 串脉冲频率 */
  strand_pulse_frequency?: number;
  /** 创建的时间戳*/
  created_at?: number;
  /** 创建者的id*/
  created_id?: number;
  /** 排序的位置*/
  sort_index?: number;
  /** trace_id*/
  trace_id?: string;
  /** 更新的时间戳*/
  updated_at?: number;
  /** 更新的id*/
  updated_id?: number;
  /** 刺激的次数*/
  treat_count?: number;
}

/**
 * 更新刺激列表的入参约束
 */
export interface UpdateStimulateParams {
  continue_stimulus_id_list: number[];
}

/**
 * 导出StimulateModel
 */
export type TreatStimulateModel = PlanStimulusModel & { actual_strength?: number };

/**
 * 连续刺激的状态枚举
 * notTreating 未开始刺激
 * treating： 刺激中
 * processing: 连续刺激的过程中
 */
export enum RepeatTreatStatusEnum {
  notTreating = 0,
  treating = 1,
  processing = 2,
}

/**
 * 设置方案参数之后，延迟开始的时间
 */
export const INTERVAL_TIME = 10000;
