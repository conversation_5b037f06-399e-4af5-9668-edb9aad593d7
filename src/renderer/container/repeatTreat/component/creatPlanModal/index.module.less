@import '../../../../static/style/baseColor.module.less';

.createPlanModal {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .moadalContent {
    width: 260px;
  }

  .modalFooter {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    margin-top: 30px;
    width: 100%;
  }

  :global {
    button#creatPlanConfirm {
      width: 80px;
      color: @colorA1;
      background-color: @colorC4 !important;
      margin-left: 40px;
    }

    button#creatConfirm {
      margin-left: 40px;
    }

    .ant-modal-body {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }

    span.ant-input-data-count {
      color: @colorA9 !important;
    }

    .relative_strength {
      display: none;
    }
  }
}
