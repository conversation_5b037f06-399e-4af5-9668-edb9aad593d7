import React, { FC, useEffect, useState } from 'react';
import styles from './index.module.less';
import { Form } from 'antd';
import { Rule } from 'antd/es/form';
import { useIntl } from 'react-intl';
import NgModal from '@/renderer/uiComponent/NgModal';
import { NgForm } from '@/renderer/uiComponent/NgForm';
import { TBSChart } from '@/renderer/component/tbsChart';
import { EditStimulateTemplate } from '@/renderer/component/template';
import { NgInputNumber } from '@/renderer/uiComponent/NgInputNumber';
import { NgTextArea } from '@/renderer/uiComponent/NgTextarea';
import NgButtonText from '@/renderer/uiComponent/NgButtonText';
import { TreatStimulateModel } from '../../utils/repeatTreat/type';
import { creatPlanModal } from '../../utils/repeatTreat/creatPlanModal';
import NgButton from '../../../../uiComponent/NgButton';
import Icon from '@ant-design/icons/lib/components/Icon';
import { AllowClear } from '../../../../uiComponent/SvgGather';

interface IProps {
  showModal: boolean;
  cancelAction(): void;
  confirmAction(stimulate: TreatStimulateModel): Promise<void>;
}

export const CreatPlanModal: FC<IProps> = (props: IProps) => {
  const intl = useIntl();
  const [paramasForm] = Form.useForm();
  const [strengthForm] = Form.useForm();
  const [nameForm] = Form.useForm();
  const { showModal, cancelAction, confirmAction } = props;
  const [formValues, setFormValues] = useState<TreatStimulateModel>(creatPlanModal.stimulateDefault());
  const [activeStrength, setActiveStrength] = useState(0);
  const [errorStrengthFields, setErrorStrengthFields] = useState<string[]>([]);

  useEffect(() => {
    creatPlanModal.initWithHook({
      intl,
      paramasForm,
      setFormValues,
      strengthForm,
      setActiveStrength,
      setErrorStrengthFields,
      nameForm,
    });
  }, []);

  /**
   * 修改了刺激类型, 重置表单
   */
  const handleChangeValues = (changeValues: any, values: any) => creatPlanModal.onChangeFormValues(changeValues, values, activeStrength, true);
  const handleChangeStrengthForm = (changeValues: any, _: any) => creatPlanModal.onChangeStrengthForm(changeValues, formValues);
  const getActiveStrengthRule = () => {
    const rules: Rule[] = [{ validator: validatorStrength(10, 100, 'active_strength') }];

    return rules;
  };

  const validatorStrength = (minValue: number, maxValue: number, keyParam: string) => {
    return creatPlanModal.validatorStrengthForm(minValue, maxValue, keyParam, formValues, true);
  };

  const handleConfirmAction = async () => await creatPlanModal.handleOkAction(formValues, confirmAction);

  const getNameRule = (): Rule[] => {
    return [
      {
        validator: async (_: any, value: any) => {
          return new Promise(async (res, rej) => {
            if (!value || !value.trim()) {
              return rej(intl.formatMessage({ id: '不可为空' }));
            }
            if (value.length > 20) {
              return rej(intl.formatMessage({ id: '请输入1-20位' }));
            }
            res('');
          });
        },
      },
    ];
  };

  return (
    <NgModal title="新建" maskClosable={false} footer={<></>} open={showModal} onCancel={cancelAction} width={600} className={styles.createPlanModal}>
      <div className={styles.moadalContent}>
        <NgForm form={nameForm} layout="vertical" className={styles.nameForm}>
          <Form.Item label={<span>{intl.formatMessage({ id: '名称' })}</span>} validateTrigger="onBlur" rules={getNameRule()} name="plan_name">
            <NgTextArea
              maxLength={20}
              className="tem_textarea"
              onPressEnter={e => e.preventDefault()}
              showCount
              allowClear={{ clearIcon: <Icon component={AllowClear} style={{ fontSize: 14, paddingTop: 4 }} rev="" /> }}
              placeholder={intl.formatMessage({ id: '请输入名称' })}
              style={{ height: 35, resize: 'none' }}
            />
          </Form.Item>
        </NgForm>
        <div className={styles.parmasChart}>{<TBSChart template={formValues} motionThreshold={100} />}</div>
        <NgForm
          form={strengthForm}
          layout="horizontal"
          labelAlign={'left'}
          labelCol={{ span: 8 }}
          wrapperCol={{ span: 16 }}
          colon={false}
          className={styles.templateForm}
          onValuesChange={handleChangeStrengthForm}
        >
          <Form.Item
            className={'active_strength'}
            name={'active_strength'}
            key={'active_strength'}
            extra={'10-100'}
            label={<span>{intl.formatMessage({ id: '实际强度（%MO）:' })}</span>}
            wrapperCol={{ span: 12 }}
            labelCol={{ span: 12 }}
            rules={getActiveStrengthRule()}
          >
            <NgInputNumber status={errorStrengthFields.includes('active_strength') ? 'error' : ''} step={1} />
          </Form.Item>
        </NgForm>
        <NgForm
          form={paramasForm}
          layout="horizontal"
          labelAlign={'left'}
          labelCol={{ span: 8 }}
          wrapperCol={{ span: 16 }}
          className={styles.templateForm}
          onValuesChange={handleChangeValues}
        >
          <EditStimulateTemplate motionThreshold={100} formRef={paramasForm} stimulate={formValues} />
        </NgForm>
      </div>
      <div className={styles.modalFooter}>
        <NgButtonText onClick={cancelAction}>{intl.formatMessage({ id: '取消' })}</NgButtonText>

        <NgButton id="creatConfirm" buttonMode={'popover'} onClick={handleConfirmAction}>
          {intl.formatMessage({ id: '确认' })}
        </NgButton>
      </div>
    </NgModal>
  );
};
