@import '../../../../static/style/baseColor.module.less';

.treatPlanItem {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  height: 94px;
  background-color: @colorA4;
  border-radius: 6px;
  margin-bottom: 20px;
  padding: 20px 30px;
  overflow: hidden;
  cursor: url(/src/renderer/static/svg/cursor.cur), pointer !important;

  .treatPlanItemContent {
    span {
      text-align: left;
      width: 200px;
    }

    .treatPlanItemTop {
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      span:last-child {
        width: 400px;
      }
    }

    .treatPlanItemBottom {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      margin-top: 12px;
      color: @colorA9 !important;

      span:last-child {
        width: 400px;
      }
    }
  }
}
