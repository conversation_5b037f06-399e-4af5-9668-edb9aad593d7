import React, { FC } from 'react';
import styles from './index.module.less';
import { RepeatTreatStatusEnum, StimulusItemModel } from '../../utils/repeatTreat/type';
import { secToTime } from '../../../../component/template/calTemplate';
import { NgIcon } from '../../../../uiComponent/NgIcon';
// eslint-disable-next-line import/no-extraneous-dependencies
import { DraggableProvided } from 'react-beautiful-dnd';
import { CrashIcon } from '../../../../uiComponent/SvgGather';

interface IProps {
  repeatTreatStatus: RepeatTreatStatusEnum;
  stimulateItem: StimulusItemModel;
  deleteStimulateById(id: number): void;
  index: number;
  currentTreatIndex: number;
  residualTime: number;
  dragProvided: DraggableProvided;
}

export const TreatPlanItem: FC<IProps> = (props: IProps) => {
  const { dragProvided, stimulateItem, deleteStimulateById, index, currentTreatIndex, repeatTreatStatus, residualTime } = props;

  /**
   * 获取背景色
   */
  const getBackgroundStyle = () => {
    if (index === -1) return;

    if (index < currentTreatIndex) {
      return {
        background: '#41727B',
      };
    } else if (index === currentTreatIndex) {
      if (residualTime === -1) return;
      const progress = (stimulateItem.treatment_time - residualTime) / stimulateItem.treatment_time;

      return {
        background: `linear-gradient(to right, #41727B ${progress * 100}%, #2C2C3C ${progress * 100}%)`,
        transition: 'background 0.3s ease',
      };
    } else {
      return {
        background: '#2C2C3C',
      };
    }
  };

  return (
    <div
      className={styles.treatPlanItem}
      ref={dragProvided.innerRef}
      {...dragProvided.draggableProps}
      {...dragProvided.dragHandleProps}
      style={{
        ...dragProvided.draggableProps.style,
        ...getBackgroundStyle(),
      }}
    >
      <div className={styles.treatPlanItemContent}>
        <div className={styles.treatPlanItemTop}>
          <span>{`实际强度（%MO）: ${stimulateItem.actual_strength}`}</span>
          <span>{`总脉冲数：${stimulateItem.pulse_total}`}</span>
          <span>{`总时长：${secToTime(stimulateItem.treatment_time)}`}</span>
          <span>{`类型：${stimulateItem.type === 2 ? 'rTMS' : 'TBS'}`}</span>
          <span>{`次数：${stimulateItem.treat_count || 0}`}</span>
          <span>{`名称：${stimulateItem.name}`}</span>
        </div>
        {stimulateItem.type === 2 ? (
          <div className={styles.treatPlanItemBottom}>
            <span>{`串脉冲频率（Hz）：${stimulateItem.strand_pulse_frequency || '--'}`}</span>
            <span>{`串内脉冲数（个）：${stimulateItem.inner_strand_pulse_count || '--'}`}</span>
            <span>{`脉冲串数（串）：${stimulateItem.strand_pulse_count || '--'}`}</span>
            <span>{`刺激间隔（秒）：${stimulateItem.intermission_time || '--'}`}</span>
            <span />
            <span />
          </div>
        ) : (
          <div className={styles.treatPlanItemBottom}>
            <span>{`丛内频率（Hz）：${stimulateItem.plexus_inner_frequency || '--'}`}</span>
            <span>{`丛间频率（Hz）：${stimulateItem.plexus_inter_frequency || '--'}`}</span>
            <span>{`丛内脉冲数（个）：${stimulateItem.plexus_inner_pulse_count || '--'}`}</span>
            <span>{`刺激丛数（丛）：${stimulateItem.plexus_count || '--'}`}</span>
            <span>{`脉冲串数（串）：${stimulateItem.strand_pulse_count || '--'}`}</span>
            <span>{`刺激间隔（秒）：${stimulateItem.intermission_time || '--'}`}</span>
          </div>
        )}
      </div>
      <div className={styles.treatPlanItemRight}>
        {index === currentTreatIndex && <div>{`剩余时长：${secToTime(residualTime)}`}</div>}
        {repeatTreatStatus === RepeatTreatStatusEnum.notTreating && (
          <NgIcon
            disabled={false}
            iconSvg={CrashIcon}
            onClick={() => {
              deleteStimulateById(stimulateItem.id);
            }}
          />
        )}
      </div>
    </div>
  );
};
