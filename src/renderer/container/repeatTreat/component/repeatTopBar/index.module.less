@import '../../../../static/style/baseColor.module.less';

.topBar {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  padding: 0 20px;

  & button {
    width: 208px;
    height: 40px;

    & span {
      display: flex;
      align-items: center;

      & svg {
        margin-right: 8px;
      }
    }

    &:disabled {
      color: @colorA9 !important;

      & span {
        position: relative;

        &::before {
          position: absolute;
          top: 2px;
          left: 0;
          display: block;
          width: 16px;
          height: 16px;
          background: url('@/renderer/static/svg/disable_incream.svg'), no-repeat;
        }
      }
    }
  }
}

:global {
  button#startTreat {
    margin-left: 20px;
    width: 180px;
    height: 40px;
  }

  button#endTreat {
    margin-left: 20px;
    width: 180px;
    height: 40px;
  }

  button#creatTreat {
    height: 40px;
  }

  button#creatTreat:disabled span::before {
    content: '';
  }
}
