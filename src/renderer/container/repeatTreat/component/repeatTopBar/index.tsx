import React, { FC, useEffect, useState } from 'react';
import NgButton from '@/renderer/uiComponent/NgButton';
import styles from './index.module.less';
import { CreateCircle } from '@/renderer/uiComponent/SvgGather';
import { RepeatTreatStatusEnum } from '../../utils/repeatTreat/type';
import { useRecoilState } from 'recoil';
import { faultAtom, getFaultWithoutType } from '../../../../recoil/fault';
import { FaultEnum } from '../../../../../common/systemFault/type';

interface IProps {
  noTreatPlan: boolean;
  repeatTreatStatus: RepeatTreatStatusEnum;
  creatTreatPlan(): void;
  startTreatPlan(): void;
  endTreatPlan(): void;
}

export const RepeatTopBar: FC<IProps> = (props: IProps) => {
  const { creatTreatPlan, startTreatPlan, endTreatPlan, repeatTreatStatus, noTreatPlan } = props;
  const [fault] = useRecoilState(faultAtom);
  const [startDisable, setStartDisable] = useState<boolean>(false);

  useEffect(() => {
    setStartDisable(getFaultWithoutType(FaultEnum.imageFault, fault).length === 0 ? false : true);
  }, [fault]);

  return (
    <div className={styles.topBar}>
      <NgButton id="creatTreat" disabled={repeatTreatStatus !== RepeatTreatStatusEnum.notTreating} onClick={creatTreatPlan}>
        <CreateCircle />
        新建
      </NgButton>
      <NgButton
        id="startTreat"
        disabled={repeatTreatStatus !== RepeatTreatStatusEnum.notTreating || startDisable || noTreatPlan}
        onClick={startTreatPlan}
      >
        开始
      </NgButton>
      <NgButton id="endTreat" disabled={repeatTreatStatus === RepeatTreatStatusEnum.notTreating} onClick={endTreatPlan}>
        结束
      </NgButton>
    </div>
  );
};
