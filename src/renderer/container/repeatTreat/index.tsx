import React, { memo, useState, useEffect, useRef } from 'react';
// eslint-disable-next-line import/no-extraneous-dependencies
import { DragDropContext, Droppable, Draggable, DroppableProvided } from 'react-beautiful-dnd';
import styles from './index.module.less';
import { RepeatTopBar } from './component/repeatTopBar';
import { CreatPlanModal } from './component/creatPlanModal';
import { INTERVAL_TIME, RepeatTreatStatusEnum, StimulusItemModel, TreatStimulateModel } from './utils/repeatTreat/type';
import { sendRenderLog } from '../../utils/renderLogger';
import { useInterval } from 'react-timing-hooks';
import { useRecoilState, useSetRecoilState } from 'recoil';
import { tmsCoilSelector } from '../../recoil/tmsError';
import { ErrorModel } from '../../component/systemErrorModel/errorModel';
import { notNAVTypeList } from '../../../common/systemFault/config';
import { v4 as uuidv4 } from 'uuid';
import { TreatPlanItem } from './component/treatPlanItem';
import { FaultStatusEnum } from '../../../common/systemFault/type';
import NgEmpty from '../../uiComponent/NgEmpty';
import { isNotTreatingAtom } from '../../recoil/isNotTreating';
import { repeatTreatModel } from './utils/repeatTreat';
import { RegistCoilHeader } from '../registCoil/component/registCoilHeader';

const RepeatTreat = () => {
  const [showCreatModal, setShowCreatModal] = useState<boolean>(false);
  const [stimulateList, setStimulateList] = useState<StimulusItemModel[]>([]);
  const [repeatTreatStatus, setRepeatTreatStatus] = useState<RepeatTreatStatusEnum>(RepeatTreatStatusEnum.notTreating);
  const repeatTreatStatusRef = useRef<RepeatTreatStatusEnum>(RepeatTreatStatusEnum.notTreating);
  const currentTreatIndexRef = useRef<number>(-1);
  const [currentTreatIndex, setCurrentTreatIndex] = useState<number>(-1);
  const [coilSelector] = useRecoilState(tmsCoilSelector);
  const checkTreatStatusRef = useRef<any>();
  const residualTimeRef = useRef<number>(-1);
  const [residualTime, setResidualTime] = useState<number>(-1);
  const tidRef = useRef<string>('');
  const settingParamsRef = useRef<boolean>(false);
  const setIsNotTreating = useSetRecoilState(isNotTreatingAtom);
  const startTreatTimeoutRef = useRef<any>();

  /**
   * 轮询检查连续刺激的状态
   */
  const checkRepeatTreatStatus = async () => {
    if (repeatTreatStatusRef.current === RepeatTreatStatusEnum.notTreating || repeatTreatStatusRef.current === RepeatTreatStatusEnum.treating) {
      return;
    }
    /** 连续刺激的过程中，进行开始刺激*/
    await startStimulateTreatPlan();
  };

  /** 定时器*/
  const {
    pause: pauseCheckInterval,
    resume: resumeCheckInterval,
    start: startCheckInterval,
    stop: stopCheckInterval,
  } = useInterval(checkRepeatTreatStatus, 1000, { startOnMount: false });

  /**
   * 新建治疗方案
   */
  const creatTreatPlan = () => {
    setShowCreatModal(true);
  };

  /**
   * 开始连续刺激
   */
  const startRepeatTreatPlan = () => {
    const updatedStimulateList = stimulateList.map(item => ({
      ...item,
      treat_count: 0,
    }));
    setStimulateList(updatedStimulateList);

    currentTreatIndexRef.current = 0;
    setCurrentTreatIndex(0);
    setRepeatTreatStatus(RepeatTreatStatusEnum.processing);
    repeatTreatStatusRef.current = RepeatTreatStatusEnum.processing;
    startCheckInterval();
  };

  /**
   * 结束连续刺激
   */
  const endRepeatTreatPlan = async () => {
    tidRef.current = uuidv4();
    if (startTreatTimeoutRef.current) {
      clearTimeout(startTreatTimeoutRef.current);
    }
    stopCheckInterval();
    clearInterval(checkTreatStatusRef.current);
    residualTimeRef.current = -1;
    setResidualTime(-1);
    setRepeatTreatStatus(RepeatTreatStatusEnum.notTreating);
    repeatTreatStatusRef.current = RepeatTreatStatusEnum.notTreating;
    currentTreatIndexRef.current = -1;
    setCurrentTreatIndex(-1);
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    getStimulusList(true);
    await repeatTreatModel.treatmentPlanEnd(tidRef.current);
  };

  /**
   * 拖拽事件监听/更新
   */
  const onDragEnd = (result: any) => {
    const { destination, source } = result;
    if (!destination) return;

    const updatedStimulateList = Array.from(stimulateList);
    const [movedStimulate] = updatedStimulateList.splice(source.index, 1);
    updatedStimulateList.splice(destination.index, 0, movedStimulate);
    setStimulateList(updatedStimulateList);

    try {
      const idList = updatedStimulateList.map(item => item.id);
      // eslint-disable-next-line @typescript-eslint/no-floating-promises
      repeatTreatModel.updateStimulusList({ continue_stimulus_id_list: idList });
    } catch (error: any) {
      sendRenderLog.info(`连续刺激更新方案失败： ${JSON.stringify(error)}`);
    }
  };

  /**
   * 新增刺激方案
   */
  const addStimulusPlan = async (stimulate: TreatStimulateModel) => {
    setShowCreatModal(false);
    try {
      await repeatTreatModel.addStimulus(stimulate);
      // eslint-disable-next-line @typescript-eslint/no-floating-promises
      getStimulusList();
    } catch (error: any) {
      sendRenderLog.info(`连续刺激新增方案失败： ${JSON.stringify(error)}`);
    }

    return;
  };

  /**
   * 删除刺激
   */
  const deleteStimulateById = async (id: number) => {
    try {
      await repeatTreatModel.deleteStimulusItem(id);
      // eslint-disable-next-line @typescript-eslint/no-floating-promises
      getStimulusList();
    } catch (error: any) {
      sendRenderLog.info(`连续刺激删除方案失败： ${JSON.stringify(error)}`);
    }
  };

  /**
   * 获取刺激列表
   */
  const getStimulusList = async (isRetainCount?: boolean) => {
    try {
      const res = await repeatTreatModel.getStimulusList();

      if (isRetainCount) {
        // 保留 treat_count
        const updatedStimulateList = res.map(newItem => {
          const oldStimulate = stimulateList.find(oldItem => oldItem.id === newItem.id);
          if (oldStimulate && oldStimulate.treat_count) {
            return { ...newItem, treat_count: oldStimulate.treat_count };
          }

          return newItem;
        });

        setStimulateList(updatedStimulateList);
      } else {
        setStimulateList(res);
      }
    } catch (error: any) {
      sendRenderLog.info(`连续刺激获取方案失败： ${JSON.stringify(error)}`);
    }
  };

  /**
   * 开始进行刺激
   */
  const startStimulateTreatPlan = async () => {
    if (stimulateList.length === 0) return;
    const stimulateItem = stimulateList[currentTreatIndexRef.current];
    pauseCheckInterval();

    const isDisableTreat = repeatTreatModel.checkisDisableTreat(coilSelector.coil_max_temperature || 0, stimulateItem as TreatStimulateModel);
    const sendParamsSuccess = await repeatTreatModel.sendParamsToTms(stimulateItem, tidRef.current);
    if (isDisableTreat || !sendParamsSuccess) {
      settingParamsRef.current = false;
      resumeCheckInterval();

      return;
    }

    startTreatTimeoutRef.current = setTimeout(async () => {
      // 发送开始指令，进行开始2
      const isStartTreat = await repeatTreatModel.treatPlanStart(tidRef.current);
      if (isStartTreat) {
        // 更改当前状态
        setRepeatTreatStatus(RepeatTreatStatusEnum.treating);
        repeatTreatStatusRef.current = RepeatTreatStatusEnum.treating;
        settingParamsRef.current = false;
        await repeatTreatModel.queryTreatmentData(stimulateItem as TreatStimulateModel, autoStopTreatPlan, tidRef.current);
      }
      resumeCheckInterval();
      clearTimeout(startTreatTimeoutRef.current);
    }, INTERVAL_TIME);
  };

  /**
   * 刺激结束的回调
   */
  const autoStopTreatPlan = () => {
    residualTimeRef.current = -1;
    setResidualTime(-1);

    // 更新刺激的次数
    if (currentTreatIndexRef.current >= 0 && currentTreatIndexRef.current < stimulateList.length) {
      const currentStimulateItem = stimulateList[currentTreatIndexRef.current];
      if (currentStimulateItem.treat_count !== undefined) {
        currentStimulateItem.treat_count += 1;
      } else {
        currentStimulateItem.treat_count = 1;
      }

      setStimulateList([...stimulateList]);
    }

    // 更新当前正在刺激的方案index
    if (currentTreatIndexRef.current < stimulateList.length - 1) {
      currentTreatIndexRef.current += 1;
    } else {
      currentTreatIndexRef.current = 0;
    }
    setCurrentTreatIndex(currentTreatIndexRef.current);
    setRepeatTreatStatus(RepeatTreatStatusEnum.processing);
    repeatTreatStatusRef.current = RepeatTreatStatusEnum.processing;
  };

  /**
   * 错误弹窗弹起的操作
   */
  const handleErrorModalShow = async () => {
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    repeatTreatModel.treatmentPlanEnd(tidRef.current);
    stopCheckInterval();
    sendRenderLog.info('连续刺激错误弹窗显示，结束当前治疗任务，停止状态轮询');
  };

  useEffect(() => {
    setIsNotTreating(false);
    tidRef.current = uuidv4();
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    getStimulusList();
    window.systemAPI.pushSystemFault({ '0A030005': FaultStatusEnum.normal }, 'previewNoImagePlan page 非治疗态清除tms故障');

    repeatTreatModel.initWithHook({
      checkTreatStatusRef,
      residualTimeRef,
      setResidualTime,
    });

    return () => {
      setIsNotTreating(true);
      stopCheckInterval();
      if (checkTreatStatusRef.current) {
        clearInterval(checkTreatStatusRef.current);
      }
      if (startTreatTimeoutRef.current) {
        clearTimeout(startTreatTimeoutRef.current);
      }
    };
  }, []);

  useEffect(() => {
    if (currentTreatIndex !== -1) {
      const stimulate = stimulateList[currentTreatIndex];
      setResidualTime(stimulate.treatment_time);
    }
  }, [currentTreatIndex]);

  return (
    <div className={styles.container}>
      <RegistCoilHeader isDisable={repeatTreatStatus !== RepeatTreatStatusEnum.notTreating} title="连续刺激详情" />
      <RepeatTopBar
        noTreatPlan={stimulateList.length === 0}
        repeatTreatStatus={repeatTreatStatus}
        creatTreatPlan={creatTreatPlan}
        startTreatPlan={startRepeatTreatPlan}
        endTreatPlan={endRepeatTreatPlan}
      />
      {stimulateList.length === 0 && (
        <div className={styles.empty_container}>
          <NgEmpty emptyType={'noInfo'} />
        </div>
      )}
      {stimulateList.length > 0 && (
        <DragDropContext onDragEnd={onDragEnd}>
          <Droppable droppableId="treatPlanList" direction="vertical">
            {(provided: DroppableProvided) => (
              <div className={styles.treatPlanList} {...provided.droppableProps} ref={provided.innerRef}>
                {stimulateList.map((stimulateItem, index) => (
                  <Draggable
                    key={stimulateItem.id}
                    draggableId={`${stimulateItem.id}`}
                    isDragDisabled={repeatTreatStatus !== RepeatTreatStatusEnum.notTreating}
                    index={index}
                  >
                    {dragProvided => (
                      <TreatPlanItem
                        repeatTreatStatus={repeatTreatStatus}
                        stimulateItem={stimulateItem}
                        deleteStimulateById={deleteStimulateById}
                        index={index}
                        currentTreatIndex={currentTreatIndex}
                        residualTime={residualTime}
                        dragProvided={dragProvided}
                      />
                    )}
                  </Draggable>
                ))}
                {provided.placeholder}
              </div>
            )}
          </Droppable>
        </DragDropContext>
      )}
      {showCreatModal && (
        <CreatPlanModal
          showModal
          cancelAction={() => {
            setShowCreatModal(false);
          }}
          confirmAction={addStimulusPlan}
        />
      )}
      {repeatTreatStatus !== RepeatTreatStatusEnum.notTreating && (
        <ErrorModel
          onOpen={handleErrorModalShow}
          isStimulate
          faultTypeList={notNAVTypeList}
          onOk={() => {
            window.systemAPI.pushSystemFault({ '0A030005': FaultStatusEnum.normal }, 'previewNoImagePlan page 非治疗态清除tms故障');
            sendRenderLog.info('连续刺激错误弹窗确认键点击');
          }}
        />
      )}
    </div>
  );
};

export default memo(RepeatTreat);
