import React, { useEffect, useRef, useState } from 'react';
import styles from './index.module.less';
import { NgBreadcrumb } from '@/renderer/uiComponent/NgBreadCrumb';
import { ParamsType } from '@/renderer/container/stimulateTemplate/component/filterCondition';
import NgEmpty from '@/renderer/uiComponent/NgEmpty';
import { CirclePlusBack, EmptyAddTask, EmptyUnCheckTask } from '@/renderer/uiComponent/SvgGather';
import { CreateStimulusTemplateModelApi, EnumPlanStimulusType, EnumSortType, PlanStimulusModel, StimulusTemplateModelApi } from '@/common/types';
import { PreviewTemplateCard } from '@/renderer/container/stimulateTemplate/component/previewTemplateCard';
import { EditTemplateCard } from '@/renderer/container/stimulateTemplate/component/editTemplateCard';
import { getM200ApiInstance } from '@/common/api/ngApiAgent';
import classnames from 'classnames';
import _ from 'lodash';
import CameraAndCoil from '@/renderer/component/cameraAndCoil';
import { setFilterTemplateParams } from '@/renderer/component/importTemplate/setFilterTemplateParams';
import { useIntl } from 'react-intl';
import Icon from '@ant-design/icons';
import NgButton from '@/renderer/uiComponent/NgButton';
import { SmallFilterCondition } from '@/renderer/container/stimulateTemplate/component/smallFilterCondition';
import { useRecoilValue } from 'recoil';
import { useLicenseAtom } from '@/renderer/recoil/license';
import NgListItem from '../../uiComponent/NgListItem';

type StimulusTemplatePageQueryModel = {
  page_num: number;
  page_size: number;
  sort_by?: EnumSortType;
  stimulus_type_enum_list?: EnumPlanStimulusType[];
  name?: string;
};
let isFetch = false;
const StimulateTemplateContainer = () => {
  const intl = useIntl();
  const [targetStimulate, setTargetStimulate] = useState<PlanStimulusModel | CreateStimulusTemplateModelApi | undefined>(undefined);
  const [stimulateList, setStimulateList] = useState<PlanStimulusModel[]>([]);
  const [isEdit, setIsEdit] = useState<boolean>(false);
  const [total, setTotal] = useState<number>(0);
  const [fetchDataByScroll, setFetchDataByScroll] = useState(false);
  const license = useRecoilValue(useLicenseAtom);
  const [filters, setFilters] = useState<StimulusTemplatePageQueryModel>({
    page_num: 1,
    page_size: 500,
    stimulus_type_enum_list: [EnumPlanStimulusType.TBS, EnumPlanStimulusType.RTMS],
    sort_by: EnumSortType.DESC,
  });
  const m200Api = getM200ApiInstance();
  const filterRef = useRef();
  // mount
  useEffect(() => {
    const fetchTimeout = setTimeout(async () => {
      await getTemplateList();
    }, 100);

    return () => {
      // eslint-disable-next-line @typescript-eslint/no-unused-expressions
      fetchTimeout && clearTimeout(fetchTimeout);
    };
  }, [filters]);

  // 因为会滚动获取数据，所以拿到数据后，需要拼接去重
  const getTemplateList = async () => {
    let res = await m200Api.getStimulusTemplateList(filters);
    if (fetchDataByScroll) {
      setStimulateList(prevStimulateList => {
        return _.uniqWith([...prevStimulateList, ...res.records], (a, b) => {
          return a.id === b.id;
        });
      });
    } else {
      setStimulateList(res.records);
    }
    isFetch = false;
    setTotal(res.total);
  };
  // 交互流程，增删改查
  const onDelete = async () => {
    try {
      let res = await m200Api.delStimulusTemplate(targetStimulate?.id!);
      if (res) {
        setTargetStimulate(undefined);
        await getTemplateList();
      }
    } catch (e) {
      //
    }
  };
  const onEdit = () => {
    setIsEdit(true);
  };
  const resetFilter = (res: PlanStimulusModel) => {
    setIsEdit(false);
    setFilters({
      page_num: 1,
      page_size: 500,
      stimulus_type_enum_list: [EnumPlanStimulusType.TBS, EnumPlanStimulusType.RTMS],
      sort_by: EnumSortType.DESC,
    });
    // @ts-ignore
    filterRef.current!.reset();
    setTargetStimulate(res);
  };
  const onSave = async (templates: PlanStimulusModel) => {
    // 参数中应该是新的 targetStimulate；
    // 调用接口后，修改isEdit为false
    try {
      if (!targetStimulate?.id) {
        // create
        const res = await m200Api.createStimulusTemplate(templates as CreateStimulusTemplateModelApi);
        if (res) {
          resetFilter(res as PlanStimulusModel);
        }
      } else {
        // update
        const params = Object.assign({}, templates, { id: targetStimulate.id });
        const res = await m200Api.updateStimulusTemplate(params as StimulusTemplateModelApi, targetStimulate?.id);
        if (res) {
          resetFilter(res as PlanStimulusModel);
        }
      }
    } catch (e: any) {
      // setErrorMessage(e.message);
    }
  };
  const onCancel = () => {
    setIsEdit(false);
    if (!stimulateList.length || !targetStimulate?.id) {
      setTargetStimulate(undefined);
    }
  };
  const addTemplate = () => {
    setIsEdit(true);
    setTargetStimulate({
      id: undefined,
      name: '',
      type: EnumPlanStimulusType.TBS,
      relative_strength: undefined as any,
      plexus_inner_frequency: undefined,
      plexus_inter_frequency: undefined,
      plexus_inner_pulse_count: undefined,
      plexus_count: undefined,
      pulse_total: 0,
      treatment_time: 0,
    });
  };
  // 顶部参数
  const controlParams = async (params: ParamsType) => {
    let newParams = setFilterTemplateParams(filters, params);
    setFetchDataByScroll(false);
    setFilters(newParams);
    setTargetStimulate(undefined);
  };

  const clickTemplate = (params: PlanStimulusModel) => {
    setIsEdit(false);
    setTargetStimulate(params);
  };

  // 监听滚动条
  const listRef = useRef<HTMLDivElement>(null);
  useEffect(() => {
    const handleScroll = async () => {
      if (isFetch) return;
      if (total <= stimulateList.length) return;
      const { scrollTop, scrollHeight, clientHeight } = listRef.current!;
      if (scrollTop + clientHeight + 20 >= scrollHeight) {
        isFetch = true;
        setFilters(prevFilters => {
          return {
            ...prevFilters,
            page_num: prevFilters.page_num + 1,
          };
        });
        setFetchDataByScroll(true);
      }
    };
    if (!listRef.current) return;

    listRef.current.addEventListener('scroll', handleScroll);

    return () => {
      if (!listRef.current) return;
      listRef.current.removeEventListener('scroll', handleScroll);
    };
  }, [listRef, total]);

  return (
    <div className={styles.stimulate_template_container}>
      <div className={styles.header}>
        <div className={styles.left}>
          <NgBreadcrumb isGray={false} />
        </div>
        <div className={styles.center}>
          <CameraAndCoil />
        </div>
        <div className={styles.right} />
      </div>
      <div className={styles.container}>
        <div className={styles.left}>
          <NgButton
            type="default"
            className={styles.addBtn}
            icon={<Icon component={CirclePlusBack} style={{ fontSize: 22, marginRight: 8 }} rev="" />}
            style={{ width: 342, height: 40 }}
            disabled={!!license.hasLicenseError}
            onClick={addTemplate}
          >
            {intl.formatMessage({ id: '新建模板' })}
          </NgButton>
          <div className={styles.left_content}>
            <SmallFilterCondition handleFilter={controlParams} ref={filterRef} />
            <div className={styles.name_list} ref={listRef}>
              {stimulateList.map(item => {
                return (
                  <NgListItem
                    key={item.id}
                    className={classnames(styles.name_item, targetStimulate?.id === item.id ? styles.is_active : '')}
                    title={item.name}
                    maxWidth={162}
                    fontSize={16}
                    onClick={() => clickTemplate(item)}
                  />
                );
              })}
            </div>
          </div>
        </div>
        <div className={classnames(isEdit ? styles.left_overlay : styles.left_overlay_none)} />
        <div className={styles.right}>
          <div className={styles.template_content}>
            {!stimulateList.length && !targetStimulate && !isEdit && !filters.name && (
              <div className={styles.empty_content}>
                <NgEmpty key={'custom'} emptyType={'custom'} customDesc={intl.formatMessage({ id: '无脉冲模板' })} customSvg={<EmptyAddTask />} />
              </div>
            )}
            {!stimulateList.length && !targetStimulate && !isEdit && filters.name && (
              <div className={styles.empty_content}>
                <NgEmpty
                  key={'custom'}
                  emptyType={'custom'}
                  customDesc={intl.formatMessage({ id: '未搜索到相关内容' })}
                  customSvg={<EmptyAddTask />}
                />
              </div>
            )}
            {!!stimulateList.length && !targetStimulate && (
              <div className={styles.empty_content}>
                <NgEmpty
                  key={'custom'}
                  emptyType={'custom'}
                  customDesc={intl.formatMessage({ id: '请选择脉冲模板' })}
                  customSvg={<EmptyUnCheckTask />}
                />
              </div>
            )}
            {!!targetStimulate && !isEdit && targetStimulate.id && (
              <PreviewTemplateCard haveFooter template={targetStimulate} handleDelete={onDelete} handleEdit={onEdit} />
            )}
            {!!targetStimulate && isEdit && <EditTemplateCard template={targetStimulate} handleSave={onSave} handleCancel={onCancel} />}
          </div>
        </div>
      </div>
    </div>
  );
};

export default StimulateTemplateContainer;
