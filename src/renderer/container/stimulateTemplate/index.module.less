@import '../../static/style/baseColor.module.less';

.stimulate_template_container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background: @colorA1;
  overflow-y: hidden;

  .header {
    position: relative;
    display: inline-flex;
    width: 100%;
    height: 90px;
    color: @colorA9;
    padding: 20px 20px 24px 20px;

    .left {
      width: 300px;
      display: inline-flex;
      position: relative;
      align-items: flex-start;
    }

    .center {
      flex: 1;
      position: relative;
      display: inline-flex;
      justify-content: center;
    }

    .right {
      width: 300px;
      display: inline-flex;
      position: relative;
    }
  }

  .container {
    flex: 1;
    display: inline-flex;
    justify-content: space-between;
    flex-direction: row;
    color: @colorA9;
    padding: 0px 20px 16px 20px;
    height: calc(100% - 90px);
    position: relative;

    .left {
      width: 342px;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      margin-right: 16px;

      .left_content {
        width: 342px;
        height: calc(100% - 56px);
        background: @colorA2;
        color: @colorA9;
        margin-top: 14px;
        border-radius: 6px;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;

        .sub_filter {
          height: auto;
        }

        .name_list {
          flex: 1;
          overflow-y: auto;
          background: @colorA2;
          color: @colorA9;
          padding: 0px 20px 16px 20px;
          border-radius: 6px;

          .name_item {
            width: 100%;
            height: 32px;
            line-height: 32px;
            padding: 0px 20px 0px 20px;
            margin-bottom: 8px;
            color: @colorA9;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .name_item:hover {
            border-radius: 8px;
            background: @colorA4;
          }

          .is_active {
            color: @colorC1;
          }
          &::-webkit-scrollbar {
            width: 6px;
          }
          &::-webkit-scrollbar-thumb {
            background: @colorA6;
            border-radius: 10px;
          }
          &::-webkit-scrollbar-thumb:vertical {
            width: 10px;
            height: 10px;
          }
        }
      }
    }

    .left_overlay {
      width: 342px;
      height: 100%;
      display: block;
      position: absolute;
      top: 0;
      left: 20px;
      background: inherit;
      z-index: 99;
      cursor: url('@/renderer/static/svg/disableMouse.cur'), not-allowed !important;
    }

    .left_overlay_none {
      display: none;
    }

    .right {
      flex: 1;
      background: @colorA2;
      border-radius: 6px;

      .template_content {
        flex: 1;
        width: 100%;
        height: 100%;

        .empty_content {
          width: 100%;
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          color: @colorA9;
        }
      }
    }
  }
}
