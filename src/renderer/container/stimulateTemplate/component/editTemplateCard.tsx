import * as React from 'react';
import { injectIntl } from 'react-intl';
import { IntlPropType } from '@/common/types/propTypes';
import { EnumPlanStimulusType, PlanStimulusModel } from '@/common/types';
import styles from '@/renderer/container/stimulateTemplate/component/templateCard.module.less';
import { EditStimulateTemplate, TbsFieldType } from '@/renderer/component/template';
import { NgForm } from '@/renderer/uiComponent/NgForm';
import Form, { Rule } from 'antd/es/form';
import { useEffect, useState } from 'react';
import { calTbsChartData } from '@/renderer/component/template/calTemplate';
import { getM200ApiInstance } from '@/common/api/ngApiAgent';
import { TBSChart } from '@/renderer/component/tbsChart';
import { getFields, normalFields, tbsFields } from '@/renderer/component/template/calRules';
import NgDarkButton from '@/renderer/uiComponent/NgDarkButton';
import Icon from '@ant-design/icons';
import { AllowClear } from '@/renderer/uiComponent/SvgGather';
import { NgTextArea } from '../../../uiComponent/NgTextarea';

type Props = IntlPropType & {
  template: PlanStimulusModel;
  handleCancel(): void;
  handleSave(template: PlanStimulusModel): void;
};
export const getRenderFields = (type: EnumPlanStimulusType) => {
  return type === EnumPlanStimulusType.TBS ? tbsFields : normalFields;
};
export const changeISComplete = (values: any, type: EnumPlanStimulusType) => {
  let renderFields: TbsFieldType[] = getRenderFields(type);
  const fieldObj = renderFields
    .map(v => v.key)
    .filter((v: any) => {
      if (values.strand_pulse_count === 1) {
        return v !== 'intermission_time';
      } else {
        return true;
      }
    });

  return fieldObj.every(v => {
    return values[v] && values[v] > 0;
  });
};
export const InnerEditTemplateCard = (props: Props) => {
  const { intl } = props;
  const [form] = Form.useForm();
  const [formName] = Form.useForm();
  const [formValues, setFormValues] = useState<any>({});

  useEffect(() => {
    form.setFieldsValue(props.template);
    formName.setFieldsValue({ name: props.template.name });
    setFormValues(props.template);
  }, []);
  const setNullOfUndefined = () => {
    const fields = getFields(formValues, 0);
    let newValues = fields.reduce(
      (res, cur) => {
        let temp = {};
        temp[cur.key] = formValues[cur.key] === undefined ? null : formValues[cur.key];

        return Object.assign({}, res, temp);
      },
      { type: formValues.type }
    );
    setFormValues(newValues);
  };

  const validateTemplateAndSave = (name: string) => {
    form
      .validateFields()
      .then(tbsValues => {
        const { treatment_time = 0, pulse_total = 0 } = calTbsChartData(tbsValues);
        if (name === '' || !name || pulse_total === 0) return;
        props.handleSave(
          Object.assign({}, tbsValues, {
            name,
            intermission_time: tbsValues.strand_pulse_count === 1 ? undefined : tbsValues.intermission_time,
            treatment_time,
            pulse_total,
          })
        );
      })
      .catch(error => {
        console.log('验证表单错误'); // eslint-disable-line no-console

        return error;
      });
  };

  const onSave = async () => {
    setNullOfUndefined();
    try {
      let nameValues = await formName.validateFields();
      validateTemplateAndSave(nameValues.name.trim());
    } catch (e) {
      validateTemplateAndSave('');
    }
  };

  const changeStateValues = (newParams: any): any => {
    form.setFieldsValue({
      ...newParams,
      intermission_time: newParams.strand_pulse_count === 1 ? undefined : newParams.intermission_time,
    });
    setFormValues({
      ...newParams,
      intermission_time: newParams.strand_pulse_count === 1 ? undefined : newParams.intermission_time,
    });
  };

  const handleChangeValues = (changeValues: any, values: any): any => {
    let key = Object.getOwnPropertyNames(changeValues)[0];
    let value = changeValues[key];
    if (changeValues.type) {
      // 修改了刺激类型, 重置表单
      let renderFields: TbsFieldType[] = getRenderFields(value);
      let template = renderFields.reduce((result: any, item: TbsFieldType) => {
        return { ...result, [item.key]: undefined };
      }, {});
      form.setFieldsValue({ type: value, ...template });
      setFormValues({
        type: value,
        ...template,
      });

      return;
    }

    changeStateValues(values);
  };

  const getRule = (): Rule[] => {
    return [
      {
        validator: async (_: any, value: any) => {
          return new Promise(async (res, rej) => {
            if (!value || !value.trim()) {
              return rej(intl.formatMessage({ id: '不可为空' }));
            }
            if (value.length > 20) {
              return rej(intl.formatMessage({ id: '请输入1-20位' }));
            }
            if (props.template.name === value.trim()) {
              return res('');
            }
            const isExit = await getM200ApiInstance().checkStimulusTemplateName(value);
            if (isExit) {
              rej(intl.formatMessage({ id: '模板名称已存在' }));

              return;
            }
            res('');
          });
        },
      },
    ];
  };

  return (
    <div className={styles.templateCard}>
      <div className={styles.header}>{!!props.template.id ? '编辑脉冲模板' : '新建脉冲模板'} </div>
      <div className={styles.content}>
        <div className={styles.template}>
          <NgForm form={formName} layout="vertical" className={styles.nameForm}>
            <Form.Item label={<span>{props.intl.formatMessage({ id: '模板名称' })}</span>} validateTrigger="onBlur" rules={getRule()} name="name">
              <NgTextArea
                maxLength={20}
                className="tem_textarea"
                onPressEnter={e => e.preventDefault()}
                showCount
                allowClear={{ clearIcon: <Icon component={AllowClear} style={{ fontSize: 14, paddingTop: 4 }} rev="" /> }}
                placeholder={props.intl.formatMessage({ id: '请输入模板名称' })}
              />
            </Form.Item>
          </NgForm>
          <NgForm
            form={form}
            layout="horizontal"
            labelAlign={'left'}
            labelCol={{ span: 8 }}
            wrapperCol={{ span: 16 }}
            className={styles.templateForm}
            onValuesChange={handleChangeValues}
          >
            <div>{<TBSChart template={formValues} />}</div>
            <EditStimulateTemplate motionThreshold={0} formRef={form} stimulate={formValues} />
          </NgForm>
        </div>
        <div className={styles.footer}>
          <NgDarkButton onClick={props.handleCancel}>{intl.formatMessage({ id: '取消' })}</NgDarkButton>
          <NgDarkButton onClick={onSave} className={styles.save}>
            {intl.formatMessage({ id: '保存' })}
          </NgDarkButton>
        </div>
      </div>
    </div>
  );
};

export const EditTemplateCard = React.memo(injectIntl(InnerEditTemplateCard));
