import * as React from 'react';
import { injectIntl } from 'react-intl';
import { IntlPropType } from '@/common/types/propTypes';
import { PreviewTemplate } from '@/renderer/component/template/preview';
import { PlanStimulusModel } from '@/common/types';
import styles from './templateCard.module.less';
import { memo, useEffect, useState } from 'react';
import NgPopover from '@/renderer/uiComponent/NgPopover';
import dayjs from 'dayjs';
import classnames from 'classnames';
import { useRecoilState } from 'recoil';
import { useLicenseAtom } from '@/renderer/recoil/license';
import NgDarkButton from '@/renderer/uiComponent/NgDarkButton';

type Props = IntlPropType & {
  haveFooter?: boolean;
  template: PlanStimulusModel;
  handleDelete(): void;
  handleEdit(): void;
};
export const InnerPreviewTemplateCard = (props: Props) => {
  const [popOpen, setPopOpen] = useState(false);
  const [license] = useRecoilState(useLicenseAtom);
  useEffect(() => {
    setPopOpen(false);
  }, [props.template]);

  return (
    <div className={styles.templateCard}>
      <div className={classnames(styles.header, !props.haveFooter ? styles.header_small : '')}>
        {props.intl.formatMessage({ id: '更新时间' })}:&nbsp; {dayjs(props.template.updated_at).format('YYYY-MM-DD HH:mm')}
      </div>
      <div className={styles.content}>
        <div className={styles.template}>
          <PreviewTemplate isSmall={!props.haveFooter} showName template={props.template} />
        </div>
        {props.haveFooter && (
          <div className={styles.footer}>
            <NgPopover
              open={popOpen}
              showOperation={{
                onOk: () => {
                  props.handleDelete();
                  setPopOpen(false);
                },
                onCancel: () => setPopOpen(false),
              }}
              title={props.intl.formatMessage({ id: '是否确认删除模板?' })}
              placement={'topRight'}
              trigger={'hover'}
              overlayClassName={styles.overlayPopover}
              content={<></>}
            >
              <div>
                <NgDarkButton disabled={!!license.hasLicenseError} onClick={() => setPopOpen(true)}>
                  删除
                </NgDarkButton>
              </div>
            </NgPopover>
            <NgDarkButton disabled={!!license.hasLicenseError} onClick={props.handleEdit}>
              {props.intl.formatMessage({ id: '编辑' })}
            </NgDarkButton>
          </div>
        )}
      </div>
    </div>
  );
};

export const PreviewTemplateCard = memo(injectIntl(InnerPreviewTemplateCard));
