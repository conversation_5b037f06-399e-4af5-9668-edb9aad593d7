@import '../../../static/style/baseColor.module.less';
@import '../../../static/style/base.module.less';
.templateCard {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 16px 16px;

  .header {
    width: 100%;
    margin-bottom: 70px;
    color: @colorA9;
    font-size: @font-size-base;
  }
  .header_small {
    margin-bottom: 16px;
  }

  .content {
    position: relative;
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;

    .template {
      display: inline-flex;
      position: relative;
      width: 262px;
      height: auto;
      min-height: 400px;
      margin-bottom: 16px;
      flex-direction: column;

      .nameForm {
        width: 262px;
        height: 60px;
        margin-bottom: 30px;
        :global {
          .ant-form-item-label label {
            color: @colorA9;
          }
          .tem_textarea {
            .ant-input {
              resize: none;
              padding: 4px 25px 4px 11px;
            }
            .ant-input-clear-icon {
              inset-block-start: 5px;
              inset-inline-end: 12px;
              cursor: url('@/renderer/static/svg/cursor.cur'), pointer !important;
            }
            .ant-input-data-count {
              width: 45px;
              text-align: right;
              color: @colorA9;
              z-index: 10;
            }
          }
        }
      }

      .templateForm {
        width: 262px;
        margin-top: 10px;
        .suffix {
          color: @colorA7;
        }
        :global {
          .ant-form-item-label label {
            color: @colorA9;
          }

          .ant-input-suffix {
            margin-top: 4px;
          }
          .ant-form-item-explain-error {
            font-size: 12px;
          }
        }
      }
    }

    .footer {
      position: relative;
      width: 262px;
      display: inline-flex;
      flex-direction: row;
      justify-content: space-between;

      button {
        width: 120px;
      }

      .save {
        :global {
          .ant-btn-default:disabled {
            background-color: @colorA4_1 !important;
            color: @colorA12 !important;
          }
        }
      }
    }
  }
}
.overlayPopover {
  :global {
    .ant-popover-inner {
      width: 208px;
      height: 107px;
      padding: 14px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .ant-popover-title {
        font-family: normal-font, serif !important;
        font-weight: normal;
      }
    }
  }
}
