import React, { useCallback, useImperativeHandle } from 'react';
import styles from './smallFilterCondition.module.less';
import NgButton from '@/renderer/uiComponent/NgButton';
import { Search, SortAsc, SortDesc, AllowClear } from '@/renderer/uiComponent/SvgGather';
import { NgIcon } from '@/renderer/uiComponent/NgIcon';
import { NgRadio } from '@/renderer/uiComponent/NgRadio';
import { NgInput } from '@/renderer/uiComponent/NgInput';
import { EnumSortType } from '@/common/types';
import { useIntl } from 'react-intl';
import Icon from '@ant-design/icons';
import { useDebounceFn } from 'ahooks';

export type ParamsType = {
  type: string;
  sort: EnumSortType;
  keyword: string;
};
type PropsType = {
  handleFilter(params: ParamsType): void;
};
const InnerSmallFilterCondition = (props: PropsType, ref: any) => {
  const intl = useIntl();
  const { handleFilter } = props;
  const [filterParams, setFilterParams] = React.useState<ParamsType>({
    type: 'all',
    sort: EnumSortType.DESC,
    keyword: '',
  });
  useImperativeHandle(ref, () => ({
    reset: () => {
      setFilterParams({
        type: 'all',
        sort: EnumSortType.DESC,
        keyword: '',
      });
    },
  }));
  const getSortIconSvg = useCallback(() => {
    if (filterParams.sort === EnumSortType.DESC) {
      return SortDesc;
    }

    return SortAsc;
  }, [filterParams.sort]);

  const changeType = (e: any) => {
    setFilterParams({ ...filterParams, type: e.target.value });
    handleFilter({ ...filterParams, type: e.target.value });
  };

  const changeSort = () => {
    const sort = filterParams.sort === EnumSortType.DESC ? EnumSortType.ASC : EnumSortType.DESC;
    setFilterParams({ ...filterParams, sort });
    handleFilter({ ...filterParams, sort });
  };

  const changeKeyword = (e: any) => {
    let newParams = { ...filterParams, keyword: e.target.value };
    setFilterParams(newParams);
    run(newParams);
  };

  const debounceChangeFilter = (params: any) => {
    handleFilter(params);
  };
  const { run } = useDebounceFn(debounceChangeFilter, { wait: 500 });

  return (
    <div className={styles.filterCondition}>
      <div className={styles.top}>
        <div className={styles.typeFilter}>
          {intl.formatMessage({ id: '类型' })} ：
          <NgRadio.Group
            defaultValue="all"
            value={filterParams.type}
            size={'middle'}
            optionType={'button'}
            buttonStyle={'solid'}
            onChange={changeType}
            className={styles.backgroundA5}
          >
            <NgRadio value="all">{intl.formatMessage({ id: '全部' })}</NgRadio>
            <NgRadio value="rTMS">rTMS</NgRadio>
            <NgRadio value="tbs">TBS</NgRadio>
          </NgRadio.Group>
        </div>
        <NgButton
          type="default"
          size={'large'}
          style={{ width: 32, height: 32, minWidth: 40, background: '#3A3A49', color: '#FFFFFF' }}
          onClick={changeSort}
        >
          <Icon component={getSortIconSvg()} style={{ fontSize: 20 }} rev="" />
        </NgButton>
      </div>
      <div className={styles.inputFilter}>
        <NgInput
          value={filterParams.keyword}
          prefix={<NgIcon iconSvg={Search} fontSize={18} />}
          placeholder={intl.formatMessage({ id: '请输入搜索内容' })}
          allowClear={{ clearIcon: <Icon component={AllowClear} style={{ fontSize: 14 }} rev="" /> }}
          style={{ height: '32px' }}
          onChange={changeKeyword}
        />
      </div>
    </div>
  );
};

export const SmallFilterCondition = React.forwardRef(InnerSmallFilterCondition);
