import React, { useCallback, useImperativeHandle } from 'react';
import styles from './filterCondition.module.less';
import NgButton from '@/renderer/uiComponent/NgButton';
import { Search, CirclePlusBack, SortAsc, SortDesc, AllowClear } from '@/renderer/uiComponent/SvgGather';
import { NgIcon } from '@/renderer/uiComponent/NgIcon';
import { NgRadio } from '@/renderer/uiComponent/NgRadio';
import { NgInput } from '@/renderer/uiComponent/NgInput';
import { EnumSortType } from '@/common/types';
import { useIntl } from 'react-intl';
import Icon from '@ant-design/icons';
import { useRecoilState } from 'recoil';
import { useLicenseAtom } from '@/renderer/recoil/license';
import { useDebounceFn } from 'ahooks';
import NgDarkButton from '@/renderer/uiComponent/NgDarkButton';
export type ParamsType = {
  type: string;
  sort: EnumSortType;
  keyword: string;
};
type PropsType = {
  haveAddBtn?: boolean;
  handleAddTemplate(): void;
  handleFilter(params: ParamsType): void;
};
const initFilterParams = {
  type: 'all',
  sort: EnumSortType.DESC,
  keyword: '',
};
const InnerFilterCondition = (props: PropsType, ref: any) => {
  const intl = useIntl();
  const [license] = useRecoilState(useLicenseAtom);
  const { handleAddTemplate, handleFilter, haveAddBtn } = props;
  const [filterParams, setFilterParams] = React.useState<ParamsType>(initFilterParams);
  useImperativeHandle(ref, () => ({
    reset: () => {
      setFilterParams(initFilterParams);
    },
  }));
  const getSortIconSvg = useCallback(() => {
    if (filterParams.sort === EnumSortType.DESC) {
      return SortDesc;
    }

    return SortAsc;
  }, [filterParams.sort]);

  const changeType = (e: any) => {
    setFilterParams({ ...filterParams, type: e.target.value });
    handleFilter({ ...filterParams, type: e.target.value });
  };

  const changeSort = () => {
    const sort = filterParams.sort === EnumSortType.DESC ? EnumSortType.ASC : EnumSortType.DESC;
    setFilterParams({ ...filterParams, sort });
    handleFilter({ ...filterParams, sort });
  };

  const changeKeyword = (e: any) => {
    let newParams = { ...filterParams, keyword: e.target.value };
    setFilterParams(newParams);
    run(newParams);
  };
  const { run } = useDebounceFn(handleFilter, { wait: 500 });

  return (
    <div className={styles.filterContainer}>
      {haveAddBtn && (
        <NgButton
          type="default"
          size={'large'}
          className={styles.addBtn}
          icon={<Icon component={CirclePlusBack} style={{ fontSize: 20 }} rev="" />}
          style={{ width: 240, height: 40 }}
          disabled={!!license.hasLicenseError}
          onClick={handleAddTemplate}
        >
          {intl.formatMessage({ id: '新建模板' })}
        </NgButton>
      )}
      <div className={styles.typeFilter}>
        {intl.formatMessage({ id: '类型' })} ：
        <NgRadio.Group
          value={filterParams.type}
          size={haveAddBtn ? 'large' : 'middle'}
          optionType={'button'}
          buttonStyle={'solid'}
          onChange={changeType}
          className={haveAddBtn ? '' : styles.backgroundA5}
        >
          <NgRadio value="all">{intl.formatMessage({ id: '全部' })}</NgRadio>
          <NgRadio value="rTMS">rTMS</NgRadio>
          <NgRadio value="tbs">TBS</NgRadio>
        </NgRadio.Group>
      </div>
      <NgDarkButton type="default" style={{ width: 180, height: 32, background: '#3A3A49', color: '#FFFFFF' }} onClick={changeSort}>
        <span className={styles.buttonContent}>
          {intl.formatMessage({ id: '更新时间' })}&nbsp;
          <Icon component={getSortIconSvg()} style={{ fontSize: 18, marginLeft: 6 }} rev="" />
        </span>
      </NgDarkButton>
      <div className={styles.inputFilter}>
        <NgInput
          prefix={<NgIcon iconSvg={Search} fontSize={18} />}
          placeholder={intl.formatMessage({ id: '请输入搜索内容' })}
          allowClear={{ clearIcon: <Icon component={AllowClear} style={{ fontSize: 16 }} rev="" /> }}
          style={{ height: haveAddBtn ? '40px' : '32px' }}
          value={filterParams.keyword}
          onChange={changeKeyword}
        />
      </div>
    </div>
  );
};

export const FilterCondition = React.forwardRef(InnerFilterCondition);
