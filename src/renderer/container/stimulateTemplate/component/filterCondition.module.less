@import '../../../static/style/baseColor.module.less';
.filterContainer {
  width: 100%;
  display: inline-flex;
  justify-content: flex-start;

  .addBtn {
    margin-right: 16px;
  }
  .typeFilter {
    margin-right: 20px;
    width: auto;
  }

  .backgroundA5 {
    :global {
      .ant-radio-button-wrapper {
        color: @colorA12;
        background: @colorA5;
        font-size: 14px;
        padding: 12px 32px;
      }
      .ant-radio-button-wrapper-checked {
        background: @colorA6;
      }
      .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
        background: @colorA6;
      }
      .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):hover {
        background: @colorA6;
      }
      .ant-radio-button-wrapper:hover {
        background-color: @colorA5_1;
      }
      .ant-radio-button-wrapper:not(:first-child)::before {
        display: none;
      }
    }
  }

  .sortFilter {
    width: 200px;
  }

  .buttonContent {
    display: inline-flex;
  }

  .inputFilter {
    flex: 1;
    margin-left: 20px;

    :global {
      .ant-input-clear-icon {
        margin-top: 2px;
      }
    }
  }
}
