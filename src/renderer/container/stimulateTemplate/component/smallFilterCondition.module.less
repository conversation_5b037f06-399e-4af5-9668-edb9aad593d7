.filterCondition {
  width: 100%;
  height: auto;
  display: flex;
  flex-direction: column;
  padding: 16px 20px;

  .top {
    display: flex;
    justify-content: space-between;
    height: 32px;
    margin-bottom: 16px;

    .typeFilter {
      width: 250px;
      display: inline-flex;
      align-items: center;

      .backgroundA5 {
        :global {
          .ant-radio-button-wrapper {
            padding: 12px 16px;
          }
        }
      }
    }
  }
  .inputFilter {
    width: 100%;
  }
}
