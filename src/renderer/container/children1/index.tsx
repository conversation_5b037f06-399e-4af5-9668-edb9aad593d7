import React from 'react';
import { withUserSession, UserSessionProps } from '../../hocComponent/withUserSession';
import { NgRadio } from '../../uiComponent/NgRadio';
import styles from './index.module.less';

type Props = UserSessionProps;
export const Children1: React.FC<Props> = props => {
  const logout = async () => {
    await window.authAPI.logout();
    props.setUserSession?.(undefined);
  };

  return (
    <div className={styles.children1}>
      <div style={{ color: 'red' }} onClick={logout}>
        退出登录
      </div>
      <div>
        <NgRadio>单元1</NgRadio>
      </div>
      <div>
        <NgRadio checked>单元2</NgRadio>
      </div>
    </div>
  );
};

export default withUserSession(Children1);
