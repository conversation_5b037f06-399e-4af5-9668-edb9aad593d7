@import '@/renderer/static/style/baseColor.module.less';

.homeContainer {
  height: 100vh;

  .homeHeader {
    display: flex;
    align-items: center;
    padding: 20px 20px 24px 20px;
    box-sizing: border-box;
    justify-content: space-between;

    .left {
      width: 500px;
      height: 46px;
      visibility: hidden;
    }

    .center {
      flex: 1;
      display: inline-flex;
      justify-content: center;
    }

    .right {
      width: 500px;
      display: flex;
      flex-direction: row-reverse;
    }
  }

  .tableContainer {
    padding: 0 20px;

    :global {
      .ant-table-thead {
        height: 54px;
      }

      .ant-table-filter-column:not(:has(.ant-table-column-sorter)) {
        .ant-table-column-title {
          margin-right: 14px;
        }
      }

      .ant-table-filter-column:has(.ant-table-column-sorter) {
        .ant-table-column-title {
          margin-right: 0;
        }

        & .ant-table-column-sorter {
          margin: 0 12px 0 18px;
        }
      }

      .ant-table-filter-trigger {
        padding: 0;
      }

      .ant-table-column-title {
        margin-right: 14px;
        width: fit-content;
      }

      .ant-table-cell {
        height: 54px;
      }

      .ant-table-pagination {
        position: fixed;
        right: 20px;
        bottom: -3px;
        z-index: 99;
      }

      .ant-table-placeholder {
        height: 65vh;
      }

      .ant-pagination-options-quick-jumper {
        margin-left: 8px;
      }
    }
  }

  .tableEmpty {
    height: calc(100vh - 160px);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: @colorA11;

    & p {
      line-height: 0;
      margin-top: 18px;
    }
  }

  .operationContainer {
    display: flex;
    padding: 0 20px;
    margin-bottom: 20px;
  }
}

.iconContainer {
  display: flex;

  & span {
    margin-right: 32px;

    &:last-child {
      margin-right: 0;
    }
  }
}

.emptyTable {
  :global {
    .home-table-name,
    .home-table-id {
      max-width: 200px;
      min-width: 200px;
      width: 200px;
    }

    .home-table-status,
    .home-table-type {
      max-width: 150px;
      min-width: 150px;
      width: 150px;
    }

    .home-table-treatment_count {
      max-width: 100px;
      min-width: 100px;
      width: 100px;
    }

    .home-table-motionThreshold {
      max-width: 190px;
      min-width: 190px;
      width: 190px;
    }

    .home-table-updated_treatment_at {
      max-width: 250px;
      min-width: 250px;
      width: 250px;
      transition: none !important;
    }

    .home-table-updated_created_at {
      max-width: 250px;
      min-width: 250px;
      width: 250px;
      transition: none !important;
    }
  }
}

:global {
  .home-table-name,
  .home-table-id {
    max-width: 180px;
    min-width: 180px;
    width: 180px;

    & > div:first-child {
      line-height: 54px;
      height: 100%;
      max-width: 100%;
      min-width: 100%;
      width: 100%;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  .home-table-status,
  .home-table-type {
    max-width: 150px;
    min-width: 150px;
    width: 150px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .home-table-motionThreshold {
    max-width: 190px;
    min-width: 190px;
    width: 190px;
  }

  .home-table-updated_treatment_at {
    max-width: 120px;
    min-width: 120px;
    width: 120px;
  }

  .home-table-updated_created_at {
    max-width: 250px;
    min-width: 250px;
    width: 250px;
  }
}

.homeLoading {
  max-height: 100% !important;
}

.filter_active {
  :global {
    .ant-table-filter-trigger {
      background: url('@/renderer/static/svg/table_filter_active.svg') no-repeat center center;
    }
  }
}
