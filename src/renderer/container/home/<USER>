import { ColumnsType } from 'antd/lib/table';
import styles from './index.module.less';
// eslint-disable-next-line import/no-internal-modules
import moment from 'moment/moment';
import { NgIcon } from '@/renderer/uiComponent/NgIcon';
import {
  StartTreatment,
  StartTreatmentDisable,
  Measure,
  Report,
  ReportDisable,
  EditPlan,
  MeasureDisable,
  EditPlanDisable,
  PreviewPlan,
  Filed,
  Restore,
  DisableField,
  DisableRestore,
} from '@/renderer/uiComponent/SvgGather';
import React, { useState } from 'react';
import { FilterDropdownProps } from 'antd/es/table/interface';
import CheckFilterDropdown, { IFilterType } from '@/renderer/uiComponent/NgTable/components/checkFilterDropdown';
import { NavigateFunction } from 'react-router-dom';
import { PlanModel } from '@/common/types';
import { IntlShape } from 'react-intl';
import classNames from 'classnames';
import { SetterOrUpdater } from 'recoil';
import { IFilterPage } from '@/renderer/recoil/tableFilterParams';
import NgListItem from '../../uiComponent/NgListItem';
import { useAsyncEffect } from 'ahooks';
import { product } from '../../constant/product';
import { isDev } from '@/common/lib/env/nodeEnv';
export enum PlanStatus {
  'Pending' = 1,
  'Normal' = 2,
}
export enum ImageType {
  'noImage' = 1, // 无影像
  'withImage' = 2, // 有影像
}

export type IPatient = PlanModel;
// eslint-disable-next-line max-lines-per-function
// @ts-ignore
export const getColumns: (
  cameraStatus: boolean,
  socketIsOpen: boolean,
  navigate: NavigateFunction,
  isRegistCoil: boolean,
  isNotConnectCoil: boolean,
  hasLicenseError: boolean,
  handleTestMeasure?: Function,
  intl?: IntlShape,
  isField?: boolean,
  handleField?: (planId: number) => Promise<any>,
  handleRestoreField?: (planId: number) => Promise<any>,
  filter?: {
    setTableFilter?: SetterOrUpdater<IFilterPage>;
    tableFilter?: IFilterPage;
    type?: 'homeFilter' | 'fieldFilter';
  }
  // eslint-disable-next-line max-lines-per-function
) => ColumnsType<IPatient> = ( // NOSONAR
  cameraStatus,
  socketIsOpen,
  navigate,
  isRegistCoil,
  isNotConnectCoil,
  hasLicenseError,
  handleTestMeasure,
  intl,
  isField,
  handleField,
  handleRestoreField,
  filter
) => { // NOSONAR
  return [
    {
      title: intl?.formatMessage({ id: '姓名' }),
      dataIndex: 'name',
      key: 'name',
      width: 200,
      className: 'home-table-name',
      render: (text, record) => {
        return <NgListItem title={record.subject_model.name} maxWidth={163} />;
      },
    },
    {
      title: intl?.formatMessage({ id: 'ID' }),
      dataIndex: 'id',
      key: 'id',
      width: 200,
      className: 'home-table-id',
      render: (text, record) => {
        return <NgListItem title={record.subject_model.code} maxWidth={163} />;
      },
    },
    {
      title: intl?.formatMessage({ id: '运动阈值（%MO）' }),
      dataIndex: 'motionThreshold',
      key: 'motionThreshold',
      className: 'home-table-motionThreshold',
      width: 190,
      render: (text, record) => {
        return <>{!record.subject_model.motion_threshold ? '--' : record.subject_model.motion_threshold}</>;
      },
    },
    {
      title: intl?.formatMessage({ id: '方案状态' }),
      dataIndex: 'status',
      key: 'status',
      width: 150,
      className: classNames('table-type', 'home-table-status', filter?.tableFilter?.[filter?.type!]?.find(status => status.fieldName === 'status')?.value && styles.filter_active),
      // 此处为自定义筛选列表
      filterDropdown: (props: FilterDropdownProps) => (
        <CheckFilterDropdown
          domClassName={'.ng-table-column-title .ant-table-filter-trigger'}
          // 这个props可自行查阅文档，传入这个props是因为我们需要props中的confirm方法。
          {...props}
          defaultCheck={filter?.tableFilter?.[filter?.type!]?.find(status => status.fieldName === 'status')?.value}
          selectList={[
            // { type: 0, value: '全部' },
            { type: 1, value: '未完善' },
            { type: 2, value: '已完善' },
          ]}
          onChange={(item: IFilterType, close: any) => {
            const setType = filter?.type;
            const splitIndex = filter?.tableFilter?.[setType!]?.findIndex(name => name?.fieldName === 'status');
            const originFilter = [...(filter?.tableFilter?.[setType!] || [])];
            originFilter?.splice(splitIndex!, 1);
            const setArr = getChangeValue(filter, originFilter, item, 'status');
            // @ts-ignore
            filter?.setTableFilter?.(setArr);
            close();
          }}
        />
      ),
      render: status => {
        const statusMap = {
          [PlanStatus.Pending]: intl?.formatMessage({ id: '未完善' }),
          [PlanStatus.Normal]: intl?.formatMessage({ id: '已完善' }),
        };

        return <>{statusMap[status]}</>;
      },
      onHeaderCell: column => {
        return {
          className: 'ng-table-column-title',
        };
      },
    },
    {
      title: intl?.formatMessage({ id: '数据类型' }),
      dataIndex: 'type',
      key: 'type',
      width: 150,
      className: classNames('table-data-type', 'home-table-type', filter?.tableFilter?.[filter?.type!]?.find(status => status.fieldName === 'type')?.value && styles.filter_active),
      // 此处为自定义筛选列表
      filterDropdown: product.isNav
        ? (props: FilterDropdownProps) => {
            const [baseList, setBaseList] = useState([
            // NOSONAR
            // { type: 0, value: '全部' },
            { type: 1, value: '无影像' },
            ]);

            useAsyncEffect(async () => {
            // NOSONAR
              const currentName = await window.systemAPI.getEnv('name');
              let is200Or201 = currentName === `${200}` || currentName === `${201}`;
              if (!is200Or201) {
                setBaseList(v => [{ type: 2, value: '影像' }, ...v]);
              }
            }, []);

            return (
              <CheckFilterDropdown
                domClassName={'.table-data-type .ant-table-filter-trigger'}
                // 这个props可自行查阅文档，传入这个props是因为我们需要props中的confirm方法。
                {...props}
                selectList={baseList}
                defaultCheck={filter?.tableFilter?.[filter?.type!]?.find(status => status.fieldName === 'type')?.value}
                onChange={(item: IFilterType, close: any) => {
                  const setType = filter?.type;
                  const splitIndex = filter?.tableFilter?.[setType!]?.findIndex(name => name?.fieldName === 'type');
                  const originFilter = [...(filter?.tableFilter?.[setType!] || [])];
                  originFilter?.splice(splitIndex!, 1);
                  const setArr = getChangeValue(filter, originFilter, item, 'type');

                  // @ts-ignore
                  filter?.setTableFilter?.(setArr);

                  close();
                }}
              />
            );
          }
        : undefined,
      render: type => {
        const imageMap = {
          [ImageType.noImage]: intl?.formatMessage({ id: '无影像' }),
          [ImageType.withImage]: intl?.formatMessage({ id: '影像' }),
        };

        return <>{imageMap[type]}</>;
      },
      onHeaderCell: column => {
        return {
          className: 'ng-table-column-title',
        };
      },
    },
    {
      title: intl?.formatMessage({ id: '次数' }),
      dataIndex: 'treatment_count',
      key: 'treatment_count',
      className: 'home-table-treatment_count',
      width: 100,
    },
    {
      title: intl?.formatMessage({ id: '最近治疗时间' }),
      defaultSortOrder: filter?.tableFilter?.[filter.type!]?.find(item => item.fieldName === 'updated_treatment_at')?.sort,
      dataIndex: 'updated_treatment_at',
      width: 250,
      className: classNames('table-recent-treatment-at', 'home-table-updated_treatment_at', filter?.tableFilter?.[filter?.type!]?.find(status => status.fieldName === 'updated_treatment_at')?.value && styles.filter_active),
      key: 'updated_treatment_at',
      // 此处为自定义筛选列表
      filterDropdown: (props: FilterDropdownProps) => {
        return (
          <CheckFilterDropdown
            domClassName={'.table-recent-treatment-at .ant-table-filter-trigger'}
            // 这个props可自行查阅文档，传入这个props是因为我们需要props中的confirm方法。
            {...props}
            selectList={[
              // { type: 0, value: '全部' },
              { type: 1, value: '一周内' },
              { type: 2, value: '1个月内' },
              { type: 3, value: '3个月内' },
            ]}
            defaultCheck={filter?.tableFilter?.[filter?.type!]?.find(status => status.fieldName === 'updated_treatment_at')?.value}
            onChange={(item: IFilterType, close: any) => {
              generateTreatmentAtFilter(filter, item);

              close();
            }}
          />
        );
      },
      render: (time: Date, recordItem) => {
        return <>{time ? moment(time).format('YYYY-MM-DD HH:mm:ss') : '--'}</>;
      },
      sorter: {
        compare: (a, b) => {
          //
        },
      },
    },
    {
      title: intl?.formatMessage({ id: '创建时间' }),
      dataIndex: 'created_at',
      width: 250,
      defaultSortOrder: filter?.tableFilter?.[filter.type!]?.find(item => item.fieldName === 'create_at')?.sort,
      key: 'created_at',
      className: 'home-table-updated_created_at',
      render: (record: Date) => {
        return <>{moment(record).format('YYYY-MM-DD HH:mm:ss')}</>;
      },
      sorter: {
        compare: (a, b) => {
          //
        },
      },
    },
    {
      title: intl?.formatMessage({ id: '操作' }),
      dataIndex: 'operation',
      key: 'operation',
      render: (value, record, index) => {
        const { treatmentMessage, icon, treatmentClick } = generateTreatmentInfo(
          record,
          isNotConnectCoil,
          cameraStatus,
          socketIsOpen,
          isRegistCoil,
          hasLicenseError,
          intl,
          navigate
        );
        const {
          icon: MeasureIcon,
          isDisabled,
          measureMessage,
        } = getMeasureInfo(isNotConnectCoil, record, isRegistCoil, cameraStatus, socketIsOpen, hasLicenseError, intl);
        const { icon: EditIcon, editClick } = getEditInfo(record, hasLicenseError, navigate);
        const { ifGenerateReport, icon: ReportInfo, message } = getReportInfo(record, intl);
        const { icons: FieldIcon, message: fieldMsg } = generateFieldStatus(hasLicenseError, 'field');
        const { icons: RestoreIcon, message: restoreMsg } = generateFieldStatus(hasLicenseError, 'restore');

        return (
          <div className={styles.iconContainer} data-type="operation">
            {isField ? (
              <NgIcon
                fontSize={26}
                tooltip={{
                  overlayClassName: styles.iconToolTip,
                  title: restoreMsg,
                }}
                iconSvg={RestoreIcon}
                onClick={async e => {
                  e.stopPropagation();
                  if (hasLicenseError) return;
                  await handleRestoreField?.(record.id);
                }}
              />
            ) : (
              <>
                <NgIcon
                  style={{
                    width: 32,
                    height: 26,
                  }}
                  fontSize={32}
                  tooltip={{
                    overlayClassName: styles.iconToolTip,
                    title: treatmentMessage,
                  }}
                  iconSvg={icon}
                  onClick={treatmentClick}
                />

                <NgIcon
                  fontSize={26}
                  tooltip={{
                    overlayClassName: styles.iconToolTip,
                    title: measureMessage,
                  }}
                  iconSvg={MeasureIcon}
                  onClick={e => {
                    e.stopPropagation();
                    handleTestMeasure?.(isDisabled, record);
                  }}
                />
                <NgIcon
                  iconSvg={EditIcon}
                  fontSize={26}
                  tooltip={{
                    overlayClassName: styles.iconToolTip,
                    title: hasLicenseError ? '' : intl?.formatMessage({ id: '编辑' }),
                  }}
                  onClick={editClick}
                />
                <NgIcon
                  fontSize={26}
                  onClick={e => {
                    e.stopPropagation();
                    if (!ifGenerateReport) return;
                    navigate?.('/report', {
                      state: record,
                    });
                  }}
                  tooltip={{
                    overlayClassName: styles.iconToolTip,
                    title: message,
                  }}
                  iconSvg={ReportInfo}
                />
                <NgIcon
                  fontSize={26}
                  tooltip={{
                    overlayClassName: styles.iconToolTip,
                    title: fieldMsg,
                  }}
                  iconSvg={FieldIcon}
                  onClick={async e => {
                    e.stopPropagation();
                    if (hasLicenseError) return;
                    await handleField?.(record.id);
                  }}
                />
                {renderPreviewPlan(hasLicenseError, navigate, record)}
              </>
            )}
          </div>
        );
      },
    },
  ];
};

const generateTreatmentAtFilter = (filter: any, item: any) => {
  const setType = filter?.type;
  const splitIndex = filter?.tableFilter?.[setType]?.findIndex((name: any) => name?.fieldName === 'updated_treatment_at');
  const originFilter = [...(filter?.tableFilter?.[setType] || [])];
  const originList = originFilter?.splice(splitIndex, 1);
  const setArr = getChangeValue(filter, originFilter, item, 'updated_treatment_at', originList[0]);
  // @ts-ignore
  filter?.setTableFilter?.(setArr);
};
const generateFieldStatus = (hasLicenseError: boolean, type: 'field' | 'restore') => {
  let icons;
  let message;
  if (type === 'field') {
    icons = hasLicenseError ? DisableField : Filed;
    message = hasLicenseError ? '' : ' 操作后可至归档列表查看';
  } else {
    icons = hasLicenseError ? DisableRestore : Restore;
    message = hasLicenseError ? '' : ' 恢复';
  }

  return {
    icons,
    message,
  };
};

const getChangeValue = (filter: any, originFilter: any, item: any, fieldName: string, originInfo: any = {}) => {
  const setArr =
    filter?.type === 'homeFilter'
      ? {
          homeFilter: [
          ...(originFilter || []),
          {
            ...originInfo,
            fieldName,
            value: item?.type || 0,
          },
          ],
          fieldFilter: [...(filter?.tableFilter?.fieldFilter || [])],
        }
      : {
          fieldFilter: [
          ...(originFilter || []),
          {
            ...originInfo,
            fieldName,
            value: item?.type || 0,
          },
          ],
          homeFilter: [...(filter?.tableFilter?.homeFilter || [])],
        };

  return setArr;
};

const generateTreatmentMsg = (
  record: PlanModel,
  isRegistCoil: boolean,
  intl: IntlShape | undefined,
  isNotConnectCoil: boolean,
  cameraStatus: boolean,
  haslicenseError: boolean
) => {
  const { status, subject_model } = record;
  const motionThreshold = subject_model.motion_threshold || 0;
  let treatmentMessage = intl?.formatMessage({ id: '开始治疗' });
  if (
    haslicenseError ||
    isNotConnectCoil ||
    (!cameraStatus && record.type === 2) ||
    (!cameraStatus && record.type === 2 && record.status === PlanStatus.Normal)
  ) {
    treatmentMessage = '';
  } else if (status === PlanStatus.Pending) {
    treatmentMessage = intl?.formatMessage({ id: '请完善患者方案后，进行操作' });
    //  有影像需要判断是否注册线圈
  } else if (!isRegistCoil && record.type === 2) {
    treatmentMessage = intl?.formatMessage({ id: '请完成线圈注册后，进行操作' });
  } else if (!motionThreshold || motionThreshold <= 0) {
    treatmentMessage = intl?.formatMessage({ id: '请测量患者阈值后，进行操作' });
  }

  return treatmentMessage;
};

// 开始治疗
export const generateTreatmentInfo = (
  record: PlanModel,
  isNotConnectCoil: boolean,
  cameraStatus: boolean,
  socketIsOpen: boolean,
  isRegistCoil: boolean,
  hasLicenseError: boolean,
  intl: IntlShape | undefined,
  navigate: NavigateFunction | undefined
) => {
  const { status, subject_model } = record;
  const motionThreshold = subject_model.motion_threshold || 0;
  const baseDisabled = hasLicenseError || isNotConnectCoil;
  // 线圈没注册，license过期，无影像方案只判断有没有license错误
  const isDisabled = record.type === 1 ? baseDisabled : baseDisabled || !cameraStatus || !socketIsOpen || !isRegistCoil;

  let isDisabledStartTreatment = motionThreshold > 0 && status === PlanStatus.Normal && !isDisabled;
  let treatmentMessage = generateTreatmentMsg(record, isRegistCoil, intl, isNotConnectCoil, (cameraStatus && socketIsOpen), hasLicenseError);

  const icon = isDisabledStartTreatment ? StartTreatment : StartTreatmentDisable;
  const treatmentClick = (e: React.MouseEvent<HTMLSpanElement>) => {
    e.stopPropagation();
    if (isDev) {
      if (record.type === 1) {
        navigate?.(`/treatmentNoImagePlan/${record.id}/${record.subject_id}`);
      } else {
        navigate?.(`/previewTreat/${record.id}/${record.subject_id}`);
      }

      return;
    }
    if (!isDisabledStartTreatment) return;
    if (record.type === 1) {
      navigate?.(`/treatmentNoImagePlan/${record.id}/${record.subject_id}`);

      return;
    }
    navigate?.(`/previewTreat/${record.id}/${record.subject_id}`);
  };

  return {
    treatmentMessage,
    isDisabledStartTreatment,
    icon,
    treatmentClick,
  };
};

// 测量阈值
const getMeasureInfo = (
  isNotConnectCoil: boolean,
  record: PlanModel,
  isRegistCoil: boolean,
  cameraStatus: boolean,
  socketIsOpen: boolean,
  hasLicenseError: boolean,
  intl: IntlShape | undefined
) => {
  let icon = Measure;
  const hasMep = record.has_mep; // 需要从record里取是否包含MEP靶点
  const sameDisable = isNotConnectCoil || hasLicenseError;
  const withImageDisabled = hasMep ? sameDisable || !cameraStatus || !socketIsOpen || !isRegistCoil : sameDisable;
  // 没注册线圈或license过期
  let isDisabled = record.type === 1 ? sameDisable : withImageDisabled;
  if (isDisabled) icon = MeasureDisable;
  let measureMessage = intl?.formatMessage({ id: '测量阈值' });
  if (hasLicenseError || isNotConnectCoil || ((!cameraStatus || !socketIsOpen) && record.type === 2 && hasMep)) {
    measureMessage = '';
  } else if (!isRegistCoil && record.type === 2 && hasMep) {
    measureMessage = intl?.formatMessage({ id: '请完成线圈注册后，进行操作' });
  }

  return { icon, isDisabled, measureMessage };
};

// 编辑方案
const getEditInfo = (record: PlanModel, hasLicenseError: boolean, navigate: NavigateFunction | undefined) => {
  let icon = EditPlan;
  // license过期不可点
  let isEditDisabled = hasLicenseError;
  if (hasLicenseError) icon = EditPlanDisable;
  const editClick = (e: React.MouseEvent<HTMLSpanElement>) => {
    e.stopPropagation();
    if (isEditDisabled) return;
    const url = record.type === 1 ? 'previewNoImagePlan' : 'previewPlan';
    navigate?.(`/${url}/${record.subject_id}/${record.id}`);
  };

  return { icon, isEditDisabled, editClick };
};

// 报告
const getReportInfo = (record: PlanModel, intl: IntlShape | undefined) => {
  const { subject_model, report_count = 0 } = record;
  const motionThreshold = subject_model.motion_threshold || 0;
  const ifGenerateReport = report_count > 0 && motionThreshold > 0;
  const icon = ifGenerateReport ? Report : ReportDisable;
  const message = !ifGenerateReport ? '治疗后可查看结果' : intl?.formatMessage({ id: '治疗结果' });

  return {
    ifGenerateReport,
    icon,
    message,
  };
};

// 预览方案
const renderPreviewPlan = (hasLicenseError: boolean, navigate: NavigateFunction, record: PlanModel) => {
  if (hasLicenseError)
    return (
      <NgIcon
        iconSvg={PreviewPlan}
        fontSize={26}
        tooltip={{
          title: '查看',
        }}
        onClick={e => {
          e.stopPropagation();
          const url = record.type === 1 ? 'previewNoImagePlan' : 'previewPlan';
          navigate?.(`/${url}/${record.subject_id}/${record.id}`);
        }}
      />
    );

  return null;
};
