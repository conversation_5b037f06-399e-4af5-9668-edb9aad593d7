export interface Root {
  size: number;
  total: number;
  records: IPlanModel[];
  current: number;
}

export interface IPlanModel {
  id: number;
  name: string;
  subject_id: number;
  has_demo: boolean;
  updated_id: number;
  created_user_name: string;
  trace_id: string;
  plan_file_model_list: PlanFileModelList[];
  created_treatment_at: number;
  plan_delete_target_id_list: number[];
  plan_target_model_list: PlanTargetModelList[];
  stimulus: Stimulus2;
  target_count: number;
  status: number;
  created_at: number;
  subject_model: SubjectModel;
  treatment_count: number;
  updated_treatment_at: number;
  updated_at: number;
  remark: string;
  plan_import_model: PlanImportModel;
  type: number;
  created_id: number;
}

export interface PlanFileModelList {
  created_at: number;
  relative_path: string;
  trace_id: string;
  created_id: number;
  name: string;
  updated_id: number;
  subject_id: number;
  remark: string;
  id: number;
  updated_at: number;
  plan_id: number;
}

export interface PlanTargetModelList {
  hemi: string;
  name: string;
  surf_ras: SurfRas;
  vertex_index: number;
  vol_ras: VolRas;
  created_at: number;
  source: string;
  subject_id: number;
  updated_at: number;
  trace_id: string;
  id: number;
  horizontal: number;
  remark: string;
  type: string;
  code: string;
  stimulus: Stimulus;
  plan_id: number;
  updated_id: number;
  score_index: number;
  has_mep: boolean;
  created_id: number;
  score: number;
  clear_normal_line: boolean;
  normal_line: NormalLine;
}

export interface SurfRas {
  x: number;
  y: number;
  z: number;
}

export interface VolRas {
  x: number;
  y: number;
  z: number;
}

export interface Stimulus {
  pulse_total: number;
  relative_strength: number;
  strand_pulse_count: number;
  target_id: number;
  treatment_time: number;
  type: string;
  plan_id: number;
  plexus_inter_frequency: number;
  created_at: number;
  subject_id: number;
  plexus_count: number;
  updated_id: number;
  remark: string;
  source: number;
  id: number;
  inner_strand_pulse_count: number;
  trace_id: string;
  intermission_time: number;
  plexus_inner_frequency: number;
  created_id: number;
  updated_at: number;
  strand_pulse_frequency: number;
  plexus_inner_pulse_count: number;
}

export interface NormalLine {
  ver: number;
  x: number;
  y: number;
  z: number;
}

export interface Stimulus2 {
  pulse_total: number;
  relative_strength: number;
  strand_pulse_count: number;
  target_id: number;
  treatment_time: number;
  type: string;
  created_id: number;
  updated_at: number;
  plexus_inner_frequency: number;
  remark: string;
  created_at: number;
  strand_pulse_frequency: number;
  plexus_count: number;
  plexus_inner_pulse_count: number;
  id: number;
  updated_id: number;
  intermission_time: number;
  source: number;
  plexus_inter_frequency: number;
  subject_id: number;
  plan_id: number;
  inner_strand_pulse_count: number;
  trace_id: string;
}

export interface SubjectModel {
  code: string;
  id: number;
  name: string;
  pinyin_username: string;
  created_at: number;
  created_user_name: string;
  remark: string;
  condition_desc: string;
  has_demo: boolean;
  treatment_count: number;
  phone: string;
  plan_count: number;
  updated_at: number;
  motion_threshold: number;
  updated_id: number;
  birth_date: number;
  sex: number;
  created_id: number;
}

export interface PlanImportModel {
  file_name: string;
  plan_name: string;
  subject_id: number;
  temp_directory: string;
}

export enum Sex{
  '未知',
  '男',
  '女',
  '其他'
}
