import React, { useEffect, useRef, useState } from 'react';
import { useNavigate, Outlet } from 'react-router-dom';
import { UserSessionProps, withUserSession } from '../../hocComponent/withUserSession';
import { generateTreatmentInfo, getColumns } from './tableConfig';
import { ConfigProvider, Form, TablePaginationConfig } from 'antd';
import NgTable from '@/renderer/uiComponent/NgTable';
import styles from './index.module.less';
import CameraAndCoil from '@/renderer/component/cameraAndCoil';
import classnames from 'classnames';
import { NgLogoIcon } from '@/renderer/uiComponent/SvgGather';
import CreatePatientButton from '@/renderer/container/home/<USER>/createPatientButton';
import ToolBoxAndPower from '@/renderer/component/toolBoxAndPower';
import { getM200ApiInstance } from '@/common/api/ngApiAgent';
import { useAsyncEffect, useMount } from 'ahooks';
import {
  createSortMap,
  EnumList,
  IPlanListParams,
  MotionThresholdModel,
  PlanModel,
  PlanTargetModel,
  UpdatedTreatmentAtRange,
  updateSortMap,
} from '@/common/types';
import { useRecoilState, useSetRecoilState } from 'recoil';
import { tmsCoilSelector } from '@/renderer/recoil/tmsError';
import { useLicenseAtom } from '@/renderer/recoil/license';
import { debounce, omit } from 'lodash';
import { SubjectMeasurement } from './components/subjectMeasurement';
import { injectIntl, useIntl } from 'react-intl';
import { IntlPropType } from '@/common/types/propTypes';
import { isDev } from '@/common/lib/env/nodeEnv';
import { IFilterPage, IFilterType, useFilterParams } from '@/renderer/recoil/tableFilterParams';
import { usePageParams } from '@/renderer/recoil/pageParams';
import { ErrorModel } from '../../component/systemErrorModel/errorModel';
import { MeasurementStrength } from './components/measurement';
import { TMSScreenState } from '../../../common/constant/tms';
import ChangePasswordModal from '@/renderer/container/home/<USER>/ChangePasswordModal';
import { axiosHttpClient } from '@/common/api/httpClient/axiosHttpClientImplMain';
import NgMessage from '@/renderer/uiComponent/NgMessage';
import { sendRenderLog } from '../../utils/renderLogger';
import { connSocket } from '../../utils/imgSocket';
import { isNotTreatingAtom } from '../../recoil/isNotTreating';
import { notNAVTypeList } from '../../../common/systemFault/config';
import { FaultEnum, FaultKeyEnum, FaultStatusEnum } from '../../../common/systemFault/type';
import { faultAtom, getFaultByKey, getFaultWithoutType } from '../../recoil/fault';
import { queryCoilInfo } from '../../router/utils';

type Props = UserSessionProps;
// 格式化筛选请求体
export const formatFilter = (filter: IFilterType[] | undefined) => {
  const result = filter?.reduce((prev, item) => {
    switch (item.fieldName) {
      case 'status':
        let statusList: EnumList[] = [];
        if (item.value === 1) {
          statusList = [EnumList.The1];
        } else if (item.value === 2) {
          statusList = [EnumList.The2];
        }
        // 方案状态筛选
        prev.status_list = statusList;
        break;
      case 'type':
        let typeList: EnumList[] = [];
        if (item.value === 1) {
          typeList = [EnumList.The1];
        } else if (item.value === 2) {
          typeList = [EnumList.The2];
        }
        // 方案状态筛选
        prev.type_list = typeList;
        break;
      case 'create_at':
        // 已有最近治疗时间的sort，直接返回避免重新赋值
        if (prev.sort_by === '3' || prev.sort_by === '4') return prev;
        prev.sort_by = createSortMap[item?.sort!];
        break;
      case 'updated_treatment_at':
        const maps = {
          0: undefined,
          1: UpdatedTreatmentAtRange.The1,
          2: UpdatedTreatmentAtRange.The2,
          3: UpdatedTreatmentAtRange.The3,
        };

        prev.updated_treatment_at_range = maps[item.value];
        if (item?.sort !== undefined) {
          prev.sort_by = updateSortMap[item?.sort];
        }
        prev.noFetch = item.noFetch;
        break;
    }

    return prev;
  }, {} as Partial<IPlanListParams>);

  return result;
};

export const setTableSort = (sorter: any, tableFilter: IFilterPage, setTableFilter: any, type: 'homeFilter' | 'fieldFilter') => {
  const saveType = type === 'homeFilter' ? 'fieldFilter' : 'homeFilter';
  if (sorter.field === 'updated_treatment_at') {
    const baseFilter = [...(tableFilter[type] || [])];
    const index = baseFilter?.findIndex(i => i.fieldName === 'updated_treatment_at');
    const item = baseFilter?.find((_item, i) => index === i);
    const createIndex = baseFilter?.findIndex(i => i?.fieldName === 'create_at');
    // // eslint-disable-next-line @typescript-eslint/no-shadow
    const createItem = baseFilter?.find((_item, i) => createIndex === i);
    // @ts-ignore 设置当前点击列的排序
    baseFilter.splice(index, 1, {
      ...item,
      sort: sorter.order,
      noFetch: true,
    });
    // @ts-ignore 互斥排序
    baseFilter.splice(createIndex, 1, {
      ...createItem,
      sort: undefined,
      noFetch: true,
    });
    setTableFilter({
      [type]: [...(baseFilter || [])],
      [saveType]: [...(tableFilter[saveType] || [])],
    });
  } else if (sorter.field === 'created_at') {
    const baseFilter = [...(tableFilter[type] || [])];
    const index = baseFilter?.findIndex(i => i?.fieldName === 'create_at');
    // eslint-disable-next-line @typescript-eslint/no-shadow
    const item = baseFilter?.find((_item, i) => index === i);
    const updateIndex = baseFilter?.findIndex(i => i?.fieldName === 'updated_treatment_at');
    // eslint-disable-next-line @typescript-eslint/no-shadow
    const updateItem = baseFilter?.find((_item, i) => updateIndex === i);
    // @ts-ignore
    baseFilter.splice(index, 1, {
      ...item,
      sort: sorter.order,
      noFetch: true,
    });
    // @ts-ignore
    baseFilter.splice(updateIndex, 1, {
      ...updateItem,
      sort: undefined,
      noFetch: true,
    });
    setTableFilter({
      [type]: [...(baseFilter || [])],
      [saveType]: [...(tableFilter[saveType] || [])],
    });
  }
};
// eslint-disable-next-line max-lines-per-function
const Home: React.FC<Props & IntlPropType> = (props: Props & IntlPropType) => {
  const navigate = useNavigate();
  const { contextHolder, success: MsgSuccess, error: MsgError } = NgMessage.useMessage();
  const [newDataCount, setNewDataCount] = useState<number>(0);
  const intl = useIntl();
  const totalCurrent = useRef(0);
  const [openPwdModal, setOpenPwdModal] = useState(props.userSession?.should_change_password || false);
  const [form] = Form.useForm();
  //  原有的数据源，用于筛选使用
  const [data, setData] = useState<PlanModel[]>([]);
  const [filterData, setFilterData] = useState<PlanModel[]>([]);
  const [coilSelector] = useRecoilState(tmsCoilSelector);
  const [tableFilter, setTableFilter] = useRecoilState(useFilterParams);
  const [license] = useRecoilState(useLicenseAtom);
  const [fault] = useRecoilState(faultAtom);
  const isRegistCoil = coilSelector?.isRegisterCoil;
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const [openMeasurement, setOpenMeasurement] = useState(false);
  const [planModel, setPlanModel] = useState<PlanModel | undefined>();
  const [subjectName, setSubjectName] = useState<string | undefined>('');
  const [pageParams, setPageParams] = useRecoilState(usePageParams);
  const { homePage } = pageParams;
  const [searchValue, setSearchValue] = useState('');
  const [_forceUpdate, setForceUpdate] = useState(false);
  const [openMeasurementStrength, setOpenMeasurementStrength] = useState(false);
  const tmsInfo = useRef({ threshold: 0 });
  const [isShowMessage, setIsShowMessage] = useState(false);
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    total: 0,
    current: 1,
    pageSize: 15,
  });
  const setIsNotTreating = useSetRecoilState(isNotTreatingAtom);
  const setTmsCoil = useSetRecoilState(tmsCoilSelector);

  /**
   * 监听单脉冲
   */
  useEffect(() => {
    setIsNotTreating(!openMeasurementStrength);
  }, [openMeasurementStrength]);

  /**
   * 监听无影像阈值测量
   */
  useEffect(() => {
    setIsNotTreating(!openMeasurement);
  }, [openMeasurement]);

  useAsyncEffect(async () => {
    await queryCoilInfo({ setCoilSelector: setTmsCoil });
    window.tmsAPI.set_beat_screen(TMSScreenState.NotStarted);
    await window.tmsAPI.noImage_treatment_plan_start('PlanEnd');
  }, []);

  const handleErrorModalOK = () => {
    setOpenMeasurement(false);
    setOpenMeasurementStrength(false);
  };
  // 测量阈值回调
  const handleTestMeasure = async (isDisabled: boolean, record: PlanModel) => {
    const plan = await m200Api.getPlanById(record.id, record.subject_id);
    const hasMep = plan.plan_target_model_list?.find((obj: PlanTargetModel) => obj.has_mep);
    if (isDev) {
      return gotoEmg(record, plan, hasMep);
    }
    if (isDisabled) return;
    connSocket.getImage = false; // 避免之前退出的时候有请求刚发出去，退回到首页结果返回造成getImage为true
    gotoEmg(record, plan, hasMep);
  };
  const m200Api = getM200ApiInstance();
  const gotoEmg = (record: PlanModel, plan: PlanModel, hasMep: PlanTargetModel | undefined) => {
    if (hasMep) {
      navigate?.(`/emg/${record.id}/${record.subject_id}`);
    } else {
      setSubjectName(record.subject_model.name);
      setOpenMeasurement(true);
      setPlanModel(plan);
      tmsInfo.current.threshold = planModel?.subject_model.motion_threshold || 60;
    }
  };
  // 归档数据
  const handleField = async (planId: number) => {
    try {
      await m200Api.archiveFiled({
        plan_id: planId,
        has_archive: true,
      });
      await fetchPlanList({
        ...formatFilter(tableFilter.homeFilter),
        keyword: searchValue,
        page_num: pagination.current,
        page_size: homePage?.pageSize,
      });
    } catch (e) {
      sendRenderLog.error(`归档数据报错： ${e}`);
    }
  };
  const columns = getColumns(
    !getFaultByKey(FaultKeyEnum.A080002, fault),
    !getFaultByKey(FaultKeyEnum.A080001, fault),
    navigate,
    isRegistCoil,
    getFaultWithoutType(FaultEnum.imageFault, fault).length > 0,
    license.hasLicenseError as boolean,
    handleTestMeasure,
    intl,
    false,
    handleField,
    // @ts-ignore
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    () => {},
    {
      setTableFilter,
      tableFilter,
      type: 'homeFilter',
    }
  );
  React.useEffect(() => {
    // data修改手动刷新组件，确保在getColumns中可以正确的获取dom，超长展示tooltips
    setForceUpdate(v => !v);
  }, [filterData]);
  const handlePaginationChange = async (current: number, pageSize: number) => {
    await fetchPlanList({
      ...omit(pagination, 'pageSize', 'current'),
      ...formatFilter(tableFilter.homeFilter),
      keyword: searchValue,
      page_num: current,
      page_size: pageSize,
    });
    setPageParams({
      homePage: {
        ...homePage,
        pageSize,
      },
      fieldPage: { ...pageParams.fieldPage },
    });
  };
  useAsyncEffect(async () => {
    let result = formatFilter(tableFilter.homeFilter);
    await fetchPlanList({
      ...result,
      keyword: searchValue,
      page_num: 1,
      page_size: homePage?.pageSize,
    });
  }, [JSON.stringify(tableFilter.homeFilter)]);

  useMount(() => {
    window.systemAPI.pushSystemFault({ '0A030005': FaultStatusEnum.normal }, 'home page 初始化清除');
  });

  const refreshTableAuto = async () => {
    let hasTherapy = await window.systemAPI.getEnv('hasTherapy');
    if (!hasTherapy) return;
    intervalRef.current = setInterval(async () => {
      try {
        let total = await m200Api.getPlanCount();
        sendRenderLog.info(
          '修改数据！！！！！！',
          JSON.stringify({
            res: total,
            total: totalCurrent.current,
          })
        );
        if (total !== totalCurrent.current) {
          sendRenderLog.info(`有数据推送：
          原有条数: ${totalCurrent.current},
          最新条数：${total}`);
          setNewDataCount(total - totalCurrent.current);
        }
      } catch (error) {
        // eslint-disable-next-line no-console
        console.log('fetch error');
      }
    }, 600000);
  };

  useEffect(() => {
    // clearInterval(intervalRef.current!);
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    refreshTableAuto();

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);
  const fetchPlanList = async (params?: IPlanListParams, isRefresh = false) => {
    try {
      let res = await m200Api.getPlanList({
        ...params,
      });
      const pageInfo = {
        current: res.current,
        pageSize: res.size,
        total: res.total,
      };
      totalCurrent.current = res.all_total;
      if (res.records.length === 0 && pagination?.current! > 1) {
        pageInfo.current -= 1;
        res = await m200Api.getPlanList({
          ...params,
          page_num: pageInfo.current,
        });
      }
      setData(res?.records);
      setFilterData(res?.records);

      setPagination({
        ...pageInfo,
      });
      if (isRefresh) {
        if (isShowMessage) return;
        setIsShowMessage(true);
        // eslint-disable-next-line no-void, @typescript-eslint/no-floating-promises
        MsgSuccess({
          content: '操作成功',
          duration: 3000,
          onClose: () => setIsShowMessage(false),
        });
      }

      return res;
    } catch (e) {
      if (isRefresh) {
        if (isShowMessage) return;
        setIsShowMessage(true);
        // eslint-disable-next-line no-void, @typescript-eslint/no-floating-promises
        MsgError({
          content: '刷新失败',
          duration: 3000,
          onClose: () => setIsShowMessage(false),
        });
      }
      sendRenderLog.error(`
        page:home,
        api:/pro/plan/page,
        error:${JSON.stringify(e)}
      `);
    }

    return {};
  };
  const handleSearch = debounce(async (value: string) => {
    setSearchValue(value);
    await fetchPlanList({
      ...formatFilter(tableFilter.homeFilter),
      keyword: value,
      page_size: homePage?.pageSize,
      page_num: 1,
    });
  }, 500);

  const onCancelClick = () => {
    setOpenMeasurement(false);
  };

  const onMeasurementStrengthCancelClick = async () => {
    setOpenMeasurementStrength(false);
  };

  const setThresholdNum = (num: number) => {
    tmsInfo.current.threshold = num;
  };

  const onSaveStrength = async (threshold: number) => {
    const params: MotionThresholdModel = {
      subject_id: planModel?.subject_id!,
      plan_id: planModel?.id!,
      motion_threshold: threshold,
    };
    await getM200ApiInstance().saveThreshold(params);
    await fetchPlanList({
      ...formatFilter(tableFilter.homeFilter),
      keyword: searchValue,
      page_num: pagination.current,
      page_size: homePage?.pageSize,
    });
    setOpenMeasurement(false);
  };
  const handleChangePassword = async () => {
    await form.validateFields();
    let formResult = form.getFieldsValue();
    let requestBody = {
      password: formResult.origin,
      new_password: formResult.confirm,
    };
    let isSuccess = await getM200ApiInstance()
      .changePWD(requestBody)
      .catch(error => {
        if (error.code === 'SU10104') {
          form.setFields([
            {
              name: 'origin',
              errors: [intl.formatMessage({ id: '密码错误' })],
            },
          ]);
        }
      });
    if (isSuccess) {
      setOpenPwdModal(false);
      navigate('/logout');
      await window.authAPI.logout();
      axiosHttpClient.resetAuthToken();
      form.resetFields();
    }
  };

  return (
    <div className={classnames(styles.homeContainer, 'ng_home_container')}>
      {contextHolder}
      <ChangePasswordModal
        title={intl.formatMessage({ id: '重置密码' })}
        open={openPwdModal}
        onOk={handleChangePassword}
        onCancel={() => {
          setOpenPwdModal(false);
          navigate('/login');
        }}
        form={form}
      />
      <div className={classnames(styles.homeHeader, 'ng_setting')}>
        <CameraAndCoil />

        <div className={styles.left}>
          <NgLogoIcon />
        </div>
        <div className={styles.right}>
          <ToolBoxAndPower />
        </div>
      </div>
      <div className={styles.operationContainer}>
        <CreatePatientButton
          onSearch={handleSearch}
          setOpenMeasurementStrength={() => setOpenMeasurementStrength(true)}
          newDataCount={newDataCount}
          onRefreshData={async () => {
            if (isShowMessage) return;
            // 刷新只保留筛选， 不保留搜索
            setSearchValue('');
            setNewDataCount(0);
            await fetchPlanList(
              {
                ...formatFilter(tableFilter.homeFilter),
                page_num: 1,
                page_size: 15,
              },
              true
            );
          }}
        />
      </div>

      <div
        className={classnames(styles.tableContainer, {
          [styles.emptyTable]: filterData.length <= 0,
        })}
      >
        <ConfigProvider
          renderEmpty={() => {
            return <></>;
          }}
        >
          <NgTable<PlanModel>
            onRow={record => {
              return {
                onClick: (event: any) => {
                  if (event.target.dataset?.type === 'operation') return;
                  const { treatmentClick } = generateTreatmentInfo(
                    record,
                    getFaultWithoutType(FaultEnum.imageFault, fault).length > 0,
                    !getFaultByKey(FaultKeyEnum.A080002, fault),
                    !getFaultByKey(FaultKeyEnum.A080001, fault),
                    isRegistCoil,
                    license.hasLicenseError as boolean,
                    intl,
                    navigate
                  );
                  treatmentClick(event);
                },
              };
            }}
            onChange={(_, _filters, sorter: any, extra) => {
              // 屏蔽翻页，避免错误调用全量排序
              if (pagination.current !== _.current || pagination.pageSize !== _.pageSize) {
                return;
              }
              setTableSort(sorter, tableFilter, setTableFilter, 'homeFilter');
            }}
            showSorterTooltip={{
              open: false,
            }}
            rowKey={record => record.id}
            data={data}
            columns={columns}
            scroll={{ x: 'max-content', y: 825 }}
            pagination={{
              ...pagination,
              pageSize: homePage?.pageSize,
              showTotal: (total, range) => {
                return <>共{total}条</>;
              },
              defaultPageSize: 15,
              onChange: handlePaginationChange,
              showQuickJumper: true,
              pageSizeOptions: ['15', '20', '50', '100'],
              showSizeChanger: true, // 隐藏每页页数
            }}
          />
        </ConfigProvider>
      </div>
      {openMeasurement && (
        <SubjectMeasurement
          onCancelClick={onCancelClick}
          onOkClick={onSaveStrength}
          setThreshold={setThresholdNum}
          title="测量阈值"
          // title={`${props.intl.formatMessage({ id: '单脉冲' })}`}
          name={subjectName}
          isSave
          hasFault={getFaultWithoutType(FaultEnum.imageFault, fault).length > 0}
          threshold={planModel?.subject_model.motion_threshold}
        />
      )}
      {(openMeasurementStrength || openMeasurement) && <ErrorModel faultTypeList={notNAVTypeList} isStimulate onOk={handleErrorModalOK} />}
      {openMeasurementStrength && (
        <MeasurementStrength
          hasFault={getFaultWithoutType(FaultEnum.imageFault, fault).length > 0}
          onCancelClick={onMeasurementStrengthCancelClick}
          onOkClick={onMeasurementStrengthCancelClick}
          setThreshold={setThresholdNum}
          title={intl.formatMessage({ id: '单脉冲' })}
        />
      )}
      <Outlet />
    </div>
  );
};

export default withUserSession(injectIntl(Home));
