@import '@/renderer/static/style/baseColor.module.less';

.modal {
  :global {
    .ant-modal-title {
      font-weight: 500;
    }
    .ant-modal-body {
      margin-top: -12px;
    }
    .ant-modal-content {
      width: 350px;
      height: 314px;
      padding: 24px;
    }
    .ant-modal-close {
      top: 25px;
    }
    .ant-modal-footer {
      margin-top: 30px;
      display: flex;
      justify-content: flex-end;
    }
  }

  .name {
    height: 37px;
    font-size: 16px;
    font-weight: 500;
    color: white;
    font-family: medium-font, serif;
    line-height: 24px;
  }

  .strength_progress {
    display: flex;
    margin-top: 20px;
    .subtitle {
      line-height: 120px;
      text-align: center;
    }
    .min_max {
      background: @colorA4;
      line-height: normal;
    }
    .ng_strength_progress {
      margin-left: 10px;
    }
  }

  .buttons {
    display: flex;
    button {
      margin-left: 20px;
    }
  }
}
