import React, { useRef, useState } from 'react';
import NgModal from '../../../../uiComponent/NgModal';
import { NgStrengthProgress } from '../../../../uiComponent/NgProgress';
import styles from './index.module.less';
import { useAsyncEffect, useThrottleFn, useUnmount } from 'ahooks';
import NgDarkButton from '../../../../uiComponent/NgDarkButton';
import { ModalProps } from 'antd';
import { TMSScreenState } from '../../../../../common/constant/tms';
import { getTh, ThresholdEnum } from '../subjectMeasurement';
import { IpcRendererEvent } from 'electron';
import { throttleOption } from '../../../../utils';

export type MeasurementStrengthType = {
  hasFault: boolean;
  onCancelClick?(): void;
  onOkClick(threshold?: number): void;
  setThreshold?(num: number): void;
  isSave?: boolean;
  title?: string;
  name?: string;
  threshold?: number;
  contentLabel?: string;
  children?: JSX.Element;
} & ModalProps;

const Footer = (props: MeasurementStrengthType) => {
  if (props.isSave) {
    return (
      <div style={{ marginTop: props.name ? 0 : 10 }} className={styles.buttons}>
        <NgDarkButton onClick={props.onCancelClick}>取消</NgDarkButton>
        <NgDarkButton onClick={() => props.onOkClick(props.threshold)}>结束</NgDarkButton>
      </div>
    );
  }

  return <NgDarkButton onClick={() => props.onOkClick(0)}>结束</NgDarkButton>;
};

export const MeasurementStrength = (props: MeasurementStrengthType) => {
  const [threshold, setThreshold] = useState(props.threshold || 60);
  const tmsInfo = useRef({
    isOpen: false,
    threshold: props.threshold || 60,
    thresholdType: ThresholdEnum.Measurement,
  });
  useAsyncEffect(async () => {
    await window.tmsAPI.set_treatment_threshold({ level: threshold });
    window.tmsAPI.set_beat_screen(TMSScreenState.SingleTreat);
  }, []);

  useAsyncEffect(async () => {
    await addListenerBeat();
  }, [props.hasFault]);

  useUnmount(async () => {
    window.tmsAPI.remove_beat_btn_by_key('MeasurementStrength');
    window.tmsAPI.remove_sock_info_by_key('MeasurementStrength');
    await window.tmsAPI.noImage_treatment_plan_start('SingleEnd');
    window.tmsAPI.set_beat_screen(TMSScreenState.NotStarted);
  });

  /**
   * 添加拍子节流
   */
  const { run: debounceBeatBtnData } = useThrottleFn((_data: any) => {
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    window.tmsAPI.noImage_treatment_plan_start('SingleStart');
  }, throttleOption);

  const addListenerBeat = async () => {
    window.tmsAPI.remove_beat_btn_by_key('MeasurementStrength');
    window.tmsAPI.remove_sock_info_by_key('MeasurementStrength');
    await window.tmsAPI.beat_btn_by_key('MeasurementStrength', async (event: IpcRendererEvent, data: any) => {
      if (tmsInfo.current.thresholdType === ThresholdEnum.Input) return;
      if (props.hasFault) return;
      if (data.data.key === 'play') {
        debounceBeatBtnData(data);

        return;
      }
      const th = getTh(data, tmsInfo.current.threshold);
      tmsInfo.current.threshold = th;
      setThreshold(tmsInfo.current.threshold);
      props.setThreshold?.(tmsInfo.current.threshold);
      window.tmsAPI.set_treatment_threshold({ level: tmsInfo.current.threshold });
    });
    window.tmsAPI.get_sock_info_by_key('MeasurementStrength', async (value, data) => {
      if (data.open && tmsInfo.current.isOpen !== data.open) {
        window.tmsAPI.set_treatment_threshold({ level: tmsInfo.current.threshold });
      }
      tmsInfo.current.isOpen = data.open;
    });
  };

  return (
    <NgModal
      title={props.title}
      okText="结束"
      footer={<Footer {...props} threshold={threshold} />}
      className={styles.modal}
      onCancel={props.onCancelClick}
      wrapClassName={props.wrapClassName}
      maskClosable={false}
      keyboard={false}
      width={349}
      open
    >
      {props.children}
      {props.name && (
        <div className={styles.name}>
          <span>姓名: </span>
          <span>{props.name}</span>
        </div>
      )}
      <div style={{ marginTop: props.name || props.children ? 20 : 65 }} className={styles.strength_progress}>
        <div className={styles.subtitle}>{props.contentLabel || '实际强度（%MO）：'}</div>
        <NgStrengthProgress
          min_value={5}
          percent={threshold}
          radius={60}
          ng_strength_progress={styles.ng_strength_progress}
          customBackgroundColorClass={styles.min_max}
        />
      </div>
    </NgModal>
  );
};
