import React, { useEffect, useRef, useState } from 'react';
import NgModal from '../../../../uiComponent/NgModal';
import { NgStrengthProgress } from '../../../../uiComponent/NgProgress';
import styles from './index.module.less';
import { IpcRendererEvent } from 'electron';
import { useAsyncEffect, useThrottleFn, useUnmount } from 'ahooks';
import { NgRadio } from '../../../../uiComponent/NgRadio';
import { Form, RadioChangeEvent } from 'antd';
import { NgInputNumber } from '../../../../uiComponent/NgInputNumber';
import { NgForm } from '../../../../uiComponent/NgForm';
import { InputStatus } from 'antd/es/_util/statusUtils';
import NgDarkButton from '../../../../uiComponent/NgDarkButton';
import { TMSScreenState } from '../../../../../common/constant/tms';
import { debounce } from 'lodash';
import { throttleOption } from '../../../../utils';

export type MeasurementStrengthType = {
  hasFault: boolean;
  onCancelClick?(): void;
  setStartLoading?(): void;
  setEndLoading?(): void;
  onOkClick(threshold?: number): void;
  onChangeRadio?(e: ThresholdEnum): void;
  setOkbuttonisDisabled?(isDisabled: boolean): void;
  setThreshold?(num: number): void;
  setIsAllDisabledFalse?(): void;
  setIsAllDisabledTrue?(): void;
  isEmg?: boolean;
  allDisabled?: boolean;
  isStartInterval?: boolean;
  isSave?: boolean;
  isDisabled?: boolean;
  title?: string;
  name?: string;
  threshold?: number;
  thresholdType?: ThresholdEnum;
};

export const getTh = (data: any, threshold: number) => {
  // eslint-disable-next-line @typescript-eslint/restrict-plus-operands
  let th = data.data.key === 'add' ? threshold + data.data.step : threshold - data.data.step;
  th = data.data.key === 'add' && th > 100 ? 100 : th;
  th = data.data.key === 'sub' && th < 5 ? 5 : th;

  return th;
};

const Footer = (props: MeasurementStrengthType) => {
  const marginTop = props.thresholdType === ThresholdEnum.Measurement || props.isStartInterval ? -20 : 0;
  const threshold = props.threshold;

  return (
    <div style={{ marginTop }} className={styles.buttons}>
      <NgDarkButton disabled={props.isStartInterval} onClick={props.onCancelClick}>
        取消
      </NgDarkButton>
      <NgDarkButton disabled={props.isDisabled || props.isStartInterval} onClick={() => props.onOkClick(threshold)}>
        保存
      </NgDarkButton>
    </div>
  );
};

export enum ThresholdEnum {
  Measurement = 1,
  Input = 2,
}

export const SelectedMeasurement = (props: MeasurementStrengthType) => {
  const [form] = Form.useForm();
  const inputValue = useRef<{ start: number | undefined; end: number | undefined }>({ start: 0, end: 0 });
  const [inputState, setInputState] = useState<InputStatus>('');
  const handleChangeValues = debounce(async (changeValues: any, values: any) => {
    try {
      await handValidateInputThreshold(undefined, values.inputThreshold);
      inputValue.current.start = props.threshold;
      inputValue.current.end = values.inputThreshold;
      props.setOkbuttonisDisabled?.(false);
      setInputState('');
    } catch (error) {
      props.setOkbuttonisDisabled?.(true);
      setInputState('error');
    }
  }, 500);

  const onChangeRadio = async (e: RadioChangeEvent) => {
    props.onChangeRadio?.(e.target.value);
    try {
      form.setFieldValue('inputThreshold', props.threshold);
      await form.validateFields(['inputThreshold']);
      setInputState('');
      props.setIsAllDisabledTrue?.();
      if (e.target.value === ThresholdEnum.Input) {
        inputValue.current.start = 0;
        inputValue.current.end = 0;
      }
      // eslint-disable-next-line @typescript-eslint/no-floating-promises
      startIntervalFun()
        .then(() => {
          props.setIsAllDisabledFalse?.();
        })
        .catch(() => {
          props.setIsAllDisabledFalse?.();
        });
    } catch (error) {
      setInputState('error');
      // eslint-disable-next-line no-console
      console.log(error, '---');
    }
  };

  const handValidateInputThreshold = async (fields: any, value: number) => {
    return new Promise((resolve, reject) => {
      if (value === null) {
        return reject('不可为空');
      } else if (typeof value !== 'number') {
        return reject('不符合限制');
      } else if (value < 5 || value > 100) {
        return reject('不符合限制');
      } else if (value.toString().indexOf('.') !== -1) {
        return reject('仅允许输入整数');
      } else {
        return resolve('');
      }
    });
  };

  useAsyncEffect(async () => {
    if (props.isStartInterval) {
      // eslint-disable-next-line @typescript-eslint/no-floating-promises
      startIntervalFun().then(() => {
        props.setThreshold?.(inputValue.current.end! || props.threshold!);
        props.onOkClick(inputValue.current.end || props.threshold!);
      });
    }
  }, [props.isStartInterval]);

  const startIntervalFun = async () => {
    return new Promise((resolve: any) => {
      const end = inputValue.current.end;
      if (end && props.threshold && end < props.threshold) {
        const interval = setInterval(() => {
          if (inputValue.current.end && inputValue.current.end < inputValue.current.start!) {
            inputValue.current.start = inputValue.current.start! - 1;
            props.setThreshold?.(inputValue.current.start);
          } else {
            clearInterval(interval);
            props.setIsAllDisabledFalse?.();
            resolve();
          }
        }, 150);
      } else {
        props.setThreshold?.(inputValue.current.end || props.threshold!);
        resolve();
      }
    });
  };

  const getMinThreshold = () => {
    return 5;
  };

  return (
    <>
      <div className={styles.radios}>
        <NgRadio.Group optionType="button" onChange={onChangeRadio} disabled={props.allDisabled} defaultValue={props.thresholdType}>
          <NgRadio disabled={inputState === 'error' || props.isStartInterval} value={ThresholdEnum.Measurement}>
            测量
          </NgRadio>
          <NgRadio value={ThresholdEnum.Input}>录入</NgRadio>
        </NgRadio.Group>
      </div>
      {(props.isStartInterval || props.thresholdType === ThresholdEnum.Measurement) && (
        <div style={{ marginTop: -4 }} className={styles.strength_progress}>
          <div className={styles.subtitle}>运动阈值（%MO）：</div>
          <NgStrengthProgress
            percent={props.threshold || 0}
            radius={60}
            min_value={getMinThreshold()}
            ng_strength_progress={styles.ng_strength_progress}
            customBackgroundColorClass={props.isEmg ? '' : styles.min_max}
          />
        </div>
      )}
      {!props.isStartInterval && props.thresholdType === ThresholdEnum.Input && (
        <div style={{ marginTop: 40, paddingRight: 20 }} className={styles.strength_progress}>
          <NgForm form={form} onValuesChange={handleChangeValues}>
            <Form.Item
              rules={[{ validator: handValidateInputThreshold }]}
              name="inputThreshold"
              labelAlign={'left'}
              labelCol={{ span: props.isEmg ? 13 : 9 }}
              label={<span>运动阈值（%MO）：</span>}
            >
              <NgInputNumber status={inputState} value={props.threshold} />
            </Form.Item>
            <div style={props.isEmg ? { right: 20 } : { right: 156 }} className={styles.translation}>{`${getMinThreshold()}-100`}</div>
          </NgForm>
        </div>
      )}
    </>
  );
};

export const SubjectMeasurement = (props: MeasurementStrengthType) => {
  const [threshold, setThreshold] = useState(props.threshold || 60);
  const [isDisabled, setIsDisabled] = useState<undefined | boolean>();
  // const [inputThreshold, setInputThreshold] = useState(60);
  const [thresholdType, setThresholdEnum] = useState(ThresholdEnum.Measurement);
  const [isStartInterval, setIsStartInterval] = useState<boolean>(false);
  const [allDisabled, setAllDisabled] = useState(false);
  const tmsInfo = useRef({
    isOpen: false,
    threshold: props.threshold || 60,
    thresholdType: ThresholdEnum.Measurement,
  });
  const setInputThresholdNum = async (num: number) => {
    setThreshold(num);
    props.setThreshold!(num);
    tmsInfo.current.threshold = num;
    window.tmsAPI.set_treatment_threshold({ level: tmsInfo.current.threshold });
  };
  const setOkbuttonisDisabled = (disabled: boolean) => {
    setIsDisabled(disabled);
  };
  const onChangeRadio = async (e: ThresholdEnum) => {
    setThresholdEnum(e);
    tmsInfo.current.thresholdType = e;
    if (e === ThresholdEnum.Measurement) {
      window.tmsAPI.set_treatment_threshold({ level: threshold });
    }
  };

  const endTms = () => {
    window.tmsAPI.remove_beat_btn_by_key('MeasurementStrength');
    window.tmsAPI.remove_sock_info_by_key('MeasurementStrength');
    window.tmsAPI.set_beat_screen(TMSScreenState.NotStarted);
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    window.tmsAPI.noImage_treatment_plan_start('SingleEnd');
  };

  useAsyncEffect(async () => {
    window.tmsAPI.set_treatment_threshold({ level: threshold });
    window.tmsAPI.set_beat_screen(TMSScreenState.SingleTreat);
  }, []);
  useAsyncEffect(async () => {
    await addListenerBeat();
  }, [allDisabled, isStartInterval]);

  useEffect(() => {
    if (!props.hasFault) return;
    endTms();
  }, [props.hasFault]);

  useUnmount(async () => {
    endTms();
  });

  /**
   * 添加拍子节流
   */
  const { run: debounceBeatBtnData } = useThrottleFn((_data: any) => {
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    window.tmsAPI.noImage_treatment_plan_start('SingleStart');
  }, throttleOption);

  const addListenerBeat = async () => {
    window.tmsAPI.remove_beat_btn_by_key('MeasurementStrength');
    window.tmsAPI.remove_sock_info_by_key('MeasurementStrength');
    await window.tmsAPI.beat_btn_by_key('MeasurementStrength', async (event: IpcRendererEvent, data: any) => {
      if (tmsInfo.current.thresholdType === ThresholdEnum.Input) return;
      if (props.hasFault) return;
      if (data.data.key === 'play') {
        if (isStartInterval || allDisabled) return;
        debounceBeatBtnData(data);

        return;
      }
      const th = getTh(data, tmsInfo.current.threshold);
      tmsInfo.current.threshold = th;
      setThreshold(tmsInfo.current.threshold);
      props.setThreshold?.(tmsInfo.current.threshold);
      window.tmsAPI.set_treatment_threshold({ level: tmsInfo.current.threshold });
    });
    window.tmsAPI.get_sock_info_by_key('MeasurementStrength', async (value, data) => {
      if (data.open && tmsInfo.current.isOpen !== data.open) {
        window.tmsAPI.set_treatment_threshold({ level: tmsInfo.current.threshold });
      }
      tmsInfo.current.isOpen = data.open;
    });
  };

  const onOkClick = () => {
    if (thresholdType === ThresholdEnum.Input) {
      props.setStartLoading?.();
      setIsStartInterval(true);
    } else {
      props.onOkClick(threshold);
    }
  };

  const setIsAllDisabledTrue = () => {
    setAllDisabled(true);
  };

  const setIsAllDisabledFalse = () => {
    setAllDisabled(false);
  };

  return (
    <NgModal
      title={props.title}
      okText="结束"
      footer={
        <Footer
          isDisabled={isDisabled}
          thresholdType={thresholdType}
          {...props}
          isStartInterval={isStartInterval || allDisabled}
          onOkClick={onOkClick}
          threshold={threshold}
        />
      }
      width={576}
      closable={!isStartInterval && !allDisabled}
      className={styles.modal}
      onCancel={props.onCancelClick}
      maskClosable={false}
      keyboard={false}
      open
    >
      {props.name && (
        <div className={styles.name}>
          <span>姓名: </span>
          <span>{props.name}</span>
        </div>
      )}
      <SelectedMeasurement
        {...props}
        isEmg={false}
        allDisabled={allDisabled}
        isStartInterval={isStartInterval}
        threshold={threshold}
        thresholdType={thresholdType}
        onChangeRadio={onChangeRadio}
        setIsAllDisabledFalse={setIsAllDisabledFalse}
        setIsAllDisabledTrue={setIsAllDisabledTrue}
        setOkbuttonisDisabled={setOkbuttonisDisabled}
        setThreshold={setInputThresholdNum}
      />
    </NgModal>
  );
};
