@import '@/renderer/static/style/baseColor.module.less';

.modal {
  :global {
    .ant-modal-title {
      font-weight: 500;
    }
    .ant-modal-body {
      margin-top: -12px;
    }
    .ant-modal-content {
      width: 576px;
      height: 337px;
      padding: 24px;
    }
    .ant-modal-close {
      top: 25px;
    }
    .ant-modal-footer {
      margin-top: 37px;
      display: flex;
      justify-content: flex-end;
    }
  }

  .name {
    height: 37px;
    font-size: 16px;
    font-weight: 500;
    color: white;
    font-family: medium-font, serif;
    line-height: 24px;
  }

  .radios {
    margin-top: 17px;
    :global {
      .ant-radio-button-wrapper {
        background-color: @colorA5;
      }
      .ant-radio-button-checked,
      .ant-radio-button-wrapper-checked {
        background: @colorA6;
      }
    }
  }

  .buttons {
    display: flex;
    button {
      margin-left: 20px;
    }
  }
}

.strength_progress {
  display: flex;
  margin-top: 20px;
  position: relative;
  :global {
    .ant-form-item .ant-form-item-label > label::after {
      content: '';
      .ant-form-item-control{
        width: 120px;
        margin-left: 15px;
      }
    }
  }
  .subtitle {
    line-height: 120px;
    text-align: center;
    color: #ffffff;
    font-family: normal-font, serif !important;
  }
  .min_max {
    background: @colorA4 !important;
    line-height: normal;
  }
  .ng_strength_progress {
    margin-left: 10px;
  }
  .translation {
    position: absolute;
    right: 0px;
    top: 33px;
    font-size: 12px;
    color: @colorA9;
  }
}

.radios{
  :global{
    .ant-radio-button-wrapper{
      padding: 0 20px !important;
    }
  }
}
