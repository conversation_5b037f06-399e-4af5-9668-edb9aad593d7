import React, { useEffect, useState } from 'react';
import NgButton from '@/renderer/uiComponent/NgButton';
import styles from './index.module.less';
import { ReactComponent as CreateCircle } from '@/renderer/static/svg/create_circle.svg';
import { ReactComponent as InputSearch } from '@/renderer/static/svg/home_input_search.svg';
import { NgInput } from '@/renderer/uiComponent/NgInput';
import classnames from 'classnames';
import { Dropdown, MenuProps, message } from 'antd';
import NgSelectFileModal from '../../../../uiComponent/NgSelectFileModal';
import { ReactComponent as Warning } from '@/renderer/static/svg/warning.svg';
import NgModal from '../../../../uiComponent/NgModal';
import { useNavigate } from 'react-router-dom';
import { usePlanInfoAtom } from '../../../../recoil/planInfo';
import { useRecoilState, useRecoilValue, useSetRecoilState } from 'recoil';
import { useLicenseAtom } from '@/renderer/recoil/license';
import { tmsCoilSelector } from '@/renderer/recoil/tmsError';
import { osUserInfo } from '@/renderer/recoil/osUserInfo';
import { cameraStatus } from '@/renderer/recoil/cameraStatus';
import { IntlShape, useIntl } from 'react-intl';
import { HomeToolBox, RefreshData } from '@/renderer/uiComponent/SvgGather';
import { product } from '../../../../constant/product';
import { isDev } from '@/common/lib/env/nodeEnv';
import { useAsyncEffect } from 'ahooks';
import { faultAtom, getFaultByKey, getFaultByType, getFaultWithoutType } from '../../../../recoil/fault';
import { FaultEnum, FaultKeyEnum } from '../../../../../common/systemFault/type';
type Props = {
  newDataCount: number;
  onRefreshData(): any;
  onSearch(value: string): any;
  setOpenMeasurementStrength(isOpen: boolean): void;
};

enum DeviceType {
  linux = 1,
  mac = 2,
}
const createPatientItems = (handleClick: (id: number) => void, hasImage: boolean, intl: IntlShape): MenuProps['items'] =>
  [
    {
      key: 1,
      label: <a onClick={() => handleClick(1)}>{intl.formatMessage({ id: '影像方案' })}</a>,
    },
    {
      key: 2,
      label: <a onClick={() => handleClick(2)}>{intl.formatMessage({ id: '无影像方案' })}</a>,
    },
  ].filter(v => hasImage || v.key === 2);
const CreatePatientButton = (props: Props) => {
  const intl = useIntl();
  const [modalVisiable, setModalVisable] = useState<boolean>(false);
  const [uploading, setUploading] = useState<boolean>(false);
  const [license] = useRecoilState(useLicenseAtom);
  const osInfo = useRecoilValue(osUserInfo);
  const [coilSelector] = useRecoilState(tmsCoilSelector);
  const [fault] = useRecoilState(faultAtom);
  const [inputValue, setInputValue] = useState('');
  const [extList, setExtList] = useState<string[]>([]);
  const [showRefreshButton, setShowRefreshButton] = useState(false);
  const [hasTmsConnectError, setHasTmsConnectError] = useState<boolean | undefined>(false);
  const navigate = useNavigate();
  const setIntlState = useSetRecoilState(usePlanInfoAtom);
  const camera = useRecoilValue(cameraStatus);
  const deviceType: DeviceType = /macintosh|mac os x/i.test(navigator.userAgent) ? 2 : 1;
  const [messageApi, contextHolder] = message.useMessage();
  // license、相机、与TMS断开错误不允许注册线圈
  const disableRegistCoil = !!(license.hasLicenseError ||
    !!getFaultByType(FaultEnum.imageFault, fault).length ||
    getFaultByKey(FaultKeyEnum.A030001) ||
    getFaultByKey(FaultKeyEnum.A040001));

  useAsyncEffect(async () => {
    let hasTherapy = await window.systemAPI.getEnv('hasTherapy');
    setShowRefreshButton(!!hasTherapy);
  }, []);
  const getDisableRegistCoil = () => {
    if (isDev) {
      return false;
    }

    return disableRegistCoil;
  };
  const toolboxItems: MenuProps['items'] = [
    {
      key: '1',
      label: <a onClick={() => navigate('/stimulate')}>{intl.formatMessage({ id: '脉冲模板' })}</a>,
    },
    {
      key: '2',
      disabled: getDisableRegistCoil(),
      label: (
        <a
          onClick={() => {
            if (isDev) {
              return navigate('/registCoil');
            }
            if (disableRegistCoil) return;
            navigate('/registCoil');
          }}
          className={classnames(styles.registCoil, {
            [styles.disabledRegistCoil]: disableRegistCoil || !camera?.open,
          })}
        >
          {intl.formatMessage({ id: '线圈注册' })}
          <span
            className={classnames({
              // 已连接线圈，同时线圈未注册
              [styles.coilButton]: !license.hasLicenseError && !coilSelector?.isRegisterCoil, // 线圈连接不展示红点
            })}
          />
        </a>
      ),
    },
    {
      key: '3',
      label: <a onClick={() => navigate('/field')}>{intl.formatMessage({ id: '归档数据' })}</a>,
    },
  ];

  const getToolboxItems = () => {
    return !product.isNav ? toolboxItems.filter(item => item?.key !== '2') : toolboxItems;
  };

  const handleCreate = async (key: number) => {
    if (key === 1) {
      try {
        const list = await window.fileAPI.getFolderInfo(deviceType === DeviceType.mac ? '/' : osInfo.filePath!);
        if (list.length === 0) throw Error(intl.formatMessage({ id: '未检测到移动设备' }));
        setModalVisable(true);
      } catch (error) {
        noFileMessage();
      }
    } else {
      navigate('/previewNoImagePlan');
    }
  };

  const onMeasurementStrength = () => {
    props.setOpenMeasurementStrength(true);
  };

  const handleModalVisible = (type: boolean) => {
    setModalVisable(type);
  };

  const noFileMessage = () => {
    // eslint-disable-next-line no-void, @typescript-eslint/no-floating-promises
    messageApi.open({
      type: 'error',
      content: intl.formatMessage({ id: '未检测到移动设备' }),
    });
  };

  const handleSumbitFileModal = async (path: string) => {
    setUploading(true);
    try {
      const res = await window.fileAPI.uploadFile(path);
      navigate('/previewPlan');
      setIntlState(res);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log(error, '新建患者的error');
      NgModal.confirm({
        content: <>{intl.formatMessage({ id: '文件信息不符合规范，请重新选择。' })}</>,
        headerIcon: <Warning />,
        closable: false,
        okText: intl.formatMessage({ id: '确定' }),
      });
    } finally {
      setUploading(false);
      setModalVisable(false);
    }
  };

  const jumpToNoPatientTms = () => {
    navigate('/noPatientTms');
  };

  const getExtList = async () => {
    try {
      const ext_list = await window.systemAPI.getEnv('extList');
      setExtList(ext_list || []);
    } catch (error) {
      //
    }
  };

  useEffect(() => {
    setHasTmsConnectError(isDev ? false : getFaultWithoutType(FaultEnum.imageFault).length > 0 || license.hasLicenseError);
  }, [fault, license]);

  useEffect(() => {
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    getExtList();
  }, []);

  return (
    <>
      {contextHolder}
      <div className={styles.container}>
        <div className={styles.createPatient}>
          {extList.length === 0 && (
            <NgButton disabled={license.hasLicenseError} onClick={async () => handleCreate(2)}>
              <CreateCircle />
              {intl.formatMessage({ id: '新建患者' })}
            </NgButton>
          )}
          {extList.length !== 0 && (
            <Dropdown
              menu={{ items: createPatientItems(handleCreate, extList.length !== 0, intl) }}
              overlayClassName={styles.createPatientDrop}
              disabled={license.hasLicenseError}
            >
              <NgButton>
                <CreateCircle />
                {intl.formatMessage({ id: '新建患者' })}
              </NgButton>
            </Dropdown>
          )}
        </div>
        <div className={styles.stimulate}>
          <NgButton disabled={hasTmsConnectError} onClick={jumpToNoPatientTms}>
            {intl.formatMessage({ id: '重复刺激' })}
          </NgButton>
        </div>
        <div className={styles.stimulate}>
          <NgButton disabled={hasTmsConnectError} onClick={onMeasurementStrength}>
            {intl.formatMessage({ id: '单脉冲' })}
          </NgButton>
        </div>

        <div className={styles.toolbox}>
          <Dropdown menu={{ items: getToolboxItems() }} overlayClassName={classnames(styles.createPatientDrop, styles.toolboxDrop)}>
            <div className={styles.innerTool}>
              <HomeToolBox />
            </div>
          </Dropdown>
          {product.isNav && (
            <div
              className={classnames({
                // 已连接线圈，同时线圈未注册
                [styles.coilError]: !license.hasLicenseError && !coilSelector?.isRegisterCoil, // 线圈连接不展示红点
              })}
            />
          )}
        </div>
        {showRefreshButton && (
          <div
            className={classnames(styles.refresh, styles.toolbox)}
            onClick={() => {
              setInputValue('');
              props.onRefreshData();
            }}
          >
            {props.newDataCount > 0 && <span className={styles.dataCount}>{props.newDataCount}</span>}
            <RefreshData />
          </div>
        )}
        <div className={styles.inputSearch}>
          <NgInput
            allowClear
            value={inputValue}
            onChange={e => {
              setInputValue(e.target.value);
              props.onSearch(e.target.value);
            }}
            prefix={<InputSearch />}
            placeholder={intl.formatMessage({ id: '请输入搜索内容' })}
          />
        </div>
      </div>
      <NgSelectFileModal
        maskClosable={false}
        filepath={deviceType === DeviceType.mac ? '/' : osInfo.filePath!}
        open={modalVisiable}
        onCancel={() => handleModalVisible(false)}
        width={770}
        controlLoading={uploading}
        handleError={noFileMessage}
        extList={extList}
        onOk={files => {
          // eslint-disable-next-line @typescript-eslint/no-floating-promises
          handleSumbitFileModal(files[0].path);
        }}
      />
    </>
  );
};
export default CreatePatientButton;
