@import '@/renderer/static/style/baseColor.module.less';
.container {
  display: flex;
  width: 100%;
  // 创建患者按钮
  .createPatient {
    & button {
      width: 208px;
      height: 40px;

      & span {
        display: flex;
        align-items: center;

        & svg {
          margin-right: 8px;
        }
      }

      &:disabled {
        color: @colorA9 !important;

        & span {
          position: relative;

          &::before {
            content: '';
            position: absolute;
            top: 2px;
            left: 0;
            display: block;
            width: 16px;
            height: 16px;
            background: url('@/renderer/static/svg/disable_incream.svg'), no-repeat;
          }
        }
      }
    }
  }

  .stimulate {
    margin-left: 20px;

    & button {
      width: 180px;
      height: 40px;

      &:disabled {
        color: @colorA9 !important;
        background-color: @colorA6;
      }

      &:not(:disabled):hover {
        background: #7ab9ba;
        color: #141426;
        cursor: url('@/renderer/static/svg/cursor.cur'), pointer !important;
      }

      font-family: normal-font, serif !important;
      border-radius: 6px;
      background: #4eb7b9;
      border: 0;
      color: #141426;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .toolbox {
    cursor: url('@/renderer/static/svg/cursor.cur'), pointer;
    width: 65px;
    height: 40px;
    border-radius: 6px;
    background: @colorA4;
    margin: 0 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    &:hover {
      background: @colorA4_1;
    }
  }
  .refresh {
    margin-left: 0;
    position: relative;
    .dataCount {
      line-height: 12px;
      text-align: center;
      position: absolute;
      right: -8px;
      top: -4px;
      width: 24px;
      font-size: 10px;
      height: 14px;
      border-radius: 10px;
      background: @colorB5;
    }
    &:hover {
      background: @colorA4_1;
    }
  }

  .coilError {
    position: absolute;
    top: -5px;
    right: -5px;
    width: 14px;
    height: 14px;
    border-radius: 50%;
    background: @colorD3_start;
  }

  .inputSearch {
    flex: 1;

    :global {
      .ant-input-prefix {
        margin-inline-end: 12px;
      }
      & .ant-input-affix-wrapper {
        height: 40px;
      }

      .ant-input-suffix {
        & span {
          width: 16px;
          height: 16px;
          background: url('@/renderer/static/svg/allowClear.svg') no-repeat;

          & svg {
            display: none;
          }
        }
      }
    }
  }
  .registCoil {
    position: relative;
  }

  .innerTool {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.coilButton {
  position: absolute;
  top: 3px;
  right: 33px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: @colorD3_start;
}
.disabledRegistCoil {
  cursor: url('@/renderer/static/svg/disableMouse.cur'), not-allowed !important;
}
.toolboxDrop {
  :global {
    .ant-dropdown-arrow {
      display: none;
    }
    .ant-dropdown-menu-title-content {
      width: 140px !important;
    }
  }
}

.createPatientDrop {
  :global {
    .ant-dropdown-menu {
      background: @colorA4 !important;
      padding: 10px !important;
    }

    .ant-dropdown-menu-item {
      cursor: url('@/renderer/static/svg/cursor.cur'), pointer !important;
      font-family:
        normal-font -apple-system,
        BlinkMacSystemFont,
        'Segoe UI',
        Roboto,
        'Helvetica Neue',
        Arial,
        'Noto Sans',
        sans-serif,
        'Apple Color Emoji',
        'Segoe UI Emoji',
        'Segoe UI Symbol',
        'Noto Color Emoji';
      text-align: center;
      border-radius: 6px !important;
      padding: 0 !important;

      &:hover {
        background: @colorC4 !important;
      }
      & a {
        cursor: url('@/renderer/static/svg/cursor.cur'), pointer !important;
        padding: 5px 12px;
      }
    }
    .ant-dropdown-menu-title-content > a {
      color: @colorA12 !important;
      position: relative;
      width: 100%;
      display: inline-block;
    }

    .ant-dropdown-menu-item-disabled .ant-dropdown-menu-title-content > a {
      color: @colorA9 !important;
    }
    .ant-dropdown-menu-item-disabled {
      cursor: url('@/renderer/static/svg/disableMouse.cur'), not-allowed !important;
    }
  }
}
