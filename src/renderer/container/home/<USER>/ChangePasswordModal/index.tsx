import React from 'react';
import NgModal from '@/renderer/uiComponent/NgModal';
import styles from '@/renderer/component/toolBoxAndPower/index.module.less';
import { NgForm, NgFormItem } from '@/renderer/uiComponent/NgForm';
import { NgInputPassword } from '@/renderer/uiComponent/NgInput';
import { Eye, EyeSlash } from '@/renderer/uiComponent/SvgGather';
import { FormInstance } from 'antd';
import { useIntl } from 'react-intl';
import { validatePassword } from '@/renderer/component/toolBoxAndPower';
import { Rule } from 'antd/es/form';
import { isEqual } from 'lodash';

type Props = {
  open: boolean;
  onOk: any;
  onCancel: any;
  form: FormInstance;
  title: string;
};

const baseInputProps = {
  type: 'password',
};
const ChangePasswordModal = (props: Props) => {
  const intl = useIntl();
  const { open, onOk, onCancel, form, title } = props;
  const rules: Rule[] = [
    {
      required: true,
      message: intl.formatMessage({ id: '密码不可为空' }),
    },
  ];
  const handleValidateOriginPwd = async (fields: any, value: string) => {
    return new Promise((resolve, reject) => {
      if (!value?.length) {
        reject('密码不可为空');
      } else if (/\s+/.test(value)) {
        reject('密码错误');
      }
      resolve('');
    });
  };
  // 新密码的校验规则
  const handleNewPwdValidate = async () => {
    const newPwd = form.getFieldsValue()?.new || '';

    return validatePassword(newPwd, intl);
  };

  const validateNewAndConfirmPwd = async () => {
    return new Promise((resolve, reject) => {
      let newPwd = form.getFieldsValue()?.new;
      let confirmPwd = form.getFieldsValue()?.confirm;
      if (confirmPwd?.length > 0 && newPwd !== confirmPwd) {
        form.setFields([
          {
            name: 'confirm',
            errors: [intl.formatMessage({ id: '与新密码输入不一致，请修改' })],
          },
        ]);
      } else {
        form.setFields([
          {
            name: 'confirm',
            errors: [],
          },
        ]);
      }
      resolve('');
    });
  };

  // 确认密码的校验规则
  const handleValidate = async () => {
    return new Promise((resolve, reject) => {
      const newPwd = form.getFieldsValue()?.new || '';
      const confirmPwd = form.getFieldsValue()?.confirm || '';
      // 与 required 规则冲突了，避免同时出现两个错误提示
      if (!confirmPwd.length) resolve('');
      if (!isEqual(newPwd, confirmPwd)) {
        reject(intl.formatMessage({ id: '与新密码输入不一致，请修改' }));
      } else {
        resolve('');
      }
    });
  };

  return (
    <NgModal maskClosable={false} destroyOnClose title={title} open={open} onOk={onOk} onCancel={onCancel}>
      <div className={styles.changePWDContainer}>
        <NgForm form={form} layout={'vertical'}>
          <NgFormItem
            validateTrigger={['onBlur']}
            rules={[
              {
                validator: handleValidateOriginPwd,
              },
            ]}
            label={intl.formatMessage({ id: '原始密码' })}
            name={'origin'}
          >
            <NgInputPassword
              iconRender={visible => (visible ? <Eye /> : <EyeSlash />)}
              {...baseInputProps}
              placeholder={intl.formatMessage({ id: '请输入原密码' })}
            />
          </NgFormItem>
          <NgFormItem
            label={intl.formatMessage({ id: '新密码' })}
            validateTrigger={['onBlur']}
            rules={[
              {
                validator: handleNewPwdValidate,
              },
              {
                validator: validateNewAndConfirmPwd,
              },
            ]}
            name={'new'}
          >
            <NgInputPassword
              iconRender={visible => (visible ? <Eye /> : <EyeSlash />)}
              {...baseInputProps}
              placeholder={intl.formatMessage({ id: '8-20位，必须包含大小写字母、数字组合' })}
            />
          </NgFormItem>
          <NgFormItem
            label={intl.formatMessage({ id: '再次输入新密码' })}
            validateTrigger={['onBlur']}
            rules={[
              {
                validator: handleValidate,
              },
              ...rules,
            ]}
            name={'confirm'}
          >
            <NgInputPassword
              iconRender={visible => (visible ? <Eye /> : <EyeSlash />)}
              {...baseInputProps}
              placeholder={intl.formatMessage({ id: '8-20位，必须包含大小写字母、数字组合' })}
            />
          </NgFormItem>
        </NgForm>
      </div>
    </NgModal>
  );
};
export default ChangePasswordModal;
