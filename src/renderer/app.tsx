import * as React from 'react';
// eslint-disable-next-line import/no-internal-modules
import * as ReactDOM from 'react-dom/client';
import { RecoilRoot } from 'recoil';
import { ConnectedIntlProvider } from './hocComponent/withIntl';
import { AppRouter } from './router';
import { setM200ApiInstance } from '@/common/api/ngApiAgent';
import { axiosHttpClient } from '@/common/api/httpClient/axiosHttpClientImplMain';
import { Setting } from '@/common/types';
import { ConfigProvider } from 'antd';
import { sendRenderLog } from './utils/renderLogger';

import('./static/style/index.css');
import('./static/style/antd.module.less');
import('./app.less');

const mainElement = document.getElementById('root')!;
document.body.appendChild(mainElement);
if (window && window.authAPI && window.authAPI.getUserSession) {
  window.authAPI
    .getUserSession()
    .then(userSession => {
      if (userSession?.jwtToken) {
        axiosHttpClient.setAuthToken(userSession.jwtToken);
        setM200ApiInstance();
      }
    })
    .catch(e => {
      sendRenderLog.error(`app.tsx row 28 ${e}`);
    });
}

if (window && window.systemAPI) {
  // eslint-disable-next-line no-void
  void window.systemAPI.getSetting().then((versionJson: Setting) => {
    axiosHttpClient.setBaseUrl(versionJson.REACT_APP_NG_API_BASEURL);
  });
  // 订阅token变化,更新axios的token
  // eslint-disable-next-line no-void
  void window.systemAPI.subscribeHttpClient((_event, res: any) => {
    console.log('main主动置换了token, render设置token', res); // eslint-disable-line no-console
    axiosHttpClient.setLanguage(res.language);
    axiosHttpClient.setBaseUrl(res.baseUrl);
    axiosHttpClient.setAuthToken(res.jwtToken);
  });
}

const AppEntry = () => {
  return (
    <ConfigProvider
      theme={{
        token: {
          fontFamily: 'normal-font, serif',
        },
      }}
    >
      <RecoilRoot>
        <ConnectedIntlProvider>
          <AppRouter />
        </ConnectedIntlProvider>
      </RecoilRoot>
    </ConfigProvider>
  );
};
const render = (Component: () => JSX.Element) => {
  const root = ReactDOM.createRoot(mainElement);
  root.render(<Component />);
};

render(AppEntry);
