import { useRef } from 'react';

// 第一个参数为单击事件函数 第二个参数为双击事件函数
export const useClick = (callback: Function, doubleCallback: Function) => {
  const clickRef = useRef<{
    clickCount: number;
    time: number | null;
    timer: NodeJS.Timeout | null;
  }>({
    clickCount: 0,
    time: 0,
    timer: null,
  });

  return (...args: any[]) => {
    clickRef.current.clickCount += 1;
    clickRef.current.time = Date.now();
    clickRef.current.timer = setTimeout(() => {
      if (Date.now() - clickRef.current.time! <= 500 && clickRef.current.clickCount === 2) {
        if (doubleCallback) {
          doubleCallback.call(null, ...args);
        }
      }
      if (clickRef.current.clickCount === 1) {
        if (callback) {
          callback.call(null, ...args);
        }
      }
      // @ts-ignore
      clearTimeout(clickRef.current.timer);
      clickRef.current.clickCount = 0;
    }, 200);
  };
};
