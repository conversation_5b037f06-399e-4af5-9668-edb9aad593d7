import { useEffect, useRef } from 'react';

const useAnimationFrameWithFps = (callback: (deltaTime?: number) => void, fps: number) => {
  const requestRef = useRef<number>();
  const previousTimeRef = useRef<number>();

  const animate = (time: number) => {
    if (previousTimeRef.current !== undefined) {
      const deltaTime = time - previousTimeRef.current;
      if (deltaTime > 1000 / fps) {
        callback(deltaTime);
        previousTimeRef.current = time;
      }
    } else {
      previousTimeRef.current = time;
    }
    requestRef.current = requestAnimationFrame(animate);
  };

  useEffect(() => {
    requestRef.current = requestAnimationFrame(animate);

    return () => cancelAnimationFrame(requestRef.current!);
  }, []); // Empty dependency array ensures effect is only run on mount and unmount

  return requestRef;
};

export default useAnimationFrameWithFps;
