@import './baseColor.module.less';

:global {

  [class^="ant-spin"],
  [class*="ant-spin"] {
    font-size: 14px !important;
  }

  .ant-tooltip-inner {
    background-color: @colorA5  !important;
    border-radius: 6px;
  }

  .ant-tooltip-arrow::before {
    background: @colorA5  !important;
  }

  .ant-input,
  .ant-radio-wrapper,
  .ant-checkbox-wrapper,
  .ant-select-item,
  .ant-select-selection-item,
  .ant-tabs,
  .ant-tabs-content-holder {
    color: @colorA12  !important;
    font-family: 'normal-font', serif !important;
    font-size: 14px !important;
  }

  .ant-breadcrumb,
  .ant-pagination-item a,
  .ant-pagination-options-quick-jumper,
  .ant-radio-group {
    font-family: 'normal-font', serif !important;
    font-size: 14px !important;
  }

  .ant-popover {
    .ant-popover-inner {
      .ant-popover-title {
        min-width: auto !important;
      }
    }
  }

  .ng_select_popup_name {
    background-color: @colorA5;
    padding: initial;

    .ant-select-item-option-content {
      font-family: 'normal-font', serif;
    }

    .ant-select-item {
      cursor: url('../../static/svg/cursor.cur'), pointer !important;
      line-height: 32px;
      text-indent: 12px;
      margin-top: 6px;
      padding: 0 4px;

      &:nth-child(1) {
        margin-top: 14px;
      }

      &:nth-last-child(1) {
        margin-bottom: 14px;
      }

      .ant-select-item-option-content {
        border-radius: 6px 6px 6px 6px;

        &:hover {
          background-color: @colorA6;
        }
      }
    }

    .ant-empty-image,
    .ant-empty-description {
      display: none;
    }

    .ant-select-item-option-selected {
      background-color: unset !important;

      .ant-select-item-option-content {
        border: none;
        color: @colorC1;
      }
    }
  }

  .ant-form-item {
    &.ant-form-item-has-error {
      .ng-number-input {
        border: 1px solid #ba6058;
      }
    }

    .ant-input-affix-wrapper-status-error {
      color: @colorB3;
      border-color: @colorB3  !important;
    }

    .ant-form-item-explain {
      max-height: 24px;
    }

    .ant-form-item-explain-error {
      color: @colorB3;
      font-size: 12px;
      line-height: 18px;
      height: 18px;
    }
  }

  .ant-table-cell-row-hover {
    background-color: @colorA1  !important;
  }

  .ant-pagination-options .ant-select {
    &:hover .ant-select-arrow:not(:last-child) {
      opacity: 1;
    }

    .ant-select-arrow {
      top: 34%;
    }
  }
}