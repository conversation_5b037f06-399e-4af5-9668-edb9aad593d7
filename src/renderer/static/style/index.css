* {
  box-sizing: border-box;
  outline: none !important;
}
html {
  height: 100%;
  width: 100%;
}

body {
  margin: 0;
  padding: 0;
  height: 100%;
  min-height: 100%;
  line-height: normal;
}

#root {
  height: 100%;
  min-height: 100%;
  width: 100%;
}

label {
  font-weight: 400;
}

.ant-modal-header .ant-modal-title {
  font-weight: 550;
}

.ant-btn {
  line-height: normal;
}

li {
  list-style-type: none;
}

/* fixed macOS can not show scroll default bug */
/* .force-show-table-scrollbar ::-webkit-scrollbar {
  -webkit-appearance: none;
  background-color: #f8f8f8;
  width: 7px;
}

.force-show-table-scrollbar ::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background-color: rgba(0, 0, 0, .5);
  box-shadow: 0 0 1px rgba(255, 255, 255, .5);
} */

.login-page {
  width: 100%;
  height: 100vh;
  display: flex;
  justify-content: flex-start;
  min-width: 1054px;
}

.login-logo-div {
  background: url('../images/login-background.jpg') no-repeat;
  background-size: 100% 100%;
  width: 54%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-logo {
  background: url('../images/login-logo.png') no-repeat;
  background-size: 100% 100%;
  width: 209.6px;
  height: 168px;
}

.login-form-div {
  width: 46%;
  height: 100%;
  align-items: center;
  justify-content: center;
  display: flex;
  background-color: white;
}

.login-form label {
  font-weight: normal !important;
}

.login-form .ant-select-selector,
.login-form .ant-select-selector .ant-select-selection-item {
  height: 40px !important;
  line-height: 40px;
}

.login-form .ant-form-item-with-help {
  margin-bottom: 0px !important;
}

.login-form .ant-alert {
  padding: 4px 15px 4px 37px;
}

.login-form .ant-alert-icon {
  top: 6.5px;
}

.login-form-forgot {
  float: right;
  font-size: 14px;
  color: #1bacc8 !important;
}

.login-form-input {
  width: 100%;
  height: 40px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
  border: 1px solid #303963;
  font-weight: 400;
  color: #ffffff;
  font-size: 16px;
}

.login-form-button {
  width: 100%;
  height: 40px;
  background: #1bacc8 !important;
  border-radius: 4px;
  font-size: 16px;
  margin-bottom: 8px;
}

.login-form .ant-form label {
  font-size: 18px;
}

.userinfo-form .ant-form label {
  font-size: 14px;
}

.update-org .ant-form label {
  font-size: 14px;
  font-weight: 500;
}

.login-title {
  font-weight: 400 !important;
  line-height: 3.5 !important;
}

.login-form .ant-form-item-label {
  line-height: 34px;
}

.ant-form-vertical .ant-form-item-label >label::after {
  display: none;
}

.login-form {
  width: 400px;
}

.sider-logo {
  width: 200px;
  height: 55px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #1bacc8;
  float: left;
}

.sider-zh-logo-dev {
  background: url('../images/sider-logo.png') no-repeat;
  background-size: 100% 100%;
  height: 40px;
  width: 140px;
}

.sider-en-logo-dev {
  background: url('../images/sider-logo-en.png') no-repeat;
  background-size: 100% 100%;
  height: 40px;
  width: 140px;
}

.sider-logo .version-div {
  width: 18px;
  height: 25px;
  font-size: 6px;
  font-weight: 400;
  color: rgba(255, 255, 255, 1);
  line-height: 40px;
}

.ant-layout-sider {
  background: #ffffff !important;
}

.project-ul {
  background: #ffffff;
  overflow: auto;
  height: calc(100% - 106px);
  box-shadow: none;
}

.project-ul .ant-menu-sub.ant-menu-inline {
  color: #000000 !important;
  opacity: 0.85;
}

.project-ul .ant-menu-inline .ant-menu-item:after {
  content: '' !important;
  border: none !important;
  opacity: 0 !important;
}

.ant-modal-wrap {
  display: flex;
  align-items: center;
}

.ant-modal-wrap .ant-modal {
  top: 0px;
}
