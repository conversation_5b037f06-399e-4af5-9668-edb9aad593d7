import React, { useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useRecoilValue } from 'recoil';
import { useSessionIsAuthenticated } from '../recoil/user';
import { withUserSession, UserSessionProps } from '../hocComponent/withUserSession';
import { withRouter, RouterProps } from '../hocComponent/withRouter';
import { initObserver } from '@/renderer/utils/elementClickEventLog';

type Props = UserSessionProps &
RouterProps & {
  children: React.ReactNode;
};
const InnerAuthRoute: React.FC<Props> = ({ userSession, router, children }: Props) => {
  const [isAuth, setIsAuth] = useState(false);
  const location = useLocation();
  const isAuthenticate = useRecoilValue(useSessionIsAuthenticated);
  useEffect(() => {
    if (!userSession) {
      queueMicrotask(() => {
        router?.navigate('/login');
      });
    } else {
      setIsAuth(true);
    }
  }, [userSession]);
  useEffect(() => {
    initObserver(location.pathname);
  }, [location]);

  return isAuth ? <>{isAuthenticate ? children : <Navigate to="/logout" replace />}</> : <></>;
};

export const AuthRoute = withUserSession(withRouter(InnerAuthRoute));
export default AuthRoute;
