import React, { lazy } from 'react';
import { Logout } from '../container/login/logout';
import { ItemType } from 'antd/es/breadcrumb/Breadcrumb';
// export type LazyRouteProps = CommonRouteProps & { component: React.LazyExoticComponent<any>; lazyChildren?: React.LazyExoticComponent<any> };
export type RouteProps = {
  path: string;
  id: string;
  lazy?: boolean;
  auth?: boolean;
  component?: React.LazyExoticComponent<any>;
  element?: React.ReactNode;
  children?: RouteProps[];
};
// const Home =
export const routers: RouteProps[] = [
  {
    path: '/',
    id: '/',
    lazy: true,
    // auth: true,
    component: lazy(async () => import('../container/login')),
  },
  {
    path: 'home',
    id: '/home',
    lazy: true,
    auth: true,
    component: lazy(async () => import('../container/home')),
  },
  {
    path: 'techsupport',
    id: '/techsupport',
    lazy: true,
    auth: true,
    component: lazy(async () => import('../container/techsupport')),
  },
  {
    path: 'about',
    id: '/about',
    lazy: true,
    auth: true,
    component: lazy(async () => import('../container/about')),
  },
  {
    path: 'report',
    id: '/report',
    lazy: true,
    auth: true,
    component: lazy(async () => import('../container/report')),
  },
  {
    path: 'manage',
    id: '/manage',
    lazy: true,
    auth: true,
    component: lazy(async () => import('../container/manage')),
  },
  {
    path: '/login',
    id: '/login',
    lazy: true,
    component: lazy(async () => import('../container/login')),
  },
  {
    path: '/logout',
    id: '/logout',
    element: <Logout />,
  },
  {
    path: '/a',
    id: '/a',
    // auth: true,
    element: <div>1111</div>,
  },
  {
    path: '/stimulate',
    id: '/stimulate',
    lazy: true,
    auth: true,
    component: lazy(async () => import('../container/stimulateTemplate')),
  },
  {
    path: 'repeatTreat',
    id: '/repeatTreat',
    lazy: true,
    auth: true,
    component: lazy(async () => import('../container/repeatTreat')),
  },
  {
    path: '/demo/*',
    id: '/demo',
    lazy: true,
    component: lazy(async () => import('../container/uiComponentDemo')),
    children: [
      {
        path: 'button',
        id: '/button',
        lazy: true,
        component: lazy(async () => import('../container/uiComponentDemo/button')),
      },
      {
        path: 'modal',
        id: '/modal',
        lazy: true,
        component: lazy(async () => import('../container/uiComponentDemo/modal')),
      },
      {
        path: 'form',
        id: '/form',
        lazy: true,
        component: lazy(async () => import('../container/uiComponentDemo/form')),
      },
      {
        path: 'input',
        id: '/input',
        lazy: true,
        auth: true,
        component: lazy(async () => import('../container/uiComponentDemo/input')),
      },
      {
        path: 'radio',
        id: '/radio',
        lazy: true,
        component: lazy(async () => import('../container/uiComponentDemo/radio')),
      },
      {
        path: 'steps',
        id: '/steps',
        lazy: true,
        component: lazy(async () => import('../container/uiComponentDemo/steps')),
      },
      {
        path: 'popover',
        id: '/popover',
        lazy: true,
        component: lazy(async () => import('../container/uiComponentDemo/popover')),
      },
      {
        path: 'select',
        id: '/select',
        lazy: true,
        component: lazy(async () => import('../container/uiComponentDemo/select')),
      },
      { path: 'icons', id: '/icons', lazy: true, component: lazy(async () => import('../container/uiComponentDemo/icons')) },
      {
        path: 'brainBar',
        id: '/brainBar',
        lazy: true,
        component: lazy(async () => import('../container/uiComponentDemo/brainBar')),
      },
      {
        path: 'message',
        id: '/message',
        lazy: true,
        component: lazy(async () => import('../container/uiComponentDemo/message')),
      },
      {
        path: 'table',
        id: '/table',
        lazy: true,
        component: lazy(async () => import('../container/uiComponentDemo/table')),
      },
      {
        path: 'breadCrumb',
        id: '/breadCrumb',
        lazy: true,
        component: lazy(async () => import('../container/uiComponentDemo/breadCrumb')),
      },
      {
        path: 'progress',
        id: '/progress',
        lazy: true,
        component: lazy(async () => import('../container/uiComponentDemo/progress')),
      },
      {
        path: 'empty',
        id: '/empty',
        lazy: true,
        component: lazy(async () => import('../container/uiComponentDemo/empty')),
      },
      {
        path: 'loading',
        id: '/loading',
        lazy: true,
        component: lazy(async () => import('../container/uiComponentDemo/loading')),
      },
      {
        path: 'pagination',
        id: '/pagination',
        lazy: true,
        component: lazy(async () => import('../container/uiComponentDemo/pagination')),
      },
    ],
  },
  {
    path: 'previewPlan/:subjectId/:planId',
    id: '/previewPlan',
    lazy: true,
    auth: true,
    component: lazy(async () => import('../container/previewPlan')),
  },
  {
    path: 'previewPlan',
    id: '/previewPlan',
    lazy: true,
    auth: true,
    component: lazy(async () => import('../container/previewPlan')),
  },
  {
    path: 'previewNoImagePlan',
    id: '/previewNoImagePlan',
    lazy: true,
    auth: true,
    component: lazy(async () => import('../container/previewNoImagePlan/index')),
  },
  {
    path: 'previewNoImagePlan/:subjectId/:planId',
    id: '/previewNoImagePlan',
    lazy: true,
    auth: true,
    component: lazy(async () => import('../container/previewNoImagePlan/index')),
  },
  {
    path: 'treatmentNoImagePlan/:subjectId/:planId',
    id: '/treatmentNoImagePlan',
    lazy: true,
    auth: true,
    component: lazy(async () => import('../container/previewNoImagePlan/index')),
  },
  {
    path: 'previewTreat/:planId/:subjectId',
    id: '/previewTreat',
    lazy: true,
    auth: true,
    component: lazy(async () => import('../container/previewTreat')),
  },
  {
    path: 'emg/:planId/:subjectId',
    id: '/emg',
    lazy: true,
    auth: true,
    component: lazy(async () => import('../container/emg/index')),
  },
  {
    path: 'batVerification/:planId/:subjectId',
    id: '/batVerification',
    lazy: true,
    auth: true,
    component: lazy(async () => import('../container/batVerification/index')),
  },
  {
    path: 'noPatientTms',
    id: '/noPatientTms',
    lazy: true,
    auth: true,
    component: lazy(async () => import('../container/noPatientTms/index')),
  },
  {
    path: 'registCoil/*',
    id: '/registCoil',
    lazy: true,
    auth: true,
    component: lazy(async () => import('../container/registCoil')),
    children: [],
  },
  {
    path: 'field',
    id: '/field',
    lazy: true,
    auth: true,
    component: lazy(async () => import('../container/field')),
    children: [],
  },
];

export const routerTitleMap = {
  '/home': '首页',
  '/children1': '子页面1',
  '/login': '登录',
  '/logout': '登出',
  '/a': 'a',
  '/demo': 'demo',
  '/button': 'button',
  '/modal': 'modal',
  '/form': 'form',
  '/input': 'input',
  '/radio': 'radio',
  '/steps': 'steps',
  '/popover': 'popover',
  '/select': 'select',
  '/icons': 'icons',
  '/brainBar': 'brainBar',
  '/breadCrumb': 'breadCrumb',
  '/progress': 'progress',
  '/loading': 'loading',
  '/pagination': 'pagination',
  '/previewPlan': '方案详情',
  '/stimulate': '脉冲模板',
  '/previewTreat': '治疗预览',
  '/report': '治疗结果',
  '/manage': '管理',
  '/about': '关于我们',
  '/noPatientTms': '重复刺激',
  '/field': '归档数据',
  '/repeatTreat': '连续刺激详情',
};
export const formatBreadcrumb = (routes: RouteProps[], path = ''): ItemType[] => {
  let result: ItemType[] = [];
  for (let item of routes) {
    if (item.children) {
      result = result.concat(formatBreadcrumb(item.children, item.id));
    } else {
      result.push({
        path: path === '' ? item.path : `${path}/${item.path}`,
        breadcrumbName: routerTitleMap[item.id],
      });
    }
  }

  return result;
};
