import React, { Suspense, useEffect, useCallback, useRef, useMemo } from 'react';
import * as Sentry from '@sentry/react';
import { IpcRendererEvent } from 'electron';
import { injectIntl, IntlShape } from 'react-intl';
import { Route, Routes, HashRouter } from 'react-router-dom';
import { useRecoilState, useRecoilValue, useSetRecoilState } from 'recoil';
import { ConfigProvider } from 'antd';
import { useAsyncEffect, useMount } from 'ahooks';
import { tmsWorkStatusSelector, tmsErrorSelector, tmsCoilSelector } from '../recoil/tmsError';
import { intlState } from '../recoil/intl';
import { connSocket } from '../utils/imgSocket';
import { userSessionAtom } from '../recoil/user';
import { AuthRoute } from './authRoute';
import { routers, RouteProps } from './routers';
import zhCN from 'antd/locale/zh_CN';
import enUs from 'antd/locale/en_US';
import { axiosHttpClient } from '@/common/api/httpClient/axiosHttpClientImplMain';
import { useOsUserInfo } from '@/renderer/recoil/osUserInfo';
import { bat } from '../utils/treat';
import { EnumUserPageQueryModelRoleEnumList, UserSession } from '../../common/types';
import { initProduct } from '@/renderer/constant/product';
import { safetyBtnStatus, safetyBtnStatusEnum, useSafetyBtnStatus } from '@/renderer/recoil/safetyBtn';
import { errorEventBus, timeOutEvent } from '../utils/errorEventBus';
import { SimpleErrorModel } from '../component/systemErrorModel/simpleErrorModel';
import { sendHistoryLog, sendRenderLog } from '../utils/renderLogger';
import { ShutdownStatusEnum } from '../component/systemErrorModel/errorModel';
import { useInterval } from 'react-timing-hooks';
import { useModalStatusAtom } from '../recoil/modalStatus';
import { useSimpleModalStatusAtom } from '../recoil/simpleModalStatus';
import { isNotTreatingAtom } from '../recoil/isNotTreating';
import { Fault2RenderMapType, FaultLevelEnum, FaultStatusEnum } from '../../common/systemFault/type';
import { useFaultSelector } from '../recoil/fault';
import { appearOperation, createCoilError, getPreOpList, getSubOpList, queryCoilInfo, subOperation } from './utils';

const TIMEOUT_ERROR_KEY = 'timeout_error_key';

export const AppRouter = injectIntl((props: { intl: IntlShape }) => {
  const [userSession, setUserSession] = useRecoilState(userSessionAtom);
  const [fault, setFaultSelector] = useRecoilState(useFaultSelector);
  const [render, setRender] = React.useState(false);
  const [showErrorModel, setShowErrorModel] = React.useState(false);
  const [shutdownStatus, setShutdownStatus] = React.useState<ShutdownStatusEnum>(ShutdownStatusEnum.init);
  const recoilRef = React.useRef({
    setTmsError: useSetRecoilState(tmsErrorSelector),
    setTmsWorkStatus: useSetRecoilState(tmsWorkStatusSelector),
    setTmsCoil: useSetRecoilState(tmsCoilSelector),
    tmsCoilInfo: useRecoilValue(tmsCoilSelector),
    setOsUserInfo: useSetRecoilState(useOsUserInfo),
  });
  const timerRef = useRef<NodeJS.Timeout | undefined>();
  const hideSafetyModalRef = useRef<any>();
  const setSafetyBtnStatus = useSetRecoilState(useSafetyBtnStatus);
  const safetyBtnStatusValue = useRecoilValue(safetyBtnStatus);
  const timeOutDateRef = useRef<Date | null>(null);
  const modalStatus = useRecoilValue(useModalStatusAtom);
  const isNotTreating = useRecoilValue(isNotTreatingAtom);
  const simpleModalStatus = useRecoilValue(useSimpleModalStatusAtom);
  const isTechSupportRef = useRef(userSession?.role_id === EnumUserPageQueryModelRoleEnumList.TechSupport);

  const timeOutGotoLogin = async () => {
    const currentTime = new Date();
    // 当前为治疗态 或者当前有异常弹窗，更新date值，直接return
    if (!isNotTreating || modalStatus || simpleModalStatus) {
      timeOutDateRef.current = currentTime;

      return;
    }

    if (timeOutDateRef.current && location.hash !== '#/login' && location.hash !== '') {
      const timeDifference = currentTime.getTime() - timeOutDateRef.current.getTime();
      if (Math.abs(timeDifference) >= 30 * 60 * 1000) {
        sendHistoryLog.info('30分钟无操作，自动登出');
        window.location.replace('#/login');
        // eslint-disable-next-line no-void
        void window.authAPI.logout();
        axiosHttpClient.resetAuthToken();
      }
    }
  };
  const { stop: timeInvervalStop } = useInterval(timeOutGotoLogin, 60000, { startOnMount: true });

  const onShutDown = () => {
    setShutdownStatus(ShutdownStatusEnum.loading);
    timerRef.current = setTimeout(() => {
      setShutdownStatus(ShutdownStatusEnum.error);
      if (hideSafetyModalRef.current.hide) {
        hideSafetyModalRef.current.hide();
      }
    }, 60000);

    window.systemAPI.shutdown();
  };

  const workStatus = useCallback(async () => {
    await window.tmsAPI.working_status((_: IpcRendererEvent, data: any) => {
      // eslint-disable-next-line no-console
      recoilRef.current.setTmsWorkStatus(data.data);
    });
  }, []);

  const tmsCoilListener = async () => {
    window.tmsAPI.get_coil_info_by_key('router-coil-query', async (_: IpcRendererEvent, data: any) => {
      bat.setBatType(data.data?.type?.trim());
      const coilInfo = {
        ...data.data,
        isRegisterCoil: await window.systemAPI.getStore('isRegisterCoil'),
      };

      recoilRef.current.setTmsCoil(coilInfo);

      const versionInfo = await window.tmsAPI.get_tms_version();
      // 发送警告
      if (versionInfo.data.treat_count && versionInfo.data.treat_count >= 30000000) {
        window.systemAPI.pushSystemFault({ '0A030320': FaultStatusEnum.abnormal }, '显示电容放电次数超限警告');
      } else {
        window.systemAPI.pushSystemFault({ '0A030320': FaultStatusEnum.normal }, '移除电容放电次数超限警告');
      }
      // 线圈连接异常不再push其他错误
      if (!data.data?.connect_status) {
        return;
      }
      window.systemAPI.pushSystemFault(createCoilError(data), `router 页面监听 coil_query, data: ${JSON.stringify(data)}`);
    });
  };

  const listenSessionStatus = async (session: UserSession | undefined) => {
    isTechSupportRef.current = session?.role_id === EnumUserPageQueryModelRoleEnumList.TechSupport;
    await window.systemAPI.getSystemFaultOnce();
  };

  useAsyncEffect(async () => {
    let res = await window.tmsAPI.auto_query_coil();
    sendRenderLog.info('**router**，手动查询线圈=====', JSON.stringify(res));
  }, []);

  useEffect(() => {
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    listenSessionStatus(userSession);
  }, [userSession]);

  useMount(async () => {
    try {
      await initProduct();
    } catch (error) {
      //
    }
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    queryCoilInfo({ setCoilSelector: recoilRef.current.setTmsCoil });

    let pre_fault = fault;
    // 监听
    window.systemAPI.getSystemFault((_, data: Fault2RenderMapType) => {
      const ans = { ...data };
      getPreOpList(pre_fault[FaultLevelEnum.error], data[FaultLevelEnum.error]).forEach(v => {
        // eslint-disable-next-line @typescript-eslint/no-floating-promises
        appearOperation[v]?.();
      });
      getSubOpList(pre_fault[FaultLevelEnum.error], data[FaultLevelEnum.error]).forEach(v => {
        subOperation[v]?.({ setCoilSelector: recoilRef.current.setTmsCoil });
      });
      if (isTechSupportRef.current) {
        Object.keys(ans).map(v => {
          const value = v as unknown as FaultLevelEnum.error | FaultLevelEnum.warning;
          ans[value] = ans[value].map(val => ({ ...val, content: val.description }));
        });
      }
      pre_fault = ans;
      setFaultSelector(ans);
    });
    // 没有变化不会推，初始调用一次用来initFault
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    window.systemAPI.getSystemFaultOnce();

    // let hideSafetyModal: any;
    // 急停
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    window.systemAPI.subscribeSafetyBtn((_event, res: any) => {
      if (res.isDown) {
        setSafetyBtnStatus(safetyBtnStatusEnum.emery);
      } else if (res.pduError) {
        setShutdownStatus(ShutdownStatusEnum.error);
      }
    });
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    window.tmsAPI.clear_data_pool();
    // 设备状态检查回调
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    workStatus();
    // tms拍子信息的监听
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    tmsCoilListener();

    // 通用协议
    connSocket.createSocketCallback = connSocket.commonAgreement;

    window.addEventListener('click', updateHandleTime);
    window.addEventListener('mousemove', updateHandleTime);

    bat.loadCB03Bat();
    bat.loadCBF03Bat();
    let osUserInfo = await window.systemAPI.getOsUserInfo();
    recoilRef.current.setOsUserInfo({
      ...osUserInfo,
      filePath: `/media/${osUserInfo.username || 'nguser'}`,
    });
  });

  const updateHandleTime = () => {
    timeOutDateRef.current = new Date();
  };

  useEffect(() => {
    const onTimeOutListen = (timeOut: boolean) => {
      if (showErrorModel === timeOut) return;
      setShowErrorModel(timeOut);
    };
    errorEventBus.on(timeOutEvent, onTimeOutListen);

    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    (async () => {
      try {
        const session = await window.authAPI.getUserSession();
        setUserSession(session);
      } catch (error) {
        //
      } finally {
        setRender(true);
        await window.systemAPI.getTimeOutError(TIMEOUT_ERROR_KEY, (_, timeOut: boolean) => {
          onTimeOutListen(timeOut);
        });
      }
    })();

    return () => {
      window.removeEventListener('click', updateHandleTime);
      window.removeEventListener('mousemove', updateHandleTime);
      window.systemAPI.removeGetTimeOutError(TIMEOUT_ERROR_KEY);
      errorEventBus.off(timeOutEvent, onTimeOutListen);
      timeInvervalStop();
    };
  }, []);

  const routerViews = (routerArray: RouteProps[]) => {
    return routerArray.map(route => {
      let element;
      if (route.lazy && route.component)
        element = (
          <Suspense fallback={<></>}>
            <route.component />
          </Suspense>
        );
      if (!route.lazy && route.element) element = route.element;
      if (!element) return <></>;

      return (
        <Route
          key={route.id}
          path={route.path}
          // @ts-ignore
          element={route.auth ? <AuthRoute>{element}</AuthRoute> : element}
        >
          {route.children && routerViews(route.children)}
        </Route>
      );
    });
  };
  const GetLocal = () => {
    let lang = useRecoilValue(intlState);
    if (lang.locale === 'zh-CN') {
      return zhCN;
    } else {
      return enUs;
    }
  };
  const setSentryLog = (error: any) => {
    sendRenderLog.error(`sentry_error===>>>>:${error.stack}`);
  };

  useEffect(() => {
    if (safetyBtnStatusValue === safetyBtnStatusEnum.error) {
      setShutdownStatus(ShutdownStatusEnum.error);
    }
  }, [safetyBtnStatusValue]);

  const getErrorLabel = useMemo(() => {
    if (shutdownStatus === ShutdownStatusEnum.error) {
      return {
        title: '关机失败',
        errorList: ['请联系售后人员或拉下空气开关断电'],
      };
    }

    if (showErrorModel) {
      return {
        title: '系统服务异常',
        errorList: ['请关机后重新启动或联系售后人员'],
      };
    }

    return {
      title: '急停',
      errorList: [],
    };
  }, [shutdownStatus, showErrorModel, safetyBtnStatusValue]);

  return (
    <HashRouter>
      <Suspense>
        {/* antd 5 中默认会给按钮中的文字添加空格，添加autoInsertSpaceInButton移除此特性*/}
        <Sentry.ErrorBoundary onError={setSentryLog}>
          <ConfigProvider theme={{ hashed: false }} locale={GetLocal()} autoInsertSpaceInButton={false}>
            {(showErrorModel ||
              shutdownStatus === ShutdownStatusEnum.error ||
              (safetyBtnStatusValue | safetyBtnStatusEnum.ERRORSTATUS) === safetyBtnStatusEnum.ERRORSTATUS) && (
              <SimpleErrorModel
                visible
                title={getErrorLabel.title}
                isStimulate={false}
                errorList={getErrorLabel.errorList}
                btnLabel={'关机'}
                btnLoading={shutdownStatus === ShutdownStatusEnum.loading}
                onOk={onShutDown}
                isHiddenBtn={shutdownStatus === ShutdownStatusEnum.error}
              />
            )}
            <Routes>{render && routerViews(routers)}</Routes>
          </ConfigProvider>
        </Sentry.ErrorBoundary>
      </Suspense>
    </HashRouter>
  );
});
