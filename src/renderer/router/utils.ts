import moment from 'moment';
import { FaultMapItemType, FaultNormalSubEnum, FaultStatusEnum } from '../../common/systemFault/type';
import { isAfterTime, isNearExpiration } from '../utils';
import { initImageTms, initNoImageTms } from '../utils/crashedAction';
/**
 * 处理监听线圈fault
 * @param data
 * @returns
 */
export const createCoilError = (data: any) => {
  const oneHundred = 1.0e5;
  const pushFaultList: { [x: string]: FaultStatusEnum } = {};

  if (!['CBF-03', 'CB-03'].some(v => data?.data?.type.trim() === v)) {
    pushFaultList['0A040002'] = FaultStatusEnum.abnormal;
  } else {
    pushFaultList['0A040002'] = FaultStatusEnum.normal;
  }

  // 线圈过期日期 = 线圈生产日期+五年
  const coilExpireTime = moment(data?.data?.production_date, 'YYYY-MM-DD').add(5, 'year');
  // !2. 超过 线圈过期日期
  if (isAfterTime(coilExpireTime)) {
    pushFaultList['0A04000C'] = FaultStatusEnum.abnormal;
    pushFaultList['0A04000B'] = FaultStatusEnum.normal;
  } else {
    pushFaultList['0A04000C'] = FaultStatusEnum.normal;
    // !3. 临近线圈使用日期，在到期时间的前三个月内  与线圈过期互斥
    const isNear = isNearExpiration(coilExpireTime, 3, 'month');
    if (isNear) {
      pushFaultList['0A04000B'] = FaultStatusEnum.abnormal;
    } else {
      pushFaultList['0A04000B'] = FaultStatusEnum.normal;
    }
  }

  // !4. SN序列号错误
  if (!data?.data?.sn) {
    pushFaultList['0A040003'] = FaultStatusEnum.abnormal;
  } else {
    pushFaultList['0A040003'] = FaultStatusEnum.normal;
  }

  // !5. 判断线圈寿命:线圈临近使用次数的最大限制
  const life = data.data?.life;
  if (life > 0) {
    if (life <= oneHundred) {
      pushFaultList['0A04000D'] = FaultStatusEnum.abnormal;
    } else {
      pushFaultList['0A04000D'] = FaultStatusEnum.normal;
    }
  }

  // !6. 判断线圈寿命:线圈使用已达到最大限制
  if (life <= 0) {
    pushFaultList['0A04000E'] = FaultStatusEnum.abnormal;
    pushFaultList['0A04000D'] = FaultStatusEnum.normal;
  } else {
    pushFaultList['0A04000E'] = FaultStatusEnum.normal;
  }

  return pushFaultList;
};

export const queryCoilInfo = async (obj: { setCoilSelector: Function }) => {
  let sn = await window.fileAPI.getBatData();
  let coil = await window.tmsAPI.auto_query_coil();
  const coilInfo = {
    ...coil.data,
  };
  const coilId = sn;
  const coilSn = coil.data?.sn;
  if (coilId !== coilSn) {
    coilInfo.isRegisterCoil = false;
  } else {
    coilInfo.isRegisterCoil = true;
  }
  await window.systemAPI.setStore('isRegisterCoil', coilInfo.isRegisterCoil);
  obj.setCoilSelector(coilInfo);
};

const closeTmsByFaultClean = (obj: { setCoilSelector: Function }) => {
  initImageTms();
  initNoImageTms();
  // eslint-disable-next-line @typescript-eslint/no-floating-promises
  queryCoilInfo(obj);
};

export const subOperation = {
  [FaultNormalSubEnum.initTms]: closeTmsByFaultClean,
};
export const appearOperation = {
  [FaultNormalSubEnum.initTms]: window.tmsAPI.clear_data_pool,
};

export const getSubOpList = (pre: FaultMapItemType[], cur: FaultMapItemType[]) => {
  return Array.from(new Set(pre.filter(v => v.subOperation).map(v => v.subOperation!))).filter(v => !cur.some(val => val.subOperation === v));
};

export const getPreOpList = (pre: FaultMapItemType[], cur: FaultMapItemType[]) => {
  return Array.from(new Set(cur.filter(v => v.subOperation).map(v => v.subOperation!))).filter(v => !pre.some(val => val.subOperation === v));
};
