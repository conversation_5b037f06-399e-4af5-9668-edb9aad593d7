import { DefaultValue, atom, selector } from 'recoil';
import { LicenseModel } from '@/common/types';
import { authInfo, devAuth } from '../utils/authConfig';
interface ICustomLicense {
  hasLicenseError?: boolean; // 页面按钮是否允许操作 true：不可操作 false：可操作
}
export const licenseAtom = atom<any>({
  key: 'licenseAtom',
  default: {},
});

const flatObj = (obj: any, pre = '') => {
  let res: any = {};

  for (let key in obj) {
    if (obj[key]?.toString() === '[object Object]') {
      res = { ...res, ...flatObj(obj[key], pre ? `${pre}.${key}` : key) };
    } else {
      res[pre ? `${pre}.${key}` : key] = obj[key];
    }
  }

  return res;
};

export const useLicenseAtom = selector<LicenseModel & ICustomLicense>({
  key: 'useLicenseAtom',
  get: ({ get }) => {
    return get(licenseAtom);
  },
  set: ({ set }, value) => {
    set(licenseAtom, value);

    if (!(value instanceof DefaultValue)) {
      const auth_info = authInfo.find(v => v.name === value.simple_status);
      const dev_info = devAuth(true);
      const flat: { [prop: string]: number } = flatObj(auth_info!.type);
      const res = Object.keys({ ...flatObj(auth_info!.type), ...dev_info }).reduce((pre, cur) => {
        // eslint-disable-next-line no-bitwise
        return { ...pre, [cur]: (flat[cur] || 0) | (dev_info[cur] || 0) };
      }, {});
      if (auth_info?.type) {
        set(authTypeState, res);
      }
    }
  },
});

export const authTypeState = atom<any>({
  key: 'authAtom',
  default: flatObj(authInfo.find(v => v.name === 200)?.type),
});
