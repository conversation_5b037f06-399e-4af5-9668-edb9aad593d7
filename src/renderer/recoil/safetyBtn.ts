import { atom, selector } from 'recoil';

export enum safetyBtnStatusEnum {
  normal = 1,
  emery = 2,
  error = 4,
  ERRORSTATUS = 6,
}

export const safetyBtnStatus = atom<safetyBtnStatusEnum>({
  key: 'safetyBtnStatus',
  default: safetyBtnStatusEnum.normal,
});

export const useSafetyBtnStatus = selector<safetyBtnStatusEnum>({
  key: 'useSafetyBtnStatus',
  get: ({ get }) => {
    return get(safetyBtnStatus);
  },
  set: ({ set }, value: any) => {
    set(safetyBtnStatus, value);
  },
});
