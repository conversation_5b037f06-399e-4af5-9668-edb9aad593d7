import { atom, selector } from 'recoil';
import os from 'os';
export type IOsInfo = os.UserInfo<string> & {
  filePath?: string;
};
export const osUserInfo = atom<IOsInfo>({
  key: 'osUserInfo',
  default: undefined,
});

export const useOsUserInfo = selector<IOsInfo>({
  key: 'useOsUserInfo',
  get: ({ get }) => {
    return get(osUserInfo);
  },
  set: ({ set }, value) => {
    set(osUserInfo, value);
  },
});
