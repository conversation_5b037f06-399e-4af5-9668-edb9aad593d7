import { atom, selector } from 'recoil';
import { TablePaginationConfig } from 'antd';
export type IPage = {
  homePage?: TablePaginationConfig;
  fieldPage?: TablePaginationConfig;
};
// 为了保留分页状态，如果后续有其他页面也需要保留，按homePage的方式添加
export const pageParams = atom<IPage>({
  key: 'pageParams',
  default: {
    homePage: {
      total: 0,
      current: 1,
      pageSize: 15,
    },
    fieldPage: {
      total: 0,
      current: 1,
      pageSize: 15,
    },
  },
});

export const usePageParams = selector<IPage>({
  key: 'usePageParams',
  get: ({ get }) => {
    return get(pageParams);
  },
  set: ({ set }, value) => {
    set(pageParams, value);
  },
});
