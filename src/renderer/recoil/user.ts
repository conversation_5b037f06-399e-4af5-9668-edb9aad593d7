import { atom, selector } from 'recoil';
import { UserSession } from '../../common/types/index';

export type UserSessionState = UserSession | undefined;
export const userSessionAtom = atom<UserSessionState>({
  key: 'userSessionAtom',
  default: undefined,
});

export const useSessionIsAuthenticated = selector<boolean>({
  key: 'useSessionIsAuthenticated',
  get: ({ get }) => {
    return get(userSessionAtom)?.jwtToken ? true : false;
  },
});
