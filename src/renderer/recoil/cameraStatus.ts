import { atom, selector } from 'recoil';
import { GetStatusCallbackParam } from '../utils/imgSocket';

export const cameraStatus = atom<any>({
  key: 'cameraStatus',
  default: undefined,
});

export const useCameraStatus = selector<GetStatusCallbackParam>({
  key: 'useCameraStatus',
  get: ({ get }) => {
    return get(cameraStatus);
  },
  set: ({ set }, value: any) => {
    set(cameraStatus, value);
  },
});
