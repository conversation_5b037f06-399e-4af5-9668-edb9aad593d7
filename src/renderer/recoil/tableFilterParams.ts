import { atom, selector } from 'recoil';
export interface IFilterType {
  fieldName:
  | 'status' // 方案状态： 0：全部 1：未完善 2：已完善
  | 'type' // 数据类型： 0：全部 1：无影像 2：影像
  | 'updated_treatment_at' // 最近治疗时间： 0：全部 1：一周内 2：1个月内 3：3个月内
  | 'create_at';
  value: number;
  sort?: string;
  noFetch?: boolean;
}
export type IFilterPage = {
  homeFilter?: IFilterType[];
  fieldFilter?: IFilterType[];
};

export const defaultHomeFilter: IFilterPage = {
  homeFilter: [
    {
      fieldName: 'status',
      value: 0,
    },
    {
      fieldName: 'type',
      value: 0,
    },
    {
      fieldName: 'updated_treatment_at',
      value: 0,
    },
    {
      fieldName: 'create_at',
      value: 0,
    },
  ],
  fieldFilter: [
    {
      fieldName: 'status',
      value: 0,
    },
    {
      fieldName: 'type',
      value: 0,
    },
    {
      fieldName: 'updated_treatment_at',
      value: 0,
    },
    {
      fieldName: 'create_at',
      value: 0,
    },
  ],
};

export const tableFilterParams = atom<IFilterPage>({
  key: 'tableFilterParams',
  default: defaultHomeFilter,
});

export const useFilterParams = selector<IFilterPage>({
  key: 'useFilterParams',
  get: ({ get }) => {
    return get(tableFilterParams);
  },
  set: ({ set }, value) => {
    set(tableFilterParams, value);
  },
});
