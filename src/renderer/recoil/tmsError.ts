import { atom, selector } from 'recoil';
// tms设备检查
export type TmsWorkStatus = {
  self_check?: number;
  standby?: number;
  treatment_setting?: number;
  treatment?: number;
  shutdown?: number;
};
export interface ITmsError {
  battery?: string[];
  cooling?: string[];
  beat?: string[];
  tmsConnect?: string[];
  fan?: number[];
}
export const tmsErrorAtom = atom<ITmsError>({
  key: 'tmsError',
  default: {
    battery: [],
    cooling: [],
    beat: [],
    tmsConnect: ['connect'], // 默认TMS未连接
    fan: [],
  },
});

export const tmsErrorSelector = selector({
  key: 'tmsErrorSelector',
  get: ({ get }) => get(tmsErrorAtom),
  set: ({ set }, value: any) => {
    set(tmsErrorAtom, value);
  },
});

export const tmsWorkStatusAtom = atom<TmsWorkStatus>({
  key: 'tmsCheckAtom',
  default: {},
});

export const tmsWorkStatusSelector = selector({
  key: 'tmsCheckSelector',
  get: ({ get }) => get(tmsWorkStatusAtom),
  set: ({ set }, value: any) => {
    set(tmsWorkStatusAtom, value);
  },
});

export type TmsCoil = {
  result?: number;
  key?: number; // 按键 , 0 无按键 | 1左键 | 2刺激| 3 右键
  status?: number; //  # 状态 0x00 正常 0x01 超温 0x02 无线圈
  gyro_x_axis?: number; // 陀螺仪姿态X轴 uint32
  gyro_y_axis?: number; // 陀螺仪姿态Y轴 uint32
  gyro_z_axis?: number; // 陀螺仪姿态Z轴 uint32
  temperature?: number; // 线圈温度, uint16
  function_temperature?: number; // 治疗面温度
  life?: number; // 寿命
  pulse?: number; // 本次脉冲数
  strength?: number; // 治疗强度
  type?: number; // 线圈类型，硬件未给出
  version?: number; // 线圈版本，硬件未给出
  software_version?: number; // 线圈版本
  production_date?: string; // 生成日期
  sn?: string; // 生产序列号
  isRegisterCoil?: boolean; // 线圈是否已经注册
  temp_0?: number; // 温度传感器A1
  temp_1?: number; // 温度传感器A2
  temp_2?: number; // 温度传感器B1
  temp_3?: number; // 温度传感器B2
  coil_max_temperature?: number; // 四个温度传感器的最大值
  commit?: string;
  render_temperature?: string; // 显示温度
};
export const tmsCoilAtom = atom<TmsCoil>({
  key: 'tmsCoilAtom',
  default: {},
});

export const tmsCoilSelector = selector({
  key: 'tmsCoilSelector',
  get: ({ get }) => get(tmsCoilAtom),
  set: ({ set }, value: any) => {
    // 真面两个的最大值
    const realCoilMaxTemp = Math.max(...[value.temp_0, value.temp_1].filter(temp => typeof temp === 'number'));
    // 伪面两个的最大值
    const backCoilMaxTemp = Math.max(...[value.temp_2, value.temp_3].filter(temp => typeof temp === 'number'));
    if (backCoilMaxTemp > 41) {
      value.coil_max_temperature = backCoilMaxTemp;
    } else {
      value.coil_max_temperature = realCoilMaxTemp;
    }

    value.render_temperature = value.coil_max_temperature === -Infinity ? '' : `${Math.floor(value.coil_max_temperature)}`;

    set(tmsCoilAtom, value);
  },
});
