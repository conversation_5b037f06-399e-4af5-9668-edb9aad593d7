import { atom, selector } from 'recoil';
import { defaultLocale } from '../../common/lib/intl/defaults';
import { Locale } from '../../common/lib/intl/locale';
// @ts-ignore
import messageZh from '../static/messages/zh-cn.json';
// @ts-ignore
import messageEn from '../static/messages/en-us.json';
import { intlMessage } from '../hocComponent/intlMessage';

const locale = localStorage.getItem('NG_LOCAL');
const defaultMessage = locale === 'en-US' ? messageEn : messageZh;

type State = { locale: Locale; messages?: any };
export const intlState = atom<State>({
  key: 'intlState',
  default: {
    locale: (localStorage.getItem('NG_LOCAL') as Locale) || defaultLocale,
    messages: defaultMessage,
  },
});

export const modifyIntlState = selector({
  key: 'modifyIntlState',
  get: ({ get }) => get(intlState),
  set: ({ set }, value: any) => {
    if (!value.locale) return;
    let message = {};
    if (value.locale === 'zh-CN') {
      message = messageZh;
    } else {
      message = messageEn;
    }
    intlMessage.m(value.locale);
    localStorage.setItem('NG_LOCAL', value.locale);
    set(intlState, { locale: value.locale, messages: message });
  },
});
