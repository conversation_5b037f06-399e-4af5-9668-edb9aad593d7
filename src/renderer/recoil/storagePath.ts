import { atom, selector } from 'recoil';

export const storagePath = atom<any>({
  key: 'storagePath',
  default: undefined,
});

export const useStoragePath = selector<string>({
  key: 'useStoragePath',
  get: ({ get }) => {
    return get(storagePath) ? get(storagePath) : localStorage.getItem('NG_STORAGE_PATH');
  },
  set: ({ set }, value: any) => {
    set(storagePath, value);
    localStorage.setItem('NG_STORAGE_PATH', value);
  },
});
