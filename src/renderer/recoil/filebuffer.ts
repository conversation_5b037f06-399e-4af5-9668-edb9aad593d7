import { atom, selector } from 'recoil';

export type SurfaceFileBuffer = { [key: string]: Buffer };
export const surfaceFileBufferAtom = atom<SurfaceFileBuffer>({
  key: 'surfaceFileBufferAtom',
  default: {},
});

export const surfaceFileBufferSelector = selector<SurfaceFileBuffer>({
  key: 'surfaceFileBufferSelector',
  get: ({ get }) => {
    return get(surfaceFileBufferAtom);
  },
  set: ({ set, get }, value: any) => {
    if (!value.path || !value.buffer) return;
    const surfaceFileBuffer = get(surfaceFileBufferAtom);
    if (surfaceFileBuffer[value.path]) return;
    set(surfaceFileBufferAtom, { ...surfaceFileBuffer, [value.path]: value.buffer });
  },
});
