import { atom, selector } from 'recoil';
import { Fault2RenderMapType, FaultEnum, FaultKeyEnum, FaultLevelEnum, FaultMapItemType } from '../../common/systemFault/type';

let faultStatic: Fault2RenderMapType = {
  [FaultLevelEnum.warning]: [],
  [FaultLevelEnum.error]: [],
};

export const faultAtom = atom<Fault2RenderMapType>({
  key: 'faultAtom',
  default: faultStatic,
});

export const useFaultSelector = selector<Fault2RenderMapType>({
  key: 'useFaultSelector',
  get: ({ get }) => {
    return get(faultAtom);
  },
  set: ({ set }, value: any) => {
    faultStatic = value;
    set(faultAtom, value);
  },
});

/**
 * 通过类型查找Fault
 * @param type 类型
 * @param info 错误Recoil
 * @returns []
 */
export const getFaultByType = (type: FaultEnum, info: Fault2RenderMapType = faultStatic) => {
  return info[FaultLevelEnum.error].filter(v => v.type === type);
};

/** 通过类型查找warn */
export const getWarnByType = (type: FaultEnum, info: Fault2RenderMapType = faultStatic) => {
  return info[FaultLevelEnum.warning].filter(v => v.type === type);
};

export const getFaultWithoutType = (type: FaultEnum, info: Fault2RenderMapType = faultStatic) => {
  return info[FaultLevelEnum.error].filter(v => v.type !== type);
};

/** 通过key获取错误 */
export const getFaultByKey = (key: FaultKeyEnum, info: Fault2RenderMapType = faultStatic) => {
  return info[FaultLevelEnum.error].find(v => v.key === key);
};
/** 通过key获取warn */
export const getWarnByKey = (key: string, info: Fault2RenderMapType = faultStatic) => {
  return info[FaultLevelEnum.warning].find(v => v.key === key);
};

export const getFaultLevel = (typeList: FaultEnum[], info: Fault2RenderMapType = faultStatic): FaultLevelEnum => {
  let level = FaultLevelEnum.normal;

  [...getFaultByTypeList(typeList, info[FaultLevelEnum.warning]), ...getFaultByTypeList(typeList, info[FaultLevelEnum.error])].forEach(v => {
    level = Math.min(v.errorLevel, level);
  });

  return level;
};

export const getFaultByTypeList = (typeList: FaultEnum[], info: FaultMapItemType[]) => {
  return info.filter(v => typeList.some(val => val === v.type));
};

/**
 * 通过key获取是否存在
 * @param keys FaultKeyEnum[]
 * @param info fault
 * @returns boolean
 */
export const getHasFaultByKeys = (keys: (FaultKeyEnum | string)[], info: Fault2RenderMapType = faultStatic) => {
  const key_set = new Set(keys);

  return info[FaultLevelEnum.error].some(v => key_set.has(v.key));
};
