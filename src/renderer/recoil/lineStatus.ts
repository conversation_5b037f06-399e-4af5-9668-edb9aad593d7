import { DefaultValue, atom, selector } from 'recoil';

export enum LineStatusType {
  loading = 'loading',
  success = 'success',
  error = 'error',
  delete = 'delete',
}

export type LineType = {
  status: LineStatusType;
  key: number;
  value?: {
    x: number;
    y: number;
    z: number;
    target_id: number;
  }[];
};

export const authTypeState = atom<LineType[]>({
  key: 'lineAtom',
  default: [],
});

export const useLineAtom = selector<LineType[]>({
  key: 'useLineAtom',
  get: ({ get }) => {
    return get(authTypeState);
  },
  set: ({ set, get }, value) => {
    if (!(value instanceof DefaultValue)) {
      const curValue = value[0];
      const list = get(authTypeState);
      const otherList = list.filter(v => v.key !== curValue.key);
      const curLines = list.find(v => v.key === curValue.key)?.value || [];
      if (curValue.status === 'delete') {
        set(authTypeState, otherList);
      } else {
        set(authTypeState, [...otherList, { ...curValue, value: [...curLines, ...(curValue.value || [])] }]);
      }
    }
  },
});
