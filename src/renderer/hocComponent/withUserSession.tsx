import React from 'react';
import { useRecoilState, SetterOrUpdater } from 'recoil';
import { userSessionAtom, UserSessionState } from '../recoil/user';

export type UserSessionProps = {
  userSession?: UserSessionState;
  setUserSession?: SetterOrUpdater<UserSessionState>;
};

export const withUserSession =
  <T extends object>(Component: React.ComponentType<T>) =>
    (props: T): React.ReactElement<UserSessionProps & T> => {
      const [userSession, setUserSession] = useRecoilState(userSessionAtom);

      return <Component userSession={userSession} setUserSession={setUserSession} {...props} />;
    };
