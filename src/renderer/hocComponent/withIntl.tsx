import React from 'react';
import { IntlProvider } from 'react-intl';
import { useRecoilValue } from 'recoil';
import { intlState } from '../recoil/intl';

type Props = { children: React.ReactElement };
export const ConnectedIntlProvider: React.FC<Props> = (props: Props) => {
  const intlStateValue = useRecoilValue(intlState);

  return <IntlProvider messages={intlStateValue.messages} locale={intlStateValue.locale} children={props.children} />;
};
