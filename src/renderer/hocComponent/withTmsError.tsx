import React from 'react';
import { useRecoilValue } from 'recoil';
import { tmsCoilAtom, tmsErrorAtom, TmsWorkStatus, tmsWorkStatusAtom, TmsCoil, ITmsError } from '../recoil/tmsError';

export type TmsWorkStatusProps = {
  tmsWorkStatus: TmsWorkStatus;
  coilInfo: TmsCoil;
};

export const withTmsError =
  <T extends object>(Component: React.ComponentType<T>) =>
    (props: T): React.ReactElement<ITmsError & T> => {
      const tmsError = useRecoilValue(tmsErrorAtom);

      return <Component tmsError={tmsError} {...props} />;
    };

export const withTmsWorkStatus =
  <T extends object>(Component: React.ComponentType<T>) =>
    (props: T): React.ReactElement<TmsWorkStatusProps & T> => {
      const tmsWorkStatus = useRecoilValue(tmsWorkStatusAtom);
      const coilInfo = useRecoilValue(tmsCoilAtom);

      return <Component tmsWorkStatus={tmsWorkStatus} coilInfo={coilInfo} {...props} />;
    };
