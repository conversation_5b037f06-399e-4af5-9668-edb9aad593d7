import React from 'react';
import { useRecoilState } from 'recoil';
import { authTypeState } from '../recoil/license';
// import { RecoilRoot } from 'recoil';

export type AuthProps = {
  authState?: any;
};

export const withAuth =
  <T extends object & { authName?: string }>(Component: React.ComponentType<T>) =>
    (props: T): React.ReactElement<AuthProps & T> => {
      const AuthItem = <HasAuth Component={Component} {...props} />;

      return props.authName ? AuthItem : <Component authState={{}} {...props} />;
    };

const HasAuth = (allprops: any): React.ReactElement<AuthProps> => {
  const [authState] = useRecoilState(authTypeState);
  const { Component, ...props } = allprops;

  return <Component authState={authState} {...props} />;
};
