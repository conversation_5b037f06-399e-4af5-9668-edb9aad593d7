import React from 'react';
import { useRecoilState, SetterOrUpdater } from 'recoil';
import { SurfaceFileBuffer, surfaceFileBufferSelector } from '../recoil/filebuffer';

export type FileBufferProps = {
  fileBuffer?: SurfaceFileBuffer;
  setFileBuffer?: SetterOrUpdater<{ buffer: Buffer; path: string }>;
};

export const withSurfaceFileBuffer =
  <T extends object>(Component: React.ComponentType<T>) =>
    (props: T): React.ReactElement<FileBufferProps & T> => {
      const [fileBuffer, setFileBuffer] = useRecoilState(surfaceFileBufferSelector);

      return <Component fileBuffer={fileBuffer} setFileBuffer={setFileBuffer} {...props} />;
    };
