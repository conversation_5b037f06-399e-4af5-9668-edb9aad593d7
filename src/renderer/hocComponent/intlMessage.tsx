// @ts-ignore
import messageZh from '../static/messages/zh-cn.json';
// @ts-ignore
import messageEn from '../static/messages/en-us.json';
import { defaultLocale } from '../../common/lib/intl/defaults';
import { Locale } from '../../common/lib/intl/locale';
let message = ((localStorage.getItem('NG_LOCAL') as Locale) || defaultLocale) === 'en-US' ? messageEn : messageZh;
export const intlMessage = {
  t: (value: string) => {
    return message[value];
  },
  m: (lang: string) => {
    message = lang === 'en-US' ? messageEn : messageZh;
  },
};
