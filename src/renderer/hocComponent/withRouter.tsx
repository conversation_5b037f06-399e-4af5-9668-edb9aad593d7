import React from 'react';
import { useLocation, useNavigate, useParams, NavigateFunction } from 'react-router-dom';

export type RouterProps<Params = undefined> = {
  router: {
    location: Location;
    navigate: NavigateFunction;
    params: Params;
  };
};

export const withRouter =
  <T extends object>(Component: React.ComponentType<T>) =>
    (props: T): React.ReactElement<RouterProps<any> & T> => {
      const location = useLocation();
      const navigate = useNavigate();
      const params = useParams();

      return <Component {...props} router={{ location, navigate, params }} />;
    };
