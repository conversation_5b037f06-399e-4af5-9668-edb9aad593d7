import { SimpleStatus } from '../../common/types';
export enum AuthEnum {
  none = 0, // 不显示
  disable = 1, // 禁止
  preview = 3, // 只读
  normal = 7, // 正常
}

export const authInfo = [
  {
    name: SimpleStatus.The200,
    type: {
      previewPlan: {
        pageItem: AuthEnum.normal,
        spotCancel: AuthEnum.normal,
        spotAdd: AuthEnum.normal,
        spotUpdate: AuthEnum.normal,
        reUpload: AuthEnum.normal,
        submit: AuthEnum.normal,
        importStimulate: AuthEnum.normal,
        spotDelete: AuthEnum.normal,
      },
      noImagePlan: {
        pageItem: AuthEnum.normal,
        iconItem: AuthEnum.normal,
        importStimulate: AuthEnum.normal,
        submit: AuthEnum.normal,
      },
    },
  },
  {
    name: SimpleStatus.The100,
    type: {
      previewPlan: {
        pageItem: AuthEnum.normal,
        spotCancel: AuthEnum.normal,
        spotAdd: AuthEnum.normal,
        spotUpdate: AuthEnum.normal,
        reUpload: AuthEnum.normal,
        submit: AuthEnum.normal,
        importStimulate: AuthEnum.normal,
        spotDelete: AuthEnum.normal,
      },
      noImagePlan: {
        pageItem: AuthEnum.normal,
        iconItem: AuthEnum.normal,
        importStimulate: AuthEnum.normal,
        submit: AuthEnum.normal,
      },
    },
  },
  {
    name: SimpleStatus.The99,
    type: {
      previewPlan: {
        pageItem: AuthEnum.disable,
        spotCancel: AuthEnum.disable,
        spotAdd: AuthEnum.disable,
        spotUpdate: AuthEnum.disable,
        reUpload: AuthEnum.disable,
        submit: AuthEnum.disable,
        importStimulate: AuthEnum.disable,
        spotDelete: AuthEnum.disable,
      },
      noImagePlan: {
        pageItem: AuthEnum.disable,
        iconItem: AuthEnum.disable,
        importStimulate: AuthEnum.disable,
        submit: AuthEnum.disable,
      },
    },
  },
];

export const devAuth = (isDev: boolean) => ({
  homeButton: isDev ? AuthEnum.normal : AuthEnum.none,
});
