import { TMSScreenState } from '../../common/constant/tms';
import { connSocket, imgSocket, personBatDistanceSocket } from './imgSocket';

export const initImageTms = (tid?: string) => {
  // eslint-disable-next-line @typescript-eslint/no-floating-promises
  window.tmsAPI.image_treatment_plan_start('PlanEnd', tid);
  // eslint-disable-next-line @typescript-eslint/no-floating-promises
  window.tmsAPI.image_treatment_plan_start('SingleEnd', tid);
  window.tmsAPI.set_beat_screen(TMSScreenState.NotStarted);
};

export const initNoImageTms = (tid?: string) => {
  // eslint-disable-next-line @typescript-eslint/no-floating-promises
  window.tmsAPI.noImage_treatment_plan_start('PlanEnd', tid);
  // eslint-disable-next-line @typescript-eslint/no-floating-promises
  window.tmsAPI.noImage_treatment_plan_start('SingleEnd', tid);
  window.tmsAPI.set_beat_screen(TMSScreenState.NotStarted);
};

export const initCamera = () => {
  connSocket.endRegistCoil();
  connSocket.clearStateInterval();
  imgSocket.stopRegistGetImg();
  imgSocket.stopReceiveImgMessage();
  imgSocket.clearInterval();
  personBatDistanceSocket.stopReceivePersonBatMessage();
  personBatDistanceSocket.clearInterval();
};

export const initTmsAndCamera = () => {
  initImageTms();
  initCamera();
};
