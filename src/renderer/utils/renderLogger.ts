import { LogType } from '../../common/ipc/ipcChannels';

const getLogData = () => {
  const date = new Date();
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const hour = date.getHours();
  const minute = date.getMinutes();
  const seconds = date.getSeconds();

  return `${year}-${month}-${day} ${hour}:${minute}:${seconds}`;
};

class RenderLogger {
  public debug(msg: string, msg1: string | number = '') {
    // eslint-disable-next-line no-console
    window.fileAPI.setRenderLog(LogType.debug, `${msg}  ${msg1}`);
  }

  public info(msg: string, msg1: string | number = '') {
    // eslint-disable-next-line no-console
    console.log(getLogData(), msg, msg1);
    window.fileAPI.setRenderLog(LogType.info, `${msg}  ${msg1}`);
  }

  public error(msg: string, msg1: string | number = '') {
    // eslint-disable-next-line no-console
    console.log(getLogData(), msg, msg1);
    window.fileAPI.setRenderLog(LogType.error, `${msg}  ${msg1}`);
  }

  public warn(msg: string, msg1: string | number = '') {
    // eslint-disable-next-line no-console
    console.log(getLogData(), msg, msg1);
    window.fileAPI.setRenderLog(LogType.warn, `${msg}  ${msg1}`);
  }
}

class TreatLogger {
  public info(...arg: any) {
    // eslint-disable-next-line no-console
    console.log(getLogData(), arg);
    window.fileAPI.setTreatLog(arg);
  }
}

class HistoryLogger {
  public info(arg: string) {
    window.fileAPI.setHistoryLog(arg);
  }
}

class TreatMatrixLog {
  public debug(arg: string) {
    window.fileAPI.setTreatMatrixLog(arg);
  }
}

/**
 * 删除故障log文件的path
 */
export const getSystemFaultLogPath = async () => {
  return `${await window.fileAPI.getLogPath()}/logs/systemFault/systemFault.log`;
};

export const sendRenderLog = new RenderLogger();
export const sendTreatLog = new TreatLogger();
export const sendHistoryLog = new HistoryLogger();
export const sendTreatMatrixLog = new TreatMatrixLog();
