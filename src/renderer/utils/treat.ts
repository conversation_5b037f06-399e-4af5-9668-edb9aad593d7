// eslint-disable-next-line import/no-internal-modules
import { AMFLoader } from 'three/examples/jsm/loaders/AMFLoader';
// eslint-disable-next-line import/no-internal-modules
import { OBJLoader } from 'three/examples/jsm/loaders/OBJLoader';
import { setBatCenterLine, setBatCenterPoint, setBatOpacity, setBatCircle, setBatOpacityOfObj } from '../component/surface/utils';
import { Group } from 'three';

export const dealUndefine: (obj: { [name: string]: any }) => { [name: string]: number } = obj => {
  // eslint-disable-next-line radix
  return Object.keys(obj).reduce((pre, cur) => ({ ...pre, [cur]: isNaN(parseInt(obj[cur])) || obj[cur] === null ? '--' : obj[cur] }), {});
};

class BatInfoByMatrix {
  public batTranslationDisplay: { angle?: number; distance?: number; x?: number; z?: number };
  public batTranslationCalc: { angle?: number; distance?: number; x?: number; z?: number };
  public batAngleDisplay: { angle?: number; horizontalAngle?: number };
  public batAngleCalc: { angle?: number; horizontalAngle?: number };
  public minDistanceDisplay?: number;
  public minDistanceCalc?: number;
  public horizontalDisplay?: number;
  public horizontalCalc?: number;
  constructor() {
    this.batTranslationDisplay = {
      angle: undefined,
      distance: undefined,
      x: undefined,
      z: undefined,
    };
    this.batTranslationCalc = {
      angle: undefined,
      distance: undefined,
      x: undefined,
      z: undefined,
    };
    this.batAngleDisplay = { angle: undefined, horizontalAngle: undefined };
    this.batAngleCalc = { angle: undefined, horizontalAngle: undefined };
    this.minDistanceDisplay = undefined;
    this.minDistanceCalc = undefined;
    this.horizontalDisplay = undefined;
    this.horizontalCalc = undefined;
  }

  public clearData = () => {
    this.batTranslationDisplay = { angle: undefined, distance: undefined, x: undefined, z: undefined };
    this.batTranslationCalc = { angle: undefined, distance: undefined, x: undefined, z: undefined };
    this.batAngleDisplay = { angle: undefined, horizontalAngle: undefined };
    this.batAngleCalc = { angle: undefined, horizontalAngle: undefined };
    this.minDistanceDisplay = undefined;
    this.minDistanceCalc = undefined;
    this.horizontalDisplay = undefined;
  };
}
export const batInfoByMatrix = new BatInfoByMatrix();

class Bat {
  public targetBat: any;
  public horizontalBat: any;
  public loadCB03Success?: (bat: Group) => void;
  public loadCBF03Success?: (bat: Group) => void;
  public batType: string;
  private cb03BatPreview: any;
  private cb03BatTreat: any;
  private cbf03BatPreview: any;
  private cbf03BatTreat: any;
  constructor() {
    this.cb03BatTreat = null;
    this.cb03BatPreview = null;

    this.cbf03BatTreat = null;
    this.cbf03BatPreview = null;

    this.targetBat = null;
    this.horizontalBat = null;
    this.batType = '';
  }

  public setBatType = (type: string) => {
    this.batType = type || 'CB-03';
    if (type === 'CB-03') {
      if (this.cb03BatTreat && !this.targetBat) {
        this.targetBat = this.cb03BatTreat.clone(true);
      }
      if (this.cb03BatPreview && !this.horizontalBat) {
        this.horizontalBat = this.cb03BatPreview.clone(true);
      }
    }
    if (['CBF-03','CBF-04','CB-04'].includes(type)) {
      if (this.cbf03BatTreat && !this.targetBat) {
        this.targetBat = this.cbf03BatTreat.clone(true);
      }
      if (this.cbf03BatPreview && !this.horizontalBat) {
        this.horizontalBat = this.cbf03BatPreview.clone(true);
      }
    }
  };

  // 加载cb03
  public loadCB03Bat = () => {
    const loader = new AMFLoader();
    loader.load('colormap/coil_cb03.amf', (cb03: Group) => {
      this.loadCB03Success?.(cb03);
      this.cb03BatTreat = cb03;
      this.cb03BatPreview = cb03.clone(true);

      this.cb03BatTreat.name = 'coil_cb03_treat';
      this.cb03BatTreat = setBatOpacity(this.cb03BatTreat, 0.75);
      this.cb03BatTreat = setBatCenterPoint(this.cb03BatTreat, 1);
      this.cb03BatTreat = setBatCircle(cb03, 1);
      this.cb03BatTreat = setBatCenterLine(this.cb03BatTreat);
      this.cb03BatTreat.position.set(2500, 2500, 2500);

      this.cb03BatPreview.name = 'coil_cb03_preview';
      this.cb03BatPreview = setBatOpacity(this.cb03BatPreview, 0.75);
      this.cb03BatPreview = setBatCenterPoint(this.cb03BatPreview, 1);
      this.cb03BatPreview.scale.set(0.15, 0.15, 0.3);
    });
  };

  // 加载cbf03
  public loadCBF03Bat = () => {
    const objLoader = new OBJLoader();
    objLoader.load('colormap/coil_cbf03-cb04-cbf04.obj', (cbf03: Group) => {
      this.loadCBF03Success?.(cbf03);
      this.cbf03BatTreat = cbf03;
      this.cbf03BatPreview = cbf03.clone(true);

      this.cbf03BatTreat.name = 'coil_cbf03-cb04-cbf04_treat';
      this.cbf03BatTreat = setBatOpacityOfObj(this.cbf03BatTreat, 0.75);
      this.cbf03BatTreat = setBatCenterPoint(this.cbf03BatTreat, 1);
      this.cbf03BatTreat = setBatCircle(cbf03, 1);
      this.cbf03BatTreat = setBatCenterLine(this.cbf03BatTreat);
      this.cbf03BatTreat.position.set(2500, 2500, 2500);

      this.cbf03BatPreview.name = 'coil_cbf03-cb04-cbf04_preview';
      this.cbf03BatPreview = setBatOpacityOfObj(this.cbf03BatPreview, 0.75);
      this.cbf03BatPreview = setBatCenterPoint(this.cbf03BatPreview, 1);
      this.cbf03BatPreview.scale.set(0.15, 0.15, 0.3);
    });
  };
}
export const bat = new Bat();
