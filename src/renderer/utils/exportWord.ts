import docxtemplater from 'docxtemplater';
// import {saveAs} from 'file-saver';
import Piz<PERSON><PERSON> from 'pizzip';
// @ts-ignore
import JSZipUtils from 'jszip-utils';
const exportWord = async (wordData: any, fileUrl: string, fileName: string) => {
  return new Promise((resolve, reject) => {
    // 读取并获得模板文件的二进制内容，是docxtemplater提供的固定写法
    JSZipUtils.getBinaryContent(fileUrl, async (error: any, content: any) => {
      // 抛出异常
      if (error) {
        throw error;
      }
      // 创建一个PizZip实例，内容为模板的内容
      let zip = new PizZip(content);
      let doc = new docxtemplater(zip, {
        linebreaks: true, // 识别\n
      });

      // 生成一个代表docxtemplater对象的zip文件（不是一个真实的文件，而是在内存中的表示）
      await doc.resolveData(wordData).then(async () => {
        doc.render();
        let out = doc.getZip().generate({
          type: 'blob',
          mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        });
        try {
          const reader = new FileReader();
          reader.onerror = reject;
          reader.onload = async () => {
            try {
              const result = await window.fileAPI.writeFileToDisk(fileName, reader.result as ArrayBuffer);

              resolve(result);
            } catch (err) {
              reject(err);
            }
          };
          reader.readAsArrayBuffer(out);
        } catch (err) {
          reject(err);
        }
      });
    });
  });
};

export { exportWord };
