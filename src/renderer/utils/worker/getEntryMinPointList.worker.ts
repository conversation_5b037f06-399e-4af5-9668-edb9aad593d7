import { getMinPoint } from './calMinEntryPoint';

// eslint-disable-next-line prefer-arrow/prefer-arrow-functions
onmessage = function (e) {
  const { volume, volSeedList } = e.data;
  let resultList = volSeedList.map((item: any) => {
    return {
      type: item.type,
      key: item.vol_ras,
      id: item.id,
      value: {},
    };
  });
  for (let item of resultList) {
    // 如果是头模数据直接返回
    item.value = getMinPoint(item.key, volume);
  }

  postMessage(resultList);
};
