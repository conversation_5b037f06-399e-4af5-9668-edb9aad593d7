import { Setting } from '@/common/types';

export let setting: Setting;

const initRenderSetting = async () => {
  setting = await window.systemAPI.getSetting();
};
// eslint-disable-next-line @typescript-eslint/no-floating-promises
initRenderSetting();

export const REACT_APP_NG_API_BASEURL = () => {
  return setting.REACT_APP_NG_API_BASEURL;
};

export const WEB_ORIGIN_URL = () => {
  return setting.WEB_ORIGIN_URL;
};

export const GATEWAY_API_BASEURL = () => {
  return setting.GATEWAY_API_BASEURL;
};

export const IMAGE_SOCKET_URL = () => {
  return setting.IMAGE_SOCKET_URL;
};

export const PDU_SOCKET_URL = () => {
  return setting.PDU_SOCKET_URL;
};

export const TMS_SOCKET_URL = () => {
  return setting.TMS_SOCKET_URL;
};

export const LOG_API_BASEURL = () => {
  return setting.LOG_API_BASEURL;
};

export const REACT_APP_SHOW_APP_MENU = () => {
  return setting.REACT_APP_SHOW_APP_MENU;
};

export const REACT_APP_LOG_DEBUG = () => {
  return setting.REACT_APP_LOG_DEBUG;
};

export const GIT_COMMIT_SHORT_SHA = () => {
  return setting.GIT_COMMIT_SHORT_SHA;
};

export const TOOL_BECAXYZ = () => {
  return setting.TOOL_BECAXYZ;
};

export const TOOL_CAXYZ = () => {
  return setting.TOOL_CAXYZ;
};

export const CHECK_SMART_BASEURL = () => {
  return setting.CHECK_SMART_BASEURL;
};
