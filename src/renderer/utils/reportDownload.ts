import moment from 'moment';
import { IntlShape } from 'react-intl';
import { exportWord } from './exportWord';
import dayjs from 'dayjs';
import { ISubjectResponse, PlanReportModel } from '@/common/types';
export enum EnumPlanStimulusType {
  /**
   * 单刺激
   */
  SINGLE = 1,

  /**
   * 重复刺激
   */
  RTMS = 2,

  /**
   * 刺激间歇性的TBS
   */
  ITBS = 3,

  /**
   * 刺激连续的丛状刺激称
   */
  CTBS = 4,

  /**
   * 爆发式刺激
   */
  TBS = 5,
}
const formatWordData = (
  planInfo: {
    plan: ISubjectResponse;
    logs: any[];
  },
  intl: IntlShape
) => {
  const { plan: subject, logs } = planInfo;
  const genderMap = {
    1: '男',
    2: '女',
    3: '其他',
  };

  return {
    name: subject.name,
    id: subject.code,
    sex: genderMap[subject.sex!] || '',
    birth_date: subject.birth_date ? moment(subject.birth_date).format('YYYY-MM-DD') : '',
    condition_desc: subject?.condition_desc ?? '',
    motion: subject.motion_threshold ?? '',
    treatmentList: logs,
  };
};

// @ts-ignore
const formatCureLogs = (logs: PlanReportModel[], intl: IntlShape) => {
  let formatLogs = logs.map((log, index) => {
    const stimulateTemplate = log.stimulus_data;
    let pushTemplate = {};
    if ([EnumPlanStimulusType.ITBS, EnumPlanStimulusType.CTBS].includes(stimulateTemplate.type)) {
      let tempStimulateTemplate = stimulateTemplate;
      pushTemplate = {
        isCommon: false,
        isITBS: true,
        isTime: stimulateTemplate.strand_pulse_count && stimulateTemplate.strand_pulse_count > 1,
        iTBS_plexusInnerFrequency: tempStimulateTemplate.plexus_inner_frequency,
        iTBS_plexusInterFrequency: tempStimulateTemplate.plexus_inter_frequency,
        iTBS_plexusInnerPulseCount: tempStimulateTemplate.plexus_inner_pulse_count,
        iTBS_plexusCount: tempStimulateTemplate.plexus_count,
        strandPulseCount: tempStimulateTemplate.strand_pulse_count,
        intermissionTime: tempStimulateTemplate.intermission_time,
      };
    } else {
      let tempStimulateTemplate = stimulateTemplate;
      pushTemplate = {
        isCommon: true,
        isITBS: false,
        isTime: true,
        common_strandPulseFrequency: tempStimulateTemplate.strand_pulse_frequency,
        common_innerStrandPulseCount: tempStimulateTemplate.inner_strand_pulse_count,
        strandPulseCount: tempStimulateTemplate.strand_pulse_count,
        intermissionTime: tempStimulateTemplate.intermission_time,
      };
    }

    return {
      startTime: dayjs(log.stimulus_start_time).format('YYYY-MM-DD HH:mm:ss'),
      endTime: dayjs(log.stimulus_end_time).format('YYYY-MM-DD HH:mm:ss'),
      stimulationTime: log.actual_stimulation_duration,
      treatmentTime: log.planned_stimulation_duration,
      spotName: log.target_data.targetName,
      spotVertex: log.target_data.code,
      volCoord: `${log.target_data.vol_seed.x.toFixed(2)},${log.target_data.vol_seed.y.toFixed(2)},${log.target_data.vol_seed.z.toFixed(2)}`,
      type: intl.formatMessage({ id: `point.cureProject.params.type.${stimulateTemplate.type}` }),
      common_stimulateType: stimulateTemplate.type,
      common_pulseTotal: stimulateTemplate.pulse_total,
      motion_threshold: log.motion_threshold,
      common_relativeStrength: stimulateTemplate.relative_strength,
      common_absoluteStrength: stimulateTemplate.actual_strength,
      ...pushTemplate,
    };
  });

  return formatLogs.flat();
};
export const onDownloadReport = async (
  plan: {
    plan: ISubjectResponse;
    logs: any[];
    fileName: string;
  },
  intl: IntlShape,
  docxTemplateUrl: string
) => {
  let wordData = formatWordData(plan, intl);
  // let fileUrl = 'http://127.0.0.1:4001/colormap/cureTemplate.docx';

  let isSuccess = await exportWord(wordData, docxTemplateUrl, plan.fileName);

  return isSuccess;
};
