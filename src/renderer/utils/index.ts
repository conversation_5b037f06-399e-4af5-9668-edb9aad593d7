import moment from 'moment';
import dayjs from 'dayjs';
import { FileInfo } from '../uiComponent/NgFileList';
type BaseUnit =
  | 'year'
  | 'years'
  | 'y'
  | 'month'
  | 'months'
  | 'M'
  | 'week'
  | 'weeks'
  | 'w'
  | 'day'
  | 'days'
  | 'd'
  | 'hour'
  | 'hours'
  | 'h'
  | 'minute'
  | 'minutes'
  | 'm'
  | 'second'
  | 'seconds'
  | 's'
  | 'millisecond'
  | 'milliseconds'
  | 'ms';
export const transformFileSize = (bytes: number, decimals = 2) => {
  if (bytes === 0) return '0 B';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(dm))} ${sizes[i]}`;
};

export const fileNameOverflow = (fileName: string, maxLength = 10) => {
  const extension = fileName.substring(fileName.lastIndexOf('.') + 1);
  const fileNameWithoutExtension = fileName.substring(0, fileName.lastIndexOf('.'));

  if (fileName.length <= maxLength || !fileNameWithoutExtension.length) {
    return fileName;
  }

  const firstLine = fileNameWithoutExtension.substring(0, maxLength);
  let secondLine = fileNameWithoutExtension.substring(maxLength);

  if (secondLine.length > maxLength) {
    secondLine = `${secondLine.substring(0, maxLength / 4)}...${secondLine.substring(Math.floor(secondLine.length - maxLength / 5))}`;
  }

  return `${firstLine}\n${secondLine}.${extension.substring(0, 5)}`;
};

/**
 * @description 判断目标日期是否临近期限
 * @param targetDate 目标时间
 * @param near 相差多少
 * @param unit 单位：月、日、年
 */
export const isNearExpiration = (targetDate: moment.Moment, near: number, unit: BaseUnit) => {
  const currentDate = moment();
  const diffInMonths = targetDate.diff(currentDate, unit, true);

  return diffInMonths <= near;
};

export const isAfterTime = (newDate: moment.Moment) => {
  const currentDate = moment();
  const isOverFiveYears = currentDate.isAfter(newDate.endOf('days'));

  return isOverFiveYears;
};

export const dateToTimestamp = (date: any) => {
  if (date) {
    const day = dayjs(date).format('YYYY-MM-DD');
    const timestamp = dayjs(day).valueOf();

    return timestamp;
  } else {
    const day = dayjs().format('YYYY-MM-DD');
    const timestamp = dayjs(day).valueOf();

    return timestamp;
  }
};

export let oneHundred = 1.0e5;

export let thirtyMillion = 3.0e7;
// 温感差
export const TemperatureDifference = 20;

/**
 * 防抖的参数设置
 */
export const debounceOption = {
  wait: 500,
  leading: true,
  trailing: false,
};

/**
 * 节流的参数设置
 */
export const throttleOption = {
  wait: 500,
  leading: true,
  trailing: false,
};

export const isDirectory = (file: FileInfo) => {
  return file.type === 'directory';
};
