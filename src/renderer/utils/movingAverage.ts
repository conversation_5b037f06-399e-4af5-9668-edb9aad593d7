class MovingAverage {
  private queue: any[];
  private length: number;
  constructor(length: number) {
    this.length = length;
    this.queue = [];
  }

  computedAverage = () => {
    const sum = this.queue.reduce((prev, next) => {
      const xSum = (prev[0] as number) + (next[0] as number);
      const ySum = (prev[1] as number) + (next[1] as number);
      const zSum = (prev[2] as number) + (next[2] as number);

      return [xSum, ySum, zSum];
    });
    const xAverage = sum[0] / this.queue.length;
    const yAverage = sum[1] / this.queue.length;
    const zAverage = sum[2] / this.queue.length;

    return [xAverage, yAverage, zAverage];
  };

  add = (matrix: any) => {
    if (this.queue.length === this.length) {
      this.queue.shift();
      this.queue.push(matrix);

      return;
    }
    this.queue.push(matrix);
  };
}

export const movingAverage = new MovingAverage(30);
