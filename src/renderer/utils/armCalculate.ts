// eslint-disable-next-line @typescript-eslint/no-var-requires
// let mathlab = require('mathlab');
import * as mathlab from 'mathlab';

// Calculaet the direction for mechanical arm
// Parm: vector    [in]  the direction of target [x, y, z] (surfXYZ - targetXYZ)
// Parm: position  [in]  the position of target [x, y, z] surfXYZ
// Parm: distance  [in]  the upward position of target
// Out : Position1 [out] the attitude matrix for mechanical arm
// Out : Position2 [out] the attitude matrix for mechanical arm with upward
export const CalculateDirection = (vector: number[], position: number[], distance: number, theta: number): any[] => {
  // Check the input data
  if (vector.length !== 3 || position.length !== 3) {
    return [];
  }
  if (vector[0] === 0) {
    vector[0] = 0.1;
  }
  if (vector[2] < 0) return [];
  // Defination for axix of 0-xyz
  let vectorZ: number[] = [0, 0, 1];
  let vectorX: number[] = [1, 0, 0];

  // The projection of Y axis
  let vectorPX: number[] = [0, vector[1], vector[2]];
  // Calculate the included angle
  let thetaY: number = 0;
  if (vector[1] > 0) {
    thetaY = mathlab.acos(mathlab.dot(vectorPX, vectorZ) / mathlab.norm2(vectorPX) / mathlab.norm2(vectorZ));
  } else {
    thetaY = 2 * Math.PI - mathlab.acos(mathlab.dot(vectorPX, vectorZ) / mathlab.norm2(vectorPX) / mathlab.norm2(vectorZ));
  }

  if (isNaN(thetaY)) return [];
  let matrixY: number[][] = [
    [1, 0, 0],
    [0, mathlab.cos(thetaY), -mathlab.sin(thetaY)],
    [0, mathlab.sin(thetaY), mathlab.cos(thetaY)],
  ];

  let transVector: number[] = mathlab.dot(matrixY, vector);
  let thetaX: number = ((Math.PI) / 2) + (mathlab.acos(mathlab.dot(transVector, vectorX) / mathlab.norm2(transVector) / mathlab.norm2(vectorX)) as number);

  let matrixYTmp: number[][] = [
    [1, 0, 0, 0],
    [0, mathlab.cos(thetaY), -mathlab.sin(thetaY), 0],
    [0, mathlab.sin(thetaY), mathlab.cos(thetaY), 0],
    [0, 0, 0, 1],
  ];

  let matrixX: number[][] = [
    [mathlab.cos(thetaX), 0, mathlab.sin(thetaX), 0],
    [0, 1, 0, 0],
    [-mathlab.sin(thetaX), 0, mathlab.cos(thetaX), 0],
    [0, 0, 0, 1],
  ];

  let matrixT: number[][] = [
    [-1, 0, 0, 0],
    [0, -1, 0, 0],
    [0, 0, 1, 0],
    [0, 0, 0, 1],
  ];

  let matrixT2: number[][] = [
    [mathlab.cos(theta), -mathlab.sin(theta), 0, 0],
    [mathlab.sin(theta), mathlab.cos(theta), 0, 0],
    [0, 0, 1, 0],
    [0, 0, 0, 1],
  ];

  // out
  let Position1: number[][] = mathlab.dot(mathlab.dot(mathlab.dot(mathlab.inv(matrixYTmp), mathlab.inv(matrixX)), matrixT), matrixT2);
  Position1[0][3] = position[0];
  Position1[1][3] = position[1];
  Position1[2][3] = position[2];

  let Position2: number[][] = mathlab.dot(mathlab.dot(mathlab.dot(mathlab.inv(matrixYTmp), mathlab.inv(matrixX)), matrixT), matrixT2);
  Position2[0][3] = position[0] + (vector[0] / mathlab.norm2(vector)) * distance;
  Position2[1][3] = position[1] + (vector[1] / mathlab.norm2(vector)) * distance;
  Position2[2][3] = position[2] + (vector[2] / mathlab.norm2(vector)) * distance;

  return [Position1, Position2];
};
