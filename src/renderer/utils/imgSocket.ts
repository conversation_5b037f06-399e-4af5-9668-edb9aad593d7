import { cloneDeep, isEqual, pick } from 'lodash';
import { CAMERA_STATUS_KEYS } from '../constant/tmsAndImageSocket';
import {
  getDistanceParams,
  getImageParams,
  imgSocketConfig,
  paramsSet,
  registerParams,
  stopDistanceParams,
  stopGetImageParams,
  stopRegisterParams,
  Response_10202_Enum,
  IPC_MODULE,
  cameraInfoParams,
} from '../constant/imgSocket';
import { IMAGE_SOCKET_URL } from './renderSetting';
import { sendRenderLog } from './renderLogger';

/**
 * 相机信息
 */
export type CameraInfoType = {
  pstCameraSerialNum: string;
  version: string;
  commit?: string;
};

export type GetStatusCallbackParam = {
  open: boolean;
  getImage: boolean;
  cameraStatus: boolean;
  cameraErrors: any;
};

export enum Panel {
  panelWhite = 0,
  panelBlack = 1,
}
// BEGIN-NOSCAN
const handleEqual = (cameraErrors: GetStatusCallbackParam['cameraErrors'], returnValue: any) => {
  const newCameraErrors = cloneDeep(cameraErrors);
  newCameraErrors.facepointcloud = returnValue.facepointcloud;
  newCameraErrors.facedetection = returnValue.facedetection;
  newCameraErrors.treatboard = returnValue.treatboard;
  // if (newCameraErrors.facepointcloud !== returnValue.facepointcloud) {
  //   sendRenderLog.debug('facepointcloud变化', returnValue.facepointcloud);
  //   sendRenderLog.debug('当前时间与facepointcloudts变化的时间差', Date.now() - returnValue.facepointcloudts);
  //   if (!newCameraErrors.facepointcloud) {
  //     newCameraErrors.facepointcloud = returnValue.facepointcloud;
  //   } else {
  //     if (Date.now() - returnValue.facepointcloudts > 1000) {
  //       sendRenderLog.debug('当前时间与facepointcloudts变化大于1s');
  //       newCameraErrors.facepointcloud = returnValue.facepointcloud;
  //     }
  //   }
  // }
  // if (newCameraErrors.facedetection !== returnValue.facedetection) {
  //   sendRenderLog.debug('facedetection变化', returnValue.facedetection);
  //   sendRenderLog.debug('当前时间与facedetectionts变化的时间差', Date.now() - returnValue.facedetectionts);
  //   if (!newCameraErrors.facedetection) {
  //     newCameraErrors.facedetection = returnValue.facedetection;
  //   } else {
  //     if (Date.now() - returnValue.facedetectionts > 1000) {
  //       sendRenderLog.debug('当前时间与facedetectionts变化大于1s');
  //       newCameraErrors.facedetection = returnValue.facedetection;
  //     }
  //   }
  // }
  // if (newCameraErrors.treatboard !== returnValue.treatboard) {
  //   sendRenderLog.debug('treatboard变化', returnValue.treatboard);
  //   sendRenderLog.debug('当前时间与treatboardts变化的时间差', Date.now() - returnValue.treatboardts);
  //   if (!newCameraErrors.treatboard) {
  //     newCameraErrors.treatboard = returnValue.treatboard;
  //   } else {
  //     if (Date.now() - returnValue.treatboardts > 1000) {
  //       sendRenderLog.debug('当前时间与treatboardts变化大于1s');
  //       newCameraErrors.treatboard = returnValue.treatboard;
  //     }
  //   }
  // }

  return newCameraErrors;
};
// END-NOSCAN

// 设置状态socket
class ConnSocket {
  private client?: WebSocket | null;
  private listeners: any;
  public getImage: boolean;
  public cameraStatus: boolean;
  public cameraErrors: any;
  public createSocketCallback?: () => void;
  public coilPanelMissingCallBack?: () => void;
  public onmessage?: (data: any) => void;
  public plyFile: string;
  public plyMd5: string;
  public import_id: string;
  public file_sha256: string;
  public file_version: string;
  public file_source: string;
  public uid: string;
  public tid: string;
  public target: (number | undefined)[][];
  public sur2vol: number[][];
  private resolve?: (obj?: any) => void;
  private reject?: (obj?: any) => void;
  private setStateInterval: any;
  public getImageCallback?: () => void;
  public navangle: (number | undefined)[];
  private session?: number;
  public accuracy: any;
  public icp_module: number;

  constructor() {
    this.getImage = false;
    this.cameraStatus = false;
    this.listeners = {};
    this.plyFile = '';
    this.plyMd5 = '';
    this.import_id = '';
    this.file_sha256 = '';
    this.file_version = '';
    this.file_source = '';
    this.uid = '';
    this.tid = '';
    this.target = [[], []];
    this.sur2vol = [];
    this.setStateInterval = 0;
    this.navangle = [];
    this.accuracy = {};
    this.icp_module = IPC_MODULE.NATIVE;
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    this.initSocket();
  }

  private initSocket = async () => {
    const isNav = await window.systemAPI.getNAV();
    if (!isNav) return;
    setInterval(async () => {
      if ([WebSocket.OPEN, WebSocket.CONNECTING].includes(this.client?.readyState as any)) return;
      await this.createSocket();
    }, 2000);
  };

  private callListeners = () => {
    Object.values(this.listeners).forEach((listen: any) => {
      listen?.({
        open: this.client?.readyState === WebSocket.OPEN,
        getImage: this.getImage,
        cameraStatus: this.cameraStatus,
        cameraErrors: this.cameraErrors,
      });
    });
  };

  public listenStatus = (key: string, callback: (status: any) => void) => {
    if (this.listeners[key] || typeof callback !== 'function') return;
    this.listeners[key] = callback;
  };

  public clearListenStatusByKey = (key: string) => {
    if (!this.listeners[key]) return;
    this.listeners[key] = null;
  };

  public clearAllStatusListener = () => {
    this.listeners = {};
  };

  private setCameraStatus = (returnValue: any) => {
    const cameraErrors = pick(returnValue, CAMERA_STATUS_KEYS);
    if (!this.cameraErrors) {
      this.cameraErrors = cameraErrors;
    }
    if (!isEqual(returnValue.camerastatus === 1, this.cameraStatus)) {
      sendRenderLog.debug('camerastatus状态变化', JSON.stringify(returnValue));
    }
    if (Date.now() - returnValue.camerastatusts > 3000) {
      if (!isEqual(returnValue.camerastatus === 1, this.cameraStatus)) {
        sendRenderLog.debug(
          'camerastatus状态变化时间大于3s',
          `returnValue.camerastatus:${returnValue.camerastatus},this.cameraStatus:${this.cameraStatus}`
        );
      }
      this.cameraStatus = returnValue.camerastatus === 1;
    }
    if (returnValue.camerastatus !== 1) {
      sendRenderLog.error('camerastatus不为1', JSON.stringify(returnValue));
    }
    this.cameraErrors = handleEqual(this.cameraErrors, returnValue);
    this.callListeners();
  };

  public createSocket = async () => {
    // NOSONAR
    try {
      const imgSocketUrl = IMAGE_SOCKET_URL();
      this.client = null;
      sendRenderLog.info('ConnSocket-WebSocket创建');
      this.client = new WebSocket(imgSocketConfig.URL || imgSocketUrl);
    } catch (err: any) {
      this.callListeners();
      sendRenderLog.error('ConnSocket实例化故障', err);
    }
    if (!this.client) return;
    this.client.onopen = () => {
      this.createSocketCallback?.();
    };
    this.client.onmessage = evt => {
      const returnValue = JSON.parse(evt.data);
      this.session = returnValue.session;
      if (this.onmessage) this.onmessage(returnValue);
      if (returnValue.id === Response_10202_Enum.id) {
        if (returnValue.code === Response_10202_Enum.success && returnValue.msg === 'start') {
          sendRenderLog.info('参数设置成功');
          this.getImage = true;
          this.getImageCallback?.();
        } else {
          sendRenderLog.info('参数设置失败:', returnValue.err);
          this.getImage = false;
          if (returnValue.code === Response_10202_Enum.panelMissing) {
            // 10202 注册矩阵丢失，抛到外部处理
            this.coilPanelMissingCallBack?.();
          }
        }
      }
      if (returnValue.id === 11307) {
        this.setCameraStatus(returnValue);
      }
      if (
        (returnValue.id === 10213 || returnValue.id === 10219 || returnValue.id === 10228 || returnValue.id === 11311) &&
        returnValue.code !== 1 &&
        this.reject
      ) {
        sendRenderLog.error(`收到失败回复命令 ${returnValue.id}`);
        this.reject?.(returnValue);
      } else if (
        (returnValue.id === 10213 || returnValue.id === 10219 || returnValue.id === 10228 || returnValue.id === 11311) &&
        returnValue.code === 1 &&
        this.reject
      ) {
        sendRenderLog.info(`收到正确回复命令 ${returnValue.id}`);
        this.resolve?.(returnValue);
      }
    };
    this.client.onclose = () => {
      this.getImage = false;
      this.cameraStatus = false;
      sendRenderLog.warn('socket close', this.session);
      this.callListeners();
    };
    this.client.onerror = () => {
      this.getImage = false;
      this.cameraStatus = false;
      sendRenderLog.warn('socket error', this.session);
      this.callListeners();
    };
  };

  public commonAgreement = () => {
    if (this.client?.readyState !== WebSocket.OPEN) return;
    this?.client?.send(JSON.stringify({ id: 11308 }));
    setInterval(() => {
      if (this.client?.readyState !== WebSocket.OPEN || this.cameraStatus) return;
      this?.client?.send(JSON.stringify({ id: 11308 }));
    }, 3000);
  };

  public setState = () => {
    if (this.client?.readyState !== WebSocket.OPEN) return;
    sendRenderLog.info('开始设置参数');
    this.getTreatmentImg();
    this.setStateInterval = setInterval(() => {
      this.getTreatmentImg();
    }, 2000);
  };

  private getTreatmentImg = () => {
    if (this.getImage === true || this.client?.readyState !== WebSocket.OPEN) return;
    const param = {
      ...paramsSet,
      opt: 1,
      ply: this.plyFile,
      // ply: '/data/neuralgalaxy/ngiq-pro-server/file/e780229877044ddd8bf36f4fb7e5a3b3/s/36/j/115/viz/prep/face.ply',
      // ply: '/data/neuralgalaxy/ngiq-pro-server/file/L7BnBnjR3_pnt/s/233/j/300/viz/prep/face.ply',  // 41.79
      // ply: '/data/neuralgalaxy/ngiq-pro-server/file/L7BnBnjR3_pnt/s/233/j/300/viz/prep/face.ply',  // 40.248
      plymd5: this.plyMd5,
      uid: this.uid,
      tid: this.tid,
      target: this.target,
      sur2vol: this.sur2vol,
      navangle: this.navangle,
      cloud_plan_id: this.import_id,
      ngfile_sha256: this.file_sha256,
      ngfile_source: this.file_source,
      ngfile_version: this.file_version,
      ...this.accuracy,
      ply_cut: this.plyFile,
      ply_cut_md5: this.plyMd5,
      icp_module: this.icp_module,
    };
    sendRenderLog.info('开始设置参数', param);
    this?.client?.send(JSON.stringify(param));
  };

  /**
   * 获取相机信息
   */
  public getCameraInfo = async () => {
    if (this.client?.readyState !== WebSocket.OPEN) return Promise.reject();
    this?.client?.send(
      JSON.stringify({
        ...cameraInfoParams,
      })
    );

    return new Promise((resolve: any, reject: any) => {
      this.resolve = resolve;
      this.reject = reject;
      this.setTimeout(reject);
    });
  };

  // 停止态
  public clearState = () => {
    if (this.client?.readyState !== WebSocket.OPEN) return;
    if (!this.getImage) return;
    this?.client?.send(
      JSON.stringify({
        ...paramsSet,
        opt: 0,
        ply: this.plyFile,
        plymd5: this.plyMd5,
        uid: this.uid,
        tid: this.tid,
        cloud_plan_id: this.import_id,
        ngfile_sha256: this.file_sha256,
        ngfile_source: this.file_source,
        ngfile_version: this.file_version,
        msg: 'end',
      })
    );
  };

  public clearStateInterval = () => {
    clearInterval(this.setStateInterval);
    this.clearState();
    this.closeGetImage();
  };

  public registCoil = async (panel: Panel) => {
    if (this.client?.readyState !== WebSocket.OPEN) return Promise.reject();
    const batData = {
      cbsn: panel, // 0: 白底黑圆标定板 1:黑底白圆标定板
      eqsn: 0, // 0:十字   1:笔
      id: 10211,
      msg: 'just do it!',
      opt: 1, // 0:结束校准 1:开始校准
      ptsn: 0, // 0,1,2,3 十字时无用
      tid: 'xxx',
      toolbecaxyz: imgSocketConfig.TOOL_BECAXYZ,
      toolcaxyz: imgSocketConfig.TOOL_CAXYZ,
      uid: 'ctrl1',
    };
    this.client?.send(JSON.stringify(batData));
    sendRenderLog.info('发送10211', panel === Panel.panelWhite ? '白底黑圆标定板' : '黑底白圆标定板, 回复是 10213'); // eslint-disable-line no-console

    return new Promise((resolve: any, reject: any) => {
      this.resolve = resolve;
      this.reject = reject;
      this.setTimeout(reject);
    });
  };

  public endRegistCoil = () => {
    if (this.client?.readyState !== WebSocket.OPEN) return;
    this.client?.send(JSON.stringify({ id: 10211, opt: 0 }));
  };

  public saveRegist = async () => {
    if (this.client?.readyState !== WebSocket.OPEN) return Promise.reject();
    const data = {
      id: 10227,
    };
    this.client?.send(JSON.stringify(data));
    sendRenderLog.info('发送保存注册协议成功10227, 回复应该是10228'); // eslint-disable-line no-console

    return new Promise((resolve: any, reject: any) => {
      this.resolve = resolve;
      this.reject = reject;
      this.setTimeout(reject);
    });
  };

  public sendRegist = async (step: number) => {
    if (this.client?.readyState !== WebSocket.OPEN) return Promise.reject();
    const data = {
      id: 10218,
    };
    this.client?.send(JSON.stringify(data));
    sendRenderLog.info(`第${step}步 发送注册成功10218`, '回复是10219'); // eslint-disable-line no-console

    return new Promise((resolve: any, reject: any) => {
      this.resolve = resolve;
      this.reject = reject;
      this.setTimeout(reject);
    });
  };

  public closeGetImage = () => {
    this.getImage = false;
  };

  private setTimeout = (reject: (obj: any) => void) => {
    setTimeout(() => {
      reject?.({ timeout: true });
    }, 10000);
  };

  public setConfig = (key: number, value: any) => {
    if (this.client?.readyState !== WebSocket.OPEN) return;
    this.client?.send(
      JSON.stringify({
        id: 11304,
        uid: this.uid,
        sw: key,
        opt: value,
        save: 1,
      })
    );
  };
}

export const connSocket = new ConnSocket();

class ImgSocket {
  private client?: WebSocket;
  public imgServerStatus: boolean;
  public onmessage?: (data: any) => void;
  public startReceiveImg: boolean;
  public createSocketCallback?: () => void;
  private interval: any;
  private startReceivedImg: boolean;
  private session?: number;

  constructor() {
    this.imgServerStatus = false;
    this.startReceiveImg = false;
    this.interval = 0;
    this.startReceivedImg = false;
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    this.initSock();
  }

  private initSock = async () => {
    const isNav = await window.systemAPI.getNAV();
    if (!isNav) return;
    setInterval(async () => {
      if ([WebSocket.OPEN, WebSocket.CONNECTING].includes(this.client?.readyState as any)) return;
      await this.createSocket();
    }, 2000);
  };

  private getConnectMessage = (data: any) => {
    const returnValue = JSON.parse(data.data);
    this.session = returnValue.session;
    if (returnValue.id === 10205 || returnValue.id === 10216) {
      if (returnValue.code === 1 && returnValue.msg === 'start') {
        this.imgServerStatus = true;
      }
    }
    if (this.imgServerStatus && (returnValue.id === 10206 || returnValue.id === 10217)) {
      if (this.startReceivedImg) {
        sendRenderLog.debug('接受到视频base64', returnValue);
        this.startReceivedImg = false;
      }
      this.onmessage?.(returnValue);
    }
  };
  public createSocket = async () => {
    try {
      const imgSocketUrl = IMAGE_SOCKET_URL();
      sendRenderLog.info('ImgSocket-WebSocket创建');
      this.client = new WebSocket(imgSocketConfig.URL || imgSocketUrl);
    } catch (err: any) {
      sendRenderLog.error('ImgSocket实例化故障', err);
    }
    if (!this.client) return;
    this.client.onmessage = this.getConnectMessage;
    this.client.onopen = () => {
      this.createSocketCallback?.();
    };
    this.client.onclose = () => {
      this.imgServerStatus = false;
      sendRenderLog.warn('socket close', this.session);
    };
    this.client.onerror = () => {
      this.imgServerStatus = false;
      sendRenderLog.warn('socket error', this.session);
    };
  };

  public sendImageMessage = () => {
    if (this.client?.readyState !== WebSocket.OPEN) return;
    sendRenderLog.info('开始获取图片');
    this?.client?.send(JSON.stringify(getImageParams));
    this.startReceivedImg = true;
    this.interval = setInterval(() => {
      if (!connSocket.cameraStatus) {
        this.imgServerStatus = false;
      }
      if (this.imgServerStatus || !this.startReceiveImg) return;
      sendRenderLog.info('开始获取图片-interval');
      this?.client?.send(JSON.stringify(getImageParams));
    }, 1000);
  };

  // 停止接收
  public stopReceiveImgMessage = () => {
    this.startReceiveImg = false;
    if (this.client?.readyState !== WebSocket.OPEN) return;
    clearInterval(this.interval);
    this?.client?.send(JSON.stringify(stopGetImageParams));
  };

  public startRegistGetImg = () => {
    if (this.client?.readyState !== WebSocket.OPEN) return;
    registerParams.width = 520;
    registerParams.height = 500;
    this.sendRegistGetImg();
    this.interval = setInterval(() => {
      this.sendRegistGetImg();
    }, 2000);
  };

  public sendRegistGetImg = () => {
    if (!connSocket.cameraStatus) {
      this.imgServerStatus = false;
    }
    if (this.imgServerStatus || !this.startReceiveImg) return;
    sendRenderLog.info('图片设置发送', 10214); // eslint-disable-line no-console
    this.startReceivedImg = true;
    this?.client?.send(JSON.stringify(registerParams));
  };

  // 停止接受注册的图片
  public stopRegistGetImg = () => {
    this.startReceiveImg = false;
    if (this.client?.readyState !== WebSocket.OPEN) return;
    clearInterval(this.interval);
    this.imgServerStatus = false; // 暂时先写这里
    this?.client?.send(JSON.stringify(stopRegisterParams));
  };

  public clearInterval = () => {
    this.imgServerStatus = false;
    clearInterval(this.interval);
  };
}

export const imgSocket = new ImgSocket();

class PersonBatDistance {
  private client?: WebSocket;
  private serverStatus: boolean;
  private messageListeners: { [key: string]: ((returnValue: any) => void) | null };
  public onmessage?: (data: any) => void;
  public createSocketCallback?: () => void;
  public startReceive: boolean;
  private interval: any;
  private session?: number;

  constructor() {
    // NOSONAR
    this.serverStatus = false;
    this.startReceive = false;
    this.messageListeners = {};
    this.interval = 0;
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    this.initSocket();
  }

  private initSocket = async () => {
    const isNav = await window.systemAPI.getNAV();
    if (!isNav) return;
    setInterval(async () => {
      if ([WebSocket.OPEN, WebSocket.CONNECTING].includes(this.client?.readyState as any)) return;
      await this.createSocket();
    }, 2000);
  };

  private getConnectMessage = (data: any) => {
    const returnValue = JSON.parse(data.data);
    this.session = returnValue.session;
    if (returnValue.id === 10209) {
      if (returnValue.code === 1 && returnValue.msg === 'start') {
        this.serverStatus = true;
      } else {
        this.serverStatus = false;
      }
    }
    if (this.serverStatus && returnValue.id === 10210) {
      Object.values(this.messageListeners)
        .filter(Boolean)
        .forEach(listener => listener?.(returnValue));
    }
  };

  public createSocket = async () => {
    if ([WebSocket.OPEN, WebSocket.CONNECTING].includes(this.client?.readyState as any)) return;
    try {
      const imgSocketUrl = IMAGE_SOCKET_URL();
      sendRenderLog.info('PersonBatDistance-WebSocket创建');
      this.client = new WebSocket(imgSocketConfig.URL || imgSocketUrl);
    } catch (err: any) {
      sendRenderLog.error('PersonBatDistance实例化故障', err);
    }
    if (!this.client) return;
    this.client.onmessage = this.getConnectMessage;
    this.client.onopen = () => {
      this.createSocketCallback?.();
    };
    this.client.onclose = () => {
      this.serverStatus = false;
      sendRenderLog.warn('socket close', this.session);
    };
    this.client.onerror = () => {
      this.serverStatus = false;
      sendRenderLog.warn('socket error', this.session);
    };
  };

  public listenMessage = (key: string, callback: (returnValue: any) => void) => {
    if (this.messageListeners[key]) return;
    this.messageListeners[key] = callback;
  };

  public clearListenMessageByKey = (key: string) => {
    this.messageListeners[key] = null;
  };

  public sendPersonBatMessage = () => {
    if (this.client?.readyState !== WebSocket.OPEN) return;
    this.interval = setInterval(() => {
      if (!connSocket.cameraStatus) {
        this.serverStatus = false;
      }
      if (this.serverStatus || !this.startReceive) return;
      this?.client?.send(JSON.stringify(getDistanceParams));
    }, 2000);
  };

  // 停止接收
  public stopReceivePersonBatMessage = () => {
    this.startReceive = false;
    if (this.client?.readyState !== WebSocket.OPEN) return;
    this?.client?.send(JSON.stringify(stopDistanceParams));
  };

  public clearInterval = () => {
    this.serverStatus = false;
    clearInterval(this.interval);
  };
}

export const personBatDistanceSocket = new PersonBatDistance();
