export const getAge = (strAge: string) => {  // NOSONAR
  let birArr = strAge.split('-');
  let birYear = Number(birArr[0]);
  let birMonth = Number(birArr[1]);
  let birDay = Number(birArr[2]);

  let d = new Date();
  let nowYear = d.getFullYear();
  let nowMonth = d.getMonth() + 1;
  let nowDay = d.getDate();
  let returnAge;

  d = new Date(birYear, birMonth - 1, birDay);
  if (d.getFullYear() === birYear && d.getMonth() + 1 === birMonth && d.getDate() === birDay) {
    if (nowYear === birYear) {
      returnAge = 0; //
    } else {
      let ageDiff = nowYear - birYear; //
      if (ageDiff > 0) {
        if (nowMonth === birMonth) {
          let dayDiff = nowDay - birDay; //
          if (dayDiff < 0) {
            returnAge = ageDiff - 1;
          } else {
            returnAge = ageDiff;
          }
        } else {
          let monthDiff = nowMonth - birMonth; //
          if (monthDiff < 0) {
            returnAge = ageDiff - 1;
          } else {
            returnAge = ageDiff;
          }
        }
      } else {
        return -1;
      }
    }

    return returnAge;
  } else {
    return -1;
  }
};
