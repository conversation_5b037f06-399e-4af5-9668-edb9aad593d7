type ErrorEventCallback = (...args: any[]) => void;

class ErrorEventBus {
  private listeners: Record<string, ErrorEventCallback[]> = {};

  on(event: string, callback: ErrorEventCallback): void {
    if (!this.listeners[event]) {
      this.listeners[event] = [];
    }
    this.listeners[event].push(callback);
  }

  off(event: string, callback: ErrorEventCallback): void {
    if (this.listeners[event]) {
      this.listeners[event] = this.listeners[event].filter(listener => listener !== callback);
    }
  }

  emit(event: string, ...args: any[]): void {
    if (this.listeners[event]) {
      this.listeners[event].forEach(callback => callback(...args));
    }
  }
}

const errorEventBus = new ErrorEventBus();
const timeOutEvent = 'timeOutEvent';
/** 非监控请求URL的list
 * 以下list中的URL不受超时的限制，后续有新的需要添加忽略的URL，放至此处
 */
const omitListenUrlList = [
  '/pro/front/config',
  '/pro/device/info',
  '/pro/device/regist',
  '/pro/device/discard',
  '/pro/plan/log/list',
  '/pro/license/get',
  '/pro/license/import',
  '/pro/plan/report/begin',
  '/pro/plan/report/pause',
  '/pro/plan/report/end',
  '/pro/plan/report/continue',
];

export {
  errorEventBus,
  timeOutEvent,
  omitListenUrlList,
};
