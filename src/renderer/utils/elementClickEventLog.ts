/**
 * @description: 监听页面元素点击事件
 * @author: ruoweng
 * 原理：利用MutationObserver监听页面dom变化，dom变化初始化一份监听器，然后对监听行为写入日志
 * cameraAndCoil 这个组件有相机错误时，会导致页面频繁变化，建议这个组件 修改错误展示方式，变化时，仅修改attribue， 而不是修改页面结构
 */
import _ from 'lodash';
import { sendHistoryLog } from './renderLogger';

let observer: any;
let pathName: string = '';
const config = {
  attributes: false,
  childList: true,
  subtree: true,
};
const whiteClassList = [
  {
    className: 'ng_alert',
    text: '页面alert警告信息',
  },
  {
    className: 'ng_radio',
    text: 'radio或者radioButton',
  },
  {
    className: 'spot_container',
    text: '靶点卡片',
  },
  {
    className: 'importTemplate__name_item',
    text: '导入刺激模板切换模板',
  },
  {
    className: 'stimulateTemplate__name_item',
    text: '刺激模板切换模板',
  },
  {
    className: 'anticon',
    text: 'icon',
  },
];
const domChange = (mutationsList: any[], observerParams: any) => {
  // 检查页面loading 效果
  checkSpinStatus();
  // 检查页面白名单中的对象，监听点击事件
  addWhiteClassClickListener();
  // 检查message 中的警告信息
  checkMessageWarningStatus();
  // 检查系统错误弹窗
  checkErrorModal();
  // 检查系统中icon点击
  addTagIClickListener();
  // 检查button 点击
  addButtonClickListener();
};

const getText = (event: any) => {
  if (event.target.nodeName === 'path' || event.target.nodeName === 'svg') {
    return 'icon';
  } else {
    let targetDom = event.target;
    for (const white of whiteClassList) {
      let target = event.path.slice(0, 6).find((item: any) => {
        return typeof item.className === 'string' && item.className.includes(white.className);
      });
      if (target) {
        targetDom = target;

        break;
      }
    }

    return targetDom ? targetDom.textContent : '--';
  }
};
const sendLog = (params: any) => {
  let { type, event } = params;
  const pathStr = event.path.slice(0, 8).reduce((res: string, path: any) => {
    return `${res} > ${path.className ? path.className : ''}`;
  }, '');
  let text = event.target.textContent;
  if (type === 'custom' && !text) {
    text = getText(event);
  }

  sendHistoryLog.info(`页面路由: ${pathName},操作类型: ${type},关键字：${text} 位置：{screenX:${event.screenX},screenY:${event.screenY}}. class路径: ${pathStr}`);
};
const debounceSendLog = _.debounce(sendLog, 1500, { maxWait: 3, leading: true, trailing: false });
// 页面button 绑定click 事件
const addButtonClickListener = () => {
  // 1. 获取所有页面上的按钮元素
  const buttons = document.querySelectorAll('button');
  // 2. 创建一个函数来处理按钮点击事件
  const handleButtonClick = (event: any) => {
    debounceSendLog({
      type: 'button',
      event: event,
    });
  };

  // 3. 遍历所有按钮并添加点击事件监听器
  buttons.forEach(button => {
    button.addEventListener('click', _.throttle(handleButtonClick, 2000));
  });
};

// 检查页面loading 效果
let isLoading = false;
const checkSpinStatus = () => {
  const spins = document.getElementsByClassName('ant-spin-spinning');
  if (spins.length && !isLoading) {
    isLoading = true;
    sendLog({
      type: 'spin',
      event: {
        screenX: 0,
        screenY: 0,
        path: [],
        target: {
          textContent: 'loading: true',
        },
      },
    });
  } else if (spins.length === 0 && isLoading) {
    sendLog({
      type: 'spin',
      event: {
        screenX: 0,
        screenY: 0,
        path: [],
        target: {
          textContent: 'loading: false',
        },
      },
    });
    isLoading = false;
  }
};

// 检查页面中 有message.warning  message.error
// ant-message-custom-content ant-message-warning
let isWriteOver = false;
const checkMessageWarningStatus = () => {
  const messageList = document.getElementsByClassName('ant-message-warning');
  if (messageList.length === 1 && !isWriteOver) {
    isWriteOver = true;
    sendLog({
      type: '警告弹窗：打开',
      event: {
        screenX: 0,
        screenY: 0,
        path: [],
        target: {
          textContent: messageList[0].textContent || '--',
        },
      },
    });
  } else if (messageList.length === 0 && isWriteOver) {
    sendLog({
      type: '警告弹窗：关闭',
      event: {
        screenX: 0,
        screenY: 0,
        path: [],
        target: {
          textContent: '关闭',
        },
      },
    });
    isWriteOver = false;
  }
};

// 检查系统错误弹窗
let isErrorModalWriteOver = false;
const checkErrorModal = () => {
  const errorModalEle = document.querySelectorAll('[class*="__errorModel"]');
  if (errorModalEle.length === 1 && !isErrorModalWriteOver) {
    const title = Array.from(errorModalEle[0].children).find((item: any) => item.className.includes('errorTitle'));
    const titleText = title ? title.textContent : '--';
    const errList = Array.from(errorModalEle[0].children).find((item: any) => item.className.includes('errList'));
    const errListText = errList
      ? Array.from(errList.children)
        .map(ele => ele.textContent)
        .join('，')
      : '--';
    isErrorModalWriteOver = true;
    sendLog({
      type: 'errorModal：打开',
      event: {
        screenX: 0,
        screenY: 0,
        path: [],
        target: {
          textContent: `${titleText}：${errListText}`,
        },
      },
    });
  } else if (errorModalEle.length === 0 && isErrorModalWriteOver) {
    sendLog({
      type: 'errorModal：关闭',
      event: {
        screenX: 0,
        screenY: 0,
        path: [],
        target: {
          textContent: '关闭',
        },
      },
    });
    isErrorModalWriteOver = false;
  }
};
// 利用类 白名单 追踪对应位置点击
const addWhiteClassClickListener = () => {
  // 1. 获取所有页面上的按钮元素
  let eleList = whiteClassList.reduce((result: any[], item: any) => {
    return [...result, ...Array.from(document.querySelectorAll(`[class*="${item.className}"]`))];
  }, []);
  const handleClick = (event: any) => {
    debounceSendLog({
      type: 'custom',
      event: event,
    });
  };
  // 3. 遍历所有按钮并添加点击事件监听器
  eleList.forEach(ele => {
    ele.addEventListener('click', _.throttle(handleClick, 2000));
  });
};

const addTagIClickListener = () => {
  // 1. 获取所有页面上的按钮元素
  const icons = document.getElementsByTagName('i');
  const handleClick = (event: any) => {
    debounceSendLog({
      type: 'icon',
      event: event,
    });
  };
  // 3. 遍历所有按钮并添加点击事件监听器
  Array.from(icons).forEach(icon => {
    icon.addEventListener('click', _.throttle(handleClick, 2000));
  });
};
export const initObserver = (pathname: string) => {
  destroyObserver();
  pathName = pathname;
  sendHistoryLog.info(`页面切换：${pathname}`);
  observer = new MutationObserver((mutationList: any, obs: any) => {
    domChange(mutationList, obs);
  });
  const targetNode = document.body;
  observer.observe(targetNode, config);
};
const destroyObserver = () => {
  if (observer) {
    observer.disconnect();
  }
};
