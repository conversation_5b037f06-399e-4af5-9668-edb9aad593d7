import { app, BrowserWindow, crashReporter, powerSaveBlocker } from 'electron';
import * as path from 'path';
// eslint-disable-next-line import/no-internal-modules
import * as remoteMain from '@electron/remote/main';
import { setAppMenu } from './appMenu';
import { getSetting, setDevtoolsIsOpened, setDirName, setLoginVersion, shouldShowDevtoolMenu } from '@/common/lib/env/env';
import { isDev, isProd } from '@/common/lib/env/nodeEnv';
import * as appLifeCycle from './lib/applifecycle/appLifeCycleInspector';
import { getAndStorageEndpointInfo } from './lib/systemOperationManager';
import { registerIpcHandle } from './ipc/ipcHandle';
import { mainLogger } from '../common/lib/appLogs/generateLogger';
import { initPduSocket } from '@/main/pduSocket';
import { errorEventBus, timeOutEvent } from '../renderer/utils/errorEventBus';
import { GET_TIMEOUT_ERROR } from '../common/ipc/ipcChannels';
import { crashed_stop_treat } from './tmsSocket';
import('./systemFault/tmsFaultSocket');

app.commandLine.appendSwitch('ignore-certificate-errors');
export let mainUiWin: BrowserWindow | null;
const gotTheLock = app.requestSingleInstanceLock();

let suspendId: number;
remoteMain.initialize();
const installExtensions = async () => {
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  const installer = await import('electron-devtools-installer');
  const forceDownload = !!process.env.UPGRADE_EXTENSIONS;
  const extensions = ['REACT_DEVELOPER_TOOLS', 'REDUX_DEVTOOLS'];

  return Promise.all(extensions.map(async name => installer.default(installer[name], forceDownload))).catch(console.log); // eslint-disable-line no-console
};

const loadMainPage = async () => {
  if (!mainUiWin) {
    return;
  }
  errorEventBus.on(timeOutEvent, (timeOut: boolean) => {
    mainUiWin?.webContents?.send(GET_TIMEOUT_ERROR, timeOut);
  });
  if (isDev) {
    process.env.ELECTRON_DISABLE_SECURITY_WARNINGS = '1';
    mainUiWin.loadURL(getSetting().WEB_ORIGIN_URL).catch((err: any) => {
      console.log('production', err); // eslint-disable-line no-console
    });
  } else {
    await mainUiWin.loadFile(path.join(__dirname, 'index.html'));
  }
};

const createWindow = async () => {
  if (isDev) {
    await installExtensions();
  }

  mainUiWin = new BrowserWindow({
    width: 1920,
    height: 1080,
    kiosk: isProd,
    minWidth: 1320,
    minHeight: 768, // titleBarStyle: 'hiddenInset',
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: true,
      webSecurity: false,
      preload: path.join(__dirname, 'preload.js'),
    },
  });

  // DO NOT comment here 不要注释这里，在本地 ./.env.production.local 文件里添加 REACT_APP_SHOW_APP_MENU=show 即可打开调试器
  if (shouldShowDevtoolMenu()) {
    // Open DevTools, see https://github.com/electron/electron/issues/12438 for why we wait for dom-ready
    mainUiWin.webContents.once('dom-ready', () => {
      mainUiWin!.webContents.openDevTools();
      // 监听 Developer Tools 的开启和关闭事件
      mainUiWin!.webContents.on('devtools-opened', () => {
        setDevtoolsIsOpened(true);
      });

      mainUiWin!.webContents.on('devtools-closed', () => {
        setDevtoolsIsOpened(false);
      });
    });
  }

  mainUiWin.on('closed', () => {
    mainUiWin = null;
  });
};

crashReporter.start({ uploadToServer: false });

if (!gotTheLock) {
  app.quit();
} else {
  app.on('ready', async () => {
    try {
      suspendId = powerSaveBlocker.start('prevent-app-suspension');
    } catch (error) {
      console.log(error); // eslint-disable-line no-console
    }
    try {
      await getAndStorageEndpointInfo();
    } catch (e) {
      mainLogger.info(e);
    }
    await setAppMenu();
    registerIpcHandle(); // 注册所有的ipc回调
    await createWindow();
    await loadMainPage();
    remoteMain.enable(mainUiWin!.webContents);
    if (isProd) {
      initPduSocket();
    }
    // eslint-disable-next-line @typescript-eslint/no-var-requires
    setLoginVersion(require('../../package').loginVersion);
    setDirName(`${__dirname}/`);
  });

  app.on('second-instance', (pushDownloadLogStatus: Event, commandLine: string[], workingDirectory: string) => {
    // 当运行第二个实例时,将会聚焦到myWindow这个窗口
    if (mainUiWin) {
      if (mainUiWin.isMinimized()) mainUiWin.restore();
      mainUiWin.focus();
    }
  });

  app.on('window-all-closed', () => {
    app.quit();
  });

  app.on('will-quit', (event: Electron.Event) => {
    if (!appLifeCycle.isReadyToQuit()) {
      // prevent quit, do prepare, then quit.
      event.preventDefault();
      appLifeCycle
        .onAppWillQuit()
        .then(() => {
          if (!isNaN(suspendId)) {
            powerSaveBlocker.stop(suspendId);
          }
          app.quit();
        })
        .catch(() => {
          app.quit();
        });
    }
  });

  app.on('activate', async () => {
    if (mainUiWin === null) {
      await createWindow();
    }
  });
}

app.on('render-process-gone', (_, content, details) => {
  mainLogger.error('渲染进程崩溃:', details.reason);
  crashed_stop_treat();
});
app.on('gpu-process-crashed', (event, kill) => {
  mainLogger.error('app:gpu-process-crashed', event, kill);
});
app.on('child-process-gone', (event, details) => {
  mainLogger.error('app:child-process-gone', event, details);
});

process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
app.commandLine.appendSwitch('ignore-certificate-errors');
