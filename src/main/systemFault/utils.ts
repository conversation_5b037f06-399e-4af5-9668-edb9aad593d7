import { ipcMain } from 'electron';
import { mainUiWin } from '../main';

const getPrivateKey = (key: string) => {
  return `private-${key}-${Date.now()}`;
};

export const callRenderer = async (key: string) => {
  return new Promise(res => {
    const nextKey = getPrivateKey(key);
    mainUiWin?.webContents.send(key, nextKey);

    ipcMain.once(nextKey, (_, val: any) => {
      res(val);
    });
  });
};

/**
 * 错误跳过方法
 * @param maxErrorCount 最大跳过次数
 * @returns cb
 */
export const skipError = (maxErrorCount: number) => {
  let index = 0;
  const max = maxErrorCount;
  let needSkip = true;

  return (cb: () => void, isError: boolean) => {
    if (needSkip) {
      if (max <= ++index || !isError) {
        needSkip = false;
      } else {
        return;
      }
    }

    cb();
  };
};
