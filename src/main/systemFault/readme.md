|   key    |                     触发页面                      |                                                                    异常条件                                                                    |                    清除条件                    |
| :------: | :-----------------------------------------------: | :--------------------------------------------------------------------------------------------------------------------------------------------: | :--------------------------------------------: |
| 0A010001 |                       login                       |                                                       front/config/list 请求 100 次失败                                                        |           front/config/list 请求成功           |
| 0A010002 |                  有、无导航治疗                   |           /plan/report/begin、/plan/report/pause、plan/report/continue 请求失败&&errorcode 不在[30223, 30224, 30225, 30228, 30229]中           |                点击弹窗我知道了                |
| 0A010003 | login、顶部存在关机按钮页面、有导航治疗、技术支持 |                                                  /metrics 中存在 size - avail / size 小于 80%                                                  |                 不存在异常条件                 |
| 0A010004 |                       login                       |                                     详情查看https://mqhfidmks7.feishu.cn/wiki/WmFbwHNDliM1cjkInvmcJxqmnx0                                      |
| 0A010005 |                       login                       |                                                                      同上                                                                      |
| 0A020001 |                     main 进程                     |                                                      pdu 未连接、pdu_master.result 不为 0                                                      |    pdu_master.result 为 0 \|\| pdu 收到消息    |
| 0A020002 | main 进程 | push state bit 2 为 0 | push state bit 2 为1 |
| 0A030001 |                     main 进程                     |                                     tms device_status tms_master.result !== 0 \|\| tms_slave.result !== 0                                      |  tms_master.result & tms_slave.result 都为 0 & render进程等待队列消费完成   |
| 0A030002 |                     main 进程                     |                                                                     fault                                                                      |                     fault                      |
| 0A030003 |                     main 进程                     |                                                                     fault                                                                      |                     fault                      |
| 0A030004 |                     main 进程                     |                                                                     fault                                                                      |                     fault                      |
| 0A030005 |         无导航治疗、有导航治疗、重复刺激          | start_treatment code === 0 && result === 0 && query_treatment.data.count < pulse_total && query_treatment.data.state === 0 && 页面状态不为结束 |     一次性错误，初始化或者点击我知道了清除     |
| 0A040001 |                     main 进程                     |                                                                     fault                                                                      |                     fault                      |
| 0A040002 |                全局轮训 coil_query                |                                                   data?.type.trim()不在['CBF-03', 'CB-03']中                                                   |    data?.type.trim()在['CBF-03', 'CB-03']中    |
| 0A040003 |                全局轮训 coil_query                |                                                               data.sn 不存在或空                                                               |                  data.sn 存在                  |
| 0A040004 |                     main 进程                     |                                                                     fault                                                                      |                     fault                      |
| 0A040005 |                     main 进程                     |                                                                     fault                                                                      |                     fault                      |
| 0A040006 |                     main 进程                     |                                                                     fault                                                                      |                     fault                      |
| 0A040007 |                     main 进程                     |                                                                     fault                                                                      |                     fault                      |
| 0A040008 |                     main 进程                     |                                                                     fault                                                                      |                     fault                      |
| 0A040009 |                     main 进程                     |                                                                     fault                                                                      |                     fault                      |
| 0A040010 |                     main 进程                     |                                                                     fault                                                                      |                     fault                      |
| 0A040011 |                     main 进程                     |                                                                     fault                                                                      |                     fault                      |
| 0A040012 |                     main 进程                     |                                                                     fault                                                                      |                     fault                      |
| 0A040013 |                     main 进程                     |                                                                     fault                                                                      |                     fault                      |
| 0A04000A |                     main 进程                     |                                                                     fault                                                                      |                     fault                      |
| 0A04000B |                全局轮训 coil_query                |                                                 data?.production_date 未超过五年 && 小于三个月                                                 | data?.production_date 未超过五年 && 大于三个月 |
| 0A04000C |                全局轮训 coil_query                |                                                         data?.production_date 超过五年                                                         |        data?.production_date 未超过五年        |
| 0A04001D |                全局轮训 coil_query                |                                                          data?.life > 0 && <= 100000                                                           |          data?.life > 0 && <= 100000           |
| 0A04000E |                全局轮训 coil_query                |                                                                data?.life <= 0                                                                 |                 data?.life > 0                 |
| 0A04000F | main进程 | fault | fault |
| 0A060001 |                     main 进程                     |                                                                     fault                                                                      |                     fault                      |
| 0A060002 |                     main 进程                     |                                                                     fault                                                                      |                     fault                      |
| 0A060003 |                     main 进程                     |                                                                     fault                                                                      |                     fault                      |
| 0A060004 |                     main 进程                     |                                                                     fault                                                                      |                     fault                      |
| 0A060005 |                     main 进程                     |                                                                     fault                                                                      |                     fault                      |
| 0A060006 |                     main 进程                     |                                                                     fault                                                                      |                     fault                      |
| 0A060007 |                     main 进程                     |                                                                     fault                                                                      |                     fault                      |
| 0A060008 |                     main 进程                     |                                                                     fault                                                                      |                     fault                      |
| 0A060009 |                     main 进程                     |                                                                     fault                                                                      |                     fault                      |
| 0A060011 |                     main 进程                     |                                                                     fault                                                                      |                     fault                      |
| 0A060012 |                     main 进程                     |                                                                     fault                                                                      |                     fault                      |
| 0A060013 |                     main 进程                     |                                                                     fault                                                                      |                     fault                      |
| 0A060014 |                     main 进程                     |                                                                     fault                                                                      |                     fault                      |
| 0A060015 |                     main 进程                     |                                                                     fault                                                                      |                     fault                      |
| 0A060016 |                     main 进程                     |                                                                     fault                                                                      |                     fault                      |
| 0A080001 |                   顶部相机组件                    |                                                           11307 socket 状态不为 open                                                           |               socket 状态为 open               |
| 0A080002 |                   顶部相机组件                    |                                                  11307  camerapst !== 1 &&不为104                                                |                camerapst !== 1  && 为104      \|\|   camerapst === 1       |
|0A080002 | 顶部相机组 | camerapst === 104 | camerapst === 1 \|\| camerapst !== 104 

