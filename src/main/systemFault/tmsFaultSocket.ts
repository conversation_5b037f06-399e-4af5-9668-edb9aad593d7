import { handleTms<PERSON><PERSON><PERSON><PERSON><PERSON>, onSelfMainFaultListen } from '.';
import { DEVICE_STATUS } from '../../common/constant/tms';
import { baseSocketLog, systemFaultLog } from '../../common/lib/appLogs/generateLogger';
import { BaseSocket } from '../../common/lib/baseSocket';
import { SocketConfig, SocketStatus, TargetEnum } from '../../common/lib/baseSocket/type';
import { getSetting } from '../../common/lib/env/env';
import { FaultKeyEnum, FaultStatusEnum } from '../../common/systemFault/type';
import { getTmsSocketReadyState } from '../tmsSocket';
import { skipError } from './utils';

const MAX_CONNECT_SOCKET_SKIP = 30;
const MAX_DEVICE_STATUS_SKIP = 5;

/** 首次链接兜底 */
const skipConnect = skipError(MAX_CONNECT_SOCKET_SKIP);
/** 首次TmsFault兜底 */
const skipDeviceStatus = skipError(MAX_DEVICE_STATUS_SKIP);

const handleHeartBeatFunc = async (socket: BaseSocket) => {
  try {
    let fault_map: {
      [key in FaultKeyEnum]?: FaultStatusEnum;
    } = {};
    let is_error = false;

    const res = await socket.send({ action: DEVICE_STATUS });
    if (res?.data?.tms_master.result === FaultStatusEnum.normal && res?.data?.tms_slave.result === FaultStatusEnum.normal) {
      if (getTmsSocketReadyState() === SocketStatus.OPEN) fault_map = { ...fault_map, [FaultKeyEnum.A030001]: FaultStatusEnum.normal };
    } else {
      fault_map = { ...fault_map, [FaultKeyEnum.A030001]: FaultStatusEnum.abnormal };
      is_error = true;
    }
    skipDeviceStatus(() => onSelfMainFaultListen(fault_map, `DEVICE_STATUS判断tms&pdu, 返回状态为: ${JSON.stringify(res?.data || {})}`), is_error);
  } catch (error) {
    const e = (error || {}) as any;
    let res = e;

    if (!(e instanceof Error)) {
      res = JSON.stringify(e);
    }
    skipDeviceStatus(() => onSelfMainFaultListen({ [FaultKeyEnum.A030001]: FaultStatusEnum.abnormal }, `tms DEVICE_STATUS异常, fault: ${res}`), true);
  }
};

const handleSocketClosed = () => {
  skipConnect(() => onSelfMainFaultListen({ [FaultKeyEnum.A030001]: FaultStatusEnum.abnormal }, 'device gateway 通信异常'), true);
};

const tmsFaultOpen = () => {
  skipConnect(() => {
    systemFaultLog.info('fault tms open');
  }, false);
  tmsFaultSocket.on('fault', (val: any) => {
    handleTmsFaultChange(val);
  });
};

const config: SocketConfig = {
  socketUrl: getSetting().TMS_SOCKET_URL,
  timeoutSecond: 1,
  environment: TargetEnum.NODE,
  callBackIdKey: {
    messageIdKey: 'index',
    eventIdKey: 'action',
    isUnion: true,
  },
  logOptions: {
    fn: (log: string) => baseSocketLog.info(log),
    includeAll: true,
    prefix: 'tmsFault-main',
  },
  heartBeatSecond: 1,
  heartBeatFunc: handleHeartBeatFunc,
};

export const tmsFaultSocket = new BaseSocket(config);

tmsFaultSocket.onopen(tmsFaultOpen);
tmsFaultSocket.onclose(handleSocketClosed);
