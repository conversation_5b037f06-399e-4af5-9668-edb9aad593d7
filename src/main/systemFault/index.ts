import { addUnKnowFault2Map, faultMapConfig, tmsSocketFault } from '../../common/systemFault/config';
import {
  FaultMapItemType,
  Fault2RenderMapType,
  FaultLevelEnum,
  FaultStatusEnum,
  FaultKeyEnum,
  Fault2RenderItemType,
} from '../../common/systemFault/type';
import { mainUiWin } from '../main';
import { GET_SYSTEM_FAULT } from '../../common/ipc/ipcChannels';
import { systemExceptionLogger, systemFaultLog } from '../../common/lib/appLogs/generateLogger';
import { PDUEmergencyButtonStatusEnum, pduSocket } from '../pduSocket';

const faultInfo = new Map<string, Fault2RenderItemType>();

/** 解析tmsFault数据到全量 */
const parseTmsFault = (faultList: string[]) => {
  const temp = { ...tmsSocketFault };
  faultList.forEach(v => {
    const key = v.replace('0x', '');
    addUnKnowFault2Map(key);
    temp[key] = FaultStatusEnum.abnormal;
  });

  return temp;
};

/**
 * tmsFault回调
 * @param data fault 返回data
 */
export const handleTmsFaultChange = (data: any) => {
  const tmsStatus = parseTmsFault(data.data.error_code);

  parseFault(tmsStatus, JSON.stringify(data.data));
};

/**
 * 获取main中self错误
 * @param info 信息
 * @param sourceVal 理由
 */
export const onSelfMainFaultListen = (
  info: {
    [key in FaultKeyEnum]?: FaultStatusEnum;
  },
  sourceVal: string
) => {
  parseFault(info, sourceVal);
};

/**
 * 获取self报错
 * @param _ **
 * @param info 信息
 * @param sourceVal 理由
 */
export const onSelfFaultListen = (
  _: any,
  info: {
    [x: string]: FaultStatusEnum;
  },
  sourceVal: string
) => {
  parseFault(info, sourceVal);
};

/**
 * 解析fault数据
 * @param info 需要的数据
 * @param sourceVal 需要计入日志数据 如果没有记录info
 */
const parseFault = (
  info: {
    [x: string]: FaultStatusEnum;
  },
  sourceVal?: string
) => {
  const additions: FaultMapItemType[] = [];
  let isChanged = false;
  Object.keys(faultMapConfig).forEach(v => {
    if (info[v] === undefined) return;
    const nextStatus = info[v];
    const curStatus = faultInfo.get(v) ? FaultStatusEnum.abnormal : FaultStatusEnum.normal;
    // 有变化
    if (nextStatus ^ curStatus) {
      isChanged = true;
      if (nextStatus) {
        const now = Date.now();
        let suffix = '';
        if (v === FaultKeyEnum.A030001 && pduSocket?.status === PDUEmergencyButtonStatusEnum.unusual) {
          suffix = '（急停）';
        }
        faultInfo.set(v, { ...faultMapConfig[v], createAt: now, suffix });
        additions.push({ ...faultMapConfig[v], createAt: now, suffix });
      } else {
        faultInfo.delete(v);
      }
    }
  });

  if (isChanged) {
    sendFault2Render();
    // 计入日志sourceVal
    systemFaultLog.info(sourceVal);
  }

  if (additions.length) {
    additions.forEach(v => systemExceptionLogger.info(JSON.stringify(v)));
  }
};

/** 解析错误给render */
export const parse2RenderFault = () => {
  const ans: Fault2RenderMapType = {
    [FaultLevelEnum.warning]: [],
    [FaultLevelEnum.error]: [],
  };
  faultInfo.forEach(val => {
    // 警告不区分来源
    if (val.errorLevel === FaultLevelEnum.warning) {
      ans[val.errorLevel] = [...ans[val.errorLevel], val];
    } else if (val.errorLevel & FaultLevelEnum.error) {
      // flatten错误
      ans[FaultLevelEnum.error] = [...ans[FaultLevelEnum.error], val];
    }
  });

  return ans;
};

/** 发送fault给render */
export const sendFault2Render = () => {
  const ans = parse2RenderFault();

  mainUiWin?.webContents.send(GET_SYSTEM_FAULT, ans);

  return ans;
};
