## pdu工作原理
> 1. 前端socket client <=> socket server (1199/t/pdu) <=> tunnel 服务  <=>  电源固件板子

## 如果一直连接补上  socket server 怎么办？
> 1. close 标记如果true， 4秒钟一次 重新连接

## 如果socket server 链接成功，但是后续tunnel 服务挂了呢？
> 1. 此时socket server 将停止给 socket client发送信息
> 2. 如果60秒没有检查到信息，则认为其断了，日志系统 推送 0A020001
> 3. 如果后续 tunnel 链接上，则 socket server 会继续给发送信息，socket client接收到消息后， 移除0A020001

## 如何关机
> 1. 发送action：close 后，socket client接受到 close 返回后，执行 shutdown
> 2. 如果发送了，但是没有返回，则 不执行  shutdown
> 3. 如果发送了，返回了，但是返回的信息 是 502，不 执行 shutdown

## 怎么判断急停
> 1. 急停码： 62720
> 2. 急停抬起但是没上电： 62721
> 3. 正常码： 63233
