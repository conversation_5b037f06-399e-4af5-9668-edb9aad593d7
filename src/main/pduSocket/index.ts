import { execSync } from 'child_process';
import { baseSocketLog, deviceStatusLog, historyLogger } from '../../common/lib/appLogs/generateLogger';
import { BaseSocket } from '../../common/lib/baseSocket';
import { TargetEnum } from '../../common/lib/baseSocket/type';
import { getSetting } from '../../common/lib/env/env';
import { mainUiWin } from '../main';
import { onSelfMainFaultListen } from '../systemFault';
import { FaultKeyEnum, FaultStatusEnum } from '../../common/systemFault/type';
import { DEVICE_STATUS } from '../../common/constant/tms';

export enum PDUEmergencyButtonStatusEnum {
  closed = 0, // 关闭状态
  unusual = 1, // 按下
  usual = 2, // 正常
}

// pdu 位判断1为正常0为异常
/** 急停状态 bit 位 */
const EMERGENCY_BIT = 1 << 0;
/** pc usb 链接状态 bit 位 */
const PC_USB_BIT = 1 << 2;

class PDUSocket {
  public status: PDUEmergencyButtonStatusEnum;
  public socket: BaseSocket | undefined;
  private resetFirstConnect: number = 8;
  constructor() {
    const config = {
      socketUrl: getSetting().PDU_SOCKET_URL,
      timeoutSecond: 3,
      environment: TargetEnum.NODE,
      callBackIdKey: {
        messageIdKey: 'index',
        eventIdKey: 'action',
        isUnion: true,
      },
      logOptions: {
        fn: (log: string) => baseSocketLog.info(log),
        includeAll: true,
        prefix: 'pdu-main',
      },
      heartBeatSecond: 1,
      heartBeatFunc: this.heartFunc,
    };
    this.status = PDUEmergencyButtonStatusEnum.closed;
    this.socket = new BaseSocket(config);
    this.socket.listenMessage(this.handleMessage);
    this.socket.onopen(this.onOpenFn);
    this.socket.onclose(this.onClosed);
  }

  private onOpenFn = () => {
    this.status = PDUEmergencyButtonStatusEnum.usual;
    this.socket?.on('push', this.listenPush);
    onSelfMainFaultListen({ '0A020001': FaultStatusEnum.normal }, 'pdu链接，清除pdu异常');
    this.resetFirstConnect = -1;
    // this.ListenPush();
  };

  private onClosed = () => {
    // 第一次容忍
    if (this.resetFirstConnect >= 0) {
      this.resetFirstConnect--;

      return;
    }
    onSelfMainFaultListen({ '0A020001': FaultStatusEnum.abnormal }, 'pdu断开');
    this.status = PDUEmergencyButtonStatusEnum.closed;
  };
  /** 收到消息 */
  private handleMessage = (data: any) => {
    //
  };

  /** 监听push命令 */
  private listenPush = (returnValue: any) => {
    const { state } = returnValue.data;
    if (!(state & EMERGENCY_BIT)) {
      // 急停, 发给render
      if (this.status !== PDUEmergencyButtonStatusEnum.unusual) {
        historyLogger.info('急停按下');
        this.status = PDUEmergencyButtonStatusEnum.unusual;
        mainUiWin?.webContents.send('GET_SAFETY_BTN', { isDown: true });
      }
    }

    if (state & PC_USB_BIT) {
      onSelfMainFaultListen({ '0A020002': FaultStatusEnum.normal }, `pdu push 判断usb正常, 结果为: ${JSON.stringify(returnValue || {})}`);
    } else {
      onSelfMainFaultListen({ '0A020002': FaultStatusEnum.abnormal }, `pdu push 判断usb异常, 结果为: ${JSON.stringify(returnValue || {})}`);
    }
  };

  private heartFunc = async (socket: BaseSocket) => {
    let log = '';
    try {
      let fault_map: {
        [key in FaultKeyEnum]?: FaultStatusEnum;
      } = {};

      const res = await socket.send({ action: DEVICE_STATUS });
      log = JSON.stringify(res);
      if (res?.data?.pdu_master.result === FaultStatusEnum.normal) {
        fault_map = { ...fault_map, [FaultKeyEnum.A020001]: FaultStatusEnum.normal };
      } else {
        fault_map = { ...fault_map, [FaultKeyEnum.A020001]: FaultStatusEnum.abnormal };
      }
      onSelfMainFaultListen(fault_map, `DEVICE_STATUS判断tms&pdu, 返回状态为: ${JSON.stringify(res?.data || {})}`);
    } catch (error) {
      const e = (error || {}) as any;
      let res = e;

      if (!(e instanceof Error)) {
        res = JSON.stringify(e);
      }
      log = res;

      onSelfMainFaultListen({ [FaultKeyEnum.A020001]: FaultStatusEnum.abnormal }, `DEVICE_STATUS异常, fault: ${res}`);
    } finally {
      deviceStatusLog.info(log);
    }
  };

  public sendPduInfoToRender = async () => {
    return (await this.socket?.send({ action: 'info' })).data;
  };

  public powerOutage = async () => {
    console.log('发送了关机命令', '------'); // eslint-disable-line no-console
    const res = await this.socket?.send({
      action: 'close',
      zone: 1,
    });
    if (res.data.result === 0) {
      execSync('sudo shutdown -h now'); // NOSONAR
    } else {
      mainUiWin?.webContents.send('GET_SAFETY_BTN', { pduError: true });
    }
  };
}

export let pduSocket: PDUSocket | undefined;

export const initPduSocket = () => {
  pduSocket = new PDUSocket();
};
