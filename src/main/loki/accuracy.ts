import fs from 'fs';
import http from 'http';
import { paramsType } from './lokiLog';
import { mainUiWin } from '../main';
import path from 'path';
import checkDiskSpace from 'check-disk-space';
import { getSetting } from '../../common/lib/env/env';
import moment from 'moment';

let fileStream: fs.WriteStream | undefined;

export const downloadAccuracy = async (params: paramsType) => {
  breakAccuracyStream();

  return new Promise(async (res, rej) => {
    const { free } = await checkDiskSpace(params.path);
    let timer = 0;
    const setting = getSetting();
    const requstUrl = `${setting.LOG_API_BASEURL}/api/v1/report/zip/${params.allTidString}/${setting.REPORT_PASSWORD}`;

    http
      .get(requstUrl, response => {
        if (response.headers['content-type'] !== 'application/octet-stream') {
          rej('');
          mainUiWin?.webContents.send('GET_ZIP_PROGRESS', {
            isZip: false,
            proportion: 0,
            total: 0,
            uFree: free,
            isErr: true,
          });

          return;
        }
        fileStream = fs.createWriteStream(path.join(params.path, `./accuracy-${moment().format('YYYY-MM-DD-HH-mm-ss')}.zip`));

        let len = 0;
        const allLen = parseInt(response.headers['content-length']!, 10);
        mainUiWin?.webContents.send('GET_ZIP_PROGRESS', {
          isZip: true,
          proportion: 0,
          total: allLen,
          uFree: free,
          isErr: false,
        });

        response.on('data', chunk => {
          len += chunk.length;
          if (Date.now() - timer < 1000) return;
          timer = Date.now();
          mainUiWin?.webContents.send('GET_ZIP_PROGRESS', {
            isZip: true,
            proportion: (len / allLen) * 100,
            total: allLen,
            uFree: free,
            isErr: false,
          });
        });
        response.on('end', () => {
          res('success');
          breakAccuracyStream();
          mainUiWin?.webContents.send('GET_ZIP_PROGRESS', {
            isZip: false,
            proportion: 100,
            total: allLen,
            uFree: free,
            isErr: false,
          });
        });
        response.on('error', () => {
          breakAccuracyStream();
          mainUiWin?.webContents.send('GET_ZIP_PROGRESS', {
            isZip: false,
            proportion: 0,
            total: allLen,
            uFree: free,
            isErr: true,
          });
          rej('');
        });
        response.pipe(fileStream);
      })
      .on('error', err => {
        breakAccuracyStream();
        mainUiWin?.webContents.send('GET_ZIP_PROGRESS', {
          isZip: false,
          proportion: 0,
          total: 0,
          uFree: free,
          isErr: true,
        });
        rej(err);
      });
  });
};

export const breakAccuracyStream = () => {
  if (fileStream) {
    fileStream.end();
    fileStream = undefined;
  }
};
