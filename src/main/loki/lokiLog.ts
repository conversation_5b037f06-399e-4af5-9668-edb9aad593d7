import dayjs from 'dayjs';
import axios from 'axios';
import { IpcMainInvokeEvent, app } from 'electron';
import fse from 'fs-extra';
import { v4 as uuidv4 } from 'uuid';
import { mainUiWin } from '@/main/main';
import { dateToTimestamp } from '@/renderer/utils';
import { getSetting } from '@/common/lib/env/env';
import checkDiskSpace from 'check-disk-space';
import fs from 'fs';
import { mainLogger } from '../../common/lib/appLogs/generateLogger';
import { SelectTypeEnmu } from '@/renderer/container/manage/components/exportTreatmentReport/config';
import { breakAccuracyStream } from './accuracy';

export type paramsType = {
  isVideo: boolean;
  startTime: any;
  endTime: any;
  path: string;
  type: SelectTypeEnmu;
  startStamp: number;
  endStamp: number;
  allTidString: string;
};
const logPath = app.getPath('userData');
let intervalInstance: any = null;

const backLogPaths = ['/var/log/ngiq-pro-server', '/var/log/mysql', '/var/log/slbs', '/var/log/device-gateway'];

let uuid = '';
const zipUrl = `${getSetting().LOG_API_BASEURL}/api/v1/log/asyn/saveto/uid/`;
/**
 * 给U盘写入日志，调用服务完成
 * @param start
 * @param end
 * @param upath
 * @param isVideo
 */
const zipLog = async (start: any, end: any, upath: string, isVideo: number) => {
  fse.cp(`${logPath}/Crashpad`, `${logPath}/logs`, { recursive: true }, () => {
    /* console.log('复制完成'); */
  });
  const logsPath = backLogPaths;
  logsPath.push(`${logPath}/logs`);
  console.log('所有文件路径', logsPath.join(',')); // eslint-disable-line no-console
  const b64dirs = Buffer.from(logsPath.join(',')).toString('base64');
  let currentTime = dayjs().format('YYYY-MM-DD-HH-mm-ss');
  const b64saveas = Buffer.from(`${upath}/${isVideo ? 'video' : 'text'}-${currentTime}.zip`)
    .toString('base64')
    .replace(/\//g, '_');
  let beg = 0;
  let endt = 0;
  if (isVideo) {
    beg = dayjs(dateToTimestamp(start.$d)).unix();
    endt =
      dayjs(dateToTimestamp(end.$d)).add(86400, 'second').unix() > dayjs().unix()
        ? dayjs().unix()
        : dayjs(dateToTimestamp(end.$d)).add(86400, 'second').unix();
  }
  console.log('压缩开始', beg, endt); // eslint-disable-line no-console
  console.log('压缩存储路径', b64saveas); // eslint-disable-line no-console

  await axios.get(`${zipUrl}${uuid}/${beg}/${endt}/all/NGsupport517/${isVideo}/${b64dirs}/${b64saveas}`);
};

/**
 * 心跳，检查是否压缩完成
 * @param params
 */
const checkIsOver = async () => {
  try {
    let res = await axios.get(`${getSetting().LOG_API_BASEURL}/api/v1/postproc/progress/${uuid}`);
    console.debug('导致日志，检查进度', res.data); // eslint-disable-line no-console
    let proportion = 0;
    if (res.data.code === 100) {
      proportion = res.data.totalsize === 0 ? 0 : (res.data.finishsize / res.data.totalsize) * 100;
    } else if (res.data.code === 1) {
      proportion = 100;
    } else if (res.data.code === 0) {
      throw new Error(res.data.msg);
    }

    return {
      code: res.data.code as number,
      isErr: false,
      proportion: Number.isNaN(proportion) ? 0 : Number.parseInt(proportion.toString(), 10),
      total: res.data.totalsize,
    };
  } catch (e) {
    return {
      code: 0,
      isErr: true,
      proportion: 0,
      total: 0,
    };
  }
};

/**
 * 初始化目录
 */
const initDir = () => {
  if (fse.existsSync(`${logPath}/logs/Crashpad/`)) {
    fse.removeSync(`${logPath}/logs/Crashpad/`);
  }
};

/**
 * loki日志处理主文件，首先获取日志类型，然后循环获取日志，最后结合视频文件等日志导出
 * @param params
 */
export const lokiLog = async (params: paramsType) => {
  try {
    initDir();
    const { free } = await checkDiskSpace(params.path);
    console.log('磁盘剩余空间', free / 1024 / 1024); // eslint-disable-line no-console
    // 写入完成后，开始压缩
    uuid = uuidv4();
    await zipLog(params.startTime, params.endTime, params.path, params.isVideo ? 1 : 0);

    // 每秒轮询一次，直到写入完成
    intervalInstance = setInterval(async () => {
      const isExit = fs.existsSync(params.path);
      let { code, proportion = 0, total } = await checkIsOver();
      if (code === 0 || !isExit) {
        clearInterval(intervalInstance);
        mainUiWin?.webContents.send('GET_ZIP_PROGRESS', {
          isZip: false,
          proportion: 0,
          total: total,
          uFree: free,
          isErr: true,
        });

        return;
      }
      if (code === 1) {
        clearInterval(intervalInstance);
        mainUiWin?.webContents.send('GET_ZIP_PROGRESS', {
          isZip: false,
          proportion: 100,
          total: total,
          uFree: free,
          isErr: false,
        });

        return;
      }
      mainUiWin?.webContents.send('GET_ZIP_PROGRESS', {
        isZip: true,
        proportion,
        total: total,
        uFree: free,
        isErr: false,
      });
    }, 1000);
  } catch (e) {
    mainLogger.error('导出日志错误监听', JSON.stringify(e));
    if (intervalInstance) {
      clearInterval(intervalInstance);
    }
    mainUiWin?.webContents.send('GET_ZIP_PROGRESS', { isZip: false, proportion: 0, total: 0, uFree: 0, isErr: true });
  }
};

export const handleClearProcess = (_: IpcMainInvokeEvent, type: SelectTypeEnmu) => {
  breakAccuracyStream();
  clearInterval(intervalInstance);
};

/*
 * @description: 终止导出日志,render 进程通过 按钮触发，触发后，renderer 下的弹窗关闭。
 * @link: https://neuralgalaxy.pingcode.com/pjm/projects/M200/backlog/64ecc87894c704053beb1ee2
 */
export const stopLokiLog = async () => {
  await axios.get(`${getSetting().LOG_API_BASEURL}/api/v1/postproc/ctrl/${uuid}/0`);
};

export const unmountUSB = async (usbPath: string) => {
  try {
    const base64Path = Buffer.from(usbPath).toString('base64').replace(/\//g, '_');

    const res = await axios.get(`${getSetting().LOG_API_BASEURL}/api/v1/postproc/usb/umount/${base64Path}`);

    return JSON.stringify(res?.data || {});
  } catch (e: any) {
    throw Error('弹出u盘失败');
  }
};
