import i18next, { i18n } from 'i18next';
import path from 'path';
import i18nextBackend from 'i18next-fs-backend';
import { restrictLocale, getLanguage } from '../../common/i18n/locale';
import { app } from 'electron';
import { getStore } from '../../common/lib/storageUtil';

const KEY_USER_SETTING_LANGUAGE = 'lang';
const languages = ['zh', 'en'];
const fallbackLanguage = 'en';
let i18nInstance: i18n;

const i18nextOptions = {
  backend: {
    // path where resources get loaded from
    // loadPath: './src/main/locales/{{lng}}/{{ns}}.json',
    loadPath: (language: string, namespace: string) => path.join(__dirname, 'locales/{{lng}}/{{ns}}.json'),

    // path to post missing resources
    addPath: './src/main/i18n/locales/{{lng}}/{{ns}}.missing.json',

    // jsonIndent to use when storing json files
    jsonIndent: 2,
  },
  interpolation: {
    escapeValue: false,
  },
  saveMissing: true,
  fallbackLng: fallbackLanguage,
  whitelist: languages,
  react: {},
};

export const initI18n = async () => {
  i18next.use(i18nextBackend);
  if (!i18next.isInitialized) {
    // initialize if not already initialized
    await i18next.init(i18nextOptions);

    const l = getStore().get(KEY_USER_SETTING_LANGUAGE) || app.getLocale();
    const locale = restrictLocale(l as string);
    const lang = getLanguage(locale, fallbackLanguage);
    await i18next.changeLanguage(lang);
  }

  return i18next;
};

export const changeLanguage = async (language: string) => {
  const locale = restrictLocale(language);
  const lang = getLanguage(locale, fallbackLanguage);
  await i18next.changeLanguage(lang);

  getStore().set(KEY_USER_SETTING_LANGUAGE, language);
};

export const getI18Next = async () => {
  if (!i18nInstance) {
    await initI18n();
    i18nInstance = i18next;
  }

  return i18nInstance;
};
