import { app, IpcMainInvokeEvent } from 'electron';
import { authenticate, getStoragePath, getUserSession, onReceivedLogoutSignal } from '../lib/userSessionManager';
import fs from 'fs';
import path from 'path';
import crypto from 'crypto';
import { FileInfo } from '@/renderer/uiComponent/NgFileList';
import { LoginParams } from '@/common/types';
import { copy, remove, readFileSync } from 'fs-extra';
import { getAuthApiEndpoint } from '@/common/api/ngApiAgent';
import { getDirName } from '@/common/lib/env/env';
import { isProd } from '@/common/lib/env/nodeEnv';
import os from 'os';
import { renderLogger, historyLogger, treatLogger, systemExceptionLogger, treatMatrixLogger } from '@/common/lib/appLogs/generateLogger';
import { pduSocket } from '../pduSocket';
import { lokiLog, paramsType, stopLokiLog, unmountUSB } from '@/main/loki/lokiLog';
import { checkDiskSmart, checkFileSystem } from '@/main/smart/checkDiskSmart';
import { get, set } from '@/common/lib/storageUtil';
import { LogType } from '../../common/ipc/ipcChannels';
import { SelectTypeEnmu } from '../../renderer/container/manage/components/exportTreatmentReport/config';
import { downloadAccuracy } from '../loki/accuracy';
import { onSelfMainFaultListen } from '../systemFault';
import { FaultStatusEnum } from '../../common/systemFault/type';
let isWritedSize80 = false;
export const handleIPCLogin = async (_: IpcMainInvokeEvent, logDto: LoginParams) => {
  return authenticate(logDto);
};

export const handleIPCLogout = async () => {
  return onReceivedLogoutSignal();
};

export const handleIPCGetUserSession = () => {
  return getUserSession();
};

export const handleGetStoragePath = () => {
  return getStoragePath();
};

export const getProjectInfo = () => {
  let sysPath = app.getPath('userData');
  let productConfigPath = path.join(sysPath, 'product_config');
  try {
    const buffer = fs.readFileSync(productConfigPath, 'utf8');
    // eslint-disable-next-line @typescript-eslint/no-var-requires,@typescript-eslint/no-shadow
    const CryptoJS = require('crypto-js');
    const str = CryptoJS.AES.decrypt(buffer.toString(), 'neuralgalaxy_product').toString(CryptoJS.enc.Utf8);

    return JSON.parse(str);
  } catch (e) {
    throw new Error(`${e}`);
  }
};

/**
 * 获取当前系统的信息
 * @param key 字段名
 */
export const getCurrentProjectName = (key: string) => {
  try {
    return getProjectInfo()[key];
  } catch (e) {
    throw new Error(`${e}`);
  }
};

export const handleGetFolderInfo = async (
  _: IpcMainInvokeEvent,
  { filePath, extList = [] }: { filePath: string; extList: string[] }
): Promise<FileInfo[]> => {
  if (!fs.existsSync(filePath)) {
    throw Error('未检测到移动设备');
  }
  const result = await readFile(filePath);

  // @ts-ignore
  return result?.filter(v => extList.some(item => v?.extname === item) || v?.type === 'directory');
};

const readFile = async (dir: string) => {
  const ignoreFile = ['.DS_Store', '.idea', '.vscode'];
  const readDirs = (dirPath: string) => {
    if (dirPath.startsWith('.')) {
      return;
    }
    let files = fs.readdirSync(dirPath);
    let result = files
      .filter(item => !ignoreFile.includes(item))
      .map(file => {
        try {
          if (file.startsWith('.')) {
            return;
          }
          let subPath = path.join(dirPath, file);
          let stats = fs.statSync(subPath);

          return {
            size: stats.size,
            path: subPath,
            name: file,
            type: stats.isDirectory() ? 'directory' : 'file',
            extname: path.extname(subPath),
          };
        } catch (error) {
          return;
        }
      });

    return result.filter(Boolean);
  };

  return readDirs(dir);
};

export const getFileContent = (_: IpcMainInvokeEvent, filePath: string, options?: { encoding: BufferEncoding; flag?: string } | BufferEncoding) => {
  return readFileSync(filePath, options);
};
export const getFileJson = (_: IpcMainInvokeEvent, filePath: string) => {
  try {
    return readFileSync(filePath).toString('utf-8');
  } catch (e) {
    return {};
  }
};
export const handleUpload = async (_: IpcMainInvokeEvent, filePath: string) => {
  const lastIndex = filePath.lastIndexOf('/') === -1 ? filePath.lastIndexOf('\\') : filePath.lastIndexOf('/');
  const folderName = lastIndex === -1 ? '' : filePath.substring(lastIndex);
  const config = await getAuthApiEndpoint().getConfig({
    group_name: 'init',
    name: 'storagePath',
  });
  const prePath = config.find(v => v.name === 'storagePath')!.value;
  try {
    await copy(filePath, `${prePath}/upload/${folderName}`);
    const info = await getAuthApiEndpoint().getplanDetailByFile(`${prePath}/upload/${folderName}`);

    return info;
  } catch (error: any) {
    throw Error(JSON.stringify(error));
  } finally {
    remove(`${prePath}/upload/${folderName}`)
      .then(res => res)
      .catch(e => {
        console.log(e); // eslint-disable-line no-console
      });
  }
};

export const removeFile = async (_: IpcMainInvokeEvent, filePath: string) => {
  await remove(filePath)
    .then(res => res)
    .catch(e => {
      console.log(e); // eslint-disable-line no-console
    });
};

export const handleSetRenderLog = (_: IpcMainInvokeEvent, type: LogType, log: string) => {
  switch (type) {
    case LogType.debug:
      renderLogger.debug(log);
      break;
    case LogType.info:
      renderLogger.info(log);
      break;
    case LogType.error:
      renderLogger.error(log);
      break;
    case LogType.warn:
      renderLogger.warn(log);
      break;
    default:
      renderLogger.info(log);
      break;
  }
};
export const handleSetHistoryLog = (_: IpcMainInvokeEvent, log: string) => {
  historyLogger.info(log);
};

export const handleSetSystemExceptionLog = (_: IpcMainInvokeEvent, log: string) => {
  systemExceptionLogger.info(log);
};

export const handleSetTreatLog = (_: IpcMainInvokeEvent, log: string) => {
  treatLogger.info(log);
};

export const handleSetTreatMatrixLog = (_: IpcMainInvokeEvent, log: string) => {
  treatMatrixLogger.debug(log);
};

export const handleShutdown = async () => {
  if (isProd && pduSocket) {
    await pduSocket.powerOutage();
  } else {
    process.exit(0);
  }
};
export const handleWriteFile = async (_: IpcMainInvokeEvent, output: string, bufferData: ArrayBuffer) => {
  // 将docxtemplater的blob转为ArrayBuffer
  let buffer = Buffer.from(bufferData);

  return new Promise((resolve, reject) => {
    fs.writeFile(output, buffer, err => {
      if (err) {
        reject(false);
      } else {
        const fileDescriptor = fs.openSync(output, 'a+');
        fs.fsyncSync(fileDescriptor);
        fs.closeSync(fileDescriptor);
        resolve(true);
      }
    });
  });
};

export const checkDisk80Percent = async (_: IpcMainInvokeEvent) => {
  try {
    const diskCapacityList = await checkFileSystem();
    if (typeof diskCapacityList === 'string') {
      throw diskCapacityList;
    }
    const havePercent80 = diskCapacityList.some((cur: any) => {
      return Number(cur.fileSystemUsed) / Number(cur.fileSystemSize) > 0.8;
    });

    if (havePercent80 && !isWritedSize80) {
      onSelfMainFaultListen({ '0A010003': FaultStatusEnum.abnormal }, '判断磁盘80%逻辑出错');
      isWritedSize80 = true;
    } else if (!havePercent80) {
      isWritedSize80 = false;
      onSelfMainFaultListen({ '0A010003': FaultStatusEnum.normal }, '判断磁盘80%逻辑正常');
    }

    return havePercent80;
  } catch (e) {
    return false;
  }
};

export const handleCheckDiskSmart = async (_: IpcMainInvokeEvent) => {
  const diskSmart = await checkDiskSmart();

  return diskSmart;
};
export const handleGetDiskSmart = async (_: IpcMainInvokeEvent) => {
  const diskDetailList = await checkDiskSmart(true);

  return diskDetailList;
};
export const handleGetPduInfo = async (_: IpcMainInvokeEvent) => {
  return pduSocket?.sendPduInfoToRender();
};
export const handleCheckDiskCapacity = async (_: IpcMainInvokeEvent) => {
  const diskCapacity = await checkFileSystem();

  return diskCapacity;
};

export const handleGetOsInfo = (_?: IpcMainInvokeEvent) => {
  const osUserInfo = os.userInfo();

  return osUserInfo;
};

export const getMd5 = (_: IpcMainInvokeEvent, filePath: string) => {
  const buffer = fs.readFileSync(filePath);
  const hash = crypto.createHash('md5');
  hash.update(buffer as unknown as string, 'utf8');
  const md5 = hash.digest('hex');
  /* eslint-disable no-console */
  console.log(md5);

  return md5;
};

export const handleGetDirName = () => {
  return getDirName();
};

export const handleLokiLog = (_: IpcMainInvokeEvent, param: paramsType) => {
  if (param.type === SelectTypeEnmu.log) {
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    downloadAccuracy(param).then().catch();

    return;
  }
  lokiLog(param)
    .then(res => res)
    .catch(e => {
      console.log(e); // eslint-disable-line no-console
    });
};

export const handleUnmountUSB = async (_: IpcMainInvokeEvent, usbPath: string) => {
  const res = await unmountUSB(usbPath);

  return res;
};

// 获取log信息
export const handleGetLogsInfo = async (_: IpcMainInvokeEvent, logPath: string) => {
  return new Promise((resolve, reject) => {
    fs.readFile(logPath, 'utf8', (err, data) => {
      if (err) {
        reject(`读取log path错误：${err}`);

        return;
      }

      const regex = /\{.*?\}/g;

      const jsonStringArray = data.match(regex);

      if (jsonStringArray) {
        const jsonObjectArray = jsonStringArray.map(jsonString => JSON.parse(jsonString));

        resolve(jsonObjectArray);
      } else {
        reject('log 为空');
      }
    });
  });
};
export const handleGetLogPath = async () => {
  return app.getPath('userData');
};

export const handleStopZip = async () => {
  await stopLokiLog();
};
export const handleSetStore = async (_: IpcMainInvokeEvent, key: string, value: string) => {
  set(key, value);
};
export const handleGetStore = async (_: IpcMainInvokeEvent, key: string) => {
  return get(key);
};
export const handleGetEnv = async (_: IpcMainInvokeEvent, key: string) => {
  try {
    return getCurrentProjectName(key);
  } catch (e) {
    return false;
  }
};

export const handleGetProductInfo = async (_: IpcMainInvokeEvent) => {
  try {
    return getProjectInfo();
  } catch (e) {
    return false;
  }
};

export const handleGetNAV = async (_: IpcMainInvokeEvent, key: string) => {
  return process.env.NAV;
};
