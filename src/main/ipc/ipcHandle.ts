import { ipcMain } from 'electron';
import {
  checkDisk80<PERSON><PERSON><PERSON>,
  getFile<PERSON>ontent,
  getFile<PERSON>son,
  getMd5,
  handleCheckDiskCapacity,
  handleCheckDiskSmart,
  handleGetDirName,
  handleGetDiskSmart,
  handleGetEnv,
  handleGetFolderInfo,
  handleGetLogPath,
  handleGetLogsInfo,
  handleGetNAV,
  handleGetOsInfo,
  handleGetPduInfo,
  handleGetProductInfo,
  handleGetStoragePath,
  handleGetStore,
  handleIPCGetUserSession,
  handleIPCLogin,
  handleIPCLogout,
  handleLokiLog,
  handleSetHistoryLog,
  handleSetRenderLog,
  handleSetStore,
  handleSetSystemExceptionLog,
  handleSetTreatLog,
  handleShutdown,
  handleStopZip,
  handleUnmountUSB,
  handleUpload,
  handleWriteFile,
  handleSetTreatMatrixLog,
  removeFile,
} from './listeners';
// eslint-disable-next-line @typescript-eslint/no-var-requires
import {
  AUTO_QUERY_COIL,
  CLEAR_DATA_POOL,
  GET_BAT_DATA,
  GET_DIR_NAME,
  GET_FILE_MD5,
  GET_LOG_PATH,
  GET_LOGS_INFO,
  GET_OS_USER_INFO,
  GET_STORE,
  GET_USER_SESSION,
  LOGIN,
  LOGOUT,
  LOKI_LOG,
  OPEN_FILE_MODAL,
  QUERY_TREATMENT,
  SET_BAT_DATA,
  SET_BEAT_SCREEM,
  SET_RENDER,
  SET_RENDER_LOG,
  SET_STORE,
  SET_TREAT_LOG,
  SET_TREATMENT_LEVEL,
  SET_TREATMENT_PLAN,
  SET_TREATMENT_THRESHOLD,
  SHUTDOWN,
  SYSTEM_EXCEPTION,
  TMS_VERSION,
  TRIGGER_QUERY,
  TRIGGER_SETTING,
  UPLOAD_FILE,
  WRITE_FILE,
  TUNNEL_INFO,
  SET_TREAT_MATRIX_LOG,
  NOIMAGE_TREATMENT_PLAN_START,
  IMAGE_TREATMENT_PLAN_START,
  SET_CLEAR_PROCESS_INTERVAL,
  GET_SYSTEM_FAULT_ONCE,
  PUSH_SYSTEM_FAULT,
  REMOVE_FILE,
} from '@/common/ipc/ipcChannels';
import { getSetting, getLoginVersion } from '@/common/lib/env/env';
import {
  auto_query_coil,
  clear_data_pool,
  get_tms_version,
  handleQueryTrigger,
  handleSetTrigger,
  query_treatment,
  set_beat_screen,
  set_render,
  set_treatment_level,
  set_treatment_plan,
  set_treatment_threshold,
  noImage_treatment_plan_start,
  image_treatment_plan_start,
  get_tunnel_info,
} from '../tmsSocket';
import { getBatData, setBatData } from '@/common/lib/logsUtil';
import { handleClearProcess } from '../loki/lokiLog';
import { onSelfFaultListen, sendFault2Render } from '../systemFault';

export const tmsSocketIpcHandle = () => {
  ipcMain.handle(SET_TREATMENT_PLAN, set_treatment_plan); // 设置治疗参数
  ipcMain.handle(IMAGE_TREATMENT_PLAN_START, image_treatment_plan_start); // 开始有影像治疗
  ipcMain.handle(NOIMAGE_TREATMENT_PLAN_START, noImage_treatment_plan_start); // 开始无影像治疗
  ipcMain.handle(SET_BEAT_SCREEM, set_beat_screen); // 开始治疗
  ipcMain.handle(QUERY_TREATMENT, query_treatment); // 治疗数据查询
  ipcMain.handle(TRIGGER_QUERY, handleQueryTrigger); // 查询 trigger状态
  ipcMain.handle(TRIGGER_SETTING, handleSetTrigger); // 设置 trigger状态
  ipcMain.handle(SET_TREATMENT_THRESHOLD, set_treatment_threshold); // 单次模式设置
  ipcMain.handle(SET_RENDER, set_render); // render告诉main render已经渲染了
  ipcMain.handle(CLEAR_DATA_POOL, clear_data_pool); // render告诉main render已经渲染了
  ipcMain.handle(TMS_VERSION, get_tms_version); // TMS版本查询
  ipcMain.handle(SET_TREATMENT_LEVEL, set_treatment_level); // 设置刺激强度
  ipcMain.handle(TUNNEL_INFO, get_tunnel_info); // tunnel版本查询
};

export const registerIpcHandle = () => {
  ipcMain.handle(LOGIN, handleIPCLogin); // 登录
  ipcMain.handle(GET_USER_SESSION, handleIPCGetUserSession); // 获取session
  ipcMain.handle('GET_STORAGE_PATH', handleGetStoragePath); // 获取StoragePath
  ipcMain.handle(GET_OS_USER_INFO, handleGetOsInfo); // 设置密码
  ipcMain.handle(LOGOUT, handleIPCLogout); // 退登
  ipcMain.handle(OPEN_FILE_MODAL, handleGetFolderInfo); // 读取目录
  ipcMain.handle('GET_FILE', getFileContent); // 读取文件
  ipcMain.handle('GET_FILE_JSON', getFileJson); // 读取文件
  ipcMain.handle(UPLOAD_FILE, handleUpload);
  ipcMain.handle(REMOVE_FILE, removeFile);
  ipcMain.handle(SET_RENDER_LOG, handleSetRenderLog);
  ipcMain.handle('SET_HISTORY_LOG', handleSetHistoryLog);
  ipcMain.handle(SYSTEM_EXCEPTION, handleSetSystemExceptionLog);
  ipcMain.handle(SHUTDOWN, handleShutdown);
  ipcMain.handle('GET_SETTING', getSetting);
  ipcMain.handle('CHECK_DISK_80_PERCENT', checkDisk80Percent);
  ipcMain.handle('GET_LOGIN_VERSION', getLoginVersion);
  ipcMain.handle(WRITE_FILE, handleWriteFile);
  ipcMain.handle(GET_FILE_MD5, getMd5);
  ipcMain.handle(GET_DIR_NAME, handleGetDirName);
  ipcMain.handle('CHECK_DISK_SMART', handleCheckDiskSmart);
  ipcMain.handle('GET_DISK_DETAIL_SMART', handleGetDiskSmart);
  ipcMain.handle('GET_PDU_INFO', handleGetPduInfo);
  ipcMain.handle('CHECK_DISK_CAPACITY', handleCheckDiskCapacity);
  ipcMain.handle(LOKI_LOG, handleLokiLog);
  ipcMain.handle('STOP_ZIP', handleStopZip);
  ipcMain.handle(SET_TREAT_LOG, handleSetTreatLog);
  ipcMain.handle(SET_TREAT_MATRIX_LOG, handleSetTreatMatrixLog);
  ipcMain.handle(AUTO_QUERY_COIL, auto_query_coil);
  ipcMain.handle(GET_LOGS_INFO, handleGetLogsInfo);
  ipcMain.handle(GET_LOG_PATH, handleGetLogPath);
  ipcMain.handle(SET_STORE, handleSetStore);
  ipcMain.handle(GET_STORE, handleGetStore);
  ipcMain.handle('UNMOUNT_USB', handleUnmountUSB);
  ipcMain.handle('GET_ENV', handleGetEnv);
  ipcMain.handle('GET_PRODUCT_INFO', handleGetProductInfo);
  ipcMain.handle('GET_NAV', handleGetNAV);
  ipcMain.handle(SET_BAT_DATA, setBatData);
  ipcMain.handle(GET_BAT_DATA, getBatData);
  ipcMain.handle(SET_CLEAR_PROCESS_INTERVAL, handleClearProcess);
  ipcMain.handle(GET_SYSTEM_FAULT_ONCE, sendFault2Render);
  ipcMain.on(PUSH_SYSTEM_FAULT, onSelfFaultListen);
  tmsSocketIpcHandle();
};
