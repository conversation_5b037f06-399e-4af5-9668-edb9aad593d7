/* istanbul ignore file */
import { Menu, MenuItemConstructorOptions, BrowserWindow } from 'electron';
import { getI18Next } from './i18n/i18next.config';
import { i18n } from 'i18next';
import { shouldShowDevtoolMenu, isOnOSX } from '@/common/lib/env/env';

const getAppMenu = (i18nInstance: i18n): MenuItemConstructorOptions => {
  return {
    label: i18nInstance.t('menuAppName') || '',
    submenu: [
      {
        label: i18nInstance.t('menuServices') || '',
        role: 'services',
        submenu: [],
      },
      {
        type: 'separator',
      },
      {
        label: i18nInstance.t('menuHide') || '',
        accelerator: 'Command+H',
        role: 'hide',
      },
      {
        label: i18nInstance.t('menuHideOthers') || '',
        accelerator: 'Command+Shift+H',
      },
      {
        label: i18nInstance.t('menuShowAll') || '',
        role: 'unhide',
      },
      {
        type: 'separator',
      },
      {
        label: i18nInstance.t('menuQuit') || '',
        accelerator: 'Command+Q',
        click: () => {
          const mainUiWin = BrowserWindow.getFocusedWindow() || BrowserWindow.getAllWindows()[0];
          mainUiWin.close();
        },
      },
    ],
  };
};

const getDevMenu = (): MenuItemConstructorOptions => {
  return {
    label: 'devTools',
    submenu: [
      {
        role: 'toggleDevTools',
      },
      {
        role: 'reload',
      },
      {
        role: 'cut',
      },
      {
        role: 'copy',
      },
      {
        role: 'paste',
      },
      {
        role: 'selectAll',
      },
    ],
  };
};

export const setAppMenu = async () => {
  const i18nInstance = await getI18Next();

  const template: MenuItemConstructorOptions[] = [];

  if (isOnOSX()) {
    template.unshift(getAppMenu(i18nInstance));
  }

  if (shouldShowDevtoolMenu()) {
    template.push(getDevMenu());
  }

  if (template.length !== 0) {
    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
  } else {
    Menu.setApplicationMenu(null);
  }
};
