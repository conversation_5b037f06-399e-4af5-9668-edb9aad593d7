import axios from 'axios';
import { getSetting } from '../../common/lib/env/env';
import { mainLogger } from '@/common/lib/appLogs/generateLogger';
import { MetricsInfo, getMetricsValue, initMetricsInfo } from './utils';
import { onSelfMainFaultListen } from '../systemFault';
import { FaultStatusEnum } from '../../common/systemFault/type';

const initKey = 'smartctl_device';
// raid 相当于将多块磁盘聚合， 相当于 服务，检查服务状态
// 值 为 0 表示正常， 有问题 直接停机
const raidErrorKey = ['node_md_state{state="inactive"}'];
// 这个属性，有问题永远报警，
const raidWarningKey = [
  'node_md_disks{state="failed"}', // 值为 0 正常
];
const commonKeys = [
  'smartctl_device_critical_warning',
  'increase(smartctl_device_media_errors[7d])',
  'increase(smartctl_device_num_err_log_entries[7d])',
  'smartctl_device_status',
  'node_filesystem_avail_bytes{fstype="ext4"}',
  'node_filesystem_size_bytes{fstype="ext4"}',
];
const sataKeys = [
  'smartctl_device_attribute{attribute_name="Reallocated_Sector_Ct",attribute_value_type="thresh"}',
  'smartctl_device_attribute{attribute_name="Reallocated_Sector_Ct",attribute_value_type="raw"}',
];
const nvmeKeys = ['smartctl_device_available_spare_threshold', 'smartctl_device_available_spare'];

const NODEEXPORTURL = `${getSetting().CHECK_NODE_EXPORT_BASEURL}/metrics`;
const SMARTURL = `${getSetting().CHECK_SMART_BASEURL}/metrics`;
type deviceType = {
  name: string;
  type: string;
  isAvailableSpare: boolean;
  isCriticalWarning: boolean;
  // isIncreaseMediaErrors: boolean;
  // isIncreaseErrLogEntries: boolean;
  isStatus: boolean;
  isReallocatedSector: boolean;
  fileSystemAvail?: number;
  fileSystemSize?: number;
  fileSystemUsed?: number;
};
type detailType = {
  name: string;
  type: string;
  status: string;
};

enum StatusType {
  error = 0,
  warn = 1,
  success = 3,
}

let devicesPublic: deviceType[] = [];
export const checkDiskSmart = async (backDetail?: boolean): Promise<string | detailType[]> => {
  let smartInfo: MetricsInfo;
  let nodeExportInfo: MetricsInfo;
  try {
    smartInfo = initMetricsInfo(await axios.get(SMARTURL));
    nodeExportInfo = initMetricsInfo(await axios.get(NODEEXPORTURL));
  } catch (e) {
    mainLogger.error('检查磁盘服务错误', JSON.stringify(e));

    return 'service-error';
  }
  const smartctlDeviceList = getMetricsValue(initKey, smartInfo);
  let devices: deviceType[] = smartctlDeviceList.map(item => {
    return {
      name: item.conditions.device,
      type: item.conditions.interface,
      isAvailableSpare: false,
      isCriticalWarning: false,
      isIncreaseMediaErrors: false,
      isIncreaseErrLogEntries: false,
      isStatus: false,
      isReallocatedSector: false,
      fileSystemAvail: 0,
      fileSystemSize: 0,
      fileSystemUsed: 0,
    };
  });
  const nvmeStatus = await checkNvmeSmart(smartInfo);
  const commonStatus = await checkCommonSmart(smartInfo);
  const sataStatus = await checkSataSmart(smartInfo);

  devicesPublic = devices.map((item: deviceType) => {
    return {
      name: item.name,
      type: item.type,
      isAvailableSpare: item.type === 'nvme' ? nvmeStatus.find(item2 => item2.name === item.name)!.isError : false,
      isCriticalWarning: commonStatus.find((item2: any) => item2.name === item.name)!.isCriticalWarning,
      // isIncreaseMediaErrors: commonStatus.find((item2: any) => item2.name === item.name)?.isIncreaseMediaErrors,
      // isIncreaseErrLogEntries: commonStatus.find((item2: any) => item2.name === item.name)?.isIncreaseErrLogEntries,
      isStatus: commonStatus.find(item2 => item2.name === item.name)!.isStatus,
      isReallocatedSector: item.type === 'nvme' ? false : sataStatus.list.find((item2: any) => item2.name === item.name)!.isReallocatedSector,
    };
  });

  if (devicesPublic.filter(v => v.isReallocatedSector).length >= 2) {
    mainLogger.error(`current: ${JSON.stringify(sataStatus.current)} \n sys: ${JSON.stringify(sataStatus.sys)}`);
  }

  if (!backDetail) {
    let raidStatus: StatusType;
    try {
      raidStatus = await checkRaidError(nodeExportInfo);
    } catch (error) {
      return 'service-error';
    }
    const raidWarnStatus = await checkRaidWarn(nodeExportInfo);
    let status = checkIsError(devicesPublic, false, raidStatus, raidWarnStatus);

    // if(status === 'error' || raidStatus === 'error'){
    //   return 'error';
    // }
    // if(raidWarnStatus === 'warning' || status === 'warning'){
    //   return 'warning';
    // }

    return status;
  } else {
    return devicesPublic.map((item: deviceType) => {
      return {
        name: item.name,
        type: item.type,
        status: checkIsError([item], true),
      };
    });
  }
};

/**
 * 检查冗余颗粒百分比, 如果小于阈值，返回true
 * @returns boolean
 */
const checkAvailableSpare = async (info: MetricsInfo) => {
  const res = getMetricsValue(nvmeKeys[1], info); // 可用冗余颗粒 当前值
  const threshold = getMetricsValue(nvmeKeys[0], info); // 可用冗余颗粒 阈值
  console.log('nvme可用冗余颗粒，当前值', res); // eslint-disable-line no-console
  console.log('nvme可用冗余颗粒，阈值', threshold); // eslint-disable-line no-console

  return res.map(item => {
    return {
      name: item.conditions.device,
      isError: item.value < threshold.find(item2 => item2.conditions.device === item.conditions.device)!.value,
    };
  });
};
/**
 * 公共指标检查， 控制器警告
 * @returns boolean
 */
const checkCriticalWarning = async (info: MetricsInfo) => {
  const res = getMetricsValue(commonKeys[0], info);
  console.log('检查控制器警告返回数据,标准是不等于"0"', res); // eslint-disable-line no-console

  return res.map(item => {
    return {
      name: item.conditions.device,
      isCriticalWarning: item.value !== 0,
    };
  });
};

/**
 * 公共指标，数据完整性错误次数
 * @returns boolean
 */

/**
 * 检查smart状态，如果不是ok，返回true
 */
const checkStatus = async (info: MetricsInfo) => {
  const res = getMetricsValue(commonKeys[3], info);
  console.log('磁盘状态，标准是不等于"1"', res); // eslint-disable-line no-console

  return res.map(item => {
    return {
      name: item.conditions.device,
      isStatus: item.value !== 1,
    };
  });
};

/**
 * 磁盘空间
 */
export const checkFileSystem = async () => {
  try {
    const fetchInfo = initMetricsInfo(await axios.get(NODEEXPORTURL));
    const res = getMetricsValue(commonKeys[4], fetchInfo); // 可用
    const res2 = getMetricsValue(commonKeys[5], fetchInfo); // 磁盘总大小

    return res.map(item => {
      const avail = item.value / 1024 / 1024;
      const size = res2.find(item2 => item2.conditions.device === item.conditions.device)?.value! / 1024 / 1024;

      return {
        name: item.conditions.device,
        mountpoint: item.conditions.mountpoint,
        fileSystemAvail: avail,
        fileSystemSize: size,
        fileSystemUsed: Number(size - avail),
      };
    });
  } catch (e) {
    return 'disk server error';
  }
};

/**
 * 检查sat 盘，重新分配扇区，如果大于阈值，返回list
 */
const checkReallocatedSector = async (info: MetricsInfo) => {
  const res = getMetricsValue(sataKeys[1], info);
  const threshold = getMetricsValue(sataKeys[0], info);
  console.log('重新分配扇区，当前错误率', res); // eslint-disable-line no-console
  console.log('重新分配扇区，系统阈值错误率', threshold); // eslint-disable-line no-console

  const result = res.map(item => {
    return {
      name: item.conditions.device,
      isReallocatedSector: item.value > threshold.find(item2 => item2.conditions.device === item.conditions.device)!.value,
    };
  });

  return {
    list: result,
    current: res,
    sys: threshold,
  };
};
// 公共指标检查，nvme sata 盘都需要检查
const checkCommonSmart = async (info: MetricsInfo) => {
  const CriticalWarningList = await checkCriticalWarning(info);
  // const IncreaseMediaErrorsList = await checkIncreaseMediaErrors(info);
  // const IncreaseErrLogEntriesList = await checkIncreaseErrLogEntries(info);
  const StatusList = await checkStatus(info);

  return CriticalWarningList.map(item => {
    return {
      name: item.name,
      isCriticalWarning: item.isCriticalWarning,
      // isIncreaseMediaErrors: IncreaseMediaErrorsList.find((item2: any) => item2.name === item.name).isIncreaseMediaErrors,
      // isIncreaseErrLogEntries: IncreaseErrLogEntriesList.find((item2: any) => item2.name === item.name).isIncreaseErrLogEntries,
      isStatus: StatusList.find(item2 => item2.name === item.name)!.isStatus,
    };
  });
};
// nvme 盘独有指标检查
const checkNvmeSmart = async (info: MetricsInfo) => {
  const nvmeStatus = await checkAvailableSpare(info);

  return nvmeStatus;
};
// sata 盘独有指标检查
const checkSataSmart = async (info: MetricsInfo) => {
  const reallocatedSectorList = await checkReallocatedSector(info);

  return reallocatedSectorList;
};

// 除了 isReallocatedSector ，扇区问题有两个盘坏了.OAO1OOO5. 其他无论怎样都是 0A010004
const checkIsError = (devices: deviceType[], isSingle: boolean, raidStatus = StatusType.success, raidWarnStatus = StatusType.success) => {
  let smartStatus: StatusType;

  switch (devices.filter(v => v.isReallocatedSector)?.length) {
    case 0:
      smartStatus = StatusType.success;
      break;
    case 1:
      smartStatus = StatusType.warn;
      break;
    default:
      smartStatus = StatusType.error;
  }
  const warnStatus =
    (devices.some(item => {
      return item.isStatus || item.isAvailableSpare || item.isReallocatedSector || item.isCriticalWarning;
    })
      ? StatusType.warn
      : StatusType.success) & raidWarnStatus;

  const errorStatus = smartStatus & raidStatus;

  const status = warnStatus & errorStatus;

  if (status === StatusType.error) {
    if (!isSingle) {
      onSelfMainFaultListen({ '0A010005': FaultStatusEnum.abnormal }, '判断磁盘逻辑出错');
    }

    return 'error';
  } else if (status === StatusType.warn) {
    if (!isSingle) {
      onSelfMainFaultListen({ '0A010004': FaultStatusEnum.abnormal, '0A010005': FaultStatusEnum.normal }, '判断磁盘严重故障');
    }

    return 'warning';
  } else {
    if (!isSingle) {
      onSelfMainFaultListen({ '0A010004': FaultStatusEnum.normal, '0A010005': FaultStatusEnum.normal }, '判断磁盘没有故障');
    }

    return 'success';
  }
};

/**
 * @description 检查raid 是否可以工作。这个属性错误，一定停机
 * 等于 0 表示正常
 */
const checkRaidError = async (info: MetricsInfo) => {
  try {
    const res = getMetricsValue(raidErrorKey[0], info);
    console.log('raid error', res); // eslint-disable-line no-console
    const isSuccess = res.length !== 0 && res[0].value === 0;
    if (isSuccess) {
      // systemError?.errorList.delete('0A010005');

      return StatusType.success;
    } else {
      mainLogger.error(`raid info: ${res}`);

      return StatusType.error;
    }
  } catch (e) {
    mainLogger.error('raid info: error by catch');

    throw Error('system error');
  }
};
/**
 * @description 这个属性 出错只能报警
 * 值为 0 表示正常
 */
const checkRaidWarn = async (info: MetricsInfo) => {
  try {
    const res = getMetricsValue(raidWarningKey[0], info);
    console.log('raid warn', res); // eslint-disable-line no-console
    const isSuccess = res[0].value === 0;
    if (isSuccess) {
      // systemError?.errorList.delete('0A010004');

      return StatusType.success;
    } else {
      return StatusType.warn;
    }
  } catch (e) {
    return StatusType.warn;
  }
};
