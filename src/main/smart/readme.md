# smart 使用文档
## 1. 系统端口
http://192.168.41.60:9099/api/v1/query?query=key     

| 类型          | key值                                                                                              | 说明 | 默认值               |    
|-------------|---------------------------------------------------------------------------------------------------| --- |-------------------|    
| 	+          | smartctl_device	                                                                                  |      设备信息	 | -                 |
| nvme	       | smartctl_device_available_spare	                                                                  |      可用冗余颗粒百分百 | -                 |
| nvme	       | smartctl_device_available_spare_threshold	                                                        |  可用冗余颗粒百分百阈值	   | 50                |
| -           | smartctl_device_capacity_bytes	                                                                   |      磁盘总容量 | -                 | 
| +           | smartctl_device_critical_warning	                                                                 |  控制器的严重警告	     | 正常：0 异常：>0        |
| +           | smartctl_device_media_errors\increase(smartctl_device_media_errors[7d])	                          |数据完整性错误次数 | 增长趋势 阈值10         |
| +           | smartctl_device_num_err_log_entries\increase(smartctl_device_num_err_log_entries[7d])	            | 错误日志条数	持续增长，| 突然大量增长 阈值10       |
| -           | smartctl_device_power_cycle_count	                                                                |     上电次数 | -                 |
| -           | smartctl_device_power_on_seconds	                                                                 | 上电运行时间 | -                 |
| -           | smartctl_device_smart_status	                                                                     | smart状态	| 正常：1 异常：0         |
| -           | smartctl_device_smartctl_exit_status	                                                             | smart exit退出状态| -                 | 
| +           | smartctl_device_status	                                                                           |     设备状态	| 正常：1 异常：0         |
| -           | smartctl_device_temperature	                                                                      |     温度	| nvme: 70 sata: 70 |
| ~~Sata~~	   | ~~smartctl_device_attribute{attribute_name="Raw_Read_Error_Rate",attribute_value_type="raw"}~~	   | 原始读取错误率 | 	值:               |
| ~~Sata~~    | ~~smartctl_device_attribute{attribute_name="Raw_Read_Error_Rate",attribute_value_type="thresh"}~~ | 原始读取错误率 | 	阈值： 16           |
| -           | smartctl_device_attribute{attribute_name="Spin_Up_Time",attribute_value_type="raw"}	              | 表示磁盘旋转时间，| 数值过底表示磁盘性能差       |
| Sata	       | smartctl_device_attribute{attribute_name="Reallocated_Sector_Ct",attribute_value_type="raw"}	     |  重新分配扇区计数 	| 值：0               |
| sata        | smartctl_device_attribute{attribute_name="Reallocated_Sector_Ct",attribute_value_type="thresh"}   |	重新分配扇区计数	 | 阈值：5              |
| +           | node_filesystem_avail_bytes{fstype="ext4"}	                                                       | 磁盘可用空间bytes | -                 |
| +           | node_filesystem_size_bytes{fstype="ext4"}	                                                        | 磁盘总大小bytes | -                 |	
 | raid（不关联磁盘） | node_md_state{device="md0", instance="node", job="node", state="inactive"}  |  raid 服务状态检查  | 0 表示正常,005 级别     |
 | raid（不关联磁盘） | node_md_disks{device="md0", instance="node", job="node", state="failed"}  |  raid 服务状态检查  | 0 表示正常,004 级别     |

## 2. 使用逻辑
 2.1 首先调用smartctl_device 来初始化数据
 2.2 检查nvme 的数据， nvme 类型和 + 类型的数据
 2.3 检查sata 的数据， sata 类型和 + 类型的数据
 
## 3. 结果
    除了 重新分配扇区计数 ，扇区问题有两个盘坏了.OAO1OOO5. 其他无论怎样都是 0A010004
    raid inactive 不为0 也是 0A010005
