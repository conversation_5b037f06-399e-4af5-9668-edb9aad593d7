export type CommonObject = {
  [prop: string]: string;
};

export type MetricsItem = {
  conditions: CommonObject;
  value: number;
};

export type MetricsInfo = {
  [props: string]: MetricsItem[];
};

export const initMetricsInfo = (fetchInfo: {data: string}) => { // NOSONAR
  const info = {};

  fetchInfo
    .data
    .split('\n')
    .filter(v => !/^#/.test(v))
    .forEach(v => { // NOSONAR
      const [, label, , condition, value] = v.match(/(.*?)(\{(.*?)\})?\s(.*)/) || [];
      if (condition) {
        const conditions = {};
        let symbolCount = 1;
        let point = 0;
        let key = '';
        for(let i = 0; i < condition.length; i++) {
          if(condition[i] === '=') {
            key = condition.slice(point, i);
            point = i + 1;
            continue;
          }
          if(condition[i] === '"') {
            if(symbolCount % 2 === 0) {
              if(i === condition.length - 1 || condition[i + 1] === ',') {
                conditions[key] = condition.slice(point, i);
                point = i + 2;
                symbolCount += 1;
              }
            }else {
              point = i + 1;
              symbolCount += 1;
            }
          }
        }
        info[label] = [...(info[label] || []), { value: parseFloat(value), conditions }];
      } else {
        info[label] = [{ value: parseFloat(value), conditions: {} }];
      }
    });

  return info as MetricsInfo;
};

export const getMetricsValue = (query: string, info: MetricsInfo) => { // NOSONAR
  const [, label, , condition] = query.match(/^(.*?)(\{(.*?)\})?$/) || [];
  const conditionList = condition ? condition.split(',').map(v => v.split('=')) : [];
  const res = (info[label] || []).filter(v => !conditionList.some(val => v.conditions[val[0]] !== val[1].slice(1, val[1].length - 1)));

  return res;
};
