{"plan": {"name": "方案名称", "type": 2, "subject_model": {"id": 1, "code": "123456", "name": "治疗验证", "sex": 1, "birth_date": 479808000000, "phone": "13988888888", "condition_desc": "脑部疾病是指影响大脑或神经系统的疾病。这些疾病可以影响思考、记忆、感知、协调和控制身体运动的能力，从而导致各种身体和心理症状。", "remark": "治疗方案需要根据患者的具体情况进行调整，建议再次面诊。", "created_at": 1681699565259, "updated_at": 1681699565259}, "plan_target_model_list": [{"source": 0, "has_mep": true, "hemi": "lh", "type": "LH_MEP", "vertex_index": 46474, "vol_ras": {"y": -33.5870208740208, "x": -20.73792785237725, "z": 79.96064940770125}, "surf_ras": {"y": -46.4224853515625, "x": -18.69185447692871, "z": 78.12596893310547}, "score_index": 0, "score": -1}, {"source": 0, "has_mep": false, "hemi": "lh", "type": "LH_SMA", "vertex_index": 93292, "vol_ras": {"y": 11.558796763422793, "x": 0.7205058288783115, "z": 81.45607941729797}, "surf_ras": {"y": -1.2766677141189575, "x": 2.7665791511535645, "z": 79.62139892578125}, "score_index": 0, "score": 0.83995736, "stimulus": {"type": 2, "relative_strength": 100, "plexus_inner_frequency": 50, "plexus_inter_frequency": 5, "plexus_inner_pulse_count": 3, "plexus_count": 10, "strand_pulse_count": 60, "intermission_time": 8, "pulse_total": 600, "treatment_time": 192}}, {"source": 2, "has_mep": false, "hemi": "lh", "type": "LH_SMA", "vertex_index": 102231, "vol_ras": {"y": 22.076749801638304, "x": 3.4798375144280502, "z": 77.76964707219445}, "surf_ras": {"y": 9.24128532409668, "x": 5.5259108543396, "z": 75.93496704101562}, "score_index": 1, "score": 0.20855287, "stimulus": {"type": 5, "relative_strength": 90, "plexus_inner_frequency": 60, "plexus_inter_frequency": 6, "plexus_inner_pulse_count": 4, "plexus_count": 11, "strand_pulse_count": 61, "intermission_time": 8, "pulse_total": 600, "treatment_time": 195}}, {"source": 1, "has_mep": true, "hemi": "rh", "type": "RH_MEP", "vertex_index": 46471, "vol_ras": {"y": -35.10234832763437, "x": 36.8383145305687, "z": 71.55592385523296}, "surf_ras": {"y": -47.93781280517578, "x": 38.8843879699707, "z": 69.72124481201172}, "score_index": 0, "score": -1, "stimulus": {"type": 1, "relative_strength": 98, "strand_pulse_frequency": 50, "inner_strand_pulse_count": 100, "plexus_inner_frequency": 99, "plexus_inter_frequency": 9, "plexus_inner_pulse_count": 9, "plexus_count": 12, "strand_pulse_count": 65, "intermission_time": 10, "pulse_total": 650, "treatment_time": 200}}], "plan_file_model_list": [{"name": "scalp_mask.obj", "relative_path": "xt6Skj7AG/s/11/j/2/viz/prep/scalp_mask.obj"}, {"name": "scalp_mask.obj.gz", "relative_path": "xt6Skj7AG/s/11/j/2/viz/prep/scalp_mask.obj.gz"}, {"name": "scalp_mask.nii.gz", "relative_path": "xt6Skj7AG/s/11/j/2/viz/prep/scalp_mask.nii.gz"}, {"name": "scalp_mask.nii", "relative_path": "xt6Skj7AG/s/11/j/2/viz/prep/scalp_mask.nii"}, {"name": "anat_parc_native_aparc_aseg.nii.gz", "relative_path": "xt6Skj7AG/s/11/j/2/viz/anat_parc/anat_parc_native_aparc_aseg.nii.gz"}, {"name": "lhrh_anat_parc_aparc.txt.gz", "relative_path": "xt6Skj7AG/s/11/j/2/viz/anat_parc/lhrh_anat_parc_aparc.txt.gz"}, {"name": "lh_anat_parc_aparc.txt", "relative_path": "xt6Skj7AG/s/11/j/2/viz/anat_parc/lh_anat_parc_aparc.txt"}, {"name": "lhrh_anat_parc_a2009s.txt", "relative_path": "xt6Skj7AG/s/11/j/2/viz/anat_parc/lhrh_anat_parc_a2009s.txt"}, {"name": "lhrh_anat_parc_a2009s.txt.gz", "relative_path": "xt6Skj7AG/s/11/j/2/viz/anat_parc/lhrh_anat_parc_a2009s.txt.gz"}, {"name": "lhrh_anat_parc_aparc.txt", "relative_path": "xt6Skj7AG/s/11/j/2/viz/anat_parc/lhrh_anat_parc_aparc.txt"}, {"name": "lh_anat_parc_aparc.txt.gz", "relative_path": "xt6Skj7AG/s/11/j/2/viz/anat_parc/lh_anat_parc_aparc.txt.gz"}, {"name": "anat_parc_native_aparc_aseg.nii", "relative_path": "xt6Skj7AG/s/11/j/2/viz/anat_parc/anat_parc_native_aparc_aseg.nii"}, {"name": "pial.gii", "relative_path": "xt6Skj7AG/s/11/j/2/viz/recon/pial.gii"}, {"name": "tkras2ras_matrices.json", "relative_path": "xt6Skj7AG/s/11/j/2/viz/recon/tkras2ras_matrices.json"}, {"name": "plan.json", "relative_path": "xt6Skj7AG/s/11/j/2/viz/recon/info/plan.json"}, {"name": "subject.json", "relative_path": "xt6Skj7AG/s/11/j/2/viz/recon/info/subject.json"}, {"name": "T1.mgz", "relative_path": "xt6Skj7AG/s/11/j/2/viz/recon/T1.mgz"}, {"name": "pial.gii.gz", "relative_path": "xt6Skj7AG/s/11/j/2/viz/recon/pial.gii.gz"}]}, "file_name": "xt6Skj7AG.ng", "temp_directory": "/Users/<USER>/Downloads/tmp/5cd42482cf0c4ff89688abe5287c9d6d"}