// eslint-disable-next-line no-shadow
export enum AppLifeCycleListenerStatus {
  UnStart = 'UnStart',
  Working = 'Working',
  Finished = 'Finished',
}

export abstract class AppLifeCycleListener {
  public status: AppLifeCycleListenerStatus;

  public constructor() {
    this.status = AppLifeCycleListenerStatus.UnStart;
  }

  public abstract actualOnAppWillQuit(): Promise<void>;

  public isFinished(): boolean {
    return this.status === AppLifeCycleListenerStatus.Finished;
  }

  public async onAppWillQuit() {
    if (this.status === AppLifeCycleListenerStatus.UnStart) {
      this.status = AppLifeCycleListenerStatus.Working;
      await this.actualOnAppWillQuit();
      this.status = AppLifeCycleListenerStatus.Finished;
    }
  }
}
