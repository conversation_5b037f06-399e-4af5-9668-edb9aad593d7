import { AppLifeCycleListener } from './appLifeCycleListener';

const PREPARE_QUIT_TIMEOUT = 3000;

export let isTimeout: boolean = false;
export const listenerObj: { [tag: string]: AppLifeCycleListener } = {};

export const onAppWillQuit = async (): Promise<undefined> => {
  return new Promise<undefined>((resolve: (value?: undefined | PromiseLike<undefined>) => void, reject: (reason?: any) => void) => {
    setTimeout(() => {
      isTimeout = true;
      resolve();
    }, PREPARE_QUIT_TIMEOUT);
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    Promise.all(
      // eslint-disable-next-line @typescript-eslint/promise-function-async
      Object.values(listenerObj).map((item: AppLifeCycleListener) => item.onAppWillQuit())
    )
      .then(() => resolve())
      .catch();
  });
};

export const isReadyToQuit = (): boolean => {
  if (isTimeout) {
    return true;
  }
  for (const listener of Object.values(listenerObj)) {
    if (!listener.isFinished()) {
      return false;
    }
  }

  return true;
};

export const registerAppLifeCycleListener = (tag: string, listener: AppLifeCycleListener) => {
  listenerObj[tag] = listener;
};

export const clearListener = (tag: string) => {
  delete listenerObj[tag];
};

export const clearAllListeners = () => {
  for (const tag of Object.keys(listenerObj)) {
    delete listenerObj[tag];
  }
};
