/* istanbul ignore file */
import { BrowserWindow, dialog, MessageBoxReturnValue, OpenDialogReturnValue, OpenDialogOptions, app } from 'electron';
import { getI18Next } from '../../i18n/i18next.config';
import { execSync, spawnSync, SpawnSyncReturns } from 'child_process';
import { join } from 'path';
import { ChooseType } from '../../../common/types/index';

export const selectFileOrDirectory = async (chooseType: ChooseType): Promise<string[] | undefined> => {
  const i18n = await getI18Next();
  let options: OpenDialogOptions;
  if (chooseType === ChooseType.File) {
    options = { properties: ['openFile'], filters: [{ extensions: ['zip'], name: 'ZIP' }] };
  } else {
    options = { properties: ['openDirectory'] };
  }
  options.title = i18n.t('menuAppName') || '';

  return new Promise<string[] | undefined>((resolve: (value?: string[]) => void) => {
    const mainUiWin = BrowserWindow.getFocusedWindow() || BrowserWindow.getAllWindows()[0];
    if (mainUiWin) {
      dialog
        .showOpenDialog(mainUiWin, options)
        .then((result: OpenDialogReturnValue) => {
          resolve(result.filePaths);
        })
        .catch((err: any) => {
          // eslint-disable-next-line no-console
          console.log('err', err);
        });
    }
  });
};

export const showMessageBox = async (messageKey: string, additionalInfo?: string): Promise<void> => {
  const i18n = await getI18Next();
  let message = i18n.t(messageKey);
  if (additionalInfo) {
    message = `${message}\n${additionalInfo}`;
  }
  const mainUiWin = BrowserWindow.getFocusedWindow() || BrowserWindow.getAllWindows()[0];
  if (mainUiWin) {
    dialog
      .showMessageBox(mainUiWin, {
        title: i18n.t('menuAppName') || '',
        message,
        buttons: [i18n.t('btnOk')],
        defaultId: 0,
        cancelId: 0,
      })
      .catch((err: any) => {
        // eslint-disable-next-line no-console
        console.log('err', err);
      });
  }
};

export const showConfirmMessageBox = async (messageKey: string, btnOkKey: string = 'btnOk', btnCancelKey: string = 'btnCancel'): Promise<boolean> => {
  const i18n = await getI18Next();

  return new Promise<boolean>((resolve: (value: boolean | PromiseLike<boolean>) => void) => {
    const mainUiWin = BrowserWindow.getFocusedWindow() || BrowserWindow.getAllWindows()[0];
    if (mainUiWin) {
      dialog
        .showMessageBox(mainUiWin, {
          title: i18n.t('menuAppName') || '',
          message: i18n.t(messageKey),
          buttons: [i18n.t(btnOkKey), i18n.t(btnCancelKey)],
          defaultId: 1,
          cancelId: 1,
        })
        .then((result: MessageBoxReturnValue) => {
          if (result.response === 0) {
            resolve(true);
          } else {
            resolve(false);
          }
        })
        .catch((err: any) => {
          // eslint-disable-next-line no-console
          console.log('err', err);
        });
    }
  });
};

export const kioskSelectDirectory = async (
  keyNoUsb: string = 'msgKioskNoMountDisk',
  keyErrUsb: string = 'msgKioskNotAvailableDisk'
): Promise<string[] | undefined> => {
  let selection: string[] | undefined;

  let linuxUserName: string | undefined;
  try {
    linuxUserName = execSync('whoami', { encoding: 'utf8' }).trim();
    if (!linuxUserName) {
      throw new Error('error on check mount disk');
    }

    const mountResultStr: string | undefined = execSync(`ls /media/${linuxUserName}`, { encoding: 'utf8' }).trim();
    if (!mountResultStr) {
      throw new Error('error on check mount disk');
    }

    const mountResult: string[] | undefined = mountResultStr.split('\n');

    if (!mountResult || mountResult.length === 0) {
      throw new Error('error on check mount disk');
    }

    if (mountResult[0].trim().length === 0) {
      await showMessageBox(keyNoUsb);

      return [];
    }

    const response: SpawnSyncReturns<string> = spawnSync(
      join(__dirname, '../../../folderChooser.plg'),
      [`file:///media/${linuxUserName}`, 'folder', `file:///media/${linuxUserName}/${mountResult[0].trim()}`],
      { encoding: 'utf8' }
    );
    if (response.status !== 0) {
      throw new Error('error on select folder');
    }

    if (response.stdout.startsWith('NoNaCcEpT')) {
      selection = undefined;
    } else {
      selection = [response.stdout];
    }
  } catch (err) {
    selection = await selectFileOrDirectory(ChooseType.Folder);
  }

  let availablePrefix: string;
  if (linuxUserName) {
    availablePrefix = `/media/${linuxUserName}/`;
  } else {
    availablePrefix = '/media/';
  }
  if (selection && selection.length > 0 && !selection[0].startsWith(availablePrefix)) {
    selection = undefined;
    await showMessageBox(keyErrUsb);
  }

  return selection;
};

export const appQuit = () => {
  app.quit();
};

export const setSleepTime = (seconds: number = 900): number => {
  try {
    execSync(`gsettings set org.gnome.desktop.session idle-delay ${seconds}`);
  } catch (e) {
    // eslint-disable-next-line no-console
    console.log('setSleepTime', e);
  }

  return seconds;
};

export const getSleepTime = (): number => {
  let seconds: any = 0;

  try {
    seconds = execSync('gsettings get org.gnome.desktop.session idle-delay');
  } catch (e) {
    // eslint-disable-next-line no-console
    console.log('getSleepTime', e);
  }

  return +seconds;
};
