import { LoginParams, LoginRes, UserSession } from '@/common/types';
import { getAuthApiEndpoint } from '../../common/api/ngApiAgent';
import { axiosHttpClient } from '../../common/api/httpClient/axiosHttpClientImplMain';
import { getSetting } from '@/common/lib/env/env';
import { M200Api } from '@/common/api/ng/m200Api';
import { renderLogger, historyLogger } from '@/common/lib/appLogs/generateLogger';
import { mainUiWin } from '@/main/main';

export const RENEW_TOKEN_BEFORE_EXPIRE_SECONDS = 5 * 60;
// to avoid some timing bug, this value (RETRY_DELAY_FAIL) should not be very small, preferably not less than 60
export const RETRY_DELAY_WHEN_REAUTH_FAIL_SECONDS = 60;

export let userSession: UserSession | undefined;
let language = 'zh-CN';
let storagePath = '';

const mapLoginResponseDTOToUserSession = (loginResponse: LoginRes): UserSession | undefined => {
  const jwtToken = loginResponse.authorization;

  return {
    jwtToken,
    ...loginResponse,
  };
};

export const clearUserSession = async () => {
  userSession = undefined;
};

const setStoragePath = async (token: string) => {
  storagePath = (await new M200Api(axiosHttpClient).getConfig({ group_name: 'init', name: 'storagePath' })).find(
    (v: any) => v.name === 'storagePath'
  )!.value;
};
export const authenticate = async (loginDto: LoginParams) => {
  try {
    language = loginDto.language;
    axiosHttpClient.setLogInstance(renderLogger);
    axiosHttpClient.setLanguage(language);
    axiosHttpClient.setBaseUrl(getSetting().REACT_APP_NG_API_BASEURL);
    const authRespDto: LoginRes = await getAuthApiEndpoint().authenticate(
      { username: loginDto.username, password: loginDto.password },
      undefined,
      3000
    );
    const session = mapLoginResponseDTOToUserSession(authRespDto);
    if (session) {
      userSession = session;
      userSession.language = language;
      axiosHttpClient.setAuthToken(session.authorization);
      sendHttpClientToRenderer();
      await setStoragePath(session.jwtToken);
      await getAuthApiEndpoint().setConfig([
        {
          group_name: 'init',
          name: 'lastAccount',
          value: loginDto.username,
        },
      ]);

      return { userSession: session };
    } else {
      throw new Error('Not Authenticated');
    }
  } catch (error) {
    return {
      apiStatus: {
        loading: false,
        error,
      },
    };
  }
};

export const getUserSession = (): UserSession | undefined => {
  return Object.assign({}, userSession, { language });
};

export const resetLoginStatus = async () => {
  // await cancelTransferWithAnswer();
  await clearUserSession();
  axiosHttpClient.resetAuthToken();
};

export const onReceivedLogoutSignal = async () => {
  await resetLoginStatus();
  // logoutRenderer();
};

export const getStoragePath = (): string => {
  return storagePath;
};

export const sendHttpClientToRenderer = () => {
  if (userSession) {
    historyLogger.info('登录成功');
  }
  mainUiWin?.webContents.send('SUBSCRIBE_HTTP_CLIENT', {
    language,
    baseUrl: getSetting().REACT_APP_NG_API_BASEURL,
    jwtToken: userSession?.jwtToken,
  });
};
