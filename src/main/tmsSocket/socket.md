1: 启动程序, 2秒检测一次连接状态, 如果是 WebSocket.OPEN, WebSocket.CONNECTING, 则不重连, 否则重连socket

2: 连上以后 2秒发一次心跳 heartbeat

3: 连上以后 3秒发一次通道检测命令 device_status

4: 连上以后 3.5秒发一次 查询拍子参数 coil_query water_cooling_query

5: working_status 启动时查询一次, 后面靠下位机推送    这个命令也基本没用了, 前端没用到

7: 技术支持查了 version

8: beat 命令靠下位机推送

9: fault 命令前端未查询, 靠下位机推送

10: warning_query 每次socket 连接成功时候查询一次

11: set_treatment_threshold 设阈值, 测阈值的地方在用, 没有处理异常

12: set_level 方案次数的时候设置强度用 有异常处理, 有报错

13 未使用命令  "action": "status"  废弃掉了

14 get_treatment_threshold 没有用到

15 set_treatment_plan 设置治疗方案的时候在用, 有异常处理

16 get_treatment_plan 前端没有用到

17 start_treatment 开始结束暂停等调用, 有异常处理

18 query_treatment 开始治疗之后前端1秒查一次

19: trigger_query trigger_setting 以后可能会用到

20: set_level 方案次数的时候设置强度用

前端维护了一个数组, 发出去的消息会存到数组里面, 回复消息了会删除数组中的元素
2秒查询一次发出去的命令回复有没有超时, 如果超过10秒未回复就认为是超时
