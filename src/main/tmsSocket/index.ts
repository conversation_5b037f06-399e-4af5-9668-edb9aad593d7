import { BrowserWindow, IpcMainInvokeEvent } from 'electron';
import WebSocket from 'ws';
import {
  BEAT,
  BEAT_BTN,
  COIL_QUERY,
  COMMIT,
  DEVICE_STATUS,
  GET_SOCK_INFO,
  HEARTBEAT,
  QUERY_TREATMENT,
  SET_BEAT_SCREEM,
  SET_LEVEL,
  SET_TREATMENT_THRESHOLD,
  START_TREATMENT,
  TMS_VERSION,
  TMSScreenState,
  TRIGGER_QUERY,
  TRIGGER_SETTING,
  TUNNEL_INFO,
  WATER_COOLING_QUERY,
  WORK_STATUS,
} from '@/common/constant/tms';
import { isEqual, omit } from 'lodash';
import { getSetting } from '@/common/lib/env/env';
import { v4 as uuidv4 } from 'uuid';
import { historyLogger, mainLogger, tmsLogger } from '@/common/lib/appLogs/generateLogger';

export enum IPassStatus {
  'Preparing', // 准备阶段
  'Running', // 正常运行
  'Disconnect', // 断开
  'ReConnect', // 重连
  'Destroy', // 销毁
}

let index = 0;
const getIndex = () => {
  index = (index + 1) % 65535;

  return index;
};

type DataPool = {
  fault: any;
  coil_query: any;
  sock_param: any;
  water_cooling_query: any;
};
const dataPool: DataPool = {
  fault: null,
  coil_query: null,
  sock_param: null,
  water_cooling_query: null,
};
const setting = getSetting();

class ImgSocket {
  private socket?: WebSocket | null;
  constructor() {
    this.initSocket();
  }
  private initSocket = () => {
    mainLogger.info(`当前为${process.env.NAV ? '有' : '无'}导航`);
    if (!process.env.NAV) {
      return;
    }
    setInterval(() => {
      if ([WebSocket.OPEN, WebSocket.CONNECTING].includes(this.socket?.readyState as any)) return;
      this.createSocket();
    }, 2000);
  };
  private createSocket = () => {
    this.socket = null;
    mainLogger.info('main-ImgSocket开始创建');
    this.socket = new WebSocket(setting.IMAGE_SOCKET_URL);
    this.socket.onopen = () => {
      mainLogger.info('main-ImgSocket链接成功');
    };
    this.socket.onclose = () => {
      mainLogger.info('main-ImgSocket close');
    };
    this.socket.onerror = () => {
      mainLogger.info('main-ImgSocket error');
    };
  };
  public sendMessage = (param: any) => {
    if (this.socket?.readyState !== WebSocket.OPEN) {
      mainLogger.info('main-ImgSocket状态不为open', this.socket?.readyState);

      return;
    }
    this.socket?.send(param);
  };
}
const imgSocket = new ImgSocket();

type RequestList = {
  [key: string]: { resolve(data: any): void; reject(data: any): void; createAt: number; params: any };
};

class TmsSocket {
  public client?: WebSocket | null;
  public socketOpen: boolean | undefined;
  public passOpen: boolean | undefined;
  public mainUiWin: BrowserWindow | null;
  private requestList: RequestList;
  public get_sock_info_callback?: (mainUiWin: BrowserWindow | null, param: any) => void;
  public socket_error_callback?: () => void;
  public socket_close_callback?: () => void;
  public has_render: boolean;
  private intervalTime: any;
  private resetFirstConnect: number;

  constructor() {
    this.socketOpen = undefined;
    this.passOpen = undefined;
    this.mainUiWin = null;
    this.has_render = false;
    this.requestList = {};
    this.resetFirstConnect = 30; // error&close都会同时调用，所以应该是30次
    this.intervalTime = setInterval(() => {
      if (this.mainUiWin) {
        clearInterval(this.intervalTime);

        return;
      }
      this.getMainUiWin();
    }, 2000);
    setInterval(() => {
      if (this.has_render && this.mainUiWin) {
        // 在没与tms建立连接时，向render发送open为undefined，避免错误记录日志
        tmsLogger.info(`socketOpen:${this.socketOpen}`, `passOpen:${this.passOpen}`);
        const sendOpen = this.socketOpen === undefined || this.passOpen === undefined ? undefined : this.socketOpen && this.passOpen;
        this.get_sock_info_callback?.(this.mainUiWin, {
          open: sendOpen,
        });
      }
      if ([WebSocket.OPEN, WebSocket.CONNECTING].includes(this.client?.readyState as any)) return;
      this.createSocket();
    }, 2000);
  }

  private createSocket = () => {
    this.client = null;
    this.client = new WebSocket(setting.TMS_SOCKET_URL);
    this.client.onopen = () => {
      this.resetFirstConnect = -1;
      initSocketStatus();
      tmsLogger.info('tms socket success');
      this.socketOpen = this.client?.readyState === WebSocket.OPEN;
    };
    this.client.onmessage = this.handleReceiveMsg;
    this.client.onclose = () => {
      tmsLogger.info('tms socket close');
      this.socket_close_callback?.();
      this.socketErrorOrClose();
    };
    this.client.onerror = () => {
      tmsLogger.info('tms socket error');
      this.socket_error_callback?.();
      this.socketErrorOrClose();
    };
  };
  public getClientStatus = () => {
    let value = true;
    if (!this.client) {
      tmsLogger.info('client未存在');
      value = false;
    }
    if (!this.socketOpen) {
      tmsLogger.info('socket未链接');
      value = false;
    }

    return value;
  };
  private socketErrorOrClose = () => {
    if (this.resetFirstConnect >= 0) {
      this.resetFirstConnect--;
    }
    if ((this.socketOpen !== undefined && this.passOpen !== undefined) || this.resetFirstConnect <= 0) {
      this.socketOpen = this.client?.readyState === WebSocket.OPEN;
      this.passOpen = false;
    }
  };
  private getMainUiWin = () => {
    if (!this.mainUiWin) {
      // eslint-disable-next-line @typescript-eslint/no-floating-promises
      import('../main').then(res => {
        if (res.mainUiWin) {
          this.mainUiWin = res.mainUiWin;
          this.mainUiWin.webContents.setMaxListeners(Infinity);
        }
      });
    }
  };

  public mapOvertime = () => {
    Object.values(this.requestList).forEach((request, ind) => {
      if ((Date.now() - request.createAt) / 1000 > 10) {
        const params = JSON.parse(request.params);
        request.resolve({ ...params, code: 400, message: '连接超时！' });
        delete this.requestList[ind];
      }
    });
  };

  // BEGIN-NOSCAN
  public handleReceiveMsg = (data: any) => {
    tmsLogger.info(data.data);
    try {
      this.getMainUiWin();
      if (!tmsSocket.has_render) return; // render进程加载了之后才能给render发送消息
      const returnValue = JSON.parse(data.data);
      const ind = returnValue?.data?.index;
      if (this.requestList[ind]) {
        this.requestList[ind].resolve(returnValue);
        delete this.requestList[ind];
      }
      // 设备状态
      if (returnValue.action === WORK_STATUS) {
        this.mainUiWin?.webContents?.send(WORK_STATUS, returnValue);
      }
      // 报警
      if (returnValue.action === COIL_QUERY) {
        const pickValue = omit(returnValue.data, 'index', 'trace', 'beg', 'time_consuming', 'actionb', 'action', '');
        if (!isEqual(dataPool.coil_query, pickValue)) {
          this.mainUiWin?.webContents?.send(COIL_QUERY, returnValue);
          dataPool.coil_query = pickValue;
        }
      }
      if (returnValue.action === WATER_COOLING_QUERY) {
        const pickValue = omit(returnValue.data, 'index', 'trace', 'beg');
        if (!isEqual(dataPool.water_cooling_query, pickValue)) {
          this.mainUiWin?.webContents?.send(WATER_COOLING_QUERY, returnValue);
          dataPool.water_cooling_query = pickValue;
        }
      }
      // 拍子按键
      if (returnValue.action === BEAT) {
        this.mainUiWin?.webContents?.send(BEAT_BTN, returnValue);
        historyLogger.info(`拍子按下，按钮：${returnValue.data.key}`);
        this.sendMessage(JSON.stringify({ action: COMMIT, index: returnValue.data.index }));
      }
      // 通道状态
      // if (returnValue.action === STATUS) {
      //   if (returnValue.data.status === IPassStatus[1]) {
      //     this.passOpen = true;
      //   } else {
      //     this.passOpen = false;
      //   }
      // }
      if (returnValue.action === DEVICE_STATUS) {
        if (returnValue.data.tms_master.result === 0 && returnValue.data.tms_slave.result === 0) {
          this.passOpen = true;
        } else {
          tmsLogger.info(
            `returnValue.data.tms_master: ${returnValue.data.tms_master}`,
            `returnValue.data.tms_slave: ${returnValue.data.tms_slave}`,
            `returnValue.data.pdu_master: ${returnValue.data.pdu_master}`
          );
          this.passOpen = false;
        }
      }
    } catch (err) {
      tmsLogger.info('tmsSocket handleReceiveMsg err', err);
    }
  };
  // END-NOSCAN
  public sendMessage = (params: any) => {
    if (!this.getClientStatus()) return;
    tmsLogger.info('发给tms的参数', params);
    this.client?.send(params);
  };

  public sendMessageByIndex = async (params: any, ind: number) => {
    // if (!this.getClientStatus()) return Promise.reject();
    tmsLogger.info('发给tms的参数', params);
    this.client?.send(params);

    return new Promise((resolve, reject) => {
      this.requestList[ind] = { params, resolve, reject, createAt: Date.now() };
    });
  };
}

const tmsSocket = new TmsSocket();

// 设备状态检查
const work_status_func = () => {
  tmsSocket.sendMessage(
    JSON.stringify({
      action: WORK_STATUS,
      index: getIndex(),
    })
  );
};

//  设置治疗参数
export const set_treatment_plan = async (_: IpcMainInvokeEvent, param: any, noImage?: boolean) => {
  tmsLogger.info(param, '设置的治疗参数');
  const sendIndex = getIndex();
  const data = await tmsSocket.sendMessageByIndex(JSON.stringify({ ...param, index: sendIndex }), sendIndex);
  if (!noImage) {
    imgSocket.sendMessage(JSON.stringify({ ...param, index: sendIndex, id: 11801 }));
  }

  return data;
};

export const set_treatment_level = async (_: IpcMainInvokeEvent, level: number, level_relative: number) => {
  tmsLogger.info(level, level_relative, '重新设置的强度');
  const sendIndex = getIndex();

  return tmsSocket.sendMessageByIndex(JSON.stringify({ action: SET_LEVEL, index: sendIndex, level, level_relative }), sendIndex);
};

export const noImage_treatment_plan_start = async (_: IpcMainInvokeEvent | null, command: string, tid?: string) => {
  return await treatment_plan_start(command, true, tid);
};

export const image_treatment_plan_start = async (_: IpcMainInvokeEvent | null, command: string, tid?: string) => {
  return await treatment_plan_start(command, false, tid);
};

const initSocketStatus = () => {
  // eslint-disable-next-line @typescript-eslint/no-floating-promises
  noImage_treatment_plan_start(null, 'SingleEnd');
  // eslint-disable-next-line @typescript-eslint/no-floating-promises
  image_treatment_plan_start(null, 'PlanEnd');
  // eslint-disable-next-line @typescript-eslint/no-floating-promises
  set_beat_screen(null, TMSScreenState.NotStarted);
};

// 治疗开始
const treatment_plan_start = async (command: string, noImage?: boolean, tid?: string) => {
  const uuid = tid ? tid : uuidv4();
  tmsLogger.info('当前治疗的命令:', command, 'tid:', uuid);
  const sendIndex = getIndex();
  const data = await tmsSocket.sendMessageByIndex(
    JSON.stringify({
      action: START_TREATMENT,
      index: sendIndex,
      command,
      tid: uuid,
    }),
    sendIndex
  );
  if (!noImage) {
    imgSocket.sendMessage(JSON.stringify({ action: START_TREATMENT, index: sendIndex, command, id: 11803 }));
  }

  return data;
};
// 设置屏幕
export const set_beat_screen = async (_: IpcMainInvokeEvent | null, command: TMSScreenState) => {
  tmsLogger.info('设置屏幕:', command);
  const sendIndex = getIndex();
  const data = await tmsSocket.sendMessageByIndex(
    JSON.stringify({
      action: SET_BEAT_SCREEM,
      index: sendIndex,
      mode: command,
    }),
    sendIndex
  );

  return data;
};

// 治疗数据查询
export const query_treatment = async () => {
  const sendIndex = getIndex();
  const data = await tmsSocket.sendMessageByIndex(
    JSON.stringify({
      action: QUERY_TREATMENT,
      index: sendIndex,
    }),
    sendIndex
  );

  return data;
};

// 单次模式设置
export const set_treatment_threshold = async (_: IpcMainInvokeEvent, param: any) => {
  const sendIndex = getIndex();
  const data = await tmsSocket.sendMessageByIndex(
    JSON.stringify({
      action: SET_TREATMENT_THRESHOLD,
      index: sendIndex,
      level: param.level,
    }),
    sendIndex
  );

  return data;
};

// 查询trigger状态
export const handleQueryTrigger = async () => {
  const sendIndex = getIndex();
  const data = await tmsSocket.sendMessageByIndex(
    JSON.stringify({
      action: TRIGGER_QUERY,
      index: sendIndex,
    }),
    sendIndex
  );

  return data;
};
// 设置trigger状态 true:in false:out
export const handleSetTrigger = async (
  _: IpcMainInvokeEvent,
  triggerFlag: {
    in?: 0 | 1; // 0：关闭状态，1：打开状态，
    out?: 0 | 1; // 0：关闭状态，1：打开状态，
  }
) => {
  const sendIndex = getIndex();
  const data = await tmsSocket.sendMessageByIndex(
    JSON.stringify({
      action: TRIGGER_SETTING,
      index: getIndex(),
      ...triggerFlag,
    }),
    sendIndex
  );

  return data;
};
// 发送sock参数
tmsSocket.get_sock_info_callback = (mainUiWin, param) => {
  if (!isEqual(dataPool.sock_param, param)) {
    mainUiWin?.webContents?.send(GET_SOCK_INFO, param);
    dataPool.sock_param = param;
  }
};

// 查询拍子参数
export const auto_query_coil = async () => {
  if (tmsSocket.client?.readyState !== WebSocket.OPEN) {
    throw Error('socket未连接');
  }
  const sendIndex = getIndex();
  const data: any = await tmsSocket.sendMessageByIndex(
    JSON.stringify({
      action: COIL_QUERY,
      index: sendIndex,
    }),
    sendIndex
  );
  if (!data) return;
  const pickValue = omit(data.data, 'index');
  if (!isEqual(dataPool.coil_query, pickValue)) {
    tmsSocket.mainUiWin?.webContents?.send(COIL_QUERY, data);
    dataPool.coil_query = pickValue;
  }

  return data;
};

export const get_tms_version = async () => {
  const sendIndex = getIndex();
  const data = await tmsSocket.sendMessageByIndex(
    JSON.stringify({
      action: TMS_VERSION,
      index: sendIndex,
    }),
    sendIndex
  );

  return data;
};

export const get_tunnel_info = async () => {
  const sendIndex = getIndex();
  if (tmsSocket.client?.readyState !== 1) {
    return {};
  }
  const data = await tmsSocket.sendMessageByIndex(
    JSON.stringify({
      action: TUNNEL_INFO,
      index: sendIndex,
    }),
    sendIndex
  );

  return data;
};

export const set_render = () => (tmsSocket.has_render = true);

export const clear_data_pool = () => {
  dataPool.coil_query = null;
  dataPool.fault = null;
  // dataPool.sock_param = null;
  dataPool.water_cooling_query = null;
};

// 心跳检查
const heart_beat = () => {
  setInterval(() => {
    tmsSocket.sendMessage(
      JSON.stringify({
        action: HEARTBEAT,
        index: getIndex(),
      })
    );
  }, 2000);
};

// 通道状态查询
export const device_status_func = () => {
  setInterval(() => {
    tmsSocket.sendMessage(
      JSON.stringify({
        action: DEVICE_STATUS,
        index: getIndex(),
      })
    );
  }, 3000);
};

const coil_query_func = () => {
  setInterval(() => {
    tmsSocket.sendMessage(
      JSON.stringify({
        action: COIL_QUERY,
        index: getIndex(),
      })
    );
  }, 1000);
};

// 水冷
const cooling_query_func = () => {
  setInterval(() => {
    tmsSocket.sendMessage(
      JSON.stringify({
        action: WATER_COOLING_QUERY,
        index: getIndex(),
      })
    );
  }, 3500);
};

export const crashed_stop_treat = () => {
  tmsLogger.info('崩溃调用结束刺激');
  const sendIndex = getIndex();
  // eslint-disable-next-line @typescript-eslint/no-floating-promises
  tmsSocket.sendMessageByIndex(
    JSON.stringify({
      action: START_TREATMENT,
      index: sendIndex,
      command: 'PlanEnd',
    }),
    sendIndex
  );
  imgSocket.sendMessage(JSON.stringify({ action: START_TREATMENT, index: sendIndex, command: 'PlanEnd', id: 11803 }));
  // eslint-disable-next-line @typescript-eslint/no-floating-promises
  tmsSocket.sendMessageByIndex(
    JSON.stringify({
      action: SET_BEAT_SCREEM,
      index: getIndex(),
      mode: TMSScreenState.NotStarted,
    }),
    sendIndex
  );
};

export const getTmsSocketReadyState = () => {
  return tmsSocket.client?.readyState;
};

heart_beat();
work_status_func();
device_status_func();
coil_query_func();
cooling_query_func();

setInterval(() => {
  tmsSocket.mapOvertime();
}, 2000);
