/** Generate by swagger-axios-codegen */
/* eslint-disable */
// @ts-nocheck
import { AxiosInstance, AxiosRequestConfig } from 'axios';
import { IntlShape } from 'react-intl';
import { Merge } from 'type-fest';

export interface IRequestOptions extends AxiosRequestConfig { }

export interface IRequestConfig {
  method?: any;
  headers?: any;
  url?: any;
  data?: any;
  params?: any;
}

// Add options interface
export interface ServiceOptions {
  axios?: AxiosInstance;
}

export type TmsInfoType = {
  firmware_version: string;
  commit: string;
  voltageB?: number;
  voltageK?: number;
  voltage_calibration?: number;
  treat_count?: number;
  sn?: string;
};

/**
 * 相机信息
 */
export type CameraInfoType = {
  cameraSerialNum: string;
  version: string;
  commit?: string;
};

export type TmsInfoType = {
  firmware_version: string;
  commit: string;
  voltageB?: number;
  voltageK?: number;
  voltage_calibration?: number;
};

// Add default options
export const serviceOptions: ServiceOptions = {};

// Instance selector
export function axios(configs: IRequestConfig, resolve: (p: any) => void, reject: (p: any) => void): Promise<any> {
  if (serviceOptions.axios) {
    return serviceOptions.axios
      .request(configs)
      .then(res => {
        resolve(res.data);
      })
      .catch(err => {
        reject(err);
      });
  } else {
    throw new Error('please inject yourself instance like axios  ');
  }
}

export function getConfigs(method: string, contentType: string, url: string, options: any): IRequestConfig {
  const configs: IRequestConfig = { ...options, method, url };
  configs.headers = {
    ...options.headers,
    'Content-Type': contentType,
  };
  return configs;
}

export const basePath = '';

export interface IList<T> extends Array<T> { }

export interface List<T> extends Array<T> { }

export interface IDictionary<TValue> {
  [key: string]: TValue;
}

export interface Dictionary<TValue> extends IDictionary<TValue> { }

export interface IListResult<T> {
  items?: T[];
}

export class ListResultDto<T> implements IListResult<T> {
  items?: T[];
}

export interface IPagedResult<T> extends IListResult<T> {
  totalCount?: number;
  items?: T[];
}

export class PagedResultDto<T = any> implements IPagedResult<T> {
  totalCount?: number;
  items?: T[];
}

// customer definition
// empty

export interface UserResetPasswordModel {
  /** 用户id */
  user_id: number;

  /** 新密码 */
  new_password: string;
}

export interface UserPageQueryModel {
  /** id 列表 */
  ids?: number[];

  /** 页码 */
  page_num?: number;

  /** 分页大小 */
  page_size?: number;

  /** 查询角色列表 */
  role_enum_list: EnumUserPageQueryModelRoleEnumList[];
}

export interface AuthorityModel {
  /** 权限ID */
  id?: number;

  /** 权限code */
  code?: string;

  /** 权限名称 */
  name?: string;

  /** 创建人 */
  create_id?: number;

  /** 修改人 */
  update_id?: number;
}

export interface PageModelUserModel {
  /** 总数 */
  total?: number;

  /** 每页显示条数，默认 10 */
  size?: number;

  /** 当前页 */
  current?: number;

  /** 分页记录列表 */
  records?: UserModel[];
}

export interface RoleModel {
  /** 角色ID */
  id?: number;

  /** 角色code */
  code?: string;

  /** 角色名称 */
  name?: string;

  /** 创建人 */
  create_id?: number;

  /** 修改人 */
  update_id?: number;

  /** 权限列表 */
  authority_list?: AuthorityModel[];
}

export interface UserModel {
  /** 用户ID */
  user_id?: number;

  /** 角色ID */
  role_id?: number;

  /** ID */
  id: number;

  /** 授权信息 */
  authorization?: string;

  /** 用户名 */
  username: string;

  /** 密码 */
  password: string;

  /**  */
  role?: RoleModel;

  /** 昵称 */
  nickname?: string;

  /** 是否需要修改密码 */
  should_change_password?: boolean;

  /** 状态 1可用 2不可用 */
  status?: number;

  /** 创建人 */
  create_id?: number;

  /** 修改人 */
  update_id?: number;
}

export interface UserLoginModel {
  /** 用户名 */
  username: string;

  /** 密码 */
  password: string;
}

export interface UserEditModel {
  /** ID */
  id: number;

  /** 昵称 */
  nickname?: string;

  /** 状态 1可用 2不可用 */
  status?: EnumUserEditModelStatus;
}

export interface UserChangePasswordModel {
  /** 新密码 */
  new_password: string;
}

export interface SubjectPageQueryModel {
  /** id 列表 */
  ids?: number[];

  /** 页码 */
  page_num?: number;

  /** 分页大小 */
  page_size?: number;

  /** id */
  id?: number;

  /** 编号 */
  code?: string;

  /** 姓名 */
  name?: string;

  /** 性别 */
  sex?: number;

  /** 生日 */
  birth_date?: number;

  /** 手机号 */
  phone?: string;

  /** 查询关键词 */
  keyword?: string;
}

export interface PageModelSubjectModel {
  /** 总数 */
  total: number;

  /** 每页显示条数，默认 10 */
  size: number;

  /** 当前页 */
  current?: number;

  /** 分页记录列表 */
  records: SubjectModel[];
}

export interface SubjectModel {
  /** id */
  id: number;

  /** 编号 */
  code: string;

  /** 姓名 */
  name?: string;

  /** 性别 0.未知 1.男 2.女 3.其他 */
  sex?: number;

  /** 生日 */
  birth_date?: number;

  /** 手机号 */
  phone?: string;

  /** 病情描述 */
  condition_desc?: string;

  /** 运动阈值 */
  motion_threshold?: number;

  /** 方案数量 */
  plan_count?: number;

  /** 治疗次数 */
  treatment_count?: number;

  /** 操作人 */
  created_user_name?: string;

  /** 备注 */
  remark?: string;

  /** 创建人ID */
  created_id?: number;

  /** 修改人ID */
  updated_id?: number;

  /** 创建时间 */
  created_at?: number;

  /** 更新时间 */
  updated_at?: number;

  /** 水平旋转角 */
  horizontal?: number;

  pinyin_username?: string;
}

export interface MotionThresholdModel {
  /** id */
  subject_id: number;
  plan_id: number;
  /** 运动阈值 */
  motion_threshold?: number;
  plan_id: number;
  horizontal_list?: {
    id?: number;
    remark?: string;
    target_id: number;
    horizontal: number;
  }[];
}


/**
 * 新建刺激方案的传参
 */
export interface AddStimulusParams {
  /** 刺激类型 1.SINGLE(单刺激) 2.rTMS(重复刺激) 3.iTBS(刺激间歇性的TBS) 4.cTBS(刺激连续的丛状刺激称) 5.TBS(爆发式刺激) */
  type: number;
  /** 实际强度 */
  actual_strength: number;
  /** 名称*/
  name?: string;
  /** 串脉冲频率 */
  strand_pulse_frequency?: number;
  /** 串内脉冲数 */
  inner_strand_pulse_count?: number;
  /** 丛内频率 */
  plexus_inner_frequency?: number;
  /** 丛间频率 */
  plexus_inter_frequency?: number;
  /** 丛内脉冲数 */
  plexus_inner_pulse_count?: number;
  /** 刺激丛数 */
  plexus_count?: number;
  /** 脉冲串数 */
  strand_pulse_count?: number;
  /** 刺激间隔 */
  intermission_time?: number;
  /** 脉冲数(前端计算出的值) */
  pulse_total?: number;
  /** 刺激总时长 */
  treatment_time?: number;
}

/**
 * 更新刺激列表的入参约束
 */
export interface UpdateStimulateParams {
  continue_stimulus_id_list: number[];
}

/**
 * 刺激方案列表的对象返参
 */
export interface StimulusItemModel {
  /** 名称*/
  name: string;
  /** 刺激类型*/
  type: EnumPlanStimulusType;
  /** 刺激的id */
  id: number;
  /** 实际强度 */
  actual_strength: number;
  /** 串内脉冲数 */
  inner_strand_pulse_count?: number;
  /** 刺激丛数 */
  plexus_count: number;
  /** 刺激间隔 */
  intermission_time: number;
  /** 丛内频率 */
  plexus_inner_frequency: number;
  /** 丛内脉冲数 */
  plexus_inner_pulse_count: number;
  /** 丛间频率 */
  plexus_inter_frequency: number;
  /** 脉冲串数 */
  strand_pulse_count: number;
  /** 刺激总时长 */
  treatment_time: number;
  /** 脉冲数 */
  pulse_total?: number;
  /** 串脉冲频率 */
  strand_pulse_frequency?: number;
  /** 创建的时间戳*/
  created_at?: number;
  /** 创建者的id*/
  created_id?: number;
  /** 排序的位置*/
  sort_index?: number;
  /** trace_id*/
  trace_id?: string;
  /** 更新的时间戳*/
  updated_at?: number;
  /** 更新的id*/
  updated_id?: number;
  /** 刺激的次数*/
  treat_count?: number;
}

export interface SubjectQueryModel {
  /** id */
  id?: number;

  /** 编号 */
  code?: string;

  /** 姓名 */
  name?: string;

  /** 性别 */
  sex?: number;

  /** 生日 */
  birth_date?: number;

  /** 手机号 */
  phone?: string;
}

export interface StimulusTemplatePageQueryModel {
  /** id 列表 */
  ids?: number[];

  /** 页码 */
  page_num: number;

  /** 分页大小 */
  page_size: number;

  /** id */
  id?: number;

  /** 刺激模版名称 */
  name?: string;

  /** 刺激类型列表 1.SINGLE(单刺激) 2.rTMS(重复刺激) 3.iTBS(刺激间歇性的TBS) 4.cTBS(刺激连续的丛状刺激称) 5.TBS(爆发式刺激) */
  stimulus_type_enum_list?: EnumPlanStimulusType[];

  /** 创建人ID */
  created_id?: number;

  sort_by?: EnumSortType;
}

export type PageModelStimulusTemplateModelApi = Required<
  PageModelStimulusTemplateModel & {
    records: StimulusTemplateModelApi[];
  }
>;
export type StimulusTemplateModelApi = Merge<
  Required<StimulusTemplateModel>,
  {
    type: EnumPlanStimulusType;
    strength?: number;
    isNotComplete: boolean;
    created_user_name: string;
    actual_strength?: number;
    intermission_time?: number;
    target_id?: number;
  }
>;

export type CreateStimulusTemplateModelApi = Merge<
  StimulusTemplateModelApi,
  {
    id?: number;
    type: EnumPlanStimulusType;
    strength?: number;
  }
>;

export interface PageModelStimulusTemplateModel {
  /** 总数 */
  total?: number;

  /** 每页显示条数，默认 10 */
  size?: number;

  /** 当前页 */
  current?: number;

  /** 分页记录列表 */
  records: StimulusTemplateModel[];
}

export interface StimulusTemplateModel {
  /** id */
  id: number;

  /** 备注 */
  remark?: string;

  /** 调用链ID */
  trace_id?: string;

  /** 创建人ID */
  created_id?: number;

  /** 修改人ID */
  updated_id?: number;

  /** 创建时间 */
  created_at?: number;

  /** 更新时间 */
  updated_at?: number;

  /** 刺激模版名称 rTMS,iTBS,cTBS */
  name: string;

  /** 刺激类型 1.SINGLE(单刺激) 2.rTMS(重复刺激) 3.iTBS(刺激间歇性的TBS) 4.cTBS(刺激连续的丛状刺激称) 5.TBS(爆发式刺激) */
  type: number;

  /** 相对强度 rTMS,iTBS,cTBS */
  relative_strength: number;

  /** 串脉冲频率 rTMS 0.1-100.0 保留一位小数 */
  strand_pulse_frequency?: number;

  /** 串内脉冲数 rTMS */
  inner_strand_pulse_count?: number;

  /** 丛内频率 iTBS,cTBS */
  plexus_inner_frequency?: number;

  /** 丛间频率 iTBS,cTBS 0.1-25.0 保留一位小数 */
  plexus_inter_frequency?: number;

  /** 丛内脉冲数 iTBS,cTBS */
  plexus_inner_pulse_count?: number;

  /** 刺激丛数 iTBS,cTBS */
  plexus_count?: number;

  /** 脉冲串数 rTMS,iTBS,cTBS */
  strand_pulse_count: number;

  /** 刺激间隔 rTMS,iTBS,cTBS */
  intermission_time?: number;

  /** 总脉冲数(前端计算出的值) rTMS,iTBS,cTBS */
  pulse_total?: number;

  /** 治疗时间(前端计算出的值) rTMS,iTBS,cTBS */
  treatment_time?: number;

  /** 操作人 */
  created_user_name?: string;
}

export interface StimulusTemplateQueryModel {
  /** id */
  id?: number;

  /** 刺激模版名称 */
  name?: string;

  /** 刺激类型列表 1.SINGLE(单刺激) 2.rTMS(重复刺激) 3.iTBS(刺激间歇性的TBS) 4.cTBS(刺激连续的丛状刺激称) 5.TBS(爆发式刺激) */
  stimulus_type_enum_list?: EnumStimulusTemplateQueryModelStimulusTypeEnumList[];

  /** 创建人ID */
  created_id?: number;
}

export interface NormalLineCoordinateModel {
  /**  */
  y: number;

  /**  */
  x: number;

  /**  */
  z: number;

  /** 法线的版本 */
  ver: number;
}

export interface PlanTargetNormalLineModel {
  /** 患者id */
  subject_id: number;

  /** 方案id */
  plan_id: number;

  /**  */
  normal_line_list: (NormalLineCoordinateModel & { target_id: number })[];
}

export interface CoordinateModel {
  /**  */
  y: number;

  /**  */
  x: number;

  /**  */
  z: number;
}

export interface PlanReportModel {
  /** ID */
  id?: number;

  /** 备注 */
  remark?: string;

  /** 调用链ID */
  trace_id?: string;

  /** 创建人ID */
  created_id?: number;

  /** 修改人ID */
  updated_id?: number;

  /** 创建时间 */
  created_at?: number;

  /** 更新时间 */
  updated_at?: number;

  /** uuid */
  uuid: string;

  /** 方案id */
  plan_id: number;

  /** 患者id */
  subject_id: number;

  /** 靶点id */
  plan_target_id: number;

  /** 刺激id */
  plan_stimulus_id: number;

  /** 刺激开始时间 */
  stimulus_start_time?: number;

  /** 刺激结束时间 开始刺激时，值为空 */
  stimulus_end_time?: number;

  /** 实际刺激时长（不含暂停）单位秒 */
  actual_stimulation_duration?: number;

  /** 预计刺激时长 单位秒 */
  planned_stimulation_duration: number;

  /** 运动阈值 范围1~100 */
  motion_threshold: number;

  /**  */
  target_data: TargetData;

  /**  */
  stimulus_data: StimulusData;

  /** 刺激强度和时间列表 */
  strength_data_list?: StrengthData[];

  /** 报告类型 1不完整 2完整  */
  type?: number;
}

export interface StimulusData {
  /** 刺激类型 1.SINGLE(单刺激) 2.rTMS(重复刺激) 3.iTBS(刺激间歇性的TBS) 4.cTBS(刺激连续的丛状刺激称) 5.TBS(爆发式刺激) */
  type: number;

  /** 相对强度 */
  relative_strength: number;

  /** 实际强度 */
  actual_strength: number;

  /** 串脉冲频率 */
  strand_pulse_frequency?: number;

  /** 串内脉冲数 */
  inner_strand_pulse_count?: number;

  /** 丛内频率 */
  plexus_inner_frequency?: number;

  /** 丛间频率 */
  plexus_inter_frequency?: number;

  /** 丛内脉冲数 */
  plexus_inner_pulse_count?: number;

  /** 刺激丛数 */
  plexus_count?: number;

  /** 脉冲串数 */
  strand_pulse_count?: number;

  /** 刺激间隔 */
  intermission_time?: number;

  /** 脉冲数(前端计算出的值) */
  pulse_total?: number;
}

export interface StrengthData {
  /** 实际强度 */
  actual_strength?: number;

  /** 刺激时间 */
  stimulus_time?: number;
}

export interface TargetData {
  /** 角顶编号 */
  code: string;

  /**  */
  vol_seed: CoordinateModel;

  /** 靶点名称 */
  targetName: string;
}

export interface PlanReportSimpleModel {
  /** uuid */
  uuid: string;

  /** 类型 1 不完整 half; 2 完整 complete */
  type?: number;

  /** 实际强度 */
  actual_strength?: number;
}

export interface PlanRegisterModel {
  /** ID */
  id?: number;

  /** 备注 */
  remark?: string;

  /** 调用链ID */
  trace_id?: string;

  /** 创建人ID */
  created_id?: number;

  /** 修改人ID */
  updated_id?: number;

  /** 创建时间 */
  created_at?: number;

  /** 更新时间 */
  updated_at?: number;

  /** 患者id */
  subject_id: number;

  /** 方案id */
  plan_id: number;

  /** 头部矩阵 */
  head_matrix?: number[];

  /** 拍子矩阵 */
  bat_matrix?: number[];

  /** 注册靶点 */
  target?: Target[];

  /** 最近的牌子注册时间 */
  last_bat_time?: number;
}

export interface Target {
  /** 靶点名称 */
  name: string;

  /** 顶角编号 */
  vertex: string;

  /**  */
  vol_ras: CoordinateModel;

  /**  */
  suf_ras: CoordinateModel;
}

export interface PlanPreviewParamModel {
  /** ng文件的路径 */
  ng_file: string;
}

export interface PlanPreviewModel {
  /**  */
  subject?: SubjectModel;

  /** 上传文件名称 */
  file_name?: string;

  /** 临时目录 */
  temp_directory?: string;
}

export interface PlanQueryPageModel {
  /** id 列表 */
  ids?: number[];

  /** 页码 */
  page_num?: number;

  /** 分页大小 */
  page_size?: number;

  /** 搜索名称 */
  keyword?: string;

  /** 患者id */
  subject_id?: number;

  /** 方案类型 1.noImage(无影像) 2.withImage(有影像) */
  type_list?: EnumPlanQueryPageModelTypeList[];

  /** 方案状态 1.Pending(待完善) 2.Normal(已完善) */
  status_list?: EnumPlanQueryPageModelStatusList[];
}

export interface PageModelPlanModel {
  /** 总数 */
  total?: number;

  /** 每页显示条数，默认 10 */
  size?: number;

  /** 当前页 */
  current?: number;

  /** 分页记录列表 */
  records: PlanModel[];
}

export interface PlanFileModel {
  /** ID */
  id?: number;

  /** 备注 */
  remark?: string;

  /** 调用链ID */
  trace_id?: string;

  /** 创建人ID */
  created_id?: number;

  /** 修改人ID */
  updated_id?: number;

  /** 创建时间 */
  created_at?: number;

  /** 更新时间 */
  updated_at?: number;

  /** 患者id */
  subject_id?: number;

  /** 方案id */
  plan_id?: number;

  /** 文件名称 */
  name?: string;

  /** 文件相对路径 */
  relative_path?: string;
}

export interface PlanImportModel {
  /** 患者ID */
  subject_id: number;

  /** 方案名称 */
  plan_name: string;

  /** 临时目录 */
  temp_directory: string;

  /** 文件名称 */
  file_name: string;
}

export interface PlanImportInfoModel {
  /** ng文件摘要 */
  file_sha256: string;

  /** 文件来源 */
  file_source: string;

  /** 文件版本 */
  file_version: string;

  /** 云端方案id */
  import_id: string;
}

export interface PlanModel {
  /** id */
  id: number;

  /** 备注 */
  remark?: string;

  /** 是否有MEP靶点 */
  has_mep?: boolean;

  /** 调用链ID */
  trace_id?: string;

  /** 创建人ID */
  created_id?: number;

  /** 修改人ID */
  updated_id?: number;

  /** 创建时间 */
  created_at?: number;

  /** 更新时间 */
  updated_at?: number;

  /** 患者id */
  subject_id: number;

  /** 方案名称 */
  name?: string;

  /** 方案类型 1.noImage(无影像) 2.withImage(有影像) */
  type?: number;

  /** 方案状态 1.Pending(待完善) 2.Normal(已完善) */
  status?: number;

  /** 靶点数量 */
  target_count?: number;

  /** 治疗次数 */
  treatment_count?: number;

  /** 第一次治疗时间 */
  created_treatment_at?: number;

  /** 最后一次治疗时间 */
  updated_treatment_at?: number;

  /**  */
  stimulus: PlanStimulusModel;

  /**  */
  subject_model: SubjectModel;

  /** 方案靶点数据 */
  plan_target_model_list?: PlanTargetModel[];

  /** 需要删除靶点的id集合 */
  plan_delete_target_id_list?: number[];

  /**  */
  plan_import_model?: PlanImportModel;

  /**  */
  plan_import_info_model?: PlanImportInfoModel;

  /** 方案文件数据 */
  plan_file_model_list?: PlanFileModel[];

  /** 日志条数 */
  report_count: number;
}

export interface PlanTargetModel {
  /** ID */
  id?: number;

  /** 备注 */
  remark?: string;

  /** 调用链ID */
  trace_id?: string;

  /** 创建人ID */
  created_id?: number;

  /** 修改人ID */
  updated_id?: number;

  /** 创建时间 */
  created_at?: number;

  /** 更新时间 */
  updated_at?: number;

  /** 患者id */
  subject_id?: number;

  /** 方案id */
  plan_id?: number;

  /** 顶角编号 */
  code?: string;

  /** 靶点名称 */
  name: string;

  /** 水平角度 */
  horizontal?: number;

  /**  */
  normal_line?: NormalLineCoordinateModel;

  /** 来源 */
  source?: EnumPlanTargetModelSource;

  /** 是否MEP靶点 */
  has_mep?: boolean;

  /** 靶点分布 */
  hemi: EnumPlanTargetModelHemi;

  /** 靶点类型(算法数据不建枚举) */
  type?: string;

  /** 角顶索引 */
  vertex_index: number;

  /**  */
  vol_ras: CoordinateModel;

  /**  */
  surf_ras: CoordinateModel;

  /**  */
  voxel_seed?: CoordinateModel;

  /** 分数排序 */
  score_index?: number;

  /** 分数 */
  score?: number;

  /**  */
  stimulus?: PlanStimulusModel;

  disable_horizontal?: boolean;

  color?: string;
}

export interface PlanLogQueryModel {
  /** id 列表 */
  ids?: number[];

  /** 页码 */
  page_num?: number;

  /** 分页大小 */
  page_size?: number;

  /** 患者id */
  subject_id?: number;

  /** 方案id */
  plan_id?: number;

  /** 方案报告id */
  plan_report_id?: number;

  /** 日志类型: 1.CREATE_TASK(创建任务) 2.CREATE_PLAN(创建方案) 3.EDIT_PLAN(编辑方案) 4.CREATE_PLAN(开始刺激) 5.PAUSE_STIMULATION(暂停刺激) 6.CONTINUE_STIMULATION(继续刺激) 7.STOP_STIMULATION(停止刺激) */
  type_list?: EnumPlanLogQueryModelTypeList[];
}

export interface BaseModel {
  /** ID */
  id?: number;

  /** 备注 */
  remark?: string;

  /** 调用链ID */
  trace_id?: string;

  /** 创建人ID */
  created_id?: number;

  /** 修改人ID */
  updated_id?: number;

  /** 创建时间 */
  created_at?: number;

  /** 更新时间 */
  updated_at?: number;
}

export interface PageModelPlanLogModel {
  /** 总数 */
  total?: number;

  /** 每页显示条数，默认 10 */
  size?: number;

  /** 当前页 */
  current?: number;

  /** 分页记录列表 */
  records: PlanLogModel[];
}

export interface PlanLogDetailModel {
  /** ID */
  id?: number;

  /** 备注 */
  remark?: string;

  /** 调用链ID */
  trace_id?: string;

  /** 创建人ID */
  created_id?: number;

  /** 修改人ID */
  updated_id?: number;

  /** 创建时间 */
  created_at?: number;

  /** 更新时间 */
  updated_at?: number;

  /** 方案日志id */
  plan_log_id?: number;

  /** 患者id */
  subject_id?: number;

  /** 方案id */
  plan_id?: number;

  /** 方案靶点id */
  plan_target_id?: number;

  /** 方案刺激参数id */
  plan_stimulus_id?: number;

  /** 方案注册id */
  plan_register_id?: number;

  /** 方案id */
  has_delete?: boolean;

  /** 日志类型 1.plan(方案) 2.plan_target(方案靶点) 3.plan_stimulus(方案刺激参数) */
  type?: number;

  /**  */
  data?: BaseModel;
}

export interface PlanLogModel {
  /** ID */
  id?: number;

  /** 备注 */
  remark?: string;

  /** 调用链ID */
  trace_id?: string;

  /** 创建人ID */
  created_id?: number;

  /** 修改人ID */
  updated_id?: number;

  /** 创建时间 */
  created_at?: number;

  /** 更新时间 */
  updated_at?: number;

  /** 患者id */
  subject_id?: number;

  /** 方案id */
  plan_id?: number;

  /** 方案报告id */
  plan_report_id?: number;

  /** 日志类型: 1.CREATE_TASK(创建任务) 2.CREATE_PLAN(创建方案) 3.EDIT_PLAN(编辑方案) 4.CREATE_PLAN(开始刺激) 5.PAUSE_STIMULATION(暂停刺激) 6.CONTINUE_STIMULATION(继续刺激) 7.STOP_STIMULATION(停止刺激) */
  type?: number;

  /** 方案日志详情 */
  plan_log_detail_model_list?: PlanLogDetailModel[];

  /** 操作人 */
  created_user_name?: string;

  /** 刺激暂停到刺激开始的暂停时长 单位秒 */
  pause_duration?: number;

  /** 刺激报告终止类型  */
  plan_report_type?: EnumPlanLogModelPlanReportType;
}

export interface FrontConfigModel {
  /** id */
  id?: number;

  /** 组名称 */
  group_name: string;

  /** 配置名称 */
  name: string;

  /** 配置值 */
  value: string;

  /** 只读 */
  read_only?: boolean;

  /** 备注 */
  remark?: string;
}

export interface FrontConfigQueryModel {
  /** 组名称 */
  group_name?: string;

  /** 配置名称 */
  name?: string;
}

export interface BuildAndGitPropertiesModel {
  /** 内部版本号 */
  version?: string;

  /**  */
  build_artifact?: string;

  /**  */
  build_group?: string;

  /**  */
  build_name?: string;

  /**  */
  build_time?: string;

  /**  */
  build_version?: string;

  /**  */
  git_branch?: string;

  /**  */
  git_build_host?: string;

  /**  */
  git_build_user_email?: string;

  /**  */
  git_build_user_name?: string;

  /**  */
  git_build_version?: string;

  /**  */
  git_closest_tag_commit_count?: string;

  /**  */
  git_closest_tag_name?: string;

  /**  */
  git_commit_id?: string;

  /**  */
  git_commit_id_abbrev?: string;

  /**  */
  git_commit_id_describe?: string;

  /**  */
  git_commit_message_full?: string;

  /**  */
  git_commit_message_short?: string;

  /**  */
  git_commit_time?: string;

  /**  */
  git_commit_user_email?: string;

  /**  */
  git_commit_username?: string;

  /**  */
  git_dirty?: string;

  /**  */
  git_remote_origin_url?: string;

  /**  */
  git_tags?: string;

  /**  */
  git_total_commit_count?: string;
}

// 登录 和账号相关
export interface LoginParams {
  username: string;
  password: string;
  language: string;
}

export interface LoginDto {
  username: string;
  password: string;
}

export type RoleType = {
  id: number;
  code: string;
  name: string;
  create_id: number;
  update_id: number;
  authority_list: {
    id: number;
    code: string;
    name: string;
    create_id: number;
    update_id: number;
  }[];
};
export type LoginRes = {
  user_id: number;
  role_id: EnumUserPageQueryModelRoleEnumList;
  should_change_password: boolean;
  id: number;
  authorization: string;
  username: string;
  password: string;
  role: RoleType;
  nickname: string;
  status: string;
  create_id: number;
  update_id: number;
};
export type JwtPayload = {
  userId: number;
  refills: number;
};

export interface ISystemErrorInfo {
  key: string;
  createAt: Date;
  content: string;
  suffix?: string;
}

export type UserSession = LoginRes & {
  jwtToken: string;
  language?: string;
};

export type AuthenticateResult = {
  userSession?: UserSession;
  apiStatus?: ApiStatus;
};
export declare type LoginDTO = {
  email: string;
  password: string;
};
// 接口报错相关
export type ApiError = {
  code?: string;
  status?: number;
  message?: string;
};

export type ApiStatus = {
  loading: boolean;
  error?: ApiError;
  status?: number;
};

export enum SystemOperatAction {
  Shutdown = 'Shutdown',
  Restart = 'Restart',
  CloseApp = 'CloseApp',
}

// 关机 重启相关
export type SystemOperationInfo = {
  action: SystemOperatAction;
  showConfirmDialog?: boolean;
  haveCuring?: boolean;
};

// 上传选择文件类型有关
export enum ChooseType {
  File = 'File',
  Folder = 'Folder',
  Zip = 'Zip',
  Magnetic = 'Magnetic',
  // Envelope = 'Envelope',
}

// M200 产品线
export enum EnumPlanStimulusType {
  /**
   * 单刺激
   */
  SINGLE = 1,

  /**
   * 重复刺激
   */
  RTMS = 2,

  /**
   * 刺激间歇性的TBS
   */
  ITBS = 3,

  /**
   * 刺激连续的丛状刺激称
   */
  CTBS = 4,

  /**
   * 爆发式刺激
   */
  TBS = 5,
}

export type PlanStimulusModel = {
  /** 备注 */
  remark?: string;

  /** 调用链ID */
  trace_id?: string;

  /** 创建人ID */
  created_id?: number;

  /** 修改人ID */
  updated_id?: number;

  /** 创建时间 */
  created_at?: number;

  /** 更新时间 */
  updated_at?: number;

  /** 患者id */
  subject_id?: number;

  /** 方案id */
  plan_id?: number;

  /** 靶点id */
  target_id?: number;

  /** 刺激来源 1.PLAN(方案) 2.TARGET(靶点) */
  source?: number;
  /** ID */
  id?: number;

  /** 名称 */
  name?: string;

  /** 刺激类型 1.SINGLE(单刺激) 2.rTMS(重复刺激) 3.iTBS(刺激间歇性的TBS) 4.cTBS(刺激连续的丛状刺激称) 5.TBS(爆发式刺激) */
  type: EnumPlanStimulusType;

  /** 相对强度 */
  // eslint-disable-next-line camelcase
  relative_strength: number;

  /** 串脉冲频率 */
  // eslint-disable-next-line camelcase
  strand_pulse_frequency?: number;

  /** 串内脉冲数 */
  inner_strand_pulse_count?: number;

  /** 丛内频率 */
  plexus_inner_frequency?: number;

  /** 丛间频率 */
  plexus_inter_frequency?: number;

  /** 丛内脉冲数 */
  plexus_inner_pulse_count?: number;

  /** 刺激丛数 */
  plexus_count?: number;

  /** 脉冲串数 */
  strand_pulse_count?: number;

  /** 刺激间隔 */
  intermission_time?: number;

  /** 脉冲数(前端计算出的值) */
  pulse_total: number;

  /** 治疗时间(前端计算出的值) */
  treatment_time: number;
};

export enum EnumUserPageQueryModelRoleEnumList {
  TechSupport = 1001,
  Admin = 1002,
  User = 1003,
}

export enum EnumUserEditModelStatus {
  'KEY_1' = '1',
  'KEY_2' = '2',
}

export enum EnumStimulusTemplatePageQueryModelStimulusTypeEnumList {
  'KEY_1' = '1',
  'KEY_2' = '2',
  'KEY_3' = '3',
  'KEY_4' = '4',
  'KEY_5' = '5',
}

export enum EnumStimulusTemplateQueryModelStimulusTypeEnumList {
  'KEY_1' = '1',
  'KEY_2' = '2',
  'KEY_3' = '3',
  'KEY_4' = '4',
  'KEY_5' = '5',
}

export enum EnumPlanQueryPageModelTypeList {
  'KEY_1' = '1',
  'KEY_2' = '2',
}

export enum EnumPlanQueryPageModelStatusList {
  'KEY_1' = '1',
  'KEY_2' = '2',
}

export enum EnumPlanTargetModelSource {
  'KEY_0' = '0',
  'KEY_1' = '1',
  'KEY_2' = '2',
}

export enum EnumPlanTargetModelHemi {
  'rh' = 'rh',
  'lh' = 'lh',
}

export enum EnumPlanLogQueryModelTypeList {
  'KEY_0' = '0',
  'KEY_1' = '1',
  'KEY_2' = '2',
  'KEY_3' = '3',
  'KEY_4' = '4',
  'KEY_5' = '5',
  'KEY_6' = '6',
  'KEY_7' = '7',
}

export enum EnumPlanLogModelPlanReportType {
  'KEY_0' = '0',
  'KEY_1' = '1',
  'KEY_2' = '2',
  'KEY_3' = '3',
}

export enum EnumSortType {
  'ASC' = '1',
  'DESC' = '2',
}

// /api/page 方案列表请求类型
export interface IPlanListParams {
  /**
   * 是否已归档  true 归档数据 false 未归档数据
   */
  has_archive?: boolean;
  /**
   * id 列表
   */
  ids?: number[];
  /**
   * 搜索名称
   */
  keyword?: string;
  /**
   * 页码
   */
  page_num?: number;
  /**
   * 分页大小
   */
  page_size?: number;
  /**
   * 排序方式 1.CreatedAtAsc(创建时间正序) 2.CreatedAtDesc(创建时间倒序) 3.UpdatedTreatmentAtAsc(最近治疗时间正序)
   * 4.UpdatedTreatmentAtDesc(最近治疗时间倒序).  不填按创建时间倒序
   */
  sort_by?: SortBy;
  /**
   * 方案状态 1.Pending(待完善) 2.Normal(已完善)
   */
  status_list?: EnumList[];
  /**
   * 患者id
   */
  subject_id?: number;
  /**
   * 方案类型 1.noImage(无影像) 2.withImage(有影像)
   */
  type_list?: EnumList[];
  /**
   * 最近治疗时间范围 0.NoLimit(不限制) 1.OneWeek(一周内) 2.OneMonth(一个月内) 3.ThreeMonth(三个月内). 不填为不限制
   */
  updated_treatment_at_range?: UpdatedTreatmentAtRange;
  // 是否重新请求接口
  noFetch?: boolean;
}

/**
 * 排序方式 1.CreatedAtAsc(创建时间正序) 2.CreatedAtDesc(创建时间倒序) 3.UpdatedTreatmentAtAsc(最近治疗时间正序)
 * 4.UpdatedTreatmentAtDesc(最近治疗时间倒序).  不填按创建时间倒序
 */
export enum SortBy {
  The1 = '1',
  The2 = '2',
  The3 = '3',
  The4 = '4',
}

export const createSortMap = {
  ascend: SortBy.The1,
  descend: SortBy.The2,
};
export const updateSortMap = {
  ascend: SortBy.The3,
  descend: SortBy.The4,
};

/**
 * 方案状态 1.Pending(待完善) 2.Normal(已完善)
 *
 * 方案类型 1.noImage(无影像) 2.withImage(有影像)
 */
export enum EnumList {
  The1 = '1',
  The2 = '2',
}

/**
 * 最近治疗时间范围 0.NoLimit(不限制) 1.OneWeek(一周内) 2.OneMonth(一个月内) 3.ThreeMonth(三个月内). 不填为不限制
 */
export enum UpdatedTreatmentAtRange {
  The0 = '0',
  The1 = '1',
  The2 = '2',
  The3 = '3',
}

/**
 * ArchiveModel
 */
export interface IArchiveRequest {
  /**
   * 归档 false 不归档 视为还原 true 归档
   */
  has_archive: boolean;
  /**
   * 方案ID
   */
  plan_id: number;
}

// 方案列表响应
export interface IPlanListResponse {
  all_total: number;
  total: number;
  size: number;
  current: number;
  records: PlanModel[];
}

export interface IBox {
  box_host: string;
}

// 工作站信息
export type IBoxInfo = Partial<IBox>;

export interface IBindingBoxParams extends IBox {
  username: string;
  password: string;
}

export interface IResetFactoryParams {
  validate_only: boolean;
  password: string;
}

/**
 * SubjectModel，患者模型
 */

export interface Setting {
  REACT_APP_NG_API_BASEURL: string;
  GATEWAY_API_BASEURL: string;
  REACT_APP_REQUEST_HEADERS_ORIGIN: string;
  REACT_APP_SHOW_APP_MENU: string;
  REACT_APP_LOG_DEBUG: string;
  GIT_COMMIT_SHORT_SHA: string;
  IMAGE_SOCKET_URL: string;
  PDU_SOCKET_URL: string;
  TMS_SOCKET_URL: string;
  LOG_API_BASEURL: string;
  WEB_ORIGIN_URL: string;
  TOOL_BECAXYZ?: [];
  TOOL_CAXYZ?: [];
  CHECK_NODE_EXPORT_BASEURL: string; // 硬盘url
  CHECK_SMART_BASEURL: string; // raidurl
  SURFACE_GRID: string; // 是否展示坐标网格
  REPORT_PASSWORD: string; // 日志密码
}

export interface ISubjectResponse {
  /**
   * 生日
   */
  birth_date?: number;
  /**
   * 编号
   */
  code: string;
  /**
   * 病情描述
   */
  condition_desc?: string;
  /**
   * 创建时间
   */
  created_at?: number;
  /**
   * 创建人ID
   */
  created_id?: number;
  /**
   * 是否是 demo 数据 false正常数据 true demo 数据
   */
  has_demo?: boolean;
  /**
   * id
   */
  id: number;
  /**
   * importId
   */
  import_id?: number;
  /**
   * 运动阈值
   */
  motion_threshold?: number;
  /**
   * 姓名
   */
  name: string;
  /**
   * 手机号
   */
  phone?: string;
  /**
   * 姓名拼音
   */
  pinyin_username: string;
  /**
   * 方案数量
   */
  plan_count?: number;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 性别 0.未知 1.男 2.女 3.其他
   */
  sex?: number;
  /**
   * 调用链ID
   */
  trace_id?: string;
  /**
   * 治疗次数
   */
  treatment_count?: number;
  /**
   * 更新时间
   */
  updated_at?: number;
  /**
   * 修改人ID
   */
  updated_id?: number;
}

/**
 * PlanReportModel，方案治疗报告模型
 */
export interface IReportModel {
  /**
   * 实际刺激时长（不含暂停）单位秒
   */
  actual_stimulation_duration: number;

  type_list: number[];
  stimulus_start_time: number;
  actual_stimuluation_duration: number;
  stimulus_pause_count: number;
  /**
   * 创建时间
   */
  created_at?: number;
  /**
   * 创建人ID
   */
  created_id?: number;
  /**
   * ID
   */
  id?: number;
  /**
   * 运动阈值 范围1~100
   */
  motion_threshold: number;
  /**
   * 方案id
   */
  plan_id: number;
  /**
   * 刺激id
   */
  plan_stimulus_id: number;
  /**
   * 靶点id
   */
  plan_target_id: number;
  /**
   * 预计刺激时长 单位秒
   */
  planned_stimulation_duration: number;
  /**
   * 备注
   */
  remark?: string;
  stimulus_data: StimulusData;
  /**
   * 刺激结束时间 开始刺激时，值为空
   */
  stimulus_end_time?: number;
  /**
   * 刺激开始时间
   */
  stimulus_start_time?: number;
  /**
   * 刺激强度和时间列表
   */
  strength_data_list?: StrengthData[];
  /**
   * 患者id
   */
  subject_id: number;
  target_data: TargetData;
  /**
   * 调用链ID
   */
  trace_id?: string;
  /**
   * 报告类型 1不完整 2完整
   */
  type?: number;
  /**
   * 更新时间
   */
  updated_at?: number;
  /**
   * 修改人ID
   */
  updated_id?: number;
  /**
   * uuid
   */
  uuid: string;
}

/**
 * StimulusData，刺激参数
 */
export interface StimulusData {
  /**
   * 实际强度
   */
  actual_strength: number;
  /**
   * 串内脉冲数
   */
  inner_strand_pulse_count?: number;
  /**
   * 刺激间隔
   */
  intermission_time?: number;
  /**
   * 刺激丛数
   */
  plexus_count?: number;
  /**
   * 丛内频率
   */
  plexus_inner_frequency?: number;
  /**
   * 丛内脉冲数
   */
  plexus_inner_pulse_count?: number;
  /**
   * 丛间频率
   */
  plexus_inter_frequency?: number;
  /**
   * 脉冲数(前端计算出的值)
   */
  pulse_total?: number;
  /**
   * 相对强度
   */
  relative_strength: number;
  /**
   * 脉冲串数
   */
  strand_pulse_count?: number;
  /**
   * 串脉冲频率
   */
  strand_pulse_frequency?: number;
  /**
   * 刺激类型 1.SINGLE(单刺激) 2.rTMS(重复刺激) 3.iTBS(刺激间歇性的TBS) 4.cTBS(刺激连续的丛状刺激称) 5.TBS(爆发式刺激)
   */
  type: number;
}

/**
 * StrengthData，刺激强度和时间
 */
export interface StrengthData {
  /**
   * 实际强度
   */
  actual_strength?: number;
  /**
   * 刺激时间
   */
  stimulus_time?: number;
}

/**
 * TargetData，靶点信息
 */
export interface TargetData {
  /**
   * 角顶编号
   */
  code: string;
  /**
   * 靶点名称
   */
  target_name: string;
  vol_ras: CoordinateModel;
}

/**
 * CoordinateModel，靶点坐标
 */
export interface CoordinateModel {
  x: number;
  y: number;
  z: number;
}

/**
 * UserPageQueryModel
 */
export interface IGetUserListRequest {
  /**
   * id 列表
   */
  ids?: number[];
  /**
   * 页码
   */
  page_num?: number;
  /**
   * 分页大小
   */
  page_size?: number;
}

/**
 * 查询角色列表
 */
export enum RoleEnumList {
  'TechSupport' = '1001', // 技术支持
  'Admin' = '1002', // 管理员
  'User' = '1003', // 用户
}

/**
 * PageModelUserModel，分页返回模型
 */
export interface IUserListResponse {
  /**
   * 当前页
   */
  current?: number;
  /**
   * 分页记录列表
   */
  records?: UserModel[];
  /**
   * 每页显示条数，默认 10
   */
  size?: number;
  /**
   * 总数
   */
  total?: number;
}

/**
 * UserModel，分页记录列表
 */
export interface UserModel {
  /**
   * 授权信息
   */
  authorization?: string;
  /**
   * 创建人
   */
  create_id?: number;
  /**
   * ID
   */
  id?: number;
  /**
   * 最后一次登入时间
   */
  last_login_at?: Date;
  /**
   * 昵称
   */
  nickname: string;
  /**
   * 密码
   */
  password: string;
  role?: RoleModel;
  /**
   * 角色ID
   */
  role_id?: number;
  /**
   * 是否需要修改密码
   */
  should_change_password?: boolean;
  /**
   * 状态 1可用 2不可用
   */
  status?: number;
  /**
   * 修改人
   */
  update_id?: number;
  /**
   * 用户ID
   */
  user_id?: number;
  /**
   * 用户名
   */
  username: string;
}

/**
 * RoleModel，角色信息
 */
export interface RoleModel {
  /**
   * 权限列表
   */
  authority_list?: AuthorityModel[];
  /**
   * 角色code
   */
  code?: string;
  /**
   * 创建人
   */
  create_id?: number;
  /**
   * 角色ID
   */
  id?: number;
  /**
   * 角色名称
   */
  name?: string;
  /**
   * 修改人
   */
  update_id?: number;
}

/**
 * AuthorityModel，权限列表
 */
export interface AuthorityModel {
  /**
   * 权限code
   */
  code?: string;
  /**
   * 创建人
   */
  create_id?: number;
  /**
   * 权限ID
   */
  id?: number;
  /**
   * 权限名称
   */
  name?: string;
  /**
   * 修改人
   */
  update_id?: number;
}

/**
 * PageModelPlanLogModel，日志分页
 */
export interface ILogsResponse {
  /**
   * 当前页
   */
  current?: number;
  /**
   * 分页记录列表
   */
  records?: PlanLogModel[];
  /**
   * 每页显示条数，默认 10
   */
  size?: number;
  /**
   * 总数
   */
  total?: number;
}

/**
 * PlanLogModel，方案日志模型
 */
export interface PlanLogModel {
  /**
   * 创建时间
   */
  created_at?: number;
  /**
   * 创建人ID
   */
  created_id?: number;
  /**
   * 操作人
   */
  created_user_name?: string;
  /**
   * ID
   */
  id?: number;
  /**
   * 刺激暂停到刺激开始的暂停时长 单位秒
   */
  pause_duration?: number;
  /**
   * 方案id
   */
  plan_id?: number;
  /**
   * 方案日志详情
   */
  plan_log_detail_model_list?: PlanLogDetailModel[];
  /**
   * 方案报告id
   */
  plan_report_id?: number;
  /**
   * 刺激报告终止类型
   */
  plan_report_type?: PlanReportType;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 患者id
   */
  subject_id?: number;
  /**
   * 调用链ID
   */
  trace_id?: string;
  /**
   * 日志类型: 1.CREATE_TASK(创建任务) 2.CREATE_PLAN(创建方案) 3.EDIT_PLAN(编辑方案) 4.CREATE_PLAN(开始刺激)
   * 5.PAUSE_STIMULATION(暂停刺激) 6.CONTINUE_STIMULATION(继续刺激) 7.STOP_STIMULATION(停止刺激既手动终止)
   * 8.END_STIMULATION(终止刺激既自动终止)
   */
  type?: number;
  /**
   * 更新时间
   */
  updated_at?: number;
  /**
   * 修改人ID
   */
  updated_id?: number;
}

export enum PlanLogEventType {
  'CREATE_PLAN' = 2,
  'EDIT_PLAN',
  'BEGIN_STIMULATION',
  'PAUSE_STIMULATION',
  'CONTINUE_STIMULATION',
  'STOP_STIMULATION',
  'END_STIMULATION',
  'SAVE_MOTION_THRESHOLD',
  'UPDATE_STRENGTH',
  'FRONT_CONFIG',
}

export const getPlanLogEventTypeMap = (intl: IntlShape) => {
  return {
    [PlanLogEventType.CREATE_PLAN]: intl.formatMessage({ id: '创建方案' }),
    [PlanLogEventType.EDIT_PLAN]: intl.formatMessage({ id: '编辑方案' }),
    [PlanLogEventType.BEGIN_STIMULATION]: intl.formatMessage({ id: '开始刺激' }),
    [PlanLogEventType.PAUSE_STIMULATION]: intl.formatMessage({ id: '暂停刺激' }),
    [PlanLogEventType.CONTINUE_STIMULATION]: intl.formatMessage({ id: '继续刺激' }),
    [PlanLogEventType.STOP_STIMULATION]: intl.formatMessage({ id: '异常结束刺激' }),
    [PlanLogEventType.END_STIMULATION]: intl.formatMessage({ id: '结束刺激' }),
    [PlanLogEventType.SAVE_MOTION_THRESHOLD]: intl.formatMessage({ id: '保存阈值' }),
    [PlanLogEventType.UPDATE_STRENGTH]: intl.formatMessage({ id: '更新强度' }),
    [PlanLogEventType.FRONT_CONFIG]: intl.formatMessage({ id: '修改配置项' }),
  };
};

/**
 * PlanLogDetailModel，方案日志模型
 */
export interface PlanLogDetailModel {
  /**
   * 创建时间
   */
  created_at?: number;
  /**
   * 创建人ID
   */
  created_id?: number;
  data?: BaseModel;
  /**
   * 方案id
   */
  has_delete?: boolean;
  /**
   * ID
   */
  id?: number;
  /**
   * 方案id
   */
  plan_id?: number;
  /**
   * 方案日志id
   */
  plan_log_id?: number;
  /**
   * 方案注册id
   */
  plan_register_id?: number;
  /**
   * 方案刺激参数id
   */
  plan_stimulus_id?: number;
  /**
   * 方案靶点id
   */
  plan_target_id?: number;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 患者id
   */
  subject_id?: number;
  /**
   * 调用链ID
   */
  trace_id?: string;
  /**
   * 日志类型 1.plan(方案) 2.plan_target(方案靶点) 3.plan_stimulus(方案刺激参数) 4.plan_register(方案注册)
   * 5.subject(患者)
   */
  type?: number;
  /**
   * 更新时间
   */
  updated_at?: number;
  /**
   * 修改人ID
   */
  updated_id?: number;
}

/**
 * BaseModel，日志的数据
 */
export interface BaseModel {
  /**
   * 创建时间
   */
  created_at?: number;
  /**
   * 创建人ID
   */
  created_id?: number;
  /**
   * ID
   */
  id?: number;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 调用链ID
   */
  trace_id?: string;
  /**
   * 更新时间
   */
  updated_at?: number;
  /**
   * 修改人ID
   */
  updated_id?: number;
}

/**
 * 刺激报告终止类型
 */
export enum PlanReportType {
  The0 = '0',
  The1 = '1',
  The2 = '2',
  The3 = '3',
}

/**
 * 添加User
 */
export interface AddUserParams {
  /**
   * 授权信息
   */
  authorization?: string;
  /**
   * 创建人
   */
  create_id?: number;
  /**
   * ID
   */
  id?: number;
  /**
   * 最后一次登入时间
   */
  last_login_at?: Date;
  /**
   * 昵称
   */
  nickname: string;
  /**
   * 密码
   */
  password: string;
  role?: RoleModel;
  /**
   * 角色ID
   */
  role_id?: number;
  /**
   * 是否需要修改密码
   */
  should_change_password?: boolean;
  /**
   * 状态 1可用 2不可用
   */
  status?: number;
  /**
   * 修改人
   */
  update_id?: number;
  /**
   * 用户ID
   */
  user_id?: number;
  /**
   * 用户名
   */
  username: string;
}

export interface IAddUserResponse {
  /**
   * 授权信息
   */
  authorization?: string;
  /**
   * 创建人
   */
  create_id?: number;
  /**
   * ID
   */
  id?: number;
  /**
   * 最后一次登入时间
   */
  last_login_at?: Date;
  /**
   * 昵称
   */
  nickname: string;
  /**
   * 密码
   */
  password: string;
  role?: RoleModel;
  /**
   * 角色ID
   */
  role_id?: number;
  /**
   * 是否需要修改密码
   */
  should_change_password?: boolean;
  /**
   * 状态 1可用 2不可用
   */
  status?: number;
  /**
   * 修改人
   */
  update_id?: number;
  /**
   * 用户ID
   */
  user_id?: number;
  /**
   * 用户名
   */
  username: string;
}

/**
 * UserResetPasswordModel，用户密码重置模型
 */
export interface ResetPWDRequest {
  /**
   * 新密码
   */
  new_password: string;
  /**
   * 用户id
   */
  user_id: number;
}

/**
 * UserEditModel，用户编辑模型
 */
export interface EditUserRequest {
  /**
   * ID
   */
  id: number;
  /**
   * 昵称
   */
  nickname: string;
  /**
   * 状态 1可用 2不可用
   */
  status?: Status;
}

/**
 * 状态 1可用 2不可用
 */
export enum AccountStatus {
  The1 = '1',
  The2 = '2',
}

/**
 * UserChangePasswordModel，用户密码修改模型
 */
export interface ChangePWDRequest {
  /**
   * 新密码
   */
  new_password: string;
  /**
   * 旧密码
   */
  password: string;
}

/**
 * LicenseContentParam
 */
export interface LicenseModel {
  /**
   * 证书结束时间
   */
  end?: Date;
  /**
   * 硬件序列号
   */
  hardware_id?: string;
  /**
   * 证书扩展信息
   */
  extra?: { [key: string]: { [key: string]: any } };
  /**
   * 证书持有者
   */
  holder_name?: string;
  /**
   * 证书信息
   */
  info?: string;
  /**
   * 证书颁发时间
   */
  issued?: Date;
  /**
   * 许可结束时间
   */
  simple_end?: number;
  /**
   * 许可状态, 99. 许可异常, 100. 试用许可正常 200. 许可正常
   */
  simple_status?: SimpleStatus;
  /**
   * 证书开始时间
   */
  start?: Date;
  /**
   * 证书状态, 104.试用未开始, 100.试用正常, 105.试用过期, 504.证书未开始, 200.证书正常, 505.证书过期, 500.证书错误
   */
  status?: LicenseStatus;
  /**
   * 证书状态人类可读
   */
  status_human?: string;
  /**
   * 证书主题
   */
  subject?: string;
  /**
   * 试用结束时间
   */
  trial_end?: Date;
  /**
   * 试用开始时间
   */
  trial_start?: Date;
}

/**
 * 许可状态, 99. 许可异常, 100. 试用许可正常 200. 许可正常
 */
export enum SimpleStatus {
  The100 = 100,
  The200 = 200,
  The99 = 99,
}

export const simpleStatusMap = {
  [SimpleStatus.The99]: '许可异常',
  [SimpleStatus.The100]: '试用许可正常',
  [SimpleStatus.The200]: '许可正常',
};

/**
 * 证书状态, 104.试用未开始, 100.试用正常, 105.试用过期, 504.证书未开始, 200.证书正常, 505.证书过期, 500.证书错误
 */
export enum LicenseStatus {
  The100 = 100,
  The104 = 104,
  The105 = 105,
  The200 = 200,
  The500 = 500,
  The504 = 504,
  The505 = 505,
}

export enum NavigateSupportTypeEnum {
  /**
   * 0. 关闭
   */
  DISABLE = 0,
  /**
   * 1. 开启
   */
  ENABLE = 1,
  /**
   * 2. 不适用
   */
  NOT_APPLICABLE = 2,
}

export type QueryPatientInfo = {
  label: string;
  value: string;
  uuids: string[];
};
