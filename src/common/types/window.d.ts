import { IntlShape } from 'react-intl';
import { AuthenticateResult, ISystemErrorInfo, LoginParams, UserSession } from '@/common/types/index';
import { FileInfo } from '@/renderer/uiComponent/NgFileList';
import os from 'os';
import { LogType } from '@/common/ipc/ipcChannels';
import { TMSScreenState } from '../constant/tms';
import { FaultKeyEnum, FaultStatusEnum } from '@/common/systemFault/type';

interface SetTriggerResponse {
  action: 'trigger_setting';
  code: 0;
  data: {
    index: 123;
    result: 0;
  };
}
export interface Setting {
  REACT_APP_NG_API_BASEURL: string;
  GATEWAY_API_BASEURL: string;
  REACT_APP_REQUEST_HEADERS_ORIGIN: string;
  REACT_APP_SHOW_APP_MENU: string;
  REACT_APP_LOG_DEBUG: string;
  GIT_COMMIT_SHORT_SHA: string;
  IMAGE_SOCKET_URL: string;
  PDU_SOCKET_URL: string;
  TMS_SOCKET_URL: string;
  LOG_API_BASEURL: string;
  WEB_ORIGIN_URL: string;
  CHECK_NODE_EXPORT_BASEURL: string; // 硬盘url
  CHECK_SMART_BASEURL: string; // raidurl
  SURFACE_GRID: string; // 是否展示坐标网格
  REPORT_PASSWORD: string; // 精度日志密码
}
interface TriggerResponse {
  action: string;
  code: number;
  data: {
    index: number;
    result: number;
    in: number; // 0：关闭状态，1：打开状态，
    out: number; // 0：关闭状态，1：打开状态
  };
}
declare global {
  // 设置全局属性
  interface Window {
    authAPI: {
      login(logDto: LoginParams): Promise<AuthenticateResult>;
      logout(): Promise<void>;
      getUserSession(): Promise<UserSession | undefined>;
      setPassword(newPwd: string): any;
    };
    testAPI: {
      test(logDto: LoginParams): Promise<any>;
    };
    fileAPI: {
      getFolderInfo(filePath: string, extList?: string[]): Promise<FileInfo[]>;
      removeFile(filePath: string): Promise<void>;
      getFile(filePath: string, options?: { encoding: BufferEncoding; flag?: string | undefined } | BufferEncoding): Promise<string | Buffer>;
      getFileJson(filePath: string): Promise<string>;
      uploadFile(filePath: string): Promise<any>;
      setRenderLog(type: LogType, log: string): void;
      setSystemExceptionLog(log: string): void;
      setTreatLog(log: string): void;
      writeFileToDisk(output: string, bufferData: ArrayBuffer): Promise<boolean>;
      getFileMd5(filePath: string): Promise<string>;
      getDirName(): Promise<string>;
      getLogsInfo(logsPath: string): Promise<any>;
      getLogPath(): Promise<any>;
      setHistoryLog(log: string): void;
      getBatData(): Promise<any>;
      setBatData(sn: string): void;
      setTreatMatrixLog(log: string): void;
    };
    systemAPI: {
      shutdown(): void;
      getSetting(): Promise<Setting>;
      getStoragePath(): Promise<string>;
      getLoginVersion(): Promise<string>;
      checkDisk80Percent(): Promise<boolean>;
      checkDiskSmart(): Promise<string>;
      getDiskDetailSmart(): Promise<any>;
      getPduInfo(): Promise<any>;
      checkDiskCapacity(): Promise<any>;
      getOsUserInfo(): Promise<os.UserInfo<string>>;
      removeGetSystemWarning(key: string): void;
      getCameraError(key: string, callback: (_: IpcMainInvokeEvent, data: any) => void): Promise<any>;
      removeGetCameraError(key: string): void;
      getTimeOutError(key: string, callback: (_: IpcMainInvokeEvent, data: any) => void): Promise<any>;
      removeGetTimeOutError(key: string): void;
      getZipProgress(callback: (_: IpcMainInvokeEvent, data: any) => void): Promise<any>;
      removeZipProgressListener(type: SelectTypeEnmu): void;
      stopZip(): Promise<any>;
      setLokiLog(logDto: any): void;
      pushSystemError(key: string): Promise<any>;

      getSystemFault(callback: (_: IpcMainInvokeEvent, data: any) => void): void;
      getSystemFaultOnce(): Promise<string>;
      pushSystemFault(faultList: { [key in FaultKeyEnum]?: FaultStatusEnum }, log: string): void;

      subscribeHttpClient(callback: (_: IpcMainInvokeEvent, data: any) => void): Promise<any>;
      unsubscribeHttpClient(): void;
      subscribeSafetyBtn(callback: (_: IpcMainInvokeEvent, data: any) => void): Promise<any>;
      unsubscribeSafetyBtn(): void;
      setStore(key: string, value: any): Promise<any>;
      getStore(key: string): Promise<any>;
      getEnv(key: string): Promise<any>;
      getProductInfo(): Promise<any>;
      getNAV(): Promise<string>;
      unmoutUSB(path: string): Promise<any>;
    };
    tmsAPI: {
      work_status_func(): Promise<any>;
      set_treatment_plan(param: any, noImage?: boolean): Promise<any>;
      set_beat_screen(param: TMSScreenState): any;
      set_treatment_threshold(param: any): any;
      noImage_treatment_plan_start(command: string, tid?: string): Promise<any>;
      image_treatment_plan_start(command: string, tid?: string): Promise<any>;
      query_treatment(): Promise<any>;
      get_tms_version(): Promise<any>;
      treat_warning(callback: (_: IpcMainInvokeEvent, data: any) => void): Promise<any>;
      fault_query(callback: (_: IpcMainInvokeEvent, data: any) => void): Promise<any>;
      beat_btn(callback: (_: IpcMainInvokeEvent, data: any) => void): Promise<any>;
      beat_btn_by_key(key: string, callback: (_: IpcMainInvokeEvent, data: any) => void): Promise<any>;
      get_tunnel_info(): Promise<any>;
      // remove_beat_btn(callback: (_: IpcMainInvokeEvent,data: any) => void): Promise<any>;
      remove_beat_btn_by_key(key): void;
      remove_all_beat_btn(): void;
      working_status(callback: (_: IpcMainInvokeEvent, data: any) => void): Promise<any>;
      triggerQuery(): Promise<TriggerResponse>;
      triggerSetting(triggerFlag: {
        in?: 0 | 1; // 0：关闭状态，1：打开状态，
        out?: 0 | 1; // 0：关闭状态，1：打开状态，
      }): Promise<SetTriggerResponse>;
      get_sock_info(callback: (_: IpcMainInvokeEvent, data: any) => void): void;
      get_sock_info_by_key(key: string, callback: (_: IpcMainInvokeEvent, data: any) => void): void;
      remove_sock_info_by_key(key: string): void;
      get_coil_info_by_key(key: string, callback: (_: IpcMainInvokeEvent, data: any) => void): void;
      remove_coil_info_by_key(key: string): void;
      coil_query(callback: (_: IpcMainInvokeEvent, data: any) => void): Promise<any>;
      water_cooling_query_by_key(key: string, callback: (_: IpcMainInvokeEvent, data: any) => void): Promise<any>;
      remove_water_cooling_query_by_key(key: string): void;
      set_render(): Promise<any>;
      clear_data_pool(): Promise<any>;
      auto_query_coil(): Promise<any>;
      set_treatment_level(level: number, level_relative: number): Promise<any>;
    };
    intl: IntlShape;
  }
}
