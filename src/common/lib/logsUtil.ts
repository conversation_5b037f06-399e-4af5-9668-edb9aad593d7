import { IpcMainInvokeEvent } from 'electron';
import Store from 'electron-store';
export const BAT_PATH = 'bat_data';

const storeObj: { [k: string]: Store } = {};

export const getBat = (): Store => {
  const path = 'bat';
  if (!storeObj[path]) {
    storeObj[path] = new Store({ name: path, cwd: BAT_PATH });
  }

  return storeObj[path];
};

export const setBatData = (_: IpcMainInvokeEvent, value: string) => {
  try {
    getBat().set({ sn: value });

    return true;
  } catch (e) {
    return false;
  }
};

export const getBatData = async () => {
  return getBat().get('sn');
};
