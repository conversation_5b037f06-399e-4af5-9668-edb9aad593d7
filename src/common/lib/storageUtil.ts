import Store from 'electron-store';
export enum PointApplicationType {
  Point100 = 100,
  Point200 = 200,
}

const STORE_PATH = 'ng_preferences';
const STORE_FILE_PREFIX = 'user_';
const STORE_NGNAVIGATOR = 'navigator';
const STORE_LOG_CONFIG = 'log_config';

const storeObj: { [k: string]: Store } = {};

export const getStore = (userId?: number, keyword?: string): Store => {
  let storeName = `${STORE_FILE_PREFIX}${userId !== undefined ? userId : 'common'}`;
  storeName = keyword ? `${storeName}_${keyword}` : storeName;
  if (!storeObj[storeName]) {
    storeObj[storeName] = new Store({ name: storeName, cwd: STORE_PATH });
  }

  return storeObj[storeName];
};

export const getLogConfig = (): Store => {
  if (!storeObj[STORE_LOG_CONFIG]) {
    storeObj[STORE_LOG_CONFIG] = new Store({ name: STORE_LOG_CONFIG, cwd: STORE_PATH });
  }

  return storeObj[STORE_LOG_CONFIG];
};

export const getNavigatorStore = (): Store => {
  if (!storeObj[STORE_NGNAVIGATOR]) {
    storeObj[STORE_NGNAVIGATOR] = new Store({ name: STORE_NGNAVIGATOR, cwd: STORE_PATH });
  }

  return storeObj[STORE_NGNAVIGATOR];
};

export const set = (key: string, value: any, userId?: number) => {
  try {
    getStore(userId).set(key, value);
  } catch (err) {
    // @ts-ignore
    if (err.code !== 'ENOSPC') {
      throw err;
    }
  }
};

export const get = (key: string, userId?: number): any => {
  return getStore(userId).get(key);
};
