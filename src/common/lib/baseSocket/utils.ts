type CommonObj = {
  [props: string]: CommonObj | any;
};
export const enFlatObj = (obj: CommonObj) => {
  let res: CommonObj = {};

  for (let key in obj) {
    if (/\./.test(key)) {
      const splitList = key.split('.');
      res[splitList[0]] = Object.assign(res[splitList[0]] || {}, enFlatObj({ [splitList.slice(1).join('.')]: obj[key] }));
    } else {
      res[key] = obj[key];
    }
  }

  return res;
};

/**
 * 合并对象
 * @param target 目标
 * @param source 合并对象
 * @returns target
 */
export const mergeObjects = (target: CommonObj, source: CommonObj) => {
  for (const key in source) {
    if (source.hasOwnProperty(key)) {
      if (
        target[key] &&
        typeof target[key] === 'object' &&
        !Array.isArray(target[key]) &&
        source[key] &&
        typeof source[key] === 'object' &&
        !Array.isArray(source[key])
      ) {
        // 递归合并对象
        mergeObjects(target[key], source[key]);
      } else {
        // 否则直接覆盖或添加属性
        target[key] = source[key];
      }
    }
  }

  return target;
};

export const flatObj = (obj: CommonObj, pre = '') => {
  let res: CommonObj = {};

  for (let key in obj) {
    if (obj[key]?.toString() === '[object Object]') {
      res = { ...res, ...flatObj(obj[key], pre ? `${pre}.${key}` : key) };
    } else {
      res[pre ? `${pre}.${key}` : key] = obj[key];
    }
  }

  return res;
};
