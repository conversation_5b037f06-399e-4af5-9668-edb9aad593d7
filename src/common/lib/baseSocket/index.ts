import WsWebSocket from 'ws';
import { v4 as uuidv4 } from 'uuid';
import {
  BaseSocketEvent,
  CallBackIdKeyType,
  CallbackType,
  TargetEnum,
  SocketConfig,
  notOpenError,
  onError,
  timeoutError,
  SocketStatus,
  BaseSocketCloseEvent,
  BaseSocketErrorEvent,
  SendExtendParam,
  WaitingQueueType,
  LogOptions,
  SocketLogFunc,
} from './type';
import { enFlatObj, flatObj, mergeObjects } from './utils';
import { getErrorMessage } from '../common';

const idKeyDefault = {
  messageIdKey: 'msg_id',
  eventIdKey: 'id',
  isUnion: false,
};

const timeoutSecondDefault = 5;
const reconnectMaxAttempts = 10000;
const heartBeatSecondDefault = 2;
const CLOSE_NORMAL = 1000;
/** 最大等待队列 */
const MAX_WAITING_QUEUE_LENGTH = 20;
/** 等待队列中销毁时间 */
const WAITING_ABORT_TIME = 15;

export class BaseSocket {
  public client?: WebSocket | WsWebSocket;
  private readonly socketUrl: string;
  private listenerCallBackObj: Record<string | number, Record<string, CallbackType>> = {};
  private callBackObj: Record<number | string, { resolve: Function; reject: Function; sendParams: { [props in string]: any } }> = {};
  private timeoutObj: Record<string, NodeJS.Timeout> = {};
  private reconnectAttempts: number;
  private readonly timeoutSecond: number;
  private readonly heartBeatSecond: number;
  private readonly autoOpenSocket?: boolean;
  private heartBeatFunc?(socket: BaseSocket): void;
  private socketLogFunc?: SocketLogFunc;
  private heartBeatTimer?: NodeJS.Timeout;
  private readonly callBackIdKey: CallBackIdKeyType;
  private onopenFunc?(socket: BaseSocket, env: BaseSocketEvent): any;
  private oncloseFunc?(socket: BaseSocket, event: BaseSocketCloseEvent): void;
  private onerrorFunc?(socket: BaseSocket, event: BaseSocketErrorEvent): void;
  private readonly environment: TargetEnum;
  private message_id: number | string;
  private scope: number;
  private waitingQueue: WaitingQueueType[] = [];
  private onCleanWaitingList?(isVoid: boolean): void;
  private handleMessage?(data: any): void;
  /** 消息拦截器 */
  private messageInterceptor?(data: any, sendParams?: { [props in string]: any }): void;
  private logOptions?: LogOptions;

  constructor(config: SocketConfig) {
    const {
      socketUrl,
      timeoutSecond,
      heartBeatSecond,
      callBackIdKey,
      heartBeatFunc,
      environment,
      autoOpenSocket,
      message_id = 0,
      scope = 2 ** 8,
      onCleanWaitingList,
      messageInterceptor,
      logOptions,
    } = config;

    this.socketUrl = socketUrl;
    this.timeoutSecond = timeoutSecond || timeoutSecondDefault;
    this.reconnectAttempts = 0;
    this.heartBeatSecond = heartBeatSecond || heartBeatSecondDefault;
    this.heartBeatFunc = heartBeatFunc;
    this.socketLogFunc = logOptions?.fn;
    this.callBackIdKey = callBackIdKey || idKeyDefault;
    this.environment = environment || TargetEnum.BROWSER;
    this.autoOpenSocket = autoOpenSocket || true;
    this.message_id = message_id;
    this.scope = scope;
    this.waitingQueue = [];
    this.onCleanWaitingList = onCleanWaitingList;
    this.clearAbortQueue();
    this.messageInterceptor = messageInterceptor;
    if (this.autoOpenSocket) this.open();
    this.logOptions = logOptions;
  }

  /**
   * 开启socket
   */
  open = () => {
    try {
      if ([SocketStatus.OPEN, SocketStatus.CONNECTING].includes(this.client?.readyState as unknown as any)) return;
      if (this.environment === TargetEnum.NODE) {
        this.client = new WsWebSocket(this.socketUrl);
      } else {
        this.client = new WebSocket(this.socketUrl);
      }
    } catch (error: any) {
      throw new Error(`## socket创建失败：:${error}`);
    }

    this.client.onopen = async (event: BaseSocketEvent) => {
      const self = this;
      // eslint-disable-next-line prefer-arrow/prefer-arrow-functions, @typescript-eslint/no-floating-promises
      (async function () {
        try {
          await self.consumptionWaitingQueue();
          self.onCleanWaitingList?.(true);
        } catch (error) {
          self.onCleanWaitingList?.(false);
          self.socketLog('消费队列出错');
        }
      })();
      this.startHeartbeat();
      this.reconnectAttempts = 0;
      if (this.onopenFunc) this.onopenFunc(this, event);
    };

    this.client.onmessage = (data: any) => {
      try {
        const res = JSON.parse(data.data);
        this.handleMessage?.(res);
        const { messageIdKey, eventIdKey, isUnion } = this.callBackIdKey;
        const message_id = flatObj(res)[messageIdKey];
        const eventId = res[eventIdKey];
        let msgIdKey = message_id;
        let log_over = false;
        if (eventId && message_id && isUnion) {
          msgIdKey = `${eventId}_${message_id}`;
        }

        // 处理回调函数
        if (msgIdKey !== undefined && this.callBackObj[msgIdKey]) {
          let response;
          log_over = true;
          try {
            this.messageInterceptor?.(res, this.callBackObj[msgIdKey].sendParams);
            response = res;
            this.callBackObj[msgIdKey].resolve(res);
          } catch (error) {
            this.callBackObj[msgIdKey].reject(error);
            response = error;
          } finally {
            this.messageLog(this.callBackObj[msgIdKey].sendParams, response);
            this.clearCallBackObj(msgIdKey);
          }
        }

        try {
          // 监听函数回调
          const callbacks = this.listenerCallBackObj[eventId];
          if (callbacks) {
            this.messageInterceptor?.(res);
            if (!log_over) this.messageLog({ [eventIdKey]: eventId }, res, true);
            Object.values(callbacks).forEach(callback => {
              try {
                callback(res);
              } catch (error) {
                this.socketLog(`监听函数报错: ${error}`);
              }
            });
          }
        } catch (error) {
          const callbacks = this.listenerCallBackObj[eventId];
          if (callbacks) {
            if (!log_over) this.messageLog({ [eventIdKey]: eventId }, error as Error, true);
            Object.values(callbacks).forEach(callback => {
              try {
                callback({}, error);
              } catch (err) {
                this.socketLog(`监听函数报错: ${err}`);
              }
            });
          }
          this.socketLog(`监听函数报错: ${error}`);
        }
      } catch (error) {
        this.socketLog(`onmessage数据处理报错: ${error}`);
      }
    };

    this.client.onerror = (event: BaseSocketErrorEvent) => {
      // 什么都不做，因为error会导致close
      this.socketLog('onerror');
      if (this.onerrorFunc) this.onerrorFunc(this, event);
    };

    this.client.onclose = (event: BaseSocketCloseEvent) => {
      if (this.oncloseFunc) this.oncloseFunc(this, event);
      // close by user
      if (event.code === CLOSE_NORMAL) {
        this.stopHeartbeat();
      } else {
        // close by other, restart
        this.caughtSocketException();
      }
    };
  };

  caughtSocketException = () => {
    for (const messageId in this.callBackObj) {
      if (this.callBackObj.hasOwnProperty(messageId)) {
        this.callBackObj[messageId].reject(onError);
        this.messageLog(this.callBackObj[messageId].sendParams, onError);
        this.clearCallBackObj(messageId as unknown as number);
      }
    }
    // 重连次数范围内进行重连尝试
    if (this.reconnectAttempts < reconnectMaxAttempts) {
      this.socketLog('socket正在尝试重连......');
      this.reconnectAttempts++;
      let timer = setTimeout(() => {
        this.open();
        clearTimeout(timer);
      }, 2000);
    } else {
      this.clearAllCallBackObj();
    }
  };

  /**
   * 事件监听
   * id 事务Id
   * key: 用于多监听添加以及移除
   */
  on = (id: string | number, cb: (data: any) => void, listenKey?: string) => {
    const key = listenKey || id;
    if (!this.listenerCallBackObj[id]) {
      this.listenerCallBackObj[id] = {};
      this.listenerCallBackObj[id][key] = cb;
    } else {
      this.listenerCallBackObj[id][key] = cb;
    }
  };

  getMessageId = () => {
    if (typeof this.message_id === 'number') {
      this.message_id = (this.message_id + 1) % this.scope;
    } else {
      this.message_id = uuidv4();
    }

    return this.message_id;
  };

  /**
   * send事件
   * extendParams： send的参数扩展（超时和重试），不设置则取用默认值
   * attempt：尝试的次数，默认为0
   */
  send = async (
    params: {},
    msgId?: number,
    extendParams: SendExtendParam = {
      timeoutSecond: this.timeoutSecond,
    },
    attempt = 0
  ): Promise<any> => {
    return new Promise((resolve, reject) => {
      const message_id = msgId || this.getMessageId();
      const obj = { extendParams, reject, resolve, message_id, attempt, params };
      const attemptToSend = () => {
        if (this.client?.readyState !== SocketStatus.OPEN) {
          this.addWaitingQueue(obj);

          return;
        }

        this.send2Socket(obj);
      };

      attemptToSend();
    });
  };

  private send2Socket = (obj: WaitingQueueType) => {
    const { message_id: msgId, reject, resolve, extendParams } = obj;
    const { eventIdKey, isUnion } = this.callBackIdKey;
    let { params } = obj;
    const message_id = msgId || this.getMessageId();
    params = mergeObjects(params, enFlatObj({ [this.callBackIdKey.messageIdKey]: message_id }));

    const msgIdKey = params[eventIdKey] && isUnion ? `${params[eventIdKey]}_${message_id}` : message_id;

    this.callBackObj[msgIdKey] = { resolve, reject, sendParams: { ...params, createAt: Date.now() } };

    this.timeoutObj[msgIdKey] = setTimeout(() => {
      if (this.callBackObj[msgIdKey]) {
        reject(timeoutError);
        this.messageLog(this.callBackObj[msgIdKey].sendParams, timeoutError);
        this.clearCallBackObj(msgIdKey);
      }
    }, extendParams.timeoutSecond * 1000);

    this.client?.send(JSON.stringify(params));
  };

  /**
   * open态监听
   */
  onopen = (cb: (socket: BaseSocket, ev: BaseSocketEvent) => any) => {
    this.onopenFunc = cb;
  };

  /**
   * close态监听
   */
  onclose = (cb: (socket: BaseSocket, ev: BaseSocketCloseEvent) => any) => {
    this.oncloseFunc = cb;
  };

  /**
   * onError
   */

  onerror = (cb: (socket: BaseSocket, ev: BaseSocketErrorEvent) => any) => {
    this.onerrorFunc = cb;
  };

  /** 监听onmessage事件 */
  listenMessage = (cb: (data: any) => void) => {
    this.handleMessage = cb;
  };

  removeMessage = () => {
    this.handleMessage = undefined;
  };

  /**
   * 关闭socket
   */
  close = () => {
    this.stopHeartbeat();
    this.clearAllCallBackObj();
    if (this.client) this.client.close(CLOSE_NORMAL);
  };

  /**
   * 移除监听
   */
  removeListener = (id: string | number, listenKey?: string | number) => {
    const key = listenKey || id;
    const callbacks = this.listenerCallBackObj[id];
    if (callbacks && callbacks[key]) {
      delete callbacks[key];
    }
  };

  /**
   * 开始心跳
   */
  private startHeartbeat = () => {
    if (!this.heartBeatFunc) return;
    if (this.heartBeatTimer) clearInterval(this.heartBeatTimer);

    this.heartBeatTimer = setInterval(() => {
      if (!this.client) {
        clearInterval(this.heartBeatTimer);

        return;
      }
      if (this.heartBeatFunc) this.heartBeatFunc(this);
    }, this.heartBeatSecond * 1000);
  };

  /**
   * 结束心跳
   */
  private stopHeartbeat = () => {
    if (this.heartBeatTimer) {
      clearInterval(this.heartBeatTimer);
      this.heartBeatTimer = undefined;
    }
  };

  /**
   * 清除回调函数并清除超时定时器
   */
  private clearCallBackObj = (messageId: number | string) => {
    delete this.callBackObj[messageId];
    clearTimeout(this.timeoutObj[messageId]);
    delete this.timeoutObj[messageId];
    this.logOptions?.fn?.(`清除掉的messageId: ${messageId}`);
  };

  /**
   * 清除所有的回调
   */
  private clearAllCallBackObj = () => {
    this.listenerCallBackObj = {};
    this.callBackObj = {};
    this.timeoutObj = {};
  };

  /** 消费队列 */
  private consumptionWaitingQueue = async () => {
    return new Promise(async (resolve, reject) => {
      while (this.waitingQueue.length) {
        if (this.client?.readyState !== SocketStatus.OPEN) {
          return reject();
        }
        const item = this.waitingQueue.shift()!;
        await new Promise(res => {
          const timer = setTimeout(() => {
            res('');
            this.send2Socket(item);
            clearTimeout(timer);
          }, 150);
        });
      }
      resolve('');
    });
  };

  /** 销毁队列项 */
  private abortWaitingQueue = () => {
    const item = this.waitingQueue.shift();
    item?.reject(notOpenError);
  };

  /** 添加到数组 */
  private addWaitingQueue = (params: WaitingQueueType) => {
    this.waitingQueue.push(params);
  };

  /** 定时清理队列 */
  private clearAbortQueue = () => {
    setInterval(() => {
      const timeoutStamp = (Date.now() / 1000 - WAITING_ABORT_TIME) * 1000;
      while (this.waitingQueue.length) {
        const limitAbortLength = this.waitingQueue.length - MAX_WAITING_QUEUE_LENGTH;
        if (limitAbortLength > 0) {
          // 超长部分
          this.abortWaitingQueue();
        } else if (this.waitingQueue[0].createAt! < timeoutStamp) {
          // 超时部分
          this.abortWaitingQueue();
        } else {
          break;
        }
      }
    }, 1000);
  };

  private messageLog = (request: { [key in string]: any }, response: { [key in string]: any } | Error, isListen = false) => {
    if (!this.logOptions) return;
    const { eventIdKey } = this.callBackIdKey;
    const { exclude, include, includeAll, prefix } = this.logOptions;
    if (exclude?.some(item => item === request[eventIdKey]) || (!includeAll && !include?.some(item => item === request[eventIdKey]))) {
      return;
    }
    const response_log = getErrorMessage(response);
    let pre_str = '';
    if (prefix) {
      pre_str = `${prefix}\n`;
    }
    if (isListen) {
      return this.socketLog(`${pre_str}listen:${response_log}`);
    }
    this.socketLog(`${pre_str}request:${JSON.stringify(request)}\nresponse:${response_log}`);
  };

  /**
   * socket日志记录
   */
  private socketLog = (logMsg: string) => {
    if (!this.socketLogFunc) {
      console.log(logMsg); // eslint-disable-line no-console
    } else {
      this.socketLogFunc(logMsg);
    }
  };
}
