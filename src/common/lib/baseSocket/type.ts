import { BaseSocket } from '.';
import WsWebsocket from 'ws';

/**
 * socket运行环境
 * NODE：main进程
 * BROWSER:render进程
 */
export enum TargetEnum {
  BROWSER,
  NODE,
}

/**
 * 错误码定义，用于回调函数报错的返参
 */
export enum ErrorCodeEnum {
  TIMEOUT,
  NOT_OPEN,
  ERROR,
  CLOSE,
  REQUEST_BAD,
}

/**
 * socket状态，兼容ws和websocket
 */
export enum SocketStatus {
  CONNECTING = 0,
  OPEN = 1,
  CLOSING = 2,
  CLOSED = 3,
}

/**
 * baseSocket初始化时候的配置参数
 */
export interface SocketConfig {
  socketUrl: string;
  callBackIdKey?: CallBackIdKeyType;
  heartBeatSecond?: number;
  heartBeatFunc?(socket: BaseSocket): void;
  logOptions?: LogOptions;
  timeoutSecond?: number;
  environment?: TargetEnum;
  autoOpenSocket?: boolean;
  message_id?: string | number;
  /** 如果id为数字的范围值 */
  scope?: number;
  /** 清空队列回调 */
  onCleanWaitingList?(isVoid: boolean): void;
  /** 消息拦截器 */
  messageInterceptor?(res: any, sendParams?: { [props in string]: any }): any;
}

/**
 * socket send的扩展类型约束
 * timeoutSecond：自定义单个send的超时时间
 * 自定义单个send的最大send尝试次数（非OPEN态）
 */
export interface SendExtendParam {
  timeoutSecond: number;
}

/**
 * socket监听函数的参数类型，兼容ws和websocket
 */
export type BaseSocketEvent = Event | WsWebsocket.Event;

export type BaseSocketCloseEvent = CloseEvent | WsWebsocket.CloseEvent;

export type BaseSocketErrorEvent = WsWebsocket.ErrorEvent | Event;

export const notOpenError: SocketError = {
  code: ErrorCodeEnum.NOT_OPEN,
  msg: '请求失败，网络连接非OPEN态',
};

/**
 * socket报错的返参常量定义
 */
export const onError: SocketError = {
  code: ErrorCodeEnum.ERROR,
  msg: '请求失败,onerror报错',
};

/**
 * socket请求超时的返参常量定义
 */
export const timeoutError: SocketError = {
  code: ErrorCodeEnum.TIMEOUT,
  msg: '请求失败，网络连接超时',
};

/**
 * socket请求超时的返参常量定义
 */
export interface SocketError {
  code: ErrorCodeEnum;
  msg: string;
}

/**
 * 回调函数的定义
 */
export type CallbackType = (...args: any[]) => void;

/**
 * 设置send回调函数/监听函数的取值key,socket回参里通过key来进行获取messageId/eventId
 * 例如tms里eventIdKey取值为action，结构光里取值为id
 */
export interface CallBackIdKeyType {
  messageIdKey: string;
  eventIdKey: string;
  isUnion?: boolean;
}

export type WaitingQueueType = {
  message_id: string | number;
  reject: Function;
  resolve: Function;
  extendParams: SendExtendParam;
  params: { [props: string]: any };
  createAt?: number;
};

export type SocketLogFunc = (logMsg: string) => void;
/** 日志配置，当exclude不包含&&(include包含的时候 || includeAll为true) */
export type LogOptions = {
  /** 日志回调 */
  fn?: SocketLogFunc;
  /** 排除eventId */
  exclude?: (number | string)[];
  /** 包含eventId */
  include?: (number | string)[];
  /** 包含所有 */
  includeAll?: boolean;
  /** 日志前缀 */
  prefix?: string;
};
