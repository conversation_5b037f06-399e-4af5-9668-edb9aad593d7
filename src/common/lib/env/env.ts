import path from 'path';
import fs from 'fs-extra';
import { Setting } from '../../types';
import prodConfig from '../../../../setting.prod.json';
import { isDev } from './nodeEnv';
import { app } from 'electron';

let dirname = '';
export const setDirName = (dir: string) => {
  dirname = dir;
};
export const getDirName = () => {
  return dirname;
};

const getSettingFromFile = (): Setting => {
  if (isDev) {
    const rootDir = path.resolve(__dirname, '..');
    const devConfigPath = path.join(rootDir, 'setting.dev.json');
    /* 本地存在dev文件*/
    if (fs.existsSync(devConfigPath)) {
      return JSON.parse(fs.readFileSync(devConfigPath, 'utf8'));
    } else {
      return {
        ...prodConfig,
      };
    }
  }
  const configInfoPath = path.join(app.getPath('userData'), 'setting.json');
  if (!fs.existsSync(configInfoPath)) {
    return {
      ...prodConfig,
    };
  }
  const proValue = JSON.parse(fs.readFileSync(configInfoPath, 'utf8'));

  return {
    ...prodConfig,
    ...proValue,
  };
};

export const REACT_APP_REQUEST_HEADERS_ORIGIN = process.env.REACT_APP_REQUEST_HEADERS_ORIGIN;

export const isOnOSX = (): boolean => {
  return process.platform === 'darwin';
};

export const shouldShowDevtoolMenu = (): boolean => {
  /*
      太长不看版：不要改这里，在本地 ./.env.production.local 文件里添加 REACT_APP_SHOW_APP_MENU=show 即可显示菜单（不显示来找我 - mcz
      */
  return getSettingFromFile().REACT_APP_SHOW_APP_MENU === 'show';
};

export const getSetting = (): Setting => {
  return getSettingFromFile();
};

let loginVersion = '';
export const setLoginVersion = (version: string) => {
  loginVersion = version;
};
export const getLoginVersion = async () => {
  return loginVersion;
};

let devtoolsIsOpened = false;
export const setDevtoolsIsOpened = (isOpened: boolean) => {
  devtoolsIsOpened = isOpened;
};
export const getDevtoolsIsOpened = () => {
  return devtoolsIsOpened;
};
