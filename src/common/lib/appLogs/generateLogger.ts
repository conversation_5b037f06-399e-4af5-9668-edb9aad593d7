import BaseLogger from './baseLogger';

const historyLogConfig = {
  name: 'historyLog',
  scope: 'HISTORY-LOG',
  fileSize: 500,
};
const mainLogConfig = {
  name: 'mainLog',
  scope: 'MAIN-LOG',
  fileSize: 50,
  isAssignLog: true,
};
const renderLogConfig = {
  name: 'renderLog',
  scope: 'RENDER-LOG',
  fileSize: 50,
};
const systemExceptionLogConfig = {
  name: 'systemFault',
  scope: 'SYSTEM-FAULT-LOG',
  fileSize: 500,
};
const tmsLogConfig = {
  name: 'tmsLog',
  scope: 'TMS-LOG',
  fileSize: 200,
};
const treatLogConfig = {
  name: 'treatLog',
  scope: 'TREAT-LOG',
  fileSize: 50,
};
const treatMatrixLogConfig = {
  name: 'treatMatrixLog',
  scope: 'TREAT-MATRIX-LOG',
  fileSize: 1024,
};
const systemFaultLogConfig = {
  name: 'systemFaultLog',
  scope: 'SYSTEM-FAULT-LOG-LOG',
  fileSize: 500,
};
const deviceStatusLogConfig = {
  name: 'deviceStatusLog',
  scope: 'DEVICE_STATUS-LOG',
  fileSize: 500,
};
const baseSocketLogConfig = {
  name: 'baseSocketLog',
  scope: 'BASE-SOCKET-LOG',
  fileSize: 500,
};

export const historyLogger = new BaseLogger(historyLogConfig);
export const mainLogger = new BaseLogger(mainLogConfig);
export const renderLogger = new BaseLogger(renderLogConfig);
export const systemExceptionLogger = new BaseLogger(systemExceptionLogConfig);
export const tmsLogger = new BaseLogger(tmsLogConfig);
export const treatLogger = new BaseLogger(treatLogConfig);
export const treatMatrixLogger = new BaseLogger(treatMatrixLogConfig);
export const systemFaultLog = new BaseLogger(systemFaultLogConfig);
export const baseSocketLog = new BaseLogger(baseSocketLogConfig);
export const deviceStatusLog = new BaseLogger(deviceStatusLogConfig);
