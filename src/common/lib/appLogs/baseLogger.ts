import { app } from 'electron';
import log from 'electron-log';
import path from 'path';
import fse from 'fs-extra';
import dayjs from 'dayjs';
import { getDevtoolsIsOpened, getSetting } from '../env/env';

interface LoggerConfig {
  name: string;
  scope: string;
  fileSize?: number;
  backupLogNum?: number;
  isAssignLog?: boolean;
}

enum LogType {
  debug = 'debug',
  info = 'info',
  error = 'error',
  warn = 'warn',
}

class BaseLogger {
  private log: log.ElectronLog;
  private logPath: string;
  private scopeId: string;

  constructor(config: LoggerConfig) {
    const { name, scope, fileSize = 50, backupLogNum = 5, isAssignLog = false } = config;
    this.scopeId = `${scope}-${getSetting().GIT_COMMIT_SHORT_SHA}`;
    this.log = log.create(name);
    this.logPath = app.getPath('userData');

    if (!fse.existsSync(`${this.logPath}/logs/${name}`)) {
      fse.mkdirSync(`${this.logPath}/logs/${name}`);
    }

    const filePath = path.join(this.logPath, `logs/${name}/${name}.log`);

    this.log.transports.file.resolvePath = () => filePath;
    this.log.transports.file.sync = false;
    this.log.transports.console.level = false;
    this.log.transports.file.level = 'silly';
    this.log.transports.file.format = '[{y}-{m}-{d} {h}:{i}:{s}.{ms}] [{level}]{scope} {text}';
    this.log.transports.file.maxSize = 1024 * 1024 * fileSize;
    // @ts-ignore
    this.log.transports.file.archiveLog = (file: string) => {
      this.customArchiveLog(file, name, backupLogNum);
    };
    this.log.transports.file.getFile();
    this.log.catchErrors({
      showDialog: false,
    });
    /** 解决当main进程崩溃的时候，console的输出可以记录到日志文件内，目前仅mainLog使用*/
    if (isAssignLog) {
      Object.assign(console, this.log.functions);
    }
  }

  debug(...params: any[]): void {
    this.writeLog(LogType.debug, ...params);
  }

  info(...params: any[]): void {
    this.writeLog(LogType.info, ...params);
  }

  warn(...params: any[]): void {
    this.writeLog(LogType.warn, ...params);
  }

  error(...params: any[]): void {
    this.writeLog(LogType.error, ...params);
  }

  writeLog(type: LogType, ...params: any[]): void {
    switch (type) {
      case LogType.debug:
        /** 非debug模式，直接略过，不做日志写入*/
        if (getSetting().REACT_APP_LOG_DEBUG !== 'debug') return;
        this.getScopeLog().debug(...params);
        break;
      case LogType.info:
        this.getScopeLog().info(...params);
        break;
      case LogType.error:
        this.getScopeLog().error(...params);
        break;
      case LogType.warn:
        this.getScopeLog().warn(...params);
        break;
      default:
        this.getScopeLog().info(...params);
        break;
    }
  }

  /* 添加scope，返回LogFunctions*/
  private getScopeLog() {
    const scopeId = getDevtoolsIsOpened() ? `${this.scopeId} devtools-opened` : this.scopeId;

    return this.log.scope(scopeId);
  }

  /**
   * 自定义archiveLog函数
   * file:日志文件信息
   * folder: 日志文件存放目录
   * logNum: 日志备份数量 */
  private customArchiveLog = (file: string, folder: string, logNum: number) => {
    file = file.toString();
    const info = path.parse(file);
    const rootDir = `${this.logPath}/logs/${folder}/`;
    const fileList = fse.readdirSync(rootDir).filter(ele => ele.includes(info.name));
    let sortFileList = [...fileList].sort((a, b) => a.localeCompare(b)).reverse();
    const deleteFileList = sortFileList.slice(logNum);
    for (const ele of deleteFileList) {
      fse.unlinkSync(`${rootDir}/${ele}`);
    }

    try {
      const dateStr = dayjs(new Date()).format('YYYY_MM_DD-HH_mm_ss');
      const newFileName = path.join(info.dir, `${info.name}.${dateStr}${info.ext}`);
      fse.renameSync(file, newFileName);
    } catch (e) {
      console.log('Could not rotate log', e); // eslint-disable-line no-console
    }
  };
}

export default BaseLogger;
