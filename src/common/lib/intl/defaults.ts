import { Locale } from './locale';
const lang = localStorage.getItem('lang') || navigator.language;
export const defaultLocale: Locale = ['zh-TW', 'zh-CN', 'zh-HK', 'zh-SG'].some(v => v === lang) ? 'zh-CN' : 'en-US';
export const defaultLanguage: string = 'en';

export const getMessages = async (locale: Locale): Promise<any> => {
  // tslint:disable-next-line:non-literal-require
  // eslint-disable-next-line @typescript-eslint/no-var-requires, security/detect-non-literal-require
  return import(`../../../renderer/static/messages/${locale.toLowerCase()}.json`);
};
