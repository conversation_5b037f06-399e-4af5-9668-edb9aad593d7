// istanbul ignore next
import { isDev } from './env/nodeEnv';
import { execSync } from 'child_process';
import { mainLogger } from './appLogs/generateLogger';
import { machineIdSync } from 'node-machine-id';
import fs from 'fs';

let hardwareId = '';
let endpointId = '';
export const never = <T>(a: never): T => {
  console.error(a);
  throw new Error('Not expected type');
};

export const wait = async (milles: number): Promise<void> => {
  await new Promise((resolve: (value?: void | PromiseLike<void>) => void) => {
    setTimeout(() => {
      resolve();
    }, milles);
  });
};

export const arrayToObject = (arr?: number[]): { [k: number]: boolean } => {
  const result: { [k: number]: boolean } = {};
  if (arr && arr.length !== 0) {
    arr.forEach((item: number) => (result[item] = true));
  }

  return result;
};
export const getMountInfo = (_path: string): string[] | undefined => {
  let mountString: string | undefined;
  try {
    mountString = execSync(`df -h ${_path.replace(/ /g, '\\ ')} | grep /`, { encoding: 'utf8' }).trim();
  } catch (err) {
    mountString = undefined;
  }
  if (!mountString) {
    return undefined;
  }

  const mountResult = mountString.split(/ +/);
  if (!mountResult || mountResult.length < 2) {
    return undefined;
  }

  return [mountResult[0], mountResult[mountResult.length - 1]];
};
export const getEndpointInfo = () => {
  if (endpointId) {
    return { hardwareId, endpointId };
  }

  if (isDev) {
    endpointId = machineIdSync(true);

    return { hardwareId, endpointId };
  }

  try {
    const endpointShellScript = '/usr/local/bin/endpointcodegen.sh';
    if (!fs.existsSync(endpointShellScript)) {
      mainLogger.error(`[common/utils][getEndpointInfo] ${endpointShellScript} is not found`);
    }
    const endpointInforStr = execSync(`${endpointShellScript} generate`, { encoding: 'utf8' });
    const { hardwareid, endpointid } = JSON.parse(endpointInforStr);
    hardwareId = hardwareid;
    endpointId = endpointid;
  } catch (err) {
    mainLogger.error(`[common/utils][getEndpointInfo] execSync or parse ${JSON.stringify(err)}.`);
    throw err;
  }
  if (!hardwareId) {
    throw new Error('can not generate hardwareId');
  }
  if (!endpointId) {
    throw new Error('can not generate endpointId');
  }

  return { hardwareId, endpointId };
};

// eslint-disable-next-line max-lines-per-function
export const getTreatLimitJson = () => {
  return [
    { apex: 1, frequency: 100, continued: 100, brevity: 100 },
    { apex: 2, frequency: 100, continued: 100, brevity: 100 },
    { apex: 3, frequency: 100, continued: 100, brevity: 100 },
    { apex: 4, frequency: 100, continued: 100, brevity: 100 },
    { apex: 5, frequency: 100, continued: 96.5, brevity: 100 },
    { apex: 6, frequency: 100, continued: 79.83, brevity: 100 },
    { apex: 7, frequency: 100, continued: 67.93, brevity: 100 },
    { apex: 8, frequency: 100, continued: 59, brevity: 100 },
    { apex: 9, frequency: 100, continued: 52.06, brevity: 100 },
    { apex: 10, frequency: 100, continued: 46.5, brevity: 100 },
    { apex: 11, frequency: 100, continued: 41.95, brevity: 100 },
    { apex: 12, frequency: 100, continued: 38.17, brevity: 91.6 },
    { apex: 13, frequency: 100, continued: 34.96, brevity: 83.91 },
    { apex: 14, frequency: 100, continued: 32.21, brevity: 77.31 },
    { apex: 15, frequency: 100, continued: 29.83, brevity: 71.6 },
    { apex: 16, frequency: 100, continued: 27.75, brevity: 66.6 },
    { apex: 17, frequency: 100, continued: 25.91, brevity: 62.19 },
    { apex: 18, frequency: 100, continued: 24.28, brevity: 58.27 },
    { apex: 19, frequency: 100, continued: 22.82, brevity: 54.76 },
    { apex: 20, frequency: 100, continued: 21.5, brevity: 51.6 },
    { apex: 21, frequency: 100, continued: 20.31, brevity: 48.74 },
    { apex: 22, frequency: 100, continued: 19.23, brevity: 46.15 },
    { apex: 23, frequency: 100, continued: 18.24, brevity: 43.77 },
    { apex: 24, frequency: 100, continued: 17.33, brevity: 41.6 },
    { apex: 25, frequency: 100, continued: 16.5, brevity: 39.6 },
    { apex: 26, frequency: 100, continued: 15.73, brevity: 37.75 },
    { apex: 27, frequency: 100, continued: 15.02, brevity: 36.04 },
    { apex: 28, frequency: 100, continued: 14.36, brevity: 34.46 },
    { apex: 29, frequency: 100, continued: 13.74, brevity: 32.98 },
    { apex: 30, frequency: 100, continued: 13.17, brevity: 31.6 },
    { apex: 31, frequency: 100, continued: 12.63, brevity: 30.31 },
    { apex: 32, frequency: 100, continued: 12.13, brevity: 29.1 },
    { apex: 33, frequency: 100, continued: 11.65, brevity: 27.96 },
    { apex: 34, frequency: 100, continued: 11.21, brevity: 26.89 },
    { apex: 35, frequency: 100, continued: 10.79, brevity: 25.89 },
    { apex: 36, frequency: 100, continued: 10.39, brevity: 24.93 },
    { apex: 37, frequency: 100, continued: 10.01, brevity: 24.03 },
    { apex: 38, frequency: 100, continued: 9.66, brevity: 23.18 },
    { apex: 39, frequency: 100, continued: 9.32, brevity: 22.37 },
    { apex: 40, frequency: 99, continued: 9, brevity: 21.6 },
    { apex: 41, frequency: 98, continued: 8.7, brevity: 20.87 },
    { apex: 42, frequency: 97, continued: 8.4, brevity: 20.17 },
    { apex: 43, frequency: 96, continued: 8.13, brevity: 19.51 },
    { apex: 44, frequency: 95, continued: 7.86, brevity: 18.87 },
    { apex: 45, frequency: 94, continued: 7.61, brevity: 18.27 },
    { apex: 46, frequency: 93, continued: 7.37, brevity: 17.69 },
    { apex: 47, frequency: 92, continued: 7.14, brevity: 17.13 },
    { apex: 48, frequency: 91, continued: 6.92, brevity: 16.6 },
    { apex: 49, frequency: 90, continued: 6.7, brevity: 16.09 },
    { apex: 50, frequency: 89, continued: 6.5, brevity: 15.6 },
    { apex: 51, frequency: 88, continued: 6.3, brevity: 12.54 },
    { apex: 52, frequency: 87, continued: 6.12, brevity: 11.31 },
    { apex: 53, frequency: 86, continued: 5.93, brevity: 10.9 },
    { apex: 54, frequency: 85, continued: 5.76, brevity: 10.51 },
    { apex: 55, frequency: 84, continued: 5.59, brevity: 10.13 },
    { apex: 56, frequency: 83, continued: 5.43, brevity: 9.77 },
    { apex: 57, frequency: 82, continued: 5.27, brevity: 9.42 },
    { apex: 58, frequency: 81, continued: 5.12, brevity: 9.09 },
    { apex: 59, frequency: 80, continued: 4.97, brevity: 8.77 },
    { apex: 60, frequency: 79, continued: 4.83, brevity: 8.46 },
    { apex: 61, frequency: 78, continued: 4.7, brevity: 8.16 },
    { apex: 62, frequency: 77, continued: 4.56, brevity: 7.87 },
    { apex: 63, frequency: 76, continued: 4.44, brevity: 7.6 },
    { apex: 64, frequency: 75, continued: 4.31, brevity: 7.33 },
    { apex: 65, frequency: 74, continued: 4.19, brevity: 7.07 },
    { apex: 66, frequency: 73, continued: 4.08, brevity: 6.83 },
    { apex: 67, frequency: 72, continued: 3.96, brevity: 6.59 },
    { apex: 68, frequency: 71, continued: 3.85, brevity: 6.36 },
    { apex: 69, frequency: 70, continued: 3.75, brevity: 6.13 },
    { apex: 70, frequency: 69, continued: 3.64, brevity: 5.92 },
    { apex: 71, frequency: 68, continued: 3.54, brevity: 5.71 },
    { apex: 72, frequency: 67, continued: 3.44, brevity: 5.51 },
    { apex: 73, frequency: 66, continued: 3.35, brevity: 5.32 },
    { apex: 74, frequency: 65, continued: 3.26, brevity: 5.13 },
    { apex: 75, frequency: 64, continued: 3.17, brevity: 4.95 },
    { apex: 76, frequency: 63, continued: 3.08, brevity: 4.77 },
    { apex: 77, frequency: 62, continued: 2.99, brevity: 4.6 },
    { apex: 78, frequency: 61, continued: 2.91, brevity: 4.44 },
    { apex: 79, frequency: 60, continued: 2.83, brevity: 4.28 },
    { apex: 80, frequency: 59, continued: 2.75, brevity: 4.13 },
    { apex: 81, frequency: 58, continued: 2.67, brevity: 3.98 },
    { apex: 82, frequency: 57, continued: 2.6, brevity: 3.83 },
    { apex: 83, frequency: 56, continued: 2.52, brevity: 3.69 },
    { apex: 84, frequency: 55, continued: 2.45, brevity: 3.56 },
    { apex: 85, frequency: 54, continued: 2.38, brevity: 3.42 },
    { apex: 86, frequency: 53, continued: 2.31, brevity: 3.3 },
    { apex: 87, frequency: 52, continued: 2.25, brevity: 3.17 },
    { apex: 88, frequency: 51, continued: 2.18, brevity: 3.05 },
    { apex: 89, frequency: 50, continued: 2.12, brevity: 2.94 },
    { apex: 90, frequency: 49, continued: 2.06, brevity: 2.83 },
    { apex: 91, frequency: 48, continued: 1.99, brevity: 2.72 },
    { apex: 92, frequency: 47, continued: 1.93, brevity: 2.61 },
    { apex: 93, frequency: 46, continued: 1.88, brevity: 2.51 },
    { apex: 94, frequency: 45, continued: 1.82, brevity: 2.41 },
    { apex: 95, frequency: 44, continued: 1.76, brevity: 2.31 },
    { apex: 96, frequency: 43, continued: 1.71, brevity: 2.22 },
    { apex: 97, frequency: 42, continued: 1.65, brevity: 2.13 },
    { apex: 98, frequency: 41, continued: 1.6, brevity: 2.04 },
    { apex: 99, frequency: 40, continued: 1.55, brevity: 1.96 },
    { apex: 100, frequency: 39, continued: 1.5, brevity: 1.88 },
  ];
};

export const secToTime = (time: number) => {
  let hours = Math.floor(time / 60 / 60); // 时
  let minutes = Math.floor((time / 60) % 60); // 分
  let seconds = Math.floor(time % 60); // 秒

  return `${hours > 9 ? hours : `0${hours}`}:${minutes > 9 ? minutes : `0${minutes}`}:${seconds > 9 ? seconds : `0${seconds}`}  `;
};
