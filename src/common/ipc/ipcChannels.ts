export const LOGIN = '@LOGIN';
export const LOGOUT = '@LOGOUT';
export const GET_USER_SESSION = '@GET_USER_SESSION';
export const GET_STORAGE_PATH = '@GET_STORAGE_PATH';
export const OPEN_FILE_MODAL = '@OPEN_FILE_MODALN';
export const GET_LOGS_INFO = '@GET_LOGS_INFO';
export const REMOVE_FILE = '@REMOVE_FILE';
export const GET_LOG_PATH = '@GET_LOG_PATH';
export const UPLOAD_FILE = '@UPLOAD_FILE';

export const SET_TREATMENT_PLAN = '@SET_TREATMENT_PLAN';
export const SET_BEAT_SCREEM = '@SET_BEAT_SCREEM';
export const NOIMAGE_TREATMENT_PLAN_START = '@NOIMAGE_TREATMENT_PLAN_START';
export const IMAGE_TREATMENT_PLAN_START = '@IMAGE_TREATMENT_PLAN_START';
export const QUERY_TREATMENT = '@QUERY_TREATMENT';
export const SET_TREATMENT_THRESHOLD = '@SET_TREATMENT_THRESHOLD';

export const SET_RENDER_LOG = '@SET_RENDER_LOG';
export const SYSTEM_EXCEPTION = '@SYSTEM_EXCEPTION';
export const WRITE_FILE = '@WRITE_FILE';
export const TRIGGER_QUERY = '@TRIGGER_QUERY';
export const TRIGGER_SETTING = '@TRIGGER_SETTING';
export const SET_PASSWORD = '@SET_PASSWORD';
export const GET_OS_USER_INFO = '@GET_OS_USER_INFO';
export const GET_TIMEOUT_ERROR = '@GET_TIMEOUT_ERROR';

export const SHUTDOWN = '@SHUTDOWN';
export const SET_STORE = '@SET_STORE';
export const GET_STORE = '@GET_STORE';

export const GET_FILE_MD5 = '@GET_FILE_MD5';
export const GET_DIR_NAME = '@GET_DIR_NAME';

export const SET_RENDER = '@SET_RENDER';
export const CLEAR_DATA_POOL = '@CLEAR_DATA_POOL';
export const AUTO_QUERY_COIL = '@AUTO_QUERY_COIL';

export const LOKI_LOG = '@LOKI_LOG';
export const TMS_VERSION = '@GET_TMS_VERSION';
export const TUNNEL_INFO = '@GET_TUNNEL_INFO';

export const SET_TREAT_LOG = '@SET_TREAT_LOG';

export const SET_TREATMENT_LEVEL = '@SET_TREATMENT_LEVEL';
export const SET_BAT_DATA = 'SET_BAT_DATA';
export const GET_BAT_DATA = 'GET_BAT_DATA';

export const SET_TREAT_MATRIX_LOG = '@SET_TREAT_MATRIX_LOG';

export const SET_CLEAR_PROCESS_INTERVAL = 'SET_CLEAR_PROCESS_INTERVAL'; // 清除进度定时器

export const GET_SYSTEM_FAULT = '@GET_SYSTEM_FAULT';
export const GET_SYSTEM_FAULT_ONCE = '@GET_SYSTEM_FAULT_ONCE';
export const PUSH_SYSTEM_FAULT = '@PUSH_SYSTEM_FAULT';

export enum LogType {
  debug = 'debug',
  info = 'info',
  error = 'error',
  warn = 'warn',
}
