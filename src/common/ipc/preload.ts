// eslint-disable-next-line @typescript-eslint/no-var-requires
import { Ip<PERSON><PERSON><PERSON>er<PERSON><PERSON>, contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron';
import {
  <PERSON>O<PERSON><PERSON>,
  GET_USER_SESSION,
  LOGOUT,
  OPEN_FILE_MODAL,
  UPLOAD_FILE,
  SET_RENDER_LOG,
  SHUTDOWN,
  WRITE_FILE,
  TRIGGER_QUERY,
  TRIGGER_SETTING,
  SET_PASSWORD,
  GET_OS_USER_INFO,
  SET_TREATMENT_PLAN,
  QUERY_TREATMENT,
  SET_TREATMENT_THRESHOLD,
  GET_FILE_MD5,
  SET_RENDER,
  GET_DIR_NAME,
  LOKI_LOG,
  CLEAR_DATA_POOL,
  AUTO_QUERY_COIL,
  SET_TREAT_LOG,
  TMS_VERSION,
  SYSTEM_EXCEPTION,
  GET_LOGS_INFO,
  GET_LOG_PATH,
  SET_BEAT_SCREEM,
  SET_STORE,
  GET_STORE,
  SET_TREATMENT_LEVEL,
  SET_BAT_DATA,
  GET_BAT_DATA,
  TUNNEL_INFO,
  LogType,
  GET_TIMEOUT_ERROR,
  SET_TREAT_MATRIX_LOG,
  NOIMAGE_TREATMENT_PLAN_START,
  IMAGE_TREATMENT_PLAN_START,
  SET_CLEAR_PROCESS_INTERVAL,
  GET_SYSTEM_FAULT_ONCE,
  GET_SYSTEM_FAULT,
  PUSH_SYSTEM_FAULT,
  REMOVE_FILE,
} from './ipcChannels';
import { LoginParams } from '@/common/types';
import { BEAT_BTN, COIL_QUERY, GET_SOCK_INFO, TREAT_WARNING, WATER_COOLING_QUERY, WORK_STATUS } from '../constant/tms';
import { SelectTypeEnmu } from '../../renderer/container/manage/components/exportTreatmentReport/config';
import { FaultKeyEnum, FaultStatusEnum } from '../systemFault/type';

const btn_cache = {};
const sock_info_cache = {};
const coil_info_cache = {};
const error_cache = {};
const cooling_water_cache = {};

contextBridge.exposeInMainWorld('authAPI', {
  login: async (logDto: LoginParams) => ipcRenderer.invoke(LOGIN, logDto),
  logout: async () => ipcRenderer.invoke(LOGOUT),
  getUserSession: async () => ipcRenderer.invoke(GET_USER_SESSION),
  setPassword: async () => ipcRenderer.invoke(SET_PASSWORD),
});

contextBridge.exposeInMainWorld('testAPI', {
  test: async (logDto: LoginParams) => ipcRenderer.invoke(LOGIN, logDto),
});
contextBridge.exposeInMainWorld('fileAPI', {
  removeFile: async (filePath: string) => {
    return ipcRenderer.invoke(REMOVE_FILE, filePath);
  },
  getLogPath: async () => {
    return ipcRenderer.invoke(GET_LOG_PATH);
  },
  getLogsInfo: async (filePath: string) => {
    return ipcRenderer.invoke(GET_LOGS_INFO, filePath);
  },
  getFolderInfo: async (filePath: string, extList?: string[]) => {
    return ipcRenderer.invoke(OPEN_FILE_MODAL, { filePath, extList });
  },
  getFile: async (filePath: string, options?: { encoding: BufferEncoding; flag?: string } | BufferEncoding) => {
    return ipcRenderer.invoke('GET_FILE', filePath, options);
  },
  getFileJson: async (filePath: string) => {
    return ipcRenderer.invoke('GET_FILE_JSON', filePath);
  },
  uploadFile: async (filePath: string) => {
    return ipcRenderer.invoke(UPLOAD_FILE, filePath);
  },
  setRenderLog: async (type: LogType, log: string) => {
    return ipcRenderer.invoke(SET_RENDER_LOG, type, log);
  },
  setSystemExceptionLog: async (log: string) => {
    return ipcRenderer.invoke(SYSTEM_EXCEPTION, log);
  },
  setTreatLog: async (log: string) => {
    return ipcRenderer.invoke(SET_TREAT_LOG, log);
  },
  writeFileToDisk: async (output: string, bufferData: ArrayBuffer) => {
    return ipcRenderer.invoke(WRITE_FILE, output, bufferData);
  },
  getFileMd5: async (path: string) => {
    return ipcRenderer.invoke(GET_FILE_MD5, path);
  },
  getDirName: async () => {
    return ipcRenderer.invoke(GET_DIR_NAME);
  },
  setHistoryLog: (log: string) => {
    ipcRenderer
      .invoke('SET_HISTORY_LOG', log)
      .then()
      .catch(err => {
        //
      });
  },
  setTreatMatrixLog: (log: string) => {
    ipcRenderer
      .invoke(SET_TREAT_MATRIX_LOG, log)
      .then()
      .catch(err => {
        //
      });
  },
  setBatData: async (str: string) => {
    return ipcRenderer.invoke(SET_BAT_DATA, str);
  },

  getBatData: async () => {
    return ipcRenderer.invoke(GET_BAT_DATA);
  },
});
contextBridge.exposeInMainWorld('systemAPI', {
  setStore: async (key: string, value: any) => ipcRenderer.invoke(SET_STORE, key, value),
  getStore: async (key: string) => ipcRenderer.invoke(GET_STORE, key),
  shutdown: async () => ipcRenderer.invoke(SHUTDOWN),
  getSetting: async () => ipcRenderer.invoke('GET_SETTING'),
  getStoragePath: async () => ipcRenderer.invoke('GET_STORAGE_PATH'),
  checkDisk80Percent: async () => ipcRenderer.invoke('CHECK_DISK_80_PERCENT'),
  checkDiskSmart: async () => ipcRenderer.invoke('CHECK_DISK_SMART'),
  getDiskDetailSmart: async () => ipcRenderer.invoke('GET_DISK_DETAIL_SMART'),
  getPduInfo: async () => ipcRenderer.invoke('GET_PDU_INFO'),
  checkDiskCapacity: async () => ipcRenderer.invoke('CHECK_DISK_CAPACITY'),
  getOsUserInfo: async () => ipcRenderer.invoke(GET_OS_USER_INFO),
  getLoginVersion: async () => ipcRenderer.invoke('GET_LOGIN_VERSION'),
  unmoutUSB: async (usbPath: string) => ipcRenderer.invoke('UNMOUNT_USB', usbPath),
  setLokiLog: async (params: any) => ipcRenderer.invoke(LOKI_LOG, params),
  getZipProgress: async (callback: (event: IpcRendererEvent, data: any) => void) => ipcRenderer.on('GET_ZIP_PROGRESS', callback),
  removeZipProgressListener: (type: SelectTypeEnmu) => {
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    ipcRenderer.invoke(SET_CLEAR_PROCESS_INTERVAL, type);
    ipcRenderer.removeAllListeners('GET_ZIP_PROGRESS');
  },
  stopZip: async () => ipcRenderer.invoke('STOP_ZIP'),
  getTimeOutError: (key: string, callback: (event: IpcRendererEvent, data: any) => void) => {
    error_cache[key] = callback;
    ipcRenderer.on(GET_TIMEOUT_ERROR, callback);
  },
  removeGetTimeOutError: async (key: string) => {
    ipcRenderer.removeListener(GET_TIMEOUT_ERROR, error_cache[key]);
    error_cache[key] = null;
  },

  pushSystemFault: (faultList: { [key in FaultKeyEnum]?: FaultStatusEnum }, log: string) => ipcRenderer.send(PUSH_SYSTEM_FAULT, faultList, log),
  getSystemFaultOnce: async () => ipcRenderer.invoke(GET_SYSTEM_FAULT_ONCE),
  getSystemFault: (cb: (event: IpcRendererEvent, data: any) => void) => {
    error_cache[GET_SYSTEM_FAULT] = cb;
    ipcRenderer.on(GET_SYSTEM_FAULT, cb);
  },

  // 登录后，添加订阅，登出后移除订阅；因为render 和 main 即便调用了同一个common文件，但是还是不同的 进程中对象，所以需要在main中订阅，然后在render中触发
  subscribeHttpClient: async (callback: (event: IpcRendererEvent, data: any) => void) => ipcRenderer.on('SUBSCRIBE_HTTP_CLIENT', callback),
  unsubscribeHttpClient: async () => ipcRenderer.removeAllListeners('SUBSCRIBE_HTTP_CLIENT'),
  // 急停状态推送给render
  subscribeSafetyBtn: async (callback: (event: IpcRendererEvent, data: any) => void) => ipcRenderer.on('GET_SAFETY_BTN', callback),
  unsubscribeSafetyBtn: async () =>
    ipcRenderer.removeListener('GET_SAFETY_BTN', () => {
      //
    }),
  getEnv: async (key: string) => ipcRenderer.invoke('GET_ENV', key),
  getProductInfo: async () => ipcRenderer.invoke('GET_PRODUCT_INFO'),
  getNAV: async () => ipcRenderer.invoke('GET_NAV'),
});
contextBridge.exposeInMainWorld('tmsAPI', {
  // 设置治疗参数
  set_treatment_plan: async (param: any, noImage?: boolean) => {
    return ipcRenderer.invoke(SET_TREATMENT_PLAN, param, noImage);
  },
  set_beat_screen: async (param: any) => {
    return ipcRenderer.invoke(SET_BEAT_SCREEM, param);
  },
  // 开始无影像治疗
  noImage_treatment_plan_start: async (command: string, tid?: string) => {
    return ipcRenderer.invoke(NOIMAGE_TREATMENT_PLAN_START, command, tid);
  },
  // 开始影像治疗
  image_treatment_plan_start: async (command: string, tid?: string) => {
    return ipcRenderer.invoke(IMAGE_TREATMENT_PLAN_START, command, tid);
  },
  // 治疗数据查询
  query_treatment: async () => {
    return ipcRenderer.invoke(QUERY_TREATMENT);
  },
  get_tms_version: async () => {
    return ipcRenderer.invoke(TMS_VERSION);
  },
  get_tunnel_info: async () => {
    return ipcRenderer.invoke(TUNNEL_INFO);
  },
  // trigger查询
  triggerQuery: async () => {
    return ipcRenderer.invoke(TRIGGER_QUERY);
  },
  // trigger查询
  triggerSetting: async (triggerFlag: {
    in?: 0 | 1; // 0：关闭状态，1：打开状态，
    out?: 0 | 1; // 0：关闭状态，1：打开状态，
  }) => {
    return ipcRenderer.invoke(TRIGGER_SETTING, triggerFlag);
  },
  set_treatment_threshold: async (param: any) => {
    return ipcRenderer.invoke(SET_TREATMENT_THRESHOLD, param);
  },
  // 警告
  treat_warning: async (callback: (event: IpcRendererEvent, data: any) => void) => ipcRenderer.on(TREAT_WARNING, callback),
  // 拍子按下新版
  beat_btn_by_key: async (key: string, callback: (event: IpcRendererEvent, data: any) => void) => {
    btn_cache[key] = callback;

    return ipcRenderer.on(BEAT_BTN, callback);
  },
  // 移除拍子按下的监听事件
  remove_beat_btn_by_key: (key: string) => {
    if (btn_cache[key]) {
      ipcRenderer.removeListener(BEAT_BTN, btn_cache[key]);
      btn_cache[key] = null;
    }
  },
  // 移除所有拍子按键监听
  remove_all_beat_btn: async () => ipcRenderer.removeAllListeners(BEAT_BTN),
  // 设备状态检查
  working_status: async (callback: (event: IpcRendererEvent, data: any) => void) => ipcRenderer.on(WORK_STATUS, callback),
  // 获取socket信息
  get_sock_info: async (callback: (event: IpcRendererEvent, data: any) => void) => ipcRenderer.on(GET_SOCK_INFO, callback),
  // 获取socket信息 新版
  get_sock_info_by_key: async (key: string, callback: (event: IpcRendererEvent, data: any) => void) => {
    sock_info_cache[key] = callback;

    return ipcRenderer.on(GET_SOCK_INFO, callback);
  },
  remove_sock_info_by_key: (key: string) => {
    if (sock_info_cache[key]) {
      ipcRenderer.removeListener(GET_SOCK_INFO, sock_info_cache[key]);
      sock_info_cache[key] = null;
    }
  },
  // 获取拍子信息 新版
  get_coil_info_by_key: async (key: string, callback: (event: IpcRendererEvent, data: any) => void) => {
    coil_info_cache[key] = callback;

    return ipcRenderer.on(COIL_QUERY, callback);
  },
  remove_coil_info_by_key: (key: string) => {
    if (coil_info_cache[key]) {
      ipcRenderer.removeListener(COIL_QUERY, coil_info_cache[key]);
      coil_info_cache[key] = null;
    }
  },
  // 获取水冷信息
  water_cooling_query_by_key: async (key: string, callback: (event: IpcRendererEvent, data: any) => void) => {
    cooling_water_cache[key] = callback;

    return ipcRenderer.on(WATER_COOLING_QUERY, callback);
  },
  remove_water_cooling_query_by_key: (key: string) => {
    if (cooling_water_cache[key]) {
      ipcRenderer.removeListener(WATER_COOLING_QUERY, cooling_water_cache[key]);
      cooling_water_cache[key] = null;
    }
  },
  // 拍子信息
  coil_query: async (callback: (event: IpcRendererEvent, data: any) => void) => ipcRenderer.on(COIL_QUERY, callback),
  water_cooling_query: async (callback: (event: IpcRendererEvent, data: any) => void) => ipcRenderer.on(WATER_COOLING_QUERY, callback),
  // 设置render加载
  set_render: async () => {
    return ipcRenderer.invoke(SET_RENDER);
  },
  // 页面刷新时重制datapool
  clear_data_pool: async () => {
    return ipcRenderer.invoke(CLEAR_DATA_POOL);
  },
  auto_query_coil: async () => {
    return ipcRenderer.invoke(AUTO_QUERY_COIL);
  },
  // 设置治疗参数
  set_treatment_level: async (level: number, level_relative: number) => {
    return ipcRenderer.invoke(SET_TREATMENT_LEVEL, level, level_relative);
  },
});
