export const WORK_STATUS = 'working_status';
export const FAULT = 'fault';
export const TREAT_WARNING = 'treat_warning';
export const COIL_QUERY = 'coil_query';
export const SET_TREATMENT_PLAN = 'set_treatment_plan';
export const START_TREATMENT = 'start_treatment';
export const SET_BEAT_SCREEM = 'set_beat_screen';
export const QUERY_TREATMENT = 'query_treatment';
export const BEAT = 'beat';
export const BEAT_BTN = 'beat_btn';
export const TRIGGER_QUERY = 'trigger_query';
export const TRIGGER_SETTING = 'trigger_setting';
export const STATUS = 'status';
export const DEVICE_STATUS = 'device_status';
export const SET_TREATMENT_THRESHOLD = 'set_treatment_threshold';
export const COMMIT = 'commit';
export const HEARTBEAT = 'heartbeat';
export const GET_SOCK_INFO = 'get_sock_info';
export const WATER_COOLING_QUERY = 'water_cooling_query';
export const GET_COIL_INFO = 'get_coil_info';
export const TMS_VERSION = 'version';
export const TUNNEL_INFO = 'info';
export const SET_LEVEL = 'set_level';

export enum TMSScreenState {
  NotStarted = 0,
  Preparation = 1,
  SingleTreat = 2,
  PlanTreat = 3,
  PlanSuspend = 4,
}

/**
 * tms的请求回复结果枚举
 * 0： 成功  非0：失败
 */
export enum TmsResponseEnum {
  success = 0,
  failure = 1,
}
