export enum FaultKeyEnum {
  /** 软件系统故障*/
  A010001 = '0A010001',
  /** 系统服务故障*/
  A010002 = '0A010002',
  /** 磁盘容量不足*/
  A010003 = '0A010003',
  /** 磁盘故障*/
  A010004 = '0A010004',
  /** 严重磁盘故障*/
  A010005 = '0A010005',
  /** 电源系统通讯故障*/
  A020001 = '0A020001',
  /** 主机上电检测故障*/
  A020002 = '0A020002',
  /** TMS通讯故障*/
  A030001 = '0A030001',
  /** TMS自检故障*/
  A030002 = '0A030002',
  /** TMS充电故障*/
  A030003 = '0A030003',
  /** TMS放电故障*/
  A030004 = '0A030004',
  /** TMS状态错误*/
  A030005 = '0A030005',
  /** 电容放电次数超限*/
  A030320 = '0A030320',
  /** 线圈未连接/通讯故障*/
  A040001 = '0A040001',
  /** 线圈类型错误*/
  A040002 = '0A040002',
  /** 线圈SN序列号错误*/
  A040003 = '0A040003',
  /** 线圈故障*/
  A040004 = '0A040004',
  /** 线圈超温*/
  A040005 = '0A040005',
  /** 线圈温度传感器A1故障*/
  A040006 = '0A040006',
  /** 线圈温度传感器A2故障*/
  A040007 = '0A040007',
  /** 真面线圈温度传感器温差过大*/
  A040008 = '0A040008',
  /** 线圈位姿传感器故障*/
  A040009 = '0A040009',
  /** 线圈位姿错误*/
  A040010 = '0A040010',
  /** 线圈温度传感器B1故障*/
  A040011 = '0A040011',
  /** 线圈温度传感器B2故障*/
  A040012 = '0A040012',
  /** 伪面线圈温度传感器温差过大*/
  A040013 = '0A040013',
  /** 线圈存储器故障*/
  A04000A = '0A04000A',
  /** 线圈临近使用期限*/
  A04000B = '0A04000B',
  /** 线圈过期 与A04000B 互斥 */
  A04000C = '0A04000C',
  /** 线圈临近最大使用次数*/
  A04000D = '0A04000D',
  /** 线圈超过最大使用次数 与 A04000D逻辑代码互斥 */
  A04000E = '0A04000E',
  /** 线圈按键故障 */
  A04000F = 'A04000F',
  /** 冷却系统未连接/通讯故障*/
  A060001 = '0A060001',
  /** 冷却系统自检故障*/
  A060002 = '0A060002',
  /** 冷却系统温度传感器故障*/
  A060003 = '0A060003',
  /** 冷却系统泵故障*/
  A060004 = '0A060004',
  /** 冷却系统散热器故障*/
  A060005 = '0A060005',
  /** 冷却系统液路故障*/
  A060006 = '0A060006',
  /** 冷却系统缺液*/
  A060007 = '0A060007',
  /** 冷却系统过载*/
  A060008 = '0A060008',
  /** 室温过高*/
  A060009 = '0A060009',
  /** 冷却系统风扇1故障*/
  A060011 = '0A060011',
  /** 冷却系统风扇2故障*/
  A060012 = '0A060012',
  /** 冷却系统风扇3故障*/
  A060013 = '0A060013',
  /** 冷却系统风扇4故障*/
  A060014 = '0A060014',
  /** 冷却系统风扇5故障*/
  A060015 = '0A060015',
  /** 冷却系统风扇6故障*/
  A060016 = '0A060016',
  /** 视觉系统故障*/
  A080001 = '0A080001',
  /** 视觉系统相机故障*/
  A080002 = '0A080002',
  /** 视觉校准文件丢失 */
  A080003 = '0A080003',
}

export enum FaultLevelEnum {
  /** 停机 */
  shutdown = 1,
  /** 待机 */
  standby = 2,
  /** 错误，自定义，方便计算 */
  error = 3,
  /** 警告 */
  warning = 4,
  /** 正常 */
  normal = 7,
}

export enum FaultEnum {
  /** 系统 */
  systemFault = '0A01',
  /** pdu */
  pduFault = '0A02',
  /** tms */
  tmsFault = '0A03',
  /** 线圈 */
  coilFault = '0A04',
  /** 液冷 */
  coolingFault = '0A06',
  /** 相机 */
  imageFault = '0A08',
  /** 未知 */
  unKnow = '0A0A',
}

export enum FaultSourceEnum {
  /** tms */
  tmsFault = 'tmsFault',
  /** pdu */
  pduFault = 'pduFault',
  /** self */
  selfFault = 'selfFault',
}

/** 正常后后续操作 */
export enum FaultNormalSubEnum {
  initTms = 'initTms',
}

export type FaultMapItemType = {
  /** 故障等级 */
  errorLevel: FaultLevelEnum;
  /** 故障类型 */
  type: FaultEnum;
  /** 故障来源 */
  source: FaultSourceEnum;
  /** 故障code */
  key: FaultKeyEnum | string;
  /** 内容 */
  content: string;
  /** 描述 */
  description: string;
  /** 发生时间 */
  createAt?: number;
  /** 后缀描述 */
  suffix?: string;
  /** 正常后后续操作 */
  subOperation?: FaultNormalSubEnum;
  /** 出现后后续操作 */
  abnormalOperation?: FaultNormalSubEnum;
};

export type FaultMapConfigType = {
  [key in string]: FaultMapItemType;
};

export type Fault2RenderItemType = FaultMapItemType & {
  createAt: number;
};

export type Fault2RenderMapType = {
  [FaultLevelEnum.warning]: Fault2RenderItemType[];
  [FaultLevelEnum.error]: Fault2RenderItemType[];
};

export enum FaultStatusEnum {
  /** 正常 */
  normal,
  /** 异常 */
  abnormal,
}
