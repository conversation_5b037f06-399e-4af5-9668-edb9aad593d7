import { FaultEnum, FaultKeyEnum, FaultLevelEnum, FaultMapConfigType, FaultNormalSubEnum, FaultSourceEnum, FaultStatusEnum } from './type';

export const faultMapConfig: FaultMapConfigType = {
  '0A010001': {
    key: FaultKeyEnum.A010001,
    content: '软件系统故障',
    description: '软件系统故障',
    source: FaultSourceEnum.selfFault,
    type: FaultEnum.systemFault,
    errorLevel: FaultLevelEnum.shutdown,
  },
  '0A010002': {
    key: FaultKeyEnum.A010002,
    content: '系统服务故障',
    description: '系统服务故障',
    type: FaultEnum.systemFault,
    source: FaultSourceEnum.selfFault,
    errorLevel: FaultLevelEnum.standby,
  },
  '0A010003': {
    key: FaultKeyEnum.A010003,
    content: '磁盘故障',
    description: '磁盘容量不足',
    type: FaultEnum.systemFault,
    source: FaultSourceEnum.selfFault,
    errorLevel: FaultLevelEnum.warning,
  },
  '0A010004': {
    key: FaultKeyEnum.A010004,
    content: '磁盘故障',
    description: '磁盘故障',
    type: FaultEnum.systemFault,
    source: FaultSourceEnum.selfFault,
    errorLevel: FaultLevelEnum.warning,
  },
  '0A010005': {
    key: FaultKeyEnum.A010005,
    content: '磁盘故障',
    description: '严重磁盘故障',
    type: FaultEnum.systemFault,
    source: FaultSourceEnum.selfFault,
    errorLevel: FaultLevelEnum.shutdown,
  },
  '0A020001': {
    key: FaultKeyEnum.A020001,
    content: '电源系统故障',
    description: '电源系统通讯故障',
    type: FaultEnum.pduFault,
    source: FaultSourceEnum.pduFault,
    errorLevel: FaultLevelEnum.standby,
  },
  '0A020002': {
    key: FaultKeyEnum.A020002,
    content: '电源系统故障',
    description: '主机上电检测故障',
    type: FaultEnum.pduFault,
    source: FaultSourceEnum.pduFault,
    errorLevel: FaultLevelEnum.warning,
  },
  '0A030001': {
    key: FaultKeyEnum.A030001,
    content: 'TMS系统故障',
    description: 'TMS通讯故障',
    type: FaultEnum.tmsFault,
    source: FaultSourceEnum.selfFault,
    errorLevel: FaultLevelEnum.standby,
    subOperation: FaultNormalSubEnum.initTms,
    abnormalOperation: FaultNormalSubEnum.initTms,
  },
  '0A030002': {
    key: FaultKeyEnum.A030002,
    content: 'TMS系统故障',
    description: 'TMS自检故障',
    type: FaultEnum.tmsFault,
    source: FaultSourceEnum.tmsFault,
    errorLevel: FaultLevelEnum.standby,
  },
  '0A030003': {
    key: FaultKeyEnum.A030003,
    content: 'TMS系统故障',
    description: 'TMS充电故障',
    type: FaultEnum.tmsFault,
    source: FaultSourceEnum.tmsFault,
    errorLevel: FaultLevelEnum.standby,
  },
  '0A030004': {
    key: FaultKeyEnum.A030004,
    content: 'TMS系统故障',
    description: 'TMS放电故障',
    type: FaultEnum.tmsFault,
    source: FaultSourceEnum.tmsFault,
    errorLevel: FaultLevelEnum.shutdown,
  },
  '0A030005': {
    key: FaultKeyEnum.A030005,
    content: 'TMS系统故障',
    description: 'TMS状态错误',
    type: FaultEnum.tmsFault,
    source: FaultSourceEnum.selfFault,
    errorLevel: FaultLevelEnum.standby,
  },
  '0A030320': {
    key: FaultKeyEnum.A030320,
    content: 'TMS系统故障',
    description: '电容放电次数超限',
    type: FaultEnum.tmsFault,
    source: FaultSourceEnum.selfFault,
    errorLevel: FaultLevelEnum.warning,
  },
  '0A040001': {
    key: FaultKeyEnum.A040001,
    content: '线圈系统故障',
    description: '线圈未连接/通讯故障',
    type: FaultEnum.coilFault,
    source: FaultSourceEnum.tmsFault,
    errorLevel: FaultLevelEnum.shutdown,
  },
  '0A040002': {
    key: FaultKeyEnum.A040002,
    content: '线圈系统故障',
    description: '线圈类型错误',
    type: FaultEnum.coilFault,
    source: FaultSourceEnum.selfFault,
    errorLevel: FaultLevelEnum.shutdown,
  },
  '0A040003': {
    key: FaultKeyEnum.A040003,
    content: '线圈系统故障',
    description: '线圈SN序列号错误',
    type: FaultEnum.coilFault,
    source: FaultSourceEnum.selfFault,
    errorLevel: FaultLevelEnum.shutdown,
  },
  '0A040004': {
    key: FaultKeyEnum.A040004,
    content: '线圈系统故障',
    description: '线圈故障',
    type: FaultEnum.coilFault,
    source: FaultSourceEnum.tmsFault,
    errorLevel: FaultLevelEnum.shutdown,
  },
  '0A040005': {
    key: FaultKeyEnum.A040005,
    content: '线圈系统故障',
    description: '线圈超温',
    type: FaultEnum.coilFault,
    source: FaultSourceEnum.tmsFault,
    errorLevel: FaultLevelEnum.standby,
  },
  '0A040006': {
    key: FaultKeyEnum.A040006,
    content: '线圈系统故障',
    description: '线圈温度传感器A1故障',
    type: FaultEnum.coilFault,
    source: FaultSourceEnum.tmsFault,
    errorLevel: FaultLevelEnum.shutdown,
  },
  '0A040007': {
    key: FaultKeyEnum.A040007,
    content: '线圈系统故障',
    description: '线圈温度传感器A2故障',
    type: FaultEnum.coilFault,
    source: FaultSourceEnum.tmsFault,
    errorLevel: FaultLevelEnum.shutdown,
  },
  '0A040008': {
    key: FaultKeyEnum.A040008,
    content: '线圈系统故障',
    description: '真面线圈温度传感器温差过大',
    type: FaultEnum.coilFault,
    source: FaultSourceEnum.tmsFault,
    errorLevel: FaultLevelEnum.warning,
  },
  '0A040009': {
    key: FaultKeyEnum.A040009,
    content: '线圈系统故障',
    description: '线圈位姿传感器故障',
    type: FaultEnum.coilFault,
    source: FaultSourceEnum.tmsFault,
    errorLevel: FaultLevelEnum.warning,
  },
  '0A040010': {
    key: FaultKeyEnum.A040010,
    content: '线圈系统故障',
    description: '线圈位姿错误',
    type: FaultEnum.coilFault,
    source: FaultSourceEnum.tmsFault,
    errorLevel: FaultLevelEnum.warning,
  },
  '0A040011': {
    key: FaultKeyEnum.A040011,
    content: '线圈系统故障',
    description: '线圈温度传感器B1故障',
    type: FaultEnum.coilFault,
    source: FaultSourceEnum.tmsFault,
    errorLevel: FaultLevelEnum.shutdown,
  },
  '0A040012': {
    key: FaultKeyEnum.A040012,
    content: '线圈系统故障',
    description: '线圈温度传感器B2故障',
    type: FaultEnum.coilFault,
    source: FaultSourceEnum.tmsFault,
    errorLevel: FaultLevelEnum.shutdown,
  },
  '0A040013': {
    key: FaultKeyEnum.A040013,
    content: '线圈系统故障',
    description: '伪面线圈温度传感器温差过大',
    type: FaultEnum.coilFault,
    source: FaultSourceEnum.tmsFault,
    errorLevel: FaultLevelEnum.warning,
  },
  '0A04000A': {
    key: FaultKeyEnum.A04000A,
    content: '线圈系统故障',
    description: '线圈存储器故障',
    type: FaultEnum.coilFault,
    source: FaultSourceEnum.tmsFault,
    errorLevel: FaultLevelEnum.shutdown,
  },
  '0A04000B': {
    key: FaultKeyEnum.A04000B,
    content: '线圈过期',
    description: '线圈临近使用期限',
    type: FaultEnum.coilFault,
    source: FaultSourceEnum.selfFault,
    errorLevel: FaultLevelEnum.warning,
  },
  '0A04000C': {
    key: FaultKeyEnum.A04000C,
    content: '线圈过期',
    description: '线圈过期',
    type: FaultEnum.coilFault,
    source: FaultSourceEnum.selfFault,
    errorLevel: FaultLevelEnum.shutdown,
  },
  '0A04000D': {
    key: FaultKeyEnum.A04000D,
    content: '线圈过期',
    description: '线圈临近最大使用次数',
    type: FaultEnum.coilFault,
    source: FaultSourceEnum.selfFault,
    errorLevel: FaultLevelEnum.warning,
  },
  '0A04000E': {
    key: FaultKeyEnum.A04000E,
    content: '线圈过期',
    description: '线圈超过最大使用次数',
    type: FaultEnum.coilFault,
    source: FaultSourceEnum.selfFault,
    errorLevel: FaultLevelEnum.shutdown,
  },
  '0A04000F': {
    key: FaultKeyEnum.A04000F,
    content: '线圈按键故障',
    description: '线圈按键故障',
    type: FaultEnum.coilFault,
    source: FaultSourceEnum.tmsFault,
    errorLevel: FaultLevelEnum.shutdown,
  },
  '0A060001': {
    key: FaultKeyEnum.A060001,
    content: '冷却系统故障',
    description: '冷却系统未连接/通讯故障',
    type: FaultEnum.coolingFault,
    source: FaultSourceEnum.tmsFault,
    errorLevel: FaultLevelEnum.shutdown,
  },
  '0A060002': {
    key: FaultKeyEnum.A060002,
    content: '冷却系统故障',
    description: '冷却系统自检故障',
    type: FaultEnum.coolingFault,
    source: FaultSourceEnum.tmsFault,
    errorLevel: FaultLevelEnum.shutdown,
  },
  '0A060003': {
    key: FaultKeyEnum.A060003,
    content: '冷却系统故障',
    description: '冷却系统温度传感器故障',
    type: FaultEnum.coolingFault,
    source: FaultSourceEnum.tmsFault,
    errorLevel: FaultLevelEnum.shutdown,
  },
  '0A060004': {
    key: FaultKeyEnum.A060004,
    content: '冷却系统故障',
    description: '冷却系统泵故障',
    type: FaultEnum.coolingFault,
    source: FaultSourceEnum.tmsFault,
    errorLevel: FaultLevelEnum.shutdown,
  },
  '0A060005': {
    key: FaultKeyEnum.A060005,
    content: '冷却系统故障',
    description: '冷却系统散热器故障',
    type: FaultEnum.coolingFault,
    source: FaultSourceEnum.tmsFault,
    errorLevel: FaultLevelEnum.shutdown,
  },
  '0A060006': {
    key: FaultKeyEnum.A060006,
    content: '冷却系统故障',
    description: '冷却系统液路故障',
    type: FaultEnum.coolingFault,
    source: FaultSourceEnum.tmsFault,
    errorLevel: FaultLevelEnum.shutdown,
  },
  '0A060007': {
    key: FaultKeyEnum.A060007,
    content: '冷却系统故障',
    description: '冷却系统缺液',
    type: FaultEnum.coolingFault,
    source: FaultSourceEnum.tmsFault,
    errorLevel: FaultLevelEnum.shutdown,
  },
  '0A060008': {
    key: FaultKeyEnum.A060008,
    content: '冷却系统故障',
    description: '冷却系统过载',
    type: FaultEnum.coolingFault,
    source: FaultSourceEnum.tmsFault,
    errorLevel: FaultLevelEnum.shutdown,
  },
  '0A060009': {
    key: FaultKeyEnum.A060009,
    content: '冷却系统故障',
    description: '室温过高',
    type: FaultEnum.coolingFault,
    source: FaultSourceEnum.selfFault,
    errorLevel: FaultLevelEnum.standby,
  },
  '0A060011': {
    key: FaultKeyEnum.A060011,
    content: '冷却系统故障',
    description: '冷却系统风扇1故障',
    type: FaultEnum.coolingFault,
    source: FaultSourceEnum.tmsFault,
    errorLevel: FaultLevelEnum.warning,
  },
  '0A060012': {
    key: FaultKeyEnum.A060012,
    content: '冷却系统故障',
    description: '冷却系统风扇2故障',
    type: FaultEnum.coolingFault,
    source: FaultSourceEnum.tmsFault,
    errorLevel: FaultLevelEnum.warning,
  },
  '0A060013': {
    key: FaultKeyEnum.A060013,
    content: '冷却系统故障',
    description: '冷却系统风扇3故障',
    type: FaultEnum.coolingFault,
    source: FaultSourceEnum.tmsFault,
    errorLevel: FaultLevelEnum.warning,
  },
  '0A060014': {
    key: FaultKeyEnum.A060014,
    content: '冷却系统故障',
    description: '冷却系统风扇4故障',
    type: FaultEnum.coolingFault,
    source: FaultSourceEnum.tmsFault,
    errorLevel: FaultLevelEnum.warning,
  },
  '0A060015': {
    key: FaultKeyEnum.A060015,
    content: '冷却系统故障',
    description: '冷却系统风扇5故障',
    type: FaultEnum.coolingFault,
    source: FaultSourceEnum.tmsFault,
    errorLevel: FaultLevelEnum.warning,
  },
  '0A060016': {
    key: FaultKeyEnum.A060016,
    content: '冷却系统故障',
    description: '冷却系统风扇6故障',
    type: FaultEnum.coolingFault,
    source: FaultSourceEnum.tmsFault,
    errorLevel: FaultLevelEnum.warning,
  },
  '0A080001': {
    key: FaultKeyEnum.A080001,
    content: '视觉系统故障',
    description: '视觉系统故障',
    type: FaultEnum.imageFault,
    source: FaultSourceEnum.selfFault,
    errorLevel: FaultLevelEnum.standby,
  },
  '0A080002': {
    key: FaultKeyEnum.A080002,
    content: '视觉系统故障',
    description: '视觉系统相机故障',
    type: FaultEnum.imageFault,
    source: FaultSourceEnum.selfFault,
    errorLevel: FaultLevelEnum.standby,
  },
  '0A080003': {
    key: FaultKeyEnum.A080003,
    content: '视觉系统故障',
    description: '视觉校准文件丢失',
    type: FaultEnum.imageFault,
    source: FaultSourceEnum.selfFault,
    errorLevel: FaultLevelEnum.standby,
  },
};

const getUnKnowFault = (key: string) => ({
  key: key,
  content: '未知故障',
  description: '未知故障',
  type: FaultEnum.unKnow,
  source: FaultSourceEnum.tmsFault,
  errorLevel: FaultLevelEnum.warning,
});

export const addUnKnowFault2Map = (key: string, source = FaultSourceEnum.tmsFault) => {
  if (faultMapConfig[key]) return;
  faultMapConfig[key] = getUnKnowFault(key);
  if (source === FaultSourceEnum.tmsFault) tmsSocketFault[key] = FaultStatusEnum.normal;
};

export const tmsSocketFault: { [key in string]: number } = Object.keys(faultMapConfig)
  .filter(v => faultMapConfig[v].source === FaultSourceEnum.tmsFault)
  .reduce((pre, cur) => ({ ...pre, [cur]: FaultStatusEnum.normal }), {});

export const FaultTypeList = [
  FaultEnum.systemFault,
  FaultEnum.pduFault,
  FaultEnum.coilFault,
  FaultEnum.coolingFault,
  FaultEnum.tmsFault,
  FaultEnum.imageFault,
];
export const notNAVTypeList = FaultTypeList.filter(v => v !== FaultEnum.imageFault);
