import axios, { AxiosError, AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import * as lodash from 'lodash';
import { IHttpClient } from './IHttpClient';
import { ApiError } from '../../types';
import * as rax from 'retry-axios';
import qs from 'query-string';
import { axiosHttpClient } from '@/common/api/httpClient/axiosHttpClientImplMain';
import { LogType } from '../../ipc/ipcChannels';
import { errorEventBus, omitListenUrlList, timeOutEvent } from '@/renderer/utils/errorEventBus';
import BaseLogger from '../../lib/appLogs/baseLogger';
import { sendRenderLog } from '../../../renderer/utils/renderLogger';

const statusMap = {
  503: '服务器开小差,请重试!',
  404: '连接失败',
  403: '您无权访问',
  400: '操作失败,请重试!',
};
const status400Map = {
  '请求参数错误，请检查后重试！': '操作失败,请重试!',
  您无权限访问: '您无权访问!',
};
const TIMEOUT = 60000;
const serverErr = ['ECONNABORTED', 'ENETUNREACH', 'ERR_NETWORK', 'ECONNREFUSED', 'ETIMEDOUT', 'ERR_BAD_RESPONSE'];
const transferMessage = (params: { status?: number; message?: string }) => {
  const { status = -1, message = '' } = params;
  if (status === 400) {
    return status400Map[message] || message;
  }

  return statusMap[status] || message;
};

export class AxiosHttpClientImpl implements IHttpClient {
  private static baseRaxConfig: rax.RetryConfig = {
    retryDelay: 1000,
    httpMethodsToRetry: ['GET', 'PUT', 'DELETE', 'POST'],
    statusCodesToRetry: [
      [100, 199],
      [408, 408],
      [411, 599],
    ],
    backoffType: 'static',
  };

  public client!: AxiosInstance;
  public resetHeader: { [props: string]: string | number };
  private baseURL: string;
  private authToken?: string;
  private language: string;
  private logInstance!: BaseLogger;

  constructor() {
    this.baseURL = '';
    this.initializeClient();
    this.resetHeader = {};
    this.language = 'zh-CN';
  }

  public setBaseUrl = (baseUrl: string) => {
    this.baseURL = baseUrl;
    this.initializeClient();
  };

  public setLanguage = (language: string) => {
    this.language = language;
  };

  public setLogInstance = (instance: BaseLogger) => {
    this.logInstance = instance;
  };

  public setAuthToken(authToken: string): void {
    this.authToken = authToken;
    this.client.defaults.headers = this.headers();
  }

  public resetAuthToken(): void {
    this.authToken = undefined;
    this.client.defaults.headers = this.headers();
  }

  public async restGetOne<TQuery, T>(url: string, query: TQuery, retryTimes?: number, timeout?: number): Promise<T> {
    try {
      const config: AxiosRequestConfig = {
        params: query,
        paramsSerializer: { serialize: this.serializeParams },
        headers: this.headers(),
      };
      if (retryTimes !== undefined && retryTimes > 0) {
        config.raxConfig = {
          ...AxiosHttpClientImpl.baseRaxConfig,
          retry: retryTimes,
          noResponseRetries: retryTimes,
          instance: this.client,
        };
      }
      config.timeout = !timeout || timeout < TIMEOUT ? TIMEOUT : timeout;
      // assuming the ID is embdedded in the url
      let response: AxiosResponse<T> = await this.client.get(url, config);

      return response.data;
    } catch (err: any) {
      this.handleHttpError(err, url);
      throw this.createError(err);
    }
  }

  public async restGet<TParams, T>(url: string, query: TParams, retryTimes?: number, timeout?: number): Promise<T[]> {
    try {
      const config: AxiosRequestConfig = {
        params: query,
        paramsSerializer: { serialize: this.serializeParams },
        headers: this.headers(),
      };
      if (retryTimes !== undefined && retryTimes > 0) {
        config.raxConfig = {
          ...AxiosHttpClientImpl.baseRaxConfig,
          retry: retryTimes,
          noResponseRetries: retryTimes,
          instance: this.client,
        };
      }
      config.timeout = !timeout || timeout < TIMEOUT ? TIMEOUT : timeout;
      // data contains information such as query criteria and/or sorting, and etc
      let response: AxiosResponse<T[]> = await this.client.get(url, config);

      return response.data;
    } catch (err: any) {
      this.handleHttpError(err, url);
      throw this.createError(err);
    }
  }

  public async restPut<TData, T>(url: string, data: TData, retryTimes?: number, timeout?: number): Promise<T> {
    try {
      const config: AxiosRequestConfig = {
        headers: this.headers(),
      };
      if (retryTimes !== undefined && retryTimes > 0) {
        config.raxConfig = {
          ...AxiosHttpClientImpl.baseRaxConfig,
          retry: retryTimes,
          noResponseRetries: retryTimes,
          instance: this.client,
        };
      }
      config.timeout = !timeout || timeout < TIMEOUT ? TIMEOUT : timeout;
      let response: AxiosResponse<T> = await this.client.put(url, data, config);

      return response.data;
    } catch (err: any) {
      this.handleHttpError(err, url);
      throw this.createError(err);
    }
  }

  public async restDelete<T>(url: string, retryTimes?: number, timeout?: number): Promise<T> {
    try {
      const config: AxiosRequestConfig = {
        headers: this.headers(),
      };
      if (retryTimes !== undefined && retryTimes > 0) {
        config.raxConfig = {
          ...AxiosHttpClientImpl.baseRaxConfig,
          retry: retryTimes,
          noResponseRetries: retryTimes,
          instance: this.client,
        };
      }
      config.timeout = !timeout || timeout < TIMEOUT ? TIMEOUT : timeout;
      let response: AxiosResponse<T> = await this.client.delete(url, config);

      return response.data;
    } catch (err: any) {
      this.handleHttpError(err, url);
      throw this.createError(err);
    }
  }

  public async restPost<TData, T>(url: string, data: TData, retryTimes?: number, timeout?: number): Promise<T> {
    this.writeRenderLog(LogType.info, `接口请求：${url}`);
    this.writeRenderLog(LogType.debug, `data：${url}`);
    this.writeRenderLog(LogType.debug, `header：${JSON.stringify(this.headers())}`);
    try {
      const config: AxiosRequestConfig = {
        headers: this.headers(),
      };
      config.timeout = !timeout || timeout < TIMEOUT ? TIMEOUT : timeout;
      if (retryTimes !== undefined && retryTimes > 0) {
        config.raxConfig = {
          ...AxiosHttpClientImpl.baseRaxConfig,
          retry: retryTimes,
          noResponseRetries: retryTimes,
          instance: this.client,
        };
      }
      let response: AxiosResponse<T> = await this.client.post(url, data, config);
      this.writeRenderLog(LogType.debug, `接口请求：${url}\n response:${JSON.stringify(response.data)}`);

      return response.data;
    } catch (err: any) {
      // eslint-disable-next-line no-console
      console.log('rest Post error', err);
      this.handleHttpError(err, url);
      throw this.createError(err);
    }
  }

  /**
   * @param err
   * @param url
   * @private
   * @description 401 重新登录， 其他错误打印日志
   */
  private handleHttpError(err: AxiosError<{ error?: any; message?: string; code?: string }>, url: string): void {
    if (serverErr.some(v => v === err.code) && !omitListenUrlList.some(prefix => url.startsWith(prefix))) {
      errorEventBus.emit(timeOutEvent, true);
    }
    if (!this.authToken) {
      return;
    }
    const code = lodash.get(err, 'response.data.code');
    const data = lodash.get(err, 'response.data');
    if (err.response && err.response.status === 401) {
      location.replace('#/login');
      this.authToken = undefined;
      axiosHttpClient.resetAuthToken();
      this.writeRenderLog(LogType.error, `token问题 ${code} :${url}\n errorMessage: ${data?.message}`);

      return;
    }
    this.writeRenderLog(LogType.error, `http请求问题 ${code} :${url}\n errorCode: ${err.code} errorMessage: ${data?.message}`);
  }

  /**
   * @description 规范给前端接口的错误信息
   * @param err
   * @private
   */

  private createError(err: AxiosError<{ error?: any; message?: string; code?: string }>): ApiError {
    // this only resolved httpStatus, our server error code in err.response.data is ignored. see zenhub #181
    if (err.status === 404 || serverErr.some(v => v === err.code)) {
      return {
        code: err.code,
        status: 404,
        message: transferMessage({ status: 404, message: err.message }),
      };
    }
    if (err.code === 'ETIMEDOUT') {
      return {
        code: err.code,
        status: 404,
        message: '连接超时',
      };
    }
    const code = lodash.get(err, 'response.data.code');
    const data = lodash.get(err, 'response.data');
    const status = lodash.get(err, 'response.status');

    return {
      code: code ? code : data?.error,
      status: status ? status : -1,
      message: transferMessage({ status, message: data?.message }),
    };
  }

  private initializeClient(): void {
    const config: AxiosRequestConfig = {
      baseURL: this.baseURL,
      withCredentials: true,
      headers: {
        'Content-Type': 'application/json; charset=utf-8',
      },
    };
    // eslint-disable-next-line no-console
    console.log('common client config', config);

    this.client = axios.create(config);
    rax.attach(this.client);
  }

  private serializeParams(params: any): string {
    return qs.stringify(params);
  }

  private headers(): any {
    const headers: any = {};
    if (this.authToken) {
      headers.Authorization = `Bearer ${this.authToken}`;
    }

    Object.assign(headers, { 'accept-language': this.language });

    return headers;
  }

  /** renderLog 写入*/
  private writeRenderLog(type: LogType, log: string) {
    if (this.logInstance) {
      this.logInstance.writeLog(type, log);
    } else {
      switch (type) {
        case LogType.info:
          sendRenderLog.info(log);
          break;
        case LogType.debug:
          sendRenderLog.debug(log);
          break;
        case LogType.error:
          sendRenderLog.error(log);
          break;
        case LogType.warn:
          sendRenderLog.warn(log);
          break;

        default:
          break;
      }
    }
  }
}
