// eslint-disable-next-line unicorn/filename-case
export interface IHttpClient {
  setAuthToken(authToken: string): void;

  resetAuthToken(): void;

  restGetOne<TQuery, T>(url: string, query: TQuery, retryTimes?: number, timeout?: number): Promise<T>;

  restGet<TQuery, T>(url: string, query: TQuery, retryTimes?: number, timeout?: number): Promise<T[]>;

  restPut<TData, T>(url: string, data: TData, retryTimes?: number, timeout?: number): Promise<T>;

  restDelete<T>(url: string, retryTimes?: number, timeout?: number): Promise<T>;

  restPost<TData, T>(url: string, data: TData, retryTimes?: number, timeout?: number): Promise<T>;
}
