import { axiosHttpClient } from './httpClient/axiosHttpClientImplMain';
import { AuthApi } from './ng/authApi';
import { M200Api } from '@/common/api/ng/m200Api';

export const getAuthApiEndpoint = (): AuthApi => {
  return new AuthApi(axiosHttpClient);
};

let m200ApiInstance: M200Api | undefined;

export const setM200ApiInstance = () => {
  m200ApiInstance = new M200Api(axiosHttpClient);
};

export const getM200ApiInstance = (): M200Api => {
  if (!m200ApiInstance) {
    window.location.replace('#/login');
    // window.location.reload();

    return {} as M200Api;
  }

  return m200ApiInstance;
};
export const resetM200ApiInstance = () => {
  m200ApiInstance = undefined;
  // window.systemAPI.unsubscribeHttpClient();
};
