import {
  InstructionInfo,
  VisibleInfoType,
  initInstructionInfo,
  initVisibleInfo,
  initTechsupportInfo,
  TechsupportInfo,
} from '../../../renderer/container/manage/components/manageDevice/config';
import { IHttpClient } from '../httpClient/IHttpClient';
import {
  StimulusTemplatePageQueryModel,
  PageModelStimulusTemplateModelApi,
  CreateStimulusTemplateModelApi,
  StimulusTemplateModelApi,
  StimulusTemplateModel,
  FrontConfigQueryModel,
  FrontConfigModel,
  IPlanListParams,
  IPlanListResponse,
  ISubjectResponse,
  IReportModel,
  IGetUserListRequest,
  IUserListResponse,
  AddUserParams,
  IAddUserResponse,
  PlanLogQueryModel,
  PageModelPlanLogModel,
  ResetPWDRequest,
  EditUserRequest,
  UserModel,
  ChangePWDRequest,
  LicenseModel,
  MotionThresholdModel,
  PlanModel,
  IArchiveRequest,
  IBoxInfo,
  IBindingBoxParams,
  IBox,
  QueryPatientInfo,
  IResetFactoryParams,
  AddStimulusParams,
  StimulusItemModel,
  UpdateStimulateParams,
} from '@/common/types';

export class M200Api {
  private httpClient: IHttpClient;

  constructor(httpClient: IHttpClient) {
    this.httpClient = httpClient;
  }
  /**
   * 获取刺激模板列表
   * @param params
   * @param retryTimes
   */
  public async getStimulusTemplateList(
    params: StimulusTemplatePageQueryModel = { page_num: 1, page_size: 1000 },
    retryTimes?: number
  ): Promise<PageModelStimulusTemplateModelApi> {
    return this.httpClient.restPost<{}, PageModelStimulusTemplateModelApi>('pro/stimulus/template/page', params, retryTimes);
  }

  /**
   * 删除刺激模板
   * @param templateId
   * @param retryTimes
   */
  public async delStimulusTemplate(templateId: number, retryTimes?: number): Promise<{}> {
    return this.httpClient.restPost<any, boolean>(`/pro/stimulus/template/delete?id=${templateId}`, undefined);
  }

  /**
   * 创建刺激模板
   * @param templateInfo
   * @param retryTimes
   */
  public async createStimulusTemplate(templateInfo: CreateStimulusTemplateModelApi, retryTimes?: number): Promise<StimulusTemplateModel> {
    return this.httpClient.restPost<CreateStimulusTemplateModelApi, StimulusTemplateModel>('pro/stimulus/template/add', templateInfo, retryTimes);
  }

  /**
   * 更新刺激模板
   * @param templateInfo
   * @param templateId
   * @param retryTimes
   */
  public async updateStimulusTemplate(
    templateInfo: StimulusTemplateModelApi,
    templateId: number,
    retryTimes?: number
  ): Promise<StimulusTemplateModel> {
    return this.httpClient.restPost<StimulusTemplateModelApi, StimulusTemplateModel>('/pro/stimulus/template/edit', templateInfo, retryTimes);
  }

  /**
   * 检查刺激模板名称是否存在
   * @param name
   */
  public async checkStimulusTemplateName(name: string): Promise<boolean> {
    return this.httpClient.restPost(`/pro/stimulus/template/exists?name=${encodeURIComponent(name)}`, {});
  }

  /**
   * 获取配置项，例如获取最后一次登录名称
   * @param params
   */
  public async getConfig(params: FrontConfigQueryModel): Promise<FrontConfigModel[]> {
    return this.httpClient.restPost<FrontConfigQueryModel, FrontConfigModel[]>('/pro/front/config/list', params);
  }

  public async getControlConfig(): Promise<[TechsupportInfo, VisibleInfoType, InstructionInfo]> {
    const info = await this.getConfig({
      group_name: 'control',
    });
    const techsupportInfo: TechsupportInfo = JSON.parse(
      info.find(v => v.group_name === 'control' && v.name === 'techsupportInfo')?.value || JSON.stringify(initTechsupportInfo)
    );
    const visiblePart: VisibleInfoType = JSON.parse(
      info.find(v => v.group_name === 'control' && v.name === 'visibleInfo')?.value || JSON.stringify(initVisibleInfo)
    );
    const instructionPart: InstructionInfo = JSON.parse(
      info.find(v => v.group_name === 'control' && v.name === 'instructionInfo')?.value || JSON.stringify(initInstructionInfo)
    );
    if (!techsupportInfo.mainControl) {
      return [techsupportInfo, initVisibleInfo, initInstructionInfo];
    }

    return [techsupportInfo, visiblePart, instructionPart];
  }

  public async setConfig(params: FrontConfigModel[]): Promise<boolean> {
    return this.httpClient.restPost<FrontConfigModel[], boolean>('/pro/front/config', params);
  }

  public async setConfigByLog(params: FrontConfigModel[]): Promise<boolean> {
    return this.httpClient.restPost<FrontConfigModel[], boolean>('/pro/front/config/savewithlog', params);
  }

  public async getPlanById(planId: number, subjectId: number): Promise<PlanModel> {
    return this.httpClient.restPost<{}, PlanModel>(`/pro/plan/detail/${subjectId}/${planId}`, {});
  }

  public async importNoImagePlan(params: any): Promise<any> {
    return this.httpClient.restPost<FrontConfigQueryModel, FrontConfigModel[]>('/pro/plan/add/noimage', params);
  }

  public async importPlan(params: any): Promise<any> {
    return this.httpClient.restPost<FrontConfigQueryModel, FrontConfigModel[]>('/pro/plan/import', params);
  }

  public async editPlan(params: any): Promise<any> {
    return this.httpClient.restPost<FrontConfigQueryModel, FrontConfigModel[]>('/pro/plan/edit', params);
  }
  /**
   * 获取方案列表
   * @param data
   */
  public async getPlanList(data: IPlanListParams): Promise<IPlanListResponse> {
    return this.httpClient.restPost<IPlanListParams, IPlanListResponse>('/pro/plan/page', data);
  }
  /**
   * 获取方案总数
   * @param data
   */
  public async getPlanCount(): Promise<number> {
    return this.httpClient.restPost<{}, number>('/pro/plan/count', {});
  }
  /**
   * 技术支持：获取工作站信息
   */
  public async getBindStateInfo(): Promise<IBoxInfo> {
    return this.httpClient.restPost<{}, IBoxInfo>('/pro/device/info', {});
  }
  /**
   * 技术支持：绑定工作站
   * @param data IP、账号、密码
   */
  public async bindingBox(data: IBindingBoxParams): Promise<IBox> {
    return this.httpClient.restPost<IBindingBoxParams, IBox>('/pro/device/regist', data);
  }
  /**
   * 技术支持：解绑工作站
   */
  public async unbindingBox(): Promise<{}> {
    return this.httpClient.restPost<{}, {}>('/pro/device/discard', {});
  }
  /**
   * 技术支持：恢复出厂设置
   */
  public async resetFactory(params: IResetFactoryParams): Promise<any> {
    return this.httpClient.restPost<any, any>('/pro/device/resetFactory', params);
  }

  /**
   * 获取患者详情
   * @param id
   */
  public async getSubjectById(id: number): Promise<ISubjectResponse> {
    return this.httpClient.restPost<{}, ISubjectResponse>(`/pro/subject/detail/${id}`, {});
  }

  /**
   * 获取报告列表
   * @param planId
   */
  public async getReport(planId: number, type_list: number[]): Promise<IReportModel[]> {
    return this.httpClient.restPost<{ type_list: number[] }, IReportModel[]>(`/pro/plan/report/${planId}/query`, { type_list });
  }

  /**
   * 账号列表
   * @param data
   */
  public async getUserList(data: IGetUserListRequest): Promise<IUserListResponse> {
    return this.httpClient.restPost<IGetUserListRequest, IUserListResponse>('/pro/user/page', data);
  }

  /**
   * 添加账号
   * @param data
   */
  public async addUser(data: AddUserParams): Promise<IAddUserResponse[]> {
    return this.httpClient.restPost<AddUserParams, IAddUserResponse[]>('/pro/user/add', data);
  }

  /**
   * 获取日志记录
   * @param data
   */
  public async getLogs(data: PlanLogQueryModel): Promise<PageModelPlanLogModel> {
    return this.httpClient.restPost<PlanLogQueryModel, PageModelPlanLogModel>('/pro/plan/log/list', data);
  }
  public async saveTargetLine(params: any): Promise<any> {
    return this.httpClient.restPost<FrontConfigQueryModel, FrontConfigModel[]>('/pro/plan/target/save/line', params);
  }

  public async saveThreshold(params: MotionThresholdModel): Promise<any> {
    return this.httpClient.restPost<MotionThresholdModel, any>('/pro/subject/editMotionThreshold', params);
  }

  /**
   * 添加刺激方案
   */
  public async addStimulus(params: AddStimulusParams): Promise<any> {
    return this.httpClient.restPost<AddStimulusParams, any>('/pro/continue/stimulus/add', params);
  }

  /**
   * 获取刺激方案列表
   */
  public async stimulusList(): Promise<StimulusItemModel[]> {
    return this.httpClient.restPost<any, StimulusItemModel[]>('/pro/continue/stimulus/list', {});
  }

  /**
   * 更新刺激方案列表
   */
  public async setOrderStimulus(params: UpdateStimulateParams) {
    return this.httpClient.restPost<any, StimulusItemModel[]>('/pro/continue/stimulus/setOrder', params);
  }

  /**
   * 删除刺激方案
   */
  public async deleteStimulusItem(id: Number) {
    return this.httpClient.restPost<any, StimulusItemModel[]>(`/pro/continue/stimulus/delete?id=${id}`, {});
  }

  /**
   * 重置密码
   * @param data
   */
  public async resetPWD(data: ResetPWDRequest): Promise<boolean> {
    return this.httpClient.restPost<ResetPWDRequest, boolean>('/pro/user/resetPassword', data);
  }
  /**
   * 编辑用户
   * @param data
   */
  public async editUser(data: EditUserRequest): Promise<UserModel> {
    return this.httpClient.restPost<EditUserRequest, UserModel>('/pro/user/edit', data);
  }
  public async isSubjectExit(code: string): Promise<boolean> {
    return this.httpClient.restPost<undefined, boolean>(`/pro/subject/exists?code=${encodeURIComponent(code)}`, undefined);
  }

  /**
   * 修改密码
   * @param data
   */
  public async changePWD(data: ChangePWDRequest): Promise<boolean> {
    return this.httpClient.restPost<ChangePWDRequest, boolean>('/pro/user/changePassword', data);
  }

  /**
   * 获取license
   */
  public async getLicense(): Promise<LicenseModel> {
    // @ts-ignore
    return this.httpClient.restGet<{}, LicenseModel>('/pro/license/get', {});
  }
  /**
   * 导入license
   */
  public async importLicense(data: any): Promise<LicenseModel> {
    // @ts-ignore
    return this.httpClient.restPost<data, LicenseModel>('/pro/license/import', data);
  }

  /**
   * 获取方案信息
   */
  public async getPlan(subjectId: number | string, planId: number | string): Promise<PlanModel> {
    return this.httpClient.restPost<{}, PlanModel>(`/pro/plan/detail/${subjectId}/${planId}`, {});
  }

  /**
   * 更新刺激参数
   */
  public async updateStimulus(data: any): Promise<any> {
    // @ts-ignore
    return this.httpClient.restPost<data, any>('/pro/plan/stimulus/save', data);
  }

  /**
   * 开始刺激
   */
  public async beginTreat(data: any): Promise<any> {
    // @ts-ignore
    return this.httpClient.restPost<data, any>('/pro/plan/report/begin', data);
  }

  /**
   * 暂停刺激
   */
  public async pauseTreat(data: any): Promise<any> {
    // @ts-ignore
    return this.httpClient.restPost<data, any>('/pro/plan/report/pause', data);
  }

  /**
   * 终止刺激
   */
  public async endTreat(data: any): Promise<any> {
    // @ts-ignore
    return this.httpClient.restPost<data, any>('/pro/plan/report/end', data);
  }

  /**
   * 继续刺激
   */
  public async continueTreat(data: any): Promise<any> {
    // @ts-ignore
    return this.httpClient.restPost<data, any>('/pro/plan/report/continue', data);
  }
  /**
   * 归档数据
   */
  public async archiveFiled(data: IArchiveRequest): Promise<boolean> {
    return this.httpClient.restPost<IArchiveRequest, boolean>('/pro/plan/archive', data);
  }

  public async getServerVersion(): Promise<any> {
    return this.httpClient.restGet<any, any>('/pro/info/version', {});
  }

  public async makeQrCode(params: any): Promise<any> {
    return this.httpClient.restPost<any, any>('/pro/info/makeQrCode', params);
  }

  /**
   * 获取序列号
   * serialNum：序列号
   */
  public async getSerialNumber(): Promise<string> {
    const data = await this.httpClient.restPost<FrontConfigQueryModel, FrontConfigModel[]>('/pro/front/config/list', {
      group_name: 'device',
      name: 'serialNumber',
    });

    return data.find(v => v.name === 'serialNumber')!.value;
  }

  /**
   * 设置序列号
   * serialNum：序列号
   */
  public async saveSerialNumber(serialNum: string): Promise<boolean> {
    const params = [
      {
        group_name: 'device',
        name: 'serialNumber',
        value: serialNum,
      },
    ];

    return await this.httpClient.restPost<FrontConfigModel[], boolean>('/pro/front/config', params);
  }

  public async getSubjectList(from: string, to: string): Promise<QueryPatientInfo[]> {
    return (await this.httpClient.restPost<any, any[]>('/pro/plan/report/queryuuidgroupbysubject', { from, to })).map(v => ({
      label: `${v.name} ${v.code}`,
      value: v.id,
      uuids: v.plan_report_uuid_list,
    }));
  }
}
