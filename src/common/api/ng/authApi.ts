import { IHttpClient } from '../httpClient/IHttpClient';
import { FrontConfigModel, FrontConfigQueryModel, LoginDto } from '@/common/types';

export class AuthApi {
  private httpClient: IHttpClient;

  constructor(httpClient: IHttpClient) {
    this.httpClient = httpClient;
  }

  public async authenticate(authData: LoginDto, retryTimes?: number, timeoutInMilliseconds?: number): Promise<any> {
    return this.httpClient.restPost<LoginDto, any>('/pro/user/login', authData, retryTimes, timeoutInMilliseconds);
  }

  public async setConfig(params: FrontConfigModel[]): Promise<boolean> {
    return this.httpClient.restPost<FrontConfigModel[], boolean>('/pro/front/config', params);
  }
  public async getConfig(params: FrontConfigQueryModel): Promise<FrontConfigModel[]> {
    return this.httpClient.restPost<FrontConfigQueryModel, FrontConfigModel[]>('/pro/front/config/list', params);
  }

  public async getplanDetailByFile(filePath: string): Promise<any> {
    return this.httpClient.restPost<{}, any>('/pro/plan/preview', { ng_file: filePath }, 1, 60000);
  }

  public async getPlan(subjectId: number, planId: number): Promise<any> {
    return this.httpClient.restPost<any, any>(`/api/plan/detail/${subjectId}/${planId}`, {});
  }
}
