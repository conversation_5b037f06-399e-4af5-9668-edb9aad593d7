import { UserSession } from './types/index';

export class Auth {
  private store: any;
  constructor(statestore: any) {
    this.store = statestore;
  }

  public getUserSession(): any {
    return this.currentUserSession();
  }
  public currentUser(): UserSession | undefined {
    const userSession = this.currentUserSession();
    if (userSession) {
      return userSession;
    }

    return undefined;
  }

  public currentUserId(): number | undefined {
    if (this.currentUser()) {
      return this.currentUser()!.id;
    }

    return undefined;
  }

  public jwtToken(): string | undefined {
    return this.currentUserSession()?.jwtToken;
  }

  public orgId(): number | undefined {
    return 1;
  }
  public isBox(): boolean {
    return true;
  }
  public isSubmission = (): boolean => {
    return false;
  };

  private currentUserSession(): UserSession | undefined {
    return this.store.userSession;
  }
}
