// eslint-disable-next-line import/no-extraneous-dependencies
import { test, expect } from '@playwright/test';
import os from 'os';
import { window, electronApp, delay, startApp, gotoLogin } from '../utils';

const createTests = () => {
  test.beforeAll(async () => {
    if (!window) {
      await startApp();
    }
    await gotoLogin();
    await delay(1000);
  });

  test('import file', async () => {
    await window.getByText('新建患者').click();
    await window.getByText('影像方案', { exact: true }).click();
    const homeList = os.homedir().split('/').slice(1);
    homeList.push('Downloads', 'ngfile.ng');
    console.log(homeList);
    for (const fileItem of homeList) {
      await window.locator('.file-name').filter({
        hasText: fileItem,
      }).dblclick();
      await delay(500);
    }
    await delay(25000);
    await window.getByLabel('ID：').fill(`${Date.now()}`);
    await window.getByText('保存方案').click();
    await delay(5000);
    const href = await window.evaluate(() => document.location.href);
    expect(href).toBe('http://127.0.0.1:4001/#/home');
  });
  test('filed plan', async () => {
    await window.locator('.ant-table-row').filter({
      hasNotText: '无影像',
    }).first()
      .locator('.ant-table-cell').last()
      .locator('.anticon').last().click();
    await delay(3000);
  });
};

export default createTests;
