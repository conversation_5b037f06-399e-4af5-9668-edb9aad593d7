// eslint-disable-next-line import/no-extraneous-dependencies
import { test, expect } from '@playwright/test';
import { window, electronApp, delay, startApp, gotoLogin } from '../utils';

const createTests = () => {
  test.beforeAll(async () => {
    if (!window) {
      await startApp();
    }
  });

  test('about setting', async () => {
    await gotoLogin();
    await delay(2000);
    await window.click('id=toolBoxAndPower-settingIcon');
    await window.click('id=toolBoxAndPower-about');
    //  正确显示：
    //  产品名称：磁刺激仪
    //  产品型号：M230（根据配置的实际型号显示）（不可三击导出日志）
    //  软件名称：磁刺激仪管控软件
    //  软件型号：M200-SW
    //  完整版本：*******.3
    //  电子邮箱：<EMAIL>（不可点击）
    //  公司网址：https://www.neuralgalaxy.cn（不可点击）
    const about = await window.screenshot({ clip: { x: 0, y: 60, width: 1920, height: 1080 - 60 }});
    expect(about).toMatchSnapshot('about.png');
    const aboutcrumbs = await window.screenshot({ clip: { x: 0, y: 30, width: 300, height: 300 } });
    expect(aboutcrumbs).toMatchSnapshot('aboutcrumbs.png');
  });

  test('about go to home', async () => {
    await gotoLogin();
    await delay(1000);
    await window.getByText('首页').click();
    await delay(3000);
    const href = await window.evaluate(() => document.location.href);
    expect(href.indexOf('#/home')).not.toBe(-1);
  });
};

export default createTests;
