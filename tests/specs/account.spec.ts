// eslint-disable-next-line import/no-extraneous-dependencies
import { test, expect, Page } from '@playwright/test';
import { window, delay, startApp, isLogin, gotoLogin } from '../utils';
import { v4 as uuidv4 } from 'uuid';

const clipStatic = {
  x: 0,
  y: 60,
  width: 1920,
  height: 1080 - 60,
};

let tableCount = 0;
const screenPage = async (page, pageName, clip?) => {
  await expect(page).toHaveScreenshot(`${pageName}.png`, {
    clip,
  });
};

const PreviewplanEdit = () => {
  test.beforeAll(async () => {
    if (!window) {
      await startApp();
    }
    await gotoLogin();
  });

  test('go to account page', async () => {
    await delay(1000);
    await window.locator('#toolBoxAndPower-settingIcon').click();
    await window.getByText('管理').click();
    await delay(2000);
  });

  test('screenshot account', async () => {
    const navList = window.getByRole('tablist');
    await navList.getByText('管理').hover();
    await screenPage(navList, 'account-title-hover-selected');
    await navList.getByText('账号').hover();
    await screenPage(navList, 'account-title-hover-not-selected');
    await navList.getByText('账号').click();
    tableCount = (await window.locator('.ant-table-row').all()).length;
  });

  test('create new acount', async () => {
    await window.getByText('新建账号').click();
    await window.waitForSelector('.ant-modal-content');
    const modal = window.locator('.ant-modal-content');
    const box = await modal.boundingBox();
    await screenPage(modal, 'account-modal-create', {x: 0, y: 10, width: box?.width, height: box!.height - 10});
    await modal.getByLabel('账号').fill(`playwrightaccount${uuidv4().slice(0,4)}`);
    await modal.getByLabel('姓名').fill('cutter');
    await modal.getByLabel('密码').fill('PhpistheNo1');
    await modal.locator('.ant-btn-default').click();
    await delay(3000);
    const resCount = (await window.locator('.ant-table-row').all()).length;
    expect(resCount).toBe(tableCount + 1);
  });
};

export default PreviewplanEdit;
