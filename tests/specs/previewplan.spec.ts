// eslint-disable-next-line import/no-extraneous-dependencies
import { _electron as electron, ElectronApplication, Locator } from 'playwright';
// eslint-disable-next-line import/no-extraneous-dependencies
import { test, expect, Page } from '@playwright/test';
import { window, delay, startApp, isLogin, gotoLogin } from '../utils';

const clip = {
  x: 0,
  y: 60,
  width: 1920,
  height: 1080 - 60,
};

const handleChangeSurfPosition = async (page) => {
  await page.locator('#.ant-tooltip').getByText('上').click();
  await expect(page).toHaveScreenshot('surf-position.png');
};

const handleChangeSurfOpacity = async (page) => {
  const tooltip = page.locator('.ant-tooltip');
  const box = tooltip.boundingBox();
  await tooltip.mouse.click(
    box.width / 2,
    box.height / 2
  );
  await expect(page).toHaveScreenshot('surf-opacity.png');
};

const surfControlList = [
  undefined,
  handleChangeSurfPosition,
  handleChangeSurfOpacity,
];

const PreviewplanEdit = () => {
  test.beforeAll(async () => {
    if (!window) {
      await startApp();
    }
    await gotoLogin();
  });

  const entryPage = async (page) => {
    const list = await page.locator('.ant-table-row').filter({
      hasNotText: '无影像',
    }).first()
      .locator('.ant-table-cell').last()
      .locator('.anticon').all();
    await list[2].click();
    await delay(5000);
    await page.mouse.move(0, 0);
  };

  test('login', async () => {
    const password = window.locator('#password');
    await password.fill('Abc12345');
    await window.locator('#loginSubmit').click();
    await delay(3000);
  });

  test('go to edit previewPlanImage', async () => {
    await entryPage(window);
    await expect(window).toHaveScreenshot('previewPlanImage.png', {
      clip,
    });
  });

  test('change patient info', async () => {
    await delay(1000);
    await window.getByLabel('姓名：').fill('  ');
    await window.getByLabel('姓名：').blur();
    await window.getByLabel('出生日期：').click();
    await expect(window.locator('#preview-plan-patient')).toHaveScreenshot('patient-label-error.png');
  });

  test('change surf icon', async () => {
    const info = await window.locator('#view-control').locator('.anticon').all();
    const container = window.locator('#view-container');
    let count = 0;
    for (let item of info) {
      await window.mouse.click(0, 0);
      if (count === 0) {
        count++;
        continue;
      }
      await item.click();
      await delay(1000);
      await expect(container).toHaveScreenshot(`surf-${count++}.png`);
      const fn = surfControlList[count];
      if (fn) {
        await fn(container);
      }
    }
  });

  test('test spot item', async () => {
    const spotContainer = window.locator('#spot-container');
    const spotItemContainer = window.locator('#spot-item-container');

    await spotItemContainer.click();
    await delay(500);
    await expect(window).toHaveScreenshot('spot-select.png');
    await delay(500);
    await spotItemContainer.locator('.anticon').click();
    await expect(spotContainer).toHaveScreenshot('delete-icon-click.png');
  });

  test('test spot form', async () => {
    const spotFormContainer = window.locator('#spot-form-container');
    await spotFormContainer.locator('#preview-plan-spot-type').getByText('MEP参考点').click();
    await delay(500);
    await expect(spotFormContainer).toHaveScreenshot('spot-form-mep.png');
  });

  test('leave to home', async () => {
    await delay(1000);
    await window.getByText('首页').click();
    await window.waitForSelector('.ant-modal');
    const modal = window.locator('.ant-modal');
    await modal.locator('.ant-btn-default').click();
    await delay(1000);
    const href = await window.evaluate(() => document.location.href);
    expect(href).toBe('http://127.0.0.1:4001/#/home');
  });

  test('test submit', async () => {
    await entryPage(window);
    await window.getByText('保存方案').click();
    await delay(1000);
    const href = await window.evaluate(() => document.location.href);
    expect(href).toBe('http://127.0.0.1:4001/#/home');
  });
};

export default PreviewplanEdit;
