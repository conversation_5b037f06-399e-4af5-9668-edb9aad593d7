import { window, electronApp, delay, startApp } from '../utils';
// eslint-disable-next-line import/no-extraneous-dependencies
import { test, expect, Page } from '@playwright/test';

const createTests = () => {
  test.beforeAll(async () => {
    if (!window) {
      await startApp();
    }
  });

  test('login ui screenshot', async () => {
    await delay(4000);
    await expect(window).toHaveScreenshot('login.png');
    await window.click('id=shutdown-icon');
    await delay(2000);
    const shutdown = await window.screenshot({ clip: { x: 730, y: 450, width: 480, height: 180 } });
    expect(shutdown).toMatchSnapshot('login-shutdown.png');
    await window.getByText('取消').click();
  });

  test('login success', async () => {
    await delay(2000);
    const password = window.locator('#password');
    await password.fill('Abc12345');
    await window.click('id=loginSubmit');
    await delay(3000);
    const href = await window.evaluate(() => document.location.href);
    expect(href.indexOf('#/home')).not.toBe(-1);
    console.log('登录成功');
  });
};

export default createTests;
