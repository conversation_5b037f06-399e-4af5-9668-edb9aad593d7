// eslint-disable-next-line import/no-extraneous-dependencies
import { ElectronApplication, _electron as electron } from 'playwright';
// eslint-disable-next-line import/no-extraneous-dependencies
import { test, expect, Page } from '@playwright/test';
const delay = async ms => new Promise(resolve => setTimeout(resolve, ms));

let window: Page;
let electronApp: ElectronApplication;
let isLogin: boolean = false;

export const startApp = async () => {
  electronApp = await electron.launch({ args: ['./dist/main.js'] });
  window = await electronApp.firstWindow();
};

export const gotoLogin = async () => {
  const href = await window.evaluate(() => document.location.href);
  if (!isLogin) {
    const password = window.locator('#password');
    await password.fill('Abc12345');
    await window.click('id=loginSubmit');
    await delay(2000);
    isLogin = true;
  }
};

export {
  window,
  electronApp,
  delay,
  isLogin,
};

