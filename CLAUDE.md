# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个基于Electron + React + TypeScript的桌面应用，用于神经星系控制站（Neural Galaxy Control Station），产品名为m200。这是一个医疗设备控制软件，用于TMS（经颅磁刺激）治疗。

## 常用命令

### 开发环境启动
```bash
# 启动渲染进程开发模式
npm run vite-start-renderer-dev

# 启动主进程开发模式  
npm run start-main-dev
```

### 构建相关
```bash
# 完整构建
npm run build

# 构建主进程
npm run build-main

# 构建渲染进程 (Vite)
npm run build-renderer-vite

# 导航版本构建
npm run build-nav
```

### 代码质量检查
```bash
# ESLint检查
npm run lint-check

# ESLint修复
npm run lint-fix

# TypeScript类型检查  
npm run check-ts

# 完整检查（类型+Lint+测试+审计）
npm run check
```

### 测试相关
```bash
# 运行Jest单元测试
npm run test

# 运行覆盖率测试
npm run coverage

# UI自动化测试 (Playwright)
npm run test-ui
```

### 国际化
```bash
# 扫描文件中的中文并提取到语言包
npm run i18n-scan

# 替换代码中的中文为国际化函数调用
npm run i18n-cover
```

## 核心架构

### 目录结构
- `src/main/` - Electron主进程代码
- `src/renderer/` - React渲染进程代码（前端UI）
- `src/common/` - 主进程和渲染进程共享的代码
- `config/` - Webpack和Vite构建配置
- `public/` - 静态资源（包含brainbrowser库）

### 技术栈
- **框架**: Electron 22.2.0 + React 18 + TypeScript 5.3
- **构建工具**: Vite (渲染进程) + Webpack (主进程)
- **状态管理**: Recoil
- **UI库**: Antd 5.2.0
- **3D渲染**: Three.js 0.125.0 + BrainBrowser
- **测试**: Jest + Playwright
- **国际化**: i18next

### 主要模块

#### IPC通信
- `src/common/ipc/ipcChannels.ts` - 定义所有IPC通道常量
- `src/main/ipc/ipcHandle.ts` - 主进程IPC处理器
- `src/common/ipc/preload.ts` - 预加载脚本

#### 状态管理（Recoil）
核心状态包括：
- 用户会话 (`user.ts`)
- 国际化 (`intl.ts`) 
- 治疗状态 (`treatStatus.ts`)
- 设备故障 (`fault.ts`)
- 模态框状态 (`modalStatus.ts`)

#### 业务核心
- **治疗计划**: `src/renderer/container/previewPlan/`
- **设备注册**: `src/renderer/container/registCoil/`
- **治疗执行**: `src/renderer/container/previewTreat/`
- **报告管理**: `src/renderer/container/report/`
- **系统管理**: `src/renderer/container/manage/`

#### 3D可视化
- **大脑渲染**: `src/renderer/component/brainbrowser/` - 基于BrainBrowser库
- **表面渲染**: `src/renderer/component/surface/` - Three.js表面处理
- **治疗可视化**: `src/renderer/component/treatmentSurface/`

#### Socket通信
主进程包含多个Socket连接：
- `src/main/tmsSocket/` - TMS设备通信
- `src/main/pduSocket/` - PDU设备通信  
- `src/main/systemFault/` - 系统故障监控

### 关键概念

#### 产品线配置
- 开发环境：修改 `src/renderer/constant/product.ts`
- 生产环境：通过 `product_config` 文件配置加密字符串

#### 国际化流程
1. 使用 `npm run i18n-scan` 扫描中文
2. 使用 `npm run i18n-cover` 自动替换为国际化调用
3. 语言包位置：`src/renderer/static/messages/`

#### 3D坐标系
- 红色: X轴，绿色: Y轴，蓝色: Z轴（BrainBrowser坐标系）
- 拍子模型要求：X轴在长臂上，Z轴指向刺激大脑方向

### 开发注意事项

#### 代码规范
- 使用ESLint配置：`.eslintrc.json`
- TypeScript严格模式开启
- 缩进：2个空格
- 引号：单引号
- 分号：必须

#### 构建配置
- 渲染进程使用Vite构建，端口4001
- 别名配置：`@/renderer` 和 `@/common`
- CSS Modules支持

#### 环境配置
- Node.js 16版本（通过nvm管理）
- 开发环境变量通过 `setting.dev.json` 配置
- 生产环境变量通过 `setting.prod.json` 配置

#### 医疗设备特殊性
- 涉及TMS治疗设备控制，代码修改需格外谨慎
- 包含实时数据处理和设备状态监控
- 具有系统故障检测和安全机制